package com.estone.erp.publish.smt.bean;

import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.Data;

import java.util.List;


@Data
public class SpuUpdateQualification {
    private String userName;
    private Integer categoryId;
    private Long productId;
    private String spu;
    private String aliexpressAccountNumber;

    /**
     * 指定修改资质图片类型 1.GPSR 2.库存图 3.外包装
     */
    private List<Integer> updateQualificationTypeList;


    //定时任务需要的参数String json, String userName boolean isSpecial
    private String json;
    private boolean isSpecial;
    private SaleAccountAndBusinessResponse saleAccountAndBusinessResponse;

    /**
     * 特殊字段：SmtUpdatePackageQualificationJobHandler传参-SKU的外包装图链接URL（以-Package.结尾）
     */
    private String packageImageUrl;

}
