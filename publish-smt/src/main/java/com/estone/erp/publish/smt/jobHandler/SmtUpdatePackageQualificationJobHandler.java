package com.estone.erp.publish.smt.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.SkuPackageImageDataDTO;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.CommonHttpUtils;
import com.estone.erp.publish.common.executors.AliexpressExecutors;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.smt.bean.SpuUpdateQualification;
import com.estone.erp.publish.smt.enums.ProductStatusTypeEnum;
import com.estone.erp.publish.smt.model.AliexpressConfig;
import com.estone.erp.publish.smt.model.AliexpressConfigExample;
import com.estone.erp.publish.smt.service.AliexpressConfigService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 仓库前一天已拍照的SKU，且在线列表链接缺少外包装图的链接，系统自动修改资质定时任务
 * 定时频率：每天一次
 * 定时内容：获取仓库系统中SKU管理列表-CE认证-SKU认证拍照需求列表中，拍照时间为前一天的SKU数据，
 * 将在线列表在售状态为上架、且是否存在资质图为外包装图为否的链接，系统自动去修改资质，
 * 记录修改资质的处理报告，记录店铺、商品ID、结果状态和操作人操作时间，
 * 修改成功后将是否存在资质图为外包装图改为是；
 */
@Slf4j
@Component
public class SmtUpdatePackageQualificationJobHandler extends AbstractJobHandler {

    public SmtUpdatePackageQualificationJobHandler() {
        super("SmtUpdatePackageQualificationJobHandler");
    }

    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;

    @Resource
    private AliexpressConfigService aliexpressConfigService;

    @Resource
    private RabbitMqSender rabbitMqSender;

    @Data
    static class InnerParam {
        /**
         * 产品ids
         */
        private List<Long> productIdList;
        /**
         * 店铺账号列表
         */
        private List<String> accountList;
        /**
         * 查询仓库系统-拍照日期（yyyy-MM-dd）
         */
        private String photoDate;
    }

    @Override
    @XxlJob("SmtUpdatePackageQualificationJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        try {
            // 解析参数
            InnerParam innerParam = parseParam(param);

            doPackageQualificationData(innerParam);

            XxlJobLogger.log("-------仓库已拍照SKU外包装图资质自动修改任务结束--------");

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("仓库已拍照SKU外包装图资质自动修改任务执行失败", e);
            XxlJobLogger.log("任务执行失败: {}", e.getMessage());
            return new ReturnT<>(ReturnT.FAIL_CODE, "任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 外包装图资质数据处理
     */
    private void doPackageQualificationData(InnerParam innerParam) {
        // 获取启用自动修改资质的店铺配置
        AliexpressConfigExample configExample = new AliexpressConfigExample();
        configExample.createCriteria().andUsableEqualTo(true).andAutoUpdateQualificationsEqualTo(true);
        List<AliexpressConfig> aliexpressConfigs = aliexpressConfigService.selectByExample(configExample);
        if (CollectionUtils.isEmpty(aliexpressConfigs)) {
            XxlJobLogger.log("没有启用店铺自动修改资质！");
            return;
        }

        List<String> passAccountList = aliexpressConfigs.stream().map(AliexpressConfig::getAccount).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(innerParam.getAccountList())) {
            passAccountList = innerParam.getAccountList().stream().filter(passAccountList::contains).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(passAccountList)) {
                XxlJobLogger.log("入参店铺 没有启用店铺自动修改资质！");
                return;
            }
        }

        // 获取仓库系统中前一天已拍照的SKU数据
        Map<String, SkuPackageImageDataDTO> sku2PhotoDataDTOMap = getWarehouseSkuPhotoData(innerParam);
        if (MapUtils.isEmpty(sku2PhotoDataDTOMap)) {
            XxlJobLogger.log("未获取到仓库系统中已拍照的SKU数据！");
            return;
        }

        XxlJobLogger.log("获取到仓库系统中已拍照的SKU数量：{}", sku2PhotoDataDTOMap.size());

        // 遍历每个店铺处理外包装图资质
        for (String account : passAccountList) {
            processAccountPackageQualification(account, innerParam.getProductIdList(), sku2PhotoDataDTOMap);
        }
    }

    /**
     * 获取仓库系统中已拍照的SKU数据
     */
    private Map<String, SkuPackageImageDataDTO> getWarehouseSkuPhotoData(InnerParam innerParam) {
        try {
            // 确定查询时间范围
            String photoDate;
            if (StringUtils.isNotBlank(innerParam.getPhotoDate())) {
                photoDate = innerParam.getPhotoDate();
            } else {
                // 默认查询前一天的数据
                LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
                photoDate = yesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }

            XxlJobLogger.log("查询仓库系统SKU拍照数据，开始时间：{}，结束时间：{}", photoDate);

            // 调用仓库系统接口获取SKU拍照数据
            ApiResult<List<SkuPackageImageDataDTO>> result = CommonHttpUtils.getSkuPackageImageData(photoDate);
            if (!result.isSuccess() || CollectionUtils.isEmpty(result.getResult())) {
                XxlJobLogger.log("仓库系统返回SKU拍照数据为空，原因：{}", result.getErrorMsg());
                return null;
            }


            Map<String, SkuPackageImageDataDTO> sku2PhotoDataDTOMap = result.getResult().stream()
                    .collect(Collectors.toMap(SkuPackageImageDataDTO::getSku, Function.identity(), (oldV, newV) -> newV));


            XxlJobLogger.log("从仓库系统获取到{}条SKU拍照数据", sku2PhotoDataDTOMap.size());
            return sku2PhotoDataDTOMap;

        } catch (Exception e) {
            log.error("获取仓库系统SKU拍照数据异常", e);
            XxlJobLogger.log("获取仓库系统SKU拍照数据异常：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 处理单个店铺的外包装图资质
     */
    private void processAccountPackageQualification(String account, List<Long> productIds, Map<String, SkuPackageImageDataDTO> sku2PhotoDataDTOMap) {
        XxlJobLogger.log("开始处理店铺：{} 的外包装图资质", account);

        // 构建查询条件
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        // 上架状态
        request.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());

        // 不存在外包装图片（外包装图为否）
        request.setIsHasPackageImg(false);
        // 默认不查产品详情
        request.setIsSeleteProduct(false);
        // 设置SKU列表查询条件
        request.setArticleNumberStr(String.join(",", sku2PhotoDataDTOMap.keySet()));

        if (CollectionUtils.isNotEmpty(productIds)) {
            request.setProductIdList(productIds);
        }

        String[] fields = {"categoryId", "productId", "aliexpressAccountNumber", "spu", "articleNumber"};
        request.setQueryFields(fields);
        request.setOrderBy("createTime");
        request.setAliexpressAccountNumber(account);

        int pageSize = 1000;
        int pageIndex = 0;
        request.setPageSize(pageSize);

        int size = 0;
        while (true) {
            Page<EsAliexpressProductListing> page = esAliexpressProductListingService.page(request, pageSize, pageIndex);
            List<EsAliexpressProductListing> esAliexpressProductListing = page.getContent();

            if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
                break;
            }

            // 根据 productId 去重，避免重复推送
            Collection<EsAliexpressProductListing> sendList = esAliexpressProductListing
                    .stream()
                    .collect(Collectors.toMap(EsAliexpressProductListing::getProductId, Function.identity(), (old, newV) -> newV))
                    .values();

            for (EsAliexpressProductListing productListing : sendList) {
                AliexpressExecutors.executeCheckQualificationTask(() -> {
                    processPackageQualificationForProduct(productListing, sku2PhotoDataDTOMap);
                });
            }

            size = size + sendList.size();
            pageIndex++;
        }
        XxlJobLogger.log("{} 查询到需要处理外包装图资质的产品数量（productId去重）:{}", account, size);
    }

    /**
     * 处理单个产品的外包装图资质
     */
    private void processPackageQualificationForProduct(EsAliexpressProductListing productListing, Map<String, SkuPackageImageDataDTO> sku2PhotoDataDTOMap) {
        try {

            SkuPackageImageDataDTO skuPackageImageDataDTO = sku2PhotoDataDTOMap.get(productListing.getArticleNumber());
            if (null == skuPackageImageDataDTO || StringUtils.isBlank(skuPackageImageDataDTO.getPackImageUrls())) {
                XxlJobLogger.log("处理产品ID：{} sku:{},获取不到外包装图", productListing.getProductId(), productListing.getArticleNumber());
                return;
            }

            SpuUpdateQualification spuUpdateQualification = new SpuUpdateQualification();
            spuUpdateQualification.setAliexpressAccountNumber(productListing.getAliexpressAccountNumber());
            spuUpdateQualification.setCategoryId(productListing.getCategoryId());
            spuUpdateQualification.setProductId(productListing.getProductId());
            spuUpdateQualification.setSpu(productListing.getSpu());
            spuUpdateQualification.setUserName("SmtUpdatePackageQualificationJobHandler");
            // 设置只处理外包装图资质类型（3代表外包装图）
            spuUpdateQualification.setUpdateQualificationTypeList(List.of(3));
            spuUpdateQualification.setPackageImageUrl(skuPackageImageDataDTO.getPackImageUrls());
            // 发送到MQ队列进行处理
            rabbitMqSender.send(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_UPDATE_QUALIFICATION_ROUTE_KEY, spuUpdateQualification);

            XxlJobLogger.log("spuUpdateQualification:" + JSON.toJSONString(spuUpdateQualification));
        } catch (Exception e) {
            log.error("处理产品ID：{} 的外包装图资质时发生异常", productListing.getProductId(), e);
            XxlJobLogger.log("处理产品ID：{} 时发生异常：{}", productListing.getProductId(), e.getMessage());
        }
    }

    /**
     * 解析任务参数
     *
     * @param param 参数字符串
     * @return 解析后的参数对象
     */
    private InnerParam parseParam(String param) {
        InnerParam innerParam = new InnerParam();

        if (StringUtils.isBlank(param)) {
            XxlJobLogger.log("参数为空，使用默认参数处理前一天的记录");
            return innerParam;
        }

        try {
            innerParam = super.passParam(param, InnerParam.class);
            return innerParam;
        } catch (Exception e) {
            XxlJobLogger.log("参数解析失败: {}, 将使用默认参数", e.getMessage());
            log.warn("参数解析失败: {}", e.getMessage());
            return new InnerParam();
        }
    }
}
