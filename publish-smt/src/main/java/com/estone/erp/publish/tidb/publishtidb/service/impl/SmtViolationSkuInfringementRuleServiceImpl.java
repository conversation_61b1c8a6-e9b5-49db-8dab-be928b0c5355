package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.publish.tidb.publishtidb.model.SmtViolationSkuInfringementRule;
import com.estone.erp.publish.tidb.publishtidb.mapper.SmtViolationSkuInfringementRuleMapper;
import com.estone.erp.publish.tidb.publishtidb.service.SmtViolationSkuInfringementRuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * smt违规处罚-sku禁售规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Service
public class SmtViolationSkuInfringementRuleServiceImpl extends ServiceImpl<SmtViolationSkuInfringementRuleMapper, SmtViolationSkuInfringementRule> implements SmtViolationSkuInfringementRuleService {

}
