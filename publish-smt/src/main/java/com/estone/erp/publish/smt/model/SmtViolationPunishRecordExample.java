package com.estone.erp.publish.smt.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class SmtViolationPunishRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public SmtViolationPunishRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andImgIsNull() {
            addCriterion("img is null");
            return (Criteria) this;
        }

        public Criteria andImgIsNotNull() {
            addCriterion("img is not null");
            return (Criteria) this;
        }

        public Criteria andImgEqualTo(String value) {
            addCriterion("img =", value, "img");
            return (Criteria) this;
        }

        public Criteria andImgNotEqualTo(String value) {
            addCriterion("img <>", value, "img");
            return (Criteria) this;
        }

        public Criteria andImgGreaterThan(String value) {
            addCriterion("img >", value, "img");
            return (Criteria) this;
        }

        public Criteria andImgGreaterThanOrEqualTo(String value) {
            addCriterion("img >=", value, "img");
            return (Criteria) this;
        }

        public Criteria andImgLessThan(String value) {
            addCriterion("img <", value, "img");
            return (Criteria) this;
        }

        public Criteria andImgLessThanOrEqualTo(String value) {
            addCriterion("img <=", value, "img");
            return (Criteria) this;
        }

        public Criteria andImgLike(String value) {
            addCriterion("img like", value, "img");
            return (Criteria) this;
        }

        public Criteria andImgNotLike(String value) {
            addCriterion("img not like", value, "img");
            return (Criteria) this;
        }

        public Criteria andImgIn(List<String> values) {
            addCriterion("img in", values, "img");
            return (Criteria) this;
        }

        public Criteria andImgNotIn(List<String> values) {
            addCriterion("img not in", values, "img");
            return (Criteria) this;
        }

        public Criteria andImgBetween(String value1, String value2) {
            addCriterion("img between", value1, value2, "img");
            return (Criteria) this;
        }

        public Criteria andImgNotBetween(String value1, String value2) {
            addCriterion("img not between", value1, value2, "img");
            return (Criteria) this;
        }

        public Criteria andAccountIsNull() {
            addCriterion("account is null");
            return (Criteria) this;
        }

        public Criteria andAccountIsNotNull() {
            addCriterion("account is not null");
            return (Criteria) this;
        }

        public Criteria andAccountEqualTo(String value) {
            addCriterion("account =", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotEqualTo(String value) {
            addCriterion("account <>", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThan(String value) {
            addCriterion("account >", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThanOrEqualTo(String value) {
            addCriterion("account >=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThan(String value) {
            addCriterion("account <", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThanOrEqualTo(String value) {
            addCriterion("account <=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLike(String value) {
            addCriterion("account like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotLike(String value) {
            addCriterion("account not like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountIn(List<String> values) {
            addCriterion("account in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotIn(List<String> values) {
            addCriterion("account not in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountBetween(String value1, String value2) {
            addCriterion("account between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotBetween(String value1, String value2) {
            addCriterion("account not between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andSellerIdIsNull() {
            addCriterion("seller_id is null");
            return (Criteria) this;
        }

        public Criteria andSellerIdIsNotNull() {
            addCriterion("seller_id is not null");
            return (Criteria) this;
        }

        public Criteria andSellerIdEqualTo(String value) {
            addCriterion("seller_id =", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotEqualTo(String value) {
            addCriterion("seller_id <>", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdGreaterThan(String value) {
            addCriterion("seller_id >", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdGreaterThanOrEqualTo(String value) {
            addCriterion("seller_id >=", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdLessThan(String value) {
            addCriterion("seller_id <", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdLessThanOrEqualTo(String value) {
            addCriterion("seller_id <=", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdLike(String value) {
            addCriterion("seller_id like", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotLike(String value) {
            addCriterion("seller_id not like", value, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdIn(List<String> values) {
            addCriterion("seller_id in", values, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotIn(List<String> values) {
            addCriterion("seller_id not in", values, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdBetween(String value1, String value2) {
            addCriterion("seller_id between", value1, value2, "sellerId");
            return (Criteria) this;
        }

        public Criteria andSellerIdNotBetween(String value1, String value2) {
            addCriterion("seller_id not between", value1, value2, "sellerId");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andScoreCountTypeIsNull() {
            addCriterion("score_count_type is null");
            return (Criteria) this;
        }

        public Criteria andScoreCountTypeIsNotNull() {
            addCriterion("score_count_type is not null");
            return (Criteria) this;
        }

        public Criteria andScoreCountTypeEqualTo(String value) {
            addCriterion("score_count_type =", value, "scoreCountType");
            return (Criteria) this;
        }

        public Criteria andScoreCountTypeNotEqualTo(String value) {
            addCriterion("score_count_type <>", value, "scoreCountType");
            return (Criteria) this;
        }

        public Criteria andScoreCountTypeGreaterThan(String value) {
            addCriterion("score_count_type >", value, "scoreCountType");
            return (Criteria) this;
        }

        public Criteria andScoreCountTypeGreaterThanOrEqualTo(String value) {
            addCriterion("score_count_type >=", value, "scoreCountType");
            return (Criteria) this;
        }

        public Criteria andScoreCountTypeLessThan(String value) {
            addCriterion("score_count_type <", value, "scoreCountType");
            return (Criteria) this;
        }

        public Criteria andScoreCountTypeLessThanOrEqualTo(String value) {
            addCriterion("score_count_type <=", value, "scoreCountType");
            return (Criteria) this;
        }

        public Criteria andScoreCountTypeLike(String value) {
            addCriterion("score_count_type like", value, "scoreCountType");
            return (Criteria) this;
        }

        public Criteria andScoreCountTypeNotLike(String value) {
            addCriterion("score_count_type not like", value, "scoreCountType");
            return (Criteria) this;
        }

        public Criteria andScoreCountTypeIn(List<String> values) {
            addCriterion("score_count_type in", values, "scoreCountType");
            return (Criteria) this;
        }

        public Criteria andScoreCountTypeNotIn(List<String> values) {
            addCriterion("score_count_type not in", values, "scoreCountType");
            return (Criteria) this;
        }

        public Criteria andScoreCountTypeBetween(String value1, String value2) {
            addCriterion("score_count_type between", value1, value2, "scoreCountType");
            return (Criteria) this;
        }

        public Criteria andScoreCountTypeNotBetween(String value1, String value2) {
            addCriterion("score_count_type not between", value1, value2, "scoreCountType");
            return (Criteria) this;
        }

        public Criteria andSubRulesTypeIsNull() {
            addCriterion("sub_rules_type is null");
            return (Criteria) this;
        }

        public Criteria andSubRulesTypeIsNotNull() {
            addCriterion("sub_rules_type is not null");
            return (Criteria) this;
        }

        public Criteria andSubRulesTypeEqualTo(String value) {
            addCriterion("sub_rules_type =", value, "subRulesType");
            return (Criteria) this;
        }

        public Criteria andSubRulesTypeNotEqualTo(String value) {
            addCriterion("sub_rules_type <>", value, "subRulesType");
            return (Criteria) this;
        }

        public Criteria andSubRulesTypeGreaterThan(String value) {
            addCriterion("sub_rules_type >", value, "subRulesType");
            return (Criteria) this;
        }

        public Criteria andSubRulesTypeGreaterThanOrEqualTo(String value) {
            addCriterion("sub_rules_type >=", value, "subRulesType");
            return (Criteria) this;
        }

        public Criteria andSubRulesTypeLessThan(String value) {
            addCriterion("sub_rules_type <", value, "subRulesType");
            return (Criteria) this;
        }

        public Criteria andSubRulesTypeLessThanOrEqualTo(String value) {
            addCriterion("sub_rules_type <=", value, "subRulesType");
            return (Criteria) this;
        }

        public Criteria andSubRulesTypeLike(String value) {
            addCriterion("sub_rules_type like", value, "subRulesType");
            return (Criteria) this;
        }

        public Criteria andSubRulesTypeNotLike(String value) {
            addCriterion("sub_rules_type not like", value, "subRulesType");
            return (Criteria) this;
        }

        public Criteria andSubRulesTypeIn(List<String> values) {
            addCriterion("sub_rules_type in", values, "subRulesType");
            return (Criteria) this;
        }

        public Criteria andSubRulesTypeNotIn(List<String> values) {
            addCriterion("sub_rules_type not in", values, "subRulesType");
            return (Criteria) this;
        }

        public Criteria andSubRulesTypeBetween(String value1, String value2) {
            addCriterion("sub_rules_type between", value1, value2, "subRulesType");
            return (Criteria) this;
        }

        public Criteria andSubRulesTypeNotBetween(String value1, String value2) {
            addCriterion("sub_rules_type not between", value1, value2, "subRulesType");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("product_id is null");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("product_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualTo(String value) {
            addCriterion("product_id =", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualTo(String value) {
            addCriterion("product_id <>", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThan(String value) {
            addCriterion("product_id >", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(String value) {
            addCriterion("product_id >=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThan(String value) {
            addCriterion("product_id <", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualTo(String value) {
            addCriterion("product_id <=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLike(String value) {
            addCriterion("product_id like", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotLike(String value) {
            addCriterion("product_id not like", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdIn(List<String> values) {
            addCriterion("product_id in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotIn(List<String> values) {
            addCriterion("product_id not in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdBetween(String value1, String value2) {
            addCriterion("product_id between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotBetween(String value1, String value2) {
            addCriterion("product_id not between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNull() {
            addCriterion("sku_code is null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNotNull() {
            addCriterion("sku_code is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualTo(String value) {
            addCriterion("sku_code =", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualTo(String value) {
            addCriterion("sku_code <>", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThan(String value) {
            addCriterion("sku_code >", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sku_code >=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThan(String value) {
            addCriterion("sku_code <", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualTo(String value) {
            addCriterion("sku_code <=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLike(String value) {
            addCriterion("sku_code like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotLike(String value) {
            addCriterion("sku_code not like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIn(List<String> values) {
            addCriterion("sku_code in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotIn(List<String> values) {
            addCriterion("sku_code not in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeBetween(String value1, String value2) {
            addCriterion("sku_code between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotBetween(String value1, String value2) {
            addCriterion("sku_code not between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andSubTypeIsNull() {
            addCriterion("sub_type is null");
            return (Criteria) this;
        }

        public Criteria andSubTypeIsNotNull() {
            addCriterion("sub_type is not null");
            return (Criteria) this;
        }

        public Criteria andSubTypeEqualTo(String value) {
            addCriterion("sub_type =", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeNotEqualTo(String value) {
            addCriterion("sub_type <>", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeGreaterThan(String value) {
            addCriterion("sub_type >", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeGreaterThanOrEqualTo(String value) {
            addCriterion("sub_type >=", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeLessThan(String value) {
            addCriterion("sub_type <", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeLessThanOrEqualTo(String value) {
            addCriterion("sub_type <=", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeLike(String value) {
            addCriterion("sub_type like", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeNotLike(String value) {
            addCriterion("sub_type not like", value, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeIn(List<String> values) {
            addCriterion("sub_type in", values, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeNotIn(List<String> values) {
            addCriterion("sub_type not in", values, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeBetween(String value1, String value2) {
            addCriterion("sub_type between", value1, value2, "subType");
            return (Criteria) this;
        }

        public Criteria andSubTypeNotBetween(String value1, String value2) {
            addCriterion("sub_type not between", value1, value2, "subType");
            return (Criteria) this;
        }

        public Criteria andPunishIdIsNull() {
            addCriterion("punish_id is null");
            return (Criteria) this;
        }

        public Criteria andPunishIdIsNotNull() {
            addCriterion("punish_id is not null");
            return (Criteria) this;
        }

        public Criteria andPunishIdEqualTo(String value) {
            addCriterion("punish_id =", value, "punishId");
            return (Criteria) this;
        }

        public Criteria andPunishIdNotEqualTo(String value) {
            addCriterion("punish_id <>", value, "punishId");
            return (Criteria) this;
        }

        public Criteria andPunishIdGreaterThan(String value) {
            addCriterion("punish_id >", value, "punishId");
            return (Criteria) this;
        }

        public Criteria andPunishIdGreaterThanOrEqualTo(String value) {
            addCriterion("punish_id >=", value, "punishId");
            return (Criteria) this;
        }

        public Criteria andPunishIdLessThan(String value) {
            addCriterion("punish_id <", value, "punishId");
            return (Criteria) this;
        }

        public Criteria andPunishIdLessThanOrEqualTo(String value) {
            addCriterion("punish_id <=", value, "punishId");
            return (Criteria) this;
        }

        public Criteria andPunishIdLike(String value) {
            addCriterion("punish_id like", value, "punishId");
            return (Criteria) this;
        }

        public Criteria andPunishIdNotLike(String value) {
            addCriterion("punish_id not like", value, "punishId");
            return (Criteria) this;
        }

        public Criteria andPunishIdIn(List<String> values) {
            addCriterion("punish_id in", values, "punishId");
            return (Criteria) this;
        }

        public Criteria andPunishIdNotIn(List<String> values) {
            addCriterion("punish_id not in", values, "punishId");
            return (Criteria) this;
        }

        public Criteria andPunishIdBetween(String value1, String value2) {
            addCriterion("punish_id between", value1, value2, "punishId");
            return (Criteria) this;
        }

        public Criteria andPunishIdNotBetween(String value1, String value2) {
            addCriterion("punish_id not between", value1, value2, "punishId");
            return (Criteria) this;
        }

        public Criteria andAssetTypesIsNull() {
            addCriterion("asset_types is null");
            return (Criteria) this;
        }

        public Criteria andAssetTypesIsNotNull() {
            addCriterion("asset_types is not null");
            return (Criteria) this;
        }

        public Criteria andAssetTypesEqualTo(String value) {
            addCriterion("asset_types =", value, "assetTypes");
            return (Criteria) this;
        }

        public Criteria andAssetTypesNotEqualTo(String value) {
            addCriterion("asset_types <>", value, "assetTypes");
            return (Criteria) this;
        }

        public Criteria andAssetTypesGreaterThan(String value) {
            addCriterion("asset_types >", value, "assetTypes");
            return (Criteria) this;
        }

        public Criteria andAssetTypesGreaterThanOrEqualTo(String value) {
            addCriterion("asset_types >=", value, "assetTypes");
            return (Criteria) this;
        }

        public Criteria andAssetTypesLessThan(String value) {
            addCriterion("asset_types <", value, "assetTypes");
            return (Criteria) this;
        }

        public Criteria andAssetTypesLessThanOrEqualTo(String value) {
            addCriterion("asset_types <=", value, "assetTypes");
            return (Criteria) this;
        }

        public Criteria andAssetTypesLike(String value) {
            addCriterion("asset_types like", value, "assetTypes");
            return (Criteria) this;
        }

        public Criteria andAssetTypesNotLike(String value) {
            addCriterion("asset_types not like", value, "assetTypes");
            return (Criteria) this;
        }

        public Criteria andAssetTypesIn(List<String> values) {
            addCriterion("asset_types in", values, "assetTypes");
            return (Criteria) this;
        }

        public Criteria andAssetTypesNotIn(List<String> values) {
            addCriterion("asset_types not in", values, "assetTypes");
            return (Criteria) this;
        }

        public Criteria andAssetTypesBetween(String value1, String value2) {
            addCriterion("asset_types between", value1, value2, "assetTypes");
            return (Criteria) this;
        }

        public Criteria andAssetTypesNotBetween(String value1, String value2) {
            addCriterion("asset_types not between", value1, value2, "assetTypes");
            return (Criteria) this;
        }

        public Criteria andPunishTimeIsNull() {
            addCriterion("punish_time is null");
            return (Criteria) this;
        }

        public Criteria andPunishTimeIsNotNull() {
            addCriterion("punish_time is not null");
            return (Criteria) this;
        }

        public Criteria andPunishTimeEqualTo(Timestamp value) {
            addCriterion("punish_time =", value, "punishTime");
            return (Criteria) this;
        }

        public Criteria andPunishTimeNotEqualTo(Timestamp value) {
            addCriterion("punish_time <>", value, "punishTime");
            return (Criteria) this;
        }

        public Criteria andPunishTimeGreaterThan(String value) {
            addCriterion("punish_time >", value, "punishTime");
            return (Criteria) this;
        }

        public Criteria andPunishTimeGreaterThanOrEqualTo(String value) {
            addCriterion("punish_time >=", value, "punishTime");
            return (Criteria) this;
        }

        public Criteria andPunishTimeLessThan(String value) {
            addCriterion("punish_time <", value, "punishTime");
            return (Criteria) this;
        }

        public Criteria andPunishTimeLessThanOrEqualTo(String value) {
            addCriterion("punish_time <=", value, "punishTime");
            return (Criteria) this;
        }

        public Criteria andPunishTimeIn(List<Timestamp> values) {
            addCriterion("punish_time in", values, "punishTime");
            return (Criteria) this;
        }

        public Criteria andPunishTimeNotIn(List<Timestamp> values) {
            addCriterion("punish_time not in", values, "punishTime");
            return (Criteria) this;
        }

        public Criteria andPunishTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("punish_time between", value1, value2, "punishTime");
            return (Criteria) this;
        }

        public Criteria andPunishTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("punish_time not between", value1, value2, "punishTime");
            return (Criteria) this;
        }

        public Criteria andPointScoreIsNull() {
            addCriterion("point_score is null");
            return (Criteria) this;
        }

        public Criteria andPointScoreIsNotNull() {
            addCriterion("point_score is not null");
            return (Criteria) this;
        }

        public Criteria andPointScoreEqualTo(Integer value) {
            addCriterion("point_score =", value, "pointScore");
            return (Criteria) this;
        }

        public Criteria andPointScoreNotEqualTo(Integer value) {
            addCriterion("point_score <>", value, "pointScore");
            return (Criteria) this;
        }

        public Criteria andPointScoreGreaterThan(Integer value) {
            addCriterion("point_score >", value, "pointScore");
            return (Criteria) this;
        }

        public Criteria andPointScoreGreaterThanOrEqualTo(Integer value) {
            addCriterion("point_score >=", value, "pointScore");
            return (Criteria) this;
        }

        public Criteria andPointScoreLessThan(Integer value) {
            addCriterion("point_score <", value, "pointScore");
            return (Criteria) this;
        }

        public Criteria andPointScoreLessThanOrEqualTo(Integer value) {
            addCriterion("point_score <=", value, "pointScore");
            return (Criteria) this;
        }

        public Criteria andPointScoreIn(List<Integer> values) {
            addCriterion("point_score in", values, "pointScore");
            return (Criteria) this;
        }

        public Criteria andPointScoreNotIn(List<Integer> values) {
            addCriterion("point_score not in", values, "pointScore");
            return (Criteria) this;
        }

        public Criteria andPointScoreBetween(Integer value1, Integer value2) {
            addCriterion("point_score between", value1, value2, "pointScore");
            return (Criteria) this;
        }

        public Criteria andPointScoreNotBetween(Integer value1, Integer value2) {
            addCriterion("point_score not between", value1, value2, "pointScore");
            return (Criteria) this;
        }

        public Criteria andPointCountIsNull() {
            addCriterion("point_count is null");
            return (Criteria) this;
        }

        public Criteria andPointCountIsNotNull() {
            addCriterion("point_count is not null");
            return (Criteria) this;
        }

        public Criteria andPointCountEqualTo(Integer value) {
            addCriterion("point_count =", value, "pointCount");
            return (Criteria) this;
        }

        public Criteria andPointCountNotEqualTo(Integer value) {
            addCriterion("point_count <>", value, "pointCount");
            return (Criteria) this;
        }

        public Criteria andPointCountGreaterThan(Integer value) {
            addCriterion("point_count >", value, "pointCount");
            return (Criteria) this;
        }

        public Criteria andPointCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("point_count >=", value, "pointCount");
            return (Criteria) this;
        }

        public Criteria andPointCountLessThan(Integer value) {
            addCriterion("point_count <", value, "pointCount");
            return (Criteria) this;
        }

        public Criteria andPointCountLessThanOrEqualTo(Integer value) {
            addCriterion("point_count <=", value, "pointCount");
            return (Criteria) this;
        }

        public Criteria andPointCountIn(List<Integer> values) {
            addCriterion("point_count in", values, "pointCount");
            return (Criteria) this;
        }

        public Criteria andPointCountNotIn(List<Integer> values) {
            addCriterion("point_count not in", values, "pointCount");
            return (Criteria) this;
        }

        public Criteria andPointCountBetween(Integer value1, Integer value2) {
            addCriterion("point_count between", value1, value2, "pointCount");
            return (Criteria) this;
        }

        public Criteria andPointCountNotBetween(Integer value1, Integer value2) {
            addCriterion("point_count not between", value1, value2, "pointCount");
            return (Criteria) this;
        }

        public Criteria andShowStatusIsNull() {
            addCriterion("show_status is null");
            return (Criteria) this;
        }

        public Criteria andShowStatusIsNotNull() {
            addCriterion("show_status is not null");
            return (Criteria) this;
        }

        public Criteria andShowStatusEqualTo(String value) {
            addCriterion("show_status =", value, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusNotEqualTo(String value) {
            addCriterion("show_status <>", value, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusGreaterThan(String value) {
            addCriterion("show_status >", value, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusGreaterThanOrEqualTo(String value) {
            addCriterion("show_status >=", value, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusLessThan(String value) {
            addCriterion("show_status <", value, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusLessThanOrEqualTo(String value) {
            addCriterion("show_status <=", value, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusLike(String value) {
            addCriterion("show_status like", value, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusNotLike(String value) {
            addCriterion("show_status not like", value, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusIn(List<String> values) {
            addCriterion("show_status in", values, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusNotIn(List<String> values) {
            addCriterion("show_status not in", values, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusBetween(String value1, String value2) {
            addCriterion("show_status between", value1, value2, "showStatus");
            return (Criteria) this;
        }

        public Criteria andShowStatusNotBetween(String value1, String value2) {
            addCriterion("show_status not between", value1, value2, "showStatus");
            return (Criteria) this;
        }

        public Criteria andPunishCauseIsNull() {
            addCriterion("punish_cause is null");
            return (Criteria) this;
        }

        public Criteria andPunishCauseIsNotNull() {
            addCriterion("punish_cause is not null");
            return (Criteria) this;
        }

        public Criteria andPunishCauseEqualTo(String value) {
            addCriterion("punish_cause =", value, "punishCause");
            return (Criteria) this;
        }

        public Criteria andPunishCauseNotEqualTo(String value) {
            addCriterion("punish_cause <>", value, "punishCause");
            return (Criteria) this;
        }

        public Criteria andPunishCauseGreaterThan(String value) {
            addCriterion("punish_cause >", value, "punishCause");
            return (Criteria) this;
        }

        public Criteria andPunishCauseGreaterThanOrEqualTo(String value) {
            addCriterion("punish_cause >=", value, "punishCause");
            return (Criteria) this;
        }

        public Criteria andPunishCauseLessThan(String value) {
            addCriterion("punish_cause <", value, "punishCause");
            return (Criteria) this;
        }

        public Criteria andPunishCauseLessThanOrEqualTo(String value) {
            addCriterion("punish_cause <=", value, "punishCause");
            return (Criteria) this;
        }

        public Criteria andPunishCauseLike(String value) {
            addCriterion("punish_cause like", value, "punishCause");
            return (Criteria) this;
        }

        public Criteria andPunishCauseNotLike(String value) {
            addCriterion("punish_cause not like", value, "punishCause");
            return (Criteria) this;
        }

        public Criteria andPunishCauseIn(List<String> values) {
            addCriterion("punish_cause in", values, "punishCause");
            return (Criteria) this;
        }

        public Criteria andPunishCauseNotIn(List<String> values) {
            addCriterion("punish_cause not in", values, "punishCause");
            return (Criteria) this;
        }

        public Criteria andPunishCauseBetween(String value1, String value2) {
            addCriterion("punish_cause between", value1, value2, "punishCause");
            return (Criteria) this;
        }

        public Criteria andPunishCauseNotBetween(String value1, String value2) {
            addCriterion("punish_cause not between", value1, value2, "punishCause");
            return (Criteria) this;
        }

        public Criteria andPunishInfluenceIsNull() {
            addCriterion("punish_influence is null");
            return (Criteria) this;
        }

        public Criteria andPunishInfluenceIsNotNull() {
            addCriterion("punish_influence is not null");
            return (Criteria) this;
        }

        public Criteria andPunishInfluenceEqualTo(String value) {
            addCriterion("punish_influence =", value, "punishInfluence");
            return (Criteria) this;
        }

        public Criteria andPunishInfluenceNotEqualTo(String value) {
            addCriterion("punish_influence <>", value, "punishInfluence");
            return (Criteria) this;
        }

        public Criteria andPunishInfluenceGreaterThan(String value) {
            addCriterion("punish_influence >", value, "punishInfluence");
            return (Criteria) this;
        }

        public Criteria andPunishInfluenceGreaterThanOrEqualTo(String value) {
            addCriterion("punish_influence >=", value, "punishInfluence");
            return (Criteria) this;
        }

        public Criteria andPunishInfluenceLessThan(String value) {
            addCriterion("punish_influence <", value, "punishInfluence");
            return (Criteria) this;
        }

        public Criteria andPunishInfluenceLessThanOrEqualTo(String value) {
            addCriterion("punish_influence <=", value, "punishInfluence");
            return (Criteria) this;
        }

        public Criteria andPunishInfluenceLike(String value) {
            addCriterion("punish_influence like", value, "punishInfluence");
            return (Criteria) this;
        }

        public Criteria andPunishInfluenceNotLike(String value) {
            addCriterion("punish_influence not like", value, "punishInfluence");
            return (Criteria) this;
        }

        public Criteria andPunishInfluenceIn(List<String> values) {
            addCriterion("punish_influence in", values, "punishInfluence");
            return (Criteria) this;
        }

        public Criteria andPunishInfluenceNotIn(List<String> values) {
            addCriterion("punish_influence not in", values, "punishInfluence");
            return (Criteria) this;
        }

        public Criteria andPunishInfluenceBetween(String value1, String value2) {
            addCriterion("punish_influence between", value1, value2, "punishInfluence");
            return (Criteria) this;
        }

        public Criteria andPunishInfluenceNotBetween(String value1, String value2) {
            addCriterion("punish_influence not between", value1, value2, "punishInfluence");
            return (Criteria) this;
        }

        public Criteria andPromptIsNull() {
            addCriterion("prompt is null");
            return (Criteria) this;
        }

        public Criteria andPromptIsNotNull() {
            addCriterion("prompt is not null");
            return (Criteria) this;
        }

        public Criteria andPromptEqualTo(String value) {
            addCriterion("prompt =", value, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptNotEqualTo(String value) {
            addCriterion("prompt <>", value, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptGreaterThan(String value) {
            addCriterion("prompt >", value, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptGreaterThanOrEqualTo(String value) {
            addCriterion("prompt >=", value, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptLessThan(String value) {
            addCriterion("prompt <", value, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptLessThanOrEqualTo(String value) {
            addCriterion("prompt <=", value, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptLike(String value) {
            addCriterion("prompt like", value, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptLikeIn(List<String> list) {
            int len = list.size();
            StringBuilder sb = new StringBuilder(len * 32);
            for (int i = 0; i < len; i++) {
                sb.append("prompt LIKE '%").append(list.get(i)).append("%' ");
                if (i != len - 1) {
                    sb.append(" OR ");
                }
            }
            addCriterion("(" + sb + ")");
            return (Criteria) this;
        }

        public Criteria andPromptNotLike(String value) {
            addCriterion("prompt not like", value, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptIn(List<String> values) {
            addCriterion("prompt in", values, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptNotIn(List<String> values) {
            addCriterion("prompt not in", values, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptBetween(String value1, String value2) {
            addCriterion("prompt between", value1, value2, "prompt");
            return (Criteria) this;
        }

        public Criteria andPromptNotBetween(String value1, String value2) {
            addCriterion("prompt not between", value1, value2, "prompt");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeIsNull() {
            addCriterion("crawl_time is null");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeIsNotNull() {
            addCriterion("crawl_time is not null");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeEqualTo(Timestamp value) {
            addCriterion("crawl_time =", value, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeNotEqualTo(Timestamp value) {
            addCriterion("crawl_time <>", value, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeGreaterThan(String value) {
            addCriterion("crawl_time >", value, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeGreaterThanOrEqualTo(String value) {
            addCriterion("crawl_time >=", value, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeLessThan(String value) {
            addCriterion("crawl_time <", value, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeLessThanOrEqualTo(String value) {
            addCriterion("crawl_time <=", value, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeIn(List<Timestamp> values) {
            addCriterion("crawl_time in", values, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeNotIn(List<Timestamp> values) {
            addCriterion("crawl_time not in", values, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("crawl_time between", value1, value2, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCrawlTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("crawl_time not between", value1, value2, "crawlTime");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Timestamp value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Timestamp value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(String value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(String value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(String value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(String value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Timestamp> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Timestamp> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNull() {
            addCriterion("update_date is null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNotNull() {
            addCriterion("update_date is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateEqualTo(Timestamp value) {
            addCriterion("update_date =", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotEqualTo(Timestamp value) {
            addCriterion("update_date <>", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThan(Timestamp value) {
            addCriterion("update_date >", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("update_date >=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThan(Timestamp value) {
            addCriterion("update_date <", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("update_date <=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIn(List<Timestamp> values) {
            addCriterion("update_date in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotIn(List<Timestamp> values) {
            addCriterion("update_date not in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_date between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_date not between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameIsNull() {
            addCriterion("infringement_type_name is null");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameIsNotNull() {
            addCriterion("infringement_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameEqualTo(String value) {
            addCriterion("infringement_type_name =", value, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameNotEqualTo(String value) {
            addCriterion("infringement_type_name <>", value, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameGreaterThan(String value) {
            addCriterion("infringement_type_name >", value, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("infringement_type_name >=", value, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameLessThan(String value) {
            addCriterion("infringement_type_name <", value, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameLessThanOrEqualTo(String value) {
            addCriterion("infringement_type_name <=", value, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameLike(String value) {
            addCriterion("infringement_type_name like", value, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameNotLike(String value) {
            addCriterion("infringement_type_name not like", value, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameIn(List<String> values) {
            addCriterion("infringement_type_name in", values, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameNotIn(List<String> values) {
            addCriterion("infringement_type_name not in", values, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameBetween(String value1, String value2) {
            addCriterion("infringement_type_name between", value1, value2, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementTypeNameNotBetween(String value1, String value2) {
            addCriterion("infringement_type_name not between", value1, value2, "infringementTypeName");
            return (Criteria) this;
        }

        public Criteria andInfringementObjIsNull() {
            addCriterion("infringement_obj is null");
            return (Criteria) this;
        }

        public Criteria andInfringementObjIsNotNull() {
            addCriterion("infringement_obj is not null");
            return (Criteria) this;
        }

        public Criteria andInfringementObjEqualTo(String value) {
            addCriterion("infringement_obj =", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjNotEqualTo(String value) {
            addCriterion("infringement_obj <>", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjGreaterThan(String value) {
            addCriterion("infringement_obj >", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjGreaterThanOrEqualTo(String value) {
            addCriterion("infringement_obj >=", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjLessThan(String value) {
            addCriterion("infringement_obj <", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjLessThanOrEqualTo(String value) {
            addCriterion("infringement_obj <=", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjLike(String value) {
            addCriterion("infringement_obj like", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjNotLike(String value) {
            addCriterion("infringement_obj not like", value, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjIn(List<String> values) {
            addCriterion("infringement_obj in", values, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjNotIn(List<String> values) {
            addCriterion("infringement_obj not in", values, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjBetween(String value1, String value2) {
            addCriterion("infringement_obj between", value1, value2, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andInfringementObjNotBetween(String value1, String value2) {
            addCriterion("infringement_obj not between", value1, value2, "infringementObj");
            return (Criteria) this;
        }

        public Criteria andBanFlagIsNull() {
            addCriterion("ban_flag is null");
            return (Criteria) this;
        }

        public Criteria andBanFlagIsNotNull() {
            addCriterion("ban_flag is not null");
            return (Criteria) this;
        }

        public Criteria andBanFlagEqualTo(Boolean value) {
            addCriterion("ban_flag =", value, "banFlag");
            return (Criteria) this;
        }

        public Criteria andBanFlagNotEqualTo(Boolean value) {
            addCriterion("ban_flag <>", value, "banFlag");
            return (Criteria) this;
        }

        public Criteria andBanFlagGreaterThan(Boolean value) {
            addCriterion("ban_flag >", value, "banFlag");
            return (Criteria) this;
        }

        public Criteria andBanFlagGreaterThanOrEqualTo(Boolean value) {
            addCriterion("ban_flag >=", value, "banFlag");
            return (Criteria) this;
        }

        public Criteria andBanFlagLessThan(Boolean value) {
            addCriterion("ban_flag <", value, "banFlag");
            return (Criteria) this;
        }

        public Criteria andBanFlagLessThanOrEqualTo(Boolean value) {
            addCriterion("ban_flag <=", value, "banFlag");
            return (Criteria) this;
        }

        public Criteria andBanFlagIn(List<Boolean> values) {
            addCriterion("ban_flag in", values, "banFlag");
            return (Criteria) this;
        }

        public Criteria andBanFlagNotIn(List<Boolean> values) {
            addCriterion("ban_flag not in", values, "banFlag");
            return (Criteria) this;
        }

        public Criteria andBanFlagBetween(Boolean value1, Boolean value2) {
            addCriterion("ban_flag between", value1, value2, "banFlag");
            return (Criteria) this;
        }

        public Criteria andBanFlagNotBetween(Boolean value1, Boolean value2) {
            addCriterion("ban_flag not between", value1, value2, "banFlag");
            return (Criteria) this;
        }

        public Criteria andBanTimeIsNull() {
            addCriterion("ban_time is null");
            return (Criteria) this;
        }

        public Criteria andBanTimeIsNotNull() {
            addCriterion("ban_time is not null");
            return (Criteria) this;
        }

        public Criteria andBanTimeEqualTo(Timestamp value) {
            addCriterion("ban_time =", value, "banTime");
            return (Criteria) this;
        }

        public Criteria andBanTimeNotEqualTo(Timestamp value) {
            addCriterion("ban_time <>", value, "banTime");
            return (Criteria) this;
        }

        public Criteria andBanTimeGreaterThan(Timestamp value) {
            addCriterion("ban_time >", value, "banTime");
            return (Criteria) this;
        }

        public Criteria andBanTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ban_time >=", value, "banTime");
            return (Criteria) this;
        }

        public Criteria andBanTimeGreaterThanOrEqualTo(String value) {
            addCriterion("ban_time >=", value, "banTime");
            return (Criteria) this;
        }

        public Criteria andBanTimeLessThan(Timestamp value) {
            addCriterion("ban_time <", value, "banTime");
            return (Criteria) this;
        }

        public Criteria andBanTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ban_time <=", value, "banTime");
            return (Criteria) this;
        }

        public Criteria andBanTimeLessThanOrEqualTo(String value) {
            addCriterion("ban_time <=", value, "banTime");
            return (Criteria) this;
        }

        public Criteria andBanTimeIn(List<Timestamp> values) {
            addCriterion("ban_time in", values, "banTime");
            return (Criteria) this;
        }

        public Criteria andBanTimeNotIn(List<Timestamp> values) {
            addCriterion("ban_time not in", values, "banTime");
            return (Criteria) this;
        }

        public Criteria andBanTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ban_time between", value1, value2, "banTime");
            return (Criteria) this;
        }

        public Criteria andBanTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ban_time not between", value1, value2, "banTime");
            return (Criteria) this;
        }

        public Criteria andOperatedByIsNull() {
            addCriterion("operated_by is null");
            return (Criteria) this;
        }

        public Criteria andOperatedByIsNotNull() {
            addCriterion("operated_by is not null");
            return (Criteria) this;
        }

        public Criteria andOperatedByEqualTo(String value) {
            addCriterion("operated_by =", value, "operatedBy");
            return (Criteria) this;
        }

        public Criteria andOperatedByNotEqualTo(String value) {
            addCriterion("operated_by <>", value, "operatedBy");
            return (Criteria) this;
        }

        public Criteria andOperatedByGreaterThan(String value) {
            addCriterion("operated_by >", value, "operatedBy");
            return (Criteria) this;
        }

        public Criteria andOperatedByGreaterThanOrEqualTo(String value) {
            addCriterion("operated_by >=", value, "operatedBy");
            return (Criteria) this;
        }

        public Criteria andOperatedByLessThan(String value) {
            addCriterion("operated_by <", value, "operatedBy");
            return (Criteria) this;
        }

        public Criteria andOperatedByLessThanOrEqualTo(String value) {
            addCriterion("operated_by <=", value, "operatedBy");
            return (Criteria) this;
        }

        public Criteria andOperatedByLike(String value) {
            addCriterion("operated_by like", value, "operatedBy");
            return (Criteria) this;
        }

        public Criteria andOperatedByNotLike(String value) {
            addCriterion("operated_by not like", value, "operatedBy");
            return (Criteria) this;
        }

        public Criteria andOperatedByIn(List<String> values) {
            addCriterion("operated_by in", values, "operatedBy");
            return (Criteria) this;
        }

        public Criteria andOperatedByNotIn(List<String> values) {
            addCriterion("operated_by not in", values, "operatedBy");
            return (Criteria) this;
        }

        public Criteria andOperatedByBetween(String value1, String value2) {
            addCriterion("operated_by between", value1, value2, "operatedBy");
            return (Criteria) this;
        }

        public Criteria andOperatedByNotBetween(String value1, String value2) {
            addCriterion("operated_by not between", value1, value2, "operatedBy");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}