package com.estone.erp.publish.smt.jobHandler;

import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.PagingUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.OnlineStatusEnum;
import com.estone.erp.publish.elasticsearch.model.EsAliexpressPunishInfoNewest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAliexpressPunishInfoNewestRequest;
import com.estone.erp.publish.elasticsearch.service.EsAliexpressPunishInfoNewestService;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductOfflineListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductOfflineRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.smt.model.SmtViolationPunishRecord;
import com.estone.erp.publish.smt.service.AliexpressProductOfflineService;
import com.estone.erp.publish.smt.service.SmtViolationPunishRecordService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 同步爬虫 smt违规数据
 * @date 2024年5月24日15:25:29
 */
@Component
@Slf4j
public class SynchEsAliexpressPunishInfoNewestJobHandler extends AbstractJobHandler {
    @Resource
    private EsAliexpressPunishInfoNewestService esAliexpressPunishInfoNewestService;
    @Resource
    private SmtViolationPunishRecordService smtViolationPunishRecordService;
    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;
    @Resource
    private AliexpressProductOfflineService aliexpressProductOfflineService;
    public SynchEsAliexpressPunishInfoNewestJobHandler() {
        super(SynchEsAliexpressPunishInfoNewestJobHandler.class.getName());
    }

    @Override
    @XxlJob("SynchEsAliexpressPunishInfoNewestJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        List<SaleAccountAndBusinessResponse> aliexpressAccounts = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SMT);
        if(StringUtils.isNotBlank(param)){
            List<String> strings = CommonUtils.splitList(param, ",");
            aliexpressAccounts = aliexpressAccounts.stream().filter(t -> strings.contains(t.getAccountNumber())).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(aliexpressAccounts)){
            return ReturnT.SUCCESS;
        }

        XxlJobLogger.log("同步es违规处罚数据账号数量:" + aliexpressAccounts.size());
        int i = 0;
        for (SaleAccountAndBusinessResponse aliexpressAccount : aliexpressAccounts) {
            i++;
            try {
                String accountNumber = aliexpressAccount.getAccountNumber();
                XxlJobLogger.log("同步总数:" + aliexpressAccounts.size() + " 进度:" + i);
                XxlJobLogger.log("同步账号:" + accountNumber);

                EsAliexpressPunishInfoNewestRequest request = new EsAliexpressPunishInfoNewestRequest();
                request.setAccount(accountNumber);
                int pageIndex = 0;
                int pageSize = 1000;
                while (true){
                    request.setPageIndex(pageIndex);
                    request.setPageSize(pageSize);
                    PageInfo<EsAliexpressPunishInfoNewest> page = esAliexpressPunishInfoNewestService.page(request);
                    if(page == null || CollectionUtils.isEmpty(page.getContents())){
                        break;
                    }
                    //插入数据库 存在更新不存在新建
                    List<EsAliexpressPunishInfoNewest> content = page.getContents();
                    //注意账号需要用接口的店铺，es账号统一做了小写处理
                    List<SmtViolationPunishRecord> recordLists = new ArrayList<>();
                    Map<String, List<EsAliexpressPunishInfoNewest>> productIdMap = content.stream().collect(Collectors.groupingBy(t -> t.getProductId()));

                    Set<String> productIdSet = productIdMap.keySet();

                    EsAliexpressProductListingRequest productListingRequest = new EsAliexpressProductListingRequest();
                    productListingRequest.setAliexpressAccountNumber(accountNumber);
                    productListingRequest.setProductIdStr(StringUtils.join(productIdSet, ","));
                    productListingRequest.setOnlineStatus(OnlineStatusEnum.ALL.getCode());
                    productListingRequest.setQueryFields(new String[]{"productId","skuCode","articleNumber", "subject", "skuDisplayImg","imageUrls"});
                    List<EsAliexpressProductListing> esList = esAliexpressProductListingService.getEsAliexpressProductListing(productListingRequest);
                    Map<Long, List<EsAliexpressProductListing>> productIdListMap = esList.stream().collect(Collectors.groupingBy(EsAliexpressProductListing::getProductId));

                    // 查询下架数据
                    EsAliexpressProductOfflineRequest OfflineRequest = new EsAliexpressProductOfflineRequest();
                    OfflineRequest.setAliexpressAccountNumber(accountNumber);
                    OfflineRequest.setQueryFields(new String[]{"productId", "skuCode", "articleNumber", "subject", "skuDisplayImg", "imageUrls"});
                    OfflineRequest.setProductIdStr(StringUtils.join(productIdSet, ","));
                    List<EsAliexpressProductOfflineListing> offlineEsList = aliexpressProductOfflineService.listOfflineProducts(OfflineRequest);

                    Map<Long, List<EsAliexpressProductListing>> offlineProductIdListMap = offlineEsList.stream()
                            .map(offlineListing -> { // 直接转换与在线列表相同结构
                                EsAliexpressProductListing productListing = new EsAliexpressProductListing();
                                BeanUtils.copyProperties(offlineListing, productListing);
                                return productListing;
                            }).filter(listing -> null != listing.getProductId())
                            .collect(Collectors.groupingBy(EsAliexpressProductListing::getProductId));
                    for (EsAliexpressPunishInfoNewest esAliexpressPunishInfoNewest : content) {
                        try {
                            //同一店铺 产品id 和 违规编号 是唯一的数据
                            String productId = esAliexpressPunishInfoNewest.getProductId();
                            if (StringUtils.isEmpty(productId)) {
                                SmtViolationPunishRecord item = getItem(esAliexpressPunishInfoNewest, accountNumber);
                                recordLists.add(item);
                                continue;
                            }

                            // 先查询在线列表数据，没有则再查下架列表
                            List<EsAliexpressProductListing> esAliexpressProductListings = productIdListMap.getOrDefault(Long.valueOf(productId), offlineProductIdListMap.get(Long.valueOf(productId)));
                            if (CollectionUtils.isEmpty(esAliexpressProductListings)) {
                                //无数据，也需要存一份
                                SmtViolationPunishRecord item = getItem(esAliexpressPunishInfoNewest, accountNumber);
                                recordLists.add(item);
                            } else {
                                for (EsAliexpressProductListing aliexpressProductListing : esAliexpressProductListings) {
                                    SmtViolationPunishRecord item = getItem(esAliexpressPunishInfoNewest, accountNumber);
                                    recordLists.add(item);
                                    String skuDisplayImg = aliexpressProductListing.getSkuDisplayImg();
                                    if (StringUtils.isBlank(skuDisplayImg)) {
                                        skuDisplayImg = CommonUtils.splitList(aliexpressProductListing.getImageUrls(), ";").get(0);
                                    }
                                    item.setTitle(aliexpressProductListing.getSubject());
                                    item.setImg(skuDisplayImg);
                                    item.setSkuCode(aliexpressProductListing.getSkuCode());
                                    item.setArticleNumber(aliexpressProductListing.getArticleNumber());
                                }
                            }
                        } catch (Exception e) {
                            XxlJobLogger.log("SynchEsAliexpressPunishInfoNewestJobHandler 循环添加异常：{}", e.getMessage());
                        }
                    }

                    List<List<SmtViolationPunishRecord>> lists = PagingUtils.newPagingList(recordLists, 1000);
                    for (List<SmtViolationPunishRecord> list : lists) {
                        // 使用批量更新插入：若存在数据则覆盖更新数据，若不存在则插入新数据
                        smtViolationPunishRecordService.batchUpsert(list);
                    }
                    pageIndex++;
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return ReturnT.SUCCESS;
    }

    private SmtViolationPunishRecord getItem(EsAliexpressPunishInfoNewest esAliexpressPunishInfoNewest, String account){
        SmtViolationPunishRecord smtViolationPunishRecord = new SmtViolationPunishRecord();
        smtViolationPunishRecord.setAccount(account);
        smtViolationPunishRecord.setSellerId(esAliexpressPunishInfoNewest.getSellerId());
        smtViolationPunishRecord.setProductId(esAliexpressPunishInfoNewest.getProductId());
        smtViolationPunishRecord.setSubType(esAliexpressPunishInfoNewest.getSubType());
        smtViolationPunishRecord.setPunishId(esAliexpressPunishInfoNewest.getPunishId());
        List<String> assetTypes = esAliexpressPunishInfoNewest.getAssetTypes();
        if(CollectionUtils.isNotEmpty(assetTypes)){
            smtViolationPunishRecord.setAssetTypes(assetTypes.get(0));
        }
        smtViolationPunishRecord.setPunishTime(new Timestamp(esAliexpressPunishInfoNewest.getPunishTime().getTime()));
        smtViolationPunishRecord.setPointScore(esAliexpressPunishInfoNewest.getPointScore());
        smtViolationPunishRecord.setPointCount(esAliexpressPunishInfoNewest.getPointCount());
        smtViolationPunishRecord.setShowStatus(esAliexpressPunishInfoNewest.getShowStatus());
        smtViolationPunishRecord.setPunishCause(esAliexpressPunishInfoNewest.getPunishCause());
        smtViolationPunishRecord.setPunishInfluence(esAliexpressPunishInfoNewest.getPunishInfluence());
        smtViolationPunishRecord.setPrompt(esAliexpressPunishInfoNewest.getPrompt());
        smtViolationPunishRecord.setCrawlTime(new Timestamp(esAliexpressPunishInfoNewest.getCrawlTime().getTime()));
        smtViolationPunishRecord.setCreateDate(new Timestamp(System.currentTimeMillis()));
        smtViolationPunishRecord.setUpdateDate(new Timestamp(System.currentTimeMillis()));
        smtViolationPunishRecord.setScoreCountType(esAliexpressPunishInfoNewest.getScoreCountType());
        smtViolationPunishRecord.setSubRulesType(esAliexpressPunishInfoNewest.getSubRulesType());
        return smtViolationPunishRecord;
    }
}
