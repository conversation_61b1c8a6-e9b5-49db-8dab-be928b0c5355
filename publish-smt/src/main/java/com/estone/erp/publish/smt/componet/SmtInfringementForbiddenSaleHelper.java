package com.estone.erp.publish.smt.componet;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.InfringementForbiddenSaleHelper;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.system.product.ProductInfringementForbiddenSaleUtils;
import com.estone.erp.publish.system.product.ProhibitionDO;
import com.estone.erp.publish.system.product.bean.ForbiddenAndSpecical;
import com.estone.erp.publish.tidb.publishtidb.model.SmtPublishOperationLog;
import com.estone.erp.publish.tidb.publishtidb.service.SmtPublishOperationLogService;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class SmtInfringementForbiddenSaleHelper {
    public static ApiResult<Map<String, Boolean>> checkForbiddenSale(List<EsAliexpressProductListing> listings, List<ProhibitionDO> prohibitionDOS, String opType) {
        if (CollectionUtils.isEmpty(listings) || CollectionUtils.isEmpty(prohibitionDOS)) {
            return ApiResult.newError("checkForbiddenSale 方法参数为空");
        }

        // 校验禁售结果映射
        Map<String, Boolean> checkSku = new HashMap<>();
        try {
            // 构建SKU到产品ID的映射关系
            Map<String, String> sku2SellerSku = new HashMap<>();
            for (EsAliexpressProductListing listing : listings) {
                sku2SellerSku.put(listing.getArticleNumber(), String.valueOf(listing.getProductId()));
            }

            List<String> skuList = new ArrayList<>(sku2SellerSku.keySet());

            // 使用分批处理，每1000条SKU为一批，避免接口调用失败
            final int batchSize = 1000;
            Map<String, ForbiddenAndSpecical> sonskuForbiddenAndSpecicalMap =
                ProductInfringementForbiddenSaleUtils.getForbiddenAndSpecicalBySonSkuBatch(skuList, batchSize);

            // 遍历所有SKU，检查禁售状态并记录操作日志
            for (String sku : skuList) {
                if (!sonskuForbiddenAndSpecicalMap.containsKey(sku)) {
                    // SKU未找到禁售信息，标记为未禁售
                    checkSku.put(sku, false);
                    continue;
                }

                // 获取SKU的禁售和特殊标签信息
                ForbiddenAndSpecical forbiddenAndSpecical = sonskuForbiddenAndSpecicalMap.get(sku);

                // 判断是否匹配禁售规则
                boolean match = InfringementForbiddenSaleHelper.isMatch(forbiddenAndSpecical, prohibitionDOS);
                checkSku.put(sku, match);

                // 记录操作日志
                String productId = sku2SellerSku.get(sku);
                SmtPublishOperationLogService smtPublishOperationLogService = SpringUtils.getBean(SmtPublishOperationLogService.class);
                SmtPublishOperationLog smtPublishOperationLog = new SmtPublishOperationLog();
                smtPublishOperationLog.setModId(productId);
                smtPublishOperationLog.setPlatform(SaleChannel.CHANNEL_SMT);
                smtPublishOperationLog.setOpType(opType);
                smtPublishOperationLog.setMetaObj(JSON.toJSONString(forbiddenAndSpecical));
                smtPublishOperationLog.setObject(JSON.toJSONString(prohibitionDOS));
                smtPublishOperationLog.setState(match ? 1 : 0);
                smtPublishOperationLog.setCreatedTime(LocalDateTime.now());
                smtPublishOperationLogService.save(smtPublishOperationLog);
            }
        } catch (Exception e) {
            String errorMsg = "调用检查禁售方法报错：" + e.getMessage();
            return ApiResult.newError(errorMsg);
        }
        return ApiResult.newSuccess(checkSku);
    }


    /**
     * 检查SKU是否已在产品系统中被SMT平台禁售
     * 使用分批处理避免大量SKU导致的接口调用失败
     *
     * @param skuList SKU货号列表
     * @param prohibitionDOS 禁售配置列表
     * @return ApiResult包装的SKU禁售状态映射，key为SKU，value为是否禁售
     */
    public static ApiResult<Map<String, Boolean>> checkSkuForbiddenInProductSystem(List<String> skuList, List<ProhibitionDO> prohibitionDOS) {
        if (CollectionUtils.isEmpty(skuList)) {
            return ApiResult.newError("SKU列表不能为空");
        }

        if (CollectionUtils.isEmpty(prohibitionDOS)) {
            return ApiResult.newError("禁售配置列表不能为空");
        }

        // 校验禁售结果映射
        Map<String, Boolean> checkSku = new HashMap<>();

        try {
            // 使用分批处理，每1000条SKU为一批，避免接口调用失败
            final int batchSize = 1000;
            Map<String, ForbiddenAndSpecical> sonskuForbiddenAndSpecicalMap =
                ProductInfringementForbiddenSaleUtils.getForbiddenAndSpecicalBySonSkuBatch(skuList, batchSize);

            // 遍历所有SKU，检查禁售状态
            for (String sku : skuList) {
                if (!sonskuForbiddenAndSpecicalMap.containsKey(sku)) {
                    // SKU未找到禁售信息，标记为未禁售
                    checkSku.put(sku, false);
                    continue;
                }

                // 获取SKU的禁售和特殊标签信息
                ForbiddenAndSpecical forbiddenAndSpecical = sonskuForbiddenAndSpecicalMap.get(sku);

                // 判断是否匹配禁售规则
                boolean match = InfringementForbiddenSaleHelper.isMatch(forbiddenAndSpecical, prohibitionDOS);
                checkSku.put(sku, match);
            }

            return ApiResult.newSuccess(checkSku);

        } catch (Exception e) {
            String errorMsg = "检查SKU禁售状态时发生异常: " + e.getMessage();
            return ApiResult.newError(errorMsg);
        }
    }
}
