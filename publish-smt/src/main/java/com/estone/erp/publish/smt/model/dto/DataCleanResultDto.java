package com.estone.erp.publish.smt.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 数据清理结果DTO
 * 用于返回数据清理操作的详细统计信息
 * 
 * <AUTHOR>
 * @date 2024-07-21
 */
@Data
public class DataCleanResultDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 清理开始时间
     */
    private Timestamp startTime;

    /**
     * 清理结束时间
     */
    private Timestamp endTime;

    /**
     * 清理耗时（毫秒）
     */
    private Long duration;

    /**
     * 删除的重复记录数量
     */
    private Integer deletedCount;

    /**
     * 清理前总记录数
     */
    private Integer totalRecordsBefore;

    /**
     * 清理后总记录数
     */
    private Integer totalRecordsAfter;

    /**
     * 是否清理成功
     */
    private Boolean success;

    /**
     * 清理结果描述信息
     */
    private String message;

    /**
     * 错误信息（如果清理失败）
     */
    private String errorMessage;

    /**
     * 分批处理的批次数量
     */
    private Integer batchCount;

    /**
     * 每批处理的记录数量
     */
    private Integer batchSize;

    public DataCleanResultDto() {
        this.startTime = new Timestamp(System.currentTimeMillis());
        this.success = true;
        this.deletedCount = 0;
        this.batchCount = 0;
        this.batchSize = 500; // 默认批次大小
    }

    /**
     * 设置清理完成时间并计算耗时
     */
    public void setEndTimeAndCalculateDuration() {
        this.endTime = new Timestamp(System.currentTimeMillis());
        if (this.startTime != null) {
            this.duration = this.endTime.getTime() - this.startTime.getTime();
        }
    }

    /**
     * 生成清理结果描述信息
     */
    public void generateMessage() {
        if (this.success) {
            if (this.deletedCount > 0) {
                this.message = String.format("数据清理完成！共删除%d条重复记录，分%d批次处理，耗时%dms", 
                    this.deletedCount, this.batchCount, this.duration);
            } else {
                this.message = "数据清理完成！未发现重复数据，无需清理";
            }
        } else {
            this.message = "数据清理失败：" + (this.errorMessage != null ? this.errorMessage : "未知错误");
        }
    }
}
