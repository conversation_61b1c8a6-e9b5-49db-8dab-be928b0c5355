package com.estone.erp.publish.tidb.publishtidb.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.smt.enums.OperateLogEnum;
import com.estone.erp.publish.smt.model.AliexpressOperateLog;
import com.estone.erp.publish.smt.service.AliexpressOperateLogService;
import com.estone.erp.publish.smt.util.AliexpressOperateLogUtils;
import com.estone.erp.publish.tidb.publishtidb.model.SmtViolationSkuInfringementRule;
import com.estone.erp.publish.tidb.publishtidb.service.SmtViolationSkuInfringementRuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * smt违规处罚-sku禁售规则 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Slf4j
@RestController
@RequestMapping("/smtViolationSkuInfringementRule")
public class SmtViolationSkuInfringementRuleController {
    @Resource
    private SmtViolationSkuInfringementRuleService smtViolationSkuInfringementRuleService;

    @Resource
    private AliexpressOperateLogService aliexpressOperateLogService;

    /**
     * 查询sku禁售规则列表
     *
     * @return
     */
    @PostMapping(value = "/queryList")
    public ApiResult<List<SmtViolationSkuInfringementRule>> queryList() {
        LambdaQueryWrapper<SmtViolationSkuInfringementRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(SmtViolationSkuInfringementRule::getId);
        List<SmtViolationSkuInfringementRule> list = smtViolationSkuInfringementRuleService.list(wrapper);
        return ApiResult.newSuccess(list);
    }

    /**
     * 批量插入或更新sku禁售规则
     * 当对象的id字段为空时执行插入操作，当id字段不为空时执行更新操作
     *
     * @param list 待处理的违规处罚-sku禁售规则列表
     * @return 操作结果
     */
    @PostMapping("/batchSaveOrUpdate")
    public ApiResult<String> batchSaveOrUpdate(@RequestBody List<SmtViolationSkuInfringementRule> list) {
        // 参数校验
        if (CollectionUtils.isEmpty(list)) {
            return ApiResult.newError("批量更新数据不能为空");
        }

        try {
            String currentUser = WebUtils.getUserName();
            LocalDateTime currentTime = LocalDateTime.now();


            Set<String> subType2PunishCauseSet = new HashSet<>();

            for (SmtViolationSkuInfringementRule rule : list) {
                // 校验违规类型和违规原因是否为空
                if (StringUtils.isNotBlank(rule.getSubTypeStr()) && StringUtils.isNotBlank(rule.getPunishCause())) {

                    String sortSubTypeStr = CommonUtils.splitList(rule.getSubTypeStr(), ",")
                            .stream().sorted().collect(Collectors.joining(","));


                    if (!subType2PunishCauseSet.add(sortSubTypeStr + "_" + rule.getPunishCause())) {
                        return ApiResult.newError(String.format("当前违规类型【%s】+违规原因【%s】已存在数据，请修改后重新添加",
                                rule.getSubTypeStr(), rule.getPunishCause()));
                    }
                }
            }

            // 遍历处理每个规则对象
            for (SmtViolationSkuInfringementRule rule : list) {
                if (rule.getId() == null) {
                    // id为空时执行插入操作
                    rule.setCreatedTime(currentTime);
                    rule.setUpdatedTime(currentTime);
                    rule.setOperatedBy(currentUser);
                    smtViolationSkuInfringementRuleService.save(rule);
                    log.info("新增违规处罚-sku禁售规则成功，规则ID：{}", rule.getId());
                } else {
                    // id不为空时执行更新操作
                    // 先查询原始数据用于记录操作日志
                    SmtViolationSkuInfringementRule originalRule = smtViolationSkuInfringementRuleService.getById(rule.getId());
                    if (originalRule == null) {
                        log.warn("未找到ID为{}的违规处罚-sku禁售规则，跳过更新", rule.getId());
                        continue;
                    }

                    rule.setCreatedTime(originalRule.getCreatedTime());
                    // 设置更新时间和操作人
                    rule.setUpdatedTime(currentTime);
                    rule.setOperatedBy(currentUser);

                    // 执行更新操作
                    smtViolationSkuInfringementRuleService.updateById(rule);

                    // 记录操作日志
                    try {
                        List<AliexpressOperateLog> logs = AliexpressOperateLogUtils.buildUpdateLog(
                                originalRule, rule, OperateLogEnum.UPDATE_VIOLATION_SKU_INFRINGEMENT_RULE, "id");
                        if (CollectionUtils.isNotEmpty(logs)) {
                            aliexpressOperateLogService.batchInsert(logs);
                        }
                    } catch (Exception e) {
                        log.error("记录操作日志失败，规则ID：{}", rule.getId(), e);
                    }

                    log.info("更新违规处罚-sku禁售规则成功，规则ID：{}，操作人：{}", rule.getId(), currentUser);
                }
            }

            return ApiResult.newSuccess("批量更新成功");

        } catch (Exception e) {
            log.error("批量更新违规处罚-sku禁售规则失败", e);
            return ApiResult.newError("批量更新失败：" + e.getMessage());
        }
    }
}
