package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * smt违规处罚-sku禁售推送日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("smt_violation_sku_infringement_push_log")
public class SmtViolationSkuInfringementPushLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 处罚日志主键
     */
    private Long recordId;

    /**
     * sku对应的单品货号
     */
    private String articleNumber;

    /**
     * 禁售类型
     */
    private String infringementTypeName;

    /**
     * 禁售原因
     */
    private String infringementObj;

    /**
     * 推送时间（刊登系统推送给产品系统的时间）
     */
    private LocalDateTime createdTime;

    /**
     * 结果状态（0-失败 1-成功）
     */
    private Integer resultStatus;


    /**
     * 结果信息
     */
    private String resultMsg;
}
