package com.estone.erp.publish.smt.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.component.converter.BooleanCodeConverter;
import com.estone.erp.publish.component.converter.TimestampFormatConverter;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 违规数据导出
 */
@Data
public class SmtViolationPunishRecordExcel {

    @ExcelProperty(value = "图片")
    private String img;

    @ExcelProperty(value = "店铺")
    private String account;

    @ExcelProperty(value = "商品id")
    private String productId;

    @ExcelProperty(value = "SKU")
    private String articleNumber;

    @ExcelProperty(value = "标题")
    private String title;


    @ExcelProperty(value = "是否设置禁售", converter = BooleanCodeConverter.class)
    private Boolean banFlag;

    @ExcelProperty(value = "禁售类型")
    private String infringementTypeName;

    @ExcelProperty(value = "禁售原因")
    private String infringementObj;

    @ExcelProperty(value = "设置禁售时间", converter = TimestampFormatConverter.class)
    private Timestamp banTime;

    @ExcelProperty(value = "违规规则类型")
    private String subRulesType;

    @ExcelProperty(value = "违规类型")
    private String subType;

    @ExcelProperty(value = "违规编号")
    private String punishId;

    @ExcelProperty(value = "违规对象")
    private String assetTypes;

    @ExcelProperty(value = "处罚时间", converter = TimestampFormatConverter.class)
    private Timestamp punishTime;

    @ExcelProperty(value = "记分计次类型")
    private String scoreCountType;

    @ExcelProperty(value = "扣分")
    private Integer pointScore;

    @ExcelProperty(value = "计次")
    private Integer pointCount;

    @ExcelProperty(value = "状态")
    private String showStatus;

    @ExcelProperty(value = "违规原因")
    private String punishCause;

    @ExcelProperty(value = "违规影响")
    private String punishInfluence;

    @ExcelProperty(value = "销售")
    private String salemanager;

    @ExcelProperty(value = "销售组长")
    private String salemanagerLeader;

    @ExcelProperty(value = "销售主管")
    private String salesSupervisorName;

    @ExcelProperty(value = "爬取时间", converter = TimestampFormatConverter.class)
    private Timestamp crawlTime;

    @ExcelProperty(value = "同步时间", converter = TimestampFormatConverter.class)
    private Timestamp createDate;

    @ExcelProperty(value = "操作建议")
    private String prompt;

}


