package com.estone.erp.publish.smt.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批量设置SKU禁售请求DTO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmtBatchSetBanDTO {


    /**
     * 禁售类型
     */
    private String infringementTypeName;

    /**
     * 禁售原因
     */
    private String infringementObj;
}
