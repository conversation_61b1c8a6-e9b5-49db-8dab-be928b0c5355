package com.estone.erp.publish.smt.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.smt.enums.ExcelTypeEnum;
import com.estone.erp.publish.smt.model.SmtViolationPunishRecord;
import com.estone.erp.publish.smt.model.SmtViolationPunishRecordCriteria;
import com.estone.erp.publish.smt.model.SmtViolationPunishRecordExample;
import com.estone.erp.publish.smt.model.dto.SmtBatchSetBanDTO;
import com.estone.erp.publish.smt.mq.excel.ExcelSend;
import com.estone.erp.publish.smt.service.SmtViolationPunishRecordService;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * 2024-05-24 11:25:09
 */
@RestController
@RequestMapping("smtViolationPunishRecord")
public class SmtViolationPunishRecordController {
    @Resource
    private SmtViolationPunishRecordService smtViolationPunishRecordService;
    @Resource
    private PermissionsHelper permissionsHelper;
    @Resource
    private ExcelSend excelSend;

    @PostMapping
    public ApiResult<?> postSmtViolationPunishRecord(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchSmtViolationPunishRecord": {
                    // 查询列表
                    CQuery<SmtViolationPunishRecordCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<SmtViolationPunishRecordCriteria>>() {
                    });
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    SmtViolationPunishRecordCriteria search = cquery.getSearch();
                    Asserts.isTrue(search != null, ErrorCode.PARAM_EMPTY_ERROR);
                    try {
                        isSmtAuth(search);
                    } catch (Exception e) {
                        return ApiResult.newError(e.getMessage());
                    }
                    CQueryResult<SmtViolationPunishRecord> results = smtViolationPunishRecordService.search(cquery);
                    return results;
                }
                case "addSmtViolationPunishRecord": {
                    // 添加
                    SmtViolationPunishRecord smtViolationPunishRecord = requestParam.getArgsValue(new TypeReference<SmtViolationPunishRecord>() {
                    });
                    smtViolationPunishRecordService.insert(smtViolationPunishRecord);
                    return ApiResult.newSuccess(smtViolationPunishRecord);
                }
                case "download": {
                    //导出
                    CQuery<SmtViolationPunishRecordCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<SmtViolationPunishRecordCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    SmtViolationPunishRecordCriteria search = cquery.getSearch();
                    Asserts.isTrue(search != null, ErrorCode.PARAM_EMPTY_ERROR);
                    try {
                        isSmtAuth(search);
                    } catch (Exception e) {
                        return ApiResult.newError(e.getMessage());
                    }
                    SmtViolationPunishRecordExample example = search.getExample();
                    int count = smtViolationPunishRecordService.countByExample(example);
                    if (count > 500000) {
                        return ApiResult.newError("导出的数据不可超过50w条");
                    }
                    ResponseJson responseJson = excelSend.downloadSend(ExcelTypeEnum.downloadViolationPunishRecord.getCode(), cquery);
                    if(!responseJson.isSuccess()){
                        return ApiResult.newError(responseJson.getMessage());
                    }
                    return ApiResult.newSuccess();
                }
            }

        }
        return ApiResult.newSuccess();
    }

    public void isSmtAuth(SmtViolationPunishRecordCriteria query) {
        List<String> accountNumbers = query.getAccountList();
        List<String> managerIds = CommonUtils.splitList(query.getSalesSupervisorName(), ",");
        List<String> leaderIds = CommonUtils.splitList(query.getSalemanagerLeader(), ",");
        List<String> saleIds = CommonUtils.splitList(query.getSalemanager(), ",");
        List<Integer> groupIds = null;
        List<String> authAccountNumbers = permissionsHelper.smtAuth(accountNumbers, managerIds, leaderIds, saleIds, groupIds, "0", false);
        query.setAccountList(authAccountNumbers);
    }

    /**
     * 获取下拉类型
     * @return
     */
    @GetMapping(value = "/getTypes")
    public ApiResult<?> getTypes() {
        Map<String,List<String>> map = new HashMap<>();
        List<String> typeList = Arrays.asList("sub_type", "asset_types", "show_status", "score_count_type", "sub_rules_type", "punish_cause");
        for (String type : typeList) {
            List<String> strings = smtViolationPunishRecordService.selectType(type);
            strings.removeIf(Objects::isNull);
            if(StringUtils.equalsIgnoreCase("sub_type", type)){
                type = "subType";
            }else if(StringUtils.equalsIgnoreCase("asset_types", type)){
                type = "assetTypes";
            }else if(StringUtils.equalsIgnoreCase("show_status", type)){
                type = "showStatus";
            }else if(StringUtils.equalsIgnoreCase("score_count_type", type)){
                type = "scoreCountType";
            }else if(StringUtils.equalsIgnoreCase("sub_rules_type", type)){
                type = "subRulesType";
            } else if (StringUtils.equalsIgnoreCase("punish_cause", type)) {
                type = "punishCause";
            }
            map.put(type, strings);
        }
        return ApiResult.newSuccess(map);
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getSmtViolationPunishRecord(@PathVariable(value = "id", required = true) Long id) {
        SmtViolationPunishRecord smtViolationPunishRecord = smtViolationPunishRecordService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(smtViolationPunishRecord);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putSmtViolationPunishRecord(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateSmtViolationPunishRecord": // 单个修改
                    SmtViolationPunishRecord smtViolationPunishRecord = requestParam.getArgsValue(new TypeReference<SmtViolationPunishRecord>() {});
                    smtViolationPunishRecordService.updateByPrimaryKeySelective(smtViolationPunishRecord);
                    return ApiResult.newSuccess(smtViolationPunishRecord);
                }
        }
        return ApiResult.newSuccess();
    }

    /**
     * 批量设置SKU禁售
     *
     * @param dto 批量设置禁售请求参数
     * @return 操作结果
     */
    @PostMapping("/batchSetBan")
    public ApiResult<?> batchSetBan(@RequestBody(required = true) ApiRequestParam<String> requestParam) {

        CQuery<SmtViolationPunishRecordCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<SmtViolationPunishRecordCriteria>>() {
        });
        Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
        SmtViolationPunishRecordCriteria search = cquery.getSearch();
        Asserts.isTrue(search != null, ErrorCode.PARAM_EMPTY_ERROR);
        try {
            isSmtAuth(search);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        SmtViolationPunishRecordExample example = search.getExample();

        List<SmtViolationPunishRecord> records = smtViolationPunishRecordService.selectByExample(example);

        try {
            SmtBatchSetBanDTO dto = SmtBatchSetBanDTO.builder().infringementObj(search.getBatchSetInfringementObj()).infringementTypeName(search.getBatchSetInfringementTypeName())
                    .build();
            String result = smtViolationPunishRecordService.batchSetBan(dto, records);
            return ApiResult.newSuccess(result);
        } catch (Exception e) {
            return ApiResult.newError("批量设置SKU禁售失败：" + e.getMessage());
        }
    }
}