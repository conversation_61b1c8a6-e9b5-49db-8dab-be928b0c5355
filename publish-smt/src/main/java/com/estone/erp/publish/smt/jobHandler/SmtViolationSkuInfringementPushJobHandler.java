package com.estone.erp.publish.smt.jobHandler;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.smt.util.SmtViolationSkuInfringementPushUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 【SMT】违规处罚SKU禁售信息推送定时任务
 * 功能说明：
 * 获取违规处罚列表中是否禁售为是的SKU和对应SKU的禁售类型、禁售原因数据，推送给产品系统，数据推送时，需判断当前数据在产品系统是否为SMT禁售，若推送的数据已存在SMT平台禁售时，则不推送数据；
 * 若推送的数据不存在SMT平台禁售时，则推送数据到产品系统，进行标记当前SKU在SMT全站点禁售，禁售类型与原因为刊登系统推送的数据，需记录推送数据信息和推送时间，
 * 1-若SKU存在多条数据时，随机推送一个SKU、禁售类型、禁售原因，推送一次即可，记录推送时间
 * 2-若已推送过的数据，不再重复推送
 * 3-需将SKU、禁售类型、禁售原因记录到推送表中，再判断是否需要推送当前数据
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Component
@Slf4j
public class SmtViolationSkuInfringementPushJobHandler extends AbstractJobHandler {

    public SmtViolationSkuInfringementPushJobHandler() {
        super("SmtViolationSkuInfringementPushJobHandler");
    }

    /**
     * 内部参数类
     */
    @Data
    private static class InnerParam {
        /**
         * 指定的记录ID列表
         * 如果为空或null，则处理所有符合条件的记录
         */
        private List<Long> recordIds;


    }

    @Override
    @XxlJob("SmtViolationSkuInfringementPushJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("-------SMT违规处罚SKU禁售信息推送任务开始--------");
        try {
            // 解析参数
            InnerParam innerParam = parseParam(param);

            // 执行SKU禁售信息推送处理
            String result = SmtViolationSkuInfringementPushUtils.pushViolationSkuInfringement(innerParam.getRecordIds());

            XxlJobLogger.log("任务执行结果: {}", result);
            XxlJobLogger.log("-------SMT违规处罚SKU禁售信息推送任务结束--------");

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("SMT违规处罚SKU禁售信息推送任务执行失败", e);
            XxlJobLogger.log("任务执行失败: {}", e.getMessage());
            return new ReturnT<>(ReturnT.FAIL_CODE, "任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 解析任务参数
     *
     * @param param 参数字符串
     * @return 解析后的参数对象
     */
    private InnerParam parseParam(String param) {
        InnerParam innerParam = new InnerParam();

        if (StringUtils.isBlank(param)) {
            XxlJobLogger.log("参数为空，使用默认参数处理所有符合条件的记录");
            return innerParam;
        }

        try {
            innerParam = super.passParam(param, InnerParam.class);
            return innerParam;
        } catch (Exception e) {
            XxlJobLogger.log("参数解析失败: {}, 将使用默认参数", e.getMessage());
            log.warn("参数解析失败: {}", e.getMessage());
            return new InnerParam();
        }
    }
}
