package com.estone.erp.publish.smt.model;

import com.estone.erp.common.util.CommonUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-05-24 11:25:09
 */
@Data
public class SmtViolationPunishRecordCriteria extends SmtViolationPunishRecord {
    private static final long serialVersionUID = 1L;

    private List<Long> idList;

    // 店铺
    private List<String> accountList;

    //处罚时间
    private String fromPunishTime;
    private String toPunishTime;

    //爬取时间
    private String fromCrawlTime;
    private String toCrawlTime;

    //同步时间
    private String fromLastSynchTime;
    private String toLastSynchTime;

    //自定义导出内容
    private List<String> downValueList;

    // 是否设置禁售
    private Boolean banFlag;

    // 禁售原因列表
    private List<String> infringementObjs;

    // 禁售类型列表
    private List<String> infringementTypeNames;

    // 设置禁售时间-开始时间
    private String fromBanTime;

    // 设置禁售时间-结束时间
    private String toBanTime;


    /**
     * 批量设置SKU禁售-禁售类型（非查询参数）
     */
    private String batchSetInfringementTypeName;

    /**
     * 批量设置SKU禁售-禁售原因（非查询参数）
     */
    private String batchSetInfringementObj;

    public SmtViolationPunishRecordExample getExample() {
        SmtViolationPunishRecordExample example = new SmtViolationPunishRecordExample();
        SmtViolationPunishRecordExample.Criteria criteria = example.createCriteria();
        if(CollectionUtils.isNotEmpty(this.getIdList())){
            criteria.andIdIn(this.getIdList());
        }
        //处罚时间
        if(StringUtils.isNotBlank(this.getFromPunishTime())){
            criteria.andPunishTimeGreaterThanOrEqualTo(this.getFromPunishTime());
        }
        if(StringUtils.isNotBlank(this.getToPunishTime())) {
            criteria.andPunishTimeLessThanOrEqualTo(this.getToPunishTime());
        }
        //爬取时间
        if(StringUtils.isNotBlank(this.getFromCrawlTime())){
            criteria.andCrawlTimeGreaterThanOrEqualTo(this.getFromCrawlTime());
        }
        if(StringUtils.isNotBlank(this.getToCrawlTime())) {
            criteria.andCrawlTimeLessThanOrEqualTo(this.getToCrawlTime());
        }
        //同步时间
        if(StringUtils.isNotBlank(this.getFromLastSynchTime())){
            criteria.andCreateDateGreaterThanOrEqualTo(this.getFromLastSynchTime());
        }
        if(StringUtils.isNotBlank(this.getToLastSynchTime())){
            criteria.andCreateDateLessThanOrEqualTo(this.getToLastSynchTime());
        }

        if(StringUtils.isNotBlank(this.getScoreCountType())){
            List<String> strings = CommonUtils.splitList(this.getScoreCountType(), ",");
            criteria.andScoreCountTypeIn(strings);
        }
        if(StringUtils.isNotBlank(this.getSubRulesType())){
            List<String> strings = CommonUtils.splitList(this.getSubRulesType(), ",");
            criteria.andSubRulesTypeIn(strings);
        }
        if (StringUtils.isNotBlank(this.getImg())) {
            criteria.andImgEqualTo(this.getImg());
        }
        if (StringUtils.isNotBlank(this.getAccount())) {
            criteria.andAccountEqualTo(this.getAccount());
        }
        if(CollectionUtils.isNotEmpty(this.getAccountList())){
            criteria.andAccountIn(this.getAccountList());
        }
        if (StringUtils.isNotBlank(this.getSellerId())) {
            criteria.andSellerIdEqualTo(this.getSellerId());
        }
        if (StringUtils.isNotBlank(this.getProductId())) {
            List<String> strings = CommonUtils.splitList(this.getProductId(), ",");
            criteria.andProductIdIn(strings);
        }
        if (StringUtils.isNotBlank(this.getSkuCode())) {
            List<String> strings = CommonUtils.splitList(this.getSkuCode(), ",");
            criteria.andSkuCodeIn(strings);
        }
        if (StringUtils.isNotBlank(this.getArticleNumber())) {
            List<String> strings = CommonUtils.splitList(this.getArticleNumber(), ",");
            criteria.andArticleNumberIn(strings);
        }
        if(StringUtils.isNotBlank(this.getTitle())){
            criteria.andTitleLike("%" + this.getTitle() + "%");
        }
        if (StringUtils.isNotBlank(this.getSubType())) {
            List<String> strings = CommonUtils.splitList(this.getSubType(), ",");
            criteria.andSubTypeIn(strings);
        }
        if (StringUtils.isNotBlank(this.getPunishId())) {
            List<String> strings = CommonUtils.splitList(this.getPunishId(), ",");
            criteria.andPunishIdIn(strings);
        }
        if (StringUtils.isNotBlank(this.getAssetTypes())) {
            List<String> strings = CommonUtils.splitList(this.getAssetTypes(), ",");
            criteria.andAssetTypesIn(strings);
        }
        if (this.getPunishTime() != null) {
            criteria.andPunishTimeEqualTo(this.getPunishTime());
        }
        if (this.getPointScore() != null) {
            criteria.andPointScoreEqualTo(this.getPointScore());
        }
        if (this.getPointCount() != null) {
            criteria.andPointCountEqualTo(this.getPointCount());
        }
        if (StringUtils.isNotBlank(this.getShowStatus())) {
            List<String> strings = CommonUtils.splitList(this.getShowStatus(), ",");
            criteria.andShowStatusIn(strings);
        }
        if (StringUtils.isNotBlank(this.getPunishCause())) {
            criteria.andPunishCauseEqualTo(this.getPunishCause());
        }
        if (StringUtils.isNotBlank(this.getPunishInfluence())) {
            criteria.andPunishInfluenceEqualTo(this.getPunishInfluence());
        }
        if (StringUtils.isNotBlank(this.getPrompt())) {
            criteria.andPromptEqualTo(this.getPrompt());
        }
        if (this.getCrawlTime() != null) {
            criteria.andCrawlTimeEqualTo(this.getCrawlTime());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (this.getUpdateDate() != null) {
            criteria.andUpdateDateEqualTo(this.getUpdateDate());
        }

        // 是否设置禁售
        if (this.getBanFlag() != null) {
            criteria.andBanFlagEqualTo(this.getBanFlag());
        }

        // 禁售原因列表
        if (CollectionUtils.isNotEmpty(this.getInfringementObjs())) {
            criteria.andInfringementObjIn(this.getInfringementObjs());
        }

        // 禁售类型列表
        if (CollectionUtils.isNotEmpty(this.getInfringementTypeNames())) {
            criteria.andInfringementTypeNameIn(this.getInfringementTypeNames());
        }

        // 设置禁售时间范围查询
        if (StringUtils.isNotBlank(this.getFromBanTime())) {
            criteria.andBanTimeGreaterThanOrEqualTo(this.getFromBanTime());
        }
        if (StringUtils.isNotBlank(this.getToBanTime())) {
            criteria.andBanTimeLessThanOrEqualTo(this.getToBanTime());
        }

        if(StringUtils.isBlank(example.getOrderByClause())){
            example.setOrderByClause("product_id, punish_time desc");
        }

        return example;
    }
}