package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * smt违规处罚-sku禁售规则
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("smt_violation_sku_infringement_rule")
public class SmtViolationSkuInfringementRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 违规类型-用逗号分割
     */
    private String subTypeStr;

    /**
     * 违规原因
     */
    private String punishCause;

    /**
     * 扣分-起始值
     */
    private Integer pointScoreStart;

    /**
     * 扣分-结束值
     */
    private Integer pointScoreEnd;

    /**
     * 计次-起始值
     */
    private Integer pointCountStart;

    /**
     * 计次-结束值
     */
    private Integer pointCountEnd;

    /**
     * 禁售类型
     */
    private String infringementTypeName;

    /**
     * 禁售原因
     */
    private String infringementObj;

    /**
     * 状态 0 禁用 1 启用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 修改时间
     */
    private LocalDateTime updatedTime;

    /**
     * 操作人
     */
    private String operatedBy;


}
