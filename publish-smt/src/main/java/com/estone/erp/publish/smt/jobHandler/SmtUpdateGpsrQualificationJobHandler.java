package com.estone.erp.publish.smt.jobHandler;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.publish.common.executors.AliexpressExecutors;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.smt.bean.SpuUpdateQualification;
import com.estone.erp.publish.smt.enums.ProductStatusTypeEnum;
import com.estone.erp.publish.smt.model.AliexpressConfig;
import com.estone.erp.publish.smt.model.AliexpressConfigExample;
import com.estone.erp.publish.smt.service.AliexpressConfigService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 链接缺失GPSR合规标签资质图，系统自动提交资质图定时任务
 * 定时频率：每三天一次
 * 定时内容：获取在线列表在售状态为上架、且是否存在资质图为GPSR合规标签为否的链接，
 * 系统每隔三天自动去提交上传资质图，记录修改资质的处理报告，
 * 记录店铺、商品ID、结果状态和操作人操作时间，
 * 修改成功后将是否存在资质图为GPSR合规标签改为是；
 * 若上传资质时检测到GPSR图片为空白时，无需提交到平台，仅记录处理报告，提交失败，备注：无GPSR图片，不修改资质
 */
@Slf4j
@Component
public class SmtUpdateGpsrQualificationJobHandler extends AbstractJobHandler {

    public SmtUpdateGpsrQualificationJobHandler() {
        super("SmtUpdateGpsrQualificationJobHandler");
    }

    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;

    @Resource
    private AliexpressConfigService aliexpressConfigService;

    @Resource
    private RabbitMqSender rabbitMqSender;

    @Data
    static class InnerParam {
        /**
         * 产品ids
         */
        private List<Long> productIdList;
        /**
         * 店铺账号列表
         */
        private List<String> accountList;
    }

    @Override
    @XxlJob("SmtUpdateGpsrQualificationJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        try {
            // 解析参数
            InnerParam innerParam = parseParam(param);

            doGpsrQualificationData(innerParam.getProductIdList(), innerParam.getAccountList());

            XxlJobLogger.log("-------链接缺失GPSR合规标签资质图自动提交任务结束--------");

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("链接缺失GPSR合规标签资质图自动提交任务执行失败", e);
            XxlJobLogger.log("任务执行失败: {}", e.getMessage());
            return new ReturnT<>(ReturnT.FAIL_CODE, "任务执行失败: " + e.getMessage());
        }
    }

    /**
     * GPSR合规标签资质图数据处理
     */
    private void doGpsrQualificationData(List<Long> productIds, List<String> accountList) {

        // 获取启用自动修改资质的店铺配置
        AliexpressConfigExample configExample = new AliexpressConfigExample();
        configExample.createCriteria().andUsableEqualTo(true).andAutoUpdateQualificationsEqualTo(true);
        List<AliexpressConfig> aliexpressConfigs = aliexpressConfigService.selectByExample(configExample);
        if (CollectionUtils.isEmpty(aliexpressConfigs)) {
            XxlJobLogger.log("没有启用店铺自动修改资质！");
            return;
        }

        List<String> passAccountList = aliexpressConfigs.stream().map(AliexpressConfig::getAccount).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(accountList)) {
            passAccountList = accountList.stream().filter(passAccountList::contains).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(passAccountList)) {
                XxlJobLogger.log("入参店铺 没有启用店铺自动修改资质！");
                return;
            }
        }

        // 遍历每个店铺处理GPSR资质图
        for (String account : passAccountList) {
            processAccountGpsrQualification(account, productIds);
        }
    }

    /**
     * 处理单个店铺的GPSR资质图
     */
    private void processAccountGpsrQualification(String account, List<Long> productIds) {
        XxlJobLogger.log("开始处理店铺：" + account + " 的GPSR资质图");

        // 构建查询条件
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        // 上架状态
        request.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
        // 不存在GPSR图片（GPSR合规标签为否）
        request.setExistIsHasGPSRImg(false);
        // 默认不查产品详情
        request.setIsSeleteProduct(false);

        if (CollectionUtils.isNotEmpty(productIds)) {
            request.setProductIdList(productIds);
        }

        String[] fields = {"categoryId", "productId", "aliexpressAccountNumber", "spu"};
        request.setQueryFields(fields);
        request.setOrderBy("createTime");
        request.setAliexpressAccountNumber(account);

        int pageSize = 1000;
        int pageIndex = 0;
        request.setPageSize(pageSize);

        int size = 0;
        while (true) {
            Page<EsAliexpressProductListing> page = esAliexpressProductListingService.page(request, pageSize, pageIndex);
            List<EsAliexpressProductListing> esAliexpressProductListing = page.getContent();

            if (CollectionUtils.isEmpty(esAliexpressProductListing)) {
                break;
            }

            // 根据 productId 去重，避免重复推送
            Collection<EsAliexpressProductListing> sendList = esAliexpressProductListing
                    .stream()
                    .collect(Collectors.toMap(EsAliexpressProductListing::getProductId, Function.identity(), (old, newV) -> newV))
                    .values();

            for (EsAliexpressProductListing productListing : sendList) {
                AliexpressExecutors.executeCheckQualificationTask(() -> {
                    processGpsrQualificationForProduct(productListing);
                });
            }

            size = size + sendList.size();
            pageIndex++;
        }
        XxlJobLogger.log(account + " 查询到需要处理GPSR资质图的产品数量（productId去重）:" + size);

    }

    /**
     * 处理单个产品的GPSR资质图
     */
    private void processGpsrQualificationForProduct(EsAliexpressProductListing productListing) {
        try {
            SpuUpdateQualification spuUpdateQualification = new SpuUpdateQualification();
            spuUpdateQualification.setAliexpressAccountNumber(productListing.getAliexpressAccountNumber());
            spuUpdateQualification.setCategoryId(productListing.getCategoryId());
            spuUpdateQualification.setProductId(productListing.getProductId());
            spuUpdateQualification.setSpu(productListing.getSpu());
            spuUpdateQualification.setUserName("SmtUpdateGpsrQualificationJobHandler");
            // 设置只处理GPSR资质图类型（1代表GPSR合规标签）
            spuUpdateQualification.setUpdateQualificationTypeList(List.of(1));

            // 发送到MQ队列进行处理
            rabbitMqSender.send(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_UPDATE_QUALIFICATION_ROUTE_KEY, spuUpdateQualification);

        } catch (Exception e) {
            log.error("处理产品ID：{} 的GPSR资质图时发生异常", productListing.getProductId(), e);
            XxlJobLogger.log("处理产品ID：" + productListing.getProductId() + " 时发生异常：" + e.getMessage(), e);
        }
    }

    /**
     * 解析任务参数
     *
     * @param param 参数字符串
     * @return 解析后的参数对象
     */
    private InnerParam parseParam(String param) {
        InnerParam innerParam = new InnerParam();

        if (StringUtils.isBlank(param)) {
            XxlJobLogger.log("参数为空，使用默认参数处理所有符合条件的记录");
            return innerParam;
        }

        try {
            innerParam = super.passParam(param, InnerParam.class);
            return innerParam;
        } catch (Exception e) {
            XxlJobLogger.log("参数解析失败: {}, 将使用默认参数", e.getMessage());
            log.warn("参数解析失败: {}", e.getMessage());
            return new InnerParam();
        }
    }
}
