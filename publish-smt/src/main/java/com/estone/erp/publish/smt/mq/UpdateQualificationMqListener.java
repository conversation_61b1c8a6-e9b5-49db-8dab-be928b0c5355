package com.estone.erp.publish.smt.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.smt.bean.SpuUpdateQualification;
import com.estone.erp.publish.smt.enums.OperateLogTypeEnum;
import com.estone.erp.publish.smt.model.dto.AliexpressCategoryQualificationVo;
import com.estone.erp.publish.smt.model.dto.AliexpressSpuCategoryQualificationVo;
import com.estone.erp.publish.smt.service.AliexpressEsExtendService;
import com.estone.erp.publish.smt.util.AliexpressLogUtils;
import com.estone.erp.publish.smt.util.AliexpressQualificationUtils;
import com.estone.erp.publish.smt.util.ExecutorJudgeUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

/**
 * Smt 修改资质监听队列
 */
@Component
@Slf4j
public class UpdateQualificationMqListener implements ChannelAwareMessageListener {

    @Resource
    private AliexpressEsExtendService aliexpressEsExtendService;

    /**
     * 页面选择修改的内容
     */
    private final static Map<Integer, List<String>> checkLabelMap = new HashMap<>(){
        {
            super.put(1, Arrays.asList(AliexpressQualificationUtils.PACKAGE_IMAGE_GPSR_LABEL));
            super.put(2, Arrays.asList(AliexpressQualificationUtils.PRODUCT_STOCK_IMAGE_LABEL));
            super.put(3, Arrays.asList(AliexpressQualificationUtils.PACKAGE_IMAGE_EU_LABEL,AliexpressQualificationUtils.PACKAGE_IMAGE_LABEL));
        }
    };

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), "UTF-8");
        try {
            if(StringUtils.isBlank(body)) {
                throw new RuntimeException("body to String is null");
            }
            SpuUpdateQualification spuUpdateQualification = JSON.parseObject(body, new TypeReference<SpuUpdateQualification>() {

            });

            if(spuUpdateQualification == null || spuUpdateQualification.getProductId() == null) {
                throw new RuntimeException(body + " 数据异常！");
            }

            List<Integer> updateQualificationTypeList = spuUpdateQualification.getUpdateQualificationTypeList();

            boolean sign = false;
            try {
                Integer categoryId = spuUpdateQualification.getCategoryId();
                Long productId = spuUpdateQualification.getProductId();
                String spu = spuUpdateQualification.getSpu();
                String aliexpressAccountNumber = spuUpdateQualification.getAliexpressAccountNumber();
                String userName = spuUpdateQualification.getUserName();
                String packageImageUrl = spuUpdateQualification.getPackageImageUrl();


                // 是否特殊处理的修改GPSR的定时任务
                boolean isSpecialGpsrJob = ExecutorJudgeUtils.judgeIsJobExecutor(userName, "SmtUpdateGpsrQualificationJobHandler");
                // 是否特殊处理的修改外包装图的定时任务
                boolean isSpecialPackageJob = ExecutorJudgeUtils.judgeIsJobExecutor(userName, "SmtUpdatePackageQualificationJobHandler");
                SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                List<AliexpressCategoryQualificationVo> categoryQualificationInfo = AliexpressQualificationUtils.getCategoryQualificationInfo(saleAccountAndBusinessResponse, categoryId);
                if(CollectionUtils.isNotEmpty(categoryQualificationInfo)){
                    List<AliexpressSpuCategoryQualificationVo> spuCategoryQualification = AliexpressQualificationUtils.init(categoryQualificationInfo, spu);
                    //  SmtUpdatePackageQualificationJobHandler定时任务特殊处理 （区别：直接使用仓库系统查询的SKU外包装图链接URL）
                    if (isSpecialPackageJob) {
                        AliexpressQualificationUtils.setSpuQualification(spuCategoryQualification, List.of(packageImageUrl));
                    } else {
                        AliexpressQualificationUtils.setSpuQualification(spuCategoryQualification, spu, categoryId, true);
                    }

                    if (isSpecialGpsrJob) {
                        boolean noHaveValue = spuCategoryQualification.stream()
                                .filter(qualification -> null != qualification.getCategoryQualificationVo() &&
                                        AliexpressQualificationUtils.PACKAGE_IMAGE_GPSR_KEY.equals(qualification.getCategoryQualificationVo().getKey()))
                                .anyMatch(qualification -> !BooleanUtils.isTrue(qualification.getHasValue()));
                        if (noHaveValue) {
                            String failInfo = "无GPSR图片，不修改资质";
                            // 记录错误日志
                            AliexpressLogUtils.saveProductLog(aliexpressAccountNumber, productId, OperateLogTypeEnum.update_qualification.getCode(),
                                    userName, false, failInfo);
                        }
                    }


                    List<AliexpressSpuCategoryQualificationVo> updateListing = AliexpressQualificationUtils.getHasQualification(spuCategoryQualification);
                    if (CollectionUtils.isNotEmpty(updateListing)) {

                        //如果页面有选择只修改部分 需要过滤
                        if(CollectionUtils.isNotEmpty(updateQualificationTypeList)){
                            List<AliexpressSpuCategoryQualificationVo> checkListing = new ArrayList<>();
                            List<String> checkLableList = new ArrayList<>();
                            for (Integer integer : updateQualificationTypeList) {
                                List<String> lableList = checkLabelMap.get(integer);
                                if(CollectionUtils.isNotEmpty(lableList)){
                                    checkLableList.addAll(lableList);
                                }
                            }
                            for (AliexpressSpuCategoryQualificationVo aliexpressSpuCategoryQualificationVo : updateListing) {
                                AliexpressCategoryQualificationVo categoryQualificationVo = aliexpressSpuCategoryQualificationVo.getCategoryQualificationVo();
                                String label = categoryQualificationVo.getLabel();
                                if(checkLableList.contains(label)){
                                    checkListing.add(aliexpressSpuCategoryQualificationVo);
                                }
                            }
                            if(CollectionUtils.isNotEmpty(checkListing)){
                                JSONArray jsonArray = AliexpressQualificationUtils.getAeopQualificationStructJsonArray(checkListing);
                                aliexpressEsExtendService.updateQualification(saleAccountAndBusinessResponse, productId, jsonArray.toJSONString(), userName, false, false);
                            }
                        }else{
                            JSONArray jsonArray = AliexpressQualificationUtils.getAeopQualificationStructJsonArray(updateListing);
                            aliexpressEsExtendService.updateQualification(saleAccountAndBusinessResponse, productId, jsonArray.toJSONString(), userName, false, false);
                        }
                    }
                }
                sign = true;
            }catch (Exception e) {
                log.error("UpdateQualificationMqListener 执行异常" + e.getMessage(), e);
            }
            try {
                if(sign){
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                }else{
                    // 最后一个参数为true 消息异常依然会回到队列下次继续执行
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                }
            }
            catch (IOException ioe) {
                log.warn("UpdateQualificationMqListener 确认异常" + ioe.getMessage(), ioe);
            }
        }catch (Exception e) {
            log.error("UpdateQualificationMqListener 异常：" + body + e.getMessage(), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            }
            catch (IOException ioe) {
                log.warn("UpdateQualificationMqListener 确认异常：" + ioe.getMessage(), ioe);
            }
        }
    }

}