package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.publish.tidb.publishtidb.model.SmtViolationSkuInfringementPushLog;
import com.estone.erp.publish.tidb.publishtidb.mapper.SmtViolationSkuInfringementPushLogMapper;
import com.estone.erp.publish.tidb.publishtidb.service.SmtViolationSkuInfringementPushLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * smt违规处罚-sku禁售推送日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-22
 */
@Service
public class SmtViolationSkuInfringementPushLogServiceImpl extends ServiceImpl<SmtViolationSkuInfringementPushLogMapper, SmtViolationSkuInfringementPushLog> implements SmtViolationSkuInfringementPushLogService {

}
