package com.estone.erp.publish.smt.util;

import aliexpress.open.param.AeopChildProductGroup;
import aliexpress.open.param.AeopProductGroup;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.smt.enums.OperateLogEnum;
import com.estone.erp.publish.smt.model.AliexpressOperateLog;
import com.estone.erp.publish.smt.service.AliexpressCategoryService;
import com.estone.erp.publish.system.product.enums.SpecialTagEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther yucm
 * @Date 2020/11/9
 */
@Slf4j
public class AliexpressOperateLogUtils {

    /**
     * 忽略字段
     */
    static List<String> filterKey = Arrays.asList("createBy", "createTime", "createDate", "updateBy", "lastUpdatedBy",
            "updateTime", "updateDate", "lastUpdateDate", "aliexpressCategorieList", "categoryIdList", "infoList",
            "rootCategoryIdList", "profitList", "calcPriceList", "positiveRate", "detailedSellerRatings", "feedbackInfo",
            "priceTrialList", "configHalfPriceIntervalList", "configHalfLableList", "priceTrialDeleteIdList",
            "notUpdateStockGroupList","aliexpressAlianceConfigRequest", "groupName", "delivery30dRateUpdateTime",
            "createdBy", "createdTime", "updatedBy", "updatedTime", "operatedBy");

    /**
     * 详细信息字段
     */
    public static List<AliexpressOperateLog> buildUpdateLog(Object oldBean, Object newBean, OperateLogEnum operateLogEnum, String businessId) {
        if (oldBean == null || newBean == null) {
            return null;
        }

        Map<String, String> oldFieldMap = getFieldMap(oldBean, false, businessId);

        // 新对象忽略null 取的是传入修改的数据 null 数据库忽略修改
        Map<String, String> newFieldMap = getFieldMap(newBean, false, businessId);

        List<AliexpressOperateLog> aliexpressOperateLogs = new ArrayList<>();
        for (Map.Entry<String, String> entry : oldFieldMap.entrySet()) {
            String key = entry.getKey();

            // 忽略的字段不比较 新对象不存在 为null忽略
            if(filterKey.contains(key) || !newFieldMap.containsKey(key)){
                continue;
            }

            // 相等不记录日志
            if(entry.getValue().equals(newFieldMap.get(key))) {
                continue;
            }

            // 记录修改日志
            AliexpressOperateLog aliexpressOperateLog = new AliexpressOperateLog();
            aliexpressOperateLogs.add(aliexpressOperateLog);

            if(StringUtils.isNotBlank(businessId)) {
                try {
                    String businessIdValue = oldFieldMap.get(businessId);
                    aliexpressOperateLog.setBusinessId(Integer.valueOf(businessIdValue));
                } catch (Exception e) {}
            }

            aliexpressOperateLog.setType(operateLogEnum.getCode());
            aliexpressOperateLog.setFieldName(key);
            aliexpressOperateLog.setBefore(entry.getValue());
            aliexpressOperateLog.setAfter(newFieldMap.get(key));
            aliexpressOperateLog.setCreateBy(StringUtils.isNotBlank(WebUtils.getUserName()) ? WebUtils.getUserName() : "admin");
            aliexpressOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
        }
        return aliexpressOperateLogs;
    }

    public static Pair<JSONObject, JSONObject> getCompareJsonPair(Object oldBean, Object newBean, List<String> ignoreKey) {
        JSONObject beforeJson = new JSONObject();

        JSONObject afterJson = new JSONObject();
        if (oldBean == null || newBean == null) {
            return Pair.of(beforeJson, afterJson);
        }
        Map<String, String> oldFieldMap = getFieldMap(oldBean, false, null);

        // 新对象忽略null 取的是传入修改的数据 null 数据库忽略修改
        Map<String, String> newFieldMap = getFieldMap(newBean, false, null);


        for (Map.Entry<String, String> entry : oldFieldMap.entrySet()) {
            String key = entry.getKey();

            // 忽略的字段不比较 新对象不存在 为null忽略
            if (ignoreKey.contains(key) || !newFieldMap.containsKey(key)) {
                continue;
            }

            // 相等不记录日志
            if (entry.getValue().equals(newFieldMap.get(key))) {
                continue;
            }
            beforeJson.put(key, entry.getValue());
            afterJson.put(key, newFieldMap.get(key));
        }

        return Pair.of(beforeJson, afterJson);


    }

    /**
     * 新增对象日志
     * @param bean
     * @param operateLogEnum
     * @param businessId
     * @return
     */
    public static AliexpressOperateLog buildAddLog(Object bean, OperateLogEnum operateLogEnum, Integer businessId) {

        AliexpressOperateLog aliexpressOperateLog = new AliexpressOperateLog();
        aliexpressOperateLog.setType(operateLogEnum.getCode());
        aliexpressOperateLog.setBusinessId(businessId);
        aliexpressOperateLog.setFieldName(businessId.toString());
        aliexpressOperateLog.setBefore("");
        aliexpressOperateLog.setAfter("新增");
        aliexpressOperateLog.setMessage(JSON.toJSONString(bean));
        aliexpressOperateLog.setCreateBy(WebUtils.getUserName());
        aliexpressOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));

        return aliexpressOperateLog;
    }

    /**
     * 删除对象日志
     * @param bean
     * @param operateLogEnum
     * @param businessId
     * @return
     */
    public static AliexpressOperateLog buildDelLog(Object bean, OperateLogEnum operateLogEnum, Integer businessId) {
        AliexpressOperateLog aliexpressOperateLog = new AliexpressOperateLog();
        aliexpressOperateLog.setType(operateLogEnum.getCode());
        aliexpressOperateLog.setBusinessId(businessId);
        aliexpressOperateLog.setFieldName(businessId.toString());
        aliexpressOperateLog.setBefore("");
        aliexpressOperateLog.setAfter("删除");
        aliexpressOperateLog.setMessage(JSON.toJSONString(bean));
        aliexpressOperateLog.setCreateBy(WebUtils.getUserName());
        aliexpressOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));

        return aliexpressOperateLog;
    }

    /**
     *  获取map对象
     * @param bean
     * @param ignoreNull
     * @return
     */
    public static Map<String, String> getFieldMap(Object bean, Boolean ignoreNull, String businessId) {

        Map<String, String> fieldMap = new HashMap<>();
        List<Field> oldFieldsList = FieldUtils.getAllFieldsList(bean.getClass());
        for (Field o : oldFieldsList) {
            if(filterKey.contains(o.getName())){
                continue;
            }

            try {
                o.setAccessible(true);
                Object val = o.get(bean);

                if(ignoreNull && null == val) {
                    continue;
                }

                fieldMap.put(o.getName(), val == null ? "" : val.toString());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return fieldMap;
    }

    /**
     * 分类日志特殊处理
     * @param configLog
     */
    public static void changeCategoryLog(AliexpressOperateLog configLog) {
        AliexpressCategoryService aliexpressCategoryService = SpringUtils.getBean(AliexpressCategoryService.class);

        String before = configLog.getBefore();
        List<String> beforeCategaryIds = CommonUtils.splitList(before, ",");

        String after = configLog.getAfter();
        List<String> afterCategaryIds =CommonUtils.splitList(after, ",");
        configLog.setMessage("before:" + before + "----after:" + after);

        // 之前有 且不存在修改之后中则为删除
        List<String> delCategaryIds = beforeCategaryIds.stream()
                .filter(o -> !afterCategaryIds.contains(o)).collect(Collectors.toList());

        // 之前无 且存在修改之后中则为新增
        List<String> addCategaryIds = afterCategaryIds.stream()
                .filter(o -> !beforeCategaryIds.contains(o)).collect(Collectors.toList());
        List<String> afterArr = new ArrayList<>();
        StringBuffer deStringBuffer= new StringBuffer();

        if(CollectionUtils.isNotEmpty(delCategaryIds)) {
//            List<AliexpressCategory> delAliexpressCategorys = aliexpressCategoryService
//                    .selectCategoryZhNameList(delCategaryIds);
            deStringBuffer.append("删除：" +  StringUtils.join(delCategaryIds, ","));
//            for (AliexpressCategory delAliexpressCategory :delAliexpressCategorys) {
//                deStringBuffer.append(delAliexpressCategory.getCategoryZhName() + "(" + delAliexpressCategory.getCategoryId() + "), ");
//            }

            String delStr = deStringBuffer.toString();
            afterArr.add(delStr);
        }

        StringBuffer addStringBuffer = new StringBuffer();
        if(CollectionUtils.isNotEmpty(addCategaryIds)) {
//            List<AliexpressCategory> addAliexpressCategorys = aliexpressCategoryService
//                    .selectCategoryZhNameList(addCategaryIds);
            addStringBuffer.append("增加：" + StringUtils.join(addCategaryIds, ","));
//            for (AliexpressCategory addAliexpressCategory : addAliexpressCategorys) {
//                addStringBuffer.append(addAliexpressCategory.getCategoryZhName() + "(" + addAliexpressCategory.getCategoryId() + "), ");
//            }

            String addStr = addStringBuffer.toString();
            afterArr.add(addStr);
        }

        configLog.setBefore("");
        configLog.setAfter(CollectionUtils.isNotEmpty(afterArr) ? JSON.toJSONString(afterArr) : "");
    }

    /**
     * 标签id 转换name
     * @param skuTags
     * @param skuTagMap
     * @return
     */
    public static String changeTagName(String skuTags, Map<String, String> skuTagMap) {
        if(null == skuTagMap || null == skuTags) {
            return null;
        }

        List<String> tags = CommonUtils.splitList(skuTags, ",");
        if(CollectionUtils.isEmpty(tags)) {
            return null;
        }

        StringBuffer stringBuffer= new StringBuffer();
        for (String beforeTag : tags) {
            String tagName = skuTagMap.get(beforeTag);
            if(StringUtils.isNotBlank(tagName)) {
                stringBuffer.append(tagName + ",");
            } else {
                stringBuffer.append(beforeTag + ",");
            }
        }

        String tagName = stringBuffer.toString();
        if(StringUtils.isNotBlank(tagName)) {
            tagName = tagName.substring(0, tagName.length() - 1);
        }
        return tagName;
    }

    /**
     * id转name
     * @param operateLog
     * @param map
     * @return
     */
    public static void changeLogIdToName(AliexpressOperateLog operateLog, Map<Long, String> map) {
        String before = AliexpressOperateLogUtils.getName(operateLog.getBefore(), map);
        String after = AliexpressOperateLogUtils.getName(operateLog.getAfter(), map);
        operateLog.setBefore(StringUtils.isNotBlank(before) ? before : operateLog.getBefore());
        operateLog.setAfter(StringUtils.isNotBlank(after) ? after : operateLog.getAfter());
    }

    public static void changeIdToName(AliexpressOperateLog operateLog, Map<Integer, String> map) {
        String before = AliexpressOperateLogUtils.getIntName(operateLog.getBefore(), map);
        String after = AliexpressOperateLogUtils.getIntName(operateLog.getAfter(), map);
        operateLog.setBefore(StringUtils.isNotBlank(before) ? before : operateLog.getBefore());
        operateLog.setAfter(StringUtils.isNotBlank(after) ? after : operateLog.getAfter());
    }

    public static String getName(String idStr, Map<Long, String> map) {
        if(StringUtils.isBlank(idStr) || null == map) {
            return null;
        }

        try {
            Long id = Long.parseLong(idStr);

            return map.get(id);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }

    public static String getIntName(String idStr, Map<Integer, String> map) {
        if(StringUtils.isBlank(idStr) || null == map) {
            return null;
        }

        try {
            Integer id = Integer.parseInt(idStr);
            return map.get(id);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }

    public static Map<Long, String> getProductGroupMap(List<AeopProductGroup> productGroups) {
        Map<Long, String> productGroupMap = productGroups.stream()
                .collect(Collectors.toMap(AeopProductGroup::getGroupId, AeopProductGroup::getGroupName));

        for (AeopProductGroup productGroup : productGroups) {
            AeopChildProductGroup[] childGroup = productGroup.getChildGroup();
            if(childGroup == null || childGroup.length == 0){
                continue;
            }

            Map<Long, String> currentMap = Arrays.stream(childGroup)
                    .collect(Collectors.toMap(AeopChildProductGroup::getGroupId, AeopChildProductGroup::getGroupName));
            productGroupMap.putAll(currentMap);
        }

        return productGroupMap;
    }

    public static AliexpressOperateLog generateLog(String type, Integer businessId, String fieldName, String before, String after, String message){
        AliexpressOperateLog aliexpressOperateLog = new AliexpressOperateLog();
        aliexpressOperateLog.setType(type);
        aliexpressOperateLog.setBusinessId(businessId);
        aliexpressOperateLog.setFieldName(fieldName);
        aliexpressOperateLog.setBefore(before);
        aliexpressOperateLog.setAfter(after);
        aliexpressOperateLog.setMessage(message);
        aliexpressOperateLog.setCreateBy(WebUtils.getUserName());
        aliexpressOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
        return aliexpressOperateLog;
    }

    public static String changeSpecialTagName(String codes) {
        if (StringUtils.isBlank(codes)) {
            return null;
        }

        List<Integer> codeList = CommonUtils.splitIntList(codes, ",");
        List<String> nameList = new ArrayList<>();
        for (Integer code : codeList) {
            String name = SpecialTagEnum.getNameByCode(code);
            nameList.add(name);
        }

        return StringUtils.join(nameList, ",");
    }

    
}
