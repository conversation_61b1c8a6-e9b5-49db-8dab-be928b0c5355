package com.estone.erp.publish.smt.jobHandler;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.smt.service.SmtViolationPunishRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 【SMT】违规处罚数据自动标记禁售定时任务
 * 需求号：http://172.16.2.103:8080/browse/ES-12822
 * 功能说明：
 * 1-根据违规处罚列表的设置SKU禁售规则且启用状态的规则，匹配违规处罚列表数据，若数据设置禁售为是的数据，过滤不处理，若列表数据与规则匹配，将对应SKU标记禁售，记录设置禁售时间；列表SKU为空的数据，过滤不处理
 * 2-若某SKU被标记禁售后，违规处罚列表所有相同的SKU均标记禁售，记录设置禁售时间，并将规则对应的禁售类型和禁售原因进行标记，无需考虑其他相同的SKU是否符合设置的SKU禁售规则，
 * 注：规则可配置多个，满足任意启用规则，均需设置禁售，规则之间均为且的关系，需违规处罚数据全部满足规则后才能设置禁售，标记的数据需记录禁售原因，禁售类型信息
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Component
@Slf4j
public class SmtViolationSkuBanJobHandler extends AbstractJobHandler {

    @Resource
    private SmtViolationPunishRecordService smtViolationPunishRecordService;

    public SmtViolationSkuBanJobHandler() {
        super("SmtViolationSkuBanJobHandler");
    }

    /**
     * 内部参数类
     */
    @Data
    private static class InnerParam {
        /**
         * 指定的规则ID列表
         * 如果为空或null，则处理所有启用状态的规则
         */
        private List<Integer> ruleIds;
    }

    @Override
    @XxlJob("SmtViolationSkuBanJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("-------SMT违规处罚SKU禁售规则自动处理任务开始--------");
        try {
            // 解析参数
            InnerParam innerParam = parseParam(param);
            List<Integer> ruleIds = innerParam != null ? innerParam.getRuleIds() : null;

            if (CollectionUtils.isNotEmpty(ruleIds)) {
                XxlJobLogger.log("指定处理规则ID: {}", ruleIds);
            } else {
                XxlJobLogger.log("处理所有启用状态的规则");
            }
            // 执行SKU禁售规则自动处理
            String result = smtViolationPunishRecordService.processSkuBanByRules(ruleIds);

            XxlJobLogger.log("任务执行结果: {}", result);
            XxlJobLogger.log("-------SMT违规处罚SKU禁售规则自动处理任务结束--------");

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("SMT违规处罚SKU禁售规则自动处理任务执行失败", e);
            XxlJobLogger.log("任务执行失败: {}", e.getMessage());
            return new ReturnT<>(ReturnT.FAIL_CODE, "任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 解析任务参数
     *
     * @param param 参数字符串
     * @return 解析后的参数对象
     */
    private InnerParam parseParam(String param) {
        if (StringUtils.isBlank(param)) {
            return null;
        }

        try {
            return super.passParam(param, InnerParam.class);
        } catch (Exception e) {
            XxlJobLogger.log("参数解析失败: {}, 将使用默认参数", e.getMessage());
            log.warn("参数解析失败: {}", e.getMessage());
            return null;
        }
    }


}
