package com.estone.erp.publish.smt.model;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class SmtViolationPunishRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column smt_violation_punish_record.id
     */
    private Long id;

    /**
     * 图片 database column smt_violation_punish_record.img
     */
    private String img;

    /**
     * 店铺 database column smt_violation_punish_record.account
     */
    private String account;

    /**
     * 店铺id database column smt_violation_punish_record.seller_id
     */
    private String sellerId;

    /**
     * 商品id database column smt_violation_punish_record.product_id
     */
    private String productId;

    /**
     * sku商品编码 database column smt_violation_punish_record.sku_code
     */
    private String skuCode;

    /**
     * sku对应的单品货号 database column smt_violation_punish_record.article_number
     */
    private String articleNumber;

    /**
     * 标题
     */
    private String title;

    /**
     * 记分计次类型
     */
    private String scoreCountType;

    /**
     * 违规规则类型
     */
    private String subRulesType;

    /**
     * 违规类型 database column smt_violation_punish_record.sub_type
     */
    private String subType;

    /**
     * 违规编号 database column smt_violation_punish_record.punish_id
     */
    private String punishId;

    /**
     * 违规对象 database column smt_violation_punish_record.asset_types
     */
    private String assetTypes;

    /**
     * 处罚时间 database column smt_violation_punish_record.punish_time
     */
    private Timestamp punishTime;

    /**
     * 扣分 database column smt_violation_punish_record.point_score
     */
    private Integer pointScore;

    /**
     * 计次 database column smt_violation_punish_record.point_count
     */
    private Integer pointCount;

    /**
     * 状态 database column smt_violation_punish_record.show_status
     */
    private String showStatus;

    /**
     * 违规原因 database column smt_violation_punish_record.punish_cause
     */
    private String punishCause;

    /**
     * 违规影响 database column smt_violation_punish_record.punish_influence
     */
    private String punishInfluence;

    /**
     * 温馨提示 database column smt_violation_punish_record.prompt
     */
    private String prompt;

    /**
     * 爬取时间 database column smt_violation_punish_record.crawl_time
     */
    private Timestamp crawlTime;

    /**
     * 创建时间 database column smt_violation_punish_record.create_date
     */
    private Timestamp createDate;

    /**
     * 修改时间 database column smt_violation_punish_record.update_date
     */
    private Timestamp updateDate;

    /**
     * 禁售类型 database column smt_violation_punish_record.infringement_type_name
     */
    private String infringementTypeName;

    /**
     * 禁售原因 database column smt_violation_punish_record.infringement_obj
     */
    private String infringementObj;

    /**
     * 是否设置禁售（true：是，false：否） database column smt_violation_punish_record.ban_flag
     */
    private Boolean banFlag;

    /**
     * 设置禁售时间 database column smt_violation_punish_record.ban_time
     */
    private Timestamp banTime;

    /**
     * 操作人 database column smt_violation_punish_record.operated_by
     */
    private String operatedBy;

    // 销售
    private String salemanager;
    // 销售组长
    private String salemanagerLeader;
    // 销售主管
    private String salesSupervisorName;
}