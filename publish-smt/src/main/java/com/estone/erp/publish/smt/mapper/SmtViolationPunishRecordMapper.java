package com.estone.erp.publish.smt.mapper;

import com.estone.erp.publish.smt.model.SmtViolationPunishRecord;
import com.estone.erp.publish.smt.model.SmtViolationPunishRecordExample;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface SmtViolationPunishRecordMapper {
    int countByExample(SmtViolationPunishRecordExample example);

    int deleteByPrimaryKey(List<Long> ids);

    int deleteByAccount(String account);

    int insert(SmtViolationPunishRecord record);

    void batchInsert(@Param("list") List<SmtViolationPunishRecord> list);


    SmtViolationPunishRecord selectByPrimaryKey(Long id);

    List<SmtViolationPunishRecord> selectByExample(SmtViolationPunishRecordExample example);

    int updateByExampleSelective(@Param("record") SmtViolationPunishRecord record, @Param("example") SmtViolationPunishRecordExample example);

    int updateByPrimaryKeySelective(SmtViolationPunishRecord record);

    @Select("select distinct ${type} from smt_violation_punish_record")
    List<String> selectType(@Param("type") String type);

    List<String> selectAccountsByExample(SmtViolationPunishRecordExample example);

    /**
     * 查询所有记录用于Java代码处理重复数据
     * 按照account、product_id、punish_id、sku_code、crawl_time DESC、id ASC排序
     * 保留策略：爬取时间最新的记录，爬取时间相同时保留ID更大的记录
     *
     * @return 所有记录列表
     */
    List<SmtViolationPunishRecord> selectAllRecordsForDuplicateCheck();

    /**
     * 批量删除重复记录
     *
     * @param ids 要删除的记录ID列表
     * @return 删除的记录数量
     */
    int batchDeleteDuplicateRecords(@Param("ids") List<Long> ids);
}