package com.estone.erp.publish.smt.controller;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.smt.call.direct.AbstractSmtOpenCall;
import com.estone.erp.publish.smt.helper.SingleDiscountOrProductHelper;
import com.estone.erp.publish.smt.jobHandler.SmtSingleDiscountCreateNewJobHandler;
import com.estone.erp.publish.smt.model.SmtMarketingSingleDiscount;
import com.estone.erp.publish.smt.jobHandler.SmtSingleDiscountAddProNewJobHandler;
import com.estone.erp.publish.smt.mq.SmtSingleDiscountProductAddMqListener;
import com.estone.erp.publish.smt.model.dto.DataCleanResultDto;
import com.estone.erp.publish.smt.service.SmtViolationPunishRecordService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import com.global.iop.util.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/smt/personalUse/test")
public class SmtPersonalUseTestController {
    @Resource
    private SmtSingleDiscountCreateNewJobHandler smtSingleDiscountCreateNewJobHandler;
    @Resource
    private SmtSingleDiscountAddProNewJobHandler smtSingleDiscountAddProNewJobHandler;

    @Resource
    private SingleDiscountOrProductHelper singleDiscountOrProductHelper;

    @Resource
    private SmtSingleDiscountProductAddMqListener smtSingleDiscountProductAddMqListener;

    @Resource
    private SmtViolationPunishRecordService smtViolationPunishRecordService;

    /**
     * 直接调用入口-smt平台接口
     * 接口文档入口:https://open.aliexpress.com/doc/api.htm?spm=a2o9m.********.0.0.6dc8ee0cEd1MTN#/api?cid=20890&path=aliexpress.marketing.limiteddiscountpromotion.addpromotionproduct&methodType=GET/POST
     *
     * @param request
     * @param accountNumber
     * @return
     */
    @PostMapping("directCallSmtApi/{accountNumber}")
    ApiResult<IopResponse> directCallSmtApi(@RequestBody IopRequest request, @PathVariable(value = "accountNumber") String accountNumber) {
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(Platform.Smt.name(), accountNumber, true);
        if (null == account) {
            return ApiResult.newError("自定义输出：账号不存在");
        }
        IopResponse iopResponse;
        try {
            iopResponse = AbstractSmtOpenCall.execute(account, request);
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }

        return ApiResult.newSuccess(iopResponse);
    }

    /**
     * 测试定时任务—单品折扣创建
     */
    @GetMapping("testSmtSingleDiscountCreateNewJobHandler")
    public String testSmtSingleDiscountCreateNewJobHandler() {
        String param = "{\"accountNumbers\":[\"<EMAIL>\"]}";
        try {
            smtSingleDiscountCreateNewJobHandler.run(param);

        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
        return "执行结束";
    }


    /**
     * 测试定时任务—单品折扣添加
     *
     * @return
     */
    @GetMapping("testSmtSingleDiscountAddProJobHandler")
    public String testSmtSingleDiscountAddProJobHandler() {
        String param = "{\"accountNumbers\":[\"<EMAIL>\"]}";
        try {
            smtSingleDiscountAddProNewJobHandler.run(param);

        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
        return "执行结束";
    }


    @GetMapping("test")
    public String test() {
        SmtMarketingSingleDiscount singleDiscount = singleDiscountOrProductHelper.getSingleDiscount("<EMAIL>", 5000000142254072L);
        smtSingleDiscountProductAddMqListener.doService(singleDiscount);
        return "执行结束";
    }

    /**
     * 清理smt_violation_punish_record表中的重复数据
     * 清理规则：以店铺账号(account)、产品ID(productId)、违规编号(punishId)、SKU编码(skuCode)四个字段组合作为唯一性判断标准
     * 对于相同组合的多条记录，仅保留爬取时间(crawlTime)最新的一条数据，爬取时间相同时保留ID更大的一条，删除其他重复记录
     *
     * @return 清理结果详情，包含删除的记录数量、耗时等统计信息
     */
    @PostMapping("cleanDuplicateViolationRecords")
    public ApiResult<DataCleanResultDto> cleanDuplicateViolationRecords() {
        try {
            log.info("开始执行违规记录重复数据清理任务");

            // 执行数据清理
            DataCleanResultDto result = smtViolationPunishRecordService.cleanDuplicateRecords();

            log.info("数据清理任务执行完成：{}", result.getMessage());

            return ApiResult.newSuccess(result);

        } catch (Exception e) {
            String errorMessage = "数据清理失败：" + e.getMessage();
            log.error(errorMessage, e);
            return ApiResult.newError(errorMessage);
        }
    }

}
