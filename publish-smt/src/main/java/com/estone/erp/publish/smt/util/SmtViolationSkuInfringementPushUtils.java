package com.estone.erp.publish.smt.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.model.ResultStatusEnum;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.smt.componet.SmtInfringementForbiddenSaleHelper;
import com.estone.erp.publish.smt.model.SmtViolationPunishRecord;
import com.estone.erp.publish.smt.model.SmtViolationPunishRecordExample;
import com.estone.erp.publish.smt.service.SmtViolationPunishRecordService;
import com.estone.erp.publish.system.product.ProductClient;
import com.estone.erp.publish.system.product.ProhibitionDO;
import com.estone.erp.publish.system.product.bean.SkuForbiddenInfoPushRequest;
import com.estone.erp.publish.tidb.publishtidb.model.SmtViolationSkuInfringementPushLog;
import com.estone.erp.publish.tidb.publishtidb.service.SmtViolationSkuInfringementPushLogService;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SMT违规处罚SKU禁售信息推送工具类
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Slf4j
public class SmtViolationSkuInfringementPushUtils {
    public static SmtViolationPunishRecordService smtViolationPunishRecordService = SpringUtils.getBean(SmtViolationPunishRecordService.class);
    public static SmtViolationSkuInfringementPushLogService smtViolationSkuInfringementPushLogService = SpringUtils.getBean(SmtViolationSkuInfringementPushLogService.class);
    public static ProductClient productClient = SpringUtils.getBean(ProductClient.class);

    /**
     * 推送违规处罚SKU禁售信息到产品系统
     *
     * @param recordIds 指定的记录ID列表，为空时处理所有符合条件的记录
     * @return 处理结果描述
     */
    public static String pushViolationSkuInfringement(List<Long> recordIds) {
        XxlJobLogger.log("开始执行SMT违规处罚SKU禁售信息推送处理");

        try {
            // 1. 查询需要推送的违规处罚记录
            List<SmtViolationPunishRecord> records = getViolationRecordsForPush(recordIds);

            if (CollectionUtils.isEmpty(records)) {
                String message = "未找到需要推送的违规处罚记录";
                XxlJobLogger.log(message);
                return message;
            }


            // 2. 按SKU分组，每个SKU随机选择一条记录
            Map<String, SmtViolationPunishRecord> skuRecordMap = groupAndRandomSelectBySku(records);

            XxlJobLogger.log("按SKU分组后，需要处理 {} 个SKU", skuRecordMap.size());

            // 3. 过滤已推送的SKU
            Map<String, SmtViolationPunishRecord> unpushedSkuMap = filterUnPushedSkus(skuRecordMap);

            if (unpushedSkuMap.isEmpty()) {
                String message = "所有SKU都已推送过，无需重复推送";
                XxlJobLogger.log(message);
                return message;
            }

            XxlJobLogger.log("过滤后，需要推送 {} 个SKU", unpushedSkuMap.size());

            // 4. 直接同步处理推送
            return processPushDirectly(unpushedSkuMap);

        } catch (Exception e) {
            String errorMessage = "推送处理异常: " + e.getMessage();
            log.error(errorMessage, e);
            XxlJobLogger.log(errorMessage);
            return errorMessage;
        }
    }

    /**
     * 推送单个SKU禁售信息到产品系统
     *
     * @return 推送结果
     */
    public static Pair<Boolean, String> pushSingleSkuToProductSystem(SmtViolationPunishRecord record) {
        String sku = record.getArticleNumber();
        String infringementObj = record.getInfringementObj();
        String infringementTypeName = record.getInfringementTypeName();
        try {
            SkuForbiddenInfoPushRequest pushData = SkuForbiddenInfoPushRequest.builder()
                    .sonSku(sku).infringementTypeName(infringementTypeName).infringementObj(infringementObj).build();

            ApiResult<Void> result = productClient.pushSkuForbiddenInfo(pushData);
            if (!result.isSuccess()) {
                log.warn("推送SKU {} 禁售信息失败: {}", sku, result.getErrorMsg());
                return Pair.of(false, result.getErrorMsg());
            } else {
                log.info("推送SKU {} 禁售信息到产品系统结果: {}", sku, true);
                return Pair.of(true, null);
            }
        } catch (Exception e) {
            log.error("推送SKU {} 禁售信息异常: {}", sku, e.getMessage(), e);
            return Pair.of(false, e.getMessage());
        }
    }

    /**
     * 查询需要推送的违规处罚记录
     */
    private static List<SmtViolationPunishRecord> getViolationRecordsForPush(List<Long> recordIds) {

        SmtViolationPunishRecordExample example = new SmtViolationPunishRecordExample();
        SmtViolationPunishRecordExample.Criteria criteria = example.createCriteria();

        // 基本条件：禁售标记为true，且SKU不为空
        criteria.andBanFlagEqualTo(true);
        criteria.andArticleNumberIsNotNull();

        // 如果指定了记录ID，则按ID查询
        if (CollectionUtils.isNotEmpty(recordIds)) {
            criteria.andIdIn(recordIds);
        }
        return smtViolationPunishRecordService.selectByExample(example);
    }

    /**
     * 按SKU分组并随机选择一条记录
     */
    private static Map<String, SmtViolationPunishRecord> groupAndRandomSelectBySku(List<SmtViolationPunishRecord> records) {
        Map<String, List<SmtViolationPunishRecord>> skuGroupMap = records.stream()
                .filter(record -> StringUtils.isNotBlank(record.getArticleNumber()))
                .collect(Collectors.groupingBy(SmtViolationPunishRecord::getArticleNumber));

        Map<String, SmtViolationPunishRecord> result = new HashMap<>();
        Random random = new Random();

        for (Map.Entry<String, List<SmtViolationPunishRecord>> entry : skuGroupMap.entrySet()) {
            String sku = entry.getKey();
            List<SmtViolationPunishRecord> skuRecords = entry.getValue();

            // 随机选择一条记录
            SmtViolationPunishRecord selectedRecord = skuRecords.get(random.nextInt(skuRecords.size()));
            result.put(sku, selectedRecord);
        }

        return result;
    }

    /**
     * 过滤已推送的SKU
     */
    private static Map<String, SmtViolationPunishRecord> filterUnPushedSkus(Map<String, SmtViolationPunishRecord> skuRecordMap) {
        if (skuRecordMap.isEmpty()) {
            return skuRecordMap;
        }


        // 查询已推送成功的SKU
        LambdaQueryWrapper<SmtViolationSkuInfringementPushLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SmtViolationSkuInfringementPushLog::getArticleNumber, skuRecordMap.keySet()).eq(SmtViolationSkuInfringementPushLog::getResultStatus, 1);
        List<SmtViolationSkuInfringementPushLog> pushedLogs = smtViolationSkuInfringementPushLogService.list(queryWrapper);
        Set<String> pushedSkus = pushedLogs.stream()
                .map(SmtViolationSkuInfringementPushLog::getArticleNumber)
                .collect(Collectors.toSet());

        // 过滤掉已推送的SKU
        return skuRecordMap.entrySet().stream()
                .filter(entry -> !pushedSkus.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    /**
     * 直接同步处理推送
     */
    private static String processPushDirectly(Map<String, SmtViolationPunishRecord> unpushedSkuMap) {
        int successCount = 0;
        int failCount = 0;


        List<String> skuList = new ArrayList<>(unpushedSkuMap.keySet());
        ProhibitionDO prohibitionDO = new ProhibitionDO(SaleChannel.CHANNEL_SMT, null, null, null);
        //在调用产品系统接口校验一下
        ApiResult<Map<String, Boolean>> checkResult = SmtInfringementForbiddenSaleHelper.checkSkuForbiddenInProductSystem(skuList, Collections.singletonList(prohibitionDO));
        if (!checkResult.isSuccess()) {
            String message = String.format("skuList[%s]获取禁售信息失败,error:%s", skuList, checkResult.getErrorMsg());
            XxlJobLogger.log(message);
            return message;
        }
        Map<String, Boolean> checkSkuMap = checkResult.getResult();


        XxlJobLogger.log("开始同步推送SKU禁售信息");

        // 直接循环处理每个SKU
        for (Map.Entry<String, SmtViolationPunishRecord> entry : unpushedSkuMap.entrySet()) {
            String sku = entry.getKey();
            SmtViolationPunishRecord record = entry.getValue();

            try {
                // 检查SKU是否已在产品系统中被SMT平台禁售
                if (BooleanUtils.isTrue(checkSkuMap.get(sku))) {
                    XxlJobLogger.log("SKU {} 已在产品系统中被SMT平台禁售，跳过推送", sku);
                    continue;// 已禁售视为成功
                }

                // 处理单个SKU的推送
                boolean pushResult = processSingleSkuPush(record);

                if (pushResult) {
                    successCount++;
                    XxlJobLogger.log("SKU {} 推送成功", sku);
                } else {
                    failCount++;
                    XxlJobLogger.log("SKU {} 推送失败", sku);
                }

            } catch (Exception e) {
                failCount++;
                log.error("处理SKU {} 推送异常: {}", sku, e.getMessage(), e);
                XxlJobLogger.log("处理SKU {} 推送异常: {}", sku, e.getMessage());
            }
        }

        String result = String.format("推送完成 - 总数: %d, 成功: %d, 失败: %d",
                unpushedSkuMap.size(), successCount, failCount);

        XxlJobLogger.log(result);
        return result;
    }

    /**
     * 处理单个SKU的推送
     */
    private static boolean processSingleSkuPush(SmtViolationPunishRecord record) {
        String sku = record.getArticleNumber();

        try {
            // 推送到产品系统
            Pair<Boolean, String> pushPair = pushSingleSkuToProductSystem(record);

            if (org.apache.commons.lang3.BooleanUtils.isTrue(pushPair.getLeft())) {
                savePushLog(record, ResultStatusEnum.SUCCESS.getCode(), pushPair.getRight());
                return true;
            } else {
                savePushLog(record, ResultStatusEnum.FAILURE.getCode(), pushPair.getRight());
                XxlJobLogger.log("SKU {} 推送到产品系统失败", sku);
                return false;
            }

        } catch (Exception e) {
            XxlJobLogger.log("处理SKU {} 推送异常: {}", sku, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 保存推送日志
     */
    private static void savePushLog(SmtViolationPunishRecord record, Integer resultStatus, String resultMsg) {
        SmtViolationSkuInfringementPushLog pushLog = new SmtViolationSkuInfringementPushLog();
        pushLog.setRecordId(record.getId());
        pushLog.setArticleNumber(record.getArticleNumber());
        pushLog.setInfringementTypeName(record.getInfringementTypeName());
        pushLog.setInfringementObj(record.getInfringementObj());
        pushLog.setCreatedTime(LocalDateTime.now());
        pushLog.setResultStatus(resultStatus);
        pushLog.setResultMsg(resultMsg);
        smtViolationSkuInfringementPushLogService.save(pushLog);
    }
}
