<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.SmtViolationPunishRecordMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.SmtViolationPunishRecord" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="img" property="img" jdbcType="VARCHAR" />
    <result column="account" property="account" jdbcType="VARCHAR" />
    <result column="seller_id" property="sellerId" jdbcType="VARCHAR" />
    <result column="product_id" property="productId" jdbcType="VARCHAR" />
    <result column="sku_code" property="skuCode" jdbcType="VARCHAR" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="score_count_type" property="scoreCountType" jdbcType="VARCHAR" />
    <result column="sub_rules_type" property="subRulesType" jdbcType="VARCHAR" />
    <result column="sub_type" property="subType" jdbcType="VARCHAR" />
    <result column="punish_id" property="punishId" jdbcType="VARCHAR" />
    <result column="asset_types" property="assetTypes" jdbcType="VARCHAR" />
    <result column="punish_time" property="punishTime" jdbcType="TIMESTAMP" />
    <result column="point_score" property="pointScore" jdbcType="INTEGER" />
    <result column="point_count" property="pointCount" jdbcType="INTEGER" />
    <result column="show_status" property="showStatus" jdbcType="VARCHAR" />
    <result column="punish_cause" property="punishCause" jdbcType="VARCHAR" />
    <result column="punish_influence" property="punishInfluence" jdbcType="VARCHAR" />
    <result column="prompt" property="prompt" jdbcType="VARCHAR" />
    <result column="crawl_time" property="crawlTime" jdbcType="TIMESTAMP" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="infringement_type_name" property="infringementTypeName" jdbcType="VARCHAR"/>
    <result column="infringement_obj" property="infringementObj" jdbcType="VARCHAR"/>
    <result column="ban_flag" property="banFlag" jdbcType="BIT"/>
    <result column="ban_time" property="banTime" jdbcType="TIMESTAMP"/>
    <result column="operated_by" property="operatedBy" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, img, account, seller_id, product_id, sku_code, article_number, title, score_count_type,
    sub_rules_type, sub_type, punish_id,
    asset_types, punish_time, point_score, point_count, show_status, punish_cause, punish_influence,
    prompt, crawl_time, create_date, update_date, infringement_type_name, infringement_obj, ban_flag, ban_time, operated_by
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.SmtViolationPunishRecordExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from smt_violation_punish_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectAccountsByExample" resultType="java.lang.String" parameterType="com.estone.erp.publish.smt.model.SmtViolationPunishRecordExample" >
    select distinct account
    from smt_violation_punish_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from smt_violation_punish_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from smt_violation_punish_record
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>

  <delete id="deleteByAccount" >
    delete from smt_violation_punish_record
    where account = #{account}
  </delete>

  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.SmtViolationPunishRecord" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into smt_violation_punish_record (img, account, seller_id,
      product_id, sku_code, article_number,
      title, score_count_type, sub_rules_type, sub_type, punish_id, asset_types,
    punish_time, point_score, point_count,
    show_status, punish_cause, punish_influence,
    prompt, crawl_time, create_date,
    update_date, infringement_type_name, infringement_obj, ban_flag, ban_time, operated_by)
    values (#{img,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, #{sellerId,jdbcType=VARCHAR},
      #{productId,jdbcType=VARCHAR}, #{skuCode,jdbcType=VARCHAR}, #{articleNumber,jdbcType=VARCHAR},
      #{title,jdbcType=VARCHAR}, #{scoreCountType,jdbcType=VARCHAR}, #{subRulesType,jdbcType=VARCHAR},
      #{subType,jdbcType=VARCHAR}, #{punishId,jdbcType=VARCHAR}, #{assetTypes,jdbcType=VARCHAR},
    #{punishTime,jdbcType=TIMESTAMP}, #{pointScore,jdbcType=INTEGER}, #{pointCount,jdbcType=INTEGER},
    #{showStatus,jdbcType=VARCHAR}, #{punishCause,jdbcType=VARCHAR}, #{punishInfluence,jdbcType=VARCHAR},
    #{prompt,jdbcType=VARCHAR}, #{crawlTime,jdbcType=TIMESTAMP}, #{createDate,jdbcType=TIMESTAMP},
    #{updateDate,jdbcType=TIMESTAMP}, #{infringementTypeName,jdbcType=VARCHAR}, #{infringementObj,jdbcType=VARCHAR},
    #{banFlag,jdbcType=BIT}, #{banTime,jdbcType=TIMESTAMP}, #{operatedBy,jdbcType=VARCHAR})
  </insert>

  <insert id="batchInsert" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into smt_violation_punish_record (img, account, seller_id,
    product_id, sku_code, article_number, title, score_count_type, sub_rules_type,
    sub_type, punish_id, asset_types,
    punish_time, point_score, point_count,
    show_status, punish_cause, punish_influence,
    prompt, crawl_time, create_date,
    update_date, infringement_type_name, infringement_obj, ban_flag, ban_time, operated_by)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.img,jdbcType=VARCHAR}, #{item.account,jdbcType=VARCHAR}, #{item.sellerId,jdbcType=VARCHAR},
      #{item.productId,jdbcType=VARCHAR}, #{item.skuCode,jdbcType=VARCHAR}, #{item.articleNumber,jdbcType=VARCHAR},
      #{item.title,jdbcType=VARCHAR}, #{item.scoreCountType,jdbcType=VARCHAR}, #{item.subRulesType,jdbcType=VARCHAR},
      #{item.subType,jdbcType=VARCHAR}, #{item.punishId,jdbcType=VARCHAR}, #{item.assetTypes,jdbcType=VARCHAR},
      #{item.punishTime,jdbcType=TIMESTAMP}, #{item.pointScore,jdbcType=INTEGER}, #{item.pointCount,jdbcType=INTEGER},
      #{item.showStatus,jdbcType=VARCHAR}, #{item.punishCause,jdbcType=VARCHAR}, #{item.punishInfluence,jdbcType=VARCHAR},
      #{item.prompt,jdbcType=VARCHAR}, #{item.crawlTime,jdbcType=TIMESTAMP}, #{item.createDate,jdbcType=TIMESTAMP},
      #{item.updateDate,jdbcType=TIMESTAMP}, #{item.infringementTypeName,jdbcType=VARCHAR},
      #{item.infringementObj,jdbcType=VARCHAR},
      #{item.banFlag,jdbcType=BIT}, #{item.banTime,jdbcType=TIMESTAMP}, #{item.operatedBy,jdbcType=VARCHAR})
    </foreach>
  </insert>


  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.SmtViolationPunishRecordExample" resultType="java.lang.Integer" >
    select count(*) from smt_violation_punish_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update smt_violation_punish_record
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.img != null" >
        img = #{record.img,jdbcType=VARCHAR},
      </if>
      <if test="record.account != null" >
        account = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerId != null" >
        seller_id = #{record.sellerId,jdbcType=VARCHAR},
      </if>
      <if test="record.productId != null" >
        product_id = #{record.productId,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCode != null" >
        sku_code = #{record.skuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null" >
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.scoreCountType != null" >
        score_count_type = #{record.scoreCountType,jdbcType=VARCHAR},
      </if>
      <if test="record.subRulesType != null" >
        sub_rules_type = #{record.subRulesType,jdbcType=VARCHAR},
      </if>
      <if test="record.subType != null" >
        sub_type = #{record.subType,jdbcType=VARCHAR},
      </if>
      <if test="record.punishId != null" >
        punish_id = #{record.punishId,jdbcType=VARCHAR},
      </if>
      <if test="record.assetTypes != null" >
        asset_types = #{record.assetTypes,jdbcType=VARCHAR},
      </if>
      <if test="record.punishTime != null" >
        punish_time = #{record.punishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.pointScore != null" >
        point_score = #{record.pointScore,jdbcType=INTEGER},
      </if>
      <if test="record.pointCount != null" >
        point_count = #{record.pointCount,jdbcType=INTEGER},
      </if>
      <if test="record.showStatus != null" >
        show_status = #{record.showStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.punishCause != null" >
        punish_cause = #{record.punishCause,jdbcType=VARCHAR},
      </if>
      <if test="record.punishInfluence != null" >
        punish_influence = #{record.punishInfluence,jdbcType=VARCHAR},
      </if>
      <if test="record.prompt != null" >
        prompt = #{record.prompt,jdbcType=VARCHAR},
      </if>
      <if test="record.crawlTime != null" >
        crawl_time = #{record.crawlTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateDate != null" >
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.infringementTypeName != null">
        infringement_type_name = #{record.infringementTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.infringementObj != null">
        infringement_obj = #{record.infringementObj,jdbcType=VARCHAR},
      </if>
      <if test="record.banFlag != null">
        ban_flag = #{record.banFlag,jdbcType=BIT},
      </if>
      <if test="record.banTime != null">
        ban_time = #{record.banTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operatedBy != null">
        operated_by = #{record.operatedBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.SmtViolationPunishRecord" >
    update smt_violation_punish_record
    <set >
      <if test="img != null" >
        img = #{img,jdbcType=VARCHAR},
      </if>
      <if test="account != null" >
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="sellerId != null" >
        seller_id = #{sellerId,jdbcType=VARCHAR},
      </if>
      <if test="productId != null" >
        product_id = #{productId,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null" >
        sku_code = #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="scoreCountType != null" >
        score_count_type = #{scoreCountType,jdbcType=VARCHAR},
      </if>
      <if test="subRulesType != null" >
        sub_rules_type = #{subRulesType,jdbcType=VARCHAR},
      </if>
      <if test="subType != null" >
        sub_type = #{subType,jdbcType=VARCHAR},
      </if>
      <if test="punishId != null" >
        punish_id = #{punishId,jdbcType=VARCHAR},
      </if>
      <if test="assetTypes != null" >
        asset_types = #{assetTypes,jdbcType=VARCHAR},
      </if>
      <if test="punishTime != null" >
        punish_time = #{punishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pointScore != null" >
        point_score = #{pointScore,jdbcType=INTEGER},
      </if>
      <if test="pointCount != null" >
        point_count = #{pointCount,jdbcType=INTEGER},
      </if>
      <if test="showStatus != null" >
        show_status = #{showStatus,jdbcType=VARCHAR},
      </if>
      <if test="punishCause != null" >
        punish_cause = #{punishCause,jdbcType=VARCHAR},
      </if>
      <if test="punishInfluence != null" >
        punish_influence = #{punishInfluence,jdbcType=VARCHAR},
      </if>
      <if test="prompt != null" >
        prompt = #{prompt,jdbcType=VARCHAR},
      </if>
      <if test="crawlTime != null" >
        crawl_time = #{crawlTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="infringementTypeName != null">
        infringement_type_name = #{infringementTypeName,jdbcType=VARCHAR},
      </if>
      <if test="infringementObj != null">
        infringement_obj = #{infringementObj,jdbcType=VARCHAR},
      </if>
      <if test="banFlag != null">
        ban_flag = #{banFlag,jdbcType=BIT},
      </if>
      <if test="banTime != null">
        ban_time = #{banTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatedBy != null">
        operated_by = #{operatedBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 查询所有记录用于Java代码处理重复数据 -->
  <select id="selectAllRecordsForDuplicateCheck" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM smt_violation_punish_record
    ORDER BY account, product_id, punish_id, sku_code, crawl_time DESC, id ASC
  </select>

  <!-- 批量删除重复记录 -->
  <delete id="batchDeleteDuplicateRecords">
    DELETE FROM smt_violation_punish_record
    WHERE id IN
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </delete>
</mapper>