package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.enums.AmazonMarketingLogTypeEnum;
import com.estone.erp.publish.amazon.service.AmazonAccountPublishConfigService;
import com.estone.erp.publish.amazon.util.AmazonConfigLogUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.*;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.enums.OfflineConfigEnums;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.rule.SaleCountRuleConfigDO;
import com.estone.erp.publish.tidb.publishtidb.domain.req.CurrentAccountRequest;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonOfflineConfigMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonMarketingConfigLog;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonOfflineConfig;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonOfflineConfigDataStatistics;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonMarketingConfigLogService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonOfflineConfigDataStatisticsService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonOfflineConfigService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <p>
 * Amazon 下架配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Service
public class AmazonOfflineConfigServiceImpl extends ServiceImpl<AmazonOfflineConfigMapper, AmazonOfflineConfig> implements AmazonOfflineConfigService {
    @Resource
    private PermissionsHelper permissionsHelper;
    @Resource
    private AmazonAccountPublishConfigService amazonAccountPublishConfigService;
    @Resource
    private AmazonMarketingConfigLogService amazonMarketingConfigLogService;
    @Resource
    private AmazonOfflineConfigDataStatisticsService amazonOfflineConfigDataStatisticsService;


    public static final BiFunction<SaleCountRuleConfigDO, AmazonAsinSaleCountDO, Integer> RULE_CONFIG_FUNC = (ruleConfig, asinSaleCountDO) -> {
        if ("order_24H_count".equals(ruleConfig.getType())) {
            return asinSaleCountDO.getSale_24_count();
        }
        if ("order_last_7d_count".equals(ruleConfig.getType())) {
            return asinSaleCountDO.getSale_7d_count();
        }
        if ("order_last_14d_count".equals(ruleConfig.getType())) {
            return asinSaleCountDO.getSale_14d_count();
        }
        if ("order_last_30d_count".equals(ruleConfig.getType())) {
            return asinSaleCountDO.getSale_30d_count();
        }
        if ("order_num_total".equals(ruleConfig.getType())) {
            return asinSaleCountDO.getSale_total_count();
        }
        return null;
    };

    @Override
    public ApiResult<String> saveOrUpdate(AmazonOfflineConfigVO editParam) {
        AmazonOfflineConfigDO paramConfig = editParam.getConfig();
        verifyRuleName(paramConfig);

        // 执行保存或更新
        AmazonOfflineConfig amazonOfflineConfig = new AmazonOfflineConfig();
        if (ObjectUtils.isEmpty(paramConfig.getId())) {
            amazonOfflineConfig = addConfig(editParam);
        } else {
            amazonOfflineConfig = updateConfig(editParam);
        }

        if (ObjectUtils.isEmpty(amazonOfflineConfig)) {
            return ApiResult.newError("配置不存在");
        }
        return ApiResult.newSuccess();
    }

    /**
     * 校验规则名称是否重复
     */
    private void verifyRuleName(AmazonOfflineConfigDO config) {
        LambdaQueryWrapper<AmazonOfflineConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AmazonOfflineConfig::getRuleName, config.getRuleName());
        if (ObjectUtils.isNotEmpty(config.getId())) {
            wrapper.ne(AmazonOfflineConfig::getId, config.getId());
        }

        int sameNameCount = baseMapper.selectCount(wrapper);
        if (sameNameCount > 0) {
            throw new BusinessException("规则名称已存在");
        }
    }

    /**
     * 更新配置
     *
     * @param editParam 编辑配置
     */
    private AmazonOfflineConfig updateConfig(AmazonOfflineConfigVO editParam) {
        AmazonOfflineConfig dbConfig = getById(editParam.getConfig().getId());

        if (dbConfig == null) {
            return null;
        }
        LocalDateTime now = LocalDateTime.now();
        AmazonOfflineConfig updateConfig = editParam.convert2Entity(editParam);
        updateConfig.setId(dbConfig.getId());
        if (editParam.getConfirm()) {
            updateConfig.setConfirmedStatus(0);
        }
        // 记录日志
        saveConfigLogs(updateConfig);

        updateConfig.setCreatedBy(dbConfig.getCreatedBy());
        updateConfig.setCreatedTime(dbConfig.getCreatedTime());
        updateConfig.setUpdatedBy(WebUtils.getUserName());
        updateConfig.setUpdatedTime(now);
        baseMapper.updateById(updateConfig);

        // 判断执行确认操作
        amazonOfflineConfigDataStatisticsService.updateAndSaveConfigLog(editParam.getConfirm(), updateConfig);

        return updateConfig;
    }

    /**
     * 新增配置
     * @param editParam 编辑参数
     */
    private AmazonOfflineConfig addConfig(AmazonOfflineConfigVO editParam) {
        // 新增逻辑
        LocalDateTime now = LocalDateTime.now();
        AmazonOfflineConfig config = editParam.convert2Entity(editParam);
        config.setConfirmedStatus(0);
        config.setCreatedBy(WebUtils.getUserName());
        config.setCreatedTime(now);
        baseMapper.insert(config);

        // 添加下架统计数据
        AmazonOfflineConfigDataStatistics dataStatistics = new AmazonOfflineConfigDataStatistics();
        dataStatistics.setConfigId(config.getId());
        dataStatistics.setExecutionStatus(OfflineConfigEnums.ExecutionStatus.PENDING_EXECUTION.getCode());
        dataStatistics.setType(config.getType());
        dataStatistics.setRuleName(config.getRuleName());
        dataStatistics.setAccounts(config.getAccounts());
        dataStatistics.setRule(config.getRule());
        dataStatistics.setCreatedBy(config.getCreatedBy());
        dataStatistics.setCreatedTime(config.getCreatedTime());
        amazonOfflineConfigDataStatisticsService.save(dataStatistics);
        return config;
    }

    @Override
    public ApiResult<AmazonOfflineConfigVO> editConfig(Integer id) {
        AmazonOfflineConfig dbConfig = getById(id);
        if (dbConfig == null) {
            return ApiResult.newError("配置不存在");
        }
        AmazonOfflineConfigVO editDO = new AmazonOfflineConfigVO(dbConfig);
        return ApiResult.newSuccess(editDO);

    }

    @Override
    public ApiResult<List<String>> getCurrentUserAccounts(CurrentAccountRequest currentAccountRequest) {
        List<String> sites = currentAccountRequest.getSites();
        if (CollectionUtils.isNotEmpty(sites)) {
            // 按站点获取当前用户管理的店铺
            List<String> userManagedSiteAccountNumbersCache = amazonAccountPublishConfigService.getCurrentUserManagedSiteAccountNumbers(sites);
            return ApiResult.newSuccess(userManagedSiteAccountNumbersCache);
        }

        if (CollectionUtils.isNotEmpty(currentAccountRequest.getEmployeeNos())) {
            // 按员工获取当前用户管理的店铺
            List<String> userManagedAccountNumbersCache = amazonAccountPublishConfigService.getEmployeeManagedAccountNumbers(currentAccountRequest.getEmployeeNos());
            return ApiResult.newSuccess(userManagedAccountNumbersCache);
        }

        // 超级管理员获取所有店铺
        Boolean superAdminOrSupervisor = permissionsHelper.isSuperAdminOrBigSupervisor(SaleChannel.CHANNEL_AMAZON);
        if (superAdminOrSupervisor) {
            List<String> allEnableAccountNumber = amazonAccountPublishConfigService.getAllEnableAccountNumber();
            return ApiResult.newSuccess(allEnableAccountNumber);
        }

        // 获取当前用户管理的店铺
        List<String> userManagedAccountNumbersCache = amazonAccountPublishConfigService.getUserManagedAccountNumbersCache(WebUtils.getUserName());
        return ApiResult.newSuccess(userManagedAccountNumbersCache);
    }

    @Override
    public ApiResult<IPage<AmazonOfflineConfigVO>> search(AmazonOfflineSearchDTO searchParam) {
        // 销售仅可查看自己的数据，组长、主管仅可查看自己和下级数据，平台销售主管和超管可查看全部数据
        isAuth(searchParam);
        LambdaQueryWrapper<AmazonOfflineConfig> wrapper = buildSearchWrapper(searchParam);
        Page<AmazonOfflineConfig> page = new Page<>(searchParam.getPageIndex(), searchParam.getPageSize());
        IPage<AmazonOfflineConfig> iPage = page(page, wrapper);

        // 扩展信息
        List<AmazonOfflineConfigVO> rows = transferAsConfigVOs(iPage.getRecords());
        IPage<AmazonOfflineConfigVO> iPageResult = new Page<>(iPage.getCurrent(), iPage.getSize(), iPage.getTotal());
        iPageResult.setPages(iPage.getPages());
        iPageResult.setRecords(rows);
        return ApiResult.newSuccess(iPageResult);
    }

    @Override
    public ApiResult<?> updateStatus(AmazonUpdateStatusDTO requestParam) {
        // 通过id获取配置，并过滤不等于状态的配置
        LambdaQueryWrapper<AmazonOfflineConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(AmazonOfflineConfig::getId, requestParam.getIds()).ne(AmazonOfflineConfig::getStatus, requestParam.getStatus());
        List<AmazonOfflineConfig> configs = baseMapper.selectList(wrapper);

        if (CollectionUtils.isNotEmpty(configs)) {
            // 修改配置并记录日志
            for (AmazonOfflineConfig config : configs) {
                config.setStatus(requestParam.getStatus());
                saveConfigLogs(config);
                updateById(config);
            }
        }
        return ApiResult.newSuccess();
    }


    private void isAuth(AmazonOfflineSearchDTO searchParam) {
        ApiResult<Boolean> superAdminOrEquivalentResult = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_AMAZON);
        if (!superAdminOrEquivalentResult.isSuccess()) {
            throw new BusinessException(superAdminOrEquivalentResult.getErrorMsg());
        }
        // 平台销售主管和超管可查看全部数据
        if (superAdminOrEquivalentResult.getResult()) {
            return;
        }
        List<String> employeeNos = permissionsHelper.getCurrentPermissionEmployeeNo(SaleChannel.CHANNEL_AMAZON, false);

        List<String> createdByList = searchParam.getCreatedByList();
        if (CollectionUtils.isNotEmpty(createdByList)) {
            createdByList.removeIf(createdBy -> !employeeNos.contains(createdBy));
            searchParam.setCreatedByList(createdByList);
            return;
        }
        searchParam.setCreatedByList(employeeNos);
    }

    private List<AmazonOfflineConfigVO> transferAsConfigVOs(List<AmazonOfflineConfig> configs) {
        return configs.stream().map(AmazonOfflineConfigVO::new).collect(Collectors.toList());
    }


    private LambdaQueryWrapper<AmazonOfflineConfig> buildSearchWrapper(AmazonOfflineSearchDTO searchParam) {
        LambdaQueryWrapper<AmazonOfflineConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(searchParam.getRuleName()), AmazonOfflineConfig::getRuleName, searchParam.getRuleName())
                .in(CollectionUtils.isNotEmpty(searchParam.getTypeList()), AmazonOfflineConfig::getType, searchParam.getTypeList())
                .in(CollectionUtils.isNotEmpty(searchParam.getStatusList()), AmazonOfflineConfig::getStatus, searchParam.getStatusList())
                .in(CollectionUtils.isNotEmpty(searchParam.getCreatedByList()), AmazonOfflineConfig::getCreatedBy, searchParam.getCreatedByList())
                .between(StringUtils.isNotBlank(searchParam.getCreatedTimeFrom()) && StringUtils.isNotBlank(searchParam.getCreatedTimeTo()),
                        AmazonOfflineConfig::getCreatedTime, searchParam.getCreatedTimeFrom(), searchParam.getCreatedTimeTo());
        wrapper.orderByDesc(StringUtils.isBlank(searchParam.getSortField()), AmazonOfflineConfig::getCreatedTime);

        List<String> accounts = searchParam.getAccounts();
        // 店铺特殊处理
        if (CollectionUtils.isNotEmpty(accounts)) {
            String condition = "JSON_OVERLAPS((accounts->'$.account'), '[\"" + StringUtils.join(accounts, "\",\"") + "\"]')";
            wrapper.apply(condition);

        }
        return wrapper;
    }


    /**
     * 保存日志
     *
     * @param config
     */
    private void saveConfigLogs(AmazonOfflineConfig config) {
        // 获取旧的配置
        AmazonOfflineConfig oldConfig = getById(config.getId());
        AmazonOfflineConfigConvertDO oldConvertDO = AmazonOfflineConfigConvertDO.reconvert(oldConfig);
        AmazonOfflineConfigConvertDO newCovertDO = AmazonOfflineConfigConvertDO.reconvert(config);

        // 日志列表
        List<AmazonMarketingConfigLog> configLogs = AmazonConfigLogUtil
                .generateLog(newCovertDO, oldConvertDO, Long.valueOf(config.getId()), AmazonMarketingLogTypeEnum.OFFLINE_CONFIG);

        if (CollectionUtils.isNotEmpty(configLogs)) {
            amazonMarketingConfigLogService.saveBatch(configLogs);
        }
    }

    /**
     * 转换成销量匹配规则
     *
     * @param saleCountRuleConfig 销量规则配置
     * @return 销量匹配规则
     */
    @Override
    public List<Predicate<AmazonAsinSaleCountDO>> transferToSaleCountCompareRules(SaleCountRuleConfigDO saleCountRuleConfig) {
        if (saleCountRuleConfig == null) {
            return List.of(
                    // 总销量为0
                    asinSaleCountDO -> {
                        Integer saleTotalCount = asinSaleCountDO.getSale_total_count();
                        return saleTotalCount == null || saleTotalCount == 0;
                    }
            );
        }
        Integer fromSaleCount = saleCountRuleConfig.getFromSaleCount();
        Integer toSaleCount = saleCountRuleConfig.getToSaleCount();
        return List.of(asinSaleCountDO -> {
            Integer saleCount = RULE_CONFIG_FUNC.apply(saleCountRuleConfig, asinSaleCountDO);
            if (saleCount == null) {
                return false;
            }
            // 左闭右开
            return fromSaleCount <= saleCount && saleCount < toSaleCount;
        });
    }

}
