package com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface LinkManagementEnums {

    @Getter
    @AllArgsConstructor
    enum ConfigTye {
        HANDING_TIME(1, "修改备货期");
        private final Integer code;
        private final String desc;
    }

    @Getter
    @AllArgsConstructor
    enum HandTimeRuleType {
        PRICE_RANGE(1, "价格区间"),
        DESIGNATED_SKU(2, "指定SKU"),
        PRICE_WEIGHT_TAG(3, "价格+重量+标签");
        private final Integer code;
        private final String desc;

        public boolean isTrue(Integer code) {
            if (code == null) {
                return false;
            }
            return code.equals(this.code);
        }

    }
}
