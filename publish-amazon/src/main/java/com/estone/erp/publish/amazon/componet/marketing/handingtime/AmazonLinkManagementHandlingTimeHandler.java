package com.estone.erp.publish.amazon.componet.marketing.handingtime;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.publish.amazon.componet.scheduler.AmazonSchedulerTaskService;
import com.estone.erp.publish.amazon.componet.scheduler.enums.AmazonSchedulerTaskEnums;
import com.estone.erp.publish.amazon.enums.SystemFeedTypeEnum;
import com.estone.erp.publish.amazon.model.AmazonProcessReport;
import com.estone.erp.publish.amazon.model.AmazonProcessReportExample;
import com.estone.erp.publish.amazon.mq.model.AmazonSchedulerTaskJobMessage;
import com.estone.erp.publish.amazon.service.AmazonProcessReportService;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.dto.AmazonProductListingDto;
import com.estone.erp.publish.publishAmazon.service.AmazonJSONListingFeedService;
import com.estone.erp.publish.system.product.ProductLogisticsUtils;
import com.estone.erp.publish.system.product.bean.ProductLogisticsInfoDO;
import com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement.AmazonLinkManagementConfigDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement.AmazonLinkManagementRuleDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement.enums.LinkManagementEnums;
import com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement.rule.PriceRangeDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement.rule.SalesRangeDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement.rule.WeightRangeDTO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonLinkManagementConfig;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonLinkManagementConfigService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonLinkManagementTaskRecordService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 链接管理-修改备货期任务处理器
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Slf4j
@Component
public class AmazonLinkManagementHandlingTimeHandler implements AmazonSchedulerTaskService {

    @Autowired
    private EsAmazonProductListingService amazonProductListingService;
    @Autowired
    private AmazonLinkManagementConfigService linkManagementConfigService;
    @Autowired
    private AmazonLinkManagementTaskRecordService linkManagementTaskRecordService;
    @Resource
    private AmazonJSONListingFeedService amazonJSONListingFeedService;
    @Resource
    private AmazonProcessReportService amazonProcessReportService;
    @Autowired
    private ProductLogisticsUtils productLogisticsUtils;

    @Override
    public String getTaskType() {
        return AmazonSchedulerTaskEnums.TaskType.LINK_MANAGEMENT_UPDATE_HANDLING_TIME.name();
    }

    @Override
    public ApiResult<String> executeTask(AmazonSchedulerTaskJobMessage message) {
        String data = message.getData();
        try {
            // 解析消息数据
            AmazonLinkManagementHandlingTimeMessage handlingTimeMessage = JSON.parseObject(data, AmazonLinkManagementHandlingTimeMessage.class);

            log.info("开始执行链接管理-修改备货期任务，处理配置ID: {}, 店铺: {}, 任务记录ID: {}",
                    handlingTimeMessage.getConfigId(),
                    handlingTimeMessage.getAccountNumber(),
                    handlingTimeMessage.getTaskRecordId());

            // 更新任务记录状态
            linkManagementTaskRecordService.updateExecuteStatus(handlingTimeMessage.getTaskRecordId(), 1);

            // 获取配置信息
            AmazonLinkManagementConfig config = linkManagementConfigService.getById(handlingTimeMessage.getConfigId());
            if (config == null) {
                log.error("配置不存在，配置ID: {}", handlingTimeMessage.getConfigId());
                return ApiResult.newError("配置不存在");
            }

            // 执行修改备货期任务
            executeUpdateHandlingTime(config, handlingTimeMessage.getAccountNumber());

            log.info("链接管理-修改备货期任务执行成功，数据：{}", data);
            return ApiResult.newSuccess("执行成功");

        } catch (Exception e) {
            log.error("链接管理-修改备货期任务执行失败，数据：{}，错误：{}", data, e.getMessage(), e);
            return ApiResult.newError("执行失败：" + e.getMessage());
        }
    }

    private void executeUpdateHandlingTime(AmazonLinkManagementConfig config, String accountNumber) {
        AmazonLinkManagementConfigDTO configDTO = AmazonLinkManagementConfigDTO.fromEntity(config);
        Integer ruleType = configDTO.getRuleType();
        if (LinkManagementEnums.HandTimeRuleType.PRICE_RANGE.isTrue(ruleType)) {
            // 价格区间
            executeUpdateHandlingTimeByPriceRange(configDTO, accountNumber);
        } else if (LinkManagementEnums.HandTimeRuleType.DESIGNATED_SKU.isTrue(ruleType)) {
            // 指定SKU
            executeUpdateHandlingTimeBySkuList(configDTO, accountNumber);
        } else if (LinkManagementEnums.HandTimeRuleType.PRICE_WEIGHT_TAG.isTrue(ruleType)) {
            // 价格+重量+标签
            executeUpdateHandlingTimeByPriceWeightTag(configDTO, accountNumber);
        }
    }

    private void executeUpdateHandlingTimeByPriceRange(AmazonLinkManagementConfigDTO configDTO, String accountNumber) {
        AmazonLinkManagementRuleDTO ruleInfo = configDTO.getRuleInfo();
        PriceRangeDTO priceRange = ruleInfo.getPriceRange();
        Integer updateLimitDay = ruleInfo.getUpdateLimitDay();
        Integer newHandlingTime = ruleInfo.getNewHandlingTime();
        EsAmazonProductListingRequest request = getBasicProductListingRequest(ruleInfo, accountNumber);

        amazonProductListingService.scrollQueryExecutorTask(request, listings -> {
            if (CollectionUtils.isEmpty(listings)) {
                return;
            }
            // 总价在线列表的总价字段，若总价为空时，计算总价，总价=价格+运费
            List<EsAmazonProductListing> matchedPriceListing = listings.stream()
                    .peek(listing -> {
                        if (listing.getTotalPrice() == null && listing.getPrice() != null) {
                            Double shippingCost = Optional.ofNullable(listing.getShippingCost()).orElse(0d);
                            listing.setTotalPrice(listing.getPrice() + shippingCost);
                        }
                        if (listing.getTotalPrice() == null && listing.getPrice() == null) {
                            listing.setTotalPrice(0d);
                        }
                    })
                    .filter(listing -> {
                        if (listing.getTotalPrice() == null) {
                            return false;
                        }

                        BigDecimal totalPrice = BigDecimal.valueOf(listing.getTotalPrice());
                        return totalPrice.compareTo(priceRange.getMinPrice()) >= 0 && totalPrice.compareTo(priceRange.getMaxPrice()) < 0;
                    }).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(matchedPriceListing)) {
                return;
            }
            executeUpdateHandlingTime(matchedPriceListing, updateLimitDay, newHandlingTime);
        });
    }

    private void executeUpdateHandlingTime(List<EsAmazonProductListing> matchedPriceListing, Integer lastOpenDateExceeding, Integer newHandlingTime) {
        try {
            filterExistUpdateHandlingTime(matchedPriceListing, lastOpenDateExceeding);
            if (CollectionUtils.isEmpty(matchedPriceListing)) {
                return;
            }

            List<AmazonProductListing> listingDataList = matchedPriceListing.stream().map(listing -> {
                Integer quantity = Optional.ofNullable(listing.getQuantity()).orElse(0);
                AmazonProductListing amazonProductListing = new AmazonProductListing();
                amazonProductListing.setAccountNumber(listing.getAccountNumber());
                amazonProductListing.setSellerSku(listing.getSellerSku());
                amazonProductListing.setFulfillmentLatency(newHandlingTime);
                if (listing.getFulfillmentLatency() != null) {
                    amazonProductListing.setOldFulfillmentLatencyValue(listing.getFulfillmentLatency().toString());
                }
                amazonProductListing.setOldQuantityValue(quantity.toString());
                amazonProductListing.setQuantity(quantity);
                return amazonProductListing;

            }).collect(Collectors.toList());
            AmazonProductListingDto updateProductListingDto = new AmazonProductListingDto();
            updateProductListingDto.setFulfillmentLatency(newHandlingTime);
            updateProductListingDto.setAmazonProductListingList(listingDataList);
            amazonJSONListingFeedService.batchListingFulfillmentLatency(updateProductListingDto);
        } catch (Exception e) {
            log.error("修改备货期异常", e);
        }
    }

    private void filterExistUpdateHandlingTime(List<EsAmazonProductListing> matchedPriceListing, Integer lastOpenDateExceeding) {
        LocalDateTime creatingDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).minusDays(lastOpenDateExceeding);

        List<String> sellerSkus = matchedPriceListing.stream().map(EsAmazonProductListing::getSellerSku).collect(Collectors.toList());
        AmazonProcessReportExample example = new AmazonProcessReportExample();
        example.setFiledColumns("id,account_number,data_value,feed_type,creation_date");
        AmazonProcessReportExample.Criteria criteria = example.createCriteria();
        criteria.andAccountNumberEqualTo(matchedPriceListing.get(0).getAccountNumber());
        criteria.andDataValueIn(sellerSkus);
        criteria.apply("(status_code = 'Processing' or status = true)");
        criteria.andRelationTypeEqualTo(SystemFeedTypeEnum.LISTING_HANDING_TIME_FEED_DOCUMENT.getSystemFeedType());
        criteria.andCreationDateGreaterThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(creatingDate));
        List<AmazonProcessReport> amazonProcessReports = amazonProcessReportService.selectFiledColumnsByExample(example);
        if (CollectionUtils.isNotEmpty(amazonProcessReports)) {
            matchedPriceListing.removeIf(listing ->
                    amazonProcessReports.stream().anyMatch(report -> report.getDataValue().equals(listing.getSellerSku())));
        }


    }

    private void executeUpdateHandlingTimeBySkuList(AmazonLinkManagementConfigDTO configDTO, String accountNumber) {
        AmazonLinkManagementRuleDTO ruleInfo = configDTO.getRuleInfo();
        List<String> sellerSkuList = ruleInfo.getSellerSkuList();
        Integer updateLimitDay = ruleInfo.getUpdateLimitDay();
        Integer newHandlingTime = ruleInfo.getNewHandlingTime();
        if (CollectionUtils.isEmpty(sellerSkuList)) {
            return;
        }
        EsAmazonProductListingRequest request = getBasicProductListingRequest(ruleInfo, accountNumber);
        request.setSellerSkuList(sellerSkuList);
        amazonProductListingService.scrollQueryExecutorTask(request, listings -> {
            if (CollectionUtils.isEmpty(listings)) {
                return;
            }
            executeUpdateHandlingTime(listings, updateLimitDay, newHandlingTime);
        });

    }

    private EsAmazonProductListingRequest getBasicProductListingRequest(AmazonLinkManagementRuleDTO ruleInfo, String accountNumber) {
        List<String> asinStatus = ruleInfo.getAsinStatus();
        List<String> itemStatus = ruleInfo.getItemStatus();
        SalesRangeDTO salesRange = ruleInfo.getSalesRange();

        EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
        request.setAccountNumber(accountNumber);
        request.setIsOnline(true);
        request.setFields(new String[]{"id", "accountNumber", "skuDataSource", "sellerSku", "site", "sonAsin", "price", "totalPrice", "shippingCost", "articleNumber", "quantity", "fulfillmentLatency"});
        if (CollectionUtils.isNotEmpty(asinStatus)) {
            request.setItemStatusList(asinStatus);
        }
        if (CollectionUtils.isNotEmpty(itemStatus)) {
            request.setSkuStatusList(itemStatus);
        }
        if (salesRange != null) {
            Triple<String, Integer, Integer> salesRangeTuple = Triple.of(salesRange.getSalesType(), salesRange.getMinSales(), salesRange.getMaxSales());
            request.setCustomSaleNumberFileRangeQuery(salesRangeTuple);
        } else {
            request.setSaleQuantityBean("order_num_total");
            request.setIsSaleQuantityNull(true);
        }
        return request;
    }

    private void executeUpdateHandlingTimeByPriceWeightTag(AmazonLinkManagementConfigDTO configDTO, String accountNumber) {
        AmazonLinkManagementRuleDTO ruleInfo = configDTO.getRuleInfo();
        List<String> productTags = ruleInfo.getProductTags();
        PriceRangeDTO priceRange = ruleInfo.getPriceRange();
        WeightRangeDTO weightRange = ruleInfo.getWeightRange();
        Integer updateLimitDay = ruleInfo.getUpdateLimitDay();
        Integer newHandlingTime = ruleInfo.getNewHandlingTime();

        EsAmazonProductListingRequest request = getBasicProductListingRequest(ruleInfo, accountNumber);
        if (CollectionUtils.isNotEmpty(productTags)) {
            request.setTagCodeList(productTags);
        }

        amazonProductListingService.scrollQueryExecutorTask(request, listings -> {
            if (CollectionUtils.isEmpty(listings)) {
                return;
            }

            List<Integer> skuDataSources = List.of(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode(), SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());

            Map<Integer, List<EsAmazonProductListing>> skuDataSourceListingsMap = listings.stream()
                    .filter(listing -> StringUtils.isNotBlank(listing.getArticleNumber()))
                    .filter(listing -> Objects.nonNull(listing.getSkuDataSource()))
                    .filter(listing -> skuDataSources.contains(listing.getSkuDataSource()))
                    .collect(Collectors.groupingBy(EsAmazonProductListing::getSkuDataSource));

            if (MapUtils.isEmpty(skuDataSourceListingsMap)) {
                return;
            }
            List<ProductLogisticsInfoDO> productLogisticsInfos = new ArrayList<>();
            skuDataSourceListingsMap.forEach((skuDataSource, skuDataSourceListings) -> {
                List<String> skus = skuDataSourceListings.stream().map(EsAmazonProductListing::getArticleNumber).collect(Collectors.toList());
                if (SkuDataSourceEnum.COMPOSE_SYSTEM.isTrue(skuDataSource)) {
                    ApiResult<List<ProductLogisticsInfoDO>> composeAndSuiteLogisticsInfos = productLogisticsUtils.getComposeAndSuiteLogisticsInfo(skus);
                    if (composeAndSuiteLogisticsInfos.isSuccess()) {
                        List<ProductLogisticsInfoDO> productLogisticsInfoDOS = composeAndSuiteLogisticsInfos.getResult();
                        productLogisticsInfos.addAll(productLogisticsInfoDOS);
                    }
                }

                if (SkuDataSourceEnum.PRODUCT_SYSTEM.isTrue(skuDataSource)) {
                    ApiResult<List<ProductLogisticsInfoDO>> singleLogisticsInfos = productLogisticsUtils.getSingleProductLogisticsInfo(skus);
                    if (singleLogisticsInfos.isSuccess()) {
                        List<ProductLogisticsInfoDO> productLogisticsInfoDOS = singleLogisticsInfos.getResult();
                        productLogisticsInfos.addAll(productLogisticsInfoDOS);
                    }
                }
            });

            if (CollectionUtils.isEmpty(productLogisticsInfos)) {
                log.error("获取物流信息失败, accountNumber={}, sellerSkus={}", accountNumber, listings.stream().map(EsAmazonProductListing::getSellerSku).collect(Collectors.toList()));
                return;
            }

            Map<String, ProductLogisticsInfoDO> productLogisticsInfoDOMap = productLogisticsInfos.stream().collect(Collectors.toMap(ProductLogisticsInfoDO::getSku, Function.identity(), (o1, o2) -> o1));

            // 总价在线列表的总价字段，若总价为空时，计算总价，总价=价格+运费
            List<EsAmazonProductListing> matchedPriceListing = listings.stream()
                    .filter(listing -> StringUtils.isNotBlank(listing.getArticleNumber()))
                    .peek(listing -> {
                        if (listing.getTotalPrice() == null && listing.getPrice() != null) {
                            Double shippingCost = Optional.ofNullable(listing.getShippingCost()).orElse(0d);
                            listing.setTotalPrice(listing.getPrice() + shippingCost);
                        }
                        if (listing.getTotalPrice() == null && listing.getPrice() == null) {
                            listing.setTotalPrice(0d);
                        }
                    })
                    .filter(listing -> {
                        if (listing.getTotalPrice() == null) {
                            return false;
                        }

                        BigDecimal totalPrice = BigDecimal.valueOf(listing.getTotalPrice());
                        return totalPrice.compareTo(priceRange.getMinPrice()) >= 0 && totalPrice.compareTo(priceRange.getMaxPrice()) < 0;
                    })
                    .filter(listing -> {
                        String articleNumber = listing.getArticleNumber();
                        ProductLogisticsInfoDO productLogisticsInfoDO = productLogisticsInfoDOMap.get(articleNumber);
                        if (productLogisticsInfoDO == null) {
                            return false;
                        }

                        BigDecimal calcWeight = BigDecimal.valueOf(productLogisticsInfoDO.getCalcWeight());
                        return calcWeight.compareTo(weightRange.getMinWeight()) >= 0 && calcWeight.compareTo(weightRange.getMaxWeight()) < 0;
                    }).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(matchedPriceListing)) {
                return;
            }
            executeUpdateHandlingTime(matchedPriceListing, updateLimitDay, newHandlingTime);
        });

    }

    /**
     * 任务消息数据类
     */
    @Data
    public static class AmazonLinkManagementHandlingTimeMessage {
        private Long configId;
        private String accountNumber;
        private Long taskRecordId;
        private Integer configLevel;
    }
}
