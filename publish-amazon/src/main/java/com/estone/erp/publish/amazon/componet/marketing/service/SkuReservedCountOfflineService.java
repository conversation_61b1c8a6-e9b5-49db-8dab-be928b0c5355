package com.estone.erp.publish.amazon.componet.marketing.service;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.alert.logger.PublishLogger;
import com.estone.erp.common.alert.logger.PublishLoggerFactory;
import com.estone.erp.common.alert.policy.DefaultAlertPolicy;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.publish.amazon.componet.marketing.AmazonOfflineConfigHandlerService;
import com.estone.erp.publish.amazon.mq.model.AmazonOfflineConfigMessage;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.AmazonOfflineConfigDO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.AmazonOfflineConfigVO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.enums.OfflineConfigEnums;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.enums.OfflineQueueEnums;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.rule.SkuReservedCountRuleDO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonOfflineConfigListingQueue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SkuReservedCountOfflineService extends AbstractOfflineConfigService implements AmazonOfflineConfigHandlerService {
    private final PublishLogger logger = PublishLoggerFactory.getLogger(SkuReservedCountOfflineService.class);

    @Override
    public String getOfflineType() {
        return OfflineConfigEnums.OfflineType.SKU_RESERVED_COUNT.name();
    }

    @Data
    static class OfflineContext {
        private Integer taskCode;
        private AmazonOfflineConfigVO config;
        private LocalDateTime afterOpenDateTime;
        private AmazonOfflineConfigMessage message;
        private List<Predicate<AmazonAsinSaleCountDO>> predicateList;
    }

    @Override
    public ApiResult<String> executeOfflineConfig(AmazonOfflineConfigMessage message) {
        // 实现SKU保留链接数下架逻辑
        try {
            Integer configId = message.getConfigId();
            ApiResult<AmazonOfflineConfigVO> configVOApiResult = amazonOfflineConfigService.editConfig(configId);
            if (!configVOApiResult.isSuccess()) {
                return ApiResult.newError("查询配置失败：" + configId);
            }
            AmazonOfflineConfigVO configVO = configVOApiResult.getResult();
            if (configVO == null) {
                return ApiResult.newError("配置不存在");
            }
            message.setConfirmedTime(configVO.getConfig().getConfirmedTime());
            // 1. 按SKU查询符合条件的Listing
            // 2. 按站点分组
            // 3. 每个SKU在对应站点的数量
            // 4. 匹配规则
            executeSearchHandler(configVO, message);
            return ApiResult.newSuccess("SKU保留链接数下架处理完成");
        } catch (Exception e) {
            logger.errorForPolicy("店铺:{},执行下架配置【{}】异常,e:{}", getOfflineType(), new DefaultAlertPolicy(), message.getAccountNumber(), message.getConfigId(), e.getMessage(), e);
            return ApiResult.newError("执行SKU保留链接数下架失败：" + e.getMessage());
        } finally {
            String countKey = RedisConstant.AMAZON_SKU_RESERVED_COUNT + message.getConfigId();
            long taskCount = PublishRedisClusterUtils.incrementAndGet(countKey);
            String totalKey = RedisConstant.AMAZON_SKU_RESERVED_COUNT_TOTAL + message.getConfigId();
            String total = PublishRedisClusterUtils.get(totalKey);
            long totalNumber = Long.parseLong(Optional.ofNullable(total).orElseGet(() -> "0"));
            logger.info("执行SKU保留链接数,configID:{},total:{},currentTask:{}", message.getConfigId(), total, taskCount);
            if (taskCount >= totalNumber && totalNumber != 0) {
                // 5. 所有消息执行完毕，统计店铺数据
                statisticsAccountReport(message);
            }

        }
    }

    private void statisticsAccountReport(AmazonOfflineConfigMessage message) {
        ApiResult<AmazonOfflineConfigVO> configVOApiResult = amazonOfflineConfigService.editConfig(message.getConfigId());
        if (!configVOApiResult.isSuccess()) {
            return;
        }
        AmazonOfflineConfigVO configVO = configVOApiResult.getResult();
        AmazonOfflineConfigDO config = configVO.getConfig();
        int taskTaskCode = OfflineConfigEnums.OfflineType.SKU_RESERVED_COUNT.getCode();
        SkuReservedCountRuleDO skuReservedCountRule = configVO.getSkuReservedCountRule();
        Integer reservedCount = skuReservedCountRule.getReservedCount();
        List<String> configAccountNumbers = config.getAccounts();
        if (CollectionUtils.isEmpty(configAccountNumbers)) {
            return;
        }

        for (String accountNumber : configAccountNumbers) {
            try {
                log.info("执行SKU保留链接数-{}, 开始统计店铺[{}]可下架链接", config.getRuleName(), accountNumber);
                // 查询店铺统计日期内待下架数量
                Integer waitOffLinkTotal = amazonOfflineConfigListingQueueService.getAccountOffCountByStatus(accountNumber, message.getConfirmedTime(), message.getScheduleTime(), OfflineQueueEnums.QueueStatusType.PENDING_OFFLINE.getCode(), OfflineConfigEnums.OfflineType.SKU_RESERVED_COUNT.getCode());

                // 获取店铺在售链接数据
                Long onlineListingNum = esAmazonProductListingService.getOnlineListingNum(List.of(accountNumber));
                Integer offlinePercentage = configVO.getConfig().getOfflinePercentage();
                double limitRatio = offlinePercentage / 100.0;
                // 下架链接总数量：在线 * limitRatio 向下取整
                double canOffLink = onlineListingNum * limitRatio;
                int needOfflineTotal = (int) Math.floor(canOffLink);
                logger.info("店铺:{},保留链接数量:{},在线链接数量:{},下架比例:{},待下架链接数量:{},可下架链接数量:{},需下架链接数量:{}", accountNumber, reservedCount, onlineListingNum, limitRatio, waitOffLinkTotal, canOffLink, needOfflineTotal);
                // 记录店铺处理报告
                message.setAccountNumber(accountNumber);
                addAccountReport(message, offlinePercentage, Math.toIntExact(onlineListingNum), waitOffLinkTotal, needOfflineTotal);

                // 检查下架数据记录表状态流转，
                amazonOfflineConfigDataStatisticsService.updateExecutionStatus(message.getConfigId());

                if (needOfflineTotal <= 0) {
                    continue;
                }
                // 按上架时间排序正序(旧->新),修改对应条数的待下架链接
                if (waitOffLinkTotal <= needOfflineTotal) {
                    amazonOfflineConfigListingQueueService.updateOffLinkStatusOrderOpenTimeLimit(accountNumber, message.getConfirmedTime(), message.getScheduleTime(), taskTaskCode, OfflineQueueEnums.QueueStatusType.CAN_OFFLINE_STATUS.getCode(), waitOffLinkTotal);
                } else {
                    amazonOfflineConfigListingQueueService.updateOffLinkStatusOrderOpenTimeLimit(accountNumber, message.getConfirmedTime(), message.getScheduleTime(), taskTaskCode, OfflineQueueEnums.QueueStatusType.CAN_OFFLINE_STATUS.getCode(), needOfflineTotal);
                }
            } catch (Exception e) {
                log.error("执行SKU保留链接数异常-{}, 店铺[{}],error:{}", config.getRuleName(), accountNumber, e.getMessage(), e);
            }
        }

    }

    private void setQueueFiled(AmazonOfflineConfigListingQueue queue, EsAmazonProductListing listing, SkuReservedCountRuleDO skuReservedCountRule, Map<String, AmazonAsinSaleCountDO> sonAsinSaleCountMap, AmazonOfflineConfigMessage message) {
        queue.setTaskType(OfflineConfigEnums.OfflineType.SKU_RESERVED_COUNT.getCode());
        setPublishQueueFiled(queue, listing, sonAsinSaleCountMap, message);
        Map<String, SkuReservedCountRuleDO> ruleData = new HashMap<>();
        SkuReservedCountRuleDO listingRule = skuReservedCountRule.matchListingRule(listing, sonAsinSaleCountMap.get(listing.getSonAsin()));
        ruleData.put("rule", skuReservedCountRule);
        ruleData.put("listing", listingRule);
        queue.setRuleInfo(JSON.toJSONString(ruleData));
    }

    /**
     * 根据sku保留链接数规则
     *
     * @param configVO 配置信息
     */
    private void executeSearchHandler(AmazonOfflineConfigVO configVO, AmazonOfflineConfigMessage message) {
        SkuReservedCountRuleDO skuReservedCountRule = configVO.getSkuReservedCountRule();
        Integer lastOpenDateWithin = skuReservedCountRule.getLastOpenDateWithin();

        AmazonOfflineConfigDO config = configVO.getConfig();
        int taskTaskCode = OfflineConfigEnums.OfflineType.SKU_RESERVED_COUNT.getCode();
        // 销量匹配规则
        List<Predicate<AmazonAsinSaleCountDO>> predicateList = transferToSaleCountCompareRules(skuReservedCountRule.getSaleCount());

        OfflineContext context = new OfflineContext();
        context.setConfig(configVO);
        context.setMessage(message);
        context.setTaskCode(taskTaskCode);
        context.setPredicateList(predicateList);

        if (lastOpenDateWithin != null) {
            LocalDateTime afterOpenDateTime = LocalDateTime.of(LocalDate.now().minusDays(lastOpenDateWithin), LocalTime.MIN);
            logger.info("根据sku保留链接数,configId:{}-{}, scheduleTime:{}, sku:{}", config.getId(), config.getRuleName(), message.getScheduleTime(), message.getSkus());
            context.setAfterOpenDateTime(afterOpenDateTime);
        }

        EsAmazonProductListingRequest request = buildEsAmazonProductListingRequest(null, skuReservedCountRule);
        request.setIsSaleQuantityNull(null);
        request.setSaleQuantityBean(null);
        request.setToSaleQuantity(null);

        // 设置SKU过滤条件
        if (StringUtils.isNotBlank(message.getSkus())) {
            request.setArticleNumberList(Arrays.asList(message.getSkus().split(",")));
        }
        // 按站点查询
        if (OfflineConfigEnums.AccountType.SITE.isTrue(config.getAccountType())) {
            List<String> siteList = Arrays.asList(config.getAccountOption().split(","));
            request.setSiteList(siteList);
        }

        esAmazonProductListingService.scrollQueryExecutorTask(request, listings -> {
            try {
                listingHandler(listings, context);
            } catch (Exception e) {
                logger.error("sku保留链接数,保留最新上架时间{}天内的链接处理失败", lastOpenDateWithin, e);
            }
        });

    }

    private void listingHandler(List<EsAmazonProductListing> listings, OfflineContext context) {
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        Map<String, List<EsAmazonProductListing>> siteListingMap = listings.stream().collect(Collectors.groupingBy(EsAmazonProductListing::getSite));
        siteListingMap.forEach((site, siteListings) -> {
            siteListingHandler(site, siteListings, context);
        });
    }

    private void siteListingHandler(String site, List<EsAmazonProductListing> siteListings, OfflineContext context) {
        // 获取所有Asin的销量
        List<String> sonAsins = siteListings.stream().map(EsAmazonProductListing::getSonAsin).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, AmazonAsinSaleCountDO> sonAsinSaleCountMap = esAmazonProductListingService.getSonAsinSaleCount(sonAsins);
        // 按货号分组
        Map<String, List<EsAmazonProductListing>> articleNumberListingMap = siteListings.stream().collect(Collectors.groupingBy(EsAmazonProductListing::getArticleNumber));
        articleNumberListingMap.forEach((sku, skuListings) -> {
            try {
                // 按SKU进行匹配规则
                skuListingHandler(site, sku, skuListings, sonAsinSaleCountMap, context);
            } catch (Exception e) {
                log.error("site:{},sku:{},统计可下架链接异常：{}", site, sku, e.getMessage(), e);
            }
        });
    }


    private void skuListingHandler(String site, String sku, List<EsAmazonProductListing> skuListings, Map<String, AmazonAsinSaleCountDO> sonAsinSaleCountMap, OfflineContext context) {
        // 按是否有销量分组
        Function<EsAmazonProductListing, String> matchedGroupFunction = listing -> {
            String sonAsin = listing.getSonAsin();
            AmazonAsinSaleCountDO amazonAsinSaleCountDO = sonAsinSaleCountMap.get(sonAsin);
            if (amazonAsinSaleCountDO == null) {
                return Boolean.FALSE.toString();
            }
            boolean matchAsinSaleCount = context.getPredicateList().stream().anyMatch(predicate -> predicate.test(amazonAsinSaleCountDO));
            // 销量满足规则
            if (matchAsinSaleCount) {
                return Boolean.FALSE.toString();
            } else {
                return Boolean.TRUE.toString();
            }

        };
        // 如果所有sku链接总数小于X个, 则不下架
        Integer reservedCount = context.getConfig().getSkuReservedCountRule().getReservedCount();
        Map<String, List<EsAmazonProductListing>> asinMatchedGroup = skuListings.stream().collect(Collectors.groupingBy(matchedGroupFunction));
        int trueSize = asinMatchedGroup.getOrDefault(Boolean.TRUE.toString(), new ArrayList<>()).size();
        int falseSize = asinMatchedGroup.getOrDefault(Boolean.FALSE.toString(), new ArrayList<>()).size();
        int allListingSize = trueSize + falseSize;
        if (allListingSize <= reservedCount) {
            logger.info("sku[{}]链接总数{}小于{}个, 不下架", sku, allListingSize, reservedCount);
            return;
        }


        // 保留链接
        List<AmazonOfflineConfigListingQueue> matchedReservedList = new ArrayList<>(skuListings.size());
        List<AmazonOfflineConfigListingQueue> noMatchedReservedList = new ArrayList<>(skuListings.size());
        AtomicInteger matchedReservedCount = new AtomicInteger(0);
        asinMatchedGroup.forEach((matchType, listingDataList) -> {
            if (matchType.equals(Boolean.TRUE.toString())) {
                if (context.getConfig().getSkuReservedCountRule().getLastOpenDateWithin() != null) {
                    int limitSize = reservedCount - matchedReservedCount.get();

                    List<EsAmazonProductListing> matchedListing = listingDataList.stream().filter(listing -> {
                        Date openDate = Optional.ofNullable(listing.getOpenDate()).orElseGet(() -> Optional.ofNullable(listing.getFirstOpenDate()).orElseGet(Date::new));
                        LocalDateTime openDateTime = LocalDateTimeUtil.of(openDate);
                        boolean matchDate = openDateTime.compareTo(context.getAfterOpenDateTime()) >= 0;
                        if (matchDate) {
                            return true;
                        } else {
                            return false;
                        }
                    }).limit(limitSize).collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(matchedListing)) {
                        return;
                    }
                    matchedReservedCount.addAndGet(matchedListing.size());
                    // 满足规则保留链接
                    BiConsumer<EsAmazonProductListing, AmazonOfflineConfigListingQueue> func = (list, queue) -> {
                        queue.setStatus(OfflineQueueEnums.QueueStatusType.RESERVE_OFFLINE.getCode());
                        setQueueFiled(queue, list, context.getConfig().getSkuReservedCountRule(), sonAsinSaleCountMap, context.getMessage());
                    };
                    List<AmazonOfflineConfigListingQueue> queues = amazonOfflineConfigListingQueueService.addListingToOffLinkQueue(matchedListing, context.getConfig(), func);
                    matchedReservedList.addAll(queues);
                    return;
                }

                // 满足规则保留链接
                BiConsumer<EsAmazonProductListing, AmazonOfflineConfigListingQueue> func = (list, queue) -> {
                    queue.setStatus(OfflineQueueEnums.QueueStatusType.RESERVE_OFFLINE.getCode());
                    setQueueFiled(queue, list, context.getConfig().getSkuReservedCountRule(), sonAsinSaleCountMap, context.getMessage());
                };
                List<AmazonOfflineConfigListingQueue> queues = amazonOfflineConfigListingQueueService.addListingToOffLinkQueue(listingDataList, context.getConfig(), func);
                matchedReservedList.addAll(queues);
            } else {
                // 过滤下架定时公共规则
                List<EsAmazonProductListing> matchingList = filterPublishConfig(listingDataList);
                if (CollectionUtils.isEmpty(matchingList)) {
                    return;
                }
                // 不满足规则写入待下架链接
                BiConsumer<EsAmazonProductListing, AmazonOfflineConfigListingQueue> func = (list, queue) -> {
                    queue.setStatus(OfflineQueueEnums.QueueStatusType.PENDING_OFFLINE.getCode());
                    setQueueFiled(queue, list, context.getConfig().getSkuReservedCountRule(), sonAsinSaleCountMap, context.getMessage());
                };
                List<AmazonOfflineConfigListingQueue> queues = amazonOfflineConfigListingQueueService.addListingToOffLinkQueue(matchingList, context.getConfig(), func);
                noMatchedReservedList.addAll(queues);
            }
        });


        // 如果sku满足规则链接大于等于X个,则保留所有有销量链接,其他加入待下架池
        if (CollectionUtils.isNotEmpty(matchedReservedList) && matchedReservedList.size() >= reservedCount) {
            logger.info("sku[{}]满足规则链接{}大于等于{}个, 无需补充", sku, matchedReservedList.size(), reservedCount);
            if (CollectionUtils.isNotEmpty(noMatchedReservedList)) {
                amazonOfflineConfigListingQueueService.saveBatch(noMatchedReservedList, 300);
            }
            if (CollectionUtils.isNotEmpty(matchedReservedList)) {
                amazonOfflineConfigListingQueueService.saveBatch(matchedReservedList, 300);
            }
            return;
        }

        // 如果总链接数大于保留链接数且满足规则链接不足保留链接数,则在不满足规则链接中补充至保留链接数,剩余的不满足链接下架
        int limitSize = reservedCount - matchedReservedList.size();
        if (CollectionUtils.isEmpty(noMatchedReservedList) || noMatchedReservedList.size() < limitSize) {
            noMatchedReservedList.forEach(queue -> {
                queue.setStatus(OfflineQueueEnums.QueueStatusType.RESERVE_OFFLINE.getCode());
                queue.setRemark("补充保留链接");
            });
        } else {
            // 对不满足规则链接按上架时间倒序
            List<AmazonOfflineConfigListingQueue> priorityOpenDateListings = noMatchedReservedList.stream()
                    .sorted(Comparator.comparing(AmazonOfflineConfigListingQueue::getOpenTime).reversed()).collect(Collectors.toList());
            for (int i = 0; i < priorityOpenDateListings.size(); i++) {
                AmazonOfflineConfigListingQueue queue = priorityOpenDateListings.get(i);
                if (i < limitSize) {
                    queue.setStatus(OfflineQueueEnums.QueueStatusType.RESERVE_OFFLINE.getCode());
                    queue.setRemark("补充保留链接");
                }
            }
        }
        if (CollectionUtils.isNotEmpty(noMatchedReservedList)) {
            amazonOfflineConfigListingQueueService.saveBatch(noMatchedReservedList, 300);
        }
        if (CollectionUtils.isNotEmpty(matchedReservedList)) {
            amazonOfflineConfigListingQueueService.saveBatch(matchedReservedList, 300);
        }
        log.info("site:{},sku:{},统计可下架链接, 满足规则链接：{}, 不满足规则链接:{}, 还需:{}, 待下架链接：{}", site, sku, matchedReservedList.size(), noMatchedReservedList.size(), limitSize, noMatchedReservedList.size() - limitSize);

    }


}
