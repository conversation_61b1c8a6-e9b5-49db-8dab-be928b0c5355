package com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement.rule;

import lombok.Data;
import java.io.Serializable;

/**
 * <p>
 * Amazon链接管理配置 - 销量区间DTO
 * 对应设计文档3.4.2 rule字段结构中的salesRange
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Data
public class SalesRangeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 销量类型
     * 例如: "order_last_30d_count" - 近30天订单数
     */
    private String salesType;

    /**
     * 最小销量
     */
    private Integer minSales;

    /**
     * 最大销量
     */
    private Integer maxSales;
}