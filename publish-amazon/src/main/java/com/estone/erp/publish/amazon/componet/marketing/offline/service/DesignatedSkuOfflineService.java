package com.estone.erp.publish.amazon.componet.marketing.offline.service;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.alert.logger.PublishLogger;
import com.estone.erp.common.alert.logger.PublishLoggerFactory;
import com.estone.erp.common.alert.policy.DefaultAlertPolicy;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.componet.marketing.offline.AmazonOfflineConfigHandlerService;
import com.estone.erp.publish.amazon.mq.model.AmazonOfflineConfigMessage;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.AmazonOfflineConfigVO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.enums.OfflineConfigEnums;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.enums.OfflineQueueEnums;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.rule.DesignatedSkuRuleDO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonOfflineConfigListingQueue;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
public class DesignatedSkuOfflineService extends AbstractOfflineConfigService implements AmazonOfflineConfigHandlerService {
    private final PublishLogger logger = PublishLoggerFactory.getLogger(DesignatedSkuOfflineService.class);

    @Override
    public String getOfflineType() {
        return OfflineConfigEnums.OfflineType.DESIGNATED_SKU.name();
    }

    @Override
    public ApiResult<String> executeOfflineConfig(AmazonOfflineConfigMessage message) {
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            // 实现链接包含侵权词下架逻辑
            Integer configId = message.getConfigId();
            String accountNumber = message.getAccountNumber();
            ApiResult<AmazonOfflineConfigVO> configVOApiResult = amazonOfflineConfigService.editConfig(configId);
            if (!configVOApiResult.isSuccess()) {
                return ApiResult.newError("查询配置失败：" + configId);
            }
            AmazonOfflineConfigVO configVO = configVOApiResult.getResult();
            if (configVO == null) {
                return ApiResult.newError("配置不存在");
            }
            message.setConfirmedTime(configVO.getConfig().getConfirmedTime());
            DesignatedSkuRuleDO designatedSkuRule = configVO.getDesignatedSkuRule();
            String skus = designatedSkuRule.getSkus();
            String[] skuArray = skus.split(",");
            List<String> skuList = Arrays.asList(skuArray);
            List<List<String>> partition = Lists.partition(skuList, 100);

            for (List<String> skuPartition : partition) {
                executeDesignatedSkuOffline(accountNumber, skuPartition, configVO, message);
            }
            stopWatch.stop();
            logger.info("店铺:{},执行指定sku下架配置成功,配置ID:{},耗时:{}", accountNumber, configId, stopWatch.formatTime());
        } catch (Exception e) {
            logger.errorForPolicy("店铺:{},执行下架配置【{}】异常,e:{}", getOfflineType(), new DefaultAlertPolicy(), message.getAccountNumber(), message.getConfigId(), e.getMessage(), e);
        }
        return ApiResult.newSuccess("执行指定sku下架配置成功");

    }

    private void executeDesignatedSkuOffline(String accountNumber, List<String> skus, AmazonOfflineConfigVO configVO, AmazonOfflineConfigMessage message) {
        DesignatedSkuRuleDO designatedSkuRule = configVO.getDesignatedSkuRule();

        List<Predicate<AmazonAsinSaleCountDO>> predicateList = transferToSaleCountCompareRules(designatedSkuRule.getSaleCount());

        EsAmazonProductListingRequest request = buildEsAmazonProductListingRequest(accountNumber, designatedSkuRule);
        Consumer<BoolQueryBuilder> func = boolQueryBuilder -> {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("articleNumber", skus));
        };
        esAmazonProductListingService.scrollQueryExecutorTask(request, func, (listings) -> {
            try {
                listingHandler(listings, designatedSkuRule, predicateList, configVO, message);
            } catch (Exception e) {
                logger.error("店铺:{},执行下架配置【{}-{}】统计链接异常,e:{}", accountNumber, configVO.getConfig().getId(), configVO.getConfig().getRuleName(), e.getMessage(), e);
            }
        });

        // 查询店铺统计日期内待下架数量
        Integer waitOffLinkTotal = amazonOfflineConfigListingQueueService.getAccountOffCountByStatus(accountNumber, message.getConfirmedTime(), message.getScheduleTime(), OfflineQueueEnums.QueueStatusType.PENDING_OFFLINE.getCode(), OfflineConfigEnums.OfflineType.DESIGNATED_SKU.getCode());

        // 获取店铺在售链接数据
        Long onlineListingNum = esAmazonProductListingService.getOnlineListingNum(List.of(accountNumber));
        Integer offlinePercentage = configVO.getConfig().getOfflinePercentage();
        double limitRatio = offlinePercentage / 100.0;
        // 下架链接总数量：在线 * limitRatio 向下取整
        double canOffLink = onlineListingNum * limitRatio;
        int needOfflineTotal = (int) Math.floor(canOffLink);

        // 记录店铺处理报告
        addAccountReport(message, offlinePercentage, Math.toIntExact(onlineListingNum), waitOffLinkTotal, needOfflineTotal);

        // 检查下架数据记录表状态流转，
        amazonOfflineConfigDataStatisticsService.updateExecutionStatus(message.getConfigId());

        if (needOfflineTotal <= 0) {
            return;
        }
        int taskTaskCode = OfflineConfigEnums.OfflineType.DESIGNATED_SKU.getCode();
        // 按上架时间排序正序(旧->新),修改对应条数的待下架链接
        if (waitOffLinkTotal <= needOfflineTotal) {
            amazonOfflineConfigListingQueueService.updateOffLinkStatusOrderOpenTimeLimit(accountNumber, message.getConfirmedTime(), message.getScheduleTime(), taskTaskCode, OfflineQueueEnums.QueueStatusType.CAN_OFFLINE_STATUS.getCode(), waitOffLinkTotal);
        } else {
            amazonOfflineConfigListingQueueService.updateOffLinkStatusOrderOpenTimeLimit(accountNumber, message.getConfirmedTime(), message.getScheduleTime(), taskTaskCode, OfflineQueueEnums.QueueStatusType.CAN_OFFLINE_STATUS.getCode(), needOfflineTotal);
        }

    }

    private void listingHandler(List<EsAmazonProductListing> listings, DesignatedSkuRuleDO designatedSkuRule, List<Predicate<AmazonAsinSaleCountDO>> predicateList, AmazonOfflineConfigVO configVO, AmazonOfflineConfigMessage message) {
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        // 过滤下架定时公共规则
        List<EsAmazonProductListing> matchingList = filterPublishConfig(listings);
        if (CollectionUtils.isEmpty(matchingList)) {
            return;
        }
        List<String> sonAsinList = matchingList.stream().map(EsAmazonProductListing::getSonAsin).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, AmazonAsinSaleCountDO> sonAsinSaleCountMap = esAmazonProductListingService.getSonAsinSaleCount(sonAsinList);

        List<EsAmazonProductListing> waitOfflineListings = matchingList.stream().filter(listing -> {
            String sonAsin = listing.getSonAsin();
            AmazonAsinSaleCountDO amazonAsinSaleCountDO = sonAsinSaleCountMap.get(sonAsin);
            if (amazonAsinSaleCountDO == null) {
                return false;
            }
            return predicateList.stream().anyMatch(predicate -> predicate.test(amazonAsinSaleCountDO));
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(waitOfflineListings)) {
            return;
        }
        List<AmazonOfflineConfigListingQueue> addQueueList = new ArrayList<>(waitOfflineListings.size());

        BiConsumer<EsAmazonProductListing, AmazonOfflineConfigListingQueue> executeFunc = (listing, queue) -> {
            queue.setStatus(OfflineQueueEnums.QueueStatusType.PENDING_OFFLINE.getCode());
            queue.setTaskType(OfflineConfigEnums.OfflineType.DESIGNATED_SKU.getCode());
            setPublishQueueFiled(queue, listing, sonAsinSaleCountMap, message);
            Map<String, DesignatedSkuRuleDO> ruleData = new HashMap<>();
            DesignatedSkuRuleDO listingRule = designatedSkuRule.matchListingRule(listing, sonAsinSaleCountMap.get(listing.getSonAsin()));
            ruleData.put("rule", designatedSkuRule);
            ruleData.put("listing", listingRule);
            queue.setRuleInfo(JSON.toJSONString(ruleData));
        };

        List<AmazonOfflineConfigListingQueue> queues = amazonOfflineConfigListingQueueService.addListingToOffLinkQueue(waitOfflineListings, configVO, executeFunc);
        addQueueList.addAll(queues);
        if (CollectionUtils.isNotEmpty(addQueueList)) {
            amazonOfflineConfigListingQueueService.saveBatch(addQueueList, 300);
        }
    }
} 