package com.estone.erp.publish.amazon.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class AmazonMustPublishNewProductExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AmazonMustPublishNewProductExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSpuIsNull() {
            addCriterion("spu is null");
            return (Criteria) this;
        }

        public Criteria andSpuIsNotNull() {
            addCriterion("spu is not null");
            return (Criteria) this;
        }

        public Criteria andSpuEqualTo(String value) {
            addCriterion("spu =", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuNotEqualTo(String value) {
            addCriterion("spu <>", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuGreaterThan(String value) {
            addCriterion("spu >", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuGreaterThanOrEqualTo(String value) {
            addCriterion("spu >=", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuLessThan(String value) {
            addCriterion("spu <", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuLessThanOrEqualTo(String value) {
            addCriterion("spu <=", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuLike(String value) {
            addCriterion("spu like", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuNotLike(String value) {
            addCriterion("spu not like", value, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuIn(List<String> values) {
            addCriterion("spu in", values, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuNotIn(List<String> values) {
            addCriterion("spu not in", values, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuBetween(String value1, String value2) {
            addCriterion("spu between", value1, value2, "spu");
            return (Criteria) this;
        }

        public Criteria andSpuNotBetween(String value1, String value2) {
            addCriterion("spu not between", value1, value2, "spu");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdIsNull() {
            addCriterion("supervisor_id is null");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdIsNotNull() {
            addCriterion("supervisor_id is not null");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdEqualTo(String value) {
            addCriterion("supervisor_id =", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdNotEqualTo(String value) {
            addCriterion("supervisor_id <>", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdGreaterThan(String value) {
            addCriterion("supervisor_id >", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdGreaterThanOrEqualTo(String value) {
            addCriterion("supervisor_id >=", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdLessThan(String value) {
            addCriterion("supervisor_id <", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdLessThanOrEqualTo(String value) {
            addCriterion("supervisor_id <=", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdLike(String value) {
            addCriterion("supervisor_id like", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdNotLike(String value) {
            addCriterion("supervisor_id not like", value, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdIn(List<String> values) {
            addCriterion("supervisor_id in", values, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdNotIn(List<String> values) {
            addCriterion("supervisor_id not in", values, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdBetween(String value1, String value2) {
            addCriterion("supervisor_id between", value1, value2, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSupervisorIdNotBetween(String value1, String value2) {
            addCriterion("supervisor_id not between", value1, value2, "supervisorId");
            return (Criteria) this;
        }

        public Criteria andSaleIdIsNull() {
            addCriterion("sale_id is null");
            return (Criteria) this;
        }

        public Criteria andSaleIdIsNotNull() {
            addCriterion("sale_id is not null");
            return (Criteria) this;
        }

        public Criteria andSaleIdEqualTo(String value) {
            addCriterion("sale_id =", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdNotEqualTo(String value) {
            addCriterion("sale_id <>", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdGreaterThan(String value) {
            addCriterion("sale_id >", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdGreaterThanOrEqualTo(String value) {
            addCriterion("sale_id >=", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdLessThan(String value) {
            addCriterion("sale_id <", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdLessThanOrEqualTo(String value) {
            addCriterion("sale_id <=", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdLike(String value) {
            addCriterion("sale_id like", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdNotLike(String value) {
            addCriterion("sale_id not like", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdIn(List<String> values) {
            addCriterion("sale_id in", values, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdNotIn(List<String> values) {
            addCriterion("sale_id not in", values, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdBetween(String value1, String value2) {
            addCriterion("sale_id between", value1, value2, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdNotBetween(String value1, String value2) {
            addCriterion("sale_id not between", value1, value2, "saleId");
            return (Criteria) this;
        }

        public Criteria andPublishedSiteIsNull() {
            addCriterion("published_site is null");
            return (Criteria) this;
        }

        public Criteria andPublishedSiteIsNotNull() {
            addCriterion("published_site is not null");
            return (Criteria) this;
        }

        public Criteria andPublishedSiteEqualTo(String value) {
            addCriterion("published_site =", value, "publishedSite");
            return (Criteria) this;
        }

        public Criteria andPublishedSiteNotEqualTo(String value) {
            addCriterion("published_site <>", value, "publishedSite");
            return (Criteria) this;
        }

        public Criteria andPublishedSiteGreaterThan(String value) {
            addCriterion("published_site >", value, "publishedSite");
            return (Criteria) this;
        }

        public Criteria andPublishedSiteGreaterThanOrEqualTo(String value) {
            addCriterion("published_site >=", value, "publishedSite");
            return (Criteria) this;
        }

        public Criteria andPublishedSiteLessThan(String value) {
            addCriterion("published_site <", value, "publishedSite");
            return (Criteria) this;
        }

        public Criteria andPublishedSiteLessThanOrEqualTo(String value) {
            addCriterion("published_site <=", value, "publishedSite");
            return (Criteria) this;
        }

        public Criteria andPublishedSiteLike(String value) {
            addCriterion("published_site like", value, "publishedSite");
            return (Criteria) this;
        }

        public Criteria andPublishedSiteNotLike(String value) {
            addCriterion("published_site not like", value, "publishedSite");
            return (Criteria) this;
        }

        public Criteria andPublishedSiteIn(List<String> values) {
            addCriterion("published_site in", values, "publishedSite");
            return (Criteria) this;
        }

        public Criteria andPublishedSiteNotIn(List<String> values) {
            addCriterion("published_site not in", values, "publishedSite");
            return (Criteria) this;
        }

        public Criteria andPublishedSiteBetween(String value1, String value2) {
            addCriterion("published_site between", value1, value2, "publishedSite");
            return (Criteria) this;
        }

        public Criteria andPublishedSiteNotBetween(String value1, String value2) {
            addCriterion("published_site not between", value1, value2, "publishedSite");
            return (Criteria) this;
        }

        public Criteria andIsBanIsNull() {
            addCriterion("is_ban is null");
            return (Criteria) this;
        }

        public Criteria andIsBanIsNotNull() {
            addCriterion("is_ban is not null");
            return (Criteria) this;
        }

        public Criteria andIsBanEqualTo(Boolean value) {
            addCriterion("is_ban =", value, "isBan");
            return (Criteria) this;
        }

        public Criteria andIsBanNotEqualTo(Boolean value) {
            addCriterion("is_ban <>", value, "isBan");
            return (Criteria) this;
        }

        public Criteria andIsBanGreaterThan(Boolean value) {
            addCriterion("is_ban >", value, "isBan");
            return (Criteria) this;
        }

        public Criteria andIsBanGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_ban >=", value, "isBan");
            return (Criteria) this;
        }

        public Criteria andIsBanLessThan(Boolean value) {
            addCriterion("is_ban <", value, "isBan");
            return (Criteria) this;
        }

        public Criteria andIsBanLessThanOrEqualTo(Boolean value) {
            addCriterion("is_ban <=", value, "isBan");
            return (Criteria) this;
        }

        public Criteria andIsBanIn(List<Boolean> values) {
            addCriterion("is_ban in", values, "isBan");
            return (Criteria) this;
        }

        public Criteria andIsBanNotIn(List<Boolean> values) {
            addCriterion("is_ban not in", values, "isBan");
            return (Criteria) this;
        }

        public Criteria andIsBanBetween(Boolean value1, Boolean value2) {
            addCriterion("is_ban between", value1, value2, "isBan");
            return (Criteria) this;
        }

        public Criteria andIsBanNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_ban not between", value1, value2, "isBan");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andSiteInfoIsNull() {
            addCriterion("site_info is null");
            return (Criteria) this;
        }

        public Criteria andSiteInfoIsNotNull() {
            addCriterion("site_info is not null");
            return (Criteria) this;
        }

        public Criteria andSiteInfoEqualTo(String value) {
            addCriterion("site_info =", value, "siteInfo");
            return (Criteria) this;
        }

        public Criteria andSiteInfoNotEqualTo(String value) {
            addCriterion("site_info <>", value, "siteInfo");
            return (Criteria) this;
        }

        public Criteria andSiteInfoGreaterThan(String value) {
            addCriterion("site_info >", value, "siteInfo");
            return (Criteria) this;
        }

        public Criteria andSiteInfoGreaterThanOrEqualTo(String value) {
            addCriterion("site_info >=", value, "siteInfo");
            return (Criteria) this;
        }

        public Criteria andSiteInfoLessThan(String value) {
            addCriterion("site_info <", value, "siteInfo");
            return (Criteria) this;
        }

        public Criteria andSiteInfoLessThanOrEqualTo(String value) {
            addCriterion("site_info <=", value, "siteInfo");
            return (Criteria) this;
        }

        public Criteria andSiteInfoLike(String value) {
            addCriterion("site_info like", value, "siteInfo");
            return (Criteria) this;
        }

        public Criteria andSiteInfoNotLike(String value) {
            addCriterion("site_info not like", value, "siteInfo");
            return (Criteria) this;
        }

        public Criteria andSiteInfoIn(List<String> values) {
            addCriterion("site_info in", values, "siteInfo");
            return (Criteria) this;
        }

        public Criteria andSiteInfoNotIn(List<String> values) {
            addCriterion("site_info not in", values, "siteInfo");
            return (Criteria) this;
        }

        public Criteria andSiteInfoBetween(String value1, String value2) {
            addCriterion("site_info between", value1, value2, "siteInfo");
            return (Criteria) this;
        }

        public Criteria andSiteInfoNotBetween(String value1, String value2) {
            addCriterion("site_info not between", value1, value2, "siteInfo");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeIsNull() {
            addCriterion("category_full_path_code is null");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeIsNotNull() {
            addCriterion("category_full_path_code is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeEqualTo(String value) {
            addCriterion("category_full_path_code =", value, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeNotEqualTo(String value) {
            addCriterion("category_full_path_code <>", value, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeGreaterThan(String value) {
            addCriterion("category_full_path_code >", value, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeGreaterThanOrEqualTo(String value) {
            addCriterion("category_full_path_code >=", value, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeLessThan(String value) {
            addCriterion("category_full_path_code <", value, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeLessThanOrEqualTo(String value) {
            addCriterion("category_full_path_code <=", value, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeLike(String value) {
            addCriterion("category_full_path_code like", value, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeNotLike(String value) {
            addCriterion("category_full_path_code not like", value, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeIn(List<String> values) {
            addCriterion("category_full_path_code in", values, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeNotIn(List<String> values) {
            addCriterion("category_full_path_code not in", values, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeBetween(String value1, String value2) {
            addCriterion("category_full_path_code between", value1, value2, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryFullPathCodeNotBetween(String value1, String value2) {
            addCriterion("category_full_path_code not between", value1, value2, "categoryFullPathCode");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNameIsNull() {
            addCriterion("category_path_name is null");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNameIsNotNull() {
            addCriterion("category_path_name is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNameEqualTo(String value) {
            addCriterion("category_path_name =", value, "categoryPathName");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNameNotEqualTo(String value) {
            addCriterion("category_path_name <>", value, "categoryPathName");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNameGreaterThan(String value) {
            addCriterion("category_path_name >", value, "categoryPathName");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNameGreaterThanOrEqualTo(String value) {
            addCriterion("category_path_name >=", value, "categoryPathName");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNameLessThan(String value) {
            addCriterion("category_path_name <", value, "categoryPathName");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNameLessThanOrEqualTo(String value) {
            addCriterion("category_path_name <=", value, "categoryPathName");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNameLike(String value) {
            addCriterion("category_path_name like", value, "categoryPathName");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNameNotLike(String value) {
            addCriterion("category_path_name not like", value, "categoryPathName");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNameIn(List<String> values) {
            addCriterion("category_path_name in", values, "categoryPathName");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNameNotIn(List<String> values) {
            addCriterion("category_path_name not in", values, "categoryPathName");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNameBetween(String value1, String value2) {
            addCriterion("category_path_name between", value1, value2, "categoryPathName");
            return (Criteria) this;
        }

        public Criteria andCategoryPathNameNotBetween(String value1, String value2) {
            addCriterion("category_path_name not between", value1, value2, "categoryPathName");
            return (Criteria) this;
        }

        public Criteria andProductTypeIsNull() {
            addCriterion("product_type is null");
            return (Criteria) this;
        }

        public Criteria andProductTypeIsNotNull() {
            addCriterion("product_type is not null");
            return (Criteria) this;
        }

        public Criteria andProductTypeEqualTo(Integer value) {
            addCriterion("product_type =", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotEqualTo(Integer value) {
            addCriterion("product_type <>", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThan(Integer value) {
            addCriterion("product_type >", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("product_type >=", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThan(Integer value) {
            addCriterion("product_type <", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanOrEqualTo(Integer value) {
            addCriterion("product_type <=", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeIn(List<Integer> values) {
            addCriterion("product_type in", values, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotIn(List<Integer> values) {
            addCriterion("product_type not in", values, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeBetween(Integer value1, Integer value2) {
            addCriterion("product_type between", value1, value2, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("product_type not between", value1, value2, "productType");
            return (Criteria) this;
        }

        public Criteria andUsPublishStatusIsNull() {
            addCriterion("us_publish_status is null");
            return (Criteria) this;
        }

        public Criteria andUsPublishStatusIsNotNull() {
            addCriterion("us_publish_status is not null");
            return (Criteria) this;
        }

        public Criteria andUsPublishStatusEqualTo(Integer value) {
            addCriterion("us_publish_status =", value, "usPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUsPublishStatusNotEqualTo(Integer value) {
            addCriterion("us_publish_status <>", value, "usPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUsPublishStatusGreaterThan(Integer value) {
            addCriterion("us_publish_status >", value, "usPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUsPublishStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("us_publish_status >=", value, "usPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUsPublishStatusLessThan(Integer value) {
            addCriterion("us_publish_status <", value, "usPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUsPublishStatusLessThanOrEqualTo(Integer value) {
            addCriterion("us_publish_status <=", value, "usPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUsPublishStatusIn(List<Integer> values) {
            addCriterion("us_publish_status in", values, "usPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUsPublishStatusNotIn(List<Integer> values) {
            addCriterion("us_publish_status not in", values, "usPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUsPublishStatusBetween(Integer value1, Integer value2) {
            addCriterion("us_publish_status between", value1, value2, "usPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUsPublishStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("us_publish_status not between", value1, value2, "usPublishStatus");
            return (Criteria) this;
        }

        public Criteria andDePublishStatusIsNull() {
            addCriterion("de_publish_status is null");
            return (Criteria) this;
        }

        public Criteria andDePublishStatusIsNotNull() {
            addCriterion("de_publish_status is not null");
            return (Criteria) this;
        }

        public Criteria andDePublishStatusEqualTo(Integer value) {
            addCriterion("de_publish_status =", value, "dePublishStatus");
            return (Criteria) this;
        }

        public Criteria andDePublishStatusNotEqualTo(Integer value) {
            addCriterion("de_publish_status <>", value, "dePublishStatus");
            return (Criteria) this;
        }

        public Criteria andDePublishStatusGreaterThan(Integer value) {
            addCriterion("de_publish_status >", value, "dePublishStatus");
            return (Criteria) this;
        }

        public Criteria andDePublishStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("de_publish_status >=", value, "dePublishStatus");
            return (Criteria) this;
        }

        public Criteria andDePublishStatusLessThan(Integer value) {
            addCriterion("de_publish_status <", value, "dePublishStatus");
            return (Criteria) this;
        }

        public Criteria andDePublishStatusLessThanOrEqualTo(Integer value) {
            addCriterion("de_publish_status <=", value, "dePublishStatus");
            return (Criteria) this;
        }

        public Criteria andDePublishStatusIn(List<Integer> values) {
            addCriterion("de_publish_status in", values, "dePublishStatus");
            return (Criteria) this;
        }

        public Criteria andDePublishStatusNotIn(List<Integer> values) {
            addCriterion("de_publish_status not in", values, "dePublishStatus");
            return (Criteria) this;
        }

        public Criteria andDePublishStatusBetween(Integer value1, Integer value2) {
            addCriterion("de_publish_status between", value1, value2, "dePublishStatus");
            return (Criteria) this;
        }

        public Criteria andDePublishStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("de_publish_status not between", value1, value2, "dePublishStatus");
            return (Criteria) this;
        }

        public Criteria andFrPublishStatusIsNull() {
            addCriterion("fr_publish_status is null");
            return (Criteria) this;
        }

        public Criteria andFrPublishStatusIsNotNull() {
            addCriterion("fr_publish_status is not null");
            return (Criteria) this;
        }

        public Criteria andFrPublishStatusEqualTo(Integer value) {
            addCriterion("fr_publish_status =", value, "frPublishStatus");
            return (Criteria) this;
        }

        public Criteria andFrPublishStatusNotEqualTo(Integer value) {
            addCriterion("fr_publish_status <>", value, "frPublishStatus");
            return (Criteria) this;
        }

        public Criteria andFrPublishStatusGreaterThan(Integer value) {
            addCriterion("fr_publish_status >", value, "frPublishStatus");
            return (Criteria) this;
        }

        public Criteria andFrPublishStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("fr_publish_status >=", value, "frPublishStatus");
            return (Criteria) this;
        }

        public Criteria andFrPublishStatusLessThan(Integer value) {
            addCriterion("fr_publish_status <", value, "frPublishStatus");
            return (Criteria) this;
        }

        public Criteria andFrPublishStatusLessThanOrEqualTo(Integer value) {
            addCriterion("fr_publish_status <=", value, "frPublishStatus");
            return (Criteria) this;
        }

        public Criteria andFrPublishStatusIn(List<Integer> values) {
            addCriterion("fr_publish_status in", values, "frPublishStatus");
            return (Criteria) this;
        }

        public Criteria andFrPublishStatusNotIn(List<Integer> values) {
            addCriterion("fr_publish_status not in", values, "frPublishStatus");
            return (Criteria) this;
        }

        public Criteria andFrPublishStatusBetween(Integer value1, Integer value2) {
            addCriterion("fr_publish_status between", value1, value2, "frPublishStatus");
            return (Criteria) this;
        }

        public Criteria andFrPublishStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("fr_publish_status not between", value1, value2, "frPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUkPublishStatusIsNull() {
            addCriterion("uk_publish_status is null");
            return (Criteria) this;
        }

        public Criteria andUkPublishStatusIsNotNull() {
            addCriterion("uk_publish_status is not null");
            return (Criteria) this;
        }

        public Criteria andUkPublishStatusEqualTo(Integer value) {
            addCriterion("uk_publish_status =", value, "ukPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUkPublishStatusNotEqualTo(Integer value) {
            addCriterion("uk_publish_status <>", value, "ukPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUkPublishStatusGreaterThan(Integer value) {
            addCriterion("uk_publish_status >", value, "ukPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUkPublishStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("uk_publish_status >=", value, "ukPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUkPublishStatusLessThan(Integer value) {
            addCriterion("uk_publish_status <", value, "ukPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUkPublishStatusLessThanOrEqualTo(Integer value) {
            addCriterion("uk_publish_status <=", value, "ukPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUkPublishStatusIn(List<Integer> values) {
            addCriterion("uk_publish_status in", values, "ukPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUkPublishStatusNotIn(List<Integer> values) {
            addCriterion("uk_publish_status not in", values, "ukPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUkPublishStatusBetween(Integer value1, Integer value2) {
            addCriterion("uk_publish_status between", value1, value2, "ukPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUkPublishStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("uk_publish_status not between", value1, value2, "ukPublishStatus");
            return (Criteria) this;
        }

        public Criteria andItPublishStatusIsNull() {
            addCriterion("it_publish_status is null");
            return (Criteria) this;
        }

        public Criteria andItPublishStatusIsNotNull() {
            addCriterion("it_publish_status is not null");
            return (Criteria) this;
        }

        public Criteria andItPublishStatusEqualTo(Integer value) {
            addCriterion("it_publish_status =", value, "itPublishStatus");
            return (Criteria) this;
        }

        public Criteria andItPublishStatusNotEqualTo(Integer value) {
            addCriterion("it_publish_status <>", value, "itPublishStatus");
            return (Criteria) this;
        }

        public Criteria andItPublishStatusGreaterThan(Integer value) {
            addCriterion("it_publish_status >", value, "itPublishStatus");
            return (Criteria) this;
        }

        public Criteria andItPublishStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("it_publish_status >=", value, "itPublishStatus");
            return (Criteria) this;
        }

        public Criteria andItPublishStatusLessThan(Integer value) {
            addCriterion("it_publish_status <", value, "itPublishStatus");
            return (Criteria) this;
        }

        public Criteria andItPublishStatusLessThanOrEqualTo(Integer value) {
            addCriterion("it_publish_status <=", value, "itPublishStatus");
            return (Criteria) this;
        }

        public Criteria andItPublishStatusIn(List<Integer> values) {
            addCriterion("it_publish_status in", values, "itPublishStatus");
            return (Criteria) this;
        }

        public Criteria andItPublishStatusNotIn(List<Integer> values) {
            addCriterion("it_publish_status not in", values, "itPublishStatus");
            return (Criteria) this;
        }

        public Criteria andItPublishStatusBetween(Integer value1, Integer value2) {
            addCriterion("it_publish_status between", value1, value2, "itPublishStatus");
            return (Criteria) this;
        }

        public Criteria andItPublishStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("it_publish_status not between", value1, value2, "itPublishStatus");
            return (Criteria) this;
        }

        public Criteria andEsPublishStatusIsNull() {
            addCriterion("es_publish_status is null");
            return (Criteria) this;
        }

        public Criteria andEsPublishStatusIsNotNull() {
            addCriterion("es_publish_status is not null");
            return (Criteria) this;
        }

        public Criteria andEsPublishStatusEqualTo(Integer value) {
            addCriterion("es_publish_status =", value, "esPublishStatus");
            return (Criteria) this;
        }

        public Criteria andEsPublishStatusNotEqualTo(Integer value) {
            addCriterion("es_publish_status <>", value, "esPublishStatus");
            return (Criteria) this;
        }

        public Criteria andEsPublishStatusGreaterThan(Integer value) {
            addCriterion("es_publish_status >", value, "esPublishStatus");
            return (Criteria) this;
        }

        public Criteria andEsPublishStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("es_publish_status >=", value, "esPublishStatus");
            return (Criteria) this;
        }

        public Criteria andEsPublishStatusLessThan(Integer value) {
            addCriterion("es_publish_status <", value, "esPublishStatus");
            return (Criteria) this;
        }

        public Criteria andEsPublishStatusLessThanOrEqualTo(Integer value) {
            addCriterion("es_publish_status <=", value, "esPublishStatus");
            return (Criteria) this;
        }

        public Criteria andEsPublishStatusIn(List<Integer> values) {
            addCriterion("es_publish_status in", values, "esPublishStatus");
            return (Criteria) this;
        }

        public Criteria andEsPublishStatusNotIn(List<Integer> values) {
            addCriterion("es_publish_status not in", values, "esPublishStatus");
            return (Criteria) this;
        }

        public Criteria andEsPublishStatusBetween(Integer value1, Integer value2) {
            addCriterion("es_publish_status between", value1, value2, "esPublishStatus");
            return (Criteria) this;
        }

        public Criteria andEsPublishStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("es_publish_status not between", value1, value2, "esPublishStatus");
            return (Criteria) this;
        }

        public Criteria andJpPublishStatusIsNull() {
            addCriterion("jp_publish_status is null");
            return (Criteria) this;
        }

        public Criteria andJpPublishStatusIsNotNull() {
            addCriterion("jp_publish_status is not null");
            return (Criteria) this;
        }

        public Criteria andJpPublishStatusEqualTo(Integer value) {
            addCriterion("jp_publish_status =", value, "jpPublishStatus");
            return (Criteria) this;
        }

        public Criteria andJpPublishStatusNotEqualTo(Integer value) {
            addCriterion("jp_publish_status <>", value, "jpPublishStatus");
            return (Criteria) this;
        }

        public Criteria andJpPublishStatusGreaterThan(Integer value) {
            addCriterion("jp_publish_status >", value, "jpPublishStatus");
            return (Criteria) this;
        }

        public Criteria andJpPublishStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("jp_publish_status >=", value, "jpPublishStatus");
            return (Criteria) this;
        }

        public Criteria andJpPublishStatusLessThan(Integer value) {
            addCriterion("jp_publish_status <", value, "jpPublishStatus");
            return (Criteria) this;
        }

        public Criteria andJpPublishStatusLessThanOrEqualTo(Integer value) {
            addCriterion("jp_publish_status <=", value, "jpPublishStatus");
            return (Criteria) this;
        }

        public Criteria andJpPublishStatusIn(List<Integer> values) {
            addCriterion("jp_publish_status in", values, "jpPublishStatus");
            return (Criteria) this;
        }

        public Criteria andJpPublishStatusNotIn(List<Integer> values) {
            addCriterion("jp_publish_status not in", values, "jpPublishStatus");
            return (Criteria) this;
        }

        public Criteria andJpPublishStatusBetween(Integer value1, Integer value2) {
            addCriterion("jp_publish_status between", value1, value2, "jpPublishStatus");
            return (Criteria) this;
        }

        public Criteria andJpPublishStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("jp_publish_status not between", value1, value2, "jpPublishStatus");
            return (Criteria) this;
        }

        public Criteria andCaPublishStatusIsNull() {
            addCriterion("ca_publish_status is null");
            return (Criteria) this;
        }

        public Criteria andCaPublishStatusIsNotNull() {
            addCriterion("ca_publish_status is not null");
            return (Criteria) this;
        }

        public Criteria andCaPublishStatusEqualTo(Integer value) {
            addCriterion("ca_publish_status =", value, "caPublishStatus");
            return (Criteria) this;
        }

        public Criteria andCaPublishStatusNotEqualTo(Integer value) {
            addCriterion("ca_publish_status <>", value, "caPublishStatus");
            return (Criteria) this;
        }

        public Criteria andCaPublishStatusGreaterThan(Integer value) {
            addCriterion("ca_publish_status >", value, "caPublishStatus");
            return (Criteria) this;
        }

        public Criteria andCaPublishStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("ca_publish_status >=", value, "caPublishStatus");
            return (Criteria) this;
        }

        public Criteria andCaPublishStatusLessThan(Integer value) {
            addCriterion("ca_publish_status <", value, "caPublishStatus");
            return (Criteria) this;
        }

        public Criteria andCaPublishStatusLessThanOrEqualTo(Integer value) {
            addCriterion("ca_publish_status <=", value, "caPublishStatus");
            return (Criteria) this;
        }

        public Criteria andCaPublishStatusIn(List<Integer> values) {
            addCriterion("ca_publish_status in", values, "caPublishStatus");
            return (Criteria) this;
        }

        public Criteria andCaPublishStatusNotIn(List<Integer> values) {
            addCriterion("ca_publish_status not in", values, "caPublishStatus");
            return (Criteria) this;
        }

        public Criteria andCaPublishStatusBetween(Integer value1, Integer value2) {
            addCriterion("ca_publish_status between", value1, value2, "caPublishStatus");
            return (Criteria) this;
        }

        public Criteria andCaPublishStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("ca_publish_status not between", value1, value2, "caPublishStatus");
            return (Criteria) this;
        }

        public Criteria andUsIsBandIsNull() {
            addCriterion("us_is_band is null");
            return (Criteria) this;
        }

        public Criteria andUsIsBandIsNotNull() {
            addCriterion("us_is_band is not null");
            return (Criteria) this;
        }

        public Criteria andUsIsBandEqualTo(Boolean value) {
            addCriterion("us_is_band =", value, "usIsBand");
            return (Criteria) this;
        }

        public Criteria andUsIsBandNotEqualTo(Boolean value) {
            addCriterion("us_is_band <>", value, "usIsBand");
            return (Criteria) this;
        }

        public Criteria andUsIsBandGreaterThan(Boolean value) {
            addCriterion("us_is_band >", value, "usIsBand");
            return (Criteria) this;
        }

        public Criteria andUsIsBandGreaterThanOrEqualTo(Boolean value) {
            addCriterion("us_is_band >=", value, "usIsBand");
            return (Criteria) this;
        }

        public Criteria andUsIsBandLessThan(Boolean value) {
            addCriterion("us_is_band <", value, "usIsBand");
            return (Criteria) this;
        }

        public Criteria andUsIsBandLessThanOrEqualTo(Boolean value) {
            addCriterion("us_is_band <=", value, "usIsBand");
            return (Criteria) this;
        }

        public Criteria andUsIsBandIn(List<Boolean> values) {
            addCriterion("us_is_band in", values, "usIsBand");
            return (Criteria) this;
        }

        public Criteria andUsIsBandNotIn(List<Boolean> values) {
            addCriterion("us_is_band not in", values, "usIsBand");
            return (Criteria) this;
        }

        public Criteria andUsIsBandBetween(Boolean value1, Boolean value2) {
            addCriterion("us_is_band between", value1, value2, "usIsBand");
            return (Criteria) this;
        }

        public Criteria andUsIsBandNotBetween(Boolean value1, Boolean value2) {
            addCriterion("us_is_band not between", value1, value2, "usIsBand");
            return (Criteria) this;
        }

        public Criteria andDeIsBandIsNull() {
            addCriterion("de_is_band is null");
            return (Criteria) this;
        }

        public Criteria andDeIsBandIsNotNull() {
            addCriterion("de_is_band is not null");
            return (Criteria) this;
        }

        public Criteria andDeIsBandEqualTo(Boolean value) {
            addCriterion("de_is_band =", value, "deIsBand");
            return (Criteria) this;
        }

        public Criteria andDeIsBandNotEqualTo(Boolean value) {
            addCriterion("de_is_band <>", value, "deIsBand");
            return (Criteria) this;
        }

        public Criteria andDeIsBandGreaterThan(Boolean value) {
            addCriterion("de_is_band >", value, "deIsBand");
            return (Criteria) this;
        }

        public Criteria andDeIsBandGreaterThanOrEqualTo(Boolean value) {
            addCriterion("de_is_band >=", value, "deIsBand");
            return (Criteria) this;
        }

        public Criteria andDeIsBandLessThan(Boolean value) {
            addCriterion("de_is_band <", value, "deIsBand");
            return (Criteria) this;
        }

        public Criteria andDeIsBandLessThanOrEqualTo(Boolean value) {
            addCriterion("de_is_band <=", value, "deIsBand");
            return (Criteria) this;
        }

        public Criteria andDeIsBandIn(List<Boolean> values) {
            addCriterion("de_is_band in", values, "deIsBand");
            return (Criteria) this;
        }

        public Criteria andDeIsBandNotIn(List<Boolean> values) {
            addCriterion("de_is_band not in", values, "deIsBand");
            return (Criteria) this;
        }

        public Criteria andDeIsBandBetween(Boolean value1, Boolean value2) {
            addCriterion("de_is_band between", value1, value2, "deIsBand");
            return (Criteria) this;
        }

        public Criteria andDeIsBandNotBetween(Boolean value1, Boolean value2) {
            addCriterion("de_is_band not between", value1, value2, "deIsBand");
            return (Criteria) this;
        }

        public Criteria andFrIsBandIsNull() {
            addCriterion("fr_is_band is null");
            return (Criteria) this;
        }

        public Criteria andFrIsBandIsNotNull() {
            addCriterion("fr_is_band is not null");
            return (Criteria) this;
        }

        public Criteria andFrIsBandEqualTo(Boolean value) {
            addCriterion("fr_is_band =", value, "frIsBand");
            return (Criteria) this;
        }

        public Criteria andFrIsBandNotEqualTo(Boolean value) {
            addCriterion("fr_is_band <>", value, "frIsBand");
            return (Criteria) this;
        }

        public Criteria andFrIsBandGreaterThan(Boolean value) {
            addCriterion("fr_is_band >", value, "frIsBand");
            return (Criteria) this;
        }

        public Criteria andFrIsBandGreaterThanOrEqualTo(Boolean value) {
            addCriterion("fr_is_band >=", value, "frIsBand");
            return (Criteria) this;
        }

        public Criteria andFrIsBandLessThan(Boolean value) {
            addCriterion("fr_is_band <", value, "frIsBand");
            return (Criteria) this;
        }

        public Criteria andFrIsBandLessThanOrEqualTo(Boolean value) {
            addCriterion("fr_is_band <=", value, "frIsBand");
            return (Criteria) this;
        }

        public Criteria andFrIsBandIn(List<Boolean> values) {
            addCriterion("fr_is_band in", values, "frIsBand");
            return (Criteria) this;
        }

        public Criteria andFrIsBandNotIn(List<Boolean> values) {
            addCriterion("fr_is_band not in", values, "frIsBand");
            return (Criteria) this;
        }

        public Criteria andFrIsBandBetween(Boolean value1, Boolean value2) {
            addCriterion("fr_is_band between", value1, value2, "frIsBand");
            return (Criteria) this;
        }

        public Criteria andFrIsBandNotBetween(Boolean value1, Boolean value2) {
            addCriterion("fr_is_band not between", value1, value2, "frIsBand");
            return (Criteria) this;
        }

        public Criteria andUkIsBandIsNull() {
            addCriterion("uk_is_band is null");
            return (Criteria) this;
        }

        public Criteria andUkIsBandIsNotNull() {
            addCriterion("uk_is_band is not null");
            return (Criteria) this;
        }

        public Criteria andUkIsBandEqualTo(Boolean value) {
            addCriterion("uk_is_band =", value, "ukIsBand");
            return (Criteria) this;
        }

        public Criteria andUkIsBandNotEqualTo(Boolean value) {
            addCriterion("uk_is_band <>", value, "ukIsBand");
            return (Criteria) this;
        }

        public Criteria andUkIsBandGreaterThan(Boolean value) {
            addCriterion("uk_is_band >", value, "ukIsBand");
            return (Criteria) this;
        }

        public Criteria andUkIsBandGreaterThanOrEqualTo(Boolean value) {
            addCriterion("uk_is_band >=", value, "ukIsBand");
            return (Criteria) this;
        }

        public Criteria andUkIsBandLessThan(Boolean value) {
            addCriterion("uk_is_band <", value, "ukIsBand");
            return (Criteria) this;
        }

        public Criteria andUkIsBandLessThanOrEqualTo(Boolean value) {
            addCriterion("uk_is_band <=", value, "ukIsBand");
            return (Criteria) this;
        }

        public Criteria andUkIsBandIn(List<Boolean> values) {
            addCriterion("uk_is_band in", values, "ukIsBand");
            return (Criteria) this;
        }

        public Criteria andUkIsBandNotIn(List<Boolean> values) {
            addCriterion("uk_is_band not in", values, "ukIsBand");
            return (Criteria) this;
        }

        public Criteria andUkIsBandBetween(Boolean value1, Boolean value2) {
            addCriterion("uk_is_band between", value1, value2, "ukIsBand");
            return (Criteria) this;
        }

        public Criteria andUkIsBandNotBetween(Boolean value1, Boolean value2) {
            addCriterion("uk_is_band not between", value1, value2, "ukIsBand");
            return (Criteria) this;
        }

        public Criteria andItIsBandIsNull() {
            addCriterion("it_is_band is null");
            return (Criteria) this;
        }

        public Criteria andItIsBandIsNotNull() {
            addCriterion("it_is_band is not null");
            return (Criteria) this;
        }

        public Criteria andItIsBandEqualTo(Boolean value) {
            addCriterion("it_is_band =", value, "itIsBand");
            return (Criteria) this;
        }

        public Criteria andItIsBandNotEqualTo(Boolean value) {
            addCriterion("it_is_band <>", value, "itIsBand");
            return (Criteria) this;
        }

        public Criteria andItIsBandGreaterThan(Boolean value) {
            addCriterion("it_is_band >", value, "itIsBand");
            return (Criteria) this;
        }

        public Criteria andItIsBandGreaterThanOrEqualTo(Boolean value) {
            addCriterion("it_is_band >=", value, "itIsBand");
            return (Criteria) this;
        }

        public Criteria andItIsBandLessThan(Boolean value) {
            addCriterion("it_is_band <", value, "itIsBand");
            return (Criteria) this;
        }

        public Criteria andItIsBandLessThanOrEqualTo(Boolean value) {
            addCriterion("it_is_band <=", value, "itIsBand");
            return (Criteria) this;
        }

        public Criteria andItIsBandIn(List<Boolean> values) {
            addCriterion("it_is_band in", values, "itIsBand");
            return (Criteria) this;
        }

        public Criteria andItIsBandNotIn(List<Boolean> values) {
            addCriterion("it_is_band not in", values, "itIsBand");
            return (Criteria) this;
        }

        public Criteria andItIsBandBetween(Boolean value1, Boolean value2) {
            addCriterion("it_is_band between", value1, value2, "itIsBand");
            return (Criteria) this;
        }

        public Criteria andItIsBandNotBetween(Boolean value1, Boolean value2) {
            addCriterion("it_is_band not between", value1, value2, "itIsBand");
            return (Criteria) this;
        }

        public Criteria andEsIsBandIsNull() {
            addCriterion("es_is_band is null");
            return (Criteria) this;
        }

        public Criteria andEsIsBandIsNotNull() {
            addCriterion("es_is_band is not null");
            return (Criteria) this;
        }

        public Criteria andEsIsBandEqualTo(Boolean value) {
            addCriterion("es_is_band =", value, "esIsBand");
            return (Criteria) this;
        }

        public Criteria andEsIsBandNotEqualTo(Boolean value) {
            addCriterion("es_is_band <>", value, "esIsBand");
            return (Criteria) this;
        }

        public Criteria andEsIsBandGreaterThan(Boolean value) {
            addCriterion("es_is_band >", value, "esIsBand");
            return (Criteria) this;
        }

        public Criteria andEsIsBandGreaterThanOrEqualTo(Boolean value) {
            addCriterion("es_is_band >=", value, "esIsBand");
            return (Criteria) this;
        }

        public Criteria andEsIsBandLessThan(Boolean value) {
            addCriterion("es_is_band <", value, "esIsBand");
            return (Criteria) this;
        }

        public Criteria andEsIsBandLessThanOrEqualTo(Boolean value) {
            addCriterion("es_is_band <=", value, "esIsBand");
            return (Criteria) this;
        }

        public Criteria andEsIsBandIn(List<Boolean> values) {
            addCriterion("es_is_band in", values, "esIsBand");
            return (Criteria) this;
        }

        public Criteria andEsIsBandNotIn(List<Boolean> values) {
            addCriterion("es_is_band not in", values, "esIsBand");
            return (Criteria) this;
        }

        public Criteria andEsIsBandBetween(Boolean value1, Boolean value2) {
            addCriterion("es_is_band between", value1, value2, "esIsBand");
            return (Criteria) this;
        }

        public Criteria andEsIsBandNotBetween(Boolean value1, Boolean value2) {
            addCriterion("es_is_band not between", value1, value2, "esIsBand");
            return (Criteria) this;
        }

        public Criteria andJpIsBandIsNull() {
            addCriterion("jp_is_band is null");
            return (Criteria) this;
        }

        public Criteria andJpIsBandIsNotNull() {
            addCriterion("jp_is_band is not null");
            return (Criteria) this;
        }

        public Criteria andJpIsBandEqualTo(Boolean value) {
            addCriterion("jp_is_band =", value, "jpIsBand");
            return (Criteria) this;
        }

        public Criteria andJpIsBandNotEqualTo(Boolean value) {
            addCriterion("jp_is_band <>", value, "jpIsBand");
            return (Criteria) this;
        }

        public Criteria andJpIsBandGreaterThan(Boolean value) {
            addCriterion("jp_is_band >", value, "jpIsBand");
            return (Criteria) this;
        }

        public Criteria andJpIsBandGreaterThanOrEqualTo(Boolean value) {
            addCriterion("jp_is_band >=", value, "jpIsBand");
            return (Criteria) this;
        }

        public Criteria andJpIsBandLessThan(Boolean value) {
            addCriterion("jp_is_band <", value, "jpIsBand");
            return (Criteria) this;
        }

        public Criteria andJpIsBandLessThanOrEqualTo(Boolean value) {
            addCriterion("jp_is_band <=", value, "jpIsBand");
            return (Criteria) this;
        }

        public Criteria andJpIsBandIn(List<Boolean> values) {
            addCriterion("jp_is_band in", values, "jpIsBand");
            return (Criteria) this;
        }

        public Criteria andJpIsBandNotIn(List<Boolean> values) {
            addCriterion("jp_is_band not in", values, "jpIsBand");
            return (Criteria) this;
        }

        public Criteria andJpIsBandBetween(Boolean value1, Boolean value2) {
            addCriterion("jp_is_band between", value1, value2, "jpIsBand");
            return (Criteria) this;
        }

        public Criteria andJpIsBandNotBetween(Boolean value1, Boolean value2) {
            addCriterion("jp_is_band not between", value1, value2, "jpIsBand");
            return (Criteria) this;
        }

        public Criteria andCaIsBandIsNull() {
            addCriterion("ca_is_band is null");
            return (Criteria) this;
        }

        public Criteria andCaIsBandIsNotNull() {
            addCriterion("ca_is_band is not null");
            return (Criteria) this;
        }

        public Criteria andCaIsBandEqualTo(Boolean value) {
            addCriterion("ca_is_band =", value, "caIsBand");
            return (Criteria) this;
        }

        public Criteria andCaIsBandNotEqualTo(Boolean value) {
            addCriterion("ca_is_band <>", value, "caIsBand");
            return (Criteria) this;
        }

        public Criteria andCaIsBandGreaterThan(Boolean value) {
            addCriterion("ca_is_band >", value, "caIsBand");
            return (Criteria) this;
        }

        public Criteria andCaIsBandGreaterThanOrEqualTo(Boolean value) {
            addCriterion("ca_is_band >=", value, "caIsBand");
            return (Criteria) this;
        }

        public Criteria andCaIsBandLessThan(Boolean value) {
            addCriterion("ca_is_band <", value, "caIsBand");
            return (Criteria) this;
        }

        public Criteria andCaIsBandLessThanOrEqualTo(Boolean value) {
            addCriterion("ca_is_band <=", value, "caIsBand");
            return (Criteria) this;
        }

        public Criteria andCaIsBandIn(List<Boolean> values) {
            addCriterion("ca_is_band in", values, "caIsBand");
            return (Criteria) this;
        }

        public Criteria andCaIsBandNotIn(List<Boolean> values) {
            addCriterion("ca_is_band not in", values, "caIsBand");
            return (Criteria) this;
        }

        public Criteria andCaIsBandBetween(Boolean value1, Boolean value2) {
            addCriterion("ca_is_band between", value1, value2, "caIsBand");
            return (Criteria) this;
        }

        public Criteria andCaIsBandNotBetween(Boolean value1, Boolean value2) {
            addCriterion("ca_is_band not between", value1, value2, "caIsBand");
            return (Criteria) this;
        }

        public Criteria andRemarksIsNull() {
            addCriterion("remarks is null");
            return (Criteria) this;
        }

        public Criteria andRemarksIsNotNull() {
            addCriterion("remarks is not null");
            return (Criteria) this;
        }

        public Criteria andRemarksEqualTo(String value) {
            addCriterion("remarks =", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotEqualTo(String value) {
            addCriterion("remarks <>", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksGreaterThan(String value) {
            addCriterion("remarks >", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksGreaterThanOrEqualTo(String value) {
            addCriterion("remarks >=", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLessThan(String value) {
            addCriterion("remarks <", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLessThanOrEqualTo(String value) {
            addCriterion("remarks <=", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksLike(String value) {
            addCriterion("remarks like", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotLike(String value) {
            addCriterion("remarks not like", value, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksIn(List<String> values) {
            addCriterion("remarks in", values, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotIn(List<String> values) {
            addCriterion("remarks not in", values, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksBetween(String value1, String value2) {
            addCriterion("remarks between", value1, value2, "remarks");
            return (Criteria) this;
        }

        public Criteria andRemarksNotBetween(String value1, String value2) {
            addCriterion("remarks not between", value1, value2, "remarks");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIsNull() {
            addCriterion("last_updated_by is null");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIsNotNull() {
            addCriterion("last_updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByEqualTo(String value) {
            addCriterion("last_updated_by =", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotEqualTo(String value) {
            addCriterion("last_updated_by <>", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByGreaterThan(String value) {
            addCriterion("last_updated_by >", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("last_updated_by >=", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLessThan(String value) {
            addCriterion("last_updated_by <", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("last_updated_by <=", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByLike(String value) {
            addCriterion("last_updated_by like", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotLike(String value) {
            addCriterion("last_updated_by not like", value, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByIn(List<String> values) {
            addCriterion("last_updated_by in", values, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotIn(List<String> values) {
            addCriterion("last_updated_by not in", values, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByBetween(String value1, String value2) {
            addCriterion("last_updated_by between", value1, value2, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andLastUpdatedByNotBetween(String value1, String value2) {
            addCriterion("last_updated_by not between", value1, value2, "lastUpdatedBy");
            return (Criteria) this;
        }

        public Criteria andSpuCreatedTimeIsNull() {
            addCriterion("spu_created_time is null");
            return (Criteria) this;
        }

        public Criteria andSpuCreatedTimeIsNotNull() {
            addCriterion("spu_created_time is not null");
            return (Criteria) this;
        }

        public Criteria andSpuCreatedTimeEqualTo(Timestamp value) {
            addCriterion("spu_created_time =", value, "spuCreatedTime");
            return (Criteria) this;
        }

        public Criteria andSpuCreatedTimeNotEqualTo(Timestamp value) {
            addCriterion("spu_created_time <>", value, "spuCreatedTime");
            return (Criteria) this;
        }

        public Criteria andSpuCreatedTimeGreaterThan(Timestamp value) {
            addCriterion("spu_created_time >", value, "spuCreatedTime");
            return (Criteria) this;
        }

        public Criteria andSpuCreatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("spu_created_time >=", value, "spuCreatedTime");
            return (Criteria) this;
        }

        public Criteria andSpuCreatedTimeLessThan(Timestamp value) {
            addCriterion("spu_created_time <", value, "spuCreatedTime");
            return (Criteria) this;
        }

        public Criteria andSpuCreatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("spu_created_time <=", value, "spuCreatedTime");
            return (Criteria) this;
        }

        public Criteria andSpuCreatedTimeIn(List<Timestamp> values) {
            addCriterion("spu_created_time in", values, "spuCreatedTime");
            return (Criteria) this;
        }

        public Criteria andSpuCreatedTimeNotIn(List<Timestamp> values) {
            addCriterion("spu_created_time not in", values, "spuCreatedTime");
            return (Criteria) this;
        }

        public Criteria andSpuCreatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("spu_created_time between", value1, value2, "spuCreatedTime");
            return (Criteria) this;
        }

        public Criteria andSpuCreatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("spu_created_time not between", value1, value2, "spuCreatedTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNull() {
            addCriterion("created_time is null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNotNull() {
            addCriterion("created_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeEqualTo(Timestamp value) {
            addCriterion("created_time =", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotEqualTo(Timestamp value) {
            addCriterion("created_time <>", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThan(Timestamp value) {
            addCriterion("created_time >", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("created_time >=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThan(Timestamp value) {
            addCriterion("created_time <", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("created_time <=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIn(List<Timestamp> values) {
            addCriterion("created_time in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotIn(List<Timestamp> values) {
            addCriterion("created_time not in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time not between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNull() {
            addCriterion("updated_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNotNull() {
            addCriterion("updated_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeEqualTo(Timestamp value) {
            addCriterion("updated_time =", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotEqualTo(Timestamp value) {
            addCriterion("updated_time <>", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThan(Timestamp value) {
            addCriterion("updated_time >", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time >=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThan(Timestamp value) {
            addCriterion("updated_time <", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time <=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIn(List<Timestamp> values) {
            addCriterion("updated_time in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotIn(List<Timestamp> values) {
            addCriterion("updated_time not in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time not between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIn(List<Integer> auditStatusList) {
            addCriterion("audit_status in", auditStatusList, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andTextAuditStatusIn(List<Integer> auditStatusList) {
            addCriterion("text_audit_status in", auditStatusList, "textAuditStatus");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}