package com.estone.erp.publish.amazon.componet.validation.executor;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.PublishCommonConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.componet.AmazonTemplateForbiddenSaleChannelHelper;
import com.estone.erp.publish.amazon.componet.AmazonTemplateRepeatPublishHelper;
import com.estone.erp.publish.amazon.componet.validation.AbstractAmazonValidationExecutor;
import com.estone.erp.publish.amazon.componet.validation.AmazonTemplateValidationContext;
import com.estone.erp.publish.amazon.componet.validation.model.BasicProductData;
import com.estone.erp.publish.amazon.enums.AmazonTemplateValidationEnum;
import com.estone.erp.publish.amazon.enums.InfringementEnums;
import com.estone.erp.publish.amazon.model.dto.AmazonTemplateBasisRequest;
import com.estone.erp.publish.amazon.model.dto.AmazonTemplateValidationDO;
import com.estone.erp.publish.amazon.model.dto.AmazonValidationData;
import com.estone.erp.publish.amazon.model.dto.CatOperationPublishConfigDO;
import com.estone.erp.publish.amazon.service.AmazonAccountPublishConfigService;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.CategoryOperationsTeamConfigService;
import com.estone.erp.publish.amazon.util.AmazonSpecialTagUtils;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.system.newUsermgt.model.SuperEmployeeInfo;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ForbiddenAndSpecical;
import com.estone.erp.publish.system.product.bean.forbidden.InfringementSaleProhibitionVO;
import com.estone.erp.publish.system.product.enums.SpecialTagEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-03-27 14:08
 */
@Slf4j
@Component
public class SingleProductValidationExecutor extends AbstractAmazonValidationExecutor {

    @Autowired
    private AmazonAccountRelationService amazonAccountRelationService;
    @Autowired
    private CategoryOperationsTeamConfigService operationsTeamConfigService;
    @Resource
    private AmazonTemplateRepeatPublishHelper amazonTemplateRepeatPublishHelper;
    @Resource
    private AmazonAccountPublishConfigService amazonAccountPublishConfigService;

    /**
     * 校验Asin 内容不完整下架
     *
     * @param context
     */
    @Override
    protected void validationAsinIntegrity(AmazonTemplateValidationContext context) {
        List<ProductInfo> productDataList = (List<ProductInfo>) context.getProductData();
        if (CollectionUtils.isEmpty(productDataList)) {
            return;
        }
        List<String> sonSku = productDataList.stream().map(ProductInfo::getSonSku).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sonSku)) {
            String spu = context.getSpu();
            sonSku.add(spu);
        }
        String site = context.getSite();
        String accountNumber = context.getAccountNumber();
        ApiResult<List<String>> apiResult = amazonTemplateRepeatPublishHelper.checkRepeatProductBadAsin(accountNumber, sonSku, site);
        if (!apiResult.isSuccess()) {
            List<String> badAsinArticles = apiResult.getResult();
            if (CollectionUtils.isNotEmpty(badAsinArticles)) {
                // asin 不完整
                context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.NOT_SALE_OR_BAD_ASIN, false, badAsinArticles));
            }
        }
    }

    /**
     * - 校验重复刊登
     *
     * @param context
     */
    @Override
    protected void validationRepeatedPublication(AmazonTemplateValidationContext context) {
        List<ProductInfo> productDataList = (List<ProductInfo>) context.getProductData();
        if (CollectionUtils.isEmpty(productDataList)) {
            context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.REPEAT_PUBLISH, true));
            return;
        }

        List<String> sonSku = productDataList.stream().map(ProductInfo::getSonSku).collect(Collectors.toList());
        // 重复刊登校验
        AmazonTemplateBasisRequest repeatPublishDO = new AmazonTemplateBasisRequest();
        repeatPublishDO.setAccountNumber(context.getAccountNumber());
        repeatPublishDO.setSpu(context.getSpu());
        repeatPublishDO.setSonSkus(sonSku);
        ApiResult<String> repeatProductSkuResult = amazonTemplateRepeatPublishHelper.checkRepeatProductSku(repeatPublishDO);
        if (repeatProductSkuResult.isSuccess()) {
            context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.REPEAT_PUBLISH, true));
            return;
        }
        String result = repeatProductSkuResult.getResult();
        if (!"true".equals(result)) {
            // 重复刊登
            context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.REPEAT_PUBLISH, false, repeatProductSkuResult.getResult()));
        }

    }

    /**
     * 校验产品属性
     * - sku插头规格
     *
     * @param context
     */
    @Override
    protected void validationProductProperties(AmazonTemplateValidationContext context) {
        List<ProductInfo> productDataList = (List<ProductInfo>) context.getProductData();
        if (CollectionUtils.isEmpty(productDataList)) {
            return;
        }

        List<String> sonSkus = context.getSonSkus();
        String site = context.getSite();

        List<AmazonTemplateValidationDO> plugSpecifications = new ArrayList<>();
        productDataList.forEach(productInfo -> {
            String plugSpecification = productInfo.getPlugSpecification();
            if (StringUtils.isBlank(plugSpecification)) {
                return;
            }
            // 插头规格是否适用
            List<String> countryOfPlugSpecification = ProductUtils.getCountryOfPlugSpecification(plugSpecification);
            if (CollectionUtils.isNotEmpty(countryOfPlugSpecification) && !countryOfPlugSpecification.contains(site)) {
                String errorMsg = productInfo.getSonSku() + ",插头规格为" + plugSpecification + ", 只允许刊登到" + StringUtils.join(countryOfPlugSpecification, ",");
                AmazonTemplateValidationDO amazonTemplateValidationDO = AmazonTemplateValidationDO.failOf(AmazonTemplateValidationEnum.NOT_APPLICABLE_PLUG_SPECIFICATION, errorMsg, null);
                if (CollectionUtils.isNotEmpty(sonSkus) && sonSkus.contains(productInfo.getSonSku())) {
                    context.addValidationData(amazonTemplateValidationDO);
                } else {
                    plugSpecifications.add(amazonTemplateValidationDO);
                }
            }
        });

        if (plugSpecifications.size() == productDataList.size()) {
            plugSpecifications.forEach(context::addValidationData);
        }
    }

    /**
     * 校验侵权禁售
     *
     * @param context
     */
    @Override
    protected void validationProhibited(AmazonTemplateValidationContext context) {
        //添加禁售平台 true：有禁售平台   false:没有禁售平台
        List<ProductInfo> productDataList = (List<ProductInfo>) context.getProductData();
        if (CollectionUtils.isEmpty(productDataList)) {
            return;
        }
        String site = context.getSite();
        String accountNumber = context.getAccountNumber();

        List<String> sonskuList = productDataList.stream().map(ProductInfo::getSonSku).collect(Collectors.toList());
        ApiResult<Map<String, Boolean>> isForbiddenSaleChannelResult = AmazonTemplateForbiddenSaleChannelHelper.checkArticleNumberIsForbiddenSaleChannel(sonskuList, site, accountNumber);
        if (!isForbiddenSaleChannelResult.isSuccess()) {
            throw new RuntimeException(isForbiddenSaleChannelResult.getErrorMsg());
        }
        Map<String, Boolean> mapForbiddenSale = isForbiddenSaleChannelResult.getResult();
        List<ProductInfo> prohibitionProducts = productDataList.stream().filter(productInfo -> {
            boolean match = mapForbiddenSale.get(productInfo.getSonSku());
            return !match;
        }).collect(Collectors.toList());
        // 非空代表校验通过
        context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.SITE_BAN, CollectionUtils.isNotEmpty(prohibitionProducts)));

    }

    /**
     * 特供产品只有特供店铺可以刊登
     *
     * @return
     */
    @Override
    public void validationCanPublishSpecialTagAccount(AmazonTemplateValidationContext context) {
        List<ProductInfo> productDataList = (List<ProductInfo>) context.getProductData();
        if (CollectionUtils.isEmpty(productDataList)) {
            context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.CAN_NOT_PUBLISH_SPECIAL_TAG_ACCOUNT, true));
            return;
        }

        String accountNumber = context.getAccountNumber();

        // 先判断店铺是否为特供店铺
        boolean isAmzSpecialGoodsAccountNumber = AmazonTemplateForbiddenSaleChannelHelper.checkIsAmzSpecialGoodsAccountNumber(accountNumber);

        if (isAmzSpecialGoodsAccountNumber) {
            // 特供店铺的刊登规则
            if (!filterSpecialSupplyStoreProducts(productDataList, context)) {
                return; // 过滤后无可刊登SKU，已添加验证数据
            }

        } else {
            // 非特供店铺的刊登规则
            if (!filterNonSpecialSupplyStoreProducts(productDataList, context)) {
                return; // 过滤后无可刊登SKU，已添加验证数据
            }
        }

        // 校验通过
        context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.CAN_NOT_PUBLISH_SPECIAL_TAG_ACCOUNT, true));
    }

    /**
     * 过滤特供店铺产品
     *
     * @param productDataList 产品数据列表
     * @param context 验证上下文
     * @return 过滤后是否还有可刊登SKU
     */
    private boolean filterSpecialSupplyStoreProducts(List<ProductInfo> productDataList, AmazonTemplateValidationContext context) {
        // 特供店铺的刊登规则 - 过滤不符合可刊登特供标签的SKU
        productDataList.removeIf(product -> {
            if (CollectionUtils.isEmpty(product.getSpecialTypeList())) {
                return false;
            }
            if (product.getSpecialTypeList().contains(SpecialTagEnum.s_2037.getCode())) {
                return true;
            }
            return false;

        });

        // 当productDataList为空时则拦截刊登
        if (CollectionUtils.isEmpty(productDataList)) {
            context.addValidationData(AmazonTemplateValidationDO.failOf(AmazonTemplateValidationEnum.CAN_NOT_PUBLISH_SPECIAL_TAG_ACCOUNT,
                    "特供店铺可刊登产品sku，过滤后无可刊登SKU", null));
            return false;
        }
        return true;
    }

    /**
     * 查找侵权-律所代理SKU
     *
     * @param values 禁售信息集合
     * @return 侵权-律所代理SKU列表
     */
    private List<String> findLawFirmAgentSkus(Collection<ForbiddenAndSpecical> values) {
        List<String> filteredSkus = new ArrayList<>();
        values.stream()
                .filter(forbidden -> StringUtils.isNotBlank(forbidden.getInfringementSaleProhibition()))
                .forEach(forbiddenAndSpecical -> {
                    String infringementSaleProhibition = forbiddenAndSpecical.getInfringementSaleProhibition();
                    if (StringUtils.isNotBlank(infringementSaleProhibition)) {
                        List<InfringementSaleProhibitionVO> infringementSaleProhibitionVOs = JSON.parseArray(infringementSaleProhibition, InfringementSaleProhibitionVO.class);
                        if (CollectionUtils.isNotEmpty(infringementSaleProhibitionVOs)) {
                            boolean isLawFirmsAgent = infringementSaleProhibitionVOs.stream().anyMatch(prohibitionVO -> 
                                    InfringementEnums.InfringementType.INFRINGEMENT.isTrue(prohibitionVO.getInfringementTypeName()) &&
                                    InfringementEnums.InfringementWord.LAW_FIRMS_AGENT.isTrue(prohibitionVO.getInfringementObj()));
                            if (isLawFirmsAgent) {
                                filteredSkus.add(forbiddenAndSpecical.getSonSku());
                            }
                        }
                    }
                });
        return filteredSkus;
    }

    /**
     * 过滤非特供店铺产品
     * 非特供店铺不能刊登包含特供标签的产品
     *
     * @param productDataList 产品数据列表
     * @param context 验证上下文
     * @return 过滤后是否还有可刊登SKU
     */
    private boolean filterNonSpecialSupplyStoreProducts(List<ProductInfo> productDataList, AmazonTemplateValidationContext context) {
        // 非特供店铺的刊登规则 - 过滤所有子SKU包含特供标签的产品
        productDataList.removeIf(product -> {
            List<Integer> specialTypeList = product.getSpecialTypeList();
            // 移除有特殊标签且包含任何Amazon特供标签的产品
            return CollectionUtils.isNotEmpty(specialTypeList) && 
                   AmazonSpecialTagUtils.containsAnyAmazonSpecialTag(specialTypeList);
        });

        // 当productDataList为空时则拦截刊登
        if (CollectionUtils.isEmpty(productDataList)) {
            context.addValidationData(AmazonTemplateValidationDO.failOf(AmazonTemplateValidationEnum.CAN_NOT_PUBLISH_SPECIAL_TAG_ACCOUNT, 
                    "非特供店铺不能刊登特供标签产品，过滤后无可刊登SKU", null));
            return false;
        }
        return true;
    }

    /**
     * EU-FBA店铺产品，只有EU-FBA店铺店铺可以刊登
     *
     * @return
     */
    @Override
    public void validationCanPublishEuFbaSpecialTagAccount(AmazonTemplateValidationContext context) {
        List<ProductInfo> productDataList = (List<ProductInfo>) context.getProductData();
        if (CollectionUtils.isEmpty(productDataList)) {
            context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.CAN_NOT_PUBLISH_EU_FBA_ACCOUNT, true));
            return;
        }

        String accountNumber = context.getAccountNumber();

        // 先判断店铺是否为EU-FBA店铺
        boolean isEuFbaAccountNumber = AmazonTemplateForbiddenSaleChannelHelper.checkIsEuFbaAccountNumber(accountNumber);

        if (!isEuFbaAccountNumber && !filterNonEuFbaSupplyStoreProducts(productDataList, context)) {
            // 非EU-FBA店铺的刊登规则
            return; // 过滤后无可刊登SKU，已添加验证数据
        }

        // 校验通过
        context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.CAN_NOT_PUBLISH_EU_FBA_ACCOUNT, true));
    }

    /**
     * 过滤非EUFBA店铺产品
     * 非EUFBA店铺不能刊登包含EUFBA标签的产品
     *
     * @param productDataList 产品数据列表
     * @param context 验证上下文
     * @return 过滤后是否还有可刊登SKU
     */
    private boolean filterNonEuFbaSupplyStoreProducts(List<ProductInfo> productDataList, AmazonTemplateValidationContext context) {
        // 非EU-FBA店铺的刊登规则 - 过滤所有子SKU包含EU-FBA标签的产品
        productDataList.removeIf(product -> {
            List<Integer> specialTypeList = product.getSpecialTypeList();
            // 移除有特殊标签且包含任何 EU_FBA 标签的产品
            return CollectionUtils.isNotEmpty(specialTypeList) &&
                    AmazonSpecialTagUtils.containsAnyAmazonSpecialTagEUFBA(specialTypeList);
        });

        // 当productDataList为空时则拦截刊登
        if (CollectionUtils.isEmpty(productDataList)) {
            context.addValidationData(AmazonTemplateValidationDO.failOf(AmazonTemplateValidationEnum.CAN_NOT_PUBLISH_EU_FBA_ACCOUNT,
                    "非EU-FBA店铺，不能刊登EU-FBA 特殊标签产品，过滤后无可刊登SKU", null));
            return false;
        }
        return true;
    }

    /**
     * 校验sku状态
     *
     * @param context
     */
    @Override
    protected void validationItemStatus(AmazonTemplateValidationContext context) {
        List<ProductInfo> productDataList = (List<ProductInfo>) context.getProductData();
        if (CollectionUtils.isEmpty(productDataList)) {
            return;
        }
        productDataList.removeIf(productInfo -> PublishCommonConstant.INTERCEPT_CODE_STATE_LIST.contains(SingleItemEnum.getCodeByEnName(productInfo.getItemStatus())));
        // 非空代表校验通过
        context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.CAN_NOT_SKU_STATUS, CollectionUtils.isNotEmpty(productDataList)));
    }

    /**
     * 校验刊登类目
     *
     * @param context
     */
    @Override
    public void validationConfigurationCategory(AmazonTemplateValidationContext context) {
        List<ProductInfo> productDataList = (List<ProductInfo>) context.getProductData();
        if (CollectionUtils.isEmpty(productDataList)) {
            return;
        }
        List<String> sonSkus = productDataList.stream().map(ProductInfo::getSonSku).collect(Collectors.toList());
        context.setSonSkus(sonSkus);

        Integer categoryId = context.getCategoryId();
        String fullPathCode = context.getFullPathCode();
        String accountNumber = context.getAccountNumber();
        List<String> publishSku = context.getPublishSku();
        if (CollectionUtils.isEmpty(publishSku)) {
            context.addValidationData(AmazonTemplateValidationDO.failOf(AmazonTemplateValidationEnum.INSUFFICIENT_PUBLISH_CATEGORY, "模板无可刊登子sku", null));
            return;
        }


        if (StringUtils.isBlank(fullPathCode)) {
            context.addValidationData(AmazonTemplateValidationDO.successOf(AmazonTemplateValidationEnum.ACCOUNT_CAN_NOT_PUBLISH_CAT));
            context.addValidationData(AmazonTemplateValidationDO.successOf(AmazonTemplateValidationEnum.INSUFFICIENT_PUBLISH_CATEGORY));
            return;
        }

        // SKU分类是否为店铺配置的可刊登分类
        ApiResult<String> apiResult = amazonAccountRelationService.checkCanPublishCategory(accountNumber, fullPathCode);
        if (!apiResult.isSuccess()) {
            context.addValidationData(AmazonTemplateValidationDO.failOf(AmazonTemplateValidationEnum.ACCOUNT_CAN_NOT_PUBLISH_CAT, apiResult.getErrorMsg(), null));
            return;
        }
        context.addValidationData(AmazonTemplateValidationDO.successOf(AmazonTemplateValidationEnum.ACCOUNT_CAN_NOT_PUBLISH_CAT));

        if (!context.getCheckCategoryPublishNumber()) {
            return;
        }

        // 获取店铺所属主管
        ApiResult<SuperEmployeeInfo> salesSubSectorLeader = EsAccountUtils.getAmazonSalesSubSectorLeader(accountNumber);
        if (!salesSubSectorLeader.isSuccess()) {
            throw new BusinessException(salesSubSectorLeader.getErrorMsg());
        }

        String saleId = salesSubSectorLeader.getResult().getEmployeeNo();
        ApiResult<CatOperationPublishConfigDO> operationsConfigResult = operationsTeamConfigService.getUserOperationsConfig(saleId, categoryId);
        if (!operationsConfigResult.isSuccess()) {
            context.addValidationData(AmazonTemplateValidationDO.failOf(AmazonTemplateValidationEnum.INSUFFICIENT_PUBLISH_CATEGORY, operationsConfigResult.getErrorMsg(), null));
            return;
        }

        CatOperationPublishConfigDO operationsConfig = operationsConfigResult.getResult();
        operationsConfig.setSaleName(salesSubSectorLeader.getResult().getName());
        context.setOperationsConfig(operationsConfig);

        String skuDayRangeVal = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "OPERATION_CATEGORY", "sku_day_range", 10);
        if (skuDayRangeVal == null) {
            log.error("主管可刊登分类-sku录入时间范围为空");
            context.addValidationData(AmazonTemplateValidationDO.failOf(AmazonTemplateValidationEnum.INSUFFICIENT_PUBLISH_CATEGORY, "主管可刊登分类-sku录入时间范围为空", null));
            return;
        }

        int publishNumberDateRange = Integer.parseInt(skuDayRangeVal);
        context.setPublishNumberDateRange(publishNumberDateRange);

        // 获取主管对应的店铺
        List<String> userManagedAccountNumbers = amazonAccountPublishConfigService.getUserManagedSiteAccountNumbersCache(saleId, context.getSite());
        if (CollectionUtils.isEmpty(userManagedAccountNumbers)) {
            log.error("店铺[{}],分类:[{}],spu:[{}],主管[{}],站点:{},管理的店铺数量:{}", accountNumber, categoryId, context.getSpu(), saleId, context.getSite(), userManagedAccountNumbers.size());
            context.addValidationData(AmazonTemplateValidationDO.failOf(AmazonTemplateValidationEnum.INSUFFICIENT_PUBLISH_CATEGORY, String.format("主管[%s],[%s]无关联店铺", saleId, context.getSite()), context.getSite()));
            return;
        }
        // 查询sku对应的刊登次数
        Map<String, Long> skuPublishNumberMap = amazonTemplateRepeatPublishHelper.getSkuAccountPublishNumber(context.getSpu(), publishSku, context.getSite(), userManagedAccountNumbers);
        Map<String, Long> skuPublishTotalNumberMap = amazonTemplateRepeatPublishHelper.getSkuAccountPublishNumber(context.getSpu(), publishSku, context.getSite(), null);
        context.setSkuPublishNumberMap(skuPublishNumberMap);
        context.setSkuPublishTotalNumberMap(skuPublishTotalNumberMap);

        // 校验刊登次数
        List<BasicProductData> basicProductData = BasicProductData.getBasicProductData(productDataList, publishSku);
        List<AmazonValidationData> validationDataList = checkValidationData(basicProductData, context);
        boolean allSuccess = validationDataList.stream().allMatch(data -> Boolean.TRUE.equals(data.getStatus()));
        if (allSuccess) {
            context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.INSUFFICIENT_PUBLISH_CATEGORY, true));
            return;
        }

        List<String> messages = validationDataList.stream()
                .filter(data -> Boolean.FALSE.equals(data.getStatus()))
                .map(AmazonValidationData::getMessages)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        context.addValidationData(AmazonTemplateValidationDO.failOf(AmazonTemplateValidationEnum.INSUFFICIENT_PUBLISH_CATEGORY, StringUtils.join(messages, "</br>"), validationDataList));
    }


    @Override
    public AmazonTemplateValidationContext init(String accountNumber, String spu, String site, Integer skuDataSource) {
        AmazonTemplateValidationContext context = new AmazonTemplateValidationContext(accountNumber, spu, site, skuDataSource);
        List<ProductInfo> productInfoList = ProductUtils.findProductInfos(List.of(spu));
        if (CollectionUtils.isEmpty(productInfoList)) {
            throw new BusinessException(spu + "未查询到产品信息");
        }
        ProductInfo productInfo = productInfoList.get(0);
        context.setCategoryId(productInfo.getCategoryId());
        context.setFullPathCode(productInfo.getFullpathcode());
        context.setProductData(productInfoList);
        context.setIsSuite(false);
        return context;
    }

    /**
     * 校验spu分类总刊登次数
     *
     * @param context
     */
    @Override
    public void validationListingPublishTotalNumber(AmazonTemplateValidationContext context) {
        List<ProductInfo> productDataList = (List<ProductInfo>) context.getProductData();
        if (CollectionUtils.isEmpty(productDataList)) {
            return;
        }
        List<String> sonSkus = productDataList.stream().map(ProductInfo::getSonSku).collect(Collectors.toList());
        context.setSonSkus(sonSkus);

        String fullPathCode = context.getFullPathCode();
        List<String> publishSku = context.getPublishSku();
        if (StringUtils.isBlank(fullPathCode)) {
            context.addValidationData(AmazonTemplateValidationDO.failOf(AmazonTemplateValidationEnum.ACCOUNT_CAN_NOT_PUBLISH_CAT, "spu分类为空", null));
            return;
        }

        // 获取类目刊登总次数
        ApiResult<CatOperationPublishConfigDO> operationsConfigResult = operationsTeamConfigService.getCategoryOperationsConfig(fullPathCode);
        if (!operationsConfigResult.isSuccess()) {
            context.addValidationData(AmazonTemplateValidationDO.failOf(AmazonTemplateValidationEnum.INSUFFICIENT_PUBLISH_CATEGORY, operationsConfigResult.getErrorMsg(), null));
            return;
        }

        CatOperationPublishConfigDO operationsConfig = operationsConfigResult.getResult();
        operationsConfig.setSaleName("");
        context.setOperationsConfig(operationsConfig);

        String skuDayRangeVal = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "OPERATION_CATEGORY", "sku_day_range", 10);
        if (skuDayRangeVal == null) {
            log.error("主管可刊登分类-sku录入时间范围为空");
            context.addValidationData(AmazonTemplateValidationDO.failOf(AmazonTemplateValidationEnum.INSUFFICIENT_PUBLISH_CATEGORY, "主管可刊登分类-sku录入时间范围为空", null));
            return;
        }

        int publishNumberDateRange = Integer.parseInt(skuDayRangeVal);
        context.setPublishNumberDateRange(publishNumberDateRange);

        // sku总刊登次数
        Map<String, Long> skuPublishTotalNumberMap = amazonTemplateRepeatPublishHelper.getSkuAccountPublishNumber(context.getSpu(), publishSku, context.getSite(), null);
        context.setSkuPublishTotalNumberMap(skuPublishTotalNumberMap);

        // 校验刊登次数
        List<BasicProductData> basicProductData = BasicProductData.getBasicProductData(productDataList, publishSku);
        List<AmazonValidationData> validationDataList = validationTotalPublishNumberData(basicProductData, context);
        boolean allSuccess = validationDataList.stream().allMatch(data -> Boolean.TRUE.equals(data.getStatus()));
        if (allSuccess) {
            context.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.INSUFFICIENT_PUBLISH_CATEGORY, true));
            return;
        }

        List<String> messages = validationDataList.stream()
                .filter(data -> Boolean.FALSE.equals(data.getStatus()))
                .map(AmazonValidationData::getMessages)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        context.addValidationData(AmazonTemplateValidationDO.failOf(AmazonTemplateValidationEnum.INSUFFICIENT_PUBLISH_CATEGORY, StringUtils.join(messages, "</br>"), validationDataList));
    }

    public List<AmazonValidationData> validationTotalPublishNumberData(List<BasicProductData> basicProductDataList, AmazonTemplateValidationContext context) {
        List<AmazonValidationData> validationDataList = new ArrayList<>();
        CatOperationPublishConfigDO operationsConfig = context.getOperationsConfig();
        Integer publishTotalNumber = operationsConfig.getPublishTotalNumber();
        String categoryName = operationsConfig.getCategoryName();

        Map<String, Long> skuPublishTotalNumberMap = context.getSkuPublishTotalNumberMap();
        basicProductDataList.forEach(productData -> {
            String sonSku = productData.getArticleNumber();
            Long publishNumber = skuPublishTotalNumberMap.get(sonSku);

            if (publishNumber == null || publishNumber == 0) {
                AmazonValidationData validationData = new AmazonValidationData();
                validationData.setStatus(true);
                validationData.setSku(sonSku);
                validationData.setPublishNumber(0L);
                validationDataList.add(validationData);
                return;
            }

            int comparePublishNumber = publishTotalNumber;
            if (Boolean.TRUE.equals(context.getPublishMode())) {
                // 如果是不是刊登中则次数+1
                publishNumber = publishNumber - 1;
            }
            if (publishNumber >= comparePublishNumber) {
                AmazonValidationData validationData = new AmazonValidationData();
                validationData.setStatus(false);
                validationData.setSku(sonSku);
                validationData.setPublishNumber(publishNumber);
                String msg = "%s,目前在分类配置[%s]下已超出刊登总数:%s,已刊登次数:%s，不允许再次刊登";
                validationData.setMessages(String.format(msg, sonSku, categoryName, comparePublishNumber, publishNumber));
                validationDataList.add(validationData);
                return;
            }
            AmazonValidationData validationData = new AmazonValidationData();
            validationData.setStatus(true);
            validationData.setSku(sonSku);
            validationData.setPublishNumber(publishNumber);
            validationDataList.add(validationData);
        });
        return validationDataList;
    }

    /**
     * 过滤模板中禁止刊登的单品状态sku
     *
     * @param context
     */
    @Override
    public void filterTemplateSkuStatus(AmazonTemplateValidationContext context) {
        AmazonTemplateBO template = context.getTemplate();
        List<ProductInfo> productInfoList = ProductUtils.findProductInfos(List.of(context.getSpu()));
        if (CollectionUtils.isEmpty(productInfoList)) {
            throw new BusinessException(context.getSpu() + "未查询到产品信息");
        }
        context.setProductData(productInfoList);
        validationItemStatus(context);
        validationProhibited(context);
        if (context.isAllSuccess()) {
            return;
        }
        List<AmazonSku> amazonSkus = template.getAmazonSkus();
        if (CollectionUtils.isEmpty(amazonSkus)) {
            return;
        }
        List<String> sonSkus = productInfoList.stream().map(ProductInfo::getSonSku).collect(Collectors.toList());
        amazonSkus.removeIf(amazonSku -> !sonSkus.contains(amazonSku.getSku()));
        if (CollectionUtils.isEmpty(amazonSkus)) {
            template.setVariations(null);
            return;
        }
        template.setVariations(JSON.toJSONString(amazonSkus));
    }
}
