package com.estone.erp.publish.amazon.componet.marketing.offline.service;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.alert.logger.PublishLogger;
import com.estone.erp.common.alert.logger.PublishLoggerFactory;
import com.estone.erp.common.alert.policy.DefaultAlertPolicy;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.componet.marketing.offline.AmazonOfflineConfigHandlerService;
import com.estone.erp.publish.amazon.mq.model.AmazonOfflineConfigMessage;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.system.product.ProductInfringementForbiddenSaleUtils;
import com.estone.erp.publish.system.product.bean.ForbiddenAndSpecical;
import com.estone.erp.publish.system.product.bean.forbidden.InfringementSaleProhibitionVO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.AmazonOfflineConfigVO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.enums.OfflineConfigEnums;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.enums.OfflineQueueEnums;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.rule.ProhibitedProductRuleDO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.rule.ProhibitedRuleConfigDO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonOfflineConfigListingQueue;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
public class ProhibitedProductOfflineService extends AbstractOfflineConfigService implements AmazonOfflineConfigHandlerService {
    private final PublishLogger logger = PublishLoggerFactory.getLogger(ProhibitedProductOfflineService.class);

    @Override
    public String getOfflineType() {
        return OfflineConfigEnums.OfflineType.PROHIBITED_PRODUCT.name();
    }

    @Override
    public ApiResult<String> executeOfflineConfig(AmazonOfflineConfigMessage message) {
        try {
            // 实现链接包含侵权词下架逻辑
            Integer configId = message.getConfigId();
            String accountNumber = message.getAccountNumber();
            ApiResult<AmazonOfflineConfigVO> configVOApiResult = amazonOfflineConfigService.editConfig(configId);
            if (!configVOApiResult.isSuccess()) {
                return ApiResult.newError("查询配置失败：" + configId);
            }
            AmazonOfflineConfigVO configVO = configVOApiResult.getResult();
            if (configVO == null) {
                return ApiResult.newError("配置不存在");
            }
            message.setConfirmedTime(configVO.getConfig().getConfirmedTime());
            executeProhibitedProductOffline(accountNumber, configVO, message);
        } catch (Exception e) {
            logger.errorForPolicy("店铺:{},执行下架配置【{}】异常,e:{}", getOfflineType(), new DefaultAlertPolicy(), message.getAccountNumber(), message.getConfigId(), e.getMessage(), e);
        }
        return ApiResult.newSuccess("执行禁售产品下架处理完成");
    }

    private void executeProhibitedProductOffline(String accountNumber, AmazonOfflineConfigVO configVO, AmazonOfflineConfigMessage message) {
        ProhibitedProductRuleDO prohibitedProductRule = configVO.getProhibitedProductRule();

        List<Predicate<AmazonAsinSaleCountDO>> predicateList = transferToSaleCountCompareRules(prohibitedProductRule.getSaleCount());

        List<ProhibitedRuleConfigDO> prohibitedRules = prohibitedProductRule.getProhibitedRules();
        List<String> infringementTypeNames = prohibitedRules.stream().map(ProhibitedRuleConfigDO::getInfringementTypeName).collect(Collectors.toList());
        List<String> infringementObjs = prohibitedRules.stream().map(ProhibitedRuleConfigDO::getInfringementObj).collect(Collectors.toList());
        List<String> platforms = prohibitedRules.stream().map(ProhibitedRuleConfigDO::getPlatformData).flatMap(List::stream).map(ProhibitedRuleConfigDO.PlatformData::getPlatform).collect(Collectors.toList());

        // 构建查询条件
        EsAmazonProductListingRequest request = buildEsAmazonProductListingRequest(accountNumber, prohibitedProductRule);
        request.setInfringementTypenames(infringementTypeNames);
        request.setInfringementObjs(infringementObjs);
        request.setForbidChannelList(platforms);
        esAmazonProductListingService.scrollQueryExecutorTask(request, listings -> {
            try {
                listingHandler(listings, prohibitedProductRule, predicateList, message, configVO);
            } catch (Exception e) {
                logger.error("店铺:{},执行下架配置【{}-{}】统计链接异常,e:{}", accountNumber, configVO.getConfig().getId(), configVO.getConfig().getRuleName(), e.getMessage(), e);
            }
        });

        // 查询店铺统计日期内待下架数量
        Integer waitOffLinkTotal = amazonOfflineConfigListingQueueService.getAccountOffCountByStatus(accountNumber, message.getConfirmedTime(), message.getScheduleTime(), OfflineQueueEnums.QueueStatusType.PENDING_OFFLINE.getCode(), OfflineConfigEnums.OfflineType.PROHIBITED_PRODUCT.getCode());

        // 获取店铺在售链接数据
        Long onlineListingNum = esAmazonProductListingService.getOnlineListingNum(List.of(accountNumber));
        Integer offlinePercentage = configVO.getConfig().getOfflinePercentage();
        double limitRatio = offlinePercentage / 100.0;
        // 下架链接总数量：在线 * limitRatio 向下取整
        double canOffLink = onlineListingNum * limitRatio;
        int needOfflineTotal = (int) Math.floor(canOffLink);

        // 记录店铺处理报告
        addAccountReport(message, offlinePercentage, Math.toIntExact(onlineListingNum), waitOffLinkTotal, needOfflineTotal);

        // 检查下架数据记录表状态流转，
        amazonOfflineConfigDataStatisticsService.updateExecutionStatus(message.getConfigId());

        if (needOfflineTotal <= 0) {
            return;
        }
        int taskTaskCode = OfflineConfigEnums.OfflineType.PROHIBITED_PRODUCT.getCode();
        // 按上架时间排序正序(旧->新),修改对应条数的待下架链接
        if (waitOffLinkTotal <= needOfflineTotal) {
            amazonOfflineConfigListingQueueService.updateOffLinkStatusOrderOpenTimeLimit(accountNumber, message.getConfirmedTime(), message.getScheduleTime(), taskTaskCode, OfflineQueueEnums.QueueStatusType.CAN_OFFLINE_STATUS.getCode(), waitOffLinkTotal);
        } else {
            amazonOfflineConfigListingQueueService.updateOffLinkStatusOrderOpenTimeLimit(accountNumber, message.getConfirmedTime(), message.getScheduleTime(), taskTaskCode, OfflineQueueEnums.QueueStatusType.CAN_OFFLINE_STATUS.getCode(), needOfflineTotal);
        }
    }

    private void listingHandler(List<EsAmazonProductListing> listings, ProhibitedProductRuleDO prohibitedProductRule, List<Predicate<AmazonAsinSaleCountDO>> predicateList, AmazonOfflineConfigMessage message, AmazonOfflineConfigVO configVO) {
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }

        // 禁售规则判断
        List<String> skuList = listings.stream().map(EsAmazonProductListing::getArticleNumber).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<List<String>> partition = Lists.partition(skuList, 100);
        Set<String> prohibitedSkus = partition.stream()
                .map(skus -> getSkuProhibitedInfo(skus, prohibitedProductRule))
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(prohibitedSkus)) {
            return;
        }

        List<EsAmazonProductListing> listingList = listings.stream()
                .filter(listing -> prohibitedSkus.contains(listing.getArticleNumber()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(listingList)) {
            return;
        }
        // 过滤下架定时公共规则
        List<EsAmazonProductListing> matchingList = filterPublishConfig(listingList);
        if (CollectionUtils.isEmpty(matchingList)) {
            return;
        }

        List<String> sonAsinList = matchingList.stream().map(EsAmazonProductListing::getSonAsin).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, AmazonAsinSaleCountDO> sonAsinSaleCountMap = esAmazonProductListingService.getSonAsinSaleCount(sonAsinList);
        List<EsAmazonProductListing> waitOfflineListings = matchingList.stream().filter(listing -> {
            String sonAsin = listing.getSonAsin();
            AmazonAsinSaleCountDO amazonAsinSaleCountDO = sonAsinSaleCountMap.get(sonAsin);
            if (amazonAsinSaleCountDO == null) {
                return false;
            }
            return predicateList.stream().anyMatch(predicate -> predicate.test(amazonAsinSaleCountDO));
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(waitOfflineListings)) {
            return;
        }
        List<AmazonOfflineConfigListingQueue> addQueueList = new ArrayList<>(waitOfflineListings.size());
        BiConsumer<EsAmazonProductListing, AmazonOfflineConfigListingQueue> executeFunc = (listing, queue) -> {
            queue.setTaskType(OfflineConfigEnums.OfflineType.PROHIBITED_PRODUCT.getCode());
            queue.setStatus(OfflineQueueEnums.QueueStatusType.PENDING_OFFLINE.getCode());
            setPublishQueueFiled(queue, listing, sonAsinSaleCountMap, message);
            Map<String, ProhibitedProductRuleDO> ruleData = new HashMap<>();
            ProhibitedProductRuleDO listingRule = prohibitedProductRule.matchListingRule(listing, sonAsinSaleCountMap.get(listing.getSonAsin()));
            ruleData.put("rule", prohibitedProductRule);
            ruleData.put("listing", listingRule);
            queue.setRuleInfo(JSON.toJSONString(ruleData));
        };

        List<AmazonOfflineConfigListingQueue> queues = amazonOfflineConfigListingQueueService.addListingToOffLinkQueue(waitOfflineListings, configVO, executeFunc);
        addQueueList.addAll(queues);
        if (CollectionUtils.isNotEmpty(addQueueList)) {
            amazonOfflineConfigListingQueueService.saveBatch(addQueueList, 300);
        }
    }

    private List<String> getSkuProhibitedInfo(List<String> skuList, ProhibitedProductRuleDO prohibitedProductRule) {
        try {
            Map<String, ForbiddenAndSpecical> sonskuForbiddenAndSpecicalMap = ProductInfringementForbiddenSaleUtils.getForbiddenAndSpecicalBySonSku(skuList);
            if (MapUtils.isEmpty(sonskuForbiddenAndSpecicalMap)) {
                return Lists.newArrayList();
            }
            Collection<ForbiddenAndSpecical> forbiddenList = sonskuForbiddenAndSpecicalMap.values();
            List<String> matchedSku = forbiddenList.stream().filter(forbiddenAndSpecical -> {
                String infringementSaleProhibition = forbiddenAndSpecical.getInfringementSaleProhibition();
                if (StringUtils.isBlank(infringementSaleProhibition)) {
                    return false;
                }
                List<InfringementSaleProhibitionVO> infringementSaleProhibitionVOs = JSON.parseArray(infringementSaleProhibition, InfringementSaleProhibitionVO.class);
                if (CollectionUtils.isEmpty(infringementSaleProhibitionVOs)) {
                    return false;
                }

                List<ProhibitedRuleConfigDO> rules = prohibitedProductRule.getProhibitedRules();
                return infringementSaleProhibitionVOs.stream().anyMatch(prohibitionInfo -> rules.stream().anyMatch(rule -> {
                    if (!rule.getInfringementObj().equalsIgnoreCase(prohibitionInfo.getInfringementObj())) {
                        return false;
                    }
                    if (!rule.getInfringementTypeName().equalsIgnoreCase(prohibitionInfo.getInfringementTypeName())) {
                        return false;
                    }
                    if (CollectionUtils.isEmpty(prohibitionInfo.getSalesProhibitionsVos())) {
                        return false;
                    }
                    List<ProhibitedRuleConfigDO.PlatformData> platformData = rule.getPlatformData();
                    boolean anyMatch = prohibitionInfo.getSalesProhibitionsVos().stream().anyMatch(platformInfo -> platformData.stream().anyMatch(rulePlatform -> {
                        if (!rulePlatform.getPlatform().equalsIgnoreCase(platformInfo.getPlat())) {
                            return false;
                        }
                        if (CollectionUtils.isEmpty(rulePlatform.getSites()) || CollectionUtils.isEmpty(platformInfo.getSites())) {
                            return true;
                        }

                        return rulePlatform.getSites().stream()
                                .anyMatch(ruleSite -> platformInfo.getSites().stream()
                                        .anyMatch(siteInfo -> siteInfo.getSite().equalsIgnoreCase(ruleSite)));
                    }));
                    if (anyMatch) {
                        logger.info("sku:{},满足规则:{},{}", forbiddenAndSpecical.getSonSku(), rule, forbiddenAndSpecical.getInfringementSaleProhibition());
                        return true;
                    }
                    return false;

                }));
            }).map(ForbiddenAndSpecical::getSonSku).collect(Collectors.toList());
            return matchedSku;
        } catch (Exception e) {
            logger.error("获取产品sku禁售信息异常,error:{}", e.getMessage(), e);
        }
        return Lists.newArrayList();
    }
} 