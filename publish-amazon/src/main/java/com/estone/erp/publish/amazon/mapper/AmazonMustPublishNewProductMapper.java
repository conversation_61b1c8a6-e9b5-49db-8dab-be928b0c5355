package com.estone.erp.publish.amazon.mapper;

import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProduct;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProductExample;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface AmazonMustPublishNewProductMapper {
    int countByExample(AmazonMustPublishNewProductExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    int insert(AmazonMustPublishNewProduct record);

    AmazonMustPublishNewProduct selectByPrimaryKey(Integer id);

    List<AmazonMustPublishNewProduct> selectByExample(AmazonMustPublishNewProductExample example);

    int updateByExampleSelective(@Param("record") AmazonMustPublishNewProduct record, @Param("example") AmazonMustPublishNewProductExample example);

    int updateByPrimaryKeySelective(AmazonMustPublishNewProduct record);

    int batchInsert(@Param("list") List<AmazonMustPublishNewProduct> list);

    List<String> listSpuBySpu(@Param("spu") List<String> spu);

    int batchUpdateSale(@Param("userName") String userName, @Param("saleId") String saleId, @Param("ids") List<Integer> ids);

    int batchUpdateRemarks(@Param("userName") String userName, @Param("remarks") String remarks, @Param("ids") List<Integer> ids);

    void batchUpdateByPrimaryKeySelective(@Param("records") List<AmazonMustPublishNewProduct> records);

    int batchAuditPass(@Param("userName") String userName, @Param("ids") List<Integer> ids, @Param("now") LocalDateTime now);

    int batchAuditImagePass(@Param("userName") String userName, @Param("ids") List<Integer> ids, @Param("now") LocalDateTime now);

    int batchAuditTextPass(@Param("userName") String userName, @Param("ids") List<Integer> ids, @Param("now") LocalDateTime now);
}