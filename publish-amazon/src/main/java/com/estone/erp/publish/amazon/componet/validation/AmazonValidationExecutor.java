package com.estone.erp.publish.amazon.componet.validation;

public interface AmazonValidationExecutor {

    default AmazonTemplateValidationContext init(String accountNumber, String spu, String site, Integer skuDataSource) {
        throw new UnsupportedOperationException("Not implemented");
    }

    /**
     * 校验模板信息 - 产品信息校验
     */
    default void validationProductData(AmazonTemplateValidationContext context) {
        throw new UnsupportedOperationException("Not implemented");
    }

    /**
     * 校验模板信息 - 模板数据校验
     */
    default void validationTemplateData(AmazonTemplateValidationContext context) {
        throw new UnsupportedOperationException("Not implemented");
    }

    /**
     * 自动刊登 店铺可刊登类目范围
     */
    default void validationAutoPublishCategory(AmazonTemplateValidationContext context) {
        throw new UnsupportedOperationException("Not implemented");
    }

    /**
     * 校验spu分类总刊登次数
     */
    default void validationListingPublishTotalNumber(AmazonTemplateValidationContext context) {
        throw new UnsupportedOperationException("Not implemented");
    }

    /**
     * 过滤模板中禁止刊登的单品状态sku
     */
    default void filterTemplateSkuStatus(AmazonTemplateValidationContext validationContext) {
        throw new UnsupportedOperationException("Not implemented");
    }

    default void validationCanPublishSpecialTagAccount(AmazonTemplateValidationContext context) {
        throw new UnsupportedOperationException("Not implemented");
    }

    default void validationCanPublishEuFbaSpecialTagAccount(AmazonTemplateValidationContext context) {
        throw new UnsupportedOperationException("Not implemented");
    }

}
