package com.estone.erp.publish.amazon.model;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class AmazonMustPublishNewProduct implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column amazon_must_publish_new_product.id
     */
    private Integer id;

    /**
     * spu database column amazon_must_publish_new_product.spu
     */
    private String spu;

    /**
     * 主管id database column amazon_must_publish_new_product.supervisor_id
     */
    private String supervisorId;

    /**
     * 销售id database column amazon_must_publish_new_product.sale_id
     */
    private String saleId;

    /**
     * 刊登成功的站点 database column amazon_must_publish_new_product.published_site
     */
    private String publishedSite;

    /**
     * 是否禁售 database column amazon_must_publish_new_product.is_ban
     */
    private Boolean isBan;

    /**
     * 状态 1 未刊登 2 部分刊登 3 全部刊登 99 禁用 database column amazon_must_publish_new_product.status
     */
    private Integer status;

    /**
     * 站点详情 database column amazon_must_publish_new_product.site_info
     */
    private String siteInfo;

    /**
     * 类目全路径code database column amazon_must_publish_new_product.category_full_path_code
     */
    private String categoryFullPathCode;

    /**
     * 类目路径名称 database column amazon_must_publish_new_product.category_path_name
     */
    private String categoryPathName;

    /**
     * 产品类型 0:单属性, 1:多属性 database column amazon_must_publish_new_product.product_type
     */
    private Integer productType;

    /**
     * 刊登状态 database column amazon_must_publish_new_product.us_publish_status
     */
    private Integer usPublishStatus;

    /**
     * 刊登状态 database column amazon_must_publish_new_product.de_publish_status
     */
    private Integer dePublishStatus;

    /**
     * 刊登状态 database column amazon_must_publish_new_product.fr_publish_status
     */
    private Integer frPublishStatus;

    /**
     * 刊登状态 database column amazon_must_publish_new_product.uk_publish_status
     */
    private Integer ukPublishStatus;

    /**
     * 刊登状态 database column amazon_must_publish_new_product.it_publish_status
     */
    private Integer itPublishStatus;

    /**
     * 刊登状态 database column amazon_must_publish_new_product.es_publish_status
     */
    private Integer esPublishStatus;

    /**
     * 刊登状态 database column amazon_must_publish_new_product.jp_publish_status
     */
    private Integer jpPublishStatus;

    /**
     * 刊登状态 database column amazon_must_publish_new_product.ca_publish_status
     */
    private Integer caPublishStatus;

    /**
     * 站点是否禁售 database column amazon_must_publish_new_product.us_is_band
     */
    private Boolean usIsBand;

    /**
     * 站点是否禁售 database column amazon_must_publish_new_product.de_is_band
     */
    private Boolean deIsBand;

    /**
     * 站点是否禁售 database column amazon_must_publish_new_product.fr_is_band
     */
    private Boolean frIsBand;

    /**
     * 站点是否禁售 database column amazon_must_publish_new_product.uk_is_band
     */
    private Boolean ukIsBand;

    /**
     * 站点是否禁售 database column amazon_must_publish_new_product.it_is_band
     */
    private Boolean itIsBand;

    /**
     * 站点是否禁售 database column amazon_must_publish_new_product.es_is_band
     */
    private Boolean esIsBand;

    /**
     * 站点是否禁售 database column amazon_must_publish_new_product.jp_is_band
     */
    private Boolean jpIsBand;

    /**
     * 站点是否禁售 database column amazon_must_publish_new_product.ca_is_band
     */
    private Boolean caIsBand;

    /**
     * 备注 database column amazon_must_publish_new_product.remarks
     */
    private String remarks;

    /**
     * 最后修改人 database column amazon_must_publish_new_product.last_updated_by
     */
    private String lastUpdatedBy;
    
    /**
     * 审核状态 0 待审核 1 优化中 2 已完成 database column amazon_must_publish_new_product.audit_status
     */
    private Integer auditStatus;


    /**
     * 审核状态 0 待审核 1 优化中 2 已完成 database column amazon_must_publish_new_product.text_audit_status
     */
    private Integer textAuditStatus;

    /**
     * 审核人 database column amazon_must_publish_new_product.audit_by
     */
    private String auditBy;

    /**
     * 审核时间 database column amazon_must_publish_new_product.audit_time
     */
    private Timestamp auditTime;

    /**
     * spu 进入管理单品时间 database column amazon_must_publish_new_product.spu_created_time
     */
    private Timestamp spuCreatedTime;

    /**
     * 创建时间 database column amazon_must_publish_new_product.created_time
     */
    private Timestamp createdTime;

    /**
     * 修改时间 database column amazon_must_publish_new_product.updated_time
     */
    private Timestamp updatedTime;
}