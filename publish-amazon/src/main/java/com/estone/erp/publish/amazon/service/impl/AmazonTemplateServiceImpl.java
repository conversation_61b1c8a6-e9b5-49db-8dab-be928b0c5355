package com.estone.erp.publish.amazon.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.alert.logger.PublishLogger;
import com.estone.erp.common.alert.logger.PublishLoggerFactory;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.*;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.bo.InfringementErrorMsgBo;
import com.estone.erp.publish.amazon.call.AmazonConstant;
import com.estone.erp.publish.amazon.call.model.AmazonMarketplace;
import com.estone.erp.publish.amazon.call.model.NameValue;
import com.estone.erp.publish.amazon.call.model.ProcessingReportStatusCode;
import com.estone.erp.publish.amazon.call.model.ProcessingReportTriggleType;
import com.estone.erp.publish.amazon.call.product.IdType;
import com.estone.erp.publish.amazon.call.sku.SellerSkuRuleUtils;
import com.estone.erp.publish.amazon.call.util.XsdUtils;
import com.estone.erp.publish.amazon.cardcode.CardCodeType;
import com.estone.erp.publish.amazon.cardcode.util.CardCodeUtils;
import com.estone.erp.publish.amazon.componet.*;
import com.estone.erp.publish.amazon.componet.validation.AmazonTemplateValidationContext;
import com.estone.erp.publish.amazon.componet.validation.AmazonValidationHelper;
import com.estone.erp.publish.amazon.componet.validation.executor.SingleProductValidationExecutor;
import com.estone.erp.publish.amazon.componet.validation.executor.TemplateDataValidationExecutor;
import com.estone.erp.publish.amazon.enums.*;
import com.estone.erp.publish.amazon.mapper.AmazonTemplateMapper;
import com.estone.erp.publish.amazon.model.*;
import com.estone.erp.publish.amazon.model.AmazonTemplateExample.Criteria;
import com.estone.erp.publish.amazon.model.dto.AmazonTemplateBasisRequest;
import com.estone.erp.publish.amazon.model.dto.AmazonTemplateCriteria;
import com.estone.erp.publish.amazon.model.dto.AmazonTemplateValidationDO;
import com.estone.erp.publish.amazon.service.*;
import com.estone.erp.publish.amazon.util.*;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.amazon.util.model.SalesTypeEnum;
import com.estone.erp.publish.amazon.util.model.SkuTheme;
import com.estone.erp.publish.base.pms.enums.PicturePlatEnum;
import com.estone.erp.publish.base.pms.enums.PictureTypeEnum;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.base.pms.model.StockKeepingUnitWithBLOBs;
import com.estone.erp.publish.base.pms.model.dto.SkuWeightAndPriceMsg;
import com.estone.erp.publish.base.pms.service.AmazonAccountService;
import com.estone.erp.publish.base.pms.service.StockKeepingUnitService;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.PublishRoleEnum;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.common.util.RoleUtils;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.platform.enums.BulletPointFilterEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.enums.TemplateQueueTypeEnum;
import com.estone.erp.publish.platform.mapper.TemplateQueueMapper;
import com.estone.erp.publish.platform.model.TemplateQueue;
import com.estone.erp.publish.platform.model.TemplateRecord;
import com.estone.erp.publish.platform.service.TemplateRecordService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListingExample;
import com.estone.erp.publish.publishAmazon.service.AmazonProductListingService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.estone.erp.publish.system.erpDas.esModel.EsAmazonAsinIncrementInfo;
import com.estone.erp.publish.system.erpDas.esModel.EsAmazonBaseListing;
import com.estone.erp.publish.system.erpDas.esModel.EsFeature;
import com.estone.erp.publish.system.erpDas.esModel.EsSelectedVariations;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.estone.erp.publish.system.pmssalePublicData.model.PublishSpuModelRequest;
import com.estone.erp.publish.system.pmssalePublicData.model.SpTemplateDataDO;
import com.estone.erp.publish.system.pmssalePublicData.model.SpTemplateDataRequest;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SkuListAndCode;
import com.estone.erp.publish.system.product.bean.SonSkuFewInfo;
import com.estone.erp.publish.system.product.bean.gt.GtProductDetail;
import com.estone.erp.publish.system.product.bean.gt.GtTortDetail;
import com.estone.erp.publish.system.product.enums.SingleSourceEnum;
import com.estone.erp.publish.system.product.enums.SpecialTagEnum;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemOfficial;
import com.estone.erp.publish.system.product.esProduct.bean.SpecialGoods;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.system.scheduler.util.QueueStatus;
import com.estone.erp.publish.system.scheduler.util.RecordStatus;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.client.enums.SpFeedType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Time;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("amazonTemplateService")
public class AmazonTemplateServiceImpl implements AmazonTemplateService {
    private final PublishLogger log = PublishLoggerFactory.getLogger(AmazonTemplateServiceImpl.class);

    @Resource
    private AmazonTemplateMapper amazonTemplateMapper;

    @Resource
    private AmazonAccountService amazonAccountService;

    @Resource
    private AmazonCategoryProductXsdService amazonCategoryProductXsdService;

    @Resource
    private AmazonProcessReportService amazonProcessReportService;

    @Resource
    private StockKeepingUnitService stockKeepingUnitService;

    private AmazonCallService amazonCallService = SpringUtils.getBean(AmazonCallService.class);

    @Resource
    private AmazonAccountRelationService amazonAccountRelationService;

    @Resource
    private AmazonConstantMarketHelper amazonConstantMarketHelper;

    @Resource
    private TemplateQueueMapper templateQueueMapper;

    @Resource
    private TemplateRecordService templateRecordService;

    @Resource
    private AmazonTemplateAutoService amazonTemplateAutoService;

    @Resource
    private AmazonAutoPublishHelper amazonAutoPublishHelper;
    @Resource
    private AmazonTemplateForbiddenSaleChannelHelper amazonTemplateForbiddenSaleChannelHelper;
    @Resource
    private AmazonTemplateRepeatPublishHelper amazonTemplateRepeatPublishHelper;
    @Resource
    private AmazonInfringementWordHelper amazonInfringementWordHelper;
    @Resource
    private TemplateDataValidationExecutor templateDataValidationExecutor;
    @Autowired
    private AmazonTemplateBuilderHelper amazonTemplateBuilderHelper;

    @Autowired
    private SingleItemEsService singleItemEsService;
    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;
    @Resource
    private AmazonValidationHelper amazonValidationHelper;
    @Autowired
    private AmazonProductListingService amazonProductListingService;
    @Autowired
    private SingleProductValidationExecutor singleProductValidationExecutor;

    @Override
    public void insert(AmazonTemplateWithBLOBs amazonTemplate) {
        if (!amazonTemplate.getSaleVariant()) {
            amazonTemplate.setVariations(null);
        }
        amazonTemplate.initTable();
        Assert.notNull(amazonTemplate.getTable(), "未设置具体table!");
        // 刊登角色
        if (null == amazonTemplate.getPublishRole() && StringUtils.isNotBlank(amazonTemplate.getCreatedBy())) {
            amazonTemplate.setPublishRole(RoleUtils.getPublishRole(amazonTemplate.getCreatedBy()));
        }
        if (null == amazonTemplate.getCreationDate()) {
            amazonTemplate.setCreationDate(new Timestamp(System.currentTimeMillis()));
        }
        // 如果修改时间为空默认取创建时间
        if (null == amazonTemplate.getLastUpdateDate()) {
            amazonTemplate.setLastUpdateDate(amazonTemplate.getCreationDate());
        }
        //amazonTemplate.setNoInfringementFilter(false);
        amazonTemplate.setListingRelationTimes(0);
        Integer code = Optional.ofNullable(amazonTemplate.getInterfaceType()).orElse(TemplateInterfaceTypeEnums.JSON.getCode());
        amazonTemplate.setInterfaceType(code);
        amazonTemplateMapper.insert(amazonTemplate);
    }

    @Override
    public void update(AmazonTemplateWithBLOBs amazonTemplate) {
        amazonTemplate.initTable();
        Assert.notNull(amazonTemplate.getTable(), "未设置具体table!");
        Assert.notNull(amazonTemplate.getId(), "主键为空!");
        amazonTemplateMapper.updateByPrimaryKeySelective(amazonTemplate);
    }

    @Override
    public ApiResult<?> copyAmazonTemplate(List<Integer> ids, Integer num) {
        List<Integer> idList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            AmazonTemplateExample example = new AmazonTemplateExample(AmazonTemplateTableEnum.AMAZON_TEMPLATE_MODEL.getCode());
            Criteria criteria = example.createCriteria();
            criteria.andIdIn(ids);
            criteria.andIsLockEqualTo(true);
            List<AmazonTemplateWithBLOBs> templates = amazonTemplateMapper.selectByExampleWithBLOBs(example);
            if (CollectionUtils.isNotEmpty(templates)) {
                List<AmazonTemplateWithBLOBs> copyTemplates = new ArrayList<AmazonTemplateWithBLOBs>(
                        templates.size() * num);
                List<Integer> categoryIdNulls = templates.stream().filter(o -> o.getCategoryId() == null).map(o -> o.getId()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(categoryIdNulls)) {
                    return ApiResult.newError(StringUtils.join(categoryIdNulls, ",") + "\n这些模板编号的产品分类分类为空，请重新选择分类！");
                }
                for (AmazonTemplateWithBLOBs template : templates) {
                    Integer id = template.getId();
                    //过滤状态
                    template = this.handleSkulifecyclephase(template);
                    if (null == template) {
                        idList.add(id);
                        continue;
                    }
                    String prefixStr = null;
                    AmazonAccount amazonAccount = amazonAccountService
                            .queryAmazonAccountByAccountNumber(template.getSellerId());
                    if (null != amazonAccount && org.apache.commons.lang.StringUtils.isNotEmpty(amazonAccount.getEanPrefix())) {
                        prefixStr = amazonAccount.getEanPrefix();
                    }
                    Map<String, Integer> publishRoleMap = new HashMap<>();
                    for (int i = 0; i < num; i++) {
                        AmazonTemplateWithBLOBs amazonTemplate = handleTemplate(template);
                        if (null == amazonTemplate.getCategoryId()) {
                            return ApiResult.newError("产品分类为空，请重新选择分类");
                        }
                        if (null == amazonTemplate.getCreationDate()) {
                            amazonTemplate.setCreationDate(new Timestamp(System.currentTimeMillis()));
                        }
                        // 如果修改时间为空默认取创建时间
                        if (null == amazonTemplate.getLastUpdateDate()) {
                            amazonTemplate.setLastUpdateDate(amazonTemplate.getCreationDate());
                        }
                        amazonTemplate.setStandardProdcutIdType(CardCodeType.EAN.name());
                        amazonTemplate.setStandardProdcutIdValue(null);
                        amazonTemplate.setIsLock(false);
                        amazonTemplate.setCreatedBy(WebUtils.getUserName());
                        // 刊登角色
                        if (StringUtils.isNotBlank(amazonTemplate.getCreatedBy())) {
                            Integer publishRole = publishRoleMap.get(amazonTemplate.getCreatedBy());
                            if (null == publishRole) {
                                publishRole = RoleUtils.getPublishRole(amazonTemplate.getCreatedBy());
                                publishRoleMap.put(amazonTemplate.getCreatedBy(), publishRole);
                            }
                            amazonTemplate.setPublishRole(publishRole);
                        }
                        amazonTemplate.setListingRelationTimes(0);
                        amazonTemplate.setIsSitePublish(null);
                        //amazonTemplate.setNoInfringementFilter(false);
                        amazonTemplate.setPublishType(PublishTypeEnum.COMMON_PUBLISH.getCode());
                        copyTemplates.add(amazonTemplate);
                    }
                }
                if (CollectionUtils.isNotEmpty(copyTemplates)) {
                    amazonTemplateMapper.batchCreateAmazonTemplate(copyTemplates, AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
                }
            }
        }
        return ApiResult.newSuccess(idList);
    }

    public AmazonTemplateWithBLOBs handleTemplate(AmazonTemplateWithBLOBs amazonTemplate) {
        AmazonTemplateWithBLOBs template = new AmazonTemplateWithBLOBs();
        BeanUtils.copyProperties(amazonTemplate, template);
        template.setId(null);
        template.setIsLock(false);
        template.setStandardProdcutIdValue(null);
        template.setStandardProdcutIdType(null);
        template.setStepTemplateStatus(null);
        template.setPublishStatus(AmaoznPublishStatusEnum.WAIT_PUBLISH.getStatusCode());
        template.setListingRelationTimes(0);
//        template.setCreatedBy(null);
        template.setCreationDate(new Date());
        template.setLastUpdateDate(new Date());
        template.setTitleRule(null);
//        template.setLastUpdatedBy(null);
        if (template.getSearchData() != null) {
            try {
                Object keyWord = KeyWordUtils.packagingKeyWord(template);
                template.setSearchTerms(JSON.toJSONString(keyWord));
            } catch (Exception e) {
                log.error("处理关键词出错：", e);
            }


//            JSONObject json = new JSONObject();
//            List<String> sale = new ArrayList<>();
//            List<String> skus = new ArrayList<>();
//            if (template.getSaleVariant()) {
//                String variations = template.getVariations();
//                JSONArray jsonArray = JSON.parseArray(variations);
//                for (int j = 0; j < jsonArray.size(); j++) {
//                    String sku = jsonArray.getJSONObject(j).getString("sku");
//                    skus.add(sku);
//                }
//            }
//            // 法、德、意、西四个国家最大字符数量修改为230
//            int maxCount = 0;
//            if (template.getCountry().equals("JP")) {
//                maxCount = 500;
//            }
//            else if (template.getCountry().equals("IN")) {
//                maxCount = 200;
//            }
//            else if ("FR,DE,IT,ES,UK".contains(template.getCountry())) {
//                maxCount = 230;
//            }
//            else if(template.getCountry().equals("US")){
//                maxCount = 240;
//            }
//            else {
//                maxCount = 250;
//            }
//            String[] splits = template.getSearchData().split(",");
//            List<String> searchDatas = new ArrayList<>();
//            for (String split : splits) {
//                if (!split.contains(":")) {
//                    searchDatas.add(split);
//                }
//            }
//
//            if (template.getSaleVariant() && CollectionUtils.isNotEmpty(skus)) {
//                for (String sku : skus) {
//                    Collections.shuffle(searchDatas);
//                    int count = 0;
//                    String result = "";
//                    for (String sd : searchDatas) {
//                        count = result.trim().length() + sd.trim().length();
//                        if (count < maxCount) {
//                            result = result.trim() + " " + sd.trim();
//                        }else if (searchDatas.size() == 1) {
//                            result = this.handleStringSpaceSplit(sd, maxCount);
//                        }
//                    }
//                    json.put(sku, Arrays.asList(result));
//                }
//            }
//            else {
//                int count = 0;
//                String result = "";
//                Collections.shuffle(searchDatas);
//                for (String sd : searchDatas) {
//                    count = result.trim().length() + sd.trim().length();
//                    if (count < maxCount) {
//                        result = result.trim() + " " + sd.trim();
//                    }else if (searchDatas.size() == 1) {
//                        result = this.handleStringSpaceSplit(sd, maxCount);
//                    }
//                }
//                sale.add(JSONObject.toJSONString(result));
//            }
//            if (template.getSaleVariant()) {
//                template.setSearchTerms(json.toString());
//            }
//            else {
//                template.setSearchTerms(sale.toString());
//            }
        }
        return template;
    }

    @Override
    public AmazonTemplateWithBLOBs findById(Integer id, String table) {
        return amazonTemplateMapper.selectByPrimaryKey(id, table);
    }

    @Override
    public void batchDelete(List<Integer> ids, String table) {
        if (CollectionUtils.isNotEmpty(ids)) {
            Assert.notNull(table, "未设置具体table!");
            //amazonTemplateMapper.batchDelete(ids);
            //物理删除过滤刊登中，刊登成功状态
            amazonTemplateMapper.batchDeleteAmazontemplate(ids, table);
        }
    }

    @Override
    public void batchDeleteTable(List<Integer> ids, String table) {
        if (CollectionUtils.isNotEmpty(ids)) {
            Assert.notNull(table, "未设置具体table!");

            amazonTemplateMapper.batchDelete(ids, table);
        }
    }

    @Override
    public CQueryResult<AmazonTemplateBO> search(CQuery<AmazonTemplateCriteria> cquery) {
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_AMAZON);
        if (!superAdminOrEquivalent.isSuccess()) {
            CQueryResult<AmazonTemplateBO> amazonDataCollectCQueryResult = new CQueryResult<>();
            amazonDataCollectCQueryResult.setErrorMsg(superAdminOrEquivalent.getErrorMsg());
            return amazonDataCollectCQueryResult;
        }
        boolean isAdmin = superAdminOrEquivalent.getResult();
        ApiResult<NewUser> userRes = NewUsermgtUtils.getUserByNo(WebUtils.getUserName());
        if (!userRes.isSuccess()) {
            return CQueryResult.failResult(userRes.getErrorMsg());
        }
        NewUser currentUser = userRes.getResult();

        AmazonTemplateCriteria query = cquery.getSearch();
        Assert.notNull(query, "search is null!");
        String table = AmazonTemplateUtils.getAmazonTemplateTable(query.getIsLock());
        AmazonTemplateExample example = new AmazonTemplateExample(table);
        Criteria criteria = example.createCriteria();

        // 范本用账号控制权 模板用创建人控制
        if (BooleanUtils.isTrue(query.getIsLock())) {
            ApiResult<List<String>> authorAccountList = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_AMAZON, false);
                if (!authorAccountList.isSuccess()) {
                    CQueryResult<AmazonTemplateBO> amazonDataCollectCQueryResult = new CQueryResult<>();
                    amazonDataCollectCQueryResult.setErrorMsg(authorAccountList.getErrorMsg());
                    return amazonDataCollectCQueryResult;
                }
                List<String> accounts = authorAccountList.getResult();
                if (CollectionUtils.isNotEmpty(accounts)) {
                    criteria.andSellerIdIn(accounts);
                } else {
                    return new CQueryResult<>();
                }
        } else if (query.getIsLock() != null && !query.getIsLock()) {
            String platform = SaleChannel.CHANNEL_AMAZON;
            // 人员权限
            Pair<Boolean, List<String>> employeePair = PermissionsHelper.getDefaultOrAuthorEmployeePair(platform, query.getCreatedBy(), null,
                    query.getSellerId(), query.getSellerIds());
            if (BooleanUtils.isTrue(employeePair.getLeft())) {
                criteria.andCreatedByIn(employeePair.getRight());
            }
        }

        /*if (!isAdmin) {
            //创建人不是admin
            criteria.andCreatedByNotEqualTo(SysAttrMapping.createByAdmin);
        }*/
        if (StringUtils.isNotBlank(query.getIdsStr())) {
            criteria.andIdIn(query.getIdList());
        }
        if (StringUtils.isNotBlank(query.getSkus())) {
            criteria.andParentSkuIn(query.getSkuList());
        }
        if (CollectionUtils.isNotEmpty(query.getSellerIds())) {
            criteria.andSellerIdIn(query.getSellerIds());
        }
        if (StringUtils.isNotBlank(query.getTitle())) {
            criteria.andTitleLike("%" + query.getTitle() + "%");
        }
        if (StringUtils.isNotBlank(query.getCreatedBy())) {
            criteria.andCreatedByEqualTo(query.getCreatedBy());
        }

        if (query.getCreationDateStart() != null) {
            criteria.andCreationDateGreaterThanOrEqualTo(query.getCreationDateStart());
        }
        if (query.getCreationDateEnd() != null) {
            criteria.andCreationDateLessThanOrEqualTo(query.getCreationDateEnd());
        }
        if (query.getLastUpdateDateStart() != null) {
            criteria.andLastUpdateDateGreaterThanOrEqualTo(query.getLastUpdateDateStart());
        }
        if (query.getLastUpdateDateEnd() != null) {
            criteria.andLastUpdateDateLessThanOrEqualTo(query.getLastUpdateDateEnd());
        }
        if (query.getSaleVariant() != null) {
            criteria.andSaleVariantEqualTo(query.getSaleVariant());
        }
        if (StringUtils.isNotBlank(query.getReportSolutionType())) {
            criteria.andReportSolutionTypeEqualTo(query.getReportSolutionType());
        }
        if (query.getIsLock() != null) {
            criteria.andIsLockEqualTo(query.getIsLock());
        }
        // 刊登状态
        if (StringUtils.isNotBlank(query.getPublishStepStatus())) {
            if ("none".equals(query.getPublishStepStatus())) {
                //待刊登
                criteria.andStepTemplateStatusIsNull();
                criteria.andPublishStatusEqualTo(AmaoznPublishStatusEnum.WAIT_PUBLISH.getStatusCode());
            } else if ("true".equals(query.getPublishStepStatus())) {
                criteria.andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISH_SUCCESS.getStatusCode());
            } else if ("false".equals(query.getPublishStepStatus())) {
                criteria.andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode());
            } else if ("publishing".equals(query.getPublishStepStatus())) {
                //刊登中
                criteria.andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISHING.getStatusCode());
            }
        }
        // 数据来源
        if (null != query.getSkuDataSource() && query.getSkuDataSource() > 0) {
            criteria.andSkuDataSourceEqualTo(query.getSkuDataSource());
        }
        // 刊登类型
        if (null != query.getPublishType() && query.getPublishType() > 0) {
            criteria.andPublishTypeEqualTo(query.getPublishType());
        }
        if (CollectionUtils.isNotEmpty(query.getPublishTypes())) {
            criteria.andPublishTypeIn(query.getPublishTypes());
        }

        // 刊登角色
        if (null != query.getPublishRole()) {
            criteria.andPublishRoleEqualTo(query.getPublishRole());
        }
        if (StringUtils.isNotEmpty(query.getSite())) {
            String site = query.getSite().trim().equalsIgnoreCase("GB") ? "UK" : query.getSite();
            criteria.andCountryEqualTo(site);
        }
        if (CollectionUtils.isNotEmpty(query.getSiteList())) {
            criteria.andCountryIn(query.getSiteList());
        }
        //分类类型
        if (StringUtils.isNotBlank(query.getProductType())) {
            criteria.andProductTypeEqualTo(query.getProductType());
        }
        if (StringUtils.isNotBlank(query.getParentProductType())) {
            criteria.andParentProductTypeEqualTo(query.getParentProductType());
        }
        // 接口类型
        if (query.getInterfaceType() != null) {
            criteria.andInterfaceTypeEqualTo(query.getInterfaceType());
        }

        long total = amazonTemplateMapper.countByExample(example);
        //问题解决方案
        if (BooleanUtils.isTrue(query.getSolutionTypeMount())) {
            CQueryResult<AmazonTemplateBO> result = new CQueryResult<>();
            result.setTotal(total);
            return result;
        }
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        // 排序
        if (StringUtils.isNotBlank(query.getOrderType())) {
            example.setOrderByClause(query.getOrderType());
        }
        // 侵权词
        //List<InfringementWord> infringementWords = infringementWordService.getRedisAllInfringingWordsList();
        List<AmazonTemplateBO> amazonTemplateBOs = amazonTemplateMapper.selectByExampleBOs(example);
        /*if (CollectionUtils.isNotEmpty(amazonTemplateBOs)) {
            for (AmazonTemplateBO amazonTemplateBO : amazonTemplateBOs) {
                if (CollectionUtils.isNotEmpty(infringementWords)) {
                    // 侵权词集合
                    List<String> tortList = new ArrayList<>();
                    for (InfringementWord infringementWord : infringementWords) {
                        if (infringementWord.getIsforbidden()){
                            continue;
                        }
                        boolean isTort = false;
                        // 侵权词包含
                        if (null == infringementWord.getIsIgnore() || infringementWord.getIsIgnore().intValue() == 1) {
                            isTort = TortUtils.matchingTort(amazonTemplateBO.getTitle(), infringementWord.getWord());
                        }
                        else {
                            isTort = TortUtils
                                    .matchingCaseTort(amazonTemplateBO.getTitle(), infringementWord.getWord());
                        }
                        if (isTort) {
                            if (!tortList.contains(infringementWord.getWord())) {
                                tortList.add(infringementWord.getWord());
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(tortList)) {
                        amazonTemplateBO.setTortList(tortList);
                    }
                }
            }
        }*/
        // 组装结果
        CQueryResult<AmazonTemplateBO> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(amazonTemplateBOs);
        return result;
    }

    @Override
    public AmazonTemplateBO getTemplateDataByArticleNumber(String articleNumber, String country, String accountNumber) {
        if (StringUtils.isNotBlank(articleNumber)) {
            // 查询产品
            List<String> articleNumberList = new ArrayList<>(1);
            articleNumberList.add(articleNumber);
            List<ProductInfo> productInfoList = ProductUtils.findProductInfos(articleNumberList);
            if (CollectionUtils.isEmpty(productInfoList)) {
                throw new BusinessException(articleNumber + "，未查询到对应产品数据");
            }
            // 非特供账号不能上架特供标签产品
            checkSpecialAccount(accountNumber, productInfoList);

            // 非EU-FBA账号不能上架EU-FBA标签产品
            checkEuFbaSpecialAccount(accountNumber, productInfoList);


            // 校验插头规格
            ProductUtils.filterCountryOfPlugSpecification(productInfoList, country);
            if (CollectionUtils.isEmpty(productInfoList)) {
                throw new BusinessException(articleNumber + "校验插头规格不适用当前站点，不可以刊登");
            }
            // 过滤 停产、存档、废弃 sku
            ProductUtils.filterCantPublishSkuStatusProduct(productInfoList);
            if (CollectionUtils.isNotEmpty(productInfoList)) {
                // 过滤禁售
                List<String> skuList = productInfoList.stream().map(productInfo -> productInfo.getSonSku()).collect(Collectors.toList());
                ApiResult<Map<String, Boolean>> isForbiddenSaleChannelResult = amazonTemplateForbiddenSaleChannelHelper.checkArticleNumberIsForbiddenSaleChannel(skuList, country, accountNumber);
                if (!isForbiddenSaleChannelResult.isSuccess()) {
                    throw new RuntimeException("查询禁售信息不成功：" + isForbiddenSaleChannelResult.getErrorMsg());
                }
                Map<String, Boolean> checkSku = isForbiddenSaleChannelResult.getResult();
                productInfoList.removeIf(productInfo -> (checkSku.containsKey(productInfo.getSonSku()) && checkSku.get(productInfo.getSonSku())));
            }

            if (CollectionUtils.isEmpty(productInfoList)) {
                throw new BusinessException(articleNumber + "禁售或停产存档废弃，不可以刊登");
            }
            ProductInfo productInfo = productInfoList.get(0);
            AmazonTemplateBO amazonTemplate = new AmazonTemplateBO();
            amazonTemplate.setCountry(country);
            List<StockKeepingUnitWithBLOBs> skus = stockKeepingUnitService.handleProductInfo(productInfoList);
            String fullpathcode = productInfo.getFullpathcode();
            amazonTemplate.setFullPathCode(fullpathcode);
            amazonTemplate.setSkuProductCategoryCode(StringUtils.substringAfterLast(fullpathcode, "_"));
            if (CollectionUtils.isEmpty(skus)) {
                return null;
            }
            if (productInfoList.size() == 1 && (productInfo.getMainSku() == productInfo.getSonSku())) {
                //添加单品状态
                amazonTemplate.setSkulifecyclephase(productInfo.getItemStatus());
            }
            Boolean saleVariant = true;
            if (null != productInfo.getType()) {
                //添加单品状态
                amazonTemplate.setSaleVariant(BooleanUtils.toBoolean(productInfo.getType()));
                saleVariant = BooleanUtils.toBoolean(productInfo.getType());
            }
            amazonTemplate.setParentSku(articleNumber);
            //匹配标题 描述 五点描述
            AmazonMatchProdInfoUtil.matchTemplateTitleDescInfo(productInfo.getMainSku(), amazonTemplate, productInfo);
            if (null != productInfo.getSingleSource()) {
                // 来源
                amazonTemplate.setSingleSource(productInfo.getSingleSource());
                if (SingleSourceEnum.TAN_YA.getCode() == productInfo.getSingleSource()) {
                    amazonTemplate.setSkuDataSource(SkuDataSourceEnum.TAN_YA_SYSTEM.getCode());
                } else if (SingleSourceEnum.GUAN_TONG.getCode() == productInfo.getSingleSource()) {
                    amazonTemplate.setSkuDataSource(SkuDataSourceEnum.GUAN_TONG_SYSTEM.getCode());
                } else {
                    amazonTemplate.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
                }
            }

            // 获取货号的Color，Size属性
            SkuTheme skuTheme = null;
            if (saleVariant) {
                if (SingleSourceEnum.TAN_YA.getCode().equals(productInfo.getSingleSource())) {
                    //探雅科技，不做属性替换
                    skuTheme = SkuThemeUtils.parseTanYa(productInfoList);
                    if (skuTheme != null) {
                        List<AmazonSku> amazonSkus = new ArrayList<AmazonSku>(skus.size());
                        amazonTemplate.setVariationThemes(skuTheme.getVariationThemes());
                        for (StockKeepingUnitWithBLOBs item : skus) {
                            String itemArticleNumber = item.getArticleNumber();
                            if (StringUtils.isNotEmpty(itemArticleNumber)) {
                                // 获取产品的变体属性
                                AmazonSku amazonSku = new AmazonSku();
                                amazonSkus.add(amazonSku);
                                amazonSku.setSku(itemArticleNumber);
                                amazonSku.setMainImage(item.getProductImage());
                                amazonSku.setNameValues(skuTheme.getVariants().get(itemArticleNumber));
                                amazonSku.setName(amazonSku.getSku());
                                //添加单品状态
                                amazonSku.setSkulifecyclephase(item.getSkulifecyclephase());
                            }
                        }
                        amazonTemplate.setAmazonSkus(amazonSkus);
                        amazonTemplate.setVariations(JSON.toJSONString(amazonSkus));
                    }
                } else if (SingleSourceEnum.GUAN_TONG.getCode().equals(productInfo.getSingleSource())) {
                    // 2020/11/17 冠通没有变体 ,预留位置

                } else {
                    //默认方式
                    skuTheme = SkuThemeUtils.parseProductAttrs(productInfoList);
                    if (skuTheme != null) {
                        List<AmazonSku> amazonSkus = new ArrayList<>(skus.size());
                        amazonTemplate.setVariationThemes(skuTheme.getVariationThemes());
                        Set<String> valueSet = new HashSet<>();
                        for (StockKeepingUnitWithBLOBs item : skus) {
                            String itemArticleNumber = item.getArticleNumber();
                            if (StringUtils.isNotEmpty(itemArticleNumber)) {
                                // 获取产品的变体属性
                                AmazonSku amazonSku = new AmazonSku();
                                amazonSkus.add(amazonSku);
                                amazonSku.setSku(itemArticleNumber);
                                //改为取文件系统
                                //amazonSku.setMainImage(item.getProductImage());
                                List<NameValue> nameValueList = skuTheme.getVariants().get(itemArticleNumber);
                                nameValueList.forEach(o -> {
                                    valueSet.add(o.getValue().trim().toUpperCase());
                                });
                                nameValueList = AmazonTemplateUtils.handleValueReplace(nameValueList, valueSet);
                                amazonSku.setNameValues(nameValueList);
                                amazonSku.setName(amazonSku.getSku());
                                //添加单品状态
                                amazonSku.setSkulifecyclephase(item.getSkulifecyclephase());
                            }
                        }
                        amazonTemplate.setAmazonSkus(amazonSkus);
                        amazonTemplate.setVariations(JSON.toJSONString(amazonSkus));
                    }
                }
            }

//            if (saleVariant && (null == productInfo.getSingleSource() || productInfo.getSingleSource()!= 4)) {
//            }else if(saleVariant){
//            }


            SkuWeightAndPriceMsg skuWeightAndPriceMsg = stockKeepingUnitService.handleWeigjtAndPriceMsg(skus);
            // 存储最大采购价传到页面
            amazonTemplate.setProfitMargin(skuWeightAndPriceMsg.getPrice());
            Double shippingWeight = skuWeightAndPriceMsg.getShippingWeight();
            if (shippingWeight != null) {
                amazonTemplate.setShippingWeight(BigDecimal.valueOf(shippingWeight).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                amazonTemplate.setWeightSku(skuWeightAndPriceMsg.getWeightSku());
                amazonTemplate.setWeightSkuTag(skuWeightAndPriceMsg.getWeightSkuTag());
            }

            //加冠通库存
            GtProductDetail other = productInfo.getOther();
            if (other != null) {
                Map<String, Integer> site2Stock = other.getSite2Stock();
                amazonTemplate.setSite2Stock(site2Stock);
            }

            return amazonTemplate;
        }
        return null;
    }

    private void checkSpecialAccount(String accountNumber, List<ProductInfo> productInfoList) {
        AmazonTemplateValidationContext validationContext = new AmazonTemplateValidationContext();
        validationContext.setProductData(productInfoList);
        validationContext.setAccountNumber(accountNumber);
        singleProductValidationExecutor.validationCanPublishSpecialTagAccount(validationContext);
        if (validationContext.isAllSuccess()) {
            return;
        }
        String errorMessage = validationContext.getValidations().stream().map(AmazonTemplateValidationDO::getErrorMsg).findFirst().orElse("非特供店铺，不能上架AMZ特供产品");
        throw new BusinessException(errorMessage);
    }

    private void checkEuFbaSpecialAccount(String accountNumber, List<ProductInfo> productInfoList) {
        AmazonTemplateValidationContext validationContext = new AmazonTemplateValidationContext();
        validationContext.setProductData(productInfoList);
        validationContext.setAccountNumber(accountNumber);
        singleProductValidationExecutor.validationCanPublishEuFbaSpecialTagAccount(validationContext);
        if (validationContext.isAllSuccess()) {
            return;
        }
        String errorMessage = validationContext.getValidations().stream().map(AmazonTemplateValidationDO::getErrorMsg).findFirst().orElse("非EU-FBA店铺，不能刊登EU-FBA 特殊标签产品");
        throw new BusinessException(errorMessage);
    }

    @Override
    public List<String> reCompose(String parentSku, String country, List<AmazonSku> amazonSkus, String templateName) {
        if (StringUtils.isBlank(parentSku)) {
            return new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(amazonSkus)) {
            amazonSkus = new ArrayList<>();
            AmazonSku amazonSku = new AmazonSku();
            amazonSku.setSku(parentSku);
            amazonSkus.add(amazonSku);
        }

        List<String> keyWords = new ArrayList<>(amazonSkus.size());
        Set<String> searchDatasSet = new HashSet<>();

        //获取es第一个产品的必填关键词
        String mainSku = ProductUtils.getMainSku(parentSku);
        SingleItemEs skuInfoByMainSku = singleItemEsService.getSkuInfoByMainSku(mainSku);
        if (skuInfoByMainSku != null) {
            List<SingleItemOfficial> singleItemOfficials = skuInfoByMainSku.getSingleItemOfficials();
            if (CollectionUtils.isNotEmpty(singleItemOfficials)) {
                for (SingleItemOfficial singleItemOfficial : singleItemOfficials) {
                    String language = singleItemOfficial.getLanguage();
                    //取en
                    if (StringUtils.equalsIgnoreCase(language, "en")) {
                        //多个关键词是英文逗号分割，1个关键词 多个是换行符分割
                        String mustKeyword = singleItemOfficial.getMustKeyword();
                        List<String> strings = JSONArray.parseArray(mustKeyword, String.class);
                        if (CollectionUtils.isEmpty(strings)) continue;
                        for (String string : strings) {
                            String s = string.replaceAll("\n", ",").replaceAll("\r", ",");
                            List<String> strings1 = CommonUtils.splitList(s, ",");
                            for (String s1 : strings1) {
                                if (!s1.contains(":")) {
                                    searchDatasSet.add(s1);
                                }
                            }
                        }
                    }
                }
            }
        }

        if (searchDatasSet.isEmpty()) {
            return new ArrayList<>();
        }
        int maxByteLength = KeyWordUtils.getKeyWordMaxLengthBySite(country, templateName);
        for (int i = 0; i < amazonSkus.size(); i++) {
            try {
                String result = KeyWordUtils.generateStr(new ArrayList<>(searchDatasSet), maxByteLength);
                if (StringUtils.isNotBlank(result)) {
                    keyWords.add(result);
                }
            } catch (Exception e) {
                log.error("获取keyword错误：", e);
            }
        }
        return keyWords;
    }

    private String handleStringSpaceSplit(String str, int maxCount) {
        str = str.trim();
        int length = str.length();
        while (length > maxCount) {
            Integer index = str.trim().lastIndexOf(" ");
            str = str.substring(0, index);
            length = str.length();
        }
        return str;
    }

    @Override
    public void generateAmazonTemplateSellerSKU(AmazonTemplateBO template) {
        if (template == null) {
            return;
        }
        // 2020/11/16 设置冠通sellerSku 前缀
        String sellerSkuPrefix = "";
        if (SkuDataSourceEnum.GUAN_TONG_SYSTEM.isTrue(template.getSkuDataSource())
                || SkuDataSourceEnum.GUAN_TONG_DA_JIAN_CLOUD_WARE_HOUSE.isTrue(template.getSkuDataSource())) {
            //SKU生成规则在加完前后缀之后，在最前面加上 GT-
            sellerSkuPrefix = "GT-";
        }

        boolean needUpdate = false;
        String sellerId = template.getSellerId();
        String createdBy = template.getCreatedBy();
        String skuSuffix = template.getSkuSuffix();

        AmazonSellerSkuRule sellerSkuRule = null;
        // 判断主SellerSku为空,或者变体情况下,存在一个空的子sellerSku 都重新生成一遍
        boolean resetSellerSku = StringUtils.isBlank(template.getSellerSKU()) || isVariantSellerSkuBlank(template);
        if (resetSellerSku) {
            sellerSkuRule = SellerSkuRuleUtils.getAmazonSellerSku(sellerId, createdBy);
            // 兼容老数据
            needUpdate = true;
            String sellerSKU;
            if (StringUtils.isEmpty(skuSuffix)) {
                sellerSKU = SellerSkuRuleUtils.generate(template.getParentSku(), sellerSkuRule);
            } else {
                sellerSKU = template.getParentSku().trim() + AmazonConstant.SKU_SUFFIX_JOIN + skuSuffix.trim();
            }
            template.setSellerSku(sellerSkuPrefix + sellerSKU);
        }
        // 若是变体，则初始化变体sellerSKU
        if (BooleanUtils.toBoolean(template.getSaleVariant())) {
            List<AmazonSku> amazonSkus = template.getAmazonSkus();
            if (CollectionUtils.isNotEmpty(amazonSkus)) {
                for (AmazonSku amazonSku : amazonSkus) {
                    String variantSellerSKU = amazonSku.getSellerSKU();
                    String sku = amazonSku.getSku();
                    if (StringUtils.isEmpty(variantSellerSKU) || resetSellerSku) {
                        needUpdate = true;
                        if (StringUtils.isEmpty(skuSuffix)) {

                            variantSellerSKU = SellerSkuRuleUtils.generate(sku, sellerSkuRule);
                        } else {
                            variantSellerSKU = sku.trim() + AmazonConstant.SKU_SUFFIX_JOIN + skuSuffix.trim();
                        }
                        amazonSku.setSellerSKU(sellerSkuPrefix + variantSellerSKU);
                    }
                }
                if (needUpdate) {
                    template.setVariations(JSON.toJSONString(amazonSkus));
                }
            }
        }

        // 保存平台货号
        if (template.getId() != null && needUpdate) {
            update(template);
        }
    }

    /**
     * 判断模板是否是变体并存在空的子sellerSku
     *
     * @param template 模板
     * @return false: 不存在空SellerSku  true: 存在空sellerSku
     */
    private boolean isVariantSellerSkuBlank(AmazonTemplateBO template) {
        if (Boolean.TRUE.equals(template.getSaleVariant())) {
            List<AmazonSku> amazonSkus = template.getAmazonSkus();
            if (CollectionUtils.isEmpty(amazonSkus)) {
                return false;
            }
            return amazonSkus.stream().map(AmazonSku::getSellerSKU).anyMatch(StringUtils::isBlank);
        }
        return false;
    }

    @Override
    public List<AmazonTemplate> list(AmazonTemplateExample example) {
        Assert.notNull(example.getTable(), "未设置具体table!");
        return amazonTemplateMapper.selectByExample(example);
    }

    @Override
    public void batchUpdateAmazonTemplateFilterNullByPrimaryKey(List<AmazonTemplateBO> entityList) {
        if (CollectionUtils.isNotEmpty(entityList)) {
            for (AmazonTemplateBO amazonTemplateBO : entityList) {
                updateAmazonTemplateFilterNullByPrimaryKey(amazonTemplateBO);
            }
        }
    }

    @Override
    public void updateAmazonTemplateFilterNullByPrimaryKey(AmazonTemplateBO amazonTemplateBO) {
        if (amazonTemplateBO == null || null == amazonTemplateBO.getId()) {
            return;
        }
        amazonTemplateBO.initTable();
        amazonTemplateBO.setLastUpdateDate(new Date());
        amazonTemplateMapper.updateByIdBO(amazonTemplateBO);
    }

    @Override
    public AmazonTemplateBO selectBoById(Integer id, String table) {
        return amazonTemplateMapper.selectBoById(id, table);
    }

    @Override
    public List<AmazonTemplateBO> selectBoByIds(List<Integer> ids, String table) {
        Assert.notNull(table, "未设置具体table!");
        return amazonTemplateMapper.selectBoByIds(ids, table);
    }

    @Override
    public List<List<String>> getProductImage(String parentSku) {
        //获取文件系统的产品库图片Amazon以及模板上传图片
        String plat = PicturePlatEnum.AMAZON_TEMPLATE_PLAT.getName();
        List<String> images = new ArrayList<String>();
        List<String> templateImages = FmsUtils.getTemplateImages(parentSku, plat);
        if (CollectionUtils.isNotEmpty(templateImages)) {
            images.addAll(templateImages);
            templateImages.clear();
        }

        String type = PictureTypeEnum.AMAZON_EXCLUSIVE_IMAGE.getName();
        List<String> productImgeList = FmsUtils.getPictureUrlBySkuAndType(parentSku, type);
        if (CollectionUtils.isEmpty(productImgeList)) {
            type = PictureTypeEnum.AMAZON1600_PRODUCT_PLAT.getName();
            productImgeList = FmsUtils.getPictureUrlBySkuAndType(parentSku, type);
        }
        if (CollectionUtils.isEmpty(productImgeList)) {
            type = PictureTypeEnum.AMAZON_PRODUCT_PLAT.getName();
            productImgeList = FmsUtils.getPictureUrlBySkuAndType(parentSku, type);
        }
        if (CollectionUtils.isNotEmpty(productImgeList)) {
            images.addAll(productImgeList);
            productImgeList.clear();
        }
        List<List<String>> result = new ArrayList<>();
        result.add(images);
        return result;
    }

    @Override
    public List<String> getProductImages(String parentSku) {
        List<List<String>> productImage = getProductImage(parentSku);
        if (CollectionUtils.isNotEmpty(productImage)) {
            return productImage.get(0);
        }
        return new ArrayList<>();
    }

    @Override
    public List<String> getAllProductImages(String parentSku) {
        //获取文件系统的产品库图片Amazon以及模板上传图片
        List<String> images = new ArrayList<>();

        String type = PictureTypeEnum.AMAZON_EXCLUSIVE_IMAGE.getName();
        List<String> productImgeList = FmsUtils.getPictureUrlBySkuAndType(parentSku, type);
        if (CollectionUtils.isNotEmpty(productImgeList)) {
            images.addAll(productImgeList);
        }
        type = PictureTypeEnum.AMAZON1600_PRODUCT_PLAT.getName();
        productImgeList = FmsUtils.getPictureUrlBySkuAndType(parentSku, type);
        if (CollectionUtils.isNotEmpty(productImgeList)) {
            images.addAll(productImgeList);
        }
        type = PictureTypeEnum.AMAZON_PRODUCT_PLAT.getName();
        productImgeList = FmsUtils.getPictureUrlBySkuAndType(parentSku, type);
        if (CollectionUtils.isNotEmpty(productImgeList)) {
            images.addAll(productImgeList);
        }
        return images;
    }

    @Override
    public List<String> handleCategoryProductType(String browseNodeId) {
        if (StringUtils.isNotEmpty(browseNodeId)) { // 只需获取到产品类型对应分类类型的结果即可
            AmazonCategoryProductXsd productTypeXsd = amazonCategoryProductXsdService.findByBrowseNodeId(browseNodeId);
            if (productTypeXsd != null) {
                return XsdUtils.resolutionXsds(productTypeXsd.getProductXsdName());
            }
        }
        return new ArrayList<>();
    }

    @Override
    public void batchCreateAmazonTemplate(List<AmazonTemplateWithBLOBs> amazonTemplates, String table) {
        Assert.notNull(table, "未设置具体table!");
        if (CollectionUtils.isNotEmpty(amazonTemplates)) {
            amazonTemplateMapper.batchCreateAmazonTemplate(amazonTemplates, table);
        }
    }

    @Override
    public List<AmazonTemplateBO> copyAmazonTemplateList(List<AmazonTemplateBO> list, List<Integer> failIdList) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<AmazonTemplateBO> templates = new ArrayList<>();
        list.forEach(amazonTemplate -> {
            AmazonTemplateWithBLOBs amazonTemplateWithBLOBs = new AmazonTemplateWithBLOBs();
            BeanUtils.copyProperties(amazonTemplate, amazonTemplateWithBLOBs);
            // 过滤
            amazonTemplateWithBLOBs = this.handleSkulifecyclephase(amazonTemplateWithBLOBs);
            if (null != amazonTemplateWithBLOBs) {
                AmazonTemplateBO amazonTemplateBO = new AmazonTemplateBO();
                BeanUtils.copyProperties(amazonTemplateWithBLOBs, amazonTemplateBO);
                templates.add(amazonTemplateBO);
            } else {
                failIdList.add(amazonTemplate.getId());
            }
        });

        List<AmazonTemplateBO> copyTemplates = new ArrayList<>(templates.size());
        if (CollectionUtils.isNotEmpty(templates)) {
            Map<String, Integer> publishRoleMap = new HashMap<>();
            for (AmazonTemplateBO template : templates) {
                AmazonTemplateBO copyTemplate = handleTemplateBO(template);
                copyTemplate.setListingRelationTimes(0);
                copyTemplate.setPublishType(PublishTypeEnum.COMMON_PUBLISH.getCode());

                // 刊登角色
                if (null == copyTemplate.getPublishRole() && StringUtils.isNotBlank(copyTemplate.getCreatedBy())) {
                    Integer publishRole = publishRoleMap.get(copyTemplate.getCreatedBy());
                    if (null == publishRole) {
                        publishRole = RoleUtils.getPublishRole(copyTemplate.getCreatedBy());
                        publishRoleMap.put(copyTemplate.getCreatedBy(), publishRole);
                    }
                    copyTemplate.setPublishRole(publishRole);
                }
                if (null == copyTemplate.getCreationDate()) {
                    copyTemplate.setCreationDate(new Timestamp(System.currentTimeMillis()));
                }
                // 如果修改时间为空默认取创建时间
                if (null == copyTemplate.getLastUpdateDate()) {
                    copyTemplate.setLastUpdateDate(copyTemplate.getCreationDate());
                }
                if (copyTemplate.getSaleVariant()) {
                    List<AmazonSku> amazonSkuList = JSON.parseArray(copyTemplate.getVariations(), AmazonSku.class);
                    for (AmazonSku amazonSku : amazonSkuList) {
                        amazonSku.setRepeatFlag(null);
                    }
                    copyTemplate.setVariations(JSON.toJSONString(amazonSkuList));
                }
                copyTemplate.initTable();
                amazonTemplateMapper.insertAmazonTemplateBO(copyTemplate);
                copyTemplates.add(copyTemplate);
            }
        }
        return copyTemplates;
    }

    /**
     * 模板母版复制，校验SKU状态，过滤停产、存档的sku,重复子sku标识置空
     *
     * @param amazonTemplateWithBLOBs
     * @return
     */
    @Override
    public AmazonTemplateWithBLOBs handleSkulifecyclephase(AmazonTemplateWithBLOBs amazonTemplateWithBLOBs) {
        if (null == amazonTemplateWithBLOBs) {
            return null;
        }
        if (SkuDataSourceEnum.COMPOSE_SYSTEM.isTrue(amazonTemplateWithBLOBs.getSkuDataSource())) {
            ApiResult<Boolean> result = amazonTemplateBuilderHelper.checkCanPublish(amazonTemplateWithBLOBs);
            if (result.isSuccess() && result.isSuccess()) {
                return amazonTemplateWithBLOBs;
            }
            return null;
        }

        if (!amazonTemplateWithBLOBs.getSaleVariant()) {
            //单品
            List<String> articleNumberList = new ArrayList<>();
            articleNumberList.add(amazonTemplateWithBLOBs.getParentSku());

            //articleNumberList = stockKeepingUnitService.selectOfferLineSkus(articleNumberList);
            //根据sku集合查询单品状态为'Stop', 'Archived'的sku
            articleNumberList = ProductUtils.findStopAndArchivedSku(articleNumberList);
            if (CollectionUtils.isNotEmpty(articleNumberList)) {
                return null;
            }
        }
        if (amazonTemplateWithBLOBs.getSaleVariant() && null != amazonTemplateWithBLOBs.getVariations()) {
            //多属性
            List<AmazonSku> amazonSkuList = JSON.parseArray(amazonTemplateWithBLOBs.getVariations(), AmazonSku.class);
            List<String> articleNumberList = new ArrayList<>();
            Map<String, AmazonSku> amazonSkuMap = new HashMap<>();
            List<String> finalArticleNumberList = articleNumberList;
            amazonSkuList.forEach(amazonSku -> {
                finalArticleNumberList.add(amazonSku.getSku());
                amazonSku.setRepeatFlag(null);
                amazonSkuMap.put(amazonSku.getSku(), amazonSku);
            });
            List<AmazonSku> newAmazonSkuList = new ArrayList<>();
            //List<String> skuList = stockKeepingUnitService.selectOfferLineSkus(articleNumberList);
            //根据sku集合查询单品状态为'Stop', 'Archived'的sku
            List<String> skuList = ProductUtils.findStopAndArchivedSku(articleNumberList);
            if (CollectionUtils.isNotEmpty(articleNumberList)) {
                for (String sku : articleNumberList) {
                    if (!skuList.contains(sku)) {
                        //过滤停产、存档的sku
                        newAmazonSkuList.add(amazonSkuMap.get(sku));
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(newAmazonSkuList)) {
                amazonTemplateWithBLOBs.setVariations(JSON.toJSONString(newAmazonSkuList));
            } else {
                return null;
            }
        }
        return amazonTemplateWithBLOBs;
    }

    /**
     * 处理复制母版
     *
     * @param amazonTemplate
     * @return
     */
    @Override
    public AmazonTemplateBO handleTemplateBO(AmazonTemplateBO amazonTemplate) {
        AmazonTemplateBO template = BeanUtil.copyProperties(amazonTemplate, AmazonTemplateBO.class);
        template.setId(null);
        template.setIsLock(false);
        template.setStepTemplateStatus(null);
        template.setPublishStatus(null);
        template.setIsSitePublish(null);
        template.setCreationDate(new Date());
        template.setLastUpdateDate(new Date());
        template.setLastUpdatedBy(null);
        template.setListingRelationTimes(0);
        template.setTitleRule(null);
        if (template.getSearchData() != null) {
            try {
                Object keyWord = KeyWordUtils.packagingKeyWord(template);
                template.setSearchTerms(JSON.toJSONString(keyWord));
            } catch (Exception e) {
                log.error("处理关键词出错：", e);
            }
        }
        return template;
    }

    @Override
    public AmazonTemplateWithBLOBs handleTemplateKeyWords(AmazonTemplateWithBLOBs template) {
        ///范本取searchData,模板取searchTerms
        if (template.getSearchTerms() != null) {
            JSONObject json = new JSONObject();
            List<String> sale = new ArrayList<>();
            List<String> skus = new ArrayList<>();
            if (template.getSaleVariant()) {
                String variations = template.getVariations();
                JSONArray jsonArray = JSON.parseArray(variations);
                for (int j = 0; j < jsonArray.size(); j++) {
                    String sku = jsonArray.getJSONObject(j).getString("sku");
                    skus.add(sku);
                }
            }
            // 法、德、意、西四个国家最大字符数量修改为230
            int maxCount = 0;
            if (template.getCountry().equals("JP")) {
                maxCount = 500;
            } else if (template.getCountry().equals("IN")) {
                maxCount = 200;
            } else if ("FR,DE,IT,ES,UK".contains(template.getCountry())) {
                maxCount = 230;
            } else if (template.getCountry().equals("US")) {
                maxCount = 240;
            } else {
                maxCount = 250;
            }
            String[] splits = template.getSearchTerms().split(",");
            List<String> searchDatas = new ArrayList<>();
            for (String split : splits) {
                if (!split.contains(":")) {
                    searchDatas.add(split);
                }
            }

            if (template.getSaleVariant() && CollectionUtils.isNotEmpty(skus) && CollectionUtils.isNotEmpty(searchDatas)) {
                for (String sku : skus) {
                    Collections.shuffle(searchDatas);
                    int count = 0;
                    String result = "";
                    for (String sd : searchDatas) {
                        count = result.trim().length() + sd.trim().length();
                        if (count < maxCount) {
                            result = result.trim() + " " + sd.trim();
                        } else if (searchDatas.size() == 1) {
                            result = this.handleStringSpaceSplit(sd, maxCount);
                        }
                    }
                    json.put(sku, Arrays.asList(result));
                }
            } else if (CollectionUtils.isNotEmpty(searchDatas)) {
                int count = 0;
                String result = "";
                Collections.shuffle(searchDatas);
                for (String sd : searchDatas) {
                    count = result.trim().length() + sd.trim().length();
                    if (count < maxCount) {
                        result = result.trim() + " " + sd.trim();
                    } else if (searchDatas.size() == 1) {
                        result = this.handleStringSpaceSplit(sd, maxCount);
                    }
                }
                sale.add(JSONObject.toJSONString(result));
            }
            if (template.getSaleVariant() && json.size() > 0) {
                template.setSearchTerms(json.toString());
            } else if (sale.size() > 0) {
                template.setSearchTerms(sale.toString());
            }
        }
        return template;
    }

    @Override
    public AmazonTemplateWithBLOBs selectByCondition(String sellerId, String sellerSku) {
        Asserts.isTrue(sellerId != null, ErrorCode.PARAM_EMPTY_ERROR);
        Asserts.isTrue(sellerSku != null, ErrorCode.PARAM_EMPTY_ERROR);
        AmazonTemplate amazonTemplate = new AmazonTemplate();
        amazonTemplate.setSellerId(sellerId);
        amazonTemplate.setSellerSku(sellerSku);
        amazonTemplate.setTable(AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
        return this.amazonTemplateMapper.selectByCondition(amazonTemplate);
    }

    @Override
    public void batchInsertAmazonTemplate(List<AmazonTemplateBO> amazonTemplates, String table) {
        if (CollectionUtils.isNotEmpty(amazonTemplates)) {
            Assert.notNull(table, "未设置具体table!");
            Map<String, Integer> publishRoleMap = new HashMap<>();
            for (AmazonTemplateBO amazonTemplate : amazonTemplates) {
                // 刊登角色
                if (null == amazonTemplate.getPublishRole() && StringUtils.isNotBlank(amazonTemplate.getCreatedBy())) {
                    Integer publishRole = publishRoleMap.get(amazonTemplate.getCreatedBy());
                    if (null == publishRole) {
                        publishRole = RoleUtils.getPublishRole(amazonTemplate.getCreatedBy());
                        publishRoleMap.put(amazonTemplate.getCreatedBy(), publishRole);
                    }
                    amazonTemplate.setPublishRole(publishRole);
                }
                if (null == amazonTemplate.getCreationDate()) {
                    amazonTemplate.setCreationDate(new Timestamp(System.currentTimeMillis()));
                }
                // 如果修改时间为空默认取创建时间
                if (null == amazonTemplate.getLastUpdateDate()) {
                    amazonTemplate.setLastUpdateDate(amazonTemplate.getCreationDate());
                }
            }

            amazonTemplateMapper.batchInsertAmazonTemplate(amazonTemplates, table);
            AmazonTemplateBO updateAmazonTemplate = new AmazonTemplateBO();
            updateAmazonTemplate.setId(amazonTemplates.get(0).getAmazonVariantId());
            updateAmazonTemplate.setIsSitePublish(true);
            //updateAmazonTemplate.setNoInfringementFilter(false);
            updateAmazonTemplate.setTable(table);
            amazonTemplateMapper.updateByPrimaryKeySelective(updateAmazonTemplate);
        }
    }

    @Override
    public void batchInsert(List<AmazonTemplateBO> amazonTemplates, String table) {
        if (CollectionUtils.isNotEmpty(amazonTemplates)) {
            Assert.notNull(table, "未设置具体table!");
            amazonTemplateMapper.batchInsertAmazonTemplate(amazonTemplates, table);
        }
    }

    @Override
    public void handleAmazonTemplatePublishStatus(int beforeMinutes, int afterMinutes) {
        String publishingTemplateLimitStr = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "AMAZON", "PUBLISHING_TEMPLATE_LIMIT", 10);

        //查询所有的未删除刊登中模板
        LocalTime currentTime = LocalTime.now();
        // 转为整点
        LocalDateTime currentDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(currentTime.getHour(), 0, 0));
        LocalDateTime beforeDateTime = currentDateTime.minusMinutes(beforeMinutes);
        LocalDateTime afterDateTime = currentDateTime.minusMinutes(afterMinutes);

        AmazonTemplateExample example = new AmazonTemplateExample();
        Criteria criteria = example.createCriteria();
        criteria.andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISHING.getStatusCode())
                .andLastUpdateDateLessThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(beforeDateTime))
                .andLastUpdateDateGreaterThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(afterDateTime))
                .andInterfaceTypeEqualTo(TemplateInterfaceTypeEnums.XSD.getCode());
        List<Integer> amazonTemplateIdList = batchGetTemplateId(example);
        if (CollectionUtils.isEmpty(amazonTemplateIdList)) {
            return;
        }
        int selectSize = amazonTemplateIdList.size();
        XxlJobLogger.log("[{}-{}]读取条数：{}", beforeDateTime, afterDateTime, selectSize);
        if (Integer.parseInt(Optional.ofNullable(publishingTemplateLimitStr).orElse("80000")) < selectSize) {
            log.errorForKey("Amazon增量模版状态更新, [{}-{}]刊登中数量超出阈值, 数量:{}, 阈值:{}", "AmazonTemplateStatus", LocalDateTimeUtil.format(afterDateTime), LocalDateTimeUtil.format(beforeDateTime), selectSize, publishingTemplateLimitStr);
        }

        int totalSize = 0;
        for (Integer id : amazonTemplateIdList) {
            try {
                List<AmazonProcessReport> amazonProcessReportList = amazonProcessReportService.selectProcessReportByTemplateId(id);
                AmazonTemplateBO amazonTemplate = this.handleAmazonTemplateStatus(amazonProcessReportList, id, false);
                if (null != amazonTemplate) {
                    totalSize++;
                    updateAmazonTemplateFilterNullByPrimaryKey(amazonTemplate);
                }
            } catch (Exception e) {
                log.error("处理模板[{}]状态出错：", id, e);
            }
        }
        XxlJobLogger.log("[{}-{}]读取条数：{}, 处理条数：{}", beforeDateTime, afterDateTime, selectSize, totalSize);
    }

    public AmazonTemplateBO handleAmazonTemplateStatus(List<AmazonProcessReport> reportList, Integer templateId, Boolean checkTypeLack) {
        if (CollectionUtils.isEmpty(reportList)) {
            return null;
        }
        AmazonTemplateBO amazonTemplate = new AmazonTemplateBO();
        amazonTemplate.setId(templateId);

        // 是否刊登拦截
        Boolean interceptPublishingStatus = handleInterceptPublishingStatus(reportList, templateId);
        if (interceptPublishingStatus) {
            amazonTemplate.setPublishStatus(AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode());
            amazonTemplate.setStepTemplateStatus(false);
            return amazonTemplate;
        }

        List<AmazonProcessReport> feedTypeProcessReportList = reportList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                () -> new TreeSet<>(Comparator.comparing(AmazonProcessReport::getFeedType))), ArrayList::new));

        int size = 4;   //单体
        long count = feedTypeProcessReportList.stream().filter(o -> SpFeedType.POST_PRODUCT_RELATIONSHIP_DATA.getValue().equals(o.getFeedType())).count();
        if (count > 0) {
            size = 5;   //变体
        }
        // 类型缺失 处理报告类型大于1且小于4则模板刊登状态为失败
        boolean typeLack = feedTypeProcessReportList.size() > 1 && feedTypeProcessReportList.size() < size;
        if (typeLack) {
            if (!checkTypeLack) {
                // 增量处理，跳过类型缺失的模板
                return null;
            }
            String processEndTimeOutHour = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "AMAZON", "PROCESS_TASKID_START", 10);
            if (StringUtils.isBlank(processEndTimeOutHour)) {
                XxlJobLogger.log("系统参数配置异常，processEndTimeOutHour:{}", processEndTimeOutHour);
                return null;
            }
            // 最新的处理报告时间
            Optional<AmazonProcessReport> newestReport = reportList.stream()
                    .filter(report -> report.getCreationDate() != null).max(Comparator.comparing(AmazonProcessReport::getCreationDate));
            if (newestReport.isPresent() && LocalDateTimeUtil.of(newestReport.get().getCreationDate()).plusMinutes(Integer.parseInt(processEndTimeOutHour)).isBefore(LocalDateTime.now())) {
                XxlJobLogger.log("类型缺失处理报告超时，templateId:{}, 超时时间:{}, creationDate:{}", templateId, processEndTimeOutHour, newestReport.get().getCreationDate());
                amazonTemplate.setPublishStatus(AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode());
                amazonTemplate.setReportSolutionId(1);
                amazonTemplate.setReportSolutionType(AmazonReportSolutionTypeEnum.SP_FEED_TYPE_LACK.getName());
                amazonTemplate.setStepTemplateStatus(false);
                return amazonTemplate;
            }
            return null;
        }

        if (feedTypeProcessReportList.size() < size) {
            return null;
        }

        // 循环判断处理报告状态处理
        List<AmazonProcessReport> feedTypeSellerSkuReportList = new ArrayList<>(reportList.stream()
                .collect(Collectors.toMap(report -> report.getFeedType() + ";" + report.getDataValue(),
                        Function.identity(),
                        (a, b) -> {
                            //上传类型数据 存在多条一样的取最新的
                            if (a.getCreationDate().before(b.getCreationDate())) {
                                return b;
                            }
                            return a;
                        })).values());

        int publishStatus = AmaoznPublishStatusEnum.PUBLISH_SUCCESS.getStatusCode();
        for (AmazonProcessReport processReport : feedTypeSellerSkuReportList) {
            if (processReport.getStatus() == null) {
                //只有产品上传,且上传到平台 ,未上传 关系、价格、库存、图片 先不判定模板状态
                return null;
            }
            // 存在失败报告，判定模板失败
            if (BooleanUtils.isFalse(processReport.getStatus()) || publishStatus == AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode()) {
                publishStatus = AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode();
            } else {
                publishStatus = AmaoznPublishStatusEnum.PUBLISH_SUCCESS.getStatusCode();
            }
        }
        amazonTemplate.setPublishStatus(publishStatus);
        amazonTemplate.setStepTemplateStatus(publishStatus == AmaoznPublishStatusEnum.PUBLISH_SUCCESS.getStatusCode());
        return amazonTemplate;
    }

    @Override
    public void batchUpdateAmaozntemplatePublishStatus(List<AmazonTemplateBO> amazonTemplateBOList, String spFeedTypeValue) {
        if (CollectionUtils.isEmpty(amazonTemplateBOList)) {
            return;
        }
        AmazonTemplateBO amazonTemplateBO = null;
        List<AmazonTemplateBO> updateAmazonTemplateList = new ArrayList<>(amazonTemplateBOList.size());
        for (AmazonTemplateBO amazonTemplate : amazonTemplateBOList) {
            amazonTemplateBO = new AmazonTemplateBO();
            amazonTemplateBO.setId(amazonTemplate.getId());
            amazonTemplateBO.setStepTemplateStatus(null);
            amazonTemplateBO.setReportSolutionType(amazonTemplate.getReportSolutionType());
            amazonTemplateBO.setReportSolutionId(amazonTemplate.getReportSolutionId());
            amazonTemplateBO.setTitle(amazonTemplate.getTitle());
            amazonTemplateBO.setSearchTerms(amazonTemplate.getSearchTerms());
            amazonTemplate.setPublishStatus(AmaoznPublishStatusEnum.PUBLISHING.getStatusCode());
            amazonTemplateBO.setPublishStatus(AmaoznPublishStatusEnum.PUBLISHING.getStatusCode());
            amazonTemplateBO.setLastUpdateDate(new Date());
            if (spFeedTypeValue.equals(SpFeedType.POST_PRODUCT_DATA.getValue())) {
                amazonTemplateBO.setBrand(amazonTemplate.getBrand());
                amazonTemplateBO.setManufacturer(amazonTemplate.getManufacturer());
                amazonTemplateBO.setMfrPartNumber(amazonTemplate.getMfrPartNumber());
                //设置其他参数
                amazonTemplateBO.setBulletPoint(amazonTemplate.getBulletPoint());
                amazonTemplateBO.setDescription(amazonTemplate.getDescription());
                amazonTemplateBO.setCategoryTemplateName(amazonTemplate.getCategoryTemplateName());
                if (StringUtils.isNotBlank(amazonTemplate.getExtraData())) {
                    amazonTemplateBO.setExtraData(amazonTemplate.getExtraData());
                }
                if (StringUtils.isNotBlank(amazonTemplate.getProductType())) {
                    amazonTemplateBO.setProductType(amazonTemplate.getProductType());
                }
                if (StringUtils.isNotBlank(amazonTemplate.getParentProductType())) {
                    amazonTemplateBO.setParentProductType(amazonTemplate.getParentProductType());
                }
                if (StringUtils.isNotBlank(amazonTemplate.getProduceAttributeMode())) {
                    amazonTemplateBO.setProduceAttributeMode(amazonTemplate.getProduceAttributeMode());
                }
                Optional<Boolean> optionalBool = Optional.ofNullable(amazonTemplate.getHeatSensitiveValue());
                if (StringUtils.isNotBlank(amazonTemplate.getHeatSensitive()) && optionalBool.isPresent()) {
                    amazonTemplateBO.setHeatSensitive(amazonTemplate.getHeatSensitive());
                    amazonTemplateBO.setHeatSensitiveValue(amazonTemplate.getHeatSensitiveValue());
                }
                if (StringUtils.isNotBlank(amazonTemplate.getVariations())) {
                    amazonTemplateBO.setVariations(amazonTemplate.getVariations());
                }
                // 重新刊登需要清空问题分类
                amazonTemplateBO.setReportSolutionType(null);
                amazonTemplateBO.setReportSolutionId(null);
            }

            updateAmazonTemplateList.add(amazonTemplateBO);
        }
        if (CollectionUtils.isNotEmpty(updateAmazonTemplateList)) {
            amazonTemplateMapper.batchUpdateAmazonTemplateStatus(updateAmazonTemplateList, AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
        }
    }

    @Override
    public Boolean handleTimeOutPublishingStatus(AmazonTemplateBO amazonTemplate, List<AmazonProcessReport> reportList) {
        String templateTimeOutHour = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "AMAZON", "TEMPLATE_TIME_OUT", 10);
        if (StringUtils.isBlank(templateTimeOutHour)) {
            return false;
        }
        // 检查模板是否超时
        Date lastUpdateDate = amazonTemplate.getLastUpdateDate();
        // 是否超出23小时无处理报告
        if (LocalDateTimeUtil.of(lastUpdateDate).plusHours(23).isBefore(LocalDateTime.now()) && CollectionUtils.isEmpty(reportList)) {
            amazonTemplate.setReportSolutionType(AmazonReportSolutionTypeEnum.SP_FEED_TYPE_LACK.getName());
            amazonTemplate.setPublishStatus(AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode());
            amazonTemplate.setStepTemplateStatus(false);
            return true;
        }


        if (LocalDateTimeUtil.of(lastUpdateDate).plusMinutes(Integer.parseInt(templateTimeOutHour)).isBefore(LocalDateTime.now())) {
            log.info("模板超时，templateId={}", amazonTemplate.getId());
            // 检查在线列表中是否有在线数据
            List<String> allSellerSkuSku = AmazonTemplateUtils.getAllSellerSkuSku(amazonTemplate);
            if (CollectionUtils.isNotEmpty(allSellerSkuSku)) {
                AmazonProductListingExample example = new AmazonProductListingExample();
                example.createCriteria()
                        .andIsOnlineEqualTo(true)
                        .andAccountNumberEqualTo(amazonTemplate.getSellerId())
                        .andSellerSkuIn(allSellerSkuSku);

                List<AmazonProductListing> productListings = amazonProductListingService.selectByExample(example, amazonTemplate.getCountry());
                if (CollectionUtils.isEmpty(productListings)) {
                    amazonTemplate.setPublishStatus(AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode());
                    amazonTemplate.setStepTemplateStatus(false);
                } else {
                    amazonTemplate.setPublishStatus(AmaoznPublishStatusEnum.PUBLISH_SUCCESS.getStatusCode());
                    amazonTemplate.setStepTemplateStatus(true);
                }
            } else {
                amazonTemplate.setPublishStatus(AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode());
                amazonTemplate.setStepTemplateStatus(false);
            }
            return true;
        }
        return false;
    }

    @Override
    public void handlePublishPrInvPicPcData(int beforeMinutes, int afterMinutes, int retryAfterMinutes) {
        try {
            // 如果产品上传有结果，则上传图片价格库存关系
            int publishNextProcessSize = handleTemplatePublishNextProcess(beforeMinutes, afterMinutes);

            // 如果存在类型缺失重传，取6小时前刊登中的模版如果类型缺失则根据刊登类型重传
            String publishTypeMissingSizeMsg = handleTemplatePublishTypeMissing(retryAfterMinutes);
            XxlJobLogger.log("刊登中模版执行上传产品后续, 正常处理数量：{}, {}", publishNextProcessSize, publishTypeMissingSizeMsg);
            //log.alertMessage("刊登中模版执行上传产品后续, 正常处理数量：{}, {}", publishNextProcessSize, publishTypeMissingSizeMsg);
        } catch (Exception e) {
            log.error("刊登价格/库存/图片/关系出错: {}", e.getMessage(), e);
            //log.errorForKey("刊登价格/库存/图片/关系出错: {}", "handlePublishPrInvPicPcData", e.getMessage());
        }
    }

    /**
     * 如果存在类型缺失重传，取6小时前刊登中的模版如果类型缺失则根据刊登类型重传
     *
     * @param retryAfterMinutes 模版创建时间
     * @return 处理数量
     */
    private String handleTemplatePublishTypeMissing(int retryAfterMinutes) {
        String publishTimeOutTime = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "AMAZON", "TEMPLATE_TIME_OUT", 10);
        // 查询所有的未删除刊登中模板
        // 转为整点
        LocalTime currentTime = LocalTime.now();
        int hour = currentTime.getHour();
        LocalDateTime currentDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(currentTime.getHour(), 0, 0));
        LocalDateTime lastUpdateDate = currentDateTime.minusMinutes(retryAfterMinutes);
        LocalDateTime timeOutDate = currentDateTime.minusMinutes(Integer.parseInt(publishTimeOutTime));

        AmazonTemplateExample example = new AmazonTemplateExample();
        Criteria criteria = example.createCriteria();
        criteria.andLastUpdateDateGreaterThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(timeOutDate))
                .andLastUpdateDateLessThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(lastUpdateDate))
                .andInterfaceTypeEqualTo(TemplateInterfaceTypeEnums.XSD.getCode())
                .andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISHING.getStatusCode());
        List<Integer> amazonTemplateIdList = batchGetTemplateId(example);
        if (CollectionUtils.isEmpty(amazonTemplateIdList)) {
            return "6小时前无刊登中模板";
        }
        XxlJobLogger.log("6小时前类型缺失重传判断刊登中模板数量：{}", amazonTemplateIdList.size());
        String process_time_out = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "AMAZON", "PROCESS_TASKID_END", 10);
        LocalDateTime processTimeOutDateTime = currentDateTime.minusMinutes(Integer.parseInt(StringUtils.defaultString(process_time_out, "360")));
        Date processTimeOutDate = LocalDateTimeUtil.passLocalDateTimeToDate(processTimeOutDateTime);

        AtomicInteger continuePublishSize = new AtomicInteger();
        AtomicInteger reTryTemplateSize = new AtomicInteger();

        List<List<Integer>> partition = Lists.partition(amazonTemplateIdList, 500);
        CountDownLatch countDownLatch = new CountDownLatch(partition.size());
        partition.forEach(templateIds -> {
            AmazonExecutors.executeQueryProcessReport(() -> {
                try {
                    // 类型缺失重传
                    Map<Integer, List<AmazonProcessReport>> reTryTemplateIdReportMap = new HashMap<>();
                    List<Integer> reTryTemplateIdList = new ArrayList<>();
                    // 继续刊登
                    List<Integer> continuePublishIdList = new ArrayList<>();
                    Map<Integer, List<AmazonProcessReport>> publishProductCreateByMap = new HashMap<>();

                    for (Integer templateId : templateIds) {
                        List<AmazonProcessReport> amazonProcessReportList = amazonProcessReportService.selectProcessReportByTemplateId(templateId);
                        if (CollectionUtils.isEmpty(amazonProcessReportList)) {
                            continue;
                        }

                        // 根据类型,sellerSku两个属性去重
                        List<AmazonProcessReport> sellerSkuTypeReportList = amazonProcessReportList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                                () -> new TreeSet<>(Comparator.comparing(o -> o.getFeedType() + ";" + o.getDataValue()))), ArrayList::new));

                        // 根据类型去重
                        List<AmazonProcessReport> feedTypeReportList = sellerSkuTypeReportList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                                () -> new TreeSet<>(Comparator.comparing(AmazonProcessReport::getFeedType))), ArrayList::new));

                        if (feedTypeReportList.size() == 1) {
                            if (hour % 2 != 0) {
                                continue;
                            }
                            boolean nextProcessPublish = true;
                            for (AmazonProcessReport amazonProcessReport : sellerSkuTypeReportList) {
                                if (StringUtils.isBlank(amazonProcessReport.getTaskId()) || amazonProcessReport.getStatus() == null) {
                                    nextProcessPublish = false;
                                    break;
                                }
                            }

                            if (nextProcessPublish) {
                                // 需要刊登价格、图片、库存、关系
                                continuePublishIdList.add(templateId);
                                publishProductCreateByMap.put(templateId, sellerSkuTypeReportList);
                            } else {
                                XxlJobLogger.log("模板:{},类型缺失关系上传, 未完成上传产品", templateId);
                                //log.errorForPolicy("模板:{},类型缺失关系上传, 未完成上传产品", "handleTemplatePublishTypeMissing", new AmazonTemplateStatusAlertPolicy(), templateId);
                            }
                            continue;
                        }
                        // 创建时间前xx分钟至前xx分钟之间，无taskID，且状态未完成
                        List<AmazonProcessReport> retryReportList = new ArrayList<>(amazonProcessReportList.size());
                        for (AmazonProcessReport amazonProcessReport : amazonProcessReportList) {
                            //包含处理中和待处理
                            if (null == amazonProcessReport.getTaskId()
                                    && amazonProcessReport.getCreationDate().before(processTimeOutDate)
                                    && null == amazonProcessReport.getResultMsg()) {
                                retryReportList.add(amazonProcessReport);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(retryReportList)) {
                            List<AmazonProcessReport> reportTypeSellerSkuReportList = retryReportList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                                    () -> new TreeSet<>(Comparator.comparing(o -> o.getFeedType() + ";" + o.getDataValue()))), ArrayList::new));
                            reTryTemplateIdList.add(templateId);
                            reTryTemplateIdReportMap.put(templateId, reportTypeSellerSkuReportList);
                        }
                    }

                    if (CollectionUtils.isNotEmpty(continuePublishIdList)) {
                        continuePublishSize.addAndGet(continuePublishIdList.size());
                        // 继续刊登图片价格库存关系
                        XxlJobLogger.log("继续刊登图片价格库存关系:{}", continuePublishIdList.size());
                        continuePublish(continuePublishIdList, publishProductCreateByMap);
                    }
                    if (CollectionUtils.isEmpty(reTryTemplateIdList)) {
                        return;
                    }
                    reTryTemplateSize.addAndGet(reTryTemplateIdList.size());

                    // 继续刊登
                    XxlJobLogger.log("类型缺失重传模版创建时间小于:{}, 刊登中 size:{}", processTimeOutDate, reTryTemplateIdList.size());
                    AmazonTemplateExample retryExample = new AmazonTemplateExample();
                    Criteria retryExampleCriteria = retryExample.createCriteria();
                    retryExampleCriteria.andLastUpdateDateGreaterThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(timeOutDate))
                            .andLastUpdateDateLessThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(lastUpdateDate))
                            .andInterfaceTypeEqualTo(TemplateInterfaceTypeEnums.XSD.getCode())
                            .andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISHING.getStatusCode());
                    retryExampleCriteria.andIdIn(reTryTemplateIdList);
                    List<AmazonTemplateWithBLOBs> amazonTemplates = amazonTemplateMapper.selectByExampleWithBLOBs(retryExample);
                    if (CollectionUtils.isEmpty(amazonTemplates)) {
                        return;
                    }
                    List<AmazonTemplateBO> amazonTemplateBOs = CommonUtils.listTransform(amazonTemplates, AmazonTemplateBO.class);
                    Map<String, List<AmazonTemplateBO>> seller2TemplatesMap = amazonTemplateBOs.stream().collect(Collectors.groupingBy(AmazonTemplate::getSellerId));
                    //刊登对应模板生成新的处理报告
                    this.handleAmazonProcessReportNeedPublishData(seller2TemplatesMap, reTryTemplateIdReportMap);
                } catch (Exception e) {
                    XxlJobLogger.log("处理模板[{}]类型缺失重传出错：{}", templateIds, e.getMessage());
                } finally {
                    countDownLatch.countDown();
                }
            });
        });
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        if (reTryTemplateSize.get() == 0) {
            return "6小时前类型缺失重传模版:0,继续刊登关系：" + continuePublishSize.get();
        }
        XxlJobLogger.log("类型缺失重传模版创建时间小于:{}, 刊登中 size:{}", processTimeOutDate, reTryTemplateSize.get());
        return "6小时前类型缺失重传模版:" + reTryTemplateSize.get() + ",继续刊登关系：" + continuePublishSize.get();
    }

    /**
     * 模版状态为刊登中且修改时间是1-2个小时前的模板
     * 如果产品上传有结果，则上传图片价格库存关系
     *
     * @return 处理数量
     */
    private int handleTemplatePublishNextProcess(int beforeMinutes, int afterMinutes) {
        String publishingTemplateLimitStr = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "AMAZON", "PUBLISHING_TEMPLATE_LIMIT", 10);
        int publishingTemplateLimit = Integer.parseInt(publishingTemplateLimitStr);

        // 转为整点
        LocalTime currentTime = LocalTime.now();
        LocalDateTime currentDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(currentTime.getHour(), 0, 0));
        LocalDateTime lessThanDate = currentDateTime.minusMinutes(beforeMinutes);
        LocalDateTime greaterThanDate = currentDateTime.minusMinutes(afterMinutes);

        //查询所有的未删除刊登中模板
        AmazonTemplateExample example = new AmazonTemplateExample();
        Criteria criteria = example.createCriteria();
        criteria.andLastUpdateDateLessThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(lessThanDate))
                .andLastUpdateDateGreaterThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(greaterThanDate))
                .andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISHING.getStatusCode())
                .andInterfaceTypeEqualTo(TemplateInterfaceTypeEnums.XSD.getCode());
        List<Integer> amazonTemplateIdList = batchGetTemplateId(example);
        if (CollectionUtils.isEmpty(amazonTemplateIdList)) {
            return 0;
        }
        XxlJobLogger.log("[{} - {}]刊登中模版执行上传产品后续判定条数:{}", greaterThanDate, lessThanDate, amazonTemplateIdList.size());
        AtomicInteger continueSize = new AtomicInteger();
        List<List<Integer>> partition = Lists.partition(amazonTemplateIdList, 500);
        CountDownLatch countDownLatch = new CountDownLatch(partition.size());
        partition.forEach(templateIds -> {
            AmazonExecutors.executeQueryProcessReport(() -> {
                try {
                    // 过滤产品刊登失败或未完成的数据
                    List<Integer> continuePublishIdList = new ArrayList<>();
                    Map<Integer, List<AmazonProcessReport>> publishProductCreateByMap = new HashMap<>();
                    for (Integer templateId : templateIds) {
                        if (continueSize.get() >= publishingTemplateLimit) {
                            log.errorForKey("Amazon模版刊登图片价格库存关系, [{}-{}]处理数量超出阈值, 数量:{}, 阈值:{}", "AmazonTemplateStatus", LocalDateTimeUtil.format(greaterThanDate), LocalDateTimeUtil.format(lessThanDate), amazonTemplateIdList.size(), publishingTemplateLimitStr);
                            break;
                        }
                        checkContinuePublish(continuePublishIdList, publishProductCreateByMap, templateId, continueSize);
                    }
                    if (CollectionUtils.isEmpty(continuePublishIdList)) {
                        XxlJobLogger.log("当前数据无需继续刊登");
                        return;
                    }
                    // 继续刊登图片价格库存关系
                    continuePublish(continuePublishIdList, publishProductCreateByMap);
                } catch (Exception e) {
                    XxlJobLogger.log("刊登中模版执行上传产品后续判定条数,处理异常:{}", e.getMessage());
                } finally {
                    countDownLatch.countDown();
                }
            });
        });
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return continueSize.get();
    }

    private void checkContinuePublish(List<Integer> continuePublishIdList, Map<Integer, List<AmazonProcessReport>> publishProductCreateByMap, Integer templateId, AtomicInteger continueSize) {
        List<AmazonProcessReport> amazonProcessReportList = amazonProcessReportService.selectProcessReportByTemplateId(templateId);
        if (CollectionUtils.isEmpty(amazonProcessReportList)) {
            return;
        }

        // 根据类型,sellerSku两个属性去重
        List<AmazonProcessReport> sellerSkuTypeReportList = amazonProcessReportList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                () -> new TreeSet<>(Comparator.comparing(o -> o.getFeedType() + ";" + o.getDataValue()))), ArrayList::new));

        // 根据类型去重
        List<AmazonProcessReport> feedTypeReportList = sellerSkuTypeReportList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                () -> new TreeSet<>(Comparator.comparing(AmazonProcessReport::getFeedType))), ArrayList::new));

        if (feedTypeReportList.size() == 1) {
            boolean nextProcessPublish = true;
            for (AmazonProcessReport amazonProcessReport : sellerSkuTypeReportList) {
                if (StringUtils.isBlank(amazonProcessReport.getTaskId()) || amazonProcessReport.getStatus() == null) {
                    nextProcessPublish = false;
                    break;
                }
            }

            if (nextProcessPublish) {
                // 需要刊登价格、图片、库存、关系
                continuePublishIdList.add(templateId);
                continueSize.getAndIncrement();
                publishProductCreateByMap.put(templateId, sellerSkuTypeReportList);
            }
        }
    }

    /**
     * 继续刊登图片价格库存关系
     */
    private void continuePublish(List<Integer> continuePublishIdList, Map<Integer, List<AmazonProcessReport>> publishProductCreateByMap) {
        AmazonTemplateExample example = new AmazonTemplateExample();
        Criteria criteria = example.createCriteria();
        criteria.andIdIn(continuePublishIdList)
                .andInterfaceTypeEqualTo(TemplateInterfaceTypeEnums.XSD.getCode())
                .andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISHING.getStatusCode());

        List<AmazonTemplateWithBLOBs> amazonTemplates = amazonTemplateMapper.selectByExampleWithBLOBs(example);
        XxlJobLogger.log("刊登中模版执行上传产品后续模板数:{}", amazonTemplates.size());
        List<AmazonTemplateBO> publishAllList = new ArrayList<>(amazonTemplates.size());
        for (AmazonTemplateWithBLOBs amazonTemplateBOS : amazonTemplates) {
            AmazonTemplateBO amazonTemplateBO = BeanUtil.copyProperties(amazonTemplateBOS, AmazonTemplateBO.class);
            if (publishProductCreateByMap.containsKey(amazonTemplateBO.getId())) {
                amazonTemplateBO.setLastUpdatedBy(publishProductCreateByMap.get(amazonTemplateBO.getId()).get(0).getCreatedBy());
            } else {
                continue;
            }
            if (amazonTemplateBO.getSaleVariant() && StringUtils.isNotEmpty(amazonTemplateBO.getVariations())) {
                int size = publishProductCreateByMap.get(amazonTemplateBO.getId()).size();
                List<AmazonSku> amazonSkuList = JSON.parseArray(amazonTemplateBO.getVariations(), AmazonSku.class);
                amazonSkuList = amazonSkuList.stream()
                        .filter(amazonSku -> (null == amazonSku.getRepeatFlag() || BooleanUtils.isFalse(amazonSku.getRepeatFlag())))
                        .collect(Collectors.toList());

                if (size <= amazonSkuList.size()) {
                    List<AmazonProcessReport> amazonProcessReportList = publishProductCreateByMap.get(amazonTemplateBO.getId());
                    List<String> sellerSkuList = amazonProcessReportList.stream().map(AmazonProcessReport::getDataValue).collect(Collectors.toList());
                    amazonSkuList = amazonSkuList.stream().filter(o -> sellerSkuList.contains(o.getSellerSKU())).collect(Collectors.toList());
                }
                amazonTemplateBO.setVariations(JSON.toJSONString(amazonSkuList));
                amazonTemplateBO.setAmazonSkus(amazonSkuList);
            }
            publishAllList.add(amazonTemplateBO);
        }

        //处理需要刊登价格、库存、图片、关系
        if (CollectionUtils.isNotEmpty(publishAllList)) {
            amazonCallService.publishTemplatePriceImageInventoryRelation(publishAllList);
        }
    }

    private void handleAmazonProcessReportNeedPublishData(Map<String, List<AmazonTemplateBO>> seller2TemplatesMap, Map<Integer, List<AmazonProcessReport>> feedTypePublishMap) {
        List<Long> amazonProcessReportIdList = new ArrayList<>();
        for (Entry<String, List<AmazonTemplateBO>> entry : seller2TemplatesMap.entrySet()) {
            //刊登对应模板生成新的处理报告,同一个账号
            List<AmazonTemplateBO> publishPriceList = new ArrayList<>();
            List<AmazonTemplateBO> publishImageList = new ArrayList<>();
            List<AmazonTemplateBO> publishInventoryList = new ArrayList<>();
            for (AmazonTemplateBO amazonTemplateBO : entry.getValue()) {
                if (feedTypePublishMap.containsKey(amazonTemplateBO.getId())) {
                    feedTypePublishMap.get(amazonTemplateBO.getId()).forEach(amazonProcessReport -> {
                        if (amazonProcessReport.getFeedType().equals(SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue())) {
                            amazonTemplateBO.setLastUpdatedBy(null == amazonProcessReport.getCreatedBy() ? null : amazonProcessReport.getCreatedBy());
                            publishInventoryList.add(amazonTemplateBO);
                            amazonProcessReportIdList.add(amazonProcessReport.getId());
                        } else if (amazonProcessReport.getFeedType().equals(SpFeedType.POST_PRODUCT_IMAGE_DATA.getValue())) {
                            amazonTemplateBO.setLastUpdatedBy(null == amazonProcessReport.getCreatedBy() ? null : amazonProcessReport.getCreatedBy());
                            publishImageList.add(amazonTemplateBO);
                            amazonProcessReportIdList.add(amazonProcessReport.getId());
                        } else if (amazonProcessReport.getFeedType().equals(SpFeedType.POST_PRODUCT_PRICING_DATA.getValue())) {
                            amazonTemplateBO.setLastUpdatedBy(null == amazonProcessReport.getCreatedBy() ? null : amazonProcessReport.getCreatedBy());
                            publishPriceList.add(amazonTemplateBO);
                            amazonProcessReportIdList.add(amazonProcessReport.getId());
                        }
                    });
                }
            }
            if (CollectionUtils.isNotEmpty(publishPriceList)) {
                amazonCallService.publishProductsPrice(entry.getKey(), publishPriceList);
            }
            if (CollectionUtils.isNotEmpty(publishInventoryList)) {
                amazonCallService.publishProductsInventory(entry.getKey(), publishInventoryList);
            }
            if (CollectionUtils.isNotEmpty(publishImageList)) {
                amazonCallService.publishProductsImage(entry.getKey(), publishImageList);
            }
        }
        // 批量更新处理报告备注
        amazonProcessReportService.batchUpdateProcessReportResultMsgByIdList(amazonProcessReportIdList);
    }


    @Override
    public void batchUpdateTemplatePublishStatus(List<Integer> idList, String publishSTempStatus) {
        if (CollectionUtils.isEmpty(idList) || StringUtils.isEmpty(publishSTempStatus)) {
            return;
        }
        Integer publishStatus = AmaoznPublishStatusEnum.WAIT_PUBLISH.getStatusCode();
        if ("none".equals(publishSTempStatus)) {
            //待刊登
            publishStatus = AmaoznPublishStatusEnum.WAIT_PUBLISH.getStatusCode();
        } else if ("true".equals(publishSTempStatus)) {
            publishStatus = AmaoznPublishStatusEnum.PUBLISH_SUCCESS.getStatusCode();
        } else if ("false".equals(publishSTempStatus)) {
            publishStatus = AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode();
        } else if ("publishing".equals(publishSTempStatus)) {
            //刊登中
            publishStatus = AmaoznPublishStatusEnum.PUBLISHING.getStatusCode();
        }
        this.amazonTemplateMapper.batchUpdateTemplatePublishStatus(idList, publishStatus, AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
    }

    @Override
    public AmazonTemplateBO handleAmazontemplateDataByEsBase(List<EsAmazonBaseListing> esAmazonBaseListingList, String mainSku) {
        esAmazonBaseListingList = esAmazonBaseListingList.stream().sorted(
                Comparator.comparing(EsAmazonBaseListing::getCrawlTime).reversed()).collect(Collectors.toList());
        AmazonTemplateBO amazonTemplate = new AmazonTemplateBO();
        //范本
        amazonTemplate.setPublishType(PublishTypeEnum.COMMON_PUBLISH.getCode());
        amazonTemplate.setSkuDataSource(SkuDataSourceEnum.ERP_DATA_SYSTEM.getCode());
        amazonTemplate.setIsLock(true);
        amazonTemplate.setParentSku(mainSku);
        if (esAmazonBaseListingList.size() == 1 && esAmazonBaseListingList.get(0).getParentAsin().equals(esAmazonBaseListingList.get(0).getAsin())) {
            //单体
            amazonTemplate.setSaleVariant(false);
        } else {
            amazonTemplate.setSaleVariant(true);
        }
        EsAmazonAsinIncrementInfo esAmazonAsinIncrementInfo = esAmazonBaseListingList.get(0).getAmazonAsinIncrementInfos().stream().sorted(
                Comparator.comparing(EsAmazonAsinIncrementInfo::getCrawlTime).reversed()).collect(Collectors.toList()).get(0);
        amazonTemplate.setTitle(esAmazonAsinIncrementInfo.getName());
        amazonTemplate.setDescription(esAmazonAsinIncrementInfo.getDescription());
        // 获取试卖列表的值
        amazonTemplate.setTitle(esAmazonAsinIncrementInfo.getName());
        amazonTemplate.setDescription(esAmazonAsinIncrementInfo.getDescription());
        String bullPoint = this.handleBullPointData(esAmazonAsinIncrementInfo);
        amazonTemplate.setBulletPoint(bullPoint);
        Double shippingWeight = null;
        if (null != esAmazonAsinIncrementInfo.getWeight()
                && StringUtils.isNotEmpty(esAmazonAsinIncrementInfo.getWeight().getShippingWeight())) {
            try {
                shippingWeight = Double.valueOf(esAmazonAsinIncrementInfo.getWeight().getShippingWeight().replace(",", ".").split(" ", 1)[0]);
            } catch (Exception e) {
                log.warn("取es重量转换出错" + e.getMessage());
            }
        }
        amazonTemplate.setShippingWeight(shippingWeight);
        if (amazonTemplate.getSaleVariant()) {
            Set<String> values = new HashSet<>();
            //变体
            List<AmazonSku> amazonSkus = new ArrayList<>();
            for (EsAmazonBaseListing esAmazonBaseListing : esAmazonBaseListingList) {
                EsAmazonAsinIncrementInfo amazonAsinIncrementInfo = esAmazonBaseListing.getAmazonAsinIncrementInfos().stream().sorted(
                        Comparator.comparing(EsAmazonAsinIncrementInfo::getCrawlTime).reversed()).collect(Collectors.toList()).get(0);
                EsSelectedVariations esSelectedVariations = amazonAsinIncrementInfo.getSelectedVariations();
                if (null == esSelectedVariations ||
                        (StringUtils.isEmpty(esSelectedVariations.getColor()) && StringUtils.isEmpty(esSelectedVariations.getSize()))) {
                    continue;
                }
                AmazonSku amazonSku = new AmazonSku();
                String sku = mainSku;
                //设置color\size
                String color = esSelectedVariations.getColor().replaceAll(" ", "").replaceAll("\"", "");
                String size = esSelectedVariations.getSize().replaceAll(" ", "").replaceAll("\"", "");
                List<NameValue> nameValueList = new ArrayList<>(2);
                if (StringUtils.isNotEmpty(color)) {
                    if (StrUtil.checkIsNum(color)) {
                        color = StrUtil.replacLettre(values);
                    }
                    values.add(color.toUpperCase());
                    NameValue nameValue = new NameValue();
                    nameValue.setDesc(color);
                    nameValue.setName("Color");
                    nameValue.setValue(color);
                    nameValueList.add(nameValue);
                    amazonSku.setName(color);
                    amazonTemplate.setVariationThemes("Color");
                    sku = sku + "-" + color;

                }
                if (StringUtils.isNotEmpty(size)) {
                    if (StrUtil.checkIsNum(size)) {
                        size = StrUtil.replacLettre(values);
                    }
                    values.add(size.toUpperCase());
                    NameValue nameValue = new NameValue();
                    nameValue.setDesc(size);
                    nameValue.setName("Size");
                    nameValue.setValue(size);
                    nameValueList.add(nameValue);
                    amazonSku.setName(size);
                    amazonTemplate.setVariationThemes("Size");
                    sku = sku + "-" + size;
                }
                if (StringUtils.isNotEmpty(color) && StringUtils.isNotEmpty(size)) {
                    amazonSku.setName(color + "-" + size);
                    amazonTemplate.setVariationThemes("Color-Size");
                }
                amazonSku.setNameValues(nameValueList);
                amazonSku.setCondition(AmazonConstant.DEFAULT_CONDITION_TYPE);
                //试卖SKU ES-1301 试卖sku 不能超过30个字符
                amazonSku.setSku((StringUtils.isNotBlank(sku) && sku.length() > 30) ? StringUtils.substring(sku, 0, 30) : sku);
                amazonSku.setStandardProdcutIdType(IdType.EAN);
                List<String> imageUrlsList = amazonAsinIncrementInfo.getImageUrls();
                if (CollectionUtils.isNotEmpty(imageUrlsList)) {
                    amazonSku.setMainImage(imageUrlsList.get(0));
                    amazonSku.setSampleImage(imageUrlsList.get(0));
                    amazonSku.setExtraImages(imageUrlsList.size() > 1 ? JSON.toJSONString(imageUrlsList.subList(1, imageUrlsList.size())) : null);
                    amazonSku.setExtraImagesList(imageUrlsList.size() > 1 ? imageUrlsList.subList(1, imageUrlsList.size()) : null);
                }
                Double salePrice = amazonAsinIncrementInfo.getPrice();
                amazonSku.setStandardPrice(salePrice);
                amazonSkus.add(amazonSku);
            }
            amazonTemplate.setVariations(JSON.toJSONString(amazonSkus));
        } else {
            amazonTemplate.setStandardProdcutIdType(IdType.EAN);
            List<String> imageUrlsList = esAmazonAsinIncrementInfo.getImageUrls();
            if (CollectionUtils.isNotEmpty(imageUrlsList)) {
                amazonTemplate.setMainImage(imageUrlsList.get(0));
                amazonTemplate.setSampleImage(imageUrlsList.get(0));
                amazonTemplate.setExtraImages(imageUrlsList.size() > 1 ? JSON.toJSONString(imageUrlsList.subList(1, imageUrlsList.size())) : null);
            }
            amazonTemplate.setCondition(AmazonConstant.DEFAULT_CONDITION_TYPE);
            Double salePrice = esAmazonAsinIncrementInfo.getPrice();
            amazonTemplate.setStandardPrice(salePrice);
        }
        return amazonTemplate;
    }

    private String handleBullPointData(EsAmazonAsinIncrementInfo esAmazonAsinIncrementInfo) {
        String bullPoint = null;
        EsFeature esFeature = esAmazonAsinIncrementInfo.getFeature();
        List<String> bullPoints = new ArrayList<>();
        if (StringUtils.isNotEmpty(esFeature.getFeature1())) {
            bullPoints.add(esFeature.getFeature1());
        }
        if (StringUtils.isNotEmpty(esFeature.getFeature2())) {
            bullPoints.add(esFeature.getFeature2());
        }
        if (StringUtils.isNotEmpty(esFeature.getFeature3())) {
            bullPoints.add(esFeature.getFeature3());
        }
        if (StringUtils.isNotEmpty(esFeature.getFeature4())) {
            bullPoints.add(esFeature.getFeature4());
        }
        if (StringUtils.isNotEmpty(esFeature.getFeature5())) {
            bullPoints.add(esFeature.getFeature5());
        }
        if (CollectionUtils.isNotEmpty(bullPoints)) {
            // 如果描述为100% brand new and high quality则过滤
            for (int i = 0; i < bullPoints.size(); i++) {
                String bulletPoint = bullPoints.get(i)
                        .replace(BulletPointFilterEnum.FIELD_WITH_FULL_STOP.getName(), "")
                        .replace(BulletPointFilterEnum.UPPERCASE_FIELD_WITH_FULL_STOP.getName(), "")
                        .replace(BulletPointFilterEnum.ORIGINAL_FIELD.getName(), "")
                        .replace(BulletPointFilterEnum.FIELD.getName(), "")
                        .replace(BulletPointFilterEnum.UPPERCASE_FIELD.getName(), "")
                        .replace(BulletPointFilterEnum.FIELD_WITH_FULL_STOP_NO_100.getName(), "")
                        .replace(BulletPointFilterEnum.UPPERCASE_FIELD_WITH_FULL_STOP_NO_100.getName(), "")
                        .replace(BulletPointFilterEnum.ORIGINAL_FIELD_NO_100.getName(), "")
                        .replace(BulletPointFilterEnum.FIELD_NO_100.getName(), "")
                        .replace(BulletPointFilterEnum.UPPERCASE_FIELD_NO_100.getName(), "");
                if (StringUtils.isBlank(bulletPoint)) {
                    bullPoints.remove(i);
                    break;
                }
                bullPoints.set(i, bulletPoint);
            }

            if (CollectionUtils.isNotEmpty(bullPoints)) {
                bullPoint = JSON.toJSONString(bullPoints);
            }
        }
        return bullPoint;
    }

    @Override
    public List<AmazonTemplateWithBLOBs> selectByExampleWithBLOBs(AmazonTemplateExample example) {
        if (null == example) {
            return null;
        }
        Assert.notNull(example.getTable(), "未设置具体table!");
        return this.amazonTemplateMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public void batchUpdateAmazontemplateVariations(List<AmazonTemplateWithBLOBs> amazonTemplateWithBLOBsList, String table) {
        if (CollectionUtils.isEmpty(amazonTemplateWithBLOBsList)) {
            return;
        }
        Assert.notNull(table, "未设置具体table!");
        this.amazonTemplateMapper.batchUpdateAmazontemplateVariations(amazonTemplateWithBLOBsList, table);
    }

    @Override
    public List<AmazonTemplateWithBLOBs> selectByExamplePatitalMsg(AmazonTemplateExample example) {
        if (null == example) {
            return null;
        }
        Assert.notNull(example.getTable(), "未设置具体table!");
        return amazonTemplateMapper.selectByExamplePatitalMsg(example);
    }

    @Override
    public void batchUpdateAmazonTemplateTitleDescImage(List<AmazonTemplateWithBLOBs> amazonTemplateWithBLOBsList, String table) {
        if (CollectionUtils.isEmpty(amazonTemplateWithBLOBsList)) {
            return;
        }
        Assert.notNull(table, "未设置具体table!");
        this.amazonTemplateMapper.batchUpdateAmazonTemplateTitleDescImage(amazonTemplateWithBLOBsList, table);
    }

    @Override
    public void batchUpdateCustom(List<AmazonTemplateBO> amazonTemplateBOList, String table) {
        if (CollectionUtils.isEmpty(amazonTemplateBOList)) {
            return;
        }
        Assert.notNull(table, "未设置具体table!");
        this.amazonTemplateMapper.batchUpdateCustom(amazonTemplateBOList, table);
    }

    @Override
    public void handleAmazonTemplateUpcExempt(AmazonTemplateBO template) {
        if (null == template.getBrowsePathById() || !template.getBrowsePathById().contains(",")) {
            return;
        }
        String sellerId = template.getSellerId();
        List<String> splitList = CommonUtils.splitList(template.getBrowsePathById(), ",");
        String browsePathById = "," + splitList.get(1) + ",";
        AmazonAccountRelationExample amazonAccountRelationExample = new AmazonAccountRelationExample();
        AmazonAccountRelationExample.Criteria criteria = amazonAccountRelationExample.createCriteria();
        criteria.andAccountNumberEqualTo(sellerId).andUpcExemptEnableEqualTo(true).andCategoryIdsLike(browsePathById);
        List<AmazonAccountRelation> amazonSellerSkuRuleList = amazonAccountRelationService.selectByExample(amazonAccountRelationExample);
        if (CollectionUtils.isEmpty(amazonSellerSkuRuleList)) {
            return;
        }
        template.setUpcExempt(true);

    }

    @Override
    public ApiResult<?> checkAccountPublish2GT(AmazonTemplate at) {
        //模板数据源不是冠通的不验证
        if (at.getSkuDataSource() != null && !SkuDataSourceEnum.GUAN_TONG_SYSTEM.getCode().equals(at.getSkuDataSource())) {
            return ApiResult.newSuccess();
        }
        /*
        1. 输入店铺，选择产品来源为冠通产品时，调用订单接口，判断店铺是否支持上冠通产品，若不支持，则提示，该店铺无法刊登冠通产品，请重新选择产品来源。
        输入店铺，输入SKU，判断SKU对应的发货区域是否与店铺满足，满足则可进行刊登。
        例如：店铺为DE站，SKU发货区域为US站，则该店铺不能刊登该SKU。
        若店铺为DE站，SKU发货区域为US,DE站，则店铺用DE站点的价格进行刊登。
        2. 输入SKU。
        判断该SKU，若，是否有商品权限：是，侵权状态：未侵权，是否上架：是。则允许刊登，否则，弹出对应的提示，商品无权限，或侵权状态未审核，或产品侵权，或产品不是上架状态，不允许刊登。
         */
        if (StringUtils.isBlank(at.getSellerId())) {
            return ApiResult.newError("店铺不能为空！");
        }
        if (StringUtils.isBlank(at.getParentSku())) {
            return ApiResult.newError("ParentSku不能为空！");
        }

        List<ProductInfo> productInfoList = ProductUtils.findProductInfos(Arrays.asList(at.getParentSku()));
        if (CollectionUtils.isEmpty(productInfoList)) {
            return ApiResult.newError("sku信息为空，无法验证：checkAccountPublish2GT！");
        }
        ProductInfo pd = productInfoList.get(0);
        GtProductDetail gtDetail = pd.getOther();
        if (gtDetail == null) {
//            return ApiResult.newError("sku获取到的冠通信息为空，无法验证：checkAccountPublish2GT！");
            return ApiResult.newSuccess();
        }

        if (!SingleSourceEnum.GUAN_TONG.getCode().equals(pd.getSingleSource())) {
            //不是冠通的不验证
            return ApiResult.newSuccess();
        }

        SaleAccountAndBusinessResponse account = null;
        int retryNum = 3;
        do {
            try {
                account = AccountUtils.getSaleAccountByAccountNumber(Platform.Amazon.name(), at.getSellerId());
                break;
            } catch (Exception e) {
                log.error("获取账号失败", e);
            }
        } while (--retryNum > 0);
        if (account == null) {
            return ApiResult.newError("无法获取到账号信息，无法验证：checkAccountPublish2GT！");
        }

        //验证账号
        if (!BooleanUtils.toBoolean(account.getDistributor())) {
            return ApiResult.newError("该店铺无法刊登冠通产品，请重新选择产品来源。");
        }

        //验证站点
        String marketplaceId = account.getMarketplaceId();
        List<AmazonMarketplace> marketplaceList = amazonConstantMarketHelper.getMarketplaceList();
        AmazonMarketplace marketplace = marketplaceList.stream()
                .filter(t -> t.getMarketplaceId().equals(marketplaceId))
                .findFirst()
                .orElseGet(() -> new AmazonMarketplace());
        if (marketplace == null || marketplace.getMarketplace() == null) {
            return ApiResult.newError("找不到账号所属站点！");
        }
        List<String> publishSiteList = gtDetail.getPublishSiteList();
        if (CollectionUtils.isEmpty(publishSiteList) || !publishSiteList.contains(marketplace.getMarketplace())) {
            return ApiResult.newError(String.format("店铺不能刊登该SKU，SKU可刊登站点:%s", JSON.toJSONString(publishSiteList)));
        }

        //验证商品、侵权、上架权限
        List<String> failList = new ArrayList<>(3);
        if (!BooleanUtils.toBoolean(gtDetail.getIsProductAuth())) {
            failList.add("商品无权限！");
        }
        if (!BooleanUtils.toBoolean(gtDetail.getPublished())) {
            failList.add("产品不是上架状态！");
        }
        List<GtTortDetail> tortDetails = gtDetail.getGtTortDetails();
        if (CollectionUtils.isNotEmpty(tortDetails)) {
            for (GtTortDetail gt : tortDetails) {
                //侵权状态：0-待审核,1-侵权,2-不侵权
                if (gt.getTortStatus() != null && gt.getTortStatus() != 2) {
                    if (gt.getTortStatus() == 0) {
                        failList.add("侵权状态未审核！");
                    } else if (gt.getTortStatus() == 1) {
                        failList.add("产品侵权！");
                    }
                }
            }
        }
        if (failList.size() > 0) {
            String msg = failList.stream().collect(Collectors.joining(","));
            return ApiResult.newError(msg + "不能刊登！");
        }


        return ApiResult.newSuccess();
    }


    @Override
    public List<AmazonTemplateBO> selectAmazonTemplateBOsByExampleWithBLOBs(AmazonTemplateExample example) {
        if (null == example) {
            return null;
        }
        Assert.notNull(example.getTable(), "未设置具体table!");
        return this.amazonTemplateMapper.selectAmazonTemplateBOsByExampleWithBLOBs(example);
    }

    @Override
    public Long countByExample(AmazonTemplateExample templateExample) {
        Assert.notNull(templateExample, "example is null!");
        Assert.notNull(templateExample.getTable(), "example is null!");
        return amazonTemplateMapper.countByExample(templateExample);
    }

    @Override
    public List<AmazonTemplateBO> selectFiledColumnsByExample(AmazonTemplateExample example) {
        Assert.notNull(example, "example is null!");
        Assert.notNull(example.getTable(), "未设置具体table!");
        return amazonTemplateMapper.selectFiledColumnsByExample(example);
    }

    @Override
    public ApiResult<String> updateEan(List<AmazonTemplateWithBLOBs> amazonTemplateWithBLOBs) {
        List<String> errorMsgList = new ArrayList<>();
        //无论之前是UPC还是EAN 统一更新成EAN 类型
        for (AmazonTemplateWithBLOBs amazonTemplateWithBLOB : amazonTemplateWithBLOBs) {
            try {
                updateEAN(amazonTemplateWithBLOB);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                errorMsgList.add(String.format("模板编号:%s 修改失败:%s", amazonTemplateWithBLOB.getId(), e.getMessage()));
            }
        }
        return CollectionUtils.isNotEmpty(errorMsgList) ? ApiResult.newError(org.apache.commons.lang.StringUtils.join(errorMsgList, "<br>")) : ApiResult.newSuccess();
    }

    @Override
    public void updateTemplateReportSolution(Integer templateId, Integer solutionId, String solutionType, Integer publishStatus) {
        amazonTemplateMapper.updateTemplateReportSolution(templateId, solutionId, solutionType, publishStatus);
    }

    @Override
    public Map<String, String> generateSellerSKUWithSku(AmazonTemplateBasisRequest request) {
        List<String> sonSkus = request.getSonSkus();
        Map<String, String> sellerSkuMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(sonSkus)) {
            return sellerSkuMap;
        }

        // 2020/11/16 设置冠通sellerSku 前缀
        String sellerSkuPrefix = "";
        if (SkuDataSourceEnum.GUAN_TONG_SYSTEM.getCode().equals(request.getSkuDataSource())) {
            //SKU生成规则在加完前后缀之后，在最前面加上 GT-
            sellerSkuPrefix = "GT-";
        }

        String accountNumber = request.getAccountNumber();
        String userName = WebUtils.getUserName();

        AmazonSellerSkuRule sellerSkuRule = SellerSkuRuleUtils.getAmazonSellerSku(accountNumber, userName);
        if (sellerSkuRule == null) {
            throw new BusinessException("sku生成规则为空");
        }
        for (String sku : sonSkus) {
            String sellerSku = SellerSkuRuleUtils.generate(sku, sellerSkuRule);
            if (StringUtils.isNotBlank(sellerSkuPrefix)) {
                sellerSku = sellerSkuPrefix + sellerSku;
            }
            sellerSkuMap.put(sku, sellerSku);
        }
        return sellerSkuMap;
    }

    /**
     * 批量刊登模板数据校验
     *
     * @param templateIds
     * @return
     */
    @Override
    public ApiResult<List<AmazonTemplateValidationDO>> batchValidationTemplateData(List<Integer> templateIds) {
        List<AmazonTemplateValidationDO> validationList = new ArrayList<>();
        // 校验过滤刊登中，刊登成功的模板Id
        List<AmazonTemplateValidationDO> validationDOS = validationTemplateStatus(templateIds);
        if (CollectionUtils.isNotEmpty(validationDOS)) {
            validationList.addAll(validationDOS);
        }


        // 重复刊登 内容不完整
        List<AmazonTemplateValidationDO> repeatTemplate = validationRepeatTemplate(templateIds);
        if (CollectionUtils.isNotEmpty(repeatTemplate)) {
            validationList.addAll(repeatTemplate);
        }

        // 品牌侵权词校验
        List<AmazonTemplateValidationDO> infringingBrandWordTemplate = validationInfringingBrandWordTemplate(templateIds);
        if (CollectionUtils.isNotEmpty(infringingBrandWordTemplate)) {
            validationList.addAll(infringingBrandWordTemplate);
        }

        // 刊登次数拦截
        List<AmazonTemplateValidationDO> publishNumbers = validationTemplatePublishNumber(templateIds);
        if (CollectionUtils.isNotEmpty(publishNumbers)) {
            validationList.addAll(publishNumbers);
        }
        return ApiResult.newSuccess(validationList);
    }

    private List<AmazonTemplateValidationDO> validationTemplatePublishNumber(List<Integer> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyList();
        }
        AmazonTemplateExample example = new AmazonTemplateExample();
        example.createCriteria()
                .andIdIn(templateIds)
                .andIsLockEqualTo(false);
        example.setColumns("id,parent_SKU,country,sku_data_source,sale_variant,seller_id,variations,system_category_code_path");

        List<AmazonTemplateBO> amazonTemplateBOS = amazonTemplateMapper.selectFiledColumnsByExample(example);
        if (CollectionUtils.isEmpty(amazonTemplateBOS)) {
            return Collections.emptyList();
        }
        List<Integer> failTemplateIds = new ArrayList<>();
        List<AmazonTemplateValidationDO> validationList = new ArrayList<>();
        amazonTemplateBOS.forEach(template -> {
            ApiResult<String> categoryPublishNumberResult = amazonValidationHelper.validationCategoryPublishNumber(template);
            if (categoryPublishNumberResult.isSuccess()) {
                return;
            }
            failTemplateIds.add(template.getId());
        });


        if (CollectionUtils.isNotEmpty(failTemplateIds)) {
            AmazonTemplateValidationDO validationDO = new AmazonTemplateValidationDO();
            validationDO.setErrorMsg("刊登分类次数不足");
            validationDO.setData(failTemplateIds);
            validationDO.setCode(AmazonTemplateValidationEnum.INSUFFICIENT_PUBLISH_CATEGORY.getCode());
            validationList.add(validationDO);
            templateIds.removeIf(failTemplateIds::contains);
        }
        return validationList;
    }

    /**
     * 更新模板状态，并且记录失败处理报告
     *
     * @param templateIds 模板Id
     * @param errorMsg    错误信息
     */
    @Override
    public void updateTemplateAndRecordFailReport(List<Integer> templateIds, String errorMsg) {
        AmazonTemplateExample example = new AmazonTemplateExample();
        example.createCriteria()
                .andIdIn(templateIds)
                .andIsLockEqualTo(false);
        example.setColumns("id,parent_SKU,country,seller_sku,seller_id,created_by");
        List<AmazonTemplateBO> amazonTemplateBOS = amazonTemplateMapper.selectFiledColumnsByExample(example);
        for (AmazonTemplateBO template : amazonTemplateBOS) {
            AmazonProcessReport report = new AmazonProcessReport();
            report.setFeedType(SpFeedType.POST_PRODUCT_DATA.getValue());
            report.setCreationDate(new Date());
            report.setAccountNumber(template.getSellerId());
            report.setStatusCode(ProcessingReportStatusCode.Complete.name());
            report.setDataType(AmazonConstant.PROCESS_REPORT_TYPE_SKU);
            report.setDataValue(template.getSellerSKU());
            report.setRelationId(template.getId());
            report.setRelationType(ProcessingReportTriggleType.Template.name());
            report.setCreatedBy(template.getCreatedBy());
            report.setStatus(false);
            report.setResultMsg(errorMsg);
            report.setFinishDate(new Timestamp(System.currentTimeMillis()));
            amazonProcessReportService.insert(report);
        }
        amazonTemplateMapper.batchUpdateTemplatePublishStatus(templateIds, AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode(), AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
    }

    private List<AmazonTemplateValidationDO> validationInfringingBrandWordTemplate(List<Integer> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyList();
        }
        AmazonTemplateExample example = new AmazonTemplateExample();
        example.createCriteria()
                .andIdIn(templateIds)
                .andBrandIsNotNull()
                .andIsLockEqualTo(false);
        example.setColumns("id,brand,country,seller_id");
        List<AmazonTemplateBO> amazonTemplateBOS = amazonTemplateMapper.selectFiledColumnsByExample(example);
        if (CollectionUtils.isEmpty(amazonTemplateBOS)) {
            return Collections.emptyList();
        }

        // 根据店铺分组 收集品牌词异步调用侵权词校验服务
        List<CompletableFuture<List<AmazonTemplateValidationDO>>> futureList = new ArrayList<>();
        Map<String, List<AmazonTemplateBO>> accountTemplateMap = amazonTemplateBOS.stream().collect(Collectors.groupingBy(AmazonTemplate::getSellerId));
        accountTemplateMap.forEach((account, templateList) -> {
            // 异步调用
            CompletableFuture<List<AmazonTemplateValidationDO>> listCompletableFuture = CompletableFuture.supplyAsync(() -> {
                List<String> brands = templateList.stream()
                        .map(AmazonTemplate::getBrand)
                        .filter(StringUtils::isNotBlank)
                        .distinct()
                        .collect(Collectors.toList());

                String site = templateList.get(0).getCountry();
                // 品牌词去重 合并
                String brandStr = StringUtils.join(brands, " ");
                AmazonTemplateBO amazonTemplateBO = new AmazonTemplateBO();
                amazonTemplateBO.setSellerId(account);
                amazonTemplateBO.setBrand(brandStr);
                amazonTemplateBO.setCountry(site);
                ApiResult<String> apiResult = templateDataValidationExecutor.checkBrandInfringementWord(amazonTemplateBO);
                if (apiResult.isSuccess()) {
                    return null;
                }

                String result = apiResult.getResult();
                // 解析返回结果，如果存在侵权词则遍历模板与侵权词忽略大小写匹配 若匹配上则添加响应结果
                InfringementErrorMsgBo infringementErrorMsgBo = JSON.parseObject(result, InfringementErrorMsgBo.class);
                if (infringementErrorMsgBo != null && StringUtils.isNotBlank(infringementErrorMsgBo.getBrandInfringement())) {
                    String[] infringementBrandArray = infringementErrorMsgBo.getBrandInfringement().split(",");
                    return templateList.stream().map(template -> {
                        String brand = template.getBrand();
                        String infringementBrandTag = Arrays.stream(infringementBrandArray)
                                .filter(infringementBrand -> StringUtils.containsIgnoreCase(brand, infringementBrand))
                                .findFirst().orElseGet(() -> null);
                        if (infringementBrandTag == null) {
                            return null;
                        }
                        AmazonTemplateValidationDO validationDO = new AmazonTemplateValidationDO();
                        validationDO.setCode(AmazonTemplateValidationEnum.INCLUDE_INFRINGING_WORD.getCode());
                        validationDO.setData(List.of(template.getId()));
                        validationDO.setErrorMsg(String.format("品牌存在侵权词%s，需修改品牌包含的侵权词后刊登。", infringementBrandTag));
                        return validationDO;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
                }
                return null;
            }, AmazonExecutors.CHECK_INFRINGEMENTWORD_POOL);
            futureList.add(listCompletableFuture);
        });
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        // 处理每个异步任务的结果，并将结果收集到一个新的列表中
        CompletableFuture<List<AmazonTemplateValidationDO>> allResults = allFutures.thenApply(v -> {
            List<AmazonTemplateValidationDO> results = new ArrayList<>();
            for (CompletableFuture<List<AmazonTemplateValidationDO>> future : futureList) {
                try {
                    List<AmazonTemplateValidationDO> result = future.get();
                    if (CollectionUtils.isNotEmpty(result)) {
                        results.addAll(result);
                    }
                } catch (InterruptedException | ExecutionException e) {
                    // 处理异常情况
                    log.error("批量校验模板品牌异常:", e);
                }
            }
            return results;
        });
        // 等待所有异步任务完成，并获取最终的结果列表
        return allResults.join();
    }

    private List<AmazonTemplateValidationDO> validationRepeatTemplate(List<Integer> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyList();
        }
        AmazonTemplateExample example = new AmazonTemplateExample();
        example.createCriteria()
                .andIdIn(templateIds)
                .andIsLockEqualTo(false);
        example.setColumns("id,parent_SKU,country,sale_variant,seller_id,variations");

        List<AmazonTemplateBO> amazonTemplateBOS = amazonTemplateMapper.selectFiledColumnsByExample(example);
        if (CollectionUtils.isEmpty(amazonTemplateBOS)) {
            return Collections.emptyList();
        }
        List<AmazonTemplateValidationDO> validationList = new ArrayList<>();
        List<Integer> badAsinTemplate = new ArrayList<>();
        List<Integer> repeatSkuTemplate = new ArrayList<>();

        amazonTemplateBOS.forEach(template -> {
            String accountNumber = template.getSellerId();
            List<String> articleNumbers = AmazonTemplateUtils.getAllSku(template);
            String site = template.getCountry();
            try {
                List<String> repeatSku = amazonTemplateRepeatPublishHelper.checkArticleNumberIsPublish(accountNumber, articleNumbers, template.getId(), site);
                if (CollectionUtils.isNotEmpty(repeatSku)) {
                    repeatSkuTemplate.add(template.getId());
                }
            } catch (Exception e) {
                if (e instanceof BusinessException) {
                    badAsinTemplate.add(template.getId());
                } else {
                    throw new RuntimeException(e);
                }
            }
        });

        if (CollectionUtils.isNotEmpty(badAsinTemplate)) {
            AmazonTemplateValidationDO validationDO = new AmazonTemplateValidationDO();
            validationDO.setCode(AmazonTemplateValidationEnum.NOT_SALE_OR_BAD_ASIN.getCode());
            validationDO.setData(badAsinTemplate);
            validationDO.setErrorMsg("重复刊登,存在在线asin状态为内容不完整.会下架内容不完整的listing后再进行刊登");
            // 添加到校验信息中，并从源数据中移除
            validationList.add(validationDO);
            templateIds.removeIf(badAsinTemplate::contains);
        }

        if (CollectionUtils.isNotEmpty(repeatSkuTemplate)) {
            AmazonTemplateValidationDO validationDO = new AmazonTemplateValidationDO();
            validationDO.setCode(AmazonTemplateValidationEnum.REPEAT_PUBLISH.getCode());
            validationDO.setData(repeatSkuTemplate);
            validationDO.setErrorMsg("是存在当天刊登成功的模板或在线的asin重复产品会过滤不刊登");
            // 添加到校验信息中，并从源数据中移除
            validationList.add(validationDO);
            templateIds.removeIf(repeatSkuTemplate::contains);
        }
        return validationList;
    }

    /**
     * 校验模板状态，过滤刊登中，刊登成功的
     *
     * @param templateIds
     * @return
     */
    private List<AmazonTemplateValidationDO> validationTemplateStatus(List<Integer> templateIds) {
        AmazonTemplateExample example = new AmazonTemplateExample();
        example.createCriteria()
                .andIdIn(templateIds)
                .andPublishStatusIn(List.of(AmaoznPublishStatusEnum.PUBLISHING.getStatusCode(), AmaoznPublishStatusEnum.PUBLISH_SUCCESS.getStatusCode()))
                .andIsLockEqualTo(false);
        example.setColumns("id,publish_status");

        List<AmazonTemplateBO> amazonTemplateBOS = amazonTemplateMapper.selectFiledColumnsByExample(example);
        if (CollectionUtils.isEmpty(amazonTemplateBOS)) {
            return Collections.emptyList();
        }

        List<AmazonTemplateValidationDO> validationList = new ArrayList<>();

        List<Integer> publishIngIds = new ArrayList<>();
        List<Integer> publishSuccessIds = new ArrayList<>();
        amazonTemplateBOS.forEach(template -> {
            if (AmaoznPublishStatusEnum.PUBLISHING.isTrue(template.getPublishStatus())) {
                publishIngIds.add(template.getId());
                return;
            }
            if (AmaoznPublishStatusEnum.PUBLISH_SUCCESS.isTrue(template.getPublishStatus())) {
                publishSuccessIds.add(template.getId());
            }
        });

        if (CollectionUtils.isNotEmpty(publishIngIds)) {
            AmazonTemplateValidationDO validationDO = new AmazonTemplateValidationDO();
            validationDO.setCode(AmazonTemplateValidationEnum.BAD_TEMPLATE_STATUS.getCode());
            validationDO.setData(publishIngIds);
            validationDO.setErrorMsg("刊登中的数据会过滤不刊登");
            // 添加到校验信息中，并从源数据中移除
            validationList.add(validationDO);
            templateIds.removeIf(publishIngIds::contains);
        }

        if (CollectionUtils.isNotEmpty(publishSuccessIds)) {
            AmazonTemplateValidationDO validationDO = new AmazonTemplateValidationDO();
            validationDO.setCode(AmazonTemplateValidationEnum.BAD_TEMPLATE_STATUS.getCode());
            validationDO.setData(publishSuccessIds);
            validationDO.setErrorMsg("刊登成功的数据会过滤不刊登");
            // 添加到校验信息中，并从源数据中移除
            validationList.add(validationDO);
            templateIds.removeIf(publishSuccessIds::contains);
        }
        return validationList;
    }


    @Override
    public void updateEAN(AmazonTemplateWithBLOBs amazonTemplateWithBLOB) {
        AmazonTemplateWithBLOBs updateAmazonTemplateWithBLOBs = new AmazonTemplateBO();
        updateAmazonTemplateWithBLOBs.setId(amazonTemplateWithBLOB.getId());
        //账号
        AmazonAccount amazonAccount = amazonAccountService.queryAmazonAccountByAccountNumber(amazonTemplateWithBLOB.getSellerId());

        Boolean saleVariant = amazonTemplateWithBLOB.getSaleVariant();
        String prefixStr = null;
        if (null != amazonAccount && StringUtils.isNotEmpty(amazonAccount.getEanPrefix())) {
            prefixStr = amazonAccount.getEanPrefix();
        }
        //变体
        if (saleVariant != null && saleVariant) {
            String variations = amazonTemplateWithBLOB.getVariations();
            List<AmazonSku> amazonSkus = JSON.parseObject(variations, new TypeReference<List<AmazonSku>>() {
            });
            List<String> strings = CardCodeUtils.generateCardCodes(CardCodeType.EAN, amazonSkus.size(), prefixStr, amazonAccount.getAccountNumber(), "updateEAN");

            for (int i = 0; i < amazonSkus.size(); i++) {
                AmazonSku amazonSku = amazonSkus.get(i);
                amazonSku.setStandardProdcutIdType("EAN");
                amazonSku.setStandardProdcutIdValue(strings.get(i));
            }
            amazonTemplateWithBLOB.setVariations(JSON.toJSONString(amazonSkus));
            updateAmazonTemplateWithBLOBs.setVariations(JSON.toJSONString(amazonSkus));

        } else {
            List<String> strings = CardCodeUtils.generateCardCodes(CardCodeType.EAN, 1, prefixStr, amazonAccount.getAccountNumber(), "updateEAN");
            updateAmazonTemplateWithBLOBs.setStandardProdcutIdType("EAN");
            updateAmazonTemplateWithBLOBs.setStandardProdcutIdValue(strings.get(0));
            amazonTemplateWithBLOB.setStandardProdcutIdType("EAN");
            amazonTemplateWithBLOB.setStandardProdcutIdValue(strings.get(0));
        }
        if (amazonTemplateWithBLOB.getStatus() != null) {
            updateAmazonTemplateWithBLOBs.setStatus(amazonTemplateWithBLOB.getStatus());
        }
        //修改
        updateAmazonTemplateWithBLOBs.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        update(updateAmazonTemplateWithBLOBs);
    }

    @Override
    public void createTimingQueue(Map<String, SkuListAndCode> spuToCodeMap, Map<String, SalesmanAccountDetail> sellerToUserListMap, List<AmazonAccountRelation> amazonAccountRelationList, int publishRole,Integer interfaceType) {
        if (MapUtils.isEmpty(spuToCodeMap)) {
            return;
        }
        for (AmazonAccountRelation amazonAccountRelation : amazonAccountRelationList) {
            AmazonExecutors.executeAmazonAutoQueuePublish(() -> {
                String account = amazonAccountRelation.getAccountNumber();
                String createBy = "";
                try {
                    SalesmanAccountDetail salesmanAccountDetail = sellerToUserListMap.get(account);
                    if (ObjectUtils.isNotEmpty(salesmanAccountDetail) && CollectionUtils.isNotEmpty(sellerToUserListMap.get(account).getSalesmanSet())) {
                        for (String sale : sellerToUserListMap.get(account).getSalesmanSet()) {
                            if (StringUtils.isBlank(sale)) {
                                continue;
                            }
                            String[] split = sale.split("-");
                            createBy = split[0];
                            if (StringUtils.isNotBlank(createBy)) {
                                break;
                            }
                        }
                    }
                    XxlJobLogger.log("执行店铺自动刊登，account:{}, spuSize:{}", account, spuToCodeMap.size());
                    ResponseJson resp = execPublish(createBy, amazonAccountRelation, spuToCodeMap, publishRole, interfaceType);
                    if (!resp.isSuccess()) {
                        // 生成一条处理报告提示
                        generateReport(account, resp.getMessage(), createBy);
                    }
                } catch (Exception e) {
                    // 生成一条处理报告提示
                    generateReport(account, e.getMessage(), createBy);
                    log.error(String.format("店铺%s 自动刊登报错", account), e);
                }
            });
        }
    }

    @Override
    public ResponseJson execPublish(String createBy, AmazonAccountRelation amazonAccountRelation, Map<String, SkuListAndCode> spuToCodeMap, int publishRole, Integer interfaceType) {
        if (StringUtils.isBlank(createBy)) {
            createBy = "admin";
        }
        String accountNumber = amazonAccountRelation.getAccountNumber();
        ResponseJson resp = new ResponseJson();
        resp.setStatus(StatusCode.FAIL);

        XxlJobLogger.log(String.format("自动刊登账号%s", accountNumber));

        // 产品分类
        String prodCategoryIds = amazonAccountRelation.getProdCategoryCodes();
        // 每天最大刊登数量
        Integer maxPublishNum = amazonAccountRelation.getPublishQuantity();
        // 自动刊登间隔时间（分钟）
        Integer publishIntervalTime = amazonAccountRelation.getPublishIntervalTime();
        // 每分钟刊登SPU数量
        Optional<Integer> minPublishMount = Optional.ofNullable(amazonAccountRelation.getMinPublishMount());
        // 每天首次刊登时间
        Time time = amazonAccountRelation.getPublishTime();

        if (StringUtils.isBlank(prodCategoryIds) || null == maxPublishNum || null == time || (minPublishMount.isEmpty() && null == publishIntervalTime)) {
            log.info(String.format("账号%s 配置参数为空!", accountNumber));
            resp.setMessage(String.format("账号%s 配置参数为空!", accountNumber));

            /*AmazonAutoPublishLog amazonAutoPublishLog = new AmazonAutoPublishLog();
            amazonAutoPublishLog.setNoRecordSpuMount(spuToCodeMap.size());
            handleAmazonAutoPublishLog(amazonAutoPublishLog,accountNumber,amazonAccountRelation.getAccountCountry(),AmazonAutoPublishLogReasonEnum.ACCOUNT_MISSING_AUTO_MSG.getCode());*/
            return resp;
        }

        //筛选 可刊登的spu
        Set<String> spuSet = new HashSet<>(amazonAccountRelation.getPublishQuantity());
        findSpus(spuSet, amazonAccountRelation, spuToCodeMap, publishRole);
        if (spuSet.isEmpty()) {
            resp.setMessage(String.format("自动刊登账号%s 没有合适的产品刊登", accountNumber));
            log.error(String.format("自动刊登账号%s 没有合适的产品刊登", accountNumber));
            return resp;
        }
        XxlJobLogger.log(String.format("自动刊登账号%s 合适的产品刊登spuSize:%s", accountNumber, spuSet.size()));
        Calendar calendarTime = Calendar.getInstance();
        calendarTime.setTime(time);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, calendarTime.get(Calendar.HOUR_OF_DAY));
        calendar.set(Calendar.MINUTE, calendarTime.get(Calendar.MINUTE));
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Timestamp publishTime = new Timestamp(calendar.getTimeInMillis());

        //设置当前用户
        DataContextHolder.setUsername(createBy);

        List<TemplateQueue> templateQueueList = new ArrayList<>();
        List<String> spuList = new ArrayList<>(spuSet);
        for (int i = 0; i < spuList.size(); i++) {
            TemplateQueue queue = new TemplateQueue();
            templateQueueList.add(queue);

            queue.setSellerId(accountNumber);
            queue.setSku(spuList.get(i));
            queue.setStatus(QueueStatus.WAITING.getCode());
            queue.setSaleChannel(SaleChannel.CHANNEL_AMAZON);
            queue.setTimingType(TemplateQueueTypeEnum.SPU.intCode());
            //第一个是选择的时间 i = 0;
            //  根据刊登角色确定
            if (publishRole == PublishRoleEnum.ADMIN.getPublishRole() && minPublishMount.isPresent()) {
                int result = (int) Math.floor((double) i / minPublishMount.get());
                queue.setStartTime(new Timestamp(publishTime.getTime() + 60L * 1000 * result));
            } else {
                queue.setStartTime(new Timestamp(publishTime.getTime() + publishIntervalTime.longValue() * 60 * 1000 * i));
            }
            queue.setCreationDate(new Timestamp(System.currentTimeMillis()));
            queue.setCreatedBy(createBy);
            queue.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            queue.setLastUpdatedBy(createBy);
            queue.setTempType(interfaceType);
            queue.setPublishRole(publishRole);
            queue.setExtendedField1(String.valueOf(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode()));
            queue.setExtendedField3(String.valueOf(PublishTypeEnum.AUTO_PUBLISH.getCode()));
        }
        templateQueueMapper.batchInsert(templateQueueList);
        resp.setStatus(StatusCode.SUCCESS);
        return resp;
    }

    public void findSpus(Set<String> spuSet, AmazonAccountRelation amazonAccountRelation, Map<String, SkuListAndCode> spuToCodeMap, Integer publishRole) {
        String accountNumber = amazonAccountRelation.getAccountNumber();
        List<String> codes = CommonUtils.splitList(amazonAccountRelation.getProdCategoryCodes(), ",");
        String country = amazonAccountRelation.getAccountCountry();
        Integer maxPublishNum = amazonAccountRelation.getPublishQuantity();
        List<String> spuList = new ArrayList<>(spuToCodeMap.keySet());
        Collections.shuffle(spuList);
        for (String spu : spuList) {
            try {
                if (spuSet.size() == maxPublishNum.intValue()) {
                    //找到合适
                    break;
                }
                SkuListAndCode skuListAndCode = spuToCodeMap.get(spu);
                List<String> sonSkuList = skuListAndCode.getSkuList();
                String code = skuListAndCode.getCode();
                List<SonSkuFewInfo> sonSkuFewInfos = skuListAndCode.getSonSkuFewInfos();

                if (!codes.contains(code)) {
                    log.debug(String.format("自动刊登账号%s spu%s 类目不匹配%s", accountNumber, spu, code));
                    continue;
                }

//                // 查询admin范本
//                AmazonTemplateAutoExample amazonTemplateAutoExample = new AmazonTemplateAutoExample();
//                amazonTemplateAutoExample.createCriteria()
//                        .andParentSkuEqualTo(spu)
//                        .andCountryEqualTo(country)
//                        .andIsLockEqualTo(true)
//                        .andCategoryIdIsNotNull()
//                        .andProductTypeIsNotNull()
//                        .andTemplateStatusEqualTo(1)
//                ;
//                List<AmazonTemplateAuto> amazonTemplateAutos = amazonTemplateAutoService.selectByExample(amazonTemplateAutoExample);
//                if (CollectionUtils.isEmpty(amazonTemplateAutos)) {
//                    //XxlJobLogger.log(String.format("自动刊登账号%s spu%s 在站点&s没有范本", accountNumber, spu, country));
//                    //log.warn(String.format("自动刊登账号%s spu%s 在站点&s没有范本", accountNumber, spu, country));
//                    continue;
//                }
                //  拦截 listing 已刊登过得spu,不生成定时队列
                if (PublishRoleEnum.ADMIN.getPublishRole() == publishRole) {
                    Boolean isExistSpuListing = amazonAutoPublishHelper.checkIsSpuHaveListing(accountNumber, spu);
                    if (isExistSpuListing) {
                        //XxlJobLogger.log(String.format("自动刊登账号%s spu%s 存在Listing", accountNumber, spu));
                        log.debug(String.format("自动刊登账号%s spu%s 存在Listing", accountNumber, spu));
                        continue;
                    }
                }

                //过滤已经存在产品
                boolean exist = amazonAutoPublishHelper.checkIsSkuHavePublished(accountNumber, spu, sonSkuList);
                if (exist) {
                    //XxlJobLogger.log(String.format("自动刊登账号%s spu%s 已经存在产品", accountNumber, spu));
                    log.debug(String.format("自动刊登账号%s spu%s 已经存在产品", accountNumber, spu));
                    continue;
                }

                // 系统刊登 过滤不满足店铺配置限制的spu
                if (PublishRoleEnum.ADMIN.getPublishRole() == publishRole) {
                    Boolean isFilterSpu = checkAccountRelationRestrict(amazonAccountRelation, sonSkuFewInfos);
                    if (isFilterSpu) {
//                        XxlJobLogger.log(String.format("自动刊登账号%s spu%s 不满足店铺配置限制条件", accountNumber, spu));
                        log.debug(String.format("自动刊登账号%s spu%s 不满足店铺配置限制条件,%s", accountNumber, spu, JSON.toJSONString(sonSkuFewInfos)));
                        continue;
                    }
                }

                // 校验特殊标签是否全部是客户定制
                if (CollectionUtils.isNotEmpty(sonSkuFewInfos)) {
                    List<SonSkuFewInfo> sonSkuFewInfoList = new ArrayList<>(sonSkuFewInfos);
                    List<SonSkuFewInfo> specialTypeSkuList = new ArrayList<>();
                    for (SonSkuFewInfo sonSkuFewInfo : sonSkuFewInfoList) {
                        List<SpecialGoods> specialGoodsList = sonSkuFewInfo.getSpecialGoodsList();
                        if (CollectionUtils.isEmpty(specialGoodsList)) {
                            continue;
                        }
                        for (SpecialGoods specialGoods : specialGoodsList) {
                            Integer specialType = specialGoods.getSpecialType();
                            if (specialType != null && specialType == SpecialTagEnum.s_2001.code) {
                                specialTypeSkuList.add(sonSkuFewInfo);
                                break;
                            }
                        }
                    }
                    sonSkuFewInfoList.removeAll(specialTypeSkuList);
                    if (CollectionUtils.isEmpty(sonSkuFewInfoList)) {
                        //XxlJobLogger.log(String.format("账号%s spu%s 是客户定制产品", accountNumber, spu));
                        log.debug(String.format("账号%s spu%s 是客户定制产品", accountNumber, spu));
                        continue;
                    }
                }

                // 过滤禁售和停产 存档 废弃状态的产品
                List<String> skuList = AmazonProductStatusUtil.checkForbiddenAndItemStatus(skuListAndCode, country, accountNumber);
                if (CollectionUtils.isEmpty(skuList)) {
                    /*AmazonAutoPublishLog amazonAutoPublishLog = new AmazonAutoPublishLog();
                    amazonAutoPublishLog.setSpu(spu);
                    handleAmazonAutoPublishLog(amazonAutoPublishLog,accountNumber,country,AmazonAutoPublishLogReasonEnum.SPU_INFRINGEMENT_FORBIDDEN.getCode());*/
                    //XxlJobLogger.log(String.format("自动刊登账号%s spu%s 包含禁售平台或者spu停产存档废弃", accountNumber, spu));
                    log.debug(String.format("自动刊登账号%s spu%s 包含禁售平台或者spu停产存档废弃", accountNumber, spu));
                    continue;
                }


                //可以刊登
                spuSet.add(spu);
            } catch (Exception e) {
                XxlJobLogger.log(String.format("%s处理spu%s数据校验异常", accountNumber, spu), e.getMessage());
                log.error(String.format("%s处理spu%s数据校验异常", accountNumber, spu), e.getMessage());
            }
        }
    }

    /**
     * 检查账号关联限制
     * 当所有sonSkuFewInfos都不满足店铺配置时返回true
     *
     * @param amazonAccountRelation 亚马逊账号关联配置
     * @param sonSkuFewInfos        子SKU信息列表
     * @return true-所有SKU都不满足配置限制，false-至少有一个SKU满足配置限制
     */
    private Boolean checkAccountRelationRestrict(AmazonAccountRelation amazonAccountRelation, List<SonSkuFewInfo> sonSkuFewInfos) {
        if (CollectionUtils.isEmpty(sonSkuFewInfos)) {
            return false;
        }

        // 检查是否所有SKU都不满足店铺配置限制
        return sonSkuFewInfos.stream().noneMatch(sonSkuFewInfo ->
                satisfiesAccountRelationRestrict(amazonAccountRelation, sonSkuFewInfo));
    }

    /**
     * 检查单个SKU是否满足账号关联限制
     *
     * @param amazonAccountRelation 亚马逊账号关联配置
     * @param sonSkuFewInfo         子SKU信息
     * @return true-满足配置限制，false-不满足配置限制
     */
    private boolean satisfiesAccountRelationRestrict(AmazonAccountRelation amazonAccountRelation, SonSkuFewInfo sonSkuFewInfo) {
        // 检查排除标签限制
        if (!satisfiesExcludeLabelRestrict(amazonAccountRelation.getExcludeLabel(), sonSkuFewInfo)) {
            return false;
        }

        // 检查重量限制
        if (!satisfiesWeightRestrict(amazonAccountRelation.getFromWeight(), amazonAccountRelation.getToWeight(), sonSkuFewInfo)) {
            return false;
        }

        // 检查销售成本价限制
        if (!satisfiesPriceRestrict(amazonAccountRelation.getFromPrice(), amazonAccountRelation.getToPrice(), sonSkuFewInfo)) {
            return false;
        }

        // 检查销量限制
        if (!satisfiesSalesRestrict(amazonAccountRelation.getSalesType(), amazonAccountRelation.getFromSales(),
                amazonAccountRelation.getToSales(), sonSkuFewInfo)) {
            return false;
        }

        // 检查库存限制
        if (!satisfiesInventoryRestrict(amazonAccountRelation.getFromInventory(), amazonAccountRelation.getToInventory(), sonSkuFewInfo)) {
            return false;
        }

        // 检查产品录入时间限制
        Boolean firstAutoPublish = BooleanUtils.isTrue(amazonAccountRelation.getFirstAutoPublish());
        if (!satisfiesInputTimeRestrict(firstAutoPublish, amazonAccountRelation.getSkuCreateTimeType(),
                amazonAccountRelation.getFromInputTime(), amazonAccountRelation.getToInputTime(),
                amazonAccountRelation.getSkuCreateTimeYear(), sonSkuFewInfo)) {
            return false;
        }

        return true;
    }

    /**
     * 检查排除标签限制
     *
     * @param excludeLabel  排除标签配置
     * @param sonSkuFewInfo 子SKU信息
     * @return true-满足限制，false-不满足限制
     */
    private boolean satisfiesExcludeLabelRestrict(String excludeLabel, SonSkuFewInfo sonSkuFewInfo) {
        if (StringUtils.isBlank(excludeLabel)) {
            return true;
        }

        if (StringUtils.isBlank(sonSkuFewInfo.getTag())) {
            return true;
        }

        List<String> excludeLabelList = CommonUtils.splitList(excludeLabel, ",");
        List<String> tagList = CommonUtils.splitList(sonSkuFewInfo.getTag(), ",");

        boolean hasExcludedTag = tagList.stream().anyMatch(excludeLabelList::contains);
        if (hasExcludedTag) {
            log.debug(String.format("spu%s 标签配置有误", sonSkuFewInfo.getSonSku()));
            return false;
        }

        return true;
    }

    /**
     * 检查重量限制
     * 起始区间与结束区间可以为空，当满足一个条件时则条件成立，区间为空也认为条件成立
     *
     * @param fromWeight    最小重量
     * @param toWeight      最大重量
     * @param sonSkuFewInfo 子SKU信息
     * @return true-满足限制，false-不满足限制
     */
    private boolean satisfiesWeightRestrict(Double fromWeight, Double toWeight, SonSkuFewInfo sonSkuFewInfo) {
        // 如果两个区间都为空，认为条件成立
        if (fromWeight == null && toWeight == null) {
            return true;
        }

        Double weight = sonSkuFewInfo.getProductWeight();
        if (weight == null) {
            log.debug(String.format("spu%s 重量限制-产品重量为空", sonSkuFewInfo.getSonSku()));
            return false;
        }

        // 检查各个条件
        boolean satisfiesFromWeight = (fromWeight == null) || (Double.doubleToLongBits(weight) >= Double.doubleToLongBits(fromWeight));
        boolean satisfiesToWeight = (toWeight == null) || (Double.doubleToLongBits(weight) <= Double.doubleToLongBits(toWeight));

        // 当起始区间与结束区间都不为空时需都满足条件，否则满足一个条件即可
        if (fromWeight != null && toWeight != null) {
            // 都不为空时，需要都满足
            if (satisfiesFromWeight && satisfiesToWeight) {
                return true;
            }
        } else {
            // 至少有一个为空时，满足一个条件即可
            if (satisfiesFromWeight || satisfiesToWeight) {
                return true;
            }
        }

        log.debug(String.format("spu%s 重量限制-不满足区间[%s-%s]，实际重量：%s",
                sonSkuFewInfo.getSonSku(), fromWeight, toWeight, weight));
        return false;
    }

    /**
     * 检查销售成本价限制
     * 起始区间与结束区间可以为空，区间为空认为条件成立
     * 当起始区间与结束区间都不为空时需都满足条件
     *
     * @param fromPrice     最小价格
     * @param toPrice       最大价格
     * @param sonSkuFewInfo 子SKU信息
     * @return true-满足限制，false-不满足限制
     */
    private boolean satisfiesPriceRestrict(Double fromPrice, Double toPrice, SonSkuFewInfo sonSkuFewInfo) {
        // 如果两个区间都为空，认为条件成立
        if (fromPrice == null && toPrice == null) {
            return true;
        }

        Double price = sonSkuFewInfo.getSaleCost();
        if (price == null) {
            log.debug(String.format("spu%s 销售成本价限制-价格为空", sonSkuFewInfo.getSonSku()));
            return false;
        }

        // 检查各个条件
        boolean satisfiesFromPrice = (fromPrice == null) || (Double.doubleToLongBits(price) >= Double.doubleToLongBits(fromPrice));
        boolean satisfiesToPrice = (toPrice == null) || (Double.doubleToLongBits(price) <= Double.doubleToLongBits(toPrice));

        // 当起始区间与结束区间都不为空时需都满足条件，否则满足一个条件即可
        if (fromPrice != null && toPrice != null) {
            // 都不为空时，需要都满足
            if (satisfiesFromPrice && satisfiesToPrice) {
                return true;
            }
        } else {
            // 至少有一个为空时，满足一个条件即可
            if (satisfiesFromPrice || satisfiesToPrice) {
                return true;
            }
        }

        log.debug(String.format("spu%s 销售成本价限制-不满足区间[%s-%s]，实际价格：%s",
                sonSkuFewInfo.getSonSku(), fromPrice, toPrice, price));
        return false;
    }

    /**
     * 检查销量限制
     * 起始区间与结束区间可以为空，区间为空认为条件成立
     * 当起始区间与结束区间都不为空时需都满足条件
     *
     * @param salesType     销量类型
     * @param fromSales     最小销量
     * @param toSales       最大销量
     * @param sonSkuFewInfo 子SKU信息
     * @return true-满足限制，false-不满足限制
     */
    private boolean satisfiesSalesRestrict(Integer salesType, Integer fromSales, Integer toSales, SonSkuFewInfo sonSkuFewInfo) {
        // 如果销量类型为空，认为条件成立
        if (salesType == null) {
            return true;
        }

        // 如果两个区间都为空，认为条件成立
        if (fromSales == null && toSales == null) {
            return true;
        }

        Double thirtySalesNum = sonSkuFewInfo.getThirtySalesNum();
        Double ninetySalesNum = sonSkuFewInfo.getNinetySalesNum();

        if (SalesTypeEnum.ORDER_LAST_30D_COUNT.getCode() == salesType) {
            if (thirtySalesNum == null) {
                log.debug(String.format("spu%s 30天销量为空", sonSkuFewInfo.getSonSku()));
                return false;
            }

            int thirtySalesIntNum = thirtySalesNum.intValue();
            log.debug(String.format("spu%s 指定销量区间[%s-%s]限制,30天销量：%s",
                    sonSkuFewInfo.getSonSku(), fromSales, toSales, thirtySalesNum));

            // 检查各个条件
            boolean satisfiesFromSales = (fromSales == null) || (thirtySalesIntNum >= fromSales);
            boolean satisfiesToSales = (toSales == null) || (thirtySalesIntNum <= toSales);

            // 当起始区间与结束区间都不为空时需都满足条件，否则满足一个条件即可
            if (fromSales != null && toSales != null) {
                return satisfiesFromSales && satisfiesToSales;
            } else {
                return satisfiesFromSales || satisfiesToSales;
            }

        } else if (SalesTypeEnum.ORDER_LAST_90D_COUNT.getCode() == salesType) {
            if (ninetySalesNum == null) {
                log.debug(String.format("spu%s 90天销量为空", sonSkuFewInfo.getSonSku()));
                return false;
            }

            int ninetySalesIntNum = ninetySalesNum.intValue();
            log.debug(String.format("spu%s 指定销量区间[%s-%s]限制,90天销量：%s",
                    sonSkuFewInfo.getSonSku(), fromSales, toSales, ninetySalesNum));

            // 检查各个条件
            boolean satisfiesFromSales = (fromSales == null) || (ninetySalesIntNum >= fromSales);
            boolean satisfiesToSales = (toSales == null) || (ninetySalesIntNum <= toSales);

            // 当起始区间与结束区间都不为空时需都满足条件，否则满足一个条件即可
            if (fromSales != null && toSales != null) {
                return satisfiesFromSales && satisfiesToSales;
            } else {
                return satisfiesFromSales || satisfiesToSales;
            }
        }

        return true;
    }

    /**
     * 检查库存限制
     * 起始区间与结束区间可以为空，区间为空认为条件成立
     * 当起始区间与结束区间都不为空时需都满足条件
     *
     * @param fromInventory 最小库存
     * @param toInventory   最大库存
     * @param sonSkuFewInfo 子SKU信息
     * @return true-满足限制，false-不满足限制
     */
    private boolean satisfiesInventoryRestrict(Integer fromInventory, Integer toInventory, SonSkuFewInfo sonSkuFewInfo) {
        // 如果两个区间都为空，认为条件成立
        if (fromInventory == null && toInventory == null) {
            return true;
        }

        Integer availableStock = sonSkuFewInfo.getAvailableStock();
        if (availableStock == null) {
            log.debug(String.format("spu%s 库存限制-库存为空", sonSkuFewInfo.getSonSku()));
            return false;
        }

        // 检查各个条件
        boolean satisfiesFromInventory = (fromInventory == null) || (availableStock >= fromInventory);
        boolean satisfiesToInventory = (toInventory == null) || (availableStock <= toInventory);

        // 当起始区间与结束区间都不为空时需都满足条件，否则满足一个条件即可
        if (fromInventory != null && toInventory != null) {
            // 都不为空时，需要都满足
            if (satisfiesFromInventory && satisfiesToInventory) {
                return true;
            }
        } else {
            // 至少有一个为空时，满足一个条件即可
            if (satisfiesFromInventory || satisfiesToInventory) {
                return true;
            }
        }

        log.debug(String.format("spu%s 库存限制-不满足区间[%s-%s]，实际库存：%s",
                sonSkuFewInfo.getSonSku(), fromInventory, toInventory, availableStock));
        return false;
    }

    /**
     * 检查产品录入时间限制
     *
     * @param firstAutoPublish  是否首批自动发布
     * @param skuCreateTimeType SKU创建时间类型
     * @param fromInputTime     录入时间起始月份
     * @param toInputTime       录入时间结束月份
     * @param skuCreateTimeYear SKU创建年份
     * @param sonSkuFewInfo     子SKU信息
     * @return true-满足限制，false-不满足限制
     */
    private boolean satisfiesInputTimeRestrict(Boolean firstAutoPublish, Integer skuCreateTimeType,
                                               Integer fromInputTime, Integer toInputTime, Integer skuCreateTimeYear, SonSkuFewInfo sonSkuFewInfo) {

        Long inSingleTime = sonSkuFewInfo.getInSingleTime();

        // 首批不判定产品录入时间限制
        if (BooleanUtils.isTrue(firstAutoPublish)) {
            return true;
        }

        // 按月份限制
        if (skuCreateTimeType != null && skuCreateTimeType.intValue() == SkuCreateTimeTypeEnum.MONTH.getCode()
                && fromInputTime != null && toInputTime != null) {
            if (inSingleTime == null) {
                log.debug(String.format("spu%s 产品录入时间限制,首批不判定", sonSkuFewInfo.getSonSku()));
                return false;
            }

            Date currentDate = new Date();
            Date lastTime = new Date(currentDate.getYear(), currentDate.getMonth(), currentDate.getDate(), 23, 59, 59);
            Date fromTime = DateUtils.addMonths(lastTime, -fromInputTime);
            Date zeroTime = new Date(currentDate.getYear(), currentDate.getMonth(), currentDate.getDate(), 0, 0, 0);
            Date toTime = DateUtils.addMonths(zeroTime, -toInputTime);

            boolean flag = DateUtils.betweenStartTimeAndEndTime(new Date(inSingleTime), toTime, fromTime);
            return flag;
        }

        // 按年份限制
        if (skuCreateTimeType != null && skuCreateTimeType.intValue() == SkuCreateTimeTypeEnum.YEAR.getCode()
                && skuCreateTimeYear != null) {
            if (inSingleTime == null) {
                log.debug(String.format("spu%s 产品录入时间限制,首批不判定", sonSkuFewInfo.getSonSku()));
                return false;
            }

            Date startDate = com.estone.erp.publish.common.util.DateUtils.getMinDateOfMonth(skuCreateTimeYear, 1);
            Date endDate = com.estone.erp.publish.common.util.DateUtils.getMaxDateOfMonth(skuCreateTimeYear, 12);
            Calendar calendar = Calendar.getInstance();
            int currentYear = calendar.get(Calendar.YEAR);
            if (skuCreateTimeYear.intValue() == currentYear) {
                endDate = new Date();
            }

            boolean flag = DateUtils.betweenStartTimeAndEndTime(new Date(inSingleTime), startDate, endDate);
            return flag;
        }

        return true;
    }

    /**
     * 生成记录
     *
     * @param account
     * @param msg
     * @param createBy
     */
    private void generateReport(String account, String msg, String createBy) {
        if (org.apache.commons.lang.StringUtils.isNotBlank(msg) && msg.length() > 65535) {
            msg = msg.substring(0, 65535);
        }
        TemplateRecord record = new TemplateRecord();
        record.setSellerId(account);
        record.setRemark(msg);
        record.setStatus(RecordStatus.FAIL.getCode());
        record.setSaleChannel(SaleChannel.CHANNEL_AMAZON);
        record.setCreatedBy(createBy);
        record.setCreationDate(new Timestamp(System.currentTimeMillis()));
        templateRecordService.insert(record);
    }

    @Override
    public CQueryResult<AmazonTemplateBO> searchAmazonTemplateHistory(CQuery<AmazonTemplateCriteria> cquery) {
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_AMAZON);
        if (!superAdminOrEquivalent.isSuccess()) {
            CQueryResult<AmazonTemplateBO> amazonDataCollectCQueryResult = new CQueryResult<>();
            amazonDataCollectCQueryResult.setErrorMsg(superAdminOrEquivalent.getErrorMsg());
            return amazonDataCollectCQueryResult;
        }
        boolean isAdmin = superAdminOrEquivalent.getResult();
        ApiResult<NewUser> userRes = NewUsermgtUtils.getUserByNo(WebUtils.getUserName());
        if (!userRes.isSuccess()) {
            return CQueryResult.failResult(userRes.getErrorMsg());
        }

        AmazonTemplateCriteria query = cquery.getSearch();
        Assert.notNull(query, "search is null!");
        String table = AmazonTemplateTableEnum.AMAZON_TEMPLATE_2021_11.getCode();
        AmazonTemplateExample example = new AmazonTemplateExample(table);
        Criteria criteria = example.createCriteria();
        example.setTable(table);

        // 用账号控制权
        if (CollectionUtils.isEmpty(query.getSellerIds()) && !isAdmin) {
            if (query.getIsLock() != null && query.getIsLock()) {
                ApiResult<List<String>> authorAccountList = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_AMAZON, false);
                if (!authorAccountList.isSuccess()) {
                    CQueryResult<AmazonTemplateBO> amazonDataCollectCQueryResult = new CQueryResult<>();
                    amazonDataCollectCQueryResult.setErrorMsg(authorAccountList.getErrorMsg());
                    return amazonDataCollectCQueryResult;
                }
                List<String> accounts = authorAccountList.getResult();
                if (CollectionUtils.isNotEmpty(accounts)) {
                    criteria.andSellerIdIn(accounts);
                } else {
                    return new CQueryResult<>();
                }
            }
        }

        if (StringUtils.isNotBlank(query.getSkus())) {
            criteria.andParentSkuIn(query.getSkuList());
        }
        if (StringUtils.isNotBlank(query.getSellerSku())) {
            criteria.andSellerSkuEqualTo(query.getSellerSku());
        }
        if (CollectionUtils.isNotEmpty(query.getSellerIds())) {
            criteria.andSellerIdIn(query.getSellerIds());
        }

        // 添加修改时间范围查询
        if (query.getLastUpdateDateStart() != null) {
            criteria.andLastUpdateDateGreaterThanOrEqualTo(query.getLastUpdateDateStart());
        }
        if (query.getLastUpdateDateEnd() != null) {
            criteria.andLastUpdateDateLessThanOrEqualTo(query.getLastUpdateDateEnd());
        }

        // 添加刊登类型查询
        if (CollectionUtils.isNotEmpty(query.getPublishTypes())) {
            criteria.andPublishTypeIn(query.getPublishTypes());
        }

        // 添加刊登角色查询
        if (query.getPublishRole() != null) {
            criteria.andPublishRoleEqualTo(query.getPublishRole());
        }

        long total = amazonTemplateMapper.countByExample(example);
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        // 排序
        if (StringUtils.isNotBlank(query.getOrderType())) {
            example.setOrderByClause(query.getOrderType());
        }
        String columns = "id,main_image,publish_status,step_template_status,title,parent_SKU,title,country,seller_sku,single_source,sku_data_source,publish_type,publish_role,seller_id,creation_date,created_by,last_update_date,product_type,parent_product_type";
        example.setColumns(columns);
        List<AmazonTemplateBO> amazonTemplateBOs = amazonTemplateMapper.selectFiledColumnsByExample(example);
        // 组装结果
        CQueryResult<AmazonTemplateBO> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(amazonTemplateBOs);
        return result;
    }

    @Override
    public Map<String, String> getAmazonTemplatePublishType(String sellerId, String sellersku) {
        Map<String, String> resultMap = new HashMap<>();
        if (StringUtils.isBlank(sellersku) || StringUtils.isBlank(sellerId)) {
            resultMap.put("erorMsg", "账号和sellersku不能为空");
            return resultMap;
        }
        String id = sellerId + "_" + sellersku;
        EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
        esAmazonProductListingRequest.setId(id);
        String[] fields = {"articleNumber", "mainSku"};
        esAmazonProductListingRequest.setFields(fields);
        List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
        if (CollectionUtils.isEmpty(esAmazonProductListingList)) {
            resultMap.put("remarks", "未查询到listing数据，暂无法匹配");
            return resultMap;
        }
        EsAmazonProductListing esAmazonProductListing = esAmazonProductListingList.get(0);
        String articleNumber = esAmazonProductListing.getArticleNumber();
        String mainSku = StringUtils.isNotBlank(esAmazonProductListing.getMainSku()) ? esAmazonProductListing.getMainSku() : (articleNumber.equals("匹配不到货号") ? null : articleNumber);
        if (StringUtils.isBlank(mainSku)) {
            resultMap.put("remarks", "未查询到listing对应的主sku，暂无法匹配");
            return resultMap;
        }
        // 优先查询历史表
        String columns = "id,parent_sku,variations,sale_variant,publish_type,seller_sku";
        String historyTable = AmazonTemplateTableEnum.AMAZON_TEMPLATE_2021_11.getCode();
        AmazonTemplateExample historyAmazonTemplateExample = new AmazonTemplateExample(historyTable);
        historyAmazonTemplateExample.createCriteria()
                .andParentSkuEqualTo(mainSku)
                .andSellerIdEqualTo(sellerId)
                .andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISH_SUCCESS.getStatusCode());
        historyAmazonTemplateExample.setColumns(columns);
        List<AmazonTemplateBO> historyTemplate = this.selectFiledColumnsByExample(historyAmazonTemplateExample);
        if (CollectionUtils.isNotEmpty(historyTemplate)) {
            for (AmazonTemplateBO amazonTemplateBO : historyTemplate) {
                if (amazonTemplateBO.getSellerSKU().equals(sellersku)) {
                    resultMap.put("publish_type", amazonTemplateBO.getPublishType().toString());
                    return resultMap;
                } else if (StringUtils.isNotBlank(amazonTemplateBO.getVariations()) && amazonTemplateBO.getSaleVariant()) {
                    for (AmazonSku amazonSku : amazonTemplateBO.getAmazonSkus()) {
                        if (amazonSku.getSellerSKU().equals(sellersku)) {
                            resultMap.put("publish_type", amazonTemplateBO.getPublishType().toString());
                            return resultMap;
                        }
                    }
                }
            }

        }

        String table = AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode();
        AmazonTemplateExample amazonTemplateExample = new AmazonTemplateExample(table);
        amazonTemplateExample.createCriteria()
                .andParentSkuEqualTo(mainSku)
                .andSellerIdEqualTo(sellerId)
                .andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISH_SUCCESS.getStatusCode());
        amazonTemplateExample.setColumns(columns);
        List<AmazonTemplateBO> amazonTemplateList = this.selectFiledColumnsByExample(amazonTemplateExample);
        if (CollectionUtils.isEmpty(amazonTemplateList)) {
            resultMap.put("remarks", "未查询到刊登成功的模板数据，暂无法匹配");
            return resultMap;
        }
        for (AmazonTemplateBO amazonTemplateBO : amazonTemplateList) {
            if (amazonTemplateBO.getSellerSKU().equals(sellersku)) {
                resultMap.put("publish_type", amazonTemplateBO.getPublishType().toString());
                return resultMap;
            }
            if (StringUtils.isNotBlank(amazonTemplateBO.getVariations()) && amazonTemplateBO.getSaleVariant()) {
                for (AmazonSku amazonSku : amazonTemplateBO.getAmazonSkus()) {
                    if (amazonSku.getSellerSKU().equals(sellersku)) {
                        resultMap.put("publish_type", amazonTemplateBO.getPublishType().toString());
                        return resultMap;
                    }
                }
            }
        }

        resultMap.put("remarks", "未查询到刊登成功的模板数据，暂无法匹配");
        return resultMap;
    }

    @Override
    public Map<String, Object> getSellerskuPublishType(AmazonTemplateBasisRequest request) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Integer> sellerskuAndPublisTypeMap = new HashMap<>();
        if (null == request || org.apache.commons.lang.StringUtils.isBlank(request.getSellersku()) || CollectionUtils.isEmpty(request.getAccountNumberList())) {
            resultMap.put("remarks", "请检查参数，sellersku和 账号集合不能为空");
            return resultMap;
        }
        String sellersku = request.getSellersku().trim();
        List<String> idList = request.getAccountNumberList().stream().map(o -> o + "_" + sellersku).collect(Collectors.toList());
        EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
        esAmazonProductListingRequest.setIdList(idList);
        String[] fields = {"articleNumber", "mainSku", "accountNumber"};
        esAmazonProductListingRequest.setFields(fields);
        List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
        if (CollectionUtils.isEmpty(esAmazonProductListingList)) {
            resultMap.put("remarks", "未查询到listing数据，暂无法匹配");
            return resultMap;
        }
        String msg = null;
        for (EsAmazonProductListing esAmazonProductListing : esAmazonProductListingList) {
            String articleNumber = esAmazonProductListing.getArticleNumber();
            String sellerId = esAmazonProductListing.getAccountNumber();
            String mainSku = StringUtils.isNotBlank(esAmazonProductListing.getMainSku()) ? esAmazonProductListing.getMainSku() : ("匹配不到货号".equals(articleNumber) ? null : articleNumber);
            if (StringUtils.isBlank(mainSku)) {
                continue;
            }
            // 优先查询历史表
            String columns = "id,parent_sku,variations,sale_variant,publish_type,seller_sku";
            String historyTable = AmazonTemplateTableEnum.AMAZON_TEMPLATE_2021_11.getCode();
            AmazonTemplateExample historyAmazonTemplateExample = new AmazonTemplateExample(historyTable);
            historyAmazonTemplateExample.createCriteria()
                    .andParentSkuEqualTo(mainSku)
                    .andSellerIdEqualTo(sellerId)
                    .andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISH_SUCCESS.getStatusCode());
            historyAmazonTemplateExample.setColumns(columns);
            List<AmazonTemplateBO> historyTemplate = this.selectFiledColumnsByExample(historyAmazonTemplateExample);
            if (CollectionUtils.isNotEmpty(historyTemplate)) {
                historyTemplate.forEach(amazonTemplateBO -> {
                    if (StringUtils.isNotBlank(amazonTemplateBO.getSellerSKU())) {
                        sellerskuAndPublisTypeMap.put(amazonTemplateBO.getSellerSKU(), amazonTemplateBO.getPublishType());
                    }
                    if (StringUtils.isNotBlank(amazonTemplateBO.getVariations()) && amazonTemplateBO.getSaleVariant()) {
                        for (AmazonSku amazonSku : amazonTemplateBO.getAmazonSkus()) {
                            if (StringUtils.isNotBlank(amazonSku.getSellerSKU())) {
                                sellerskuAndPublisTypeMap.put(amazonSku.getSellerSKU(), amazonTemplateBO.getPublishType());
                            }
                        }
                    }
                });

            }

            String table = AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode();
            AmazonTemplateExample amazonTemplateExample = new AmazonTemplateExample(table);
            amazonTemplateExample.createCriteria()
                    .andParentSkuEqualTo(mainSku)
                    .andSellerIdEqualTo(sellerId)
                    .andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISH_SUCCESS.getStatusCode());
            amazonTemplateExample.setColumns(columns);
            List<AmazonTemplateBO> amazonTemplateList = this.selectFiledColumnsByExample(amazonTemplateExample);
            if (CollectionUtils.isEmpty(amazonTemplateList) && sellerskuAndPublisTypeMap.isEmpty()) {
                msg = "未查询到刊登成功的模板数据，暂无法匹配";
                continue;
            }
            amazonTemplateList.forEach(amazonTemplateBO -> {
                if (StringUtils.isNotBlank(amazonTemplateBO.getSellerSKU())) {
                    sellerskuAndPublisTypeMap.put(amazonTemplateBO.getSellerSKU(), amazonTemplateBO.getPublishType());
                }
                if (StringUtils.isNotBlank(amazonTemplateBO.getVariations()) && amazonTemplateBO.getSaleVariant()) {
                    for (AmazonSku amazonSku : amazonTemplateBO.getAmazonSkus()) {
                        if (StringUtils.isNotBlank(amazonSku.getSellerSKU())) {
                            sellerskuAndPublisTypeMap.put(amazonSku.getSellerSKU(), amazonTemplateBO.getPublishType());
                        }
                    }
                }
            });

        }
        if (!sellerskuAndPublisTypeMap.isEmpty()) {
            resultMap.put("sellerskuAndPublisTypeMap", sellerskuAndPublisTypeMap);
        } else {
            resultMap.put("remarks", msg);
        }
        return resultMap;
    }

    @Override
    public int statisticsDayTemplate(String accountNumber, String day, PublishRoleEnum admin, AmaoznPublishStatusEnum publishStatusEnum) {
        String openDay = day + " 00:00:00";
        String endDay = day + " 23:59:59";
        return amazonTemplateMapper.statisticsDayTemplate(accountNumber, openDay, endDay, admin.getPublishRole(), publishStatusEnum.getStatusCode());
    }

    @Override
    public void handleAmazonTemplateAllPublishingStatus(int fullTimeRangeMinutes) {
        // 模版修改时间为4小时前刊登中的模版
        LocalTime currentTime = LocalTime.now();
        // 转为整点
        LocalDateTime currentDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(currentTime.getHour(), 0, 0));
        LocalDateTime beforeDateTime = currentDateTime.minusMinutes(fullTimeRangeMinutes);
        // 超时时间 36 + 12
        String publishTimeOutTime = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "AMAZON", "TEMPLATE_TIME_OUT", 10);
        Date timeOutDate = DateUtils.addMinutes(new Date(), -Integer.parseInt(publishTimeOutTime + (12 * 60)));

        AmazonTemplateExample example = new AmazonTemplateExample();
        Criteria criteria = example.createCriteria();
        criteria.andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISHING.getStatusCode())
                .andInterfaceTypeEqualTo(TemplateInterfaceTypeEnums.XSD.getCode())
                .andLastUpdateDateLessThanOrEqualTo(LocalDateTimeUtil.passLocalDateTimeToDate(beforeDateTime))
                .andLastUpdateDateGreaterThanOrEqualTo(timeOutDate);
        List<Integer> amazonTemplateIdList = batchGetTemplateId(example);
        if (CollectionUtils.isEmpty(amazonTemplateIdList)) {
            XxlJobLogger.log(beforeDateTime + " - " + timeOutDate + "全量读取条数：" + amazonTemplateIdList.size());
            return;
        }
        int selectSize = amazonTemplateIdList.size();
        XxlJobLogger.log(beforeDateTime + " - " + timeOutDate + "全量读取条数：" + amazonTemplateIdList.size());
        int total = 0;
        for (Integer templateId : amazonTemplateIdList) {
            try {
                List<AmazonProcessReport> reportList = amazonProcessReportService.selectProcessReportByTemplateId(templateId);
                AmazonTemplateBO amazonTemplateBO = handleFullTimeAmazonTemplateStatus(templateId, reportList);
                if (amazonTemplateBO != null) {
                    total++;
                    updateAmazonTemplateFilterNullByPrimaryKey(amazonTemplateBO);
                }
            } catch (Exception e) {
                log.error("处理模板状态异常，templateId={}", templateId, e);
            }
        }
        if (selectSize > 200000) {
            log.alertMessage(" [ {} - {} ] Amazon全量模版状态更新完成\n\n 读取模版：{}，更新模版：{}", LocalDateTimeUtil.format(timeOutDate), LocalDateTimeUtil.format(beforeDateTime), selectSize, total);
        }
        XxlJobLogger.log("全量处理完成：读取模版：{}，更新数量：{} ", amazonTemplateIdList.size(), total);
    }

    /**
     * 全量定时处理模板状态
     * 数据范围：模版修改时间为4小时前刊登中的模版
     * 1. 处理报告状态，判定成功失败
     * 2. 缺少类型判定失败 或者只有产品上传，处理报告类型大于1且小于4则模板刊登状态为失败，但系统拦截处理报告是失败，判定模板失败
     * 3. 若超过系统参数超时时间，36小时，若模板店铺+sellersku存在在线列表，则判定成功，否则均判定失败
     */
    private AmazonTemplateBO handleFullTimeAmazonTemplateStatus(Integer templateId, List<AmazonProcessReport> reportList) {
        AmazonTemplateBO amazonTemplate = getSimpleTemplate(templateId);
        if (amazonTemplate == null) {
            return null;
        }

        // 模板超时处理
        Boolean isPublishTimeOut = handleTimeOutPublishingStatus(amazonTemplate, reportList);
        if (Boolean.TRUE.equals(isPublishTimeOut)) {
            return amazonTemplate;
        }
        // 检查处理报告模版状态
        return handleAmazonTemplateStatus(reportList, templateId, true);
    }

    private Boolean handleInterceptPublishingStatus(List<AmazonProcessReport> reportList, Integer templateId) {
        Map<String, List<AmazonProcessReport>> feedTypeReportMap = reportList.stream().collect(Collectors.groupingBy(AmazonProcessReport::getFeedType));
        // 处理报告只有一条
        if (reportList.size() == 1 || feedTypeReportMap.size() == 1) {
            List<AmazonProcessReport> processReports = feedTypeReportMap.get(SpFeedType.POST_PRODUCT_DATA.getValue());
            if (CollectionUtils.isEmpty(processReports)) {
                return false;
            }
            AmazonProcessReport processReport = processReports.stream().min(Comparator.comparing(AmazonProcessReport::getCreationDate)).orElseGet(() -> null);
            if (processReport == null) {
                return false;
            }

            boolean notTaskIdFlag = StringUtils.isBlank(processReport.getTaskId());
            if (notTaskIdFlag) {
                // 处理报告只有刊登产品类型记录为失败状态且没有TaskId,则模板刊登失败 (刊登校验拦截)
                if (SpFeedType.POST_PRODUCT_DATA.getValue().equals(processReport.getFeedType())
                        && BooleanUtils.isFalse(processReport.getStatus())) {
                    return true;
                }
            }
        }
        return false;
    }

    private AmazonTemplateBO getSimpleTemplate(Integer templateId) {
        return selectColumnsTemplateByPrimaryKey(templateId, "id, country, seller_sku, seller_id, step_template_status, publish_status, sale_variant, variations, last_update_date, creation_date");
    }

    @Override
    public AmazonTemplateBO selectColumnsTemplateByPrimaryKey(Integer templateId, String columns) {
        AmazonTemplateExample example = new AmazonTemplateExample();
        example.createCriteria().andIdEqualTo(templateId);
        example.setColumns(columns);
        example.setLimit(1);
        example.setTable(AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
        List<AmazonTemplateBO> amazonTemplates = amazonTemplateMapper.selectFiledColumnsByExample(example);
        if (CollectionUtils.isEmpty(amazonTemplates)) {
            return null;
        }
        return amazonTemplates.get(0);
    }

    @Override
    public List<Integer> batchGetTemplateId(AmazonTemplateExample example) {
        List<Integer> templateIdList = new ArrayList<>();
        int startId = 0;
        while (true) {
            AmazonTemplateExample queryExample = new AmazonTemplateExample();
            Criteria queryExampleCriteria = queryExample.createCriteria();
            Criteria criteria = example.getOredCriteria().get(0);
            List<AmazonTemplateExample.Criterion> allCriteria = criteria.getAllCriteria();
            for (AmazonTemplateExample.Criterion criterion : allCriteria) {
                queryExampleCriteria.addCriterion(criterion);
            }
            queryExample.setLimit(1000);
            queryExample.setOrderByClause("id asc");
            queryExampleCriteria.andIdGreaterThan(startId);
            List<Integer> amazonTemplateIdList = amazonTemplateMapper.selectTemplateId(queryExample);
            if (CollectionUtils.isEmpty(amazonTemplateIdList)) {
                break;
            }
            templateIdList.addAll(amazonTemplateIdList);
            startId = amazonTemplateIdList.get(amazonTemplateIdList.size() - 1);
        }
        return templateIdList;
    }

    @Override
    public int getPublishSpuModelPageTotal( PublishSpuModelRequest request){
        return amazonTemplateMapper.getPublishSpuModelPageTotal(request);
    }

    @Override
    public List<AmazonTemplateWithBLOBs> getPublishSpuModelPage(PublishSpuModelRequest request,  int offset,  Integer limit){
        return amazonTemplateMapper.getPublishSpuModelPage(request,offset,limit);
    }

    @Override
    public void updateByPrimaryKeySelective(AmazonTemplateBO adminTemplate) {
        amazonTemplateMapper.updateByPrimaryKeySelective(adminTemplate);
    }

    @Override
    public CQueryResult<AmazonTemplateBO> searchJsonAdminTemplate(CQuery<AmazonTemplateCriteria> cquery) {
        AmazonTemplateCriteria query = cquery.getSearch();
        Assert.notNull(query, "search is null!");
        String table = AmazonTemplateTableEnum.AMAZON_TEMPLATE_ADMIN.getCode();
        AmazonTemplateExample example = new AmazonTemplateExample(table);
        Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(query.getIdsStr())) {
            List<Integer> ids = Arrays.asList(query.getIdsStr().replace("，",",").split(","))
                    .stream()
                    .map((o -> {
                        try {
                            return Integer.parseInt(o.trim());
                        }catch (Exception e){
                            return -1;
                        }
                    }))
                    .collect(Collectors.toList());
            criteria.andIdIn(ids);
        }
        if (StringUtils.isNotBlank(query.getSkus())) {
            List<String> skus = Arrays.asList(query.getSkus().replace("，", ",").split(","));
            criteria.andParentSkuIn(skus);
        }
        if (CollectionUtils.isNotEmpty(query.getSellerIds())) {
            criteria.andSellerIdIn(query.getSellerIds());
        }
        if (StringUtils.isNotBlank(query.getTitle())) {
            criteria.andTitleLike("%" + query.getTitle() + "%");
        }
        if (StringUtils.isNotBlank(query.getCreatedBy())) {
            criteria.andCreatedByEqualTo(query.getCreatedBy());
        }
        if (query.getCreationDateStart() != null) {
            criteria.andCreationDateGreaterThanOrEqualTo(query.getCreationDateStart());
        }
        if (query.getCreationDateEnd() != null) {
            criteria.andCreationDateLessThanOrEqualTo(query.getCreationDateEnd());
        }
        if (query.getLastUpdateDateStart() != null) {
            criteria.andLastUpdateDateGreaterThanOrEqualTo(query.getLastUpdateDateStart());
        }
        if (query.getLastUpdateDateEnd() != null) {
            criteria.andLastUpdateDateLessThanOrEqualTo(query.getLastUpdateDateEnd());
        }
        // 数据来源
        if (null != query.getSkuDataSource() && query.getSkuDataSource() > 0) {
            criteria.andSkuDataSourceEqualTo(query.getSkuDataSource());
        }
        if (null != query.getPublishType() && query.getPublishType() > 0) {
            criteria.andPublishTypeEqualTo(query.getPublishType());
        }
        if (CollectionUtils.isNotEmpty(query.getPublishTypes())) {
            criteria.andPublishTypeIn(query.getPublishTypes());
        }

        if (null != query.getSaleVariant() ) {
            criteria.andSaleVariantEqualTo(query.getSaleVariant());
        }

        // 启用禁用
        if (query.getStatus() != null) {
            criteria.andStatusEqualTo(query.getStatus());
        }

        if (StringUtils.isNotEmpty(query.getSite())) {
            String site = query.getSite().trim().equalsIgnoreCase("GB") ? "UK" : query.getSite();
            criteria.andCountryEqualTo(site);
        }
        if (CollectionUtils.isNotEmpty(query.getSiteList())) {
            criteria.andCountryIn(query.getSiteList());
        }


        long total = amazonTemplateMapper.countByExample(example);
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        // 排序
        if (StringUtils.isNotBlank(query.getOrderType())) {
            example.setOrderByClause(query.getOrderType());
        }else {
            example.setOrderByClause("creation_date desc");
        }
        List<AmazonTemplateBO> amazonTemplateBOs = amazonTemplateMapper.selectByExampleBOs(example);
        // 组装结果
        CQueryResult<AmazonTemplateBO> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(amazonTemplateBOs);
        return result;
    }

    @Override
    public int updateAmazonTemplateAdminStatus( AmazonTemplate record,  AmazonTemplateExample example){
        Assert.notNull(record, "record is null!");
        Assert.notNull(example, "example is null!");
        return amazonTemplateMapper.updateAmazonTemplateAdminStatus(record,example);
    }

    @Override
    public void updateImageMapping(AmazonTemplateBO template) {
        if (template == null || template.getId() == null) {
            return;
        }
        amazonTemplateMapper.updateImageMapping(template.getId(), template.getOssImageData());
    }

    @Override
    public ApiResult<List<SpTemplateDataDO>> getSPTemplateData(SpTemplateDataRequest request) {
        String spu = request.getSpu();
        List<SpTemplateDataDO> dataDOList = new ArrayList<>();
        // 获取模版数据
        List<AmazonTemplateBO> spTemplateData = amazonTemplateMapper.getSPTemplateData(spu, AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
        if (CollectionUtils.isNotEmpty(spTemplateData)) {
            transferSPTemplateData(spTemplateData, dataDOList);
        }
        // 获取历史数据
        List<AmazonTemplateBO> spTemplateHistoryData = amazonTemplateMapper.getSPTemplateData(spu, AmazonTemplateTableEnum.AMAZON_TEMPLATE_2021_11.getCode());
        if (CollectionUtils.isNotEmpty(spTemplateHistoryData)) {
            transferSPTemplateData(spTemplateHistoryData, dataDOList);
        }
        return ApiResult.newSuccess(dataDOList);
    }

    @Override
    public void updateProductType(AmazonTemplateBO template) {
        if (template == null || template.getId() == null) {
            return;
        }
        amazonTemplateMapper.updateProductType(template.getId(), template.getProductType());
    }

    private void transferSPTemplateData(List<AmazonTemplateBO> spTemplateData, List<SpTemplateDataDO> dataDOList) {
        List<SpTemplateDataDO> templateDataDOS = spTemplateData.stream().map(template -> {
            SpTemplateDataDO dataDO = new SpTemplateDataDO();
            dataDO.setSpu(template.getParentSku());
            String brand = template.getBrand();
            if (StringUtils.isNotBlank(template.getTitle())) {
                dataDO.setTitle(template.getTitle().replaceAll(brand, ""));
            }
            if (StringUtils.isNotBlank(template.getDescription())) {
                dataDO.setDescription(template.getDescription().replaceAll(brand, ""));
            }
            if (StringUtils.isNotBlank(template.getBulletPoint())) {
                dataDO.setBulletPoint(template.getBulletPoint().replaceAll(brand, ""));
            }
            return dataDO;
        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(templateDataDOS)) {
            dataDOList.addAll(templateDataDOS);
        }
    }
}
