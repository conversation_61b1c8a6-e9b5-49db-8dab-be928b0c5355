package com.estone.erp.publish.amazon.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProduct;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProductCriteria;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProductExample;
import com.estone.erp.publish.amazon.model.dto.AmazonMustPublishNewProductExVO;
import com.estone.erp.publish.amazon.model.dto.AmazonMustPublishNewProductVO;
import com.estone.erp.publish.amazon.model.request.RollbackFixPictureRequest;
import com.estone.erp.publish.amazon.service.AmazonMustPublishNewProductService;
import com.estone.erp.publish.base.pms.enums.PictureTypeEnum;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> amazon_must_publish_new_product
 * 2023-10-20 16:23:30
 */
@RestController
@RequestMapping("amazonMustPublishNewProduct")
public class AmazonMustPublishNewProductController {
    @Resource
    private AmazonMustPublishNewProductService amazonMustPublishNewProductService;
    @Autowired
    private PermissionsHelper permissionsHelper;

    @PostMapping
    public ApiResult<?> postAmazonMustPublishNewProduct(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAmazonMustPublishNewProduct": // 查询列表
                    CQuery<AmazonMustPublishNewProductCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AmazonMustPublishNewProductCriteria>>() {
                    });
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<AmazonMustPublishNewProductVO> results = null;
                    try {
                        results = amazonMustPublishNewProductService.search(cquery);
                    } catch (Exception e) {
                        return ApiResult.newError(e.getMessage());
                    }
                    return results;
                case "addAmazonMustPublishNewProduct": // 添加
                    AmazonMustPublishNewProduct amazonMustPublishNewProduct = requestParam.getArgsValue(new TypeReference<AmazonMustPublishNewProduct>() {
                    });
                    amazonMustPublishNewProductService.insert(amazonMustPublishNewProduct);
                    return ApiResult.newSuccess(amazonMustPublishNewProduct);
            }
        }
        return ApiResult.newSuccess();
    }

    /**
     * 批量修改销售
     */
    @PostMapping("batchUpdateSale")
    public ApiResult<String> batchUpdateSale(@RequestBody AmazonMustPublishNewProductCriteria request) {
        return amazonMustPublishNewProductService.batchUpdateSale(request);
    }

    /**
     * 批量备注
     */
    @PostMapping("batchUpdateRemarks")
    public ApiResult<String> batchUpdateRemarks(@RequestBody AmazonMustPublishNewProductCriteria request) {
        return amazonMustPublishNewProductService.batchUpdateRemarks(request);
    }

    /**
     * 导出
     */
    @PostMapping("exportFile")
    public void exportFile(@RequestBody AmazonMustPublishNewProductCriteria request, HttpServletResponse response) throws Exception {
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_AMAZON);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
        }

        // 是超管则不限制权限
        if (!superAdminOrEquivalent.getResult()) {
            // 非超管查询员工工号权限限制
            List<String> currentPermissionEmployeeNo = permissionsHelper.getCurrentUserEmployeeNoPermission(request.getSaleAccount(),
                    request.getSaleAccountManager(),
                    request.getSaleAccountLeader(),
                    SaleChannel.CHANNEL_AMAZON);
            request.setSaleAccount(currentPermissionEmployeeNo);
        }

        ExcelWriter excelWriter = null;
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
        int pageIndex = 0;
        int pageSize = 500;
        while (true) {
            AmazonMustPublishNewProductExample example = request.getExample();
            example.setLimit(500);
            example.setOffset(pageIndex * pageSize);
            example.setOrderByClause("id desc");
            List<AmazonMustPublishNewProduct> mustPublishNewProducts = amazonMustPublishNewProductService.selectByExample(example);
            if (CollectionUtils.isEmpty(mustPublishNewProducts)) {
                break;
            }
            if (excelWriter == null) {
                excelWriter = EasyExcel.write(response.getOutputStream(), AmazonMustPublishNewProductExVO.class)
                        .build();
            }
            List<AmazonMustPublishNewProductExVO> productVOS = mustPublishNewProducts.stream().map(AmazonMustPublishNewProductExVO::conventToVO).collect(Collectors.toList());
            excelWriter.write(productVOS, writeSheet);
            pageIndex++;
        }
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("monitorListing" + LocalDateTime.now(), "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        excelWriter.finish();
    }

    /**
     * 获取1600Pics
     *
     * @param spu spu
     * @return
     */
    @GetMapping("getAmazon1600Pics/{spu}")
    public ApiResult<List<String>> getAmazon1600Pics(@PathVariable("spu") String spu) {
        String type = PictureTypeEnum.AMAZON1600_PRODUCT_PLAT.getName();
        List<String> productImgeList = FmsUtils.getPictureUrlBySkuAndType(spu, type);
        return ApiResult.newSuccess(productImgeList);
    }


    /**
     * 回退修图
     */
    @PostMapping("rollbackFixPicture")
    public ApiResult<String> rollbackFixPicture(@RequestBody List<RollbackFixPictureRequest> requests) {
        return amazonMustPublishNewProductService.rollbackFixPicture(requests);
    }

    /**
     * 批量审核通过
     *
     * @param ids 记录列表Id
     */
    @PostMapping("batchAuditPass")
    public ApiResult<String> batchAuditPass(@RequestBody List<Integer> ids) {
        return amazonMustPublishNewProductService.batchAuditPass(ids);
    }

    /**
     * 图片审核通过
     *
     * @param ids 记录列表Id
     */
    @PostMapping("imageAuditPass")
    public ApiResult<String> imageAuditPass(@RequestBody List<Integer> ids) {
        return amazonMustPublishNewProductService.imageAuditPass(ids);
    }

}
