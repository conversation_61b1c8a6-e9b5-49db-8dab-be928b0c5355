package com.estone.erp.publish.amazon.jobHandler.template.publish;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.model.AmazonTemplateExample;
import com.estone.erp.publish.amazon.model.AmazonTemplateWithBLOBs;
import com.estone.erp.publish.amazon.service.AmazonCallService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonPublishOperationLogService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.client.enums.SpFeedType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * amazon 更换EAN自动刊登定时任务
 * 处理报告code为8572或8541的模板，换EAN后重新刊登，
 * 若刊登后还是报错8572和8541，重试3次
 * ES-5009
 * <AUTHOR>
 * @date 2023-04-19 15:51
 */
@Slf4j
@Component
public class AmazonUpdateEANRePublishJobHandler extends AbstractJobHandler {

    @Autowired
    private AmazonTemplateService templateService;
    @Autowired
    private AmazonCallService amazonCallService;
    @Autowired
    private AmazonPublishOperationLogService amazonPublishOperationLogService;

    public AmazonUpdateEANRePublishJobHandler() {
        super(AmazonUpdateEANRePublishJobHandler.class.getName());
    }

    @Data
    static class InnerParam {
        /**
         * 模板编号
         */
        private List<Integer> templateIds;

        /**
         * 匹配的问题分类
         */
        private List<String> reportSolutionTypes;

        /**
         * 店铺
         */
        private String accountNumber;

        /**
         * 品牌
         */
        private String brand;

        /**
         * 开始时间范围
         */
        private String starTime;
    }

    @Override
    @XxlJob("AmazonUpdateEANRePublishJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            innerParam = new InnerParam();
        }
        if (StringUtils.isBlank(innerParam.getStarTime())) {
            LocalDateTime yesterdayDateTime = LocalDateTime.of(LocalDate.now().minusDays(1),
                    LocalTime.of(21, 0, 0));
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String startTimesStr = dateTimeFormatter.format(yesterdayDateTime);
            innerParam.setStarTime(startTimesStr);
        }

        XxlJobLogger.log("star time :{}",innerParam.getStarTime());
        List<Integer> updateEnaIds = new ArrayList<>();
        List<Integer> updateBrandIds = new ArrayList<>();
        Integer lastId = null;
        while (true) {
            AmazonTemplateExample queryExample = createQueryExample(innerParam, innerParam.getStarTime());
            if (lastId != null) {
                queryExample.getOredCriteria().get(0).andIdGreaterThan(lastId);
            }
            List<AmazonTemplateBO> amazonTemplateBOS = templateService.selectFiledColumnsByExample(queryExample);
            if (CollectionUtils.isEmpty(amazonTemplateBOS)) {
                break;
            }
            for (AmazonTemplateBO templateBO : amazonTemplateBOS) {
                Integer status = templateBO.getStatus();
                if (status == null || status < 3) {
                    // 修改Ena模板重新刊登
                    rePublishHandler(templateBO,null);
                    updateEnaIds.add(templateBO.getId());
                }else {
                    rePublishHandler(templateBO,innerParam.getBrand());
                    updateBrandIds.add(templateBO.getId());
                    XxlJobLogger.log("id {}- template {}，修改ENA超出重试次数,更换品牌为：{}", templateBO.getId(), status, innerParam.getBrand());
                }
                lastId = templateBO.getId();
            }
        }

        recodeOptionLog(updateEnaIds,updateBrandIds);
        XxlJobLogger.log("本次累计处理数据：{}个", updateEnaIds.size() + updateBrandIds.size());
        return ReturnT.SUCCESS;
    }

    private void recodeOptionLog(List<Integer> updateEnaIds, List<Integer> updateBrandIds) {
        if (CollectionUtils.isNotEmpty(updateEnaIds)) {
            List<Integer> templateIds = updateEnaIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(templateIds)) {
                return;
            }
            saveRePublishOperationLog("update_ean", templateIds);
        }

        if (CollectionUtils.isNotEmpty(updateBrandIds)) {
            List<Integer> templateIds = updateBrandIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(templateIds)) {
                return;
            }
            saveRePublishOperationLog("update_ean_brand", templateIds);
        }
    }

    private void saveRePublishOperationLog(String modId, List<Integer> templateIds) {
        List<List<Integer>> partition = Lists.partition(templateIds, 10000);
        partition.forEach(part->{
            LocalDate now = LocalDate.now();
            AmazonPublishOperationLog operationLog = new AmazonPublishOperationLog();
            operationLog.setModId(modId);
            operationLog.setOpType("re_publish");
            operationLog.setPlatform(SaleChannelEnum.AMAZON.getChannelName());
            operationLog.setUser("admin");
            operationLog.setObject(now.toString());
            operationLog.setState(1);
            Map<String, Object> metaData = new HashMap<>();
            metaData.put("templateIds", JSON.toJSONString(part));
            operationLog.setMetaObj(JSON.toJSONString(metaData));
            operationLog.setCreatedTime(new Timestamp(System.currentTimeMillis()));
            amazonPublishOperationLogService.insert(operationLog);
        });
    }



    /**
     * 根据模板编号替换品牌重新刊登
     *
     * @param templateBO    模板
     */
    private void rePublishHandler(AmazonTemplateBO templateBO, String brand) {
        Integer templateId = templateBO.getId();
        try {
            String table = AmazonTemplateUtils.getAmazonTemplateTable(false);
            AmazonTemplateWithBLOBs template = templateService.findById(templateId, table);
            if (template != null && template.getPublishStatus().equals(AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode())) {
                // 用来记录次数
                int number = template.getStatus() == null ? 1 : template.getStatus() + 1;
                template.setStatus(number);
                if (StringUtils.isBlank(brand)) {
                    templateService.updateEAN(template);
                    XxlJobLogger.log("{}-模板更换EAN重新刊登", templateId);
                }else {
                    template.setBrand(brand);
                    template.setManufacturer(brand);
                    template.setMfrPartNumber(brand);
                    //templateService.update(template);
                    XxlJobLogger.log("{}-模板更换EAN失败，替换品牌：{}，重新刊登", templateId, brand);
                }

                AmazonTemplateBO amazonTemplateBO = BeanUtil.copyProperties(template, AmazonTemplateBO.class);
                List<AmazonTemplateBO> templates = Lists.newArrayList(amazonTemplateBO);
                //刊登中状态
                templateService.batchUpdateAmaozntemplatePublishStatus(templates, SpFeedType.POST_PRODUCT_DATA.getValue());
                AmazonUtils.initAmazonTemplateSellerSKU(templates);
                amazonCallService.publishTemplates(templates);
                return;
            }
            XxlJobLogger.log("{}-未查询到模板或者模板状态不是失败", templateId);
        } catch (Exception e) {
            XxlJobLogger.log("{}-模板更换EAN重新刊登异常：", templateId, e);
        }
    }


    private AmazonTemplateExample createQueryExample(InnerParam innerParam, String startTimesStr) {
        AmazonTemplateExample example = new AmazonTemplateExample();
        example.setLimit(100);
        example.setOrderByClause("id");
        example.setColumns("id, report_solution_type, publish_status, status");
        List<String> reportSolutionTypes = innerParam.getReportSolutionTypes();
        AmazonTemplateExample.Criteria criteria = example.createCriteria();
        criteria.andReportSolutionTypeIn(reportSolutionTypes);
        criteria.andLastUpdateDateGreaterThanOrEqualTo(startTimesStr);
        criteria.andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode());
        return example;
    }
}
