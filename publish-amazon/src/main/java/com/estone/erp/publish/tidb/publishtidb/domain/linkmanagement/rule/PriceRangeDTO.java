package com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement.rule;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * Amazon链接管理配置 - 价格区间DTO
 * 对应设计文档3.4.2 rule字段结构中的priceRange
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Data
public class PriceRangeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 最小价格
     */
    private BigDecimal minPrice;

    /**
     * 最大价格
     */
    private BigDecimal maxPrice;

}