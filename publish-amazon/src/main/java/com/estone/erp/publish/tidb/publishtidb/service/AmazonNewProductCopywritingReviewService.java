package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.publish.tidb.publishtidb.domain.CheckCopywritingInfringementWordDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.ReviewBasisInfoVO;
import com.estone.erp.publish.tidb.publishtidb.domain.ReviewSaveDTO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonNewProductCopywritingReview;

/**
 * <p>
 * amazon新品文案审核 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
public interface AmazonNewProductCopywritingReviewService extends IService<AmazonNewProductCopywritingReview> {
    ReviewBasisInfoVO getReviewBasisInfo(String spu);


    void saveCopywritingReview(ReviewSaveDTO dto);

    CheckCopywritingInfringementWordDTO checkCopywritingInfringementWords( ReviewSaveDTO dto);


    /**
     * 推送新品文案审核结果到产品更新
     *
     * @param spu
     */
    void pushCopywritingReviewToProduct(String spu);

    CheckCopywritingInfringementWordDTO delCopywritingInfringementWords(ReviewSaveDTO dto);
}
