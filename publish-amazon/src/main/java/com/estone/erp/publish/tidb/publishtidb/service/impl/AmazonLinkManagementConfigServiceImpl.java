package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.enums.AmazonMarketingLogTypeEnum;
import com.estone.erp.publish.amazon.util.AmazonConfigLogUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement.AmazonLinkManagementConfigDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.req.AmazonLinkManagementConfigRequest;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonLinkManagementConfigMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonLinkManagementConfig;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonMarketingConfigLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonLinkManagementConfigService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonMarketingConfigLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <p>
 * Amazon链接管理配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Slf4j
@Service
public class AmazonLinkManagementConfigServiceImpl extends ServiceImpl<AmazonLinkManagementConfigMapper, AmazonLinkManagementConfig> implements AmazonLinkManagementConfigService {

    @Resource
    private AmazonMarketingConfigLogService amazonMarketingConfigLogService;
    @Resource
    private PermissionsHelper permissionsHelper;

    @Override
    public CQueryResult<AmazonLinkManagementConfigDTO> queryPage(CQuery<AmazonLinkManagementConfigRequest> query) {
        try {
            // 创建分页对象
            IPage<AmazonLinkManagementConfig> page = new Page<>(query.getPage(), query.getLimit());

            // 权限校验
            isAuth(query.getSearch());

            // 构建查询条件
            LambdaQueryWrapper<AmazonLinkManagementConfig> wrapper = buildQueryWrapper(query.getSearch());

            // 执行查询
            IPage<AmazonLinkManagementConfig> pageResult = page(page, wrapper);
            List<AmazonLinkManagementConfigDTO> dataList = pageResult.getRecords().stream()
                    .map(AmazonLinkManagementConfigDTO::fromEntity)
                    .collect(Collectors.toList());

            // 构建返回结果
            CQueryResult<AmazonLinkManagementConfigDTO> result = new CQueryResult<>();
            result.setTotal(pageResult.getTotal());
            result.setTotalPages(Long.valueOf(pageResult.getPages()).intValue());
            result.setRows(dataList);
            result.setSuccess(true);
            return result;

        } catch (Exception e) {
            log.error("亚马逊链接管理配置查询失败", e);
            return CQueryResult.failResult("查询失败：" + e.getMessage());
        }
    }

    private void isAuth(AmazonLinkManagementConfigRequest searchParam) {
        ApiResult<Boolean> superAdminOrEquivalentResult = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_AMAZON);
        if (!superAdminOrEquivalentResult.isSuccess()) {
            throw new BusinessException(superAdminOrEquivalentResult.getErrorMsg());
        }
        // 平台销售主管和超管可查看全部数据
        if (superAdminOrEquivalentResult.getResult()) {
            return;
        }
        List<String> employeeNos = permissionsHelper.getCurrentPermissionEmployeeNo(SaleChannel.CHANNEL_AMAZON, false);

        List<String> createdByList = searchParam.getCreateByList();
        if (CollectionUtils.isNotEmpty(createdByList)) {
            createdByList.removeIf(createdBy -> !employeeNos.contains(createdBy));
            searchParam.setCreateByList(createdByList);
            return;
        }
        searchParam.setCreateByList(employeeNos);
    }


    @Override
    public ApiResult<String> saveOrUpdateConfig(AmazonLinkManagementConfigDTO entity) {
        Long id = entity.getId();
        if (Objects.nonNull(id)) {
            return updateConfig(entity);
        }

        return addConfig(entity);
    }

    @Override
    public boolean updateStatus(AmazonLinkManagementConfigRequest request) {
        if (CollectionUtils.isEmpty(request.getIds())) {
            return false;
        }
        Integer status = request.getStatus();
        if (status == null) {
            return false;
        }
        List<AmazonLinkManagementConfig> amazonLinkManagementConfigs = baseMapper.selectList(new LambdaQueryWrapper<AmazonLinkManagementConfig>()
                .in(AmazonLinkManagementConfig::getId, request.getIds())
                .select(AmazonLinkManagementConfig::getId, AmazonLinkManagementConfig::getStatus));
        for (AmazonLinkManagementConfig managementConfig : amazonLinkManagementConfigs) {
            if (managementConfig.getStatus().equals(status)) {
                continue;
            }

            // 记录日志
            AmazonMarketingConfigLog log = AmazonConfigLogUtil.checkEqualLog(managementConfig.getId(), AmazonMarketingLogTypeEnum.LINK_MANAGE_HANDING_TIME,
                    "status", "启用状态",
                    managementConfig.getStatus(), status, WebUtils.getUserName(), LocalDateTime.now());
            if (log != null) {
                amazonMarketingConfigLogService.save(log);
            }
            managementConfig.setStatus(status);
            managementConfig.setUpdatedBy(WebUtils.getUserName());
            managementConfig.setUpdatedTime(LocalDateTime.now());
            baseMapper.updateById(managementConfig);
        }
        return true;
    }

    private ApiResult<String> addConfig(AmazonLinkManagementConfigDTO configDTO) {
        AmazonLinkManagementConfig entity = configDTO.toEntity();

        String ruleName = entity.getRuleName();
        if (existRuleName(ruleName)) {
            return ApiResult.newError("规则名称已存在，请重新输入！");
        }
        // 检查是否已存在相同优先级的店铺
        checkAccountLevel(configDTO.getAccountList(), configDTO.getLevel(), null);


        entity.setCreatedBy(WebUtils.getUserName());
        entity.setCreatedTime(LocalDateTime.now());
        baseMapper.insert(entity);
        return ApiResult.newSuccess("新增成功");
    }

    private void checkAccountLevel(List<String> accounts, Integer level, Long ignoreId) {
        if (CollectionUtils.isEmpty(accounts)) {
            return;
        }

        LambdaQueryWrapper<AmazonLinkManagementConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmazonLinkManagementConfig::getLevel, level)
                .select(AmazonLinkManagementConfig::getId, AmazonLinkManagementConfig::getAccounts)
                .ne(Objects.nonNull(ignoreId), AmazonLinkManagementConfig::getId, ignoreId)
                .apply("JSON_OVERLAPS((accounts->'$.accounts'), '[\"" + StringUtils.join(accounts, "\",\"") + "\"]')");

        baseMapper.selectList(queryWrapper).stream().findFirst().ifPresent(config -> {
            String accountsJson = config.getAccounts();
            if (StringUtils.isBlank(accountsJson)) {
                return;
            }
            Map<String, List<String>> accountMap = JSON.parseObject(accountsJson, new TypeReference<Map<String, List<String>>>() {
            });
            List<String> accountList = accountMap.get("accounts");
            if (CollectionUtils.isEmpty(accountList)) {
                return;
            }
            List<String> commonAccounts = accountList.stream().filter(accounts::contains).collect(Collectors.toList());
            throw new BusinessException("已存在相同优先级的店铺：" + StringUtils.join(commonAccounts, ","));
        });

    }


    private Boolean existRuleName(String ruleName) {
        if (StringUtils.isBlank(ruleName)) {
            return false;
        }

        return baseMapper.selectCount(new LambdaQueryWrapper<AmazonLinkManagementConfig>()
                .eq(AmazonLinkManagementConfig::getRuleName, ruleName).last("limit 1")
        ) > 0;
    }

    private ApiResult<String> updateConfig(AmazonLinkManagementConfigDTO configDTO) {
        AmazonLinkManagementConfig dbConfig = baseMapper.selectById(configDTO.getId());
        if (!StringUtils.equals(dbConfig.getRuleName(), configDTO.getRuleName())) {
            return ApiResult.newError("规则名称不一致！");
        }
        AmazonLinkManagementConfig entity = configDTO.toEntity();
        entity.setUpdatedBy(WebUtils.getUserName());
        entity.setUpdatedTime(LocalDateTime.now());

        // 检查是否已存在相同优先级的店铺
        checkAccountLevel(configDTO.getAccountList(), configDTO.getLevel(), dbConfig.getId());

        // 记录日志
        saveConfigLogs(entity);

        baseMapper.updateById(entity);
        return ApiResult.newSuccess();
    }

    /**
     * 保存日志
     *
     * @param config 配置
     */
    private void saveConfigLogs(AmazonLinkManagementConfig config) {
        // 获取旧的配置
        AmazonLinkManagementConfig oldConfig = getById(config.getId());
        AmazonLinkManagementConfigDTO oldConfigDTO = AmazonLinkManagementConfigDTO.fromEntity(oldConfig);
        AmazonLinkManagementConfigDTO newConfigDTO = AmazonLinkManagementConfigDTO.fromEntity(config);

        // 日志列表
        List<AmazonMarketingConfigLog> configLogs = AmazonConfigLogUtil
                .generateLog(newConfigDTO, oldConfigDTO, config.getId(), AmazonMarketingLogTypeEnum.LINK_MANAGE_HANDING_TIME);

        if (CollectionUtils.isEmpty(configLogs)) {
            return;
        }
        amazonMarketingConfigLogService.saveBatch(configLogs);
    }



    /**
     * 构建查询条件
     * 根据需求文档4.1查询条件设计和4.2查询逻辑实现
     *
     * @param searchEntity 查询条件实体
     * @return 查询包装器
     */
    private LambdaQueryWrapper<AmazonLinkManagementConfig> buildQueryWrapper(AmazonLinkManagementConfigRequest searchEntity) {
        LambdaQueryWrapper<AmazonLinkManagementConfig> wrapper = new LambdaQueryWrapper<>();

        if (searchEntity != null) {
            // 规则名称: 模糊查询
            if (StringUtils.isNotBlank(searchEntity.getRuleName())) {
                wrapper.like(AmazonLinkManagementConfig::getRuleName, searchEntity.getRuleName());
            }

            // 店铺: 多店铺同时查询, accounts是JSON字段
            if (!CollectionUtils.isEmpty(searchEntity.getAccounts())) {
                String condition = "JSON_OVERLAPS((accounts->'$.accounts'), '[\"" + StringUtils.join(searchEntity.getAccounts(), "\",\"") + "\"]')";
                wrapper.apply(condition);
            }
            // 配置类型
            if (StringUtils.isNotBlank(searchEntity.getConfigType())) {
                wrapper.eq(AmazonLinkManagementConfig::getType, searchEntity.getConfigType());
            }

            // 启用状态
            if (searchEntity.getStatus() != null) {
                wrapper.eq(AmazonLinkManagementConfig::getStatus, searchEntity.getStatus());
            }

            // 创建人
            if (!StringUtils.isEmpty(searchEntity.getCreateBy())) {
                wrapper.in(AmazonLinkManagementConfig::getCreatedBy, searchEntity.getCreateBy());
            } else if (CollectionUtils.isNotEmpty(searchEntity.getCreateByList())) {
                wrapper.in(AmazonLinkManagementConfig::getCreatedBy, searchEntity.getCreateByList());
            }

            // 创建时间
            if (searchEntity.getCreateTimeStart() != null) {
                wrapper.ge(AmazonLinkManagementConfig::getCreatedTime, searchEntity.getCreateTimeStart());
            }
            if (searchEntity.getCreateTimeEnd() != null) {
                wrapper.le(AmazonLinkManagementConfig::getCreatedTime, searchEntity.getCreateTimeEnd());
            }
        }

        // 排序规则：默认按创建时间倒序排列，避免使用主键排序（TiDB优化）
        wrapper.orderByDesc(AmazonLinkManagementConfig::getCreatedTime);
        return wrapper;
    }
}
