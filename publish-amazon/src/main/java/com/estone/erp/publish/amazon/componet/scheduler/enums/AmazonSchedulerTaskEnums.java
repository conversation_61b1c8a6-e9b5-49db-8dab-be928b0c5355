package com.estone.erp.publish.amazon.componet.scheduler.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface AmazonSchedulerTaskEnums {


    @Getter
    @AllArgsConstructor
    enum TaskType {
        UPLOAD_LISTING_GPSR_INFO(1, "上传Listing GPSR信息"),
        UPDATE_LISTING_ORIGIN_COUNTRY(2, "修改在线列表原产地"),
        UPDATE_NEW_YEAR_HANDLING_TIME(3, "新年-发货时间"),
        UPDATE_NEW_YEAR_STOCK(4, "新年-库存"),
        OVER_LIMIT_STATISTICS_SKU(5, "超量SKU下架统计"),
        BATCH_OFFLINE_LISTING(6, "定时任务批量下架"),
        BATCH_UPDATE_LISTING_PRICE(7, "定时任务批量更新价格"),
        LINK_MANAGEMENT_UPDATE_HANDLING_TIME(8, "链接管理-修改备货期");

        private final int code;
        private final String desc;
    }
}
