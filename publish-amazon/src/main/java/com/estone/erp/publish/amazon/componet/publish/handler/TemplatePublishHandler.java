package com.estone.erp.publish.amazon.componet.publish.handler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.componet.publish.AbstractAmazonPublishExecutor;
import com.estone.erp.publish.amazon.componet.publish.AmazonPublishContext;
import com.estone.erp.publish.amazon.componet.publish.PublishExecutor;
import com.estone.erp.publish.amazon.componet.publish.enums.AmazonPublishTypeEnums;
import com.estone.erp.publish.amazon.componet.validation.AmazonTemplateValidationContext;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.enums.AmazonReportSolutionTypeEnum;
import com.estone.erp.publish.amazon.enums.AmazonTemplateTableEnum;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.system.product.ProductUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Amazon 模版刊登处理器
 *
 * <AUTHOR>
 * @date 2024-11-14 15:38
 */
@Slf4j
@Component
public class TemplatePublishHandler extends AbstractAmazonPublishExecutor implements PublishExecutor {

    /**
     * @param context
     * @return
     */
    @Override
    public ApiResult<String> execute(AmazonPublishContext context) {
        Integer templateId = context.getMessage().getTemplateId();
        AmazonTemplateBO amazonTemplateBO = getAmazonTemplateBO(templateId);
        if (amazonTemplateBO == null) {
            return ApiResult.newError("模板不存在");
        }
        context.setTemplate(amazonTemplateBO);
        // 数据校验
        ApiResult<String> validatePublishRule = validateTemplatePublishRule(context);
        if (!validatePublishRule.isSuccess()) {
            // 数据校验失败
            amazonTemplateBO.setPublishStatus(AmaoznPublishStatusEnum.PUBLISH_FAIL.getStatusCode());
            amazonTemplateBO.setStepTemplateStatus(false);
            amazonTemplateService.update(amazonTemplateBO);
            return validatePublishRule;
        }

        // 执行刊登
        ApiResult<String> publishResult = publishTemplate(context);
        log.info("模板刊登:{}", publishResult);
        return publishResult;
    }


    @Override
    public String getType() {
        return AmazonPublishTypeEnums.TEMPLATE.name();
    }

    public AmazonTemplateBO getAmazonTemplateBO(Integer templateId) {
        return amazonTemplateService.selectBoById(templateId, AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
    }

    /**
     * 模板校验
     * 过滤重复刊登产品
     * 当sku为侵权产品或状态为停产、存档时，不允许刊登,店铺配置未配置sku的分类也不允许刊登
     * 如果产品全部侵权禁售或停产存档，则生成一条主sku的处理报告
     * 如果产品存在插头规格并不符合刊登站点则进行拦截，所有都不符合生成一条主SKU处理报告
     * 主管刊登分类次数拦截
     * 判定某些词汇不可写在标题首位
     * 检查ENA/UPC是否缺失
     */
    private ApiResult<String> validateTemplatePublishRule(AmazonPublishContext context) {
        AmazonTemplateBO template = context.getTemplate();

        // 模板状态校验
        if (AmaoznPublishStatusEnum.PUBLISH_SUCCESS.isTrue(template.getPublishStatus())) {
            return ApiResult.newError("模板已发布，请勿重复发布");
        }
        AmazonAccount account = context.getAccount();
        // 验证冠通刊登权限
        ApiResult<?> result = amazonTemplateService.checkAccountPublish2GT(template);
        if (!result.isSuccess()) {
            template.setReportSolutionId(496);
            template.setReportSolutionType(AmazonReportSolutionTypeEnum.ACCOUNT_CONFIG.getName());
            return ApiResult.newError(result.getErrorMsg());
        }

        // 过滤店铺配置未配置的分类sku
        ApiResult<?> checkCategoryIdResult = amazonTemplateBuilderHelper.checkCategoryId(template, true);
        if (!checkCategoryIdResult.isSuccess()) {
            template.setReportSolutionId(777);
            template.setReportSolutionType(AmazonReportSolutionTypeEnum.CATEGORY_MISS.getName());
            return ApiResult.newError(result.getErrorMsg());
        }

        // 店铺分类必填
        ApiResult<String> publishCategoryApiResult = amazonValidationHelper.validationAmazonPublishCategory(template);
        if (!publishCategoryApiResult.isSuccess()) {
            return publishCategoryApiResult;
        }

        // 非特供账号不能上架特供标签产品
        ApiResult<String> canPublishSpecialTagAccount = amazonValidationHelper.validationCanPublishSpecialTagAccount(template);
        if (!canPublishSpecialTagAccount.isSuccess()) {
            return canPublishSpecialTagAccount;
        }

        // 非EU-FBA账号不能上架EU-FBA标签产品
        ApiResult<String> canPublishEuFbaSpecialTagAccount = amazonValidationHelper.validationCanPublishEuFbaSpecialTagAccount(template);
        if (!canPublishEuFbaSpecialTagAccount.isSuccess()) {
            return canPublishEuFbaSpecialTagAccount;
        }


        // 过滤模版停产、存档 sku
        ApiResult<String> templateSkuStatus = amazonValidationHelper.filterTemplateSkuStatus(template);
        if (!templateSkuStatus.isSuccess()) {
            template.setReportSolutionId(717);
            template.setReportSolutionType(AmazonReportSolutionTypeEnum.PRODUCT_STATUS.getName());
            return templateSkuStatus;
        }

        // 插头规格校验
        List<String> allSku = AmazonTemplateUtils.getAllSku(template);
        ProductUtils.validationCountryOfPlugSpecification(allSku, template.getCountry());
        if (template.getSaleVariant()) {
            List<AmazonSku> amazonSkus = template.getAmazonSkus();
            amazonSkus.removeIf(amazonSku -> !allSku.contains(amazonSku.getSku()));
            if (CollectionUtils.isEmpty(amazonSkus)) {
                return ApiResult.newError("插头规格不符合当前站点，无可用子sku, 不允许刊登!");
            }
            template.setVariations(JSON.toJSONString(amazonSkus));
            template.setAmazonSkus(amazonSkus);
        }

        // 过滤重复刊登产品
        ApiResult<String> filterRepeatPublish = amazonTemplateRepeatPublishHelper.filterRepeatPublish(template);
        if (!filterRepeatPublish.isSuccess()) {
            template.setReportSolutionId(703);
            template.setReportSolutionType(AmazonReportSolutionTypeEnum.REPEAT_PUBLISH.getName());
            return filterRepeatPublish;
        }

        // 分类刊登次数拦截
        ApiResult<String> categoryPublishNumberResult = amazonValidationHelper.validationCategoryPublishNumber(template);
        if (!categoryPublishNumberResult.isSuccess()) {
            template.setReportSolutionId(704);
            template.setReportSolutionType(AmazonReportSolutionTypeEnum.CATEGORY_LIMIT.getName());
            return categoryPublishNumberResult;
        }

        // 判定某些词汇不可写在标题首位
        AmazonTemplateUtils.removeTitleFirstWord(template);

        // 生成/验证EAN/UPC
        generateTemplateProductCode(template, account);

        // ENA是否为空
        ApiResult<String> validationEANResult = amazonValidationHelper.validationEAN(template);
        if (!validationEANResult.isSuccess()) {
            template.setReportSolutionId(807);
            template.setReportSolutionType(AmazonReportSolutionTypeEnum.MISSING_EAN.getName());
            return validationEANResult;
        }
        // 验证模板必填属性
        AmazonTemplateValidationContext validationContext = new AmazonTemplateValidationContext(template);
        validationContext.setIsCheckAttr(false);
        amazonValidationHelper.validationTemplateData(validationContext);
        if (CollectionUtils.isNotEmpty(validationContext.getAdditionalProperties())) {
            // 默认属性中，可能存在非必须的附加属性，需要在刊登时移除
            context.setAdditionalProperties(validationContext.getAdditionalProperties());
        }
        return ApiResult.newSuccess();
    }


}


