package com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement.rule;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * Amazon链接管理配置 - 重量区间DTO
 * 对应设计文档3.4.2 rule字段结构中的weightRange
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Data
public class WeightRangeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 最小重量
     */
    private BigDecimal minWeight;

    /**
     * 最大重量
     */
    private BigDecimal maxWeight;
}