package com.estone.erp.publish.amazon.componet.marketing.offline;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class AmazonOfflineConfigFactory implements ApplicationContextAware, InitializingBean {
    private static final Map<String, AmazonOfflineConfigHandlerService> OFFLINE_CONFIG_MAP = new HashMap<>();
    
    private ApplicationContext context;
    
    public AmazonOfflineConfigHandlerService getService(String offlineType) {
        return OFFLINE_CONFIG_MAP.get(offlineType);
    }
    
    @Override
    public void afterPropertiesSet() throws Exception {
        context.getBeansOfType(AmazonOfflineConfigHandlerService.class)
                .values()
                .forEach(service -> OFFLINE_CONFIG_MAP.put(service.getOfflineType(), service));
    }
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }
} 