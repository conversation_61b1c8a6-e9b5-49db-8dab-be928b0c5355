package com.estone.erp.publish.amazon.componet;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.enums.InfringementEnums;
import com.estone.erp.publish.amazon.util.AmazonSpecialTagUtils;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProhibitionInfringementInfo;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.product.ProductInfringementForbiddenSaleUtils;
import com.estone.erp.publish.system.product.bean.ForbiddenAndSpecical;
import com.estone.erp.publish.system.product.bean.forbidden.InfringementSaleProhibitionVO;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.bean.forbidden.Sites;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonPublishOperationLog;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonPublishOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: Amazon禁售相关
 * ES-7464 【Amazon】参数配置配置了可刊登成人用品的店铺，只拦截管理单品，组合刊登禁售类型为：侵权类型，不拦截禁售类型为：平台规则，产品限售，物流规则（先做先发）
 * 可刊登成人用品的店铺，只拦截管理单品、组合刊登标记了amazon当前账号站点禁售，且禁售类型为：侵权类型
 * http://************:8080/browse/ES-7468
 * http://************:8080/browse/ES-7464
 * http://************:8080/browse/ES-10697
 */
@Slf4j
@Component
public class AmazonTemplateForbiddenSaleChannelHelper {

    /**
     * 检测是否是成人用品店铺,redis默认缓存2h
     * @return
     */
    public static boolean checkIsSystemConfigAccountNumber(String accountNumber){
        String paramValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON,"amazon_param","Adult_Products_Category_Not_Delete_Account",null);
        if(StringUtils.isNotBlank(paramValue)){
            String[] split = StringUtils.split(paramValue, ",");
            for (String account : split) {
                if(StringUtils.trim(account).equals(accountNumber)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 检测是否是亚马逊特供产品可刊登店铺,redis默认缓存2h
     * @return
     */
    public static boolean checkIsAmzSpecialGoodsAccountNumber(String accountNumber){
        String paramValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON,"amazon_param","AMZ_Special_Goods_Account",null);
        if(StringUtils.isNotBlank(paramValue)){
            String[] split = StringUtils.split(paramValue, ",");
            for (String account : split) {
                if(StringUtils.trim(account).equals(accountNumber)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 检测是否是亚马逊EU_FBA 产品可刊登店铺,redis默认缓存2h
     * @return
     */
    public static boolean checkIsEuFbaAccountNumber(String accountNumber){
        String paramValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON,"amazon_param","EU_FBA_Account",null);
        if(StringUtils.isNotBlank(paramValue)){
            String[] split = StringUtils.split(paramValue, ",");
            for (String account : split) {
                if(StringUtils.trim(account).equals(accountNumber)) {
                    return true;
                }
            }
        }
        return false;
    }

    private static boolean checkForbiden(List<InfringementSaleProhibitionVO> infringementSaleProhibitionVO,String site){
        return infringementSaleProhibitionVO.stream()
                .map(InfringementSaleProhibitionVO::getSalesProhibitionsVos)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(vo -> SaleChannelEnum.AMAZON.getChannelName().equals(vo.getPlat()))
                .map(SalesProhibitionsVo::getSites)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .map(Sites::getSite)
                .filter(StringUtils::isNotBlank)
                .anyMatch(siteCode -> StringUtils.equals(site, siteCode));
    }

    /**
     * 平台是否存在禁售,组合和套装 无特殊标签,成人用品店铺需要特殊判定
     * @param infringementSaleProhibition json格式禁售信息。
     */
    public static boolean isForbidChannelSuitOrComposeByAmazon(String infringementSaleProhibition,String accountNumber,String site){
        if (StringUtils.isBlank(infringementSaleProhibition) || StringUtils.isBlank(site) || StringUtils.isBlank(accountNumber)) {
            return false;
        }
        boolean match = false;
        if (checkIsSystemConfigAccountNumber(accountNumber)){
            // 是成人用品店铺
            List<InfringementSaleProhibitionVO> infringementSaleProhibitionVO = JSON.parseArray(infringementSaleProhibition, InfringementSaleProhibitionVO.class);
            infringementSaleProhibitionVO = infringementSaleProhibitionVO.stream()
                    .filter(saleProhibitionVO -> InfringementEnums.InfringementType.INFRINGEMENT.isTrue(saleProhibitionVO.getInfringementTypeName()))
                    .collect(Collectors.toList());
            match = checkForbiden(infringementSaleProhibitionVO,site);
            return match;
        }
        match = ProductInfringementForbiddenSaleUtils.isForbidChannelSite(SaleChannelEnum.AMAZON,site, infringementSaleProhibition,null);
        return match;
    }

    /**
     * 平台是否存在禁售,单品，成人用品店铺需要特殊判定
     * @param infringementSaleProhibition json格式禁售信息。
     */
    public static boolean isForbidChannelSingleItemEsByAmazon(String infringementSaleProhibition,String accountNumber,String site,List<Integer> specialGoodsTypes,List<String>  infringementTypeList){
        if (StringUtils.isBlank(infringementSaleProhibition) || StringUtils.isBlank(site) || StringUtils.isBlank(accountNumber)) {
            return false;
        }
        List<InfringementSaleProhibitionVO> infringementSaleProhibitionVO = JSON.parseArray(infringementSaleProhibition, InfringementSaleProhibitionVO.class);
        if (CollectionUtils.isNotEmpty(infringementTypeList) && CollectionUtils.isNotEmpty(infringementSaleProhibitionVO)){
            infringementSaleProhibitionVO = infringementSaleProhibitionVO.stream()
                    .filter(InfringementSaleProhibitionVO ->StringUtils.isNotBlank(InfringementSaleProhibitionVO.getInfringementTypeName()) && infringementTypeList.contains(InfringementSaleProhibitionVO.getInfringementTypeName())).collect(Collectors.toList());
        }

        boolean match = false;
        if (checkIsSystemConfigAccountNumber(accountNumber)){
            // 是成人用品店铺
            infringementSaleProhibitionVO = infringementSaleProhibitionVO.stream()
                    .filter(saleProhibitionVO -> InfringementEnums.InfringementType.INFRINGEMENT.isTrue(saleProhibitionVO.getInfringementTypeName()))
                    .collect(Collectors.toList());
            match = checkForbiden(infringementSaleProhibitionVO,site);
        }
        if (BooleanUtils.isFalse(match) && checkIsAmzSpecialGoodsAccountNumber(accountNumber)) {
            // 是AMZ特供店铺
            if (AmazonSpecialTagUtils.containsSpecialShopAllowedTag(specialGoodsTypes)) {
                // 指定特供标签产品 禁售-律所代理
                infringementSaleProhibitionVO = infringementSaleProhibitionVO.stream()
                        .filter(saleProhibitionVO -> InfringementEnums.InfringementType.INFRINGEMENT.isTrue(saleProhibitionVO.getInfringementTypeName()))
                        .filter(saleProhibitionVO -> InfringementEnums.InfringementWord.LAW_FIRMS_AGENT.isTrue(saleProhibitionVO.getInfringementObj()))
                        .collect(Collectors.toList());
                return CollectionUtils.isNotEmpty(infringementSaleProhibitionVO);
            } else {
                // 特殊标签包含 TG-KF
                return AmazonSpecialTagUtils.isForbiddenSpecialCodes(specialGoodsTypes);
            }

        }else if (!checkIsSystemConfigAccountNumber(accountNumber)){
            match = ProductInfringementForbiddenSaleUtils.isForbidChannelSite(SaleChannelEnum.AMAZON, site, infringementSaleProhibition,infringementTypeList);
        }
        return match;
    }


    /**
     * 检测 禁售侵权数据列表 货号 是不是禁售产品
     * @param bean
     * @param forbidTypeList
     * @return
     */
    public static ApiResult<Map<String, Boolean>> prohibitionListingcheckForbiddenSaleChannel(EsAmazonProhibitionInfringementInfo bean, List<String> forbidTypeList, boolean deleteListing) {
        if (CollectionUtils.isEmpty(forbidTypeList) || null == bean) {
            return ApiResult.newError("prohibitionListingcheckForbiddenSaleChannel 方法参数为空");
        }
        String sonsku = bean.getSku();
        String country = bean.getSite();
        String accountNumber = bean.getSaleAccount();
        String sellerSku = bean.getSellerSku();
        //  校验禁售
        Map<String, Boolean> checkSku = new HashMap<>();
        try {
            Map<String, ForbiddenAndSpecical> sonskuForbiddenAndSpecicalMap = ProductInfringementForbiddenSaleUtils.getForbiddenAndSpecicalBySonSku(new ArrayList<>(Arrays.asList(sonsku)));
            if (!sonskuForbiddenAndSpecicalMap.containsKey(sonsku)) {
                checkSku.put(sonsku, false);
                return ApiResult.newSuccess(checkSku);
            }
            // 平台是Amazon且站点对应上就认为禁售
            ForbiddenAndSpecical forbiddenAndSpecical = sonskuForbiddenAndSpecicalMap.get(sonsku);
            boolean match = AmazonTemplateForbiddenSaleChannelHelper.isForbidChannelSingleItemEsByAmazon(forbiddenAndSpecical.getInfringementSaleProhibition(), accountNumber, country, forbiddenAndSpecical.getSpecialGoodsTypes(), forbidTypeList);
            checkSku.put(sonsku, match);
            AmazonPublishOperationLogService amazonPublishOperationLogService = SpringUtils.getBean(AmazonPublishOperationLogService.class);
            if (deleteListing) {
                // 记录获取接口数据，以便跟踪数据延迟误下架
                AmazonPublishOperationLog amazonPublishOperationLog = new AmazonPublishOperationLog();
                amazonPublishOperationLog.setModId(sellerSku);
                amazonPublishOperationLog.setPlatform(country);
                String opType = deleteListing ? "SyncAmazonProhibitionListingDeleteJobHandler" : "SyncAmazonProhibitionListingNewJobHandler";
                amazonPublishOperationLog.setOpType(opType);
                amazonPublishOperationLog.setMetaObj(JSON.toJSONString(forbiddenAndSpecical));
                amazonPublishOperationLog.setObject(JSON.toJSONString(forbidTypeList));
                amazonPublishOperationLog.setState(match ? 1 : 0);
                amazonPublishOperationLog.setCreatedTime(new Timestamp(System.currentTimeMillis()));
                amazonPublishOperationLogService.insert(amazonPublishOperationLog);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ApiResult.newError("调用检查禁售方法报错：" + e.getMessage());
        }
        return ApiResult.newSuccess(checkSku);
    }

    /**
     * 检测货号是不是禁售产品
     * @param skuList
     * @param country
     * @return
     */
    public static ApiResult<Map<String, Boolean>> checkArticleNumberIsForbiddenSaleChannel(List<String> skuList, String country,String accountNumber) {
        if (CollectionUtils.isEmpty(skuList) && StringUtils.isBlank(country)) {
            return ApiResult.newError("checkArticleNumberIsForbiddenSaleChannel方法参数为空");
        }
        //  校验禁售
        Map<String, Boolean> checkSku = new HashMap<>();
        try {
            Map<String, ForbiddenAndSpecical> sonskuForbiddenAndSpecicalMap = ProductInfringementForbiddenSaleUtils.getForbiddenAndSpecicalBySonSku(skuList);
            for (String sku : skuList) {
                if (!sonskuForbiddenAndSpecicalMap.containsKey(sku)) {
                    checkSku.put(sku, false);
                    continue;
                }
                // 平台是Amazon且站点对应上就认为禁售
                ForbiddenAndSpecical forbiddenAndSpecical = sonskuForbiddenAndSpecicalMap.get(sku);
                boolean match = AmazonTemplateForbiddenSaleChannelHelper.isForbidChannelSingleItemEsByAmazon(forbiddenAndSpecical.getInfringementSaleProhibition(), accountNumber, country, forbiddenAndSpecical.getSpecialGoodsTypes(),Collections.emptyList());
                checkSku.put(sku, match);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ApiResult.newError("调用检查禁售方法报错：" + e.getMessage());
        }
        return ApiResult.newSuccess(checkSku);
    }

    /**
     * 检测模板是否是禁售产品,并过滤禁售产品
     * @param templates
     * @return
     */
    public ApiResult<List<AmazonTemplateBO>> checkIsForbiddenSaleChannelAmazonTemplateList(List<AmazonTemplateBO> templates) {

        List<AmazonTemplateBO> amazonTemplateBOList = new ArrayList<>();

        for (AmazonTemplateBO amazonTemplate : templates) {

            List<String> skuList = new ArrayList<>();
            if (!amazonTemplate.getSaleVariant()) {
                // 单体
                String spu = amazonTemplate.getParentSku();
                skuList.add(spu);
                ApiResult<Map<String, Boolean>> result = this.checkArticleNumberIsForbiddenSaleChannel(skuList, amazonTemplate.getCountry(),amazonTemplate.getSellerId());
                if (!result.isSuccess()) {
                    return ApiResult.newError(result.getErrorMsg());
                }
                Map<String, Boolean> checkSku = result.getResult();
                if (!checkSku.get(spu)) {
                    // 过滤禁售产品
                    amazonTemplateBOList.add(amazonTemplate);
                }
            } else {
                // 变体
                if (StringUtils.isBlank(amazonTemplate.getVariations()) && CollectionUtils.isEmpty(amazonTemplate.getAmazonSkus())) {
                    return ApiResult.newError("变体获取多属性值为空");
                }
                List<AmazonSku> amazonSkuList = JSON.parseArray(amazonTemplate.getVariations(), AmazonSku.class);
                if (CollectionUtils.isEmpty(amazonSkuList)) {
                    amazonSkuList = amazonTemplate.getAmazonSkus();
                }
                skuList = amazonSkuList.stream().map(amazonSku -> amazonSku.getSku()).collect(Collectors.toList());
                ApiResult<Map<String, Boolean>> result = this.checkArticleNumberIsForbiddenSaleChannel(skuList, amazonTemplate.getCountry(),amazonTemplate.getSellerId());
                if (!result.isSuccess()) {
                    return ApiResult.newError(result.getErrorMsg());
                }
                Map<String, Boolean> checkSku = result.getResult();

                // 如果sku禁售，过滤
                for (String sku : checkSku.keySet()) {
                    Boolean isInfringement = checkSku.get(sku);
                    if (isInfringement) {
                        amazonSkuList = amazonSkuList.stream().filter(a -> !a.getSku().equals(sku)).collect(Collectors.toList());
                    }
                }

                // 如果sku全部禁售，过滤掉整个spu
                if (amazonSkuList.size() != 0) {
                    amazonTemplate.setAmazonSkus(amazonSkuList);
                    amazonTemplate.setVariations(JSON.toJSONString(amazonSkuList));
                    amazonTemplateBOList.add(amazonTemplate);
                }
            }
        }
        return ApiResult.newSuccess(amazonTemplateBOList);
    }

}
