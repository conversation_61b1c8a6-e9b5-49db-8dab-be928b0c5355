package com.estone.erp.publish.amazon.jobHandler.marketing.linkmanage;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.publish.amazon.componet.scheduler.enums.AmazonSchedulerTaskEnums;
import com.estone.erp.publish.amazon.mq.AmazonQueues;
import com.estone.erp.publish.amazon.mq.model.AmazonSchedulerTaskJobMessage;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.platform.enums.DateGranularityEnum;
import com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement.AmazonLinkManagementConfigDTO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonLinkManagementConfig;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonLinkManagementTaskRecord;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonLinkManagementConfigService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonLinkManagementTaskRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 链接管理-修改备货期
 */
@Slf4j
@Component
public class AmazonHandingTimeLinkManageJobHandler extends AbstractJobHandler {

    @Autowired
    private AmazonLinkManagementConfigService linkManagementConfigService;
    @Autowired
    private AmazonLinkManagementTaskRecordService linkManagementTaskRecordService;
    @Autowired
    private RabbitMqSender rabbitMqSender;


    public AmazonHandingTimeLinkManageJobHandler() {
        super(AmazonHandingTimeLinkManageJobHandler.class.getName());
    }


    @Data
    private static class InnerParam {
        private List<Long> configIds;
        private List<String> accountNumber;
    }

    /**
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    @XxlJob("AmazonHandingTimeLinkManageJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            XxlJobLogger.log("参数错误, 请检查参数");
            return ReturnT.FAIL;
        }

        try {
            // 按时间段查询可以执行的配置,最近一小时内的配置
            LocalDateTime now = LocalDateTime.now();
            int currentHour = now.getHour();
            LocalDateTime startTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(currentHour, 0, 0));
            LocalDateTime endTime = LocalDateTime.of(LocalDate.now(), LocalTime.of(currentHour, 59, 59));
            XxlJobLogger.log("开始执行策略区间: {} - {}", startTime, endTime);

            // 查询 type = 1 (修改备货期) 的启用配置
            List<AmazonLinkManagementConfig> configs = getEnabledHandlingTimeConfigs(innerParam);
            if (CollectionUtils.isEmpty(configs)) {
                XxlJobLogger.log("未找到启用的修改备货期配置");
                return ReturnT.SUCCESS;
            }

            XxlJobLogger.log("找到 {} 个启用的修改备货期配置", configs.size());

            // 匹配执行频率
            Predicate<AmazonLinkManagementConfig> matchExecuteFrequency = frequencyConfig -> {
                String exeFrequency = frequencyConfig.getExeFrequency();
                if (StringUtils.isBlank(exeFrequency)) {
                    return false;
                }
                if (DateGranularityEnum.DAY.descEquals(exeFrequency)) {
                    return true;
                }
                if (StringUtils.isBlank(frequencyConfig.getExecuteDate())) {
                    return false;
                }
                String[] timeArray = frequencyConfig.getExecuteDate().split(",");
                return Arrays.stream(timeArray).anyMatch(time -> {
                    int execData = Integer.parseInt(time);
                    if (DateGranularityEnum.WEEK.descEquals(exeFrequency)) {
                        return LocalDate.now().getDayOfWeek().getValue() == execData;
                    }
                    if (DateGranularityEnum.MONTH.descEquals(exeFrequency)) {
                        return LocalDate.now().getDayOfMonth() == execData;
                    }
                    return false;
                });
            };

            // 执行策略
            for (AmazonLinkManagementConfig config : configs) {
                if (!matchExecuteFrequency.test(config)) {
                    XxlJobLogger.log("策略执行频率不匹配: {} - {}, 执行频率:{}, 执行日期:{}",
                            config.getId(), config.getRuleName(), config.getExeFrequency(), config.getExecuteDate());
                    continue;
                }

                // 匹配执行时间在当前时段内则执行
                String exeTime = config.getExeTime();
                if (StringUtils.isBlank(exeTime)) {
                    XxlJobLogger.log("策略执行时间为空: {} - {}", config.getId(), config.getRuleName());
                    continue;
                }

                LocalTime excTimeLocalTime = LocalTime.parse(exeTime);
                boolean matchTime = startTime.toLocalTime().compareTo(excTimeLocalTime) <= 0 && endTime.toLocalTime().compareTo(excTimeLocalTime) >= 0;
                if (!matchTime) {
                    XxlJobLogger.log("策略执行时间不在当前时段内: {} - {} ,执行时段：【{}-{}】 配置时间:{}",
                            config.getId(), config.getRuleName(), startTime, endTime, excTimeLocalTime);
                    continue;
                }

                XxlJobLogger.log("开始执行策略: {} - {}", config.getId(), config.getRuleName());
                processConfig(config, innerParam);
            }

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("执行链接管理-修改备货期任务失败", e);
            XxlJobLogger.log("执行任务失败: {}", e.getMessage());
            return ReturnT.FAIL;
        }
    }

    /**
     * 获取启用的修改备货期配置
     */
    private List<AmazonLinkManagementConfig> getEnabledHandlingTimeConfigs(InnerParam innerParam) {
        LambdaQueryWrapper<AmazonLinkManagementConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(innerParam.getConfigIds()), AmazonLinkManagementConfig::getId, innerParam.getConfigIds())
                .eq(AmazonLinkManagementConfig::getType, 1) // type = 1 为修改备货期
                .eq(AmazonLinkManagementConfig::getStatus, 1); // status = 1 为启用状态

        if (CollectionUtils.isNotEmpty(innerParam.getAccountNumber())) {
            // 店铺: 多店铺同时查询, accounts是JSON字段
            String condition = "JSON_OVERLAPS((accounts->'$.accounts'), '[\"" + StringUtils.join(innerParam.getAccountNumber(), "\",\"") + "\"]')";
            queryWrapper.apply(condition);
        }

        queryWrapper.nested(query -> query.and(childQuery ->
                        childQuery.le(AmazonLinkManagementConfig::getStrategyStartTime, LocalDate.now())
                                .ge(AmazonLinkManagementConfig::getStrategyEndTime, LocalDate.now()))
                .or(childQuery -> childQuery.le(AmazonLinkManagementConfig::getStrategyStartTime, LocalDate.now())
                        .isNull(AmazonLinkManagementConfig::getStrategyEndTime))
        );
        return linkManagementConfigService.list(queryWrapper);
    }

    /**
     * 处理单个配置
     */
    private void processConfig(AmazonLinkManagementConfig config, InnerParam innerParam) {
        try {
            XxlJobLogger.log("开始处理配置: {} - {}", config.getId(), config.getRuleName());

            // 转换为DTO以便获取解析后的账号列表
            AmazonLinkManagementConfigDTO configDTO = AmazonLinkManagementConfigDTO.fromEntity(config);
            List<String> accountList = configDTO.getAccountList();

            if (CollectionUtils.isEmpty(accountList)) {
                XxlJobLogger.log("配置 {} 没有关联的店铺账号", config.getId());
                return;
            }

            // 按店铺处理
            for (String accountNumber : accountList) {
                processAccountConfig(config, accountNumber);
            }

            XxlJobLogger.log("配置 {} 处理完成", config.getId());
        } catch (Exception e) {
            log.error("处理配置失败: configId={}", config.getId(), e);
            XxlJobLogger.log("处理配置失败: configId={}, error={}", config.getId(), e.getMessage());
        }
    }

    /**
     * 处理单个店铺的配置
     */
    private void processAccountConfig(AmazonLinkManagementConfig config, String accountNumber) {
        try {
            // 1. 检查是否存在更高优先级的配置需要执行
            if (hasHigherPriorityConfigForAccount(config, accountNumber)) {
                XxlJobLogger.log("店铺 {} 存在更高优先级配置需要执行，跳过当前配置: {} - {}",
                        accountNumber, config.getId(), config.getRuleName());
                return;
            }

            // 2. 记录执行日志到 AmazonLinkManagementTaskRecord
            AmazonLinkManagementTaskRecord taskRecord = createTaskRecord(config, accountNumber);
            linkManagementTaskRecordService.save(taskRecord);

            XxlJobLogger.log("为店铺 {} 创建任务记录: {}", accountNumber, taskRecord.getId());

            // 3. 发送消息至 AMAZON_TASK_JOB_SCHEDULING_QUEUE 队列
            sendTaskMessage(config, accountNumber, taskRecord);

            XxlJobLogger.log("为店铺 {} 发送任务消息成功", accountNumber);

        } catch (Exception e) {
            log.error("处理店铺配置失败: configId={}, accountNumber={}", config.getId(), accountNumber, e);
            XxlJobLogger.log("处理店铺配置失败: configId={}, accountNumber={}, error={}",
                    config.getId(), accountNumber, e.getMessage());
        }
    }

    /**
     * 检查指定店铺是否存在更高优先级的配置需要在当天执行
     *
     * @param currentConfig 当前配置
     * @param accountNumber 店铺账号
     * @return true-存在更高优先级配置需要在当天执行，false-不存在
     */
    private boolean hasHigherPriorityConfigForAccount(AmazonLinkManagementConfig currentConfig, String accountNumber) {
        try {
            // 1. 查询包含该店铺的所有启用配置
            InnerParam innerParam = new InnerParam();
            innerParam.setAccountNumber(List.of(accountNumber));
            List<AmazonLinkManagementConfig> accountConfigs = getEnabledHandlingTimeConfigs(innerParam);
            if (CollectionUtils.isEmpty(accountConfigs)) {
                return false;
            }

            // 2. 过滤出优先级高于当前配置的配置（level数值越小优先级越高）
            List<AmazonLinkManagementConfig> higherPriorityConfigs = accountConfigs.stream()
                    .filter(config -> !config.getId().equals(currentConfig.getId())) // 排除当前配置
                    .filter(config -> config.getLevel() < currentConfig.getLevel()) // 优先级更高
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(higherPriorityConfigs)) {
                return false;
            }

            // 3. 检查这些高优先级配置是否需要在当天执行
            for (AmazonLinkManagementConfig config : higherPriorityConfigs) {
                if (shouldExecuteConfigNow(config)) {
                    XxlJobLogger.log("发现更高优先级配置需要在当天执行: {} - {}, 优先级: {}, 当前配置优先级: {}",
                            config.getId(), config.getRuleName(), config.getLevel(), currentConfig.getLevel());
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            log.error("检查店铺优先级配置失败: accountNumber={}, currentConfigId={}",
                    accountNumber, currentConfig.getId(), e);
            // 出现异常时，为了安全起见，不跳过当前配置
            return false;
        }
    }


    /**
     * 判断配置是否应该在当天执行
     *
     * @param config 配置
     * @return true-应该执行，false-不应该执行
     */
    private boolean shouldExecuteConfigNow(AmazonLinkManagementConfig config) {
        try {
            // 1. 检查执行频率
            String exeFrequency = config.getExeFrequency();
            if (StringUtils.isBlank(exeFrequency)) {
                return false;
            }

            boolean frequencyMatch = false;
            if (DateGranularityEnum.DAY.descEquals(exeFrequency)) {
                frequencyMatch = true;
            } else if (StringUtils.isNotBlank(config.getExecuteDate())) {
                String[] timeArray = config.getExecuteDate().split(",");
                frequencyMatch = Arrays.stream(timeArray).anyMatch(time -> {
                    int execData = Integer.parseInt(time);
                    if (DateGranularityEnum.WEEK.descEquals(exeFrequency)) {
                        return LocalDate.now().getDayOfWeek().getValue() == execData;
                    }
                    if (DateGranularityEnum.MONTH.descEquals(exeFrequency)) {
                        return LocalDate.now().getDayOfMonth() == execData;
                    }
                    return false;
                });
            }

            if (!frequencyMatch) {
                return false;
            }

            // 2. 检查是否配置了执行时间（有执行时间就认为当天需要执行）
            String exeTime = config.getExeTime();
            if (StringUtils.isBlank(exeTime)) {
                return false;
            }

            // 只要频率匹配且有执行时间，就认为当天需要执行
            return true;

        } catch (Exception e) {
            log.error("判断配置执行时间失败: configId={}", config.getId(), e);
            return false;
        }
    }

    /**
     * 创建任务记录
     */
    private AmazonLinkManagementTaskRecord createTaskRecord(AmazonLinkManagementConfig config, String accountNumber) {
        AmazonLinkManagementTaskRecord record = new AmazonLinkManagementTaskRecord();
        record.setConfigId(config.getId());
        record.setAccountNumber(accountNumber);
        record.setConfigLevel(config.getLevel());
        record.setExecuteDate(LocalDate.now());
        record.setExecuteTime(LocalDateTime.now());
        record.setExecuteStatus(0); // 0-待处理
        record.setCreatedTime(LocalDateTime.now());
        return record;
    }

    /**
     * 发送任务消息到队列
     */
    private void sendTaskMessage(AmazonLinkManagementConfig config, String accountNumber, AmazonLinkManagementTaskRecord taskRecord) {
        try {
            // 构建消息数据
            AmazonLinkManagementHandlingTimeMessage messageData = new AmazonLinkManagementHandlingTimeMessage();
            messageData.setConfigId(config.getId());
            messageData.setAccountNumber(accountNumber);
            messageData.setTaskRecordId(taskRecord.getId());
            messageData.setConfigLevel(config.getLevel());

            // 创建调度任务消息
            AmazonSchedulerTaskJobMessage message = new AmazonSchedulerTaskJobMessage();
            message.setData(JSON.toJSONString(messageData));
            message.setScheduleTaskType(AmazonSchedulerTaskEnums.TaskType.LINK_MANAGEMENT_UPDATE_HANDLING_TIME.name());

            // 发送到队列
            rabbitMqSender.allPublishVHostRabbitTemplateSend(
                    PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE,
                    AmazonQueues.AMAZON_TASK_JOB_SCHEDULING_QUEUE_KEY,
                    message
            );

        } catch (Exception e) {
            log.error("发送任务消息失败: configId={}, accountNumber={}", config.getId(), accountNumber, e);
            throw new RuntimeException("发送任务消息失败", e);
        }
    }

    /**
     * 任务消息数据类
     */
    @Data
    public static class AmazonLinkManagementHandlingTimeMessage {
        private Long configId;
        private String accountNumber;
        private Long taskRecordId;
        private Integer configLevel;
    }
}
