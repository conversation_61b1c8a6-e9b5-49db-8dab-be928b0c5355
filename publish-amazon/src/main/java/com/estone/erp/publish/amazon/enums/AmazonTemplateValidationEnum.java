package com.estone.erp.publish.amazon.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模板校验错误码
 *
 * <AUTHOR>
 * @date 2023-08-24 17:18
 */
@Getter
@AllArgsConstructor
public enum AmazonTemplateValidationEnum {
    CAN_NOT_SKU_STATUS(4001,"不可刊登SKU状态"),
    DISABLE_COMPOSE_PRODUCE(4002,"禁用的组合套装"),
    SITE_BAN(4003,"站点禁售"),
    ACCOUNT_CAN_NOT_PUBLISH_CAT(4004,"店铺不可刊登分类"),
    REPEAT_PUBLISH(4005,"重复刊登"),
    NOT_SALE_OR_BAD_ASIN(4006,"存在asin状态为内容不完整或不可售的数据"),
    INCLUDE_INFRINGING_WORD(4007,"存在侵权词"),
    BAD_TEMPLATE_STATUS(4008,"模板状态不可用"),
    NOT_APPLICABLE_PLUG_SPECIFICATION(4009,"不适用的插头规格"),
    INSUFFICIENT_PUBLISH_CATEGORY(40010,"刊登分类次数不足"),
    EAN_EMPTY(40011, "EAN码缺失"),
    PRODUCT_TYPE_MISS(40012, "分类类型缺失,请重新选择分类类型"),
    PRODUCT_PROPERTY_MISS(40013, "属性缺失,请重新补充完整属性"),
    CAN_NOT_PUBLISH_SPECIAL_TAG_ACCOUNT(40014, "不可刊登特殊标签产品"),
    CAN_NOT_PUBLISH_EU_FBA_ACCOUNT(40015, "不可刊登EU-FBA标签产品"),
    ;

    private final int code;
    private final String desc;


    public boolean isTrue(Integer val) {
        if (val == null) {
            return false;
        }
        return this.code == val;
    }
}
