package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * amazon市场更改配置日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("amazon_marketing_config_log")
public class AmazonMarketingConfigLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 配置id
     */
    private Long marketingId;

    /**
     * 1 上架配置
     */
    private Integer type;

    /**
     * 操作的属性
     */
    private String operateAttr;

    /**
     * 操作的属性描述
     */
    private String operateAttrDesc;

    /**
     * 改前值
     */
    private String previousValue;

    /**
     * 改后值
     */
    private String afterValue;

    /**
     * 操作者
     */
    private String operator;

    /**
     * 操作时间
     */
    private LocalDateTime operateTime;


}
