package com.estone.erp.publish.tidb.publishtidb.componet;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.nacos.common.utils.MapUtil;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.WordProhibitionVO;
import com.estone.erp.publish.tidb.publishtidb.domain.TrademarkWordWhitelistSaveDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.excel.TrademarkWordWhitelistExcel;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonTrademarkWordWhitelistService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AmazonTrademarkWordWhitelistHelper {

    @Resource
    private AmazonTrademarkWordWhitelistService amazonTrademarkWordWhitelistService;


    /**
     * 查询侵权词汇详细信息
     * left:商标词标识-trademarkIdentification
     * middle:禁售站点-prohibitionSite
     * right:禁售平台-forbidChannel
     *
     * @param infringementWords
     * @return
     */
    public Map<String, Triple<String, String, String>> getInfringementWordTripleMap(List<String> infringementWords) {
        Map<String, Triple<String, String, String>> resultMap = new HashMap<>();

        Map<String, WordProhibitionVO> prohibitionVOMap = ProductUtils.queryProhibitionByWords(infringementWords);

        infringementWords.forEach(word -> {
            String wordUpperCase = StringUtils.upperCase(word);
            WordProhibitionVO prohibitionVO = prohibitionVOMap.get(wordUpperCase);
            if (null != prohibitionVO) {
                resultMap.put(wordUpperCase, Triple.of(prohibitionVO.getTrademarkIdentification(), prohibitionVO.getProhibitionSite(), prohibitionVO.getForbidChannel()));
            }
        });

        return resultMap;
    }


    /**
     * 批量添加商标词白名单
     *
     * @param whitelistMap 白名单映射，key为侵权词，value为站点列表
     */
    public void addWhitelists(Map<String, List<String>> whitelistMap, Boolean belongImportData) {
        if (MapUtil.isEmpty(whitelistMap)) {
            return;
        }

        // 获取侵权词详细信息映射
        Map<String, Triple<String, String, String>> wordTripleMap =
                getInfringementWordTripleMap(new ArrayList<>(whitelistMap.keySet()));

        // 遍历处理每个侵权词
        whitelistMap.forEach((word, sites) -> {
            TrademarkWordWhitelistSaveDTO saveDTO = buildSaveDTO(word, sites, wordTripleMap, belongImportData);
            amazonTrademarkWordWhitelistService.addWhitelist(saveDTO);
        });
    }

    /**
     * 构建保存DTO对象
     *
     * @param infringementWord 侵权词
     * @param siteList         站点列表
     * @param wordTripleMap    侵权词详细信息映射
     * @return 保存DTO对象
     */
    private TrademarkWordWhitelistSaveDTO buildSaveDTO(String infringementWord,
                                                       List<String> siteList,
                                                       Map<String, Triple<String, String, String>> wordTripleMap, Boolean belongImportData) {
        // 处理站点列表：如果包含空值则设为null表示全站点
        List<String> processedSites = processSiteList(siteList);

        // 获取侵权词详细信息
        Triple<String, String, String> wordTriple = wordTripleMap.getOrDefault(
                StringUtils.upperCase(infringementWord), Triple.of(null, null, null));

        // 构建并返回DTO
        TrademarkWordWhitelistSaveDTO saveDTO = new TrademarkWordWhitelistSaveDTO();
        saveDTO.setInfringementWord(infringementWord);
        saveDTO.setSiteList(processedSites);
        saveDTO.setTrademarkIdentification(wordTriple.getLeft());
        saveDTO.setProhibitionSite(wordTriple.getMiddle());
        saveDTO.setForbidChannel(wordTriple.getRight());
        saveDTO.setBelongImportData(belongImportData);
        return saveDTO;
    }

    /**
     * 处理站点列表：如果包含空值则返回null表示全站点
     *
     * @param siteList 原始站点列表
     * @return 处理后的站点列表
     */
    private List<String> processSiteList(List<String> siteList) {
        if (CollectionUtils.isEmpty(siteList)) {
            return siteList;
        }

        // 如果站点列表中存在空值，则返回null表示全站点
        boolean hasBlankSite = siteList.stream().anyMatch(StringUtils::isBlank);
        return hasBlankSite ? null : siteList;
    }

    public ApiResult<String> importData(MultipartFile file) {
        List<TrademarkWordWhitelistExcel> dataList = Lists.newArrayList();
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        try {
            EasyExcel.read(file.getInputStream(), TrademarkWordWhitelistExcel.class, new AnalysisEventListener<TrademarkWordWhitelistExcel>() {
                @Override
                public void invoke(TrademarkWordWhitelistExcel rowData, AnalysisContext analysisContext) {
                    dataList.add(rowData);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    DataContextHolder.setUsername(currentUser);
                    Map<String, List<TrademarkWordWhitelistExcel>> excelMap = dataList.stream().collect(Collectors.groupingBy(TrademarkWordWhitelistExcel::getInfringementWord));

                    if (MapUtil.isEmpty(excelMap)) {
                        return;
                    }

                    Map<String, List<String>> whitelistMap = new HashMap<>();

                    excelMap.forEach((key, value) -> {
                        // 去重
                        List<String> siteList = value.stream()
                                .map(TrademarkWordWhitelistExcel::getSite).distinct().collect(Collectors.toList());
                        whitelistMap.put(key, siteList);
                    });
                    addWhitelists(whitelistMap, true);
                }
            }).sheet().doRead();
            return ApiResult.newSuccess();
        } catch (IOException e) {
            return ApiResult.newError(e.getMessage());
        }
    }
}
