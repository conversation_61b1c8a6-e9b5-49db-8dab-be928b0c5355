package com.estone.erp.publish.amazon.jobHandler.offline;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.amazon.enums.AmazonListingitemtypeEnum;
import com.estone.erp.publish.amazon.enums.AmazonOfflineEnums;
import com.estone.erp.publish.amazon.model.dto.DeleteAmazonListingDto;
import com.estone.erp.publish.amazon.util.AmazonSpecialTagUtils;
import com.estone.erp.publish.amazon.util.DeleteAmazonListingUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.bean.SpecialGoods;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * 特殊标签下架
 * - 非特供账号上架特供标签产品后会自动下架
 * - 非EU-FBA店铺上架EU-FBA产品，系统自动下架
 */
@Slf4j
@Component
public class AmazonSpecialTypeCodeOfflineJobHandler extends AbstractJobHandler {
    private static final String EXECUTE_AMAZON_SPECIALTYPE_CODE_OFFLINE_TYPE = "executeAmazonSpecialTypeCodeOffline";
    private static final String EXECUTE_AMAZON_EU_FBA_CODE_OFFLINE_TYPE = "executeAmazonEuFbaCodeOffline";


    @Autowired
    private EsAmazonProductListingService esAmazonProductListingService;
    @Autowired
    private SystemParamService systemParamService;
    @Autowired
    private SingleItemEsService singleItemEsService;

    public AmazonSpecialTypeCodeOfflineJobHandler() {
        super(AmazonSpecialTypeCodeOfflineJobHandler.class.getName());
    }


    @Data
    static class InnerParam {
        private List<String> accountNumbers;
        private List<String> sellerSkuList;
        private List<String> typeList;
    }

    @Override
    @XxlJob("amazonSpecialTypeCodeOfflineJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            return ReturnT.SUCCESS;
        }

        try {
            // 特供标签产品下架处理
            amazonSpecialTypeCodeCodeOffline(innerParam);
        } catch (Exception e) {
            XxlJobLogger.log(e.getMessage());
        }

        try {
            //EU FBA标签产品下架处理
            amazonEuFbaCodeOffline(innerParam);
        } catch (Exception e) {
            XxlJobLogger.log(e.getMessage());
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 将EsAmazonProductListing转换为AmazonProductListing
     */
    private List<AmazonProductListing> convertToAmazonProductListing(List<EsAmazonProductListing> esListings) {
        return esListings.stream().map(esListing -> {
            AmazonProductListing listing = new AmazonProductListing();
            listing.setAccountNumber(esListing.getAccountNumber());
            listing.setSite(esListing.getSite());
            listing.setSellerSku(esListing.getSellerSku());
            listing.setParentAsin(esListing.getParentAsin());
            listing.setSonAsin(esListing.getSonAsin());
            listing.setMainSku(esListing.getMainSku());
            listing.setArticleNumber(esListing.getArticleNumber());
            listing.setIsOnline(esListing.getIsOnline());
            listing.setSkuStatus(esListing.getSkuStatus());
            listing.setSkuDataSource(esListing.getSkuDataSource());
            listing.setItemStatus(esListing.getItemStatus());
            listing.setItemType(esListing.getItemType());
            return listing;
        }).collect(Collectors.toList());
    }

    /**
     * 特供标签产品下架处理
     * 调用公共方法处理特供标签产品下架逻辑
     *
     * @param innerParam 内部参数对象，包含账号列表、SKU列表等信息
     * @throws Exception 处理过程中可能抛出的异常
     */
    public void amazonSpecialTypeCodeCodeOffline(InnerParam innerParam) throws Exception {
        amazonSpecialCodeOffline(innerParam, EXECUTE_AMAZON_SPECIALTYPE_CODE_OFFLINE_TYPE);
    }

    /**
     * EU FBA标签产品下架处理
     * 调用公共方法处理EU FBA标签产品下架逻辑
     *
     * @param innerParam 内部参数对象，包含账号列表、SKU列表等信息
     * @throws Exception 处理过程中可能抛出的异常
     */
    public void amazonEuFbaCodeOffline(InnerParam innerParam) throws Exception {
        amazonSpecialCodeOffline(innerParam, EXECUTE_AMAZON_EU_FBA_CODE_OFFLINE_TYPE);
    }

    /**
     * 亚马逊特殊标签产品下架公共方法
     * 根据传入的类型参数处理不同类型的特殊标签产品下架逻辑
     *
     * @param innerParam 内部参数对象，包含账号列表、SKU列表、类型列表等信息
     * @param type       处理类型，用于区分特供标签(EXECUTE_AMAZON_SPECIALTYPE_CODE_OFFLINE_TYPE)
     *                   或EU FBA标签(EXECUTE_AMAZON_EU_FBA_CODE_OFFLINE_TYPE)
     * @throws Exception 处理过程中可能抛出的异常
     */
    public void amazonSpecialCodeOffline(InnerParam innerParam, String type) throws Exception {
        // 检查类型列表是否包含当前处理类型
        List<String> typeList = innerParam.getTypeList();
        if (StringUtils.isBlank(type) || (CollectionUtils.isNotEmpty(typeList) && !typeList.contains(type))) {
            XxlJobLogger.log("无需处理类型：" + type);
            return;
        }

        // 根据类型初始化相关参数
        String logMessage = null;
        String paramKey = null;
        List<String> specialGoodsCodes = null;
        AmazonOfflineEnums.Type amazonOfflineEnums = null;

        if (EXECUTE_AMAZON_SPECIALTYPE_CODE_OFFLINE_TYPE.equals(type)) {
            // 特供标签相关配置
            paramKey = "AMZ_Special_Goods_Account";
            logMessage = "特供标签";
            specialGoodsCodes = AmazonSpecialTagUtils.getSpecialTagQueryString();
            amazonOfflineEnums = AmazonOfflineEnums.Type.OFFLINE_EXCLUSIVE_PRODUCTS;
        } else if (EXECUTE_AMAZON_EU_FBA_CODE_OFFLINE_TYPE.equals(type)) {
            // EU FBA标签相关配置
            paramKey = "EU_FBA_Account";
            logMessage = "EU-FBA标签";
            specialGoodsCodes = AmazonSpecialTagUtils.getEuFbaQueryString();
            amazonOfflineEnums = AmazonOfflineEnums.Type.OFFLINE_EUFBA_PRODUCTS;
        } else {
            // 未知类型，直接返回
            XxlJobLogger.log("未知的处理类型: {}", type);
            return;
        }

        // 获取特殊账号列表（特供账号或EU FBA账号）
        SystemParam systemParam = systemParamService.queryParamValue(SaleChannel.CHANNEL_AMAZON, "amazon_param", paramKey);
        final List<String> specialAccounts = new ArrayList<>();
        if (systemParam != null && StringUtils.isNotBlank(systemParam.getParamValue())) {
            specialAccounts.addAll(Arrays.asList(systemParam.getParamValue().split(",")));
        }
        XxlJobLogger.log("{}账号列表: {}", logMessage, specialAccounts);

        // 创建ES查询请求
        EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
        request.setSpecialGoodsCodes(specialGoodsCodes); // 设置特殊标签查询条件
        request.setIsOnline(true); // 只查询在线的listing
        request.setItemTypeList(List.of(
                AmazonListingitemtypeEnum.Vriant_Item.getStatusCode(),
                AmazonListingitemtypeEnum.Monomer_Item.getStatusCode()
        ));
        request.setFields(DeleteAmazonListingUtils.ES_AMAZON_PRODUCT_LISTING_FIELDS);

        // 设置账号过滤条件
        if (CollectionUtils.isNotEmpty(innerParam.getAccountNumbers())) {
            // 如果传入了账号列表，过滤掉特殊账号
            List<String> nonSpecialAccounts = innerParam.getAccountNumbers().stream()
                    .filter(acc -> !specialAccounts.contains(acc))
                    .collect(Collectors.toList());
            request.setAccountNumberList(nonSpecialAccounts);
        } else {
            // 否则排除所有特殊账号
            request.setExcludeAccountNumber(specialAccounts);
        }

        // 设置SKU过滤条件
        if (innerParam.getSellerSkuList() != null && !innerParam.getSellerSkuList().isEmpty()) {
            request.setSellerSkuList(innerParam.getSellerSkuList());
        }

        // 统计下架数量
        AtomicInteger offlineCount = new AtomicInteger(0);
        final String finalLogMessage = logMessage;
        final AmazonOfflineEnums.Type finalAmazonOfflineEnums = amazonOfflineEnums;

        // 执行分页查询并处理下架逻辑
        int totalCount = esAmazonProductListingService.scrollQueryExecutorTask(request, listingList -> {
            if (listingList != null && !listingList.isEmpty()) {
                // 查询产品系统中的特殊标签信息
                List<String> skus = listingList.stream()
                        .map(EsAmazonProductListing::getArticleNumber)
                        .collect(Collectors.toList());
                List<SingleItemEs> skuSpecialGoodsCodes = singleItemEsService.getSkuSpecialGoodsCode(skus);

                // 构建SKU到特殊标签的映射关系
                Map<String, List<SpecialGoods>> skuSpecialGoodsCodeMap = skuSpecialGoodsCodes.stream()
                        .filter(item -> StringUtils.isNotBlank(item.getSonSku()))
                        .filter(item -> CollectionUtils.isNotEmpty(item.getSpecialGoodsList()))
                        .collect(Collectors.toMap(
                                SingleItemEs::getSonSku,
                                SingleItemEs::getSpecialGoodsList,
                                (k1, k2) -> k2
                        ));

                // 过滤需要下架的listing
                List<EsAmazonProductListing> filteredListings = listingList.stream()
                        .filter(listing -> {
                            String articleNumber = listing.getArticleNumber();
                            List<SpecialGoods> specialGoods = skuSpecialGoodsCodeMap.get(articleNumber);

                            // 检查是否有特殊标签
                            if (CollectionUtils.isEmpty(specialGoods)) {
                                XxlJobLogger.log("account: {}, sellerSku: {}, sku: {}, listing特殊标签: {}, 无特殊标签",
                                        listing.getAccountNumber(), listing.getSellerSku(),
                                        listing.getArticleNumber(), listing.getSpecialGoodsCode());
                                return false;
                            }

                            // 根据类型检查是否匹配相应的特殊标签
                            boolean anyMatch = false;
                            if (EXECUTE_AMAZON_SPECIALTYPE_CODE_OFFLINE_TYPE.equals(type)) {
                                anyMatch = specialGoods.stream().anyMatch(specialGood ->
                                        AmazonSpecialTagUtils.isAmazonSpecialTag(specialGood.getSpecialType()));
                            } else if (EXECUTE_AMAZON_EU_FBA_CODE_OFFLINE_TYPE.equals(type)){
                                anyMatch = specialGoods.stream().anyMatch(specialGood ->
                                        AmazonSpecialTagUtils.isAmazonSpecialTagEUFBA(specialGood.getSpecialType()));
                            }

                            if (anyMatch) {
                                return true;
                            }

                            XxlJobLogger.log("account: {}, sellerSku: {}, sku: {}, 产品系统无{}过滤",
                                    listing.getAccountNumber(), listing.getSellerSku(),
                                    listing.getArticleNumber(), finalLogMessage);
                            return false;
                        })
                        .filter(listing -> !specialAccounts.contains(listing.getAccountNumber()))
                        .collect(Collectors.toList());

                // 执行批量下架操作
                if (!filteredListings.isEmpty()) {
                    List<AmazonProductListing> amazonProductListings = convertToAmazonProductListing(filteredListings);

                    // 创建下架请求对象
                    DeleteAmazonListingDto deleteAmazonListingDto = new DeleteAmazonListingDto();
                    deleteAmazonListingDto.setAmazonOfflineEnumType(finalAmazonOfflineEnums);
                    deleteAmazonListingDto.setUseDefaultRemark(true);

                    // 执行批量下架
                    DeleteAmazonListingUtils.systemBatchRetireProduct(amazonProductListings, deleteAmazonListingDto, null);

                    offlineCount.addAndGet(amazonProductListings.size());
                    XxlJobLogger.log("下架{}产品，批次数量: {}", finalLogMessage, amazonProductListings.size());
                }
            }
        });

        // 输出任务完成日志
        XxlJobLogger.log("{}产品下架任务完成，总查询数量: {}，下架数量: {}", finalLogMessage, totalCount, offlineCount.get());
        XxlJobLogger.log(String.format("%s产品下架任务完成，总查询数量: %d，下架数量: %d", finalLogMessage, totalCount, offlineCount.get()));
    }
}
