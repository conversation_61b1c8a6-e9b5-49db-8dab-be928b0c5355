package com.estone.erp.publish.tidb.publishtidb.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReviewSaveDTO {

    /**
     * 新品记录 recordId
     */
    private Integer recordId;

    /**
     * spu
     */
    private String spu;

    /**
     * 审核通过的长标题6
     */
    private String longTitle6Approved;

    /**
     * 审核通过的长标题7
     */
    private String longTitle7Approved;

    /**
     * 审核通过的长标题8
     */
    private String longTitle8Approved;

    /**
     * 审核通过的必选关键词
     */
    private String mustKeywordApproved;

}
