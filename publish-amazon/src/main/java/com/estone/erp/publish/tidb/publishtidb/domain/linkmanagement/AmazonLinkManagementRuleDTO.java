package com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement.rule.PriceRangeDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement.rule.SalesRangeDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement.rule.WeightRangeDTO;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * Amazon链接管理配置 - 规则JSON字段DTO
 * 对应设计文档3.4.2 rule字段结构
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class AmazonLinkManagementRuleDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 新备货期天数
     */
    private Integer newHandlingTime;

    /**
     * 防重复调整天数
     */
    private Integer updateLimitDay;

    /**
     * 价格区间
     */
    private PriceRangeDTO priceRange;

    /**
     * 卖家SKU列表
     */
    private List<String> sellerSkuList;

    /**
     * 重量区间
     */
    private WeightRangeDTO weightRange;

    /**
     * 产品标签列表
     */
    private List<String> productTags;

    /**
     * ASIN状态列表
     */
    private List<String> asinStatus;

    /**
     * 销量区间
     */
    private SalesRangeDTO salesRange;

    /**
     * 商品状态列表
     */
    private List<String> itemStatus;


    /**
     * 最后开启日期超过天数
     */
    private Integer lastOpenDateExceeding;


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}