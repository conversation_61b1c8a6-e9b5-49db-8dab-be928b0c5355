package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.estone.erp.common.annotation.NeedToLog;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * Amazon链接管理配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("amazon_link_management_config")
public class AmazonLinkManagementConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 配置类型：1-修改备货期
     */
    private Integer type;

    /**
     * 规则名称，配置规则的唯一标识名称
     */
    @NeedToLog("规则名称")
    private String ruleName;

    /**
     * 账户类型：1-指定账户，2-站点账户
     */
    @NeedToLog("适用类型")
    private Integer accountType;

    /**
     * 账户选项，存储站点信息如US,UK,DE等
     */
    @NeedToLog("适用选项")
    private String accountOption;

    /**
     * 适用店铺账号JSON数组
     */
    @JsonIgnore
    private String accounts;

    /**
     * 优先级，数字越小优先级越高，范围1-999
     */
    @NeedToLog("优先级")
    private Integer level;

    /**
     * 规则类型：1-价格区间，2-指定SKU，3-价格+重量+标签
     */
    @NeedToLog("规则类型")
    private Integer ruleType;

    /**
     * 规则配置JSON，包含筛选条件、执行策略、新备货期天数、防重复调整天数等
     */
    @JsonIgnore
    private String rule;

    /**
     * 启用状态：0-禁用，1-启用
     */
    @NeedToLog("启用状态")
    private Integer status;

    /**
     * 执行频率：day-每日，week-每周，month-每月
     */
    private String exeFrequency;

    /**
     * 执行日期
     */
    private String executeDate;
    /**
     * 执行时间，格式如：09:00
     */
    private String exeTime;

    /**
     * 策略开始生效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime strategyStartTime;

    /**
     * 策略结束时间，NULL表示永久有效
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime strategyEndTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;


}
