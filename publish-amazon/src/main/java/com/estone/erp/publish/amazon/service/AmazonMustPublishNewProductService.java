package com.estone.erp.publish.amazon.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProduct;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProductCriteria;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProductExample;
import com.estone.erp.publish.amazon.model.dto.AmazonMustPublishNewProductVO;
import com.estone.erp.publish.amazon.model.dto.AmazonMustPublishNewSpuSiteInfoDO;
import com.estone.erp.publish.amazon.model.request.RollbackFixPictureRequest;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;

import java.util.List;

/**
 * <AUTHOR> amazon_must_publish_new_product
 * 2023-10-20 16:23:30
 */
public interface AmazonMustPublishNewProductService {
    int countByExample(AmazonMustPublishNewProductExample example);

    CQueryResult<AmazonMustPublishNewProductVO> search(CQuery<AmazonMustPublishNewProductCriteria> cquery);

    List<AmazonMustPublishNewProduct> selectByExample(AmazonMustPublishNewProductExample example);

    AmazonMustPublishNewProduct selectByPrimaryKey(Integer id);

    int insert(AmazonMustPublishNewProduct record);

    int updateByPrimaryKeySelective(AmazonMustPublishNewProduct record);

    int updateByExampleSelective(AmazonMustPublishNewProduct record, AmazonMustPublishNewProductExample example);

    int deleteByPrimaryKey(List<Integer> ids);


    /**
     * 将siteInfo中的值横向扩展设置到对应的DB字段中
     * @param siteInfos    站点数据明细
     * @param sourceBean   dbBean
     */
    void setSitesColumnValue(List<AmazonMustPublishNewSpuSiteInfoDO> siteInfos, AmazonMustPublishNewProduct sourceBean);

    /**
     * 批量插入
     * @param beans
     */
    void batchInsert(List<AmazonMustPublishNewProduct> beans);

    List<String> listExistSpuList(List<String> spus);

    List<AmazonMustPublishNewSpuSiteInfoDO> initPublishAndBandStatus(SalesProhibitionsVo salesProhibitionsVo, List<String> enableSites);

    /**
     * 批量修改销售
     * @param request
     * @return
     */
    ApiResult<String> batchUpdateSale(AmazonMustPublishNewProductCriteria request);

    /**
     * 批量修改备注
     * @param request
     * @return
     */
    ApiResult<String> batchUpdateRemarks(AmazonMustPublishNewProductCriteria request);

    /**
     * 批量更新记录
     * @param records 记录
     */
    void batchUpdateByPrimaryKeySelective(List<AmazonMustPublishNewProduct> records);

    ApiResult<String> batchAuditPass(List<Integer> ids);

    ApiResult<String> rollbackFixPicture(List<RollbackFixPictureRequest> requests);

    /**
     * 修图完成后，确认发布
     *
     * @param spu spu
     * @return
     */
    ApiResult<String> psConfirmToPublish(String spu);

    /**
     * 图片审核通过
     *
     * @param ids
     * @return
     */
    ApiResult<String> imageAuditPass(List<Integer> ids);

    /**
     * 文案审核通过
     *
     * @param ids
     * @return
     */
    ApiResult<String> textAuditPass(List<Integer> ids);
}