package com.estone.erp.publish.amazon.componet.marketing.service;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.publish.amazon.mq.model.AmazonOfflineConfigMessage;
import com.estone.erp.publish.amazon.util.DeleteAmazonListingUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.elasticsearch.util.CheckOrderSalesTimeUtils;
import com.estone.erp.publish.system.order.OrderUtils;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.enums.OfflineConfigEnums;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.rule.BaseOfflineRuleConfig;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.rule.SaleCountRuleConfigDO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonOfflineConfigAccountReport;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonOfflineConfigListingQueue;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonOfflineConfigAccountReportService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonOfflineConfigDataStatisticsService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonOfflineConfigListingQueueService;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonOfflineConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-01-13 11:14
 */
@Slf4j
public abstract class AbstractOfflineConfigService {
    public static final String[] LISTING_FIELDS = {"id", "accountNumber", "site", "parentAsin", "sonAsin", "sellerSku",
            "mainSku", "articleNumber", "itemStatus", "isOnline", "itemType", "skuStatus", "skuDataSource",
            "categoryId", "order_num_total", "normalSale", "openDate", "firstOpenDate", "infringementObj",
            "infringementWord", "infringementTypename", "forbidChannel", "normalSale"};

    @Autowired
    protected AmazonOfflineConfigService amazonOfflineConfigService;
    @Autowired
    protected EsAmazonProductListingService esAmazonProductListingService;
    @Autowired
    protected AmazonOfflineConfigListingQueueService amazonOfflineConfigListingQueueService;
    @Autowired
    protected AmazonOfflineConfigAccountReportService amazonOfflineConfigAccountReportService;
    @Autowired
    protected AmazonOfflineConfigDataStatisticsService amazonOfflineConfigDataStatisticsService;


    /**
     * 过滤公共配置
     * 下架定时公共规则：
     * <p>1、过滤未授权到sp-api的账号，或者冻结账号，或者排除账号US-jhgk16748i6y<p/> <p>这个规则在执行店铺配置的时候已经过滤掉了，所以不需要再次过滤<p/>
     * <p>2、货号为 匹配不到货号 的不进行下架<p/>
     * <p>3、总销量写入时间不符合，不下架(1>如果查不到销量写入时间 可以下架:2>如果总销量写入时间为当天，可以下架:3>如果总销量为0，且销量写入时间和总销量写入时间为同一天，可以下架）<p/>
     * <p>4、排除FBAasin，不区分店铺，只要是FBA的asin，则不进行下架<p/>
     * <p>5、公司自注册sku，且是us站点，不下架(5AC407513,5AC407511-B)<p/>
     * <p>6、itemType 为空不下架(itemType 是空无法准确判定是否为父体，不下架)<p/>
     * <p>7、排除参数配置的sellersku，默认配置上附件表格的sellersku<p/>
     * <p>8、排除sellersku中包含 handmade的链接，不进行下架<p/>
     *
     * @param listingList 待下架的listing列表
     * @return 过滤后的listing列表
     */
    protected List<EsAmazonProductListing> filterPublishConfig(List<EsAmazonProductListing> listingList) {
        Set<String> fbaAsinSet = OrderUtils.getAllAsinCodeCache();
        return listingList.stream()
                .filter(listing -> !"匹配不到货号".equals(listing.getArticleNumber()))
                .filter(listing -> Objects.nonNull(listing.getItemType()))
                .filter(listing -> !listing.getSellerSku().contains("handmade"))
                .filter(listing -> !fbaAsinSet.contains(listing.getSonAsin()))
                .filter(listing -> !DeleteAmazonListingUtils.checkSelfRegisteredSku(listing.getArticleNumber(), listing.getSite()))
                .filter(listing -> !DeleteAmazonListingUtils.checkConfiguredSellerSku(listing.getSellerSku()))
                .filter(this::checkSaleTotalCountWriteTime)
                .collect(Collectors.toList());
    }


    /**
     * 校验ASIN销量
     *
     * @param listingList listing
     * @return
     */
    protected List<EsAmazonProductListing> filterAsinSaleCount(List<EsAmazonProductListing> listingList, List<Predicate<AmazonAsinSaleCountDO>> saleCountCompareRules) {
        List<String> sonAsinList = listingList.stream().map(EsAmazonProductListing::getSonAsin).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, AmazonAsinSaleCountDO> sonAsinSaleCountMap = esAmazonProductListingService.getSonAsinSaleCount(sonAsinList);
        return listingList.stream()
                .filter(listing -> {
                    AmazonAsinSaleCountDO asinSaleCountDO = sonAsinSaleCountMap.get(listing.getSonAsin());
                    if (asinSaleCountDO == null) {
                        return false;
                    }
                    return saleCountCompareRules.stream().allMatch(rule -> rule.test(asinSaleCountDO));
                })
                .collect(Collectors.toList());
    }

    /**
     * 转换成销量匹配规则
     *
     * @param saleCountRuleConfig 销量规则配置
     * @return 销量匹配规则
     */
    protected List<Predicate<AmazonAsinSaleCountDO>> transferToSaleCountCompareRules(SaleCountRuleConfigDO saleCountRuleConfig) {
        return amazonOfflineConfigService.transferToSaleCountCompareRules(saleCountRuleConfig);
    }


    /**
     * 检查总销量写入时间
     *
     * @param listing listing
     * @return true 可以下架，false 不可以下架
     */
    private boolean checkSaleTotalCountWriteTime(EsAmazonProductListing listing) {
        String id = StringUtils.join(List.of(SaleChannel.CHANNEL_AMAZON, listing.getAccountNumber(), listing.getSellerSku()), "_");
        return CheckOrderSalesTimeUtils.checkSaleTotalCountWriteTime(id);

    }

    protected EsAmazonProductListingRequest buildEsAmazonProductListingRequest(String accountNumber, BaseOfflineRuleConfig baseOfflineRuleConfig) {
        EsAmazonProductListingRequest request = new EsAmazonProductListingRequest();
        request.setFields(LISTING_FIELDS);
        if (StringUtils.isNotBlank(accountNumber)) {
            request.setAccountNumber(accountNumber);
        }
        request.setIsOnline(true);
        request.setItemTypeList(DeleteAmazonListingUtils.ITEM_TYPE_LIST);
        if (baseOfflineRuleConfig.getLastOpenDateExceeding() != null) {
            Integer lastOpenDateExceeding = baseOfflineRuleConfig.getLastOpenDateExceeding();
            LocalDateTime lastOpenDateTime = LocalDateTime.of(LocalDate.now().minusDays(lastOpenDateExceeding), LocalTime.MIN);
            String endOpenDate = LocalDateTimeUtil.format(lastOpenDateTime);
            // 查询店铺链接上架时间超过多少天的链接
            request.setEndFirstOrOpenDate(endOpenDate);
        }
        if (StringUtils.isNotBlank(baseOfflineRuleConfig.getAsinStatus())) {
            request.setItemStatus(baseOfflineRuleConfig.getAsinStatus());
        }

        SaleCountRuleConfigDO saleCount = baseOfflineRuleConfig.getSaleCount();
        if (saleCount == null) {
            // 销量配置为空默=总销量为空
            request.setIsSaleQuantityNull(true);
            request.setSaleQuantityBean("order_num_total");
            request.setToSaleQuantity(0L);
        } else {
            request.setSaleQuantityBean(saleCount.getType());
            if (saleCount.getFromSaleCount() == 0) {
                request.setIsSaleQuantityNull(true);
            }
            request.setFromSaleQuantity(Long.valueOf(saleCount.getFromSaleCount()));
            // 左闭右开 这里是用的小于等于所以-1
            long toSaleCount = saleCount.getToSaleCount() - 1;
            request.setToSaleQuantity(toSaleCount);
        }
        request.setOrderBy("createDate");
        request.setSequence("ASC");
        return request;
    }

    /**
     * 记录店铺下架报告
     */
    protected void addAccountReport(AmazonOfflineConfigMessage message, Integer percentage, Integer onlineCount, Integer offlineCount, Integer actualOfflineCount) {
        OfflineConfigEnums.OfflineType offlineType = OfflineConfigEnums.OfflineType.getByName(message.getOfflineType());
        if (offlineType == null) {
            throw new IllegalArgumentException(String.format("未找到对应的下架类型：%s", message.getOfflineType()));
        }

        AmazonOfflineConfigAccountReport accountReport = new AmazonOfflineConfigAccountReport();
        accountReport.setConfigId(message.getConfigId());
        accountReport.setConfigPercentage(percentage);
        accountReport.setAccountNumber(message.getAccountNumber());
        accountReport.setOnlineCount(onlineCount);
        accountReport.setOfflineCount(offlineCount);
        accountReport.setActualOfflineCount(actualOfflineCount);
        // 如果实际下架数量大于等于下架数量，则实际下架数量为下架数量
        if (offlineCount <= actualOfflineCount) {
            accountReport.setActualOfflineCount(offlineCount);
        }
        accountReport.setStatisticsDate(message.getConfirmedTime());
        accountReport.setTaskType(offlineType.getCode());
        accountReport.setStatus(1);
        accountReport.setExtraData(null);
        accountReport.setCreatedTime(message.getScheduleTime());
        accountReport.setUpdatedTime(LocalDateTime.now());
        amazonOfflineConfigAccountReportService.save(accountReport);
    }


    protected void setPublishQueueFiled(AmazonOfflineConfigListingQueue queue, EsAmazonProductListing listing, Map<String, AmazonAsinSaleCountDO> sonAsinSaleCountMap, AmazonOfflineConfigMessage message) {
        queue.setStatisticsDate(message.getConfirmedTime());
        queue.setSchedulingTime(message.getScheduleTime());
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put("infringementWord", Optional.ofNullable(listing.getInfringingBrandWord()).orElseGet(() -> ""));
        extraMap.put("infringementObj", Optional.ofNullable(listing.getInfringementObj()).orElseGet(() -> ""));
        extraMap.put("infringementTypename", Optional.ofNullable(listing.getInfringementTypename()).orElseGet(() -> ""));
        extraMap.put("forbidChannel", Optional.ofNullable(listing.getForbidChannel()).orElseGet(() -> ""));
        extraMap.put("normalSale", Optional.ofNullable(listing.getNormalSale()).orElseGet(() -> ""));
        AmazonAsinSaleCountDO amazonAsinSaleCountDO = sonAsinSaleCountMap.get(listing.getSonAsin());
        if (amazonAsinSaleCountDO != null) {
            queue.setSalesTotalCount(amazonAsinSaleCountDO.getSale_total_count());
            extraMap.put("saleCount", amazonAsinSaleCountDO);
        }
        queue.setExtraData(JSON.toJSONString(extraMap));
    }
}
