package com.estone.erp.publish.amazon.componet.marketing.offline.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.estone.erp.common.alert.logger.PublishLogger;
import com.estone.erp.common.alert.logger.PublishLoggerFactory;
import com.estone.erp.common.alert.policy.DefaultAlertPolicy;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.publish.amazon.componet.marketing.offline.AmazonOfflineConfigHandlerService;
import com.estone.erp.publish.amazon.mq.model.AmazonOfflineConfigMessage;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.AmazonOfflineConfigVO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.enums.OfflineConfigEnums;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.enums.OfflineQueueEnums;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.rule.ReservedCountRuleDO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonOfflineConfigListingQueue;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
public class ReservedCountOfflineService extends AbstractOfflineConfigService implements AmazonOfflineConfigHandlerService {
    private final PublishLogger logger = PublishLoggerFactory.getLogger(ReservedCountOfflineService.class);

    @Override
    public String getOfflineType() {
        return OfflineConfigEnums.OfflineType.RESERVED_COUNT.name();
    }

    @Override
    public ApiResult<String> executeOfflineConfig(AmazonOfflineConfigMessage message) {
        // 实现保留链接数下架逻辑
        try {
            Integer configId = message.getConfigId();
            String accountNumber = message.getAccountNumber();

            ApiResult<AmazonOfflineConfigVO> configVOApiResult = amazonOfflineConfigService.editConfig(configId);
            if (!configVOApiResult.isSuccess()) {
                return ApiResult.newError("查询配置失败：" + configId);
            }
            AmazonOfflineConfigVO configVO = configVOApiResult.getResult();
            if (configVO == null) {
                return ApiResult.newError("配置不存在");
            }
            message.setConfirmedTime(configVO.getConfig().getConfirmedTime());
            ReservedCountRuleDO reservedCountRule = configVO.getReservedCountRule();

            Integer lastOpenDateWithin = reservedCountRule.getLastOpenDateWithin();
            if (lastOpenDateWithin != null) {
                extractLastOpenDateWithin(configVO, message);
            } else {
                executeLastOpenDateExceeding(configVO, accountNumber, message);
            }
            return ApiResult.newSuccess(accountNumber + ",保留链接数下架处理完成");
        } catch (Exception e) {
            logger.errorForPolicy("店铺:{},执行下架配置【{}】异常,e:{}", getOfflineType(), new DefaultAlertPolicy(), message.getAccountNumber(), message.getConfigId(), e.getMessage(), e);
            return ApiResult.newError("执行保留链接数下架失败：" + e.getMessage());
        }
    }

    /**
     * 根据保留链接数规则，保留最新上架时间超过多少天的链接
     *
     * @param configVO      配置信息
     * @param accountNumber 店铺账号
     */
    private void executeLastOpenDateExceeding(AmazonOfflineConfigVO configVO, String accountNumber, AmazonOfflineConfigMessage message) {
        ReservedCountRuleDO reservedCountRule = configVO.getReservedCountRule();
        Integer lastOpenDateExceeding = reservedCountRule.getLastOpenDateExceeding();
        if (lastOpenDateExceeding == null) {
            return;
        }
        // 销量匹配规则
        List<Predicate<AmazonAsinSaleCountDO>> predicateList = transferToSaleCountCompareRules(reservedCountRule.getSaleCount());
        int taskTaskCode = OfflineConfigEnums.OfflineType.RESERVED_COUNT.getCode();

        EsAmazonProductListingRequest request = buildEsAmazonProductListingRequest(accountNumber, reservedCountRule);
        request.setIsSaleQuantityNull(null);
        request.setSaleQuantityBean(null);
        request.setToSaleQuantity(null);
        AtomicInteger matchedOfflineCount = new AtomicInteger();
        esAmazonProductListingService.scrollQueryExecutorTask(request, listings -> {
            try {
                listingHandlerExceeding(listings, reservedCountRule, predicateList, configVO, message, matchedOfflineCount);
            } catch (Exception e) {
                logger.error("店铺:{},执行下架配置【{}-{}】统计链接异常,e:{}", accountNumber, configVO.getConfig().getId(), configVO.getConfig().getRuleName(), e.getMessage(), e);
            }
        });

        // 保留链接数
        Integer reservedCount = reservedCountRule.getReservedCount();
        if (reservedCount == null) {
            reservedCount = 0;
        }

        // 获取当前店铺链接满足条件总数
        if (matchedOfflineCount.get() < reservedCount) {
            int addCount = reservedCount - matchedOfflineCount.get();
            logger.info("店铺:{},保留链接数量少于保留链接数:{},需补充:{}个链接", accountNumber, reservedCount, addCount);
            matchedOfflineCount.addAndGet(addCount);
            addPendingListingToReservedQueue(message, accountNumber, addCount);
        }

        // 查询店铺统计日期内待下架数量
        Integer waitOffLinkTotal = amazonOfflineConfigListingQueueService.getAccountOffCountByStatus(accountNumber, message.getConfirmedTime(), message.getScheduleTime(), OfflineQueueEnums.QueueStatusType.PENDING_OFFLINE.getCode(), OfflineConfigEnums.OfflineType.RESERVED_COUNT.getCode());

        // 获取店铺在售链接数据
        Long onlineListingNum = esAmazonProductListingService.getOnlineListingNum(List.of(accountNumber));
        Integer offlinePercentage = configVO.getConfig().getOfflinePercentage();
        double limitRatio = offlinePercentage / 100.0;
        // 下架链接总数量：在线 * limitRatio 向下取整
        double canOffLink = onlineListingNum * limitRatio;
        int needOfflineTotal = (int) Math.floor(canOffLink);
        logger.info("店铺:{},保留链接数量:{},在线链接数量:{},下架比例:{},待下架链接数量:{},可下架链接数量:{},需下架链接数量:{}", accountNumber, reservedCount, onlineListingNum, limitRatio, waitOffLinkTotal, canOffLink, needOfflineTotal);
        // 记录店铺处理报告
        addAccountReport(message, offlinePercentage, Math.toIntExact(onlineListingNum), waitOffLinkTotal, needOfflineTotal);

        // 检查下架数据记录表状态流转，
        amazonOfflineConfigDataStatisticsService.updateExecutionStatus(message.getConfigId());

        if (needOfflineTotal <= 0) {
            return;
        }
        // 按上架时间排序正序(旧->新),修改对应条数的待下架链接
        if (waitOffLinkTotal <= needOfflineTotal) {
            amazonOfflineConfigListingQueueService.updateOffLinkStatusOrderOpenTimeLimit(accountNumber, message.getConfirmedTime(), message.getScheduleTime(), taskTaskCode, OfflineQueueEnums.QueueStatusType.CAN_OFFLINE_STATUS.getCode(), waitOffLinkTotal);
        } else {
            amazonOfflineConfigListingQueueService.updateOffLinkStatusOrderOpenTimeLimit(accountNumber, message.getConfirmedTime(), message.getScheduleTime(), taskTaskCode, OfflineQueueEnums.QueueStatusType.CAN_OFFLINE_STATUS.getCode(), needOfflineTotal);
        }

    }

    private void listingHandlerExceeding(List<EsAmazonProductListing> listings, ReservedCountRuleDO reservedCountRule, List<Predicate<AmazonAsinSaleCountDO>> predicateList, AmazonOfflineConfigVO configVO, AmazonOfflineConfigMessage message, AtomicInteger matchedOfflineCount) {
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }

        Integer reservedCount = reservedCountRule.getReservedCount();
        List<String> sonAsinList = listings.stream().map(EsAmazonProductListing::getSonAsin).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, AmazonAsinSaleCountDO> sonAsinSaleCountMap = esAmazonProductListingService.getSonAsinSaleCount(sonAsinList);
        Function<EsAmazonProductListing, String> asinSaleCountGroupFunction = listing -> {
            String sonAsin = listing.getSonAsin();
            AmazonAsinSaleCountDO amazonAsinSaleCountDO = sonAsinSaleCountMap.get(sonAsin);
            if (amazonAsinSaleCountDO == null) {
                return Boolean.FALSE.toString();
            }
            boolean match = predicateList.stream().anyMatch(predicate -> predicate.test(amazonAsinSaleCountDO));
            if (match) {
                return Boolean.FALSE.toString();
            } else {
                return Boolean.TRUE.toString();
            }
        };

        List<AmazonOfflineConfigListingQueue> addQueueList = new ArrayList<>(listings.size());
        // 按ASIN总销量是否满足规则分组
        Map<String, List<EsAmazonProductListing>> asinMatchedGroup = listings.stream().collect(Collectors.groupingBy(asinSaleCountGroupFunction));
        asinMatchedGroup.forEach((matchType, listingDataList) -> {
            if (matchType.equals(Boolean.TRUE.toString()) && matchedOfflineCount.get() < reservedCount) {
                // 满足规则保留链接
                BiConsumer<EsAmazonProductListing, AmazonOfflineConfigListingQueue> func = (list, queue) -> {
                    queue.setStatus(OfflineQueueEnums.QueueStatusType.RESERVE_OFFLINE.getCode());
                    setQueueFiled(queue, list, reservedCountRule, sonAsinSaleCountMap, message);
                };
                List<AmazonOfflineConfigListingQueue> queues = amazonOfflineConfigListingQueueService.addListingToOffLinkQueue(listingDataList, configVO, func);
                matchedOfflineCount.addAndGet(queues.size());
                addQueueList.addAll(queues);
            }
            if (matchType.equals(Boolean.FALSE.toString())) {
                // 过滤下架定时公共规则
                List<EsAmazonProductListing> matchingList = filterPublishConfig(listingDataList);
                if (CollectionUtils.isEmpty(matchingList)) {
                    return;
                }
                // 不满足规则写入待下架链接
                BiConsumer<EsAmazonProductListing, AmazonOfflineConfigListingQueue> func = (list, queue) -> {
                    queue.setStatus(OfflineQueueEnums.QueueStatusType.PENDING_OFFLINE.getCode());
                    setQueueFiled(queue, list, reservedCountRule, sonAsinSaleCountMap, message);
                };
                List<AmazonOfflineConfigListingQueue> queues = amazonOfflineConfigListingQueueService.addListingToOffLinkQueue(matchingList, configVO, func);
                addQueueList.addAll(queues);
            }
        });
        if (CollectionUtils.isNotEmpty(addQueueList)) {
            amazonOfflineConfigListingQueueService.saveBatch(addQueueList, 300);
        }
    }

    /**
     * 根据保留链接数规则，保留最新上架时间超过多少天的链接
     *
     * @param accountNumber   店铺账号
     * @param reservedCount   添加保留链接数
     */
    private void addPendingListingToReservedInOpenDayQueue(LocalDateTime confirmedTime, LocalDateTime schedulingTime, LocalDateTime afterOpenDate, String accountNumber, int reservedCount) {
        LambdaUpdateWrapper<AmazonOfflineConfigListingQueue> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AmazonOfflineConfigListingQueue::getAccountNumber, accountNumber)
                .eq(AmazonOfflineConfigListingQueue::getTaskType, OfflineConfigEnums.OfflineType.RESERVED_COUNT.getCode())
                .eq(AmazonOfflineConfigListingQueue::getStatus, OfflineQueueEnums.QueueStatusType.PENDING_OFFLINE.getCode())
                .eq(AmazonOfflineConfigListingQueue::getSchedulingTime, schedulingTime)
                .eq(AmazonOfflineConfigListingQueue::getStatisticsDate, confirmedTime)
                .ge(AmazonOfflineConfigListingQueue::getOpenTime, afterOpenDate)
                .set(AmazonOfflineConfigListingQueue::getStatus, OfflineQueueEnums.QueueStatusType.RESERVE_OFFLINE.getCode())
                .set(AmazonOfflineConfigListingQueue::getRemark, "补充保留链接")
                .last("order by open_time desc LIMIT " + reservedCount);
        amazonOfflineConfigListingQueueService.update(updateWrapper);
    }

    /**
     * 根据保留链接数规则，保留最新上架时间超过多少天的链接
     *
     * @param accountNumber   店铺账号
     * @param reservedCount   添加保留链接数
     */
    private void addPendingListingToReservedQueue(AmazonOfflineConfigMessage message, String accountNumber, int reservedCount) {
        LambdaUpdateWrapper<AmazonOfflineConfigListingQueue> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(AmazonOfflineConfigListingQueue::getAccountNumber, accountNumber)
                .eq(AmazonOfflineConfigListingQueue::getTaskType, OfflineConfigEnums.OfflineType.RESERVED_COUNT.getCode())
                .eq(AmazonOfflineConfigListingQueue::getStatus, OfflineQueueEnums.QueueStatusType.PENDING_OFFLINE.getCode())
                .eq(AmazonOfflineConfigListingQueue::getStatisticsDate, message.getConfirmedTime())
                .eq(AmazonOfflineConfigListingQueue::getSchedulingTime, message.getScheduleTime())
                .set(AmazonOfflineConfigListingQueue::getStatus, OfflineQueueEnums.QueueStatusType.RESERVE_OFFLINE.getCode())
                .set(AmazonOfflineConfigListingQueue::getRemark, "补充保留链接")
                .last("order by open_time desc LIMIT " + reservedCount);
        amazonOfflineConfigListingQueueService.update(updateWrapper);
    }


    private void setQueueFiled(AmazonOfflineConfigListingQueue queue, EsAmazonProductListing listing, ReservedCountRuleDO reservedCountRule, Map<String, AmazonAsinSaleCountDO> sonAsinSaleCountMap, AmazonOfflineConfigMessage message) {
        queue.setTaskType(OfflineConfigEnums.OfflineType.RESERVED_COUNT.getCode());
        setPublishQueueFiled(queue, listing, sonAsinSaleCountMap, message);
        Map<String, ReservedCountRuleDO> ruleData = new HashMap<>();
        ReservedCountRuleDO listingRule = reservedCountRule.matchListingRule(listing, sonAsinSaleCountMap.get(listing.getSonAsin()));
        ruleData.put("rule", reservedCountRule);
        ruleData.put("listing", listingRule);
        queue.setRuleInfo(JSON.toJSONString(ruleData));
    }


    /**
     * 根据保留链接数规则，保留最新上架时间多少天内链接
     *
     * @param configVO      配置信息
     */
    private void extractLastOpenDateWithin(AmazonOfflineConfigVO configVO, AmazonOfflineConfigMessage message) {
        ReservedCountRuleDO reservedCountRule = configVO.getReservedCountRule();
        Integer lastOpenDateWithin = reservedCountRule.getLastOpenDateWithin();
        if (lastOpenDateWithin == null) {
            return;
        }
        String accountNumber = message.getAccountNumber();
        LocalDateTime scheduleTime = message.getScheduleTime();
        LocalDateTime confirmedTime = message.getConfirmedTime();
        // 日期
        LocalDateTime afterOpenDateTime = LocalDateTime.of(LocalDate.now().minusDays(lastOpenDateWithin), LocalTime.MIN);
        logger.info("店铺:{},查询最新上架时间{}天内, 在[{}]之后的链接", accountNumber, lastOpenDateWithin, afterOpenDateTime);
        int taskTaskCode = OfflineConfigEnums.OfflineType.RESERVED_COUNT.getCode();
        Integer reservedCount = reservedCountRule.getReservedCount();
        // 销量匹配规则
        List<Predicate<AmazonAsinSaleCountDO>> predicateList = transferToSaleCountCompareRules(reservedCountRule.getSaleCount());

        // 查询X天内符合条件的链接
        AtomicInteger matchedOfflineCount = new AtomicInteger();
        EsAmazonProductListingRequest request = buildEsAmazonProductListingRequest(accountNumber, reservedCountRule);
        request.setIsSaleQuantityNull(null);
        request.setSaleQuantityBean(null);
        request.setToSaleQuantity(null);
        esAmazonProductListingService.scrollQueryExecutorTask(request, listings -> {
            try {
                listingHandlerIn(listings, reservedCountRule, predicateList, configVO, message, matchedOfflineCount, reservedCount, afterOpenDateTime);
            } catch (Exception e) {
                logger.error("店铺:{},保留最新上架时间{}天内的链接处理失败", accountNumber, lastOpenDateWithin, e);
            }
        });

        // 获取当前店铺链接满足条件总数
        if (matchedOfflineCount.get() < reservedCount) {
            int addCount = reservedCount - matchedOfflineCount.get();
            logger.info("店铺:{},保留链接数量少于保留链接数:{},需补充:{}个链接", accountNumber, reservedCount, addCount);
            matchedOfflineCount.addAndGet(addCount);
            addPendingListingToReservedInOpenDayQueue(confirmedTime, scheduleTime, afterOpenDateTime, accountNumber, addCount);
        }

        // 查询店铺统计日期内待下架数量
        Integer waitOffLinkTotal = amazonOfflineConfigListingQueueService.getAccountOffCountByStatus(accountNumber, confirmedTime, scheduleTime, OfflineQueueEnums.QueueStatusType.PENDING_OFFLINE.getCode(), OfflineConfigEnums.OfflineType.RESERVED_COUNT.getCode());

        // 获取店铺在售链接数据
        Long onlineListingNum = esAmazonProductListingService.getOnlineListingNum(List.of(accountNumber));
        Integer offlinePercentage = configVO.getConfig().getOfflinePercentage();
        double limitRatio = offlinePercentage / 100.0;
        // 下架链接总数量：在线 * limitRatio 向下取整
        double canOffLink = onlineListingNum * limitRatio;
        int needOfflineTotal = (int) Math.floor(canOffLink);
        logger.info("店铺:{},保留链接数量:{},在线链接数量:{},下架比例:{},待下架链接数量:{},可下架链接数量:{},需下架链接数量:{}", accountNumber, reservedCount, onlineListingNum, limitRatio, waitOffLinkTotal, canOffLink, needOfflineTotal);
        // 记录店铺处理报告
        addAccountReport(message, offlinePercentage, Math.toIntExact(onlineListingNum), waitOffLinkTotal, needOfflineTotal);

        // 检查下架数据记录表状态流转，
        amazonOfflineConfigDataStatisticsService.updateExecutionStatus(message.getConfigId());

        if (needOfflineTotal <= 0) {
            return;
        }
        // 按上架时间排序正序(旧->新),修改对应条数的带下架链接
        if (waitOffLinkTotal <= needOfflineTotal) {
            amazonOfflineConfigListingQueueService.updateOffLinkStatusOrderOpenTimeLimit(accountNumber, confirmedTime, scheduleTime, taskTaskCode, OfflineQueueEnums.QueueStatusType.CAN_OFFLINE_STATUS.getCode(), waitOffLinkTotal);
        } else {
            amazonOfflineConfigListingQueueService.updateOffLinkStatusOrderOpenTimeLimit(accountNumber, confirmedTime, scheduleTime, taskTaskCode, OfflineQueueEnums.QueueStatusType.CAN_OFFLINE_STATUS.getCode(), needOfflineTotal);
        }
    }

    private void listingHandlerIn(List<EsAmazonProductListing> listings, ReservedCountRuleDO reservedCountRule, List<Predicate<AmazonAsinSaleCountDO>> predicateList, AmazonOfflineConfigVO configVO, AmazonOfflineConfigMessage message, AtomicInteger matchedOfflineCount, Integer reservedCount, LocalDateTime afterOpenDateTime) {
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        List<String> sonAsinList = listings.stream().map(EsAmazonProductListing::getSonAsin).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, AmazonAsinSaleCountDO> sonAsinSaleCountMap = esAmazonProductListingService.getSonAsinSaleCount(sonAsinList);
        Function<EsAmazonProductListing, String> asinSaleCountGroupFunction = listing -> {
            String sonAsin = listing.getSonAsin();
            AmazonAsinSaleCountDO amazonAsinSaleCountDO = sonAsinSaleCountMap.get(sonAsin);
            if (amazonAsinSaleCountDO == null) {
                return Boolean.FALSE.toString();
            }
            boolean match = predicateList.stream().anyMatch(predicate -> predicate.test(amazonAsinSaleCountDO));
            if (match) {
                return Boolean.FALSE.toString();
            } else {
                return Boolean.TRUE.toString();
            }
        };

        List<AmazonOfflineConfigListingQueue> addQueueList = new ArrayList<>(listings.size());
        // 按ASIN总销量是否满足规则分组
        Map<String, List<EsAmazonProductListing>> asinMatchedGroup = listings.stream().collect(Collectors.groupingBy(asinSaleCountGroupFunction));
        asinMatchedGroup.forEach((matchType, listingDataList) -> {
            if (matchType.equals(Boolean.TRUE.toString()) && matchedOfflineCount.get() < reservedCount) {
                // X天内的符合规则的链接数量
                List<EsAmazonProductListing> afterLastOpenDateTimeListing = listingDataList.stream().filter(listing -> {
                    Date openDate = Optional.ofNullable(listing.getOpenDate()).orElseGet(() -> Optional.ofNullable(listing.getFirstOpenDate()).orElseGet(Date::new));
                    LocalDateTime openDateTime = LocalDateTimeUtil.of(openDate);
                    if (openDateTime.compareTo(afterOpenDateTime) >= 0) {
                        return true;
                    } else {
                        return false;
                    }
                }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(afterLastOpenDateTimeListing)) {
                    return;
                }

                // 满足规则保留链接
                BiConsumer<EsAmazonProductListing, AmazonOfflineConfigListingQueue> func = (list, queue) -> {
                    queue.setStatus(OfflineQueueEnums.QueueStatusType.RESERVE_OFFLINE.getCode());
                    setQueueFiled(queue, list, reservedCountRule, sonAsinSaleCountMap, message);
                };
                List<AmazonOfflineConfigListingQueue> queues = amazonOfflineConfigListingQueueService.addListingToOffLinkQueue(afterLastOpenDateTimeListing, configVO, func);
                addQueueList.addAll(queues);
                matchedOfflineCount.addAndGet(queues.size());
            }
            if (matchType.equals(Boolean.FALSE.toString())) {
                // 过滤下架定时公共规则
                List<EsAmazonProductListing> matchingList = filterPublishConfig(listingDataList);
                if (CollectionUtils.isEmpty(matchingList)) {
                    return;
                }
                // 不满足规则写入待下架链接
                BiConsumer<EsAmazonProductListing, AmazonOfflineConfigListingQueue> func = (list, queue) -> {
                    queue.setStatus(OfflineQueueEnums.QueueStatusType.PENDING_OFFLINE.getCode());
                    setQueueFiled(queue, list, reservedCountRule, sonAsinSaleCountMap, message);
                };
                List<AmazonOfflineConfigListingQueue> queues = amazonOfflineConfigListingQueueService.addListingToOffLinkQueue(matchingList, configVO, func);
                addQueueList.addAll(queues);
            }
        });
        if (CollectionUtils.isNotEmpty(addQueueList)) {
            amazonOfflineConfigListingQueueService.saveBatch(addQueueList, 300);
        }
    }
} 