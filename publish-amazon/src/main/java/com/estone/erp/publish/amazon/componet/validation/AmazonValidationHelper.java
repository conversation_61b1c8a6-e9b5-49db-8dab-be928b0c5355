package com.estone.erp.publish.amazon.componet.validation;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.componet.validation.executor.*;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.enums.AmazonTemplateValidationEnum;
import com.estone.erp.publish.amazon.model.dto.AmazonTemplateValidationDO;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.request.ProductSourceRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.NoSuchElementException;

/**
 * <AUTHOR>
 * @date 2024-03-27 18:13
 */
@Slf4j
@Component
public class AmazonValidationHelper {
    private final SingleProductValidationExecutor singleProductValidationExecutor;
    private final ComposeSkuValidationExecutor composeSkuValidationExecutor;
    private final SuiteSkuValidationExecutor suiteSkuValidationExecutor;
    private final ErpDataValidationExecutor erpDataValidationExecutor;
    private final TemplateDataValidationExecutor templateDataValidationExecutor;
    private final GTDJCloudWarehouseValidationExecutor gtdjCloudWarehouseValidationExecutor;
    private final FollowSellValidationExecutor followSellValidationExecutor;


    @Autowired
    public AmazonValidationHelper(SingleProductValidationExecutor singleProductValidationExecutor, ComposeSkuValidationExecutor composeSkuValidationExecutor, SuiteSkuValidationExecutor suiteSkuValidationExecutor, ErpDataValidationExecutor erpDataValidationExecutor, TemplateDataValidationExecutor templateDataValidationExecutor, GTDJCloudWarehouseValidationExecutor gtdjCloudWarehouseValidationExecutor, com.estone.erp.publish.amazon.componet.validation.executor.FollowSellValidationExecutor followSellValidationExecutor) {
        this.singleProductValidationExecutor = singleProductValidationExecutor;
        this.composeSkuValidationExecutor = composeSkuValidationExecutor;
        this.suiteSkuValidationExecutor = suiteSkuValidationExecutor;
        this.erpDataValidationExecutor = erpDataValidationExecutor;
        this.templateDataValidationExecutor = templateDataValidationExecutor;
        this.gtdjCloudWarehouseValidationExecutor = gtdjCloudWarehouseValidationExecutor;
        this.followSellValidationExecutor = followSellValidationExecutor;
    }


    public AmazonValidationExecutor getExecutor(String articleNumber, Integer skuDataSource) {
        ProductSourceRequest productSource = new ProductSourceRequest(articleNumber, skuDataSource);
        ProductUtils.getSpuSkuDataSource(productSource);

        // 单品
        if (SkuDataSourceEnum.PRODUCT_SYSTEM.isTrue(productSource.getSkuDataSource())) {
            return singleProductValidationExecutor;
        }

        // 套装
        if (productSource.getIsSuite()) {
            return suiteSkuValidationExecutor;
        }

        // 组合
        if (SkuDataSourceEnum.COMPOSE_SYSTEM.isTrue(productSource.getSkuDataSource())) {
            return composeSkuValidationExecutor;
        }

        // 数据分析
        if (SkuDataSourceEnum.ERP_DATA_SYSTEM.isTrue(productSource.getSkuDataSource())) {
            return erpDataValidationExecutor;
        }


        // 冠通大健云仓
        if (SkuDataSourceEnum.GUAN_TONG_DA_JIAN_CLOUD_WARE_HOUSE.isTrue(productSource.getSkuDataSource())) {
            return gtdjCloudWarehouseValidationExecutor;
        }

        throw new NoSuchElementException("无可用校验器,货号:" + articleNumber);
    }

    /**
     * 校验模板信息 - 产品信息校验
     *
     * @param articleNumber 货号
     * @param skuDataSource sku产品数据来源
     */
    public AmazonTemplateValidationContext validationProductData(String accountNumber, String articleNumber, String site, Integer skuDataSource) throws RuntimeException {
        AmazonValidationExecutor executor = getExecutor(articleNumber, skuDataSource);
        AmazonTemplateValidationContext context = executor.init(accountNumber, articleNumber, site, skuDataSource);
        executor.validationProductData(context);
        return context;
    }

    /**
     * 校验模板信息 - 刊登规则校验
     * 侵权词提示
     */
    public void validationTemplateData(AmazonTemplateValidationContext context) {
        templateDataValidationExecutor.validationTemplateData(context);
    }

    /**
     * 跟卖校验 - 刊登规则校验
     * 侵权词提示
     */
    public void validationFollowSellData(AmazonTemplateValidationContext context) {
        followSellValidationExecutor.validationProductData(context);
    }


    /**
     * 自动刊登 店铺可刊登类目范围
     *
     * @param accountNumber 店铺
     * @param spu           货号
     * @param productSource 产品类型
     */
    public AmazonTemplateValidationContext validationAutoPublishCategory(String accountNumber, String spu, Integer productSource) {
        int skuDataSource = productSource;
        AmazonValidationExecutor executor = getExecutor(spu, skuDataSource);
        AmazonTemplateValidationContext context = executor.init(accountNumber, spu, null, skuDataSource);
        context.setCheckCategoryPublishNumber(false);
        executor.validationAutoPublishCategory(context);
        return context;

    }

    /**
     * 模板校验主管分类刊登次数
     * @param publishTemplate 刊登模板
     */
    public ApiResult<String> validationCategoryPublishNumber(AmazonTemplateBO publishTemplate) {
        String spu = publishTemplate.getParentSku();
        String site = publishTemplate.getCountry();
        String accountNumber = publishTemplate.getSellerId();
        Integer skuDataSource = publishTemplate.getSkuDataSource();
        AmazonValidationExecutor executor = getExecutor(spu, skuDataSource);
        AmazonTemplateValidationContext context = executor.init(accountNumber, spu, site, skuDataSource);
        context.setCheckCategoryPublishNumber(true);
        context.setTemplate(publishTemplate);
        // 如果模版为刊登中则为刊登模式验证
        if (Integer.valueOf(AmaoznPublishStatusEnum.PUBLISHING.getStatusCode())
                .equals(publishTemplate.getPublishStatus())) {
            context.setPublishMode(true);
        }
        executor.validationAutoPublishCategory(context);
        if (context.isAllSuccess()) {
            return ApiResult.newSuccess("success");
        }
        AmazonTemplateValidationDO failValidationDO = context.getValidations()
                .stream()
                .filter(validation -> AmazonTemplateValidationEnum.INSUFFICIENT_PUBLISH_CATEGORY.isTrue(validation.getCode()))
                .findFirst()
                .orElse(context.getValidations().stream().filter(validation -> Boolean.FALSE.equals(validation.getSuccess())).findFirst().orElseGet(() -> null));
        if (failValidationDO != null) {
            return ApiResult.newError(failValidationDO.getErrorMsg());
        }
        return ApiResult.newError("主管分类刊登次数不足,"+ JSON.toJSONString(context.getValidations()));
    }

    /**
     * 校验spu分类总刊登次数
     * @param spu            spu
     * @param skuDataSource  sku数据来源
     */
    public AmazonTemplateValidationContext validationListingPublishTotalNumber(String spu, Integer skuDataSource) {
        AmazonValidationExecutor executor = getExecutor(spu, skuDataSource);
        AmazonTemplateValidationContext context = executor.init(null, spu, null, skuDataSource);
        executor.validationListingPublishTotalNumber(context);
        return context;
    }

    /**
     * 验证EAN是否为空
     *
     * @param template
     */
    public ApiResult<String> validationEAN(AmazonTemplateBO template) {
        AmazonTemplateValidationContext context = new AmazonTemplateValidationContext(template);
        if (BooleanUtils.isFalse(template.getUpcExempt())) {
            templateDataValidationExecutor.checkTemplateEAN(template, context);
            List<AmazonTemplateValidationDO> validations = context.getValidations();
            if (CollectionUtils.isNotEmpty(validations)) {
                AmazonTemplateValidationDO amazonTemplateValidationDO = validations.get(0);
                return ApiResult.newError(amazonTemplateValidationDO.getErrorMsg());
            }
        }
        return ApiResult.newSuccess("success");

    }

    /**
     * 检查Amazon刊登平台分类
     *
     * @param template 模版
     * @return apiResult
     */
    public ApiResult<String> validationAmazonPublishCategory(AmazonTemplateBO template) {
        return templateDataValidationExecutor.validationAmazonPublishCategory(template);
    }


    /**
     * 非特供账号不能上架特供标签产品
     *
     * @param template 模版
     * @return apiResult
     */

    public ApiResult<String> validationCanPublishSpecialTagAccount(AmazonTemplateBO template) {
        String spu = template.getParentSku();
        Integer skuDataSource = template.getSkuDataSource();
        AmazonValidationExecutor executor = getExecutor(spu, skuDataSource);
        AmazonTemplateValidationContext validationContext = new AmazonTemplateValidationContext(template);
        executor.filterTemplateSkuStatus(validationContext);
        singleProductValidationExecutor.validationCanPublishSpecialTagAccount(validationContext);
        if (validationContext.isAllSuccess()) {
            return ApiResult.newSuccess();
        }
        return ApiResult.newError(validationContext.getValidations().stream()
                .map(AmazonTemplateValidationDO::getErrorMsg)
                .filter(StringUtils::isNotBlank)
                .findFirst().orElse("非特供店铺，不能上架AMZ特供产品"));
    }

    /**
     * 非EU-FBA账号不能上架EU-FBA标签产品
     *
     * @param template 模版
     * @return apiResult
     */
    public ApiResult<String> validationCanPublishEuFbaSpecialTagAccount(AmazonTemplateBO template) {
        String spu = template.getParentSku();
        Integer skuDataSource = template.getSkuDataSource();
        AmazonValidationExecutor executor = getExecutor(spu, skuDataSource);
        AmazonTemplateValidationContext validationContext = new AmazonTemplateValidationContext(template);
        executor.filterTemplateSkuStatus(validationContext);
        singleProductValidationExecutor.validationCanPublishEuFbaSpecialTagAccount(validationContext);
        if (validationContext.isAllSuccess()) {
            return ApiResult.newSuccess();
        }
        return ApiResult.newError(validationContext.getValidations().stream()
                .map(AmazonTemplateValidationDO::getErrorMsg)
                .filter(StringUtils::isNotBlank)
                .findFirst().orElse("非EU-FBA店铺，不能刊登EU-FBA 特殊标签产品"));
    }


    /**
     * 过滤模板中禁止刊登的单品状态sku
     *
     * @param template 模版
     */
    public ApiResult<String> filterTemplateSkuStatus(AmazonTemplateBO template) {
        String spu = template.getParentSku();
        Integer skuDataSource = template.getSkuDataSource();
        AmazonValidationExecutor executor = getExecutor(spu, skuDataSource);
        AmazonTemplateValidationContext validationContext = new AmazonTemplateValidationContext(template);
        executor.filterTemplateSkuStatus(validationContext);
        if (validationContext.isAllSuccess()) {
            return ApiResult.newSuccess();
        }
        return ApiResult.newError("模板所有sku为禁售或停产存档废弃，不可以刊登");
    }
}
