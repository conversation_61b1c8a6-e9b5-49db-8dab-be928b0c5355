package com.estone.erp.publish.tidb.publishtidb.controller;


import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.componet.checkserver.template.TemplateCheckingWordService;
import com.estone.erp.publish.tidb.publishtidb.domain.CheckCopywritingInfringementWordDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.ReviewBasisInfoVO;
import com.estone.erp.publish.tidb.publishtidb.domain.ReviewSaveDTO;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonNewProductCopywritingReviewService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
* <p>
* amazon新品文案审核 前端控制器
* </p>
*
* <AUTHOR>
* @since 2025-07-11
*/
@RestController
@RequestMapping("/amazonNewProductCopywritingReview")
public class AmazonNewProductCopywritingReviewController {
    @Resource
    private AmazonNewProductCopywritingReviewService amazonNewProductCopywritingReviewService;

    @Resource
    private TemplateCheckingWordService templateCheckingWordService;
    /**
     * 获取文案审核基础数据
     */
    @GetMapping("getReviewBasisInfo/{spu}")
    public ApiResult<ReviewBasisInfoVO> getReviewBasisInfo(@PathVariable("spu") String spu) {
        try {
            return ApiResult.newSuccess(amazonNewProductCopywritingReviewService.getReviewBasisInfo(spu));
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 检测文案审核侵权词
     */
    @PostMapping("checkCopywritingInfringementWords")
    public ApiResult<CheckCopywritingInfringementWordDTO> checkCopywritingInfringementWords(@RequestBody ReviewSaveDTO dto) {
        try {
            return ApiResult.newSuccess(amazonNewProductCopywritingReviewService.checkCopywritingInfringementWords(dto));
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 删除侵权词
     */
    @PostMapping("delCopywritingInfringementWords")
    public ApiResult<CheckCopywritingInfringementWordDTO> delCopywritingInfringementWords(@RequestBody ReviewSaveDTO dto) {
        try {
            return ApiResult.newSuccess(amazonNewProductCopywritingReviewService.delCopywritingInfringementWords(dto));
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }


    /**
     * 保存并通过文案审核
     */
    @PostMapping("saveCopywritingReview")
    public ApiResult<Void> saveCopywritingReview(@RequestBody ReviewSaveDTO dto) {
        try {
            amazonNewProductCopywritingReviewService.saveCopywritingReview(dto);
            return ApiResult.newSuccess();
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

}
