package com.estone.erp.publish.amazon.model;

import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> amazon_must_publish_new_product
 * 2023-10-23 11:39:18
 */
public class AmazonMustPublishNewProductCriteria extends AmazonMustPublishNewProduct {
    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    private List<Integer> ids;


    /**
     * spu
     */
    private List<String> spuList;

    /**
     * 主管id
     */
    private String supervisorId;

    /**
     * 主管id集合
     */
    private List<String> supervisorIds;


    /**
     * 类目全路径code
     */
    private List<String> categoryFullPathCodes;

    /**
     * 销售
     */
    private List<String> saleAccount;

    /**
     * 销售为空
     */
    private Boolean salesBlank;

    /**
     * 销售组长
     */
    private List<String> saleAccountLeader;

    /**
     * 销售主管
     */
    private List<String> saleAccountManager;

    /**
     * 推送时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date starPushTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endPushTime;

    /**
     * 进入单品时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date starSpuCreatedTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endSpuCreatedTime;

    /**
     * 站点
     */
    private String site;

    /**
     * 刊登状态
     */
    private List<Integer> publishStatus;

    /**
     * 审核状态
     */
    private List<Integer> auditStatusList;


    /**
     * 文案审核状态
     */
    private List<Integer> textAuditStatusList;



    public AmazonMustPublishNewProductExample getExample() {
        AmazonMustPublishNewProductExample example = new AmazonMustPublishNewProductExample();
        AmazonMustPublishNewProductExample.Criteria criteria = example.createCriteria();

        if (CollectionUtils.isNotEmpty(this.getIds())) {
            criteria.andIdIn(this.getIds());
        }
        if (StringUtils.isNotBlank(this.getSpu())) {
            criteria.andSpuEqualTo(this.getSpu());
        }
        // spu
        if (CollectionUtils.isNotEmpty(this.getSpuList())) {
            criteria.andSpuIn(this.getSpuList());
        }
        // 主管id
        if (StringUtils.isNotEmpty(this.getSupervisorId())) {
            criteria.andSupervisorIdEqualTo(this.getSupervisorId());
        }
        if (CollectionUtils.isNotEmpty(this.getSupervisorIds()) && CollectionUtils.isNotEmpty(this.getSaleAccount())) {
            StringBuilder sqlBuilder = new StringBuilder();
            String supervisorIds = this.getSupervisorIds().stream().map(id -> String.format("'%s'", id)).collect(Collectors.joining(","));
            String saleAccount = this.getSaleAccount().stream().map(id -> String.format("'%s'", id)).collect(Collectors.joining(","));
            sqlBuilder.append("(").append("supervisor_id in (" + supervisorIds + ")").append(" or ").append("sale_id in (" + saleAccount + ")").append(")");
            criteria.addCriterion(sqlBuilder.toString());
        }else {
            // 主管id集合
            if (CollectionUtils.isNotEmpty(this.getSupervisorIds())) {
                criteria.andSupervisorIdIn(this.getSupervisorIds());
            }
            // 销售
            if (CollectionUtils.isNotEmpty(this.getSaleAccount())) {
                criteria.andSaleIdIn(this.getSaleAccount());
            }
        }

        // 销售为空
        if (ObjectUtils.isNotEmpty(this.getSalesBlank())) {
            if (this.getSalesBlank()) {
                criteria.andSaleIdIsNull();
            }
        }
        // 类目全路径code
        if (CollectionUtils.isNotEmpty(this.getCategoryFullPathCodes())) {
            criteria.andCategoryFullPathCodeIn(this.getCategoryFullPathCodes());
        }
        // 推送时间
        if (this.getStarPushTime() != null && this.getEndPushTime()!= null) {
            criteria.andCreatedTimeBetween(new Timestamp(this.getStarPushTime().getTime()), new Timestamp(this.getEndPushTime().getTime()));
        }

        // 进入单品时间
        if (this.getStarSpuCreatedTime() != null && this.getEndSpuCreatedTime()!= null) {
            criteria.andSpuCreatedTimeBetween(new Timestamp(this.getStarSpuCreatedTime().getTime()), new Timestamp(this.getEndSpuCreatedTime().getTime()));
        }

        // 刊登状态-未选中站点状态
        if (StringUtils.isBlank(this.getSite()) && CollectionUtils.isNotEmpty(this.getPublishStatus())) {
            String paramValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "must_publish_new_spu" ,"site", 10);
            List<String> sites = Arrays.stream(paramValue.split(",")).collect(Collectors.toList());
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("(");
            for (Integer status : this.getPublishStatus()) {
                for (int i = 0; i < sites.size(); i++) {
                    String site = sites.get(i);
                    sqlBuilder.append(site.toLowerCase()).append("_publish_status = ").append(status).append(" ");
                    if (i != sites.size() - 1) {
                        sqlBuilder.append(" or ");
                    }
                }
                sqlBuilder.append(" or ");
            }
            int lastIndexOf = sqlBuilder.lastIndexOf(" or ");
            sqlBuilder.delete(lastIndexOf, sqlBuilder.length());
            sqlBuilder.append(")");
            criteria.addCriterion(sqlBuilder.toString());
        }

        // 刊登状态-选中站点状态
        if (StringUtils.isNotBlank(this.getSite()) &&  CollectionUtils.isNotEmpty(this.getPublishStatus())) {
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("(");
            for (int i = 0; i < this.getPublishStatus().size(); i++) {
                Integer status = this.getPublishStatus().get(i);
                sqlBuilder.append(this.getSite().toLowerCase()).append("_publish_status = ").append(status).append(" ");
                if (i != this.getPublishStatus().size() - 1) {
                    sqlBuilder.append(" or ");
                }
            }
            sqlBuilder.append(")");
            criteria.addCriterion(sqlBuilder.toString());
        }

        // 是否禁售-未选中站点状态
        if (StringUtils.isBlank(this.getSite()) && this.getIsBan() != null) {
            criteria.andIsBanEqualTo(this.getIsBan());
        }

        // 是否禁售-选中站点状态
        if (StringUtils.isNotBlank(this.getSite()) && this.getIsBan() != null) {
            criteria.addCriterion(String.format("%s_is_band = %s", this.getSite().toLowerCase(), this.getIsBan()));
        }

        // 备注
        if (StringUtils.isNotBlank(this.getRemarks())) {
            criteria.andRemarksLike("%" + this.getRemarks() + "%");
        }

        // 审核状态
        if (CollectionUtils.isNotEmpty(this.getAuditStatusList())) {
            criteria.andAuditStatusIn(this.getAuditStatusList());
        }

        // 审核状态
        if (CollectionUtils.isNotEmpty(this.getTextAuditStatusList())) {
            criteria.andTextAuditStatusIn(this.getTextAuditStatusList());
        }

        if (this.getSaleId() != null) {
            criteria.andSaleIdEqualTo(this.getSaleId());
        }
        if (StringUtils.isNotBlank(this.getPublishedSite())) {
            criteria.andPublishedSiteEqualTo(this.getPublishedSite());
        }
        if (this.getStatus() != null) {
            criteria.andStatusEqualTo(this.getStatus());
        }
        if (StringUtils.isNotBlank(this.getSiteInfo())) {
            criteria.andSiteInfoEqualTo(this.getSiteInfo());
        }
        if (this.getProductType() != null) {
            criteria.andProductTypeEqualTo(this.getProductType());
        }
        if (this.getUsPublishStatus() != null) {
            criteria.andUsPublishStatusEqualTo(this.getUsPublishStatus());
        }
        if (this.getDePublishStatus() != null) {
            criteria.andDePublishStatusEqualTo(this.getDePublishStatus());
        }
        if (this.getFrPublishStatus() != null) {
            criteria.andFrPublishStatusEqualTo(this.getFrPublishStatus());
        }
        if (this.getUkPublishStatus() != null) {
            criteria.andUkPublishStatusEqualTo(this.getUkPublishStatus());
        }
        if (this.getItPublishStatus() != null) {
            criteria.andItPublishStatusEqualTo(this.getItPublishStatus());
        }
        if (this.getEsPublishStatus() != null) {
            criteria.andEsPublishStatusEqualTo(this.getEsPublishStatus());
        }
        if (this.getJpPublishStatus() != null) {
            criteria.andJpPublishStatusEqualTo(this.getJpPublishStatus());
        }
        if (this.getCaPublishStatus() != null) {
            criteria.andCaPublishStatusEqualTo(this.getCaPublishStatus());
        }
        if (this.getUsIsBand() != null) {
            criteria.andUsIsBandEqualTo(this.getUsIsBand());
        }
        if (this.getDeIsBand() != null) {
            criteria.andDeIsBandEqualTo(this.getDeIsBand());
        }
        if (this.getFrIsBand() != null) {
            criteria.andFrIsBandEqualTo(this.getFrIsBand());
        }
        if (this.getUkIsBand() != null) {
            criteria.andUkIsBandEqualTo(this.getUkIsBand());
        }
        if (this.getItIsBand() != null) {
            criteria.andItIsBandEqualTo(this.getItIsBand());
        }
        if (this.getEsIsBand() != null) {
            criteria.andEsIsBandEqualTo(this.getEsIsBand());
        }
        if (this.getJpIsBand() != null) {
            criteria.andJpIsBandEqualTo(this.getJpIsBand());
        }
        if (this.getCaIsBand() != null) {
            criteria.andCaIsBandEqualTo(this.getCaIsBand());
        }
        if (StringUtils.isNotBlank(this.getLastUpdatedBy())) {
            criteria.andLastUpdatedByEqualTo(this.getLastUpdatedBy());
        }
        return example;
    }

    public List<Integer> getIds() {
        return ids;
    }

    public void setIds(List<Integer> ids) {
        this.ids = ids;
    }

    public List<String> getSpuList() {
        return spuList;
    }

    public void setSpuList(List<String> spuList) {
        this.spuList = spuList;
    }

    public String getSupervisorId() {
        return supervisorId;
    }

    public void setSupervisorId(String supervisorId) {
        this.supervisorId = supervisorId;
    }
    public List<String> getSupervisorIds() {
        return supervisorIds;
    }

    public void setSupervisorIds(List<String> supervisorIds) {
        this.supervisorIds = supervisorIds;
    }

    public List<String> getCategoryFullPathCodes() {
        return categoryFullPathCodes;
    }

    public void setCategoryFullPathCodes(List<String> categoryFullPathCodes) {
        this.categoryFullPathCodes = categoryFullPathCodes;
    }

    public Boolean getSalesBlank() {
        return salesBlank;
    }

    public void setSalesBlank(Boolean salesBlank) {
        this.salesBlank = salesBlank;
    }

    public List<String> getSaleAccount() {
        return saleAccount;
    }

    public void setSaleAccount(List<String> saleAccount) {
        this.saleAccount = saleAccount;
    }

    public List<String> getSaleAccountLeader() {
        return saleAccountLeader;
    }

    public void setSaleAccountLeader(List<String> saleAccountLeader) {
        this.saleAccountLeader = saleAccountLeader;
    }

    public List<String> getSaleAccountManager() {
        return saleAccountManager;
    }

    public void setSaleAccountManager(List<String> saleAccountManager) {
        this.saleAccountManager = saleAccountManager;
    }

    public Date getStarPushTime() {
        return starPushTime;
    }

    public void setStarPushTime(Date starPushTime) {
        this.starPushTime = starPushTime;
    }

    public Date getEndPushTime() {
        return endPushTime;
    }

    public void setEndPushTime(Date endPushTime) {
        this.endPushTime = endPushTime;
    }

    public Date getStarSpuCreatedTime() {
        return starSpuCreatedTime;
    }

    public void setStarSpuCreatedTime(Date starSpuCreatedTime) {
        this.starSpuCreatedTime = starSpuCreatedTime;
    }

    public Date getEndSpuCreatedTime() {
        return endSpuCreatedTime;
    }

    public void setEndSpuCreatedTime(Date endSpuCreatedTime) {
        this.endSpuCreatedTime = endSpuCreatedTime;
    }

    public String getSite() {
        return site;
    }

    public void setSite(String site) {
        this.site = site;
    }

    public List<Integer> getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStatus(List<Integer> publishStatus) {
        this.publishStatus = publishStatus;
    }

    public List<Integer> getAuditStatusList() {
        return auditStatusList;
    }

    public void setAuditStatusList(List<Integer> auditStatusList) {
        this.auditStatusList = auditStatusList;
    }

    public List<Integer> getTextAuditStatusList() {
        return textAuditStatusList;
    }

    public void setTextAuditStatusList(List<Integer> textAuditStatusList) {
        this.textAuditStatusList = textAuditStatusList;
    }
}