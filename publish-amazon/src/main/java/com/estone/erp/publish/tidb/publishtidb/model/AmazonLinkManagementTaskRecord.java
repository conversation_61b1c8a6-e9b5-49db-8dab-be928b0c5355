package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * Amazon链接管理任务执行记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("amazon_link_management_task_record")
public class AmazonLinkManagementTaskRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 配置规则ID，关联amazon_link_management_config.id
     */
    private Long configId;

    /**
     * 店铺账号
     */
    private String accountNumber;

    /**
     * 配置优先级，记录执行时的优先级
     */
    private Integer configLevel;

    /**
     * 执行日期，格式YYYY-MM-DD
     */
    private LocalDate executeDate;

    /**
     * 具体执行时间
     */
    private LocalDateTime executeTime;

    /**
     * 执行状态：0-待处理，1-执行中，2-执行成功，3-执行失败
     */
    private Integer executeStatus;

    /**
     * 记录创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 记录更新时间
     */
    private LocalDateTime updatedTime;


}
