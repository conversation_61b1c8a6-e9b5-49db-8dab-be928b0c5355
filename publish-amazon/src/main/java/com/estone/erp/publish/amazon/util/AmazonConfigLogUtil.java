package com.estone.erp.publish.amazon.util;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.annotation.NeedToLog;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.enums.AmazonMarketingLogTypeEnum;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.OfflineRuleCompareDo;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonMarketingConfigLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Amazon日志工具
 */
@Slf4j
public class AmazonConfigLogUtil {
    /**
     * 生成日志
     *
     * @param newV 新对象
     * @param oldV 旧对象
     * @param type 日志类型
     * @return 日志列表
     */
    public static List<AmazonMarketingConfigLog> generateLog(Object newV, Object oldV, Long id, AmazonMarketingLogTypeEnum type) {
        if (newV == null || oldV == null || id == null || type == null) {
            return new ArrayList<>();
        }
        String userName = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        if (StringUtils.isBlank(userName)) {
            userName = "admin";
        }
        LocalDateTime now = LocalDateTime.now();
        List<AmazonMarketingConfigLog> list = new ArrayList<>();
        Class<?> aClass = newV.getClass();
        while (aClass != null && !aClass.equals(Object.class)) {
            //
            for (Field field : aClass.getDeclaredFields()) {
                if (field.isAnnotationPresent(NeedToLog.class)) {
                    try {
                        field.setAccessible(true);
                        NeedToLog annotation = field.getAnnotation(NeedToLog.class);
                        String name = field.getName();
                        String description = annotation.value();
                        Object newValue = field.get(newV);
                        Object oldValue = field.get(oldV);
                        AmazonMarketingConfigLog configLog = checkEqualLog(id, type, name, description, oldValue, newValue, userName, now);
                        if (configLog != null) {
                            list.add(configLog);
                        }
                    } catch (Exception e) {
                        log.error("add log error : {}", e.getMessage());
                    }
                }
            }
            aClass = aClass.getSuperclass();
        }
        return list;
    }


    /**
     * 生成日志
     *
     * @param id          id
     * @param type        类型
     * @param name        属性名称
     * @param description 属性名称中文
     * @param oldValue    旧值
     * @param newValue    新值
     * @param userName    操作人
     * @param now         时间
     * @return 日志
     */
    public static AmazonMarketingConfigLog initAmazonMarketingConfigLog(Long id, AmazonMarketingLogTypeEnum type, String name, String description, Object oldValue, Object newValue, String userName, LocalDateTime now) {
        AmazonMarketingConfigLog log = new AmazonMarketingConfigLog();
        log.setMarketingId(id);
        log.setType(type.getCode());
        log.setOperator(userName);
        log.setOperateAttr(name);
        log.setOperateAttrDesc(description);
        log.setOperateTime(now);
        if (oldValue != null) {
            if (oldValue instanceof Collection) {
                log.setPreviousValue(JSON.toJSONString(oldValue));
            } else {
                log.setPreviousValue(oldValue.toString());
            }
        } else {
            log.setPreviousValue("");
        }
        if (newValue != null) {
            if (newValue instanceof Collection) {
                log.setAfterValue(JSON.toJSONString(newValue));
            } else {
                log.setAfterValue(newValue.toString());
            }
        }
        return log;
    }


    /**
     * 检查是否相等
     *
     * @param id          id
     * @param type        类型
     * @param name        属性名称
     * @param oldValue    旧值
     * @param newValue    新值
     * @param userName    操作人
     * @param description 属性名称中文
     * @param now         时间
     * @return 日志
     */
    public static AmazonMarketingConfigLog checkEqualLog(Long id, AmazonMarketingLogTypeEnum type, String name, String description, Object oldValue, Object newValue, String userName, LocalDateTime now) {
        if (!checkEquals(name, oldValue, newValue, type)) {
            return initAmazonMarketingConfigLog(id, type, name, description, oldValue, newValue, userName, now);
        }
        return null;
    }


    /**
     * 检查是否相等
     *
     * @param name     名字
     * @param oldValue 旧值
     * @param newValue 新值
     * @param type     日志类型
     * @return
     */
    public static boolean checkEquals(String name, Object oldValue, Object newValue, AmazonMarketingLogTypeEnum type) {
        if (AmazonMarketingLogTypeEnum.OFFLINE_CONFIG.getCode().equals(type.getCode())
                || AmazonMarketingLogTypeEnum.LINK_MANAGE_HANDING_TIME.getCode().equals(type.getCode())) {
            if ("accountList".equals(name)) {
                String s1 = Optional.ofNullable(oldValue).map(String::valueOf).orElse("");
                String s2 = Optional.ofNullable(newValue).map(String::valueOf).orElse("");
                Set<String> collect1 = Arrays.stream(s2.split(",")).collect(Collectors.toSet());
                Set<String> collect2 = Arrays.stream(s1.split(",")).collect(Collectors.toSet());
                return collect1.equals(collect2);
            }
            if ("accounts".equals(name)) {
                String s1 = Optional.ofNullable(oldValue).map(String::valueOf).orElse("");
                String s2 = Optional.ofNullable(newValue).map(String::valueOf).orElse("");
                Set<String> collect1 = Arrays.stream(s2.split(",")).collect(Collectors.toSet());
                Set<String> collect2 = Arrays.stream(s1.split(",")).collect(Collectors.toSet());
                return collect1.equals(collect2);
            }
            if ("rule".equals(name)) {
                return OfflineRuleCompareDo.areJsonStringsEqual(oldValue, newValue);
            }
        }
        return Objects.equals(newValue, oldValue);
    }
}
