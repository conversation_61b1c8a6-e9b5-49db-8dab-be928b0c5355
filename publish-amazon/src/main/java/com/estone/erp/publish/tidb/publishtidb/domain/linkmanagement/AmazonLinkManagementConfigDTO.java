package com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement;

import com.estone.erp.common.annotation.NeedToLog;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonLinkManagementConfig;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * Amazon链接管理配置DTO
 * 扩展AmazonLinkManagementConfig实体，将JSON字段解析为结构化对象
 * 对应设计文档3.4 JSON字段结构设计
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Slf4j
public class AmazonLinkManagementConfigDTO extends AmazonLinkManagementConfig {

    private static final long serialVersionUID = 1L;
    
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 账户列表
     * 示例: ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
     */
    @NeedToLog("适用店铺")
    private List<String> accountList;

    /**
     * 规则JSON对象
     * 对应rule字段
     */
    @NeedToLog("配置规则")
    private AmazonLinkManagementRuleDTO ruleInfo;


    /**
     * 时间配置
     */
    @NeedToLog("时间配置")
    private AmazonLinkManagementTimeDTO timeConfig;

    /**
     * 从实体对象创建DTO对象，并解析JSON字段
     *
     * @param entity 实体对象
     * @return DTO对象
     */
    public static AmazonLinkManagementConfigDTO fromEntity(AmazonLinkManagementConfig entity) {
        if (entity == null) {
            return null;
        }

        AmazonLinkManagementConfigDTO dto = new AmazonLinkManagementConfigDTO();
        // 复制基本属性
        dto.setId(entity.getId());
        dto.setType(entity.getType());
        dto.setRuleName(entity.getRuleName());
        dto.setAccountType(entity.getAccountType());
        dto.setAccountOption(entity.getAccountOption());
        dto.setAccounts(entity.getAccounts());
        dto.setLevel(entity.getLevel());
        dto.setRuleType(entity.getRuleType());
        dto.setRule(entity.getRule());
        dto.setStatus(entity.getStatus());
        dto.setExeFrequency(entity.getExeFrequency());
        dto.setExeTime(entity.getExeTime());
        dto.setStrategyStartTime(entity.getStrategyStartTime());
        dto.setStrategyEndTime(entity.getStrategyEndTime());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setUpdatedBy(entity.getUpdatedBy());
        dto.setCreatedTime(entity.getCreatedTime());
        dto.setUpdatedTime(entity.getUpdatedTime());

        // 解析JSON字段
        try {
            if (entity.getAccounts() != null && !entity.getAccounts().isEmpty()) {
                Map<String, List<String>> accountsMap = objectMapper.readValue(entity.getAccounts(), Map.class);
                dto.setAccountList(accountsMap.get("accounts"));
            }
            
            if (entity.getRule() != null && !entity.getRule().isEmpty()) {
                dto.setRuleInfo(objectMapper.readValue(entity.getRule(), AmazonLinkManagementRuleDTO.class));
                AmazonLinkManagementRuleDTO ruleInfoConfig = dto.getRuleInfo();
                if (ruleInfoConfig != null && ruleInfoConfig.getSalesRange() != null) {
                    if (StringUtils.isBlank(ruleInfoConfig.getSalesRange().getSalesType())) {
                        ruleInfoConfig.setSalesRange(null);
                    }
                }

            }

            AmazonLinkManagementTimeDTO timeConfig = new AmazonLinkManagementTimeDTO();
            timeConfig.setExeFrequency(entity.getExeFrequency());
            timeConfig.setExeTime(entity.getExeTime());
            if (StringUtils.isNotBlank(entity.getExecuteDate())) {
                List<String> executeDate = Arrays.stream(entity.getExecuteDate().split(",")).collect(Collectors.toList());
                timeConfig.setExecuteDate(executeDate);
            }
            timeConfig.setStrategyStartTime(entity.getStrategyStartTime());
            timeConfig.setStrategyEndTime(entity.getStrategyEndTime());
            dto.setTimeConfig(timeConfig);
        } catch (JsonProcessingException e) {
            log.error("解析JSON字段失败", e);
        }

        return dto;
    }

    /**
     * 将DTO对象转换为实体对象，并序列化JSON字段
     *
     * @return 实体对象
     */
    public AmazonLinkManagementConfig toEntity() {
        AmazonLinkManagementConfig entity = new AmazonLinkManagementConfig();
        // 复制基本属性
        entity.setId(this.getId());
        entity.setType(this.getType());
        entity.setRuleName(this.getRuleName());
        entity.setAccountType(this.getAccountType());
        entity.setAccountOption(this.getAccountOption());
        entity.setLevel(this.getLevel());
        entity.setRuleType(this.getRuleType());
        entity.setStatus(this.getStatus());
        entity.setCreatedBy(this.getCreatedBy());
        entity.setUpdatedBy(this.getUpdatedBy());
        entity.setCreatedTime(this.getCreatedTime());
        entity.setUpdatedTime(this.getUpdatedTime());

        // 序列化JSON字段
        try {
            if (CollectionUtils.isNotEmpty(this.getAccountList())) {
                entity.setAccounts(objectMapper.writeValueAsString(Map.of("accounts", this.getAccountList())));
            } else {
                entity.setAccounts(this.getAccounts());
            }
            
            if (this.getRuleInfo() != null) {
                entity.setRule(objectMapper.writeValueAsString(this.getRuleInfo()));
            } else {
                entity.setRule(this.getRule());
            }
            if (this.getTimeConfig() != null) {
                entity.setExeFrequency(this.getTimeConfig().getExeFrequency());
                entity.setExeTime(this.getTimeConfig().getExeTime());
                entity.setExecuteDate(StringUtils.join(this.getTimeConfig().getExecuteDate(), ","));
                entity.setStrategyStartTime(this.getTimeConfig().getStrategyStartTime());
                entity.setStrategyEndTime(this.getTimeConfig().getStrategyEndTime());
            }
        } catch (JsonProcessingException e) {
            log.error("序列化JSON字段失败", e);
            // 保留原始JSON字符串
            entity.setAccounts(this.getAccounts());
            entity.setRule(this.getRule());
        }

        return entity;
    }
}