package com.estone.erp.publish.amazon.enums;

import lombok.Getter;

@Getter
public enum AmazonMarketingLogTypeEnum {    // 1 营销活动配置 2 链接管理
    OFFLINE_CONFIG(1, "下架配置"),
    OFFLINE_DATA_STATISTICS(2, "下架数据统计"),
    LINK_MANAGE_HANDING_TIME(3, "链接管理-修改备货期");

    private final Integer code;

    private final String desc;

    AmazonMarketingLogTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
