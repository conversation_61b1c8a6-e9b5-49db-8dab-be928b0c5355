package com.estone.erp.publish.tidb.publishtidb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement.AmazonLinkManagementConfigDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.req.AmazonLinkManagementConfigRequest;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonLinkManagementConfig;

/**
 * <p>
 * Amazon链接管理配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface AmazonLinkManagementConfigService extends IService<AmazonLinkManagementConfig> {
    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    CQueryResult<AmazonLinkManagementConfigDTO> queryPage(CQuery<AmazonLinkManagementConfigRequest> query);


    ApiResult<String> saveOrUpdateConfig(AmazonLinkManagementConfigDTO entity);

    boolean updateStatus(AmazonLinkManagementConfigRequest request);
}