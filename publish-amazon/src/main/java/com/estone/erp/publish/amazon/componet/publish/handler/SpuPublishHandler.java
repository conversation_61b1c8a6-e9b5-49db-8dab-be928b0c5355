package com.estone.erp.publish.amazon.componet.publish.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiR<PERSON>ult;
import com.estone.erp.publish.amazon.bo.AmazonAutoPublishParam;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.product.IdType;
import com.estone.erp.publish.amazon.call.util.AmazonUtils;
import com.estone.erp.publish.amazon.componet.AmazonAutoPublishHelper;
import com.estone.erp.publish.amazon.componet.AmazonConstantMarketHelper;
import com.estone.erp.publish.amazon.componet.publish.AbstractAmazonPublishExecutor;
import com.estone.erp.publish.amazon.componet.publish.AmazonPublishContext;
import com.estone.erp.publish.amazon.componet.publish.PublishExecutor;
import com.estone.erp.publish.amazon.componet.publish.enums.AmazonPublishTypeEnums;
import com.estone.erp.publish.amazon.componet.publish.util.AmazonTemplatePropertiesUtil;
import com.estone.erp.publish.amazon.componet.validation.executor.TemplateDataValidationExecutor;
import com.estone.erp.publish.amazon.enums.*;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonCategorySpProductType;
import com.estone.erp.publish.amazon.model.AmazonTemplateExample;
import com.estone.erp.publish.amazon.model.ProductTypeTemplateJsonAttr;
import com.estone.erp.publish.amazon.model.dto.AmazonRequiredAttributeDO;
import com.estone.erp.publish.amazon.mq.model.AmazonPublishMessage;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonCategorySpProductTypeService;
import com.estone.erp.publish.amazon.service.ProductTypeTemplateJsonAttrService;
import com.estone.erp.publish.amazon.util.AmazonAutoPublishUtil;
import com.estone.erp.publish.amazon.util.AmazonTemplateUtils;
import com.estone.erp.publish.amazon.util.model.AmazonSku;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.PublishRoleEnum;
import com.estone.erp.publish.tidb.publishtidb.service.IAmazonListingGpsrInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Amazon spu 刊登处理器
 *
 * <AUTHOR>
 * @date 2024-11-14 15:14
 */
@Slf4j
@Component
public class SpuPublishHandler extends AbstractAmazonPublishExecutor implements PublishExecutor {
    @Autowired
    private AmazonConstantMarketHelper amazonConstantMarketHelper;
    @Autowired
    private AmazonAutoPublishHelper amazonAutoPublishHelper;
    @Resource
    private AmazonAccountRelationService amazonAccountRelationService;
    @Autowired
    private TemplateDataValidationExecutor templateDataValidationExecutor;
    @Autowired
    private ProductTypeTemplateJsonAttrService productTypeTemplateJsonAttrService;
    @Resource
    private AmazonCategorySpProductTypeService amazonCategorySpProductTypeService;
    @Autowired
    private IAmazonListingGpsrInfoService amazonListingGpsrInfoService;

    /**
     * @param context
     * @return
     */
    @Override
    public ApiResult<String> execute(AmazonPublishContext context) {
        AmazonPublishMessage message = context.getMessage();
        AmazonAccount account = context.getAccount();
        String site = amazonConstantMarketHelper.getMarketplaceIdMap().get(account.getMarketplaceId()).getMarketplace();
        message.setSite(site);
        // 生成模板
        ApiResult<AmazonTemplateBO> templateResult = generateTemplate(context);
        if (!templateResult.isSuccess()) {
            return ApiResult.newError(templateResult.getErrorMsg());
        }
        AmazonTemplateBO templateBO = templateResult.getResult();
        templateBO.setPublishRole(message.getPublishRole());
        if (PublishRoleEnum.ADMIN.isTrue(message.getPublishRole())) {
            templateBO.setPublishType(PublishTypeEnum.AUTO_PUBLISH.getCode());
        }
        templateBO.setCreatedBy(message.getUser());
        if (MapUtils.isNotEmpty(message.getSkuAsinMap())) {
            // 使用asin刊登
            setTemplateAsin(templateBO, message.getSkuAsinMap());
            templateBO.setProductType(message.getProductType());
        }

        AmazonAccountRelation accountRelation = getAccountRelation(account.getAccountNumber());
        // 校验模板数据
        ApiResult<String> validateTemplateData = validateTemplateData(context, templateBO, accountRelation);
        if (!validateTemplateData.isSuccess()) {
            return ApiResult.newError(validateTemplateData.getErrorMsg());
        }
        // 更新处理报告sellerSku、templateId
        context.getProcessReport().setDataValue(templateBO.getSellerSKU());
        context.getProcessReport().setRelationId(templateBO.getId());
        // 执行刊登
        ApiResult<String> publishResult = publishTemplate(context);
        if (!publishResult.isSuccess()) {
            log.error("spu直接刊登失败, spu {}, site：{}, productType:{}: result:{}", templateBO.getParentSku(), templateBO.getCountry(), templateBO.getProductType(), publishResult);
        }
        return publishResult;
    }

    /**
     * 使用 asin 进行刊登
     *
     * @param templateBO 模版
     * @param skuAsinMap sku与asin映射关系
     */
    private void setTemplateAsin(AmazonTemplateBO templateBO, Map<String, String> skuAsinMap) {
        if (!AmazonTemplateUtils.isSaleVariant(templateBO)) {
            // 单体
            String parentSku = templateBO.getParentSku();
            String asin = skuAsinMap.get(parentSku);
            if (StringUtils.isNotBlank(asin)) {
                templateBO.setStandardProdcutIdType(IdType.ASIN);
                templateBO.setStandardProdcutIdValue(asin);
            }
            return;
        }


        // 变体
        List<AmazonSku> amazonSkus = templateBO.getAmazonSkus();
        if (CollectionUtils.isNotEmpty(amazonSkus)) {
            for (AmazonSku amazonSku : amazonSkus) {
                String asin = skuAsinMap.get(amazonSku.getSku());
                if (StringUtils.isNotBlank(asin)) {
                    amazonSku.setStandardProdcutIdType(IdType.ASIN);
                    amazonSku.setStandardProdcutIdValue(asin);
                }
            }
        }
        String asin = skuAsinMap.get(templateBO.getParentSku());
        templateBO.setStandardProdcutIdType(IdType.ASIN);
        templateBO.setStandardProdcutIdValue(asin);
        templateBO.setAmazonSkus(amazonSkus);
        templateBO.setVariations(JSON.toJSONString(amazonSkus));

    }

    /**
     * 匹配必填属性
     * 运营配置 > 技术部配置 > admin范本
     *
     * @param context
     */
    private void matchRequiredAttribute(AmazonPublishContext context) {
        AmazonTemplateBO templateInfo = context.getTemplate();
        String productType = templateInfo.getProductType();
        String site = templateInfo.getCountry();
        if (StringUtils.isBlank(productType)) {
            return;
        }

        AmazonRequiredAttributeDO requiredAttributeReq = new AmazonRequiredAttributeDO();
        requiredAttributeReq.setProductType(productType);
        requiredAttributeReq.setSite(site);
        List<ProductTypeTemplateJsonAttr> requiredAttributes = productTypeTemplateJsonAttrService.getTemplateRequiredAttribute(requiredAttributeReq);
        if (CollectionUtils.isEmpty(requiredAttributes)) {
            return;
        }

        // JSON schema
        AmazonCategorySpProductType productTypeDef = amazonCategorySpProductTypeService.getSchemaUrlProductType(site, productType);
        if (productTypeDef == null) {
            return;
        }

        String schemaUrl = productTypeDef.getSchemaUrl();
        if (StringUtils.isBlank(schemaUrl)) {
            return;
        }
        String schemaData = amazonCategorySpProductTypeService.getProductTypeSchemaLocalCache(productTypeDef.getSchemaUrl());
        if (StringUtils.isBlank(schemaData)) {
            throw new BusinessException(templateInfo.getCountry() + templateInfo.getProductType() + "获取schema properties失败");
        }
        JSONObject schemaJson = JSON.parseObject(schemaData);
        JSONObject properties = schemaJson.getJSONObject("properties");
        if (properties == null) {
            log.error("{}-{},获取schema properties失败", templateInfo.getCountry(), templateInfo.getProductType());
            throw new BusinessException(templateInfo.getCountry() + templateInfo.getProductType() + "获取schema properties失败");
        }
        if (StringUtils.isBlank(schemaData)) {
            return;
        }

        // GPSR 图片
        if (AmazonGPSRLocalEnums.getNeedGPSRSiteList().contains(templateInfo.getCountry())) {
            amazonListingGpsrInfoService.generateImage(templateInfo);
        }

        // 匹配技术部必填属性
        AmazonTemplatePropertiesUtil.matchTechnicalRequiredAttribute(templateInfo, requiredAttributes, schemaData, properties);
        // 运营配置
        AmazonTemplatePropertiesUtil.matchOperationalRequiredAttribute(templateInfo, requiredAttributes, properties);
        // 变体匹配变体属性
        AmazonTemplatePropertiesUtil.matchVariantTheme(templateInfo);
        // 默认属性中，可能存在非必须的附加属性，需要在刊登时移除
        Boolean retryMatchRequired = AmazonTemplatePropertiesUtil.removeAdditionalProperties(context, schemaData);
        if (retryMatchRequired) {
            AmazonTemplatePropertiesUtil.matchTechnicalRequiredAttribute(templateInfo, requiredAttributes, schemaData, properties);
        }
    }

    private ApiResult<String> calculatePrice(AmazonTemplateBO templateBO, AmazonAccountRelation accountRelation) {
        // 算价
        String calcPriceErrorResult = amazonAutoPublishHelper.calcPrice(accountRelation, templateBO, true);
        if (StringUtils.isNotBlank(calcPriceErrorResult)) {
            return ApiResult.newError(calcPriceErrorResult);
        }

        if (StringUtils.isBlank(calcPriceErrorResult) && accountRelation.getPromotionMargin() != null && accountRelation.getPromotionMargin() > 0) {
            // 计算促销价
            String calcPriceErrorResult2 = amazonAutoPublishHelper.calcPrice(accountRelation, templateBO, false);
            if (StringUtils.isNotBlank(calcPriceErrorResult2)) {
                return ApiResult.newError("计算促销价失败：" + calcPriceErrorResult2);
            }
        }
        return ApiResult.newSuccess();
    }

    /**
     * 校验模板数据
     */
    private ApiResult<String> validateTemplateData(AmazonPublishContext context, AmazonTemplateBO template, AmazonAccountRelation accountRelation) {
        Boolean isRePublish = context.getMessage().getIsRePublish();
        if (isRePublish) {
            // 重新刊登
            context.setTemplate(template);
            // 匹配必填属性
            matchRequiredAttribute(context);
            // 保存最新模板数据
            template.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
            template.setLastUpdatedBy(DataContextHolder.getUsername());
            template.setPublishStatus(AmaoznPublishStatusEnum.PUBLISHING.getStatusCode());
            template.setTable(AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
            if (context.getMessage().getTemplateId() != null) {
                template.setId(context.getMessage().getTemplateId());
                amazonTemplateService.updateByPrimaryKeySelective(template);
            } else {
                amazonTemplateService.insert(template);
            }
            return ApiResult.newSuccess("校验模板数据成功");
        }

        // 验证必填参数是否完整
        ApiResult<Object> result = AmazonAutoPublishUtil.verifyParamComplete(template, accountRelation);
        if (!result.isSuccess()) {
            return ApiResult.newError(result.getErrorMsg());
        }
        // 过滤重复刊登产品
        ApiResult<String> filterRepeatPublish = amazonTemplateRepeatPublishHelper.filterRepeatPublish(template);
        if (!filterRepeatPublish.isSuccess()) {
            filterRepeatPublish.setErrorMsg(template.getParentSku() + "是重复产品，不允许刊登和保存");
            return filterRepeatPublish;
        }
        // 刊登次数拦截
        ApiResult<String> categoryPublishNumberResult = amazonValidationHelper.validationCategoryPublishNumber(template);
        if (!categoryPublishNumberResult.isSuccess()) {
            return categoryPublishNumberResult;
        }
        // 刊登Amazon分类校验
        ApiResult<String> publishCategoryApiResult = amazonValidationHelper.validationAmazonPublishCategory(template);
        if (!publishCategoryApiResult.isSuccess()) {
            return publishCategoryApiResult;
        }

        // jp站点翻译前 删除侵权词
        if ("jp".equalsIgnoreCase(template.getCountry())) {
            ApiResult<String> delResult = templateDataValidationExecutor.delTemplateInfringementWords(template);
            if (!delResult.isSuccess()) {
                templateDataValidationExecutor.delTemplateInfringementWords(template);
            }
        }

        // 调用翻译接口翻译信息
        ApiResult<AmazonTemplateBO> translateResult = AmazonAutoPublishUtil.googleTranslateBo(template);
        if (!translateResult.isSuccess()) {
            return ApiResult.newError(translateResult.getErrorMsg());
        }
        // 校验品牌词
        ApiResult<String> brandResult = templateDataValidationExecutor.checkBrandInfringementWord(template);
        if (!brandResult.isSuccess()) {
            return brandResult;
        }

        // 翻译后 删除侵权词
        ApiResult<String> delResult = templateDataValidationExecutor.delTemplateInfringementWords(template);
        if (!delResult.isSuccess()) {
            templateDataValidationExecutor.delTemplateInfringementWords(template);
        }


        try {
            AmazonUtils.initAmazonTemplateSellerSKU(List.of(template));
        } catch (Exception e) {
            return ApiResult.newError("生成sellerSKU失败：" + e.getMessage());
        }

        if (!IdType.ASIN.equals(template.getStandardProdcutIdType())) {
            // 生成/验证EAN/UPC
            generateTemplateProductCode(template, context.getAccount());
            // ENA是否为空
            ApiResult<String> validationEANResult = amazonValidationHelper.validationEAN(template);
            if (!validationEANResult.isSuccess()) {
                return validationEANResult;
            }
        }

        // 算价
        ApiResult<String> calculatePriceResult = calculatePrice(template, accountRelation);
        if (!calculatePriceResult.isSuccess()) {
            return calculatePriceResult;
        }

        context.setTemplate(template);
        // 匹配必填属性
        matchRequiredAttribute(context);
        // 保存最新模板数据
        template.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        template.setLastUpdatedBy(DataContextHolder.getUsername());
        template.setPublishStatus(AmaoznPublishStatusEnum.PUBLISHING.getStatusCode());
        template.setTable(AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
        if (context.getMessage().getTemplateId() != null) {
            template.setId(context.getMessage().getTemplateId());
            amazonTemplateService.updateByPrimaryKeySelective(template);
        } else {
            amazonTemplateService.insert(template);
        }
        return ApiResult.newSuccess("校验模板数据成功");
    }


    @Override
    public String getType() {
        return AmazonPublishTypeEnums.SPU.name();
    }

    /**
     * 生成模板
     */
    private ApiResult<AmazonTemplateBO> generateTemplate(AmazonPublishContext context) {
        // 查询是否存在模板
        AmazonPublishMessage message = context.getMessage();
        // 重刊登模版复用
        if (Boolean.TRUE.equals(message.getIsRePublish())) {
            AmazonTemplateBO amazonTemplateBO = amazonTemplateService.selectBoById(message.getTemplateId(), AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
            amazonTemplateBO.setProductType(message.getProductType());
            log.info("重刊登模版复用:{},report:{},{}", message.getTemplateId(), message.getReportId(), message.getPublishType());
            return ApiResult.newSuccess(amazonTemplateBO);
        }
        // 失败模版复用ID
        ApiResult<AmazonTemplateBO> publishTemplate = getPublishTemplate(message.getAccountNumber(), message.getSpu(), message.getSite());
        if (!publishTemplate.isSuccess()) {
            return ApiResult.newError(publishTemplate.getErrorMsg());
        }
        AmazonTemplateBO templateBO = publishTemplate.getResult();
        if (templateBO != null) {
            message.setTemplateId(templateBO.getId());
        }
        // 生成新模版
        AmazonAutoPublishParam autoPublishParam = new AmazonAutoPublishParam();
        autoPublishParam.setParentSku(message.getSpu());
        autoPublishParam.setAccountNumber(message.getAccountNumber());
        autoPublishParam.setPublishRole(message.getPublishRole());
        autoPublishParam.setCountry(message.getSite());
        autoPublishParam.setSkuDataSource(message.getSkuDataSource());
        autoPublishParam.setInterfaceType(TemplateInterfaceTypeEnums.JSON.getCode());
        if (AmazonPublishTypeEnums.TIME_PUBLISH.isTrue(context.getPublishType().getCode())) {
            autoPublishParam.setPublishType(PublishTypeEnum.AUTO_PUBLISH_TEMPLATE_TIME.getCode());
        } else {
            autoPublishParam.setPublishType(PublishTypeEnum.AUTO_PUBLISH_TEMPLATE.getCode());
        }
        return amazonAutoPublishHelper.generateTemplate(autoPublishParam, context.getAccount());
    }


    /**
     * 查询Admin范本
     */
    private AmazonTemplateBO getAdminTemplate(String spu, String site) {
        AmazonTemplateExample example = new AmazonTemplateExample();
        example.setColumns("id,parent_sku,product_type,category_id,country,publish_status");
        example.setTable(AmazonTemplateTableEnum.AMAZON_TEMPLATE_ADMIN.getCode());
        example.createCriteria().andParentSkuEqualTo(spu)
                .andStatusEqualTo(1) // 启用
                .andCountryEqualTo(site);

        List<AmazonTemplateBO> amazonTemplateBOS = amazonTemplateService.selectFiledColumnsByExample(example);
        if (CollectionUtils.isEmpty(amazonTemplateBOS)) {
            return null;
        }
        return amazonTemplateBOS.get(0);
    }

    /**
     * 查询刊登模版
     */
    private ApiResult<AmazonTemplateBO> getPublishTemplate(String accountNumber, String spu, String site) {
        AmazonTemplateExample example = new AmazonTemplateExample();
        example.setColumns("id,parent_sku,product_type,category_id,country,publish_status");
        example.setTable(AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
        example.createCriteria()
                .andParentSkuEqualTo(spu)
                .andSellerIdEqualTo(accountNumber)
                .andCountryEqualTo(site);

        List<AmazonTemplateBO> amazonTemplateBOS = amazonTemplateService.selectFiledColumnsByExample(example);
        if (CollectionUtils.isEmpty(amazonTemplateBOS)) {
            return ApiResult.newSuccess(null);
        }

        Optional<AmazonTemplateBO> failTemplate = amazonTemplateBOS.stream()
                .filter(template -> AmaoznPublishStatusEnum.PUBLISH_FAIL.isTrue(template.getPublishStatus()))
                .findFirst();
        return failTemplate.map(ApiResult::newSuccess).orElseGet(() -> ApiResult.newSuccess(null));


    }


    private AmazonAccountRelation getAccountRelation(String accountNumber) {
        return amazonAccountRelationService.selectByAccount(accountNumber);
    }
}
