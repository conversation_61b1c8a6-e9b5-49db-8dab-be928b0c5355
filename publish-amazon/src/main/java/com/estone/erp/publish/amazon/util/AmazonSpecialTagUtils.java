package com.estone.erp.publish.amazon.util;

import com.estone.erp.publish.system.product.enums.SpecialTagEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Amazon特供标签工具类
 *
 * <AUTHOR>
 * @date 2025-06-05 11:11:24 +08:00
 * @description 统一管理Amazon特供标签相关判断逻辑，支持AMZ特供、TG-IP、TG-MZ、TG-KF四种特供标签
 */
public class AmazonSpecialTagUtils {

    /**
     * 所有Amazon特供标签的Code列表
     */
    public static final List<Integer> AMAZON_SPECIAL_TAG_CODES = Arrays.asList(
            SpecialTagEnum.s_2028.getCode(), // AMZ特供
            SpecialTagEnum.s_2039.getCode(), // TG-IP
            SpecialTagEnum.s_2038.getCode(), // TG-MZ
            SpecialTagEnum.s_2037.getCode()  // TG-KF
    );

    /**
     * 特供店铺可刊登的特殊标签Code列表（不包括TG-KF）
     */
    public static final List<Integer> SPECIAL_SHOP_ALLOWED_TAG_CODES = Arrays.asList(
            SpecialTagEnum.s_2028.getCode(), // AMZ特供
            SpecialTagEnum.s_2039.getCode(), // TG-IP
            SpecialTagEnum.s_2038.getCode()  // TG-MZ
    );


    /**
     * 所有Amazon EU-FBA标签标签的Code列表
     */
    public static final List<Integer> AMAZON_SPECIAL_TAG_CODES_EU_FBA = Arrays.asList(
            SpecialTagEnum.s_2040.getCode()  //EU-FBA
    );

    /**
     * 所有Amazon特供标签的Code Set（用于快速判断）
     */
    public static final Set<Integer> AMAZON_SPECIAL_TAG_CODE_SET =
            new HashSet<>(AMAZON_SPECIAL_TAG_CODES);

    /**
     * 判断特殊标签Code是否为Amazon特供标签
     *
     * @param specialTagCode 特殊标签Code
     * @return true-是特供标签，false-不是特供标签
     */
    public static boolean isAmazonSpecialTag(Integer specialTagCode) {
        return specialTagCode != null && AMAZON_SPECIAL_TAG_CODE_SET.contains(specialTagCode);
    }

    /**
     * 判断特殊标签Code列表中是否包含任一Amazon特供标签
     *
     * @param specialTagCodes 特殊标签Code列表
     * @return true-包含特供标签，false-不包含特供标签
     */
    public static boolean containsAnyAmazonSpecialTag(List<Integer> specialTagCodes) {
        if (CollectionUtils.isEmpty(specialTagCodes)) {
            return false;
        }
        return specialTagCodes.stream().anyMatch(AmazonSpecialTagUtils::isAmazonSpecialTag);
    }

    /**
     * 检查给定的标签列表是否包含特供店铺允许刊登的特殊标签
     *
     * @param specialTagCodes 标签Code列表
     * @return 是否包含特供店铺允许的标签
     */
    public static boolean containsSpecialShopAllowedTag(List<Integer> specialTagCodes) {
        if (CollectionUtils.isEmpty(specialTagCodes)) {
            return false;
        }
        if (isForbiddenSpecialCodes(specialTagCodes)) {
            return false;
        }

        return specialTagCodes.stream().anyMatch(SPECIAL_SHOP_ALLOWED_TAG_CODES::contains);
    }

    /**
     * 判断是否为TG-KF  禁止刊登特的供标签
     *
     * @param specialTagCodes
     * @return
     */
    public static boolean isForbiddenSpecialCodes(List<Integer> specialTagCodes) {
        if (CollectionUtils.isEmpty(specialTagCodes)) {
            return false;
        }
        return specialTagCodes.contains(SpecialTagEnum.s_2037.getCode());
    }




    /**
     * 判断特殊标签字符串中是否包含任一Amazon特供标签
     *
     * @param specialGoodsCode 特殊标签字符串（如：",2028,2039,"）
     * @return true-包含特供标签，false-不包含特供标签
     */
    public static boolean containsAnyAmazonSpecialTag(String specialGoodsCode) {
        if (StringUtils.isBlank(specialGoodsCode)) {
            return false;
        }
        return AMAZON_SPECIAL_TAG_CODES.stream()
                .anyMatch(code -> specialGoodsCode.contains("," + code + ","));
    }

    /**
     * 获取Amazon特供标签的查询条件字符串（用于ES查询）
     * 格式：2028,2039,2038,2037
     *
     * @return 特供标签查询条件字符串
     */
    public static List<String> getSpecialTagQueryString() {
        return AMAZON_SPECIAL_TAG_CODES.stream()
                .map(String::valueOf)
                .collect(Collectors.toList());
    }


    /**
     * 所有Amazon EU_FBA 的Code Set（用于快速判断）
     */
    public static final Set<Integer> AMAZON_SPECIAL_TAG_CODE_EU_FBA_SET =
            new HashSet<>(AMAZON_SPECIAL_TAG_CODES_EU_FBA);

    /**
     * 判断特殊标签Code是否为EU_FBA 标签
     *
     * @param specialTagCode 特殊标签Code
     * @return true-是EU_FBA标签，false-不是EU_FBA标签
     */
    public static boolean isAmazonSpecialTagEUFBA(Integer specialTagCode) {
        return specialTagCode != null && AMAZON_SPECIAL_TAG_CODE_EU_FBA_SET.contains(specialTagCode);
    }

    /**
     * 判断特殊标签Code列表中是否包含 EU_FBA
     *
     * @param specialTagCodes 特殊标签Code列表
     * @return true-包含EU_FBA 标签，false-不包含EU_FBA标签
     */
    public static boolean containsAnyAmazonSpecialTagEUFBA(List<Integer> specialTagCodes) {
        if (CollectionUtils.isEmpty(specialTagCodes)) {
            return false;
        }
        return specialTagCodes.stream().anyMatch(AmazonSpecialTagUtils::isAmazonSpecialTagEUFBA);
    }

    /**
     * 获取eu_fba 的查询条件字符串（用于ES查询）
     * 格式：2040
     *
     * @return eu_fba 标签查询条件字符串
     */
    public static List<String> getEuFbaQueryString() {
        return AMAZON_SPECIAL_TAG_CODES_EU_FBA.stream()
                .map(String::valueOf)
                .collect(Collectors.toList());
    }
} 