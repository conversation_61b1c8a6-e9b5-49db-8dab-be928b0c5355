package com.estone.erp.publish.tidb.publishtidb.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckCopywritingInfringementWordDTO {
    /**
     * 长标题6侵权词
     */
    private String longTitle6Check;

    /**
     * 长标题7侵权词
     */
    private String longTitle7Check;

    /**
     * 长标题8侵权词
     */
    private String longTitle8Check;

    /**
     * 关键词侵权词
     */
    private String mustKeywordApproved;

}
