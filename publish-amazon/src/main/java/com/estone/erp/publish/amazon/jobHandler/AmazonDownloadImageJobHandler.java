package com.estone.erp.publish.amazon.jobHandler;

import com.alibaba.fastjson.util.IOUtils;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.SeaweedFile;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.upload.FmsApiUtil;
import com.estone.erp.publish.amazon.biz.ExportProductSkuImageProcesser;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.base.pms.service.AmazonAccountService;
import com.estone.erp.publish.common.PictureCommon;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskTypeEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: ${description}
 * @Author: yjy
 * @Date: 2019/12/30 14:42
 * @Version: 1.0.0
 */
@Component
@Slf4j
public class AmazonDownloadImageJobHandler extends AbstractJobHandler {

    private FeedTaskService feedTaskService = SpringUtils.getBean(FeedTaskService.class);

    private AmazonAccountService amazonAccountService = SpringUtils.getBean(AmazonAccountService.class);

    public AmazonDownloadImageJobHandler() {
        super("AmazonDownloadImageJobHandler");
    }

    @Override
    @XxlJob("AmazonDownloadImageJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        logger.warn("*****************亚马逊图片下载开始*****************");
        long startTime = System.currentTimeMillis();
        this.batchDownloadAmazonImage();
        long endTime = System.currentTimeMillis();
        logger.warn("*****************亚马逊图片下载结束*****************耗时" + ((endTime - startTime) / 1000L));
        return ReturnT.SUCCESS;
    }

    /**
     * //完成时间超过2天状态改为过期
     */
    private void batchUpdateAmazonDownloadTaskStatusExpired(){
        FeedTask feedTask = new FeedTask();
        feedTask.setTaskStatus(TaskStatusEnum.EXPIRED.getStatusCode());
        feedTask.setTaskType(TaskTypeEnum.DOANLOAD_IMAGE.getStatusMsgEn());
        Date startTime = com.estone.erp.common.util.DateUtils.addDays(new Date(),-2);
        feedTask.setFinishTime(new Timestamp(startTime.getTime()));
        this.feedTaskService.updateAmazonDownloadTaskStatusExpired(feedTask);
    }

    /**
     * 运行时间超过1h状态改为失败(500个主sku下载的)
     */
    private void batchUpdateAmazonDownloadTaskStatusFail(){
        FeedTask feedTask = new FeedTask();
        feedTask.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
        feedTask.setTaskType(TaskTypeEnum.DOANLOAD_IMAGE.getStatusMsgEn());
        Date startTime = new Date();
        int downloadImageRunTime = Integer.valueOf(CacheUtils.SystemParamGet("AMAZON.IMAGE_DOWNLOAD_RUN_TIME").getParamValue());
        feedTask.setRunTime(new Timestamp(com.estone.erp.common.util.DateUtils.addMinutes(startTime,-downloadImageRunTime).getTime()));
        feedTask.setResultStatus(ResultStatusEnum.RESULT_FAIL.getStatusCode());
        feedTask.setFinishTime(new Timestamp(startTime.getTime()));
        feedTask.setResultMsg("下载超时请重新下载");
        this.feedTaskService.updateAmazonDownloadTaskStatusFile(feedTask);
    }

    private void batchDownloadAmazonImage(){
        //执行时间超过1h改为失败
        this.batchUpdateAmazonDownloadTaskStatusFail();
        //完成时间超过24小时状态改为过期
        this.batchUpdateAmazonDownloadTaskStatusExpired();
        // 查询在执行中的数量
        FeedTask feedTask = new FeedTask();
        feedTask.setTaskStatus(TaskStatusEnum.EXECUTING.getStatusCode());
        feedTask.setTaskType(TaskTypeEnum.DOANLOAD_IMAGE.getStatusMsgEn());
        int amount = feedTaskService.getAmazonDownloadImageExecutingCount(feedTask);
        int downloadImageExcutor = Integer.valueOf(CacheUtils.SystemParamGet("AMAZON.MAX_DOWNLOAD_AMAZON_IMAGE").getParamValue());
        if (amount< downloadImageExcutor){
            FeedTaskExample example = new FeedTaskExample();
            FeedTaskExample.Criteria criteria = example.createCriteria();
            criteria.andTaskTypeEqualTo(TaskTypeEnum.DOANLOAD_IMAGE.getStatusMsgEn());
            criteria.andTaskStatusEqualTo(TaskStatusEnum.WAITING.getStatusCode());
            //只跑20个主sku的
            criteria.andAttribute3IsNull();
            example.setLimit(downloadImageExcutor-amount);
            example.setOffset(0);
            example.setOrderByClause("create_time");
            //下载任务排队中
            List<FeedTask> feedTaskList = feedTaskService.selectByExample(example, Platform.Amazon.name());
            if (CollectionUtils.isNotEmpty(feedTaskList)){
                //更改为执行中
                this.downloadAmazonImage(feedTaskList);
            }
        }

    }

    public void downloadAmazonImage(List<FeedTask> feedTaskList) {
        for (FeedTask feedTask : feedTaskList){
            feedTask.setTaskStatus(TaskStatusEnum.EXECUTING.getStatusCode());
            feedTask.setRunTime(new Timestamp(System.currentTimeMillis()));
            feedTask.setPlatform(Platform.Amazon.name());
            feedTask.setTableIndex();
            feedTaskService.updateByPrimaryKeySelective(feedTask);
            AmazonExecutors.downloadAmazonImage(() -> {
                AmazonAccount account = amazonAccountService
                        .queryAmazonAccountByAccountNumber(feedTask.getAccountNumber());
                ExportProductSkuImageProcesser processer = new ExportProductSkuImageProcesser(account,
                        CommonUtils.splitList(feedTask.getArticleNumber().trim(), ",").stream()
                                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()),feedTask.getAssociationId(),feedTask.getAttribute6());

                long start = System.currentTimeMillis();
                String fileName = "amazon-Image-" + feedTask.getAccountNumber().trim()+ "-" + System.currentTimeMillis()+ ".xlsx";
                File file = new File(fileName.trim());
                if (file.exists()){
                    file.delete();
                }
                OutputStream os = null;
                try {
                    os = new FileOutputStream(file, true);
                    processer.exportAmazonImages(os);
                    String modle = PictureCommon.FILE_UPLOAD_IMAGE_DOWNLOAD_MODULE;
                    String source = PictureCommon.FILE_UPLOAD_AMAZON_SOURCE;
                    if (file.length() > 0) {
                        // 使用FmsApiUtil工具类替换pictureUploadService.uploadFile方法
                        ApiResult<SeaweedFile> uploadResult = FmsApiUtil.publishFileUpload(file, file.getName(), modle, source);
                        file.delete();
                        if (uploadResult.isSuccess()) {
                            SeaweedFile seaweedFile = uploadResult.getResult();
                            if (seaweedFile != null && seaweedFile.getUrl2() != null && seaweedFile.getUrl() != null) {
                                feedTask.setResultStatus(ResultStatusEnum.RESULT_SUCCESS.getStatusCode());
                                //路径
                                feedTask.setAttribute2(seaweedFile.getUrl2());
                            } else {
                                feedTask.setResultStatus(ResultStatusEnum.RESULT_FAIL.getStatusCode());
                                feedTask.setResultMsg("请求成功，上传Excel未获取到返回地址");
                            }
                        } else {
                            feedTask.setResultStatus(ResultStatusEnum.RESULT_FAIL.getStatusCode());
                            feedTask.setResultMsg(uploadResult.getErrorMsg() != null ? uploadResult.getErrorMsg() : "上传文件失败");
                        }
                    }else {
                        feedTask.setResultStatus(ResultStatusEnum.RESULT_FAIL.getStatusCode());
                        feedTask.setResultMsg("生成Excel失败");
                    }
                    feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                    feedTask.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
                    feedTaskService.updateByPrimaryKeySelective(feedTask);
                }catch (IOException e){
                    log.error(e.getMessage(), e);
                }
                finally {
                    long end = System.currentTimeMillis();
                    log.info("[{}] download image total time:[{}] s", feedTask.getAccountNumber(),
                            (end - start) / 1000);
                    IOUtils.close(os);
                }
            });
        }
    }

}
