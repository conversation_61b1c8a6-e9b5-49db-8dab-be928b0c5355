package com.estone.erp.publish.tidb.publishtidb.controller;


import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonLinkManagementTaskRecord;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonLinkManagementTaskRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
* <p>
* Amazon链接管理任务执行记录表 前端控制器
* </p>
*
* <AUTHOR>
* @since 2025-06-30
*/
@Slf4j
@RestController
@RequestMapping("/amazonLinkManagementTaskRecord")
public class AmazonLinkManagementTaskRecordController {
    @Resource
    private AmazonLinkManagementTaskRecordService amazonLinkManagementTaskRecordService;

    /**
     * 分页查询
     */
    @PostMapping("/queryPage")
    public CQueryResult<AmazonLinkManagementTaskRecord> queryPage(@RequestBody CQuery<AmazonLinkManagementTaskRecord> query) {
        try {
            return amazonLinkManagementTaskRecordService.queryPage(query);
        } catch (Exception e) {
            log.error("queryPage fail", e);
            return CQueryResult.failResult(e.getMessage());
        }
    }


    /**
     * 根据id查询详情
     */
    @GetMapping("/getDetail/{id}")
    public ApiResult<AmazonLinkManagementTaskRecord> getDetailById(@PathVariable Serializable id) {
        AmazonLinkManagementTaskRecord detail = amazonLinkManagementTaskRecordService.getById(id);
        return ApiResult.newSuccess(detail);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public ApiResult<String> update(@RequestBody AmazonLinkManagementTaskRecord entity) {
        boolean success = amazonLinkManagementTaskRecordService.updateById(entity);
        return ApiResult.newSuccess(success ? "更新成功" : "更新失败");
    }

    /**
     * 新增
     */
    @PostMapping("/save")
    public ApiResult<String> save(@RequestBody AmazonLinkManagementTaskRecord entity) {
        boolean success = amazonLinkManagementTaskRecordService.save(entity);
        return ApiResult.newSuccess(success ? "新增成功" : "新增失败");
    }

    /**
     * 根据id列表删除
     */
    @PostMapping("/delete")
    public ApiResult<String> deleteByIds(@RequestBody List<Serializable> ids) {
        boolean success = amazonLinkManagementTaskRecordService.removeByIds(ids);
        return ApiResult.newSuccess(success ? "删除成功" : "删除失败");
    }
}
