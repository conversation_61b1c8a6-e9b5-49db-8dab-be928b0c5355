package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.DingTalkUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.componet.download.AmazonDownloadTypeEnums;
import com.estone.erp.publish.amazon.enums.AmazonMarketingLogTypeEnum;
import com.estone.erp.publish.amazon.mq.AmazonQueues;
import com.estone.erp.publish.amazon.mq.model.AmazonOfflineConfigMessage;
import com.estone.erp.publish.amazon.util.AmazonConfigLogUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.system.excel.enums.ExcelDownloadStatusEnums;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import com.estone.erp.publish.system.excel.service.ExcelDownloadLogService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.*;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.enums.OfflineConfigEnums;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.enums.OfflineQueueEnums;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.queue.AmazonOfflineConfigListingQueueVO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.queue.AmazonOfflineQueueSearchDTO;
import com.estone.erp.publish.tidb.publishtidb.domain.req.CurrentAccountRequest;
import com.estone.erp.publish.tidb.publishtidb.mapper.AmazonOfflineConfigDataStatisticsMapper;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonMarketingConfigLog;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonOfflineConfig;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonOfflineConfigAccountReport;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonOfflineConfigDataStatistics;
import com.estone.erp.publish.tidb.publishtidb.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.groovy.util.Maps;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.estone.erp.publish.tidb.publishtidb.domain.offline.enums.OfflineConfigEnums.ExecutionStatus.*;

/**
 * <p>
 * Amazon 下架数据统计 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
@Slf4j
@Service
public class AmazonOfflineConfigDataStatisticsServiceImpl extends ServiceImpl<AmazonOfflineConfigDataStatisticsMapper, AmazonOfflineConfigDataStatistics> implements AmazonOfflineConfigDataStatisticsService {

    private static final String TOKEN = "0ed33de3d7d5045c215178c84ddfed598a8e2cda7881a98fd71d6f50dc5da171";

    private static final String SECRET = "SECac228a590929f9a6b44656d1ce36c7efaaf5e7d9f915123d52cb7799d0d728b3";

    @Resource
    private AmazonOfflineConfigService amazonOfflineConfigService;

    @Resource
    private AmazonMarketingConfigLogService amazonMarketingConfigLogService;

    @Resource
    private AmazonOfflineConfigListingQueueService amazonOfflineConfigListingQueueService;

    @Resource
    private AmazonOfflineConfigDataStatisticsService amazonOfflineConfigDataStatisticsService;

    @Resource
    private PermissionsHelper permissionsHelper;

    @Resource
    private RabbitMqSender rabbitMqSender;


    @Resource
    private AmazonOfflineConfigAccountReportService amazonOfflineConfigAccountReportService;

    @Resource
    private ExcelDownloadLogService excelDownloadLogService;


    @Override
    public String downloadTestAccount(AmazonOfflineConfigDataStatisticsSearchDTO searchDTO) {
        searchDTO.setIsDownloadTestAccountsDetails(true);
        searchDTO.setPageNum(1);
        searchDTO.setPageSize(Integer.MAX_VALUE);
        IPage<AmazonOfflineConfigDataStatisticsVO> search = this.search(searchDTO);
        if (CollectionUtils.isEmpty(search.getRecords())) {
            throw new BusinessException("过滤后没有可以操作的数据！");
        }
        List<Integer> configIds = search.getRecords().stream().map(AmazonOfflineConfigDataStatisticsVO::getConfigId).collect(Collectors.toList());


        Collection<AmazonOfflineConfig> amazonOfflineConfigs = amazonOfflineConfigService.listByIds(configIds);
        if (CollectionUtils.isEmpty(amazonOfflineConfigs)) {
            throw new BusinessException("获取不到下架配置");
        }

        //过滤出确认时间相同的数据
        Map<Integer, LocalDateTime> configConfirmTimeMap = amazonOfflineConfigs.stream()
                .filter(config -> config.getId() != null && config.getConfirmedTime() != null)
                .collect(Collectors.toMap(AmazonOfflineConfig::getId, AmazonOfflineConfig::getConfirmedTime));


        int downloadCount = 0;
        for (AmazonOfflineConfigDataStatisticsVO record : search.getRecords()) {
            List<String> testAccounts = record.getTestAccounts();
            if (CollectionUtils.isEmpty(testAccounts)) {
                continue;
            }

            boolean isDownloaded = false;
            for (String testAccount : testAccounts) {
                LocalDateTime time = configConfirmTimeMap.get(record.getConfigId());
                if (ObjectUtils.isEmpty(time)) {
                    continue;
                }
                String timeString = time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                AmazonOfflineQueueSearchDTO queueSearchDTO = new AmazonOfflineQueueSearchDTO();
                queueSearchDTO.setAccounts(List.of(testAccount));
                queueSearchDTO.setRuleIdList(List.of(record.getConfigId()));
                queueSearchDTO.setPageSize(1);
                queueSearchDTO.setPageIndex(1);
                queueSearchDTO.setStatusList(List.of(OfflineQueueEnums.QueueStatusType.PENDING_OFFLINE.getCode(), OfflineQueueEnums.QueueStatusType.CAN_OFFLINE_STATUS.getCode()));
                //将Time处理为年月日时分秒
                queueSearchDTO.setConfirmedTime(timeString);
                IPage<AmazonOfflineConfigListingQueueVO> iPage = amazonOfflineConfigListingQueueService.search(queueSearchDTO);
                if (CollectionUtils.isEmpty(iPage.getRecords())) {
                    continue;
                }
                long total = iPage.getTotal();
                AmazonOfflineConfigListingQueueDownLoadDTO amazonOfflineConfigListingQueueDownLoadDTO = new AmazonOfflineConfigListingQueueDownLoadDTO();
                amazonOfflineConfigListingQueueDownLoadDTO.setConfigIds(List.of(record.getConfigId()));
                amazonOfflineConfigListingQueueDownLoadDTO.setAccountNumbers(List.of(testAccount));
                amazonOfflineConfigListingQueueDownLoadDTO.setStatusList(List.of(OfflineQueueEnums.QueueStatusType.PENDING_OFFLINE.getCode(), OfflineQueueEnums.QueueStatusType.CAN_OFFLINE_STATUS.getCode()));
                amazonOfflineConfigListingQueueDownLoadDTO.setConfirmTime(timeString);
                String queryCondition = JSON.toJSONString(amazonOfflineConfigListingQueueDownLoadDTO);
                //发送队列
                sendDownloadMqMsg(AmazonDownloadTypeEnums.OFFLINE_CONFIG_DATA_STATISTICS_DETAIL.getType(), queryCondition, (int) total);
                isDownloaded = true;
            }

            if (isDownloaded){
                //记录下载时间
                this.recordDownloadTime(List.of(record.getId()), OfflineConfigEnums.ExecutionTime.TEST_ACCOUNT_DATA_DOWNLOAD_TIME.getName());
                downloadCount++;
            }

        }

        if (downloadCount == 0){
            throw new BusinessException("过滤后没有可以操作的数据！");
        }
        return "导出成功，稍后前往导出日志查看！";

    }


    private void sendDownloadMqMsg(String type, String queryCondition, int count) {
        ExcelDownloadLog downloadLog = new ExcelDownloadLog();
        downloadLog.setType(type);
        downloadLog.setQueryCondition(queryCondition);
        downloadLog.setDownloadCount(count);
        downloadLog.setStatus(ExcelDownloadStatusEnums.WAIT.getCode());
        downloadLog.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
        downloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
        // 发送队列
        excelDownloadLogService.addAndPushLog(downloadLog, SaleChannel.CHANNEL_AMAZON, PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_DOWNLOAD_QUEUE_KEY);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AmazonOfflineConfigDataStatisticsUpdateTestAccountVO configTestAccount(AmazonOfflineConfigDataStatisticsUpdateTestAccountDTO dto) {
        AmazonOfflineConfigDataStatisticsUpdateTestAccountVO vo = new AmazonOfflineConfigDataStatisticsUpdateTestAccountVO();
        if (ObjectUtils.isEmpty(dto)) {
            throw new BusinessException("请输入参数");
        }
        if (StringUtils.isBlank(dto.getAccountNumbers())) {
            throw new BusinessException("请输入店铺账号");
        }
        if (CollectionUtils.isEmpty(dto.getIds())) {
            throw new BusinessException("ID为空");
        }


        LambdaQueryWrapper<AmazonOfflineConfigDataStatistics> queryWrapper = new LambdaQueryWrapper<AmazonOfflineConfigDataStatistics>()
                .in(AmazonOfflineConfigDataStatistics::getId, dto.getIds())
                .notIn(AmazonOfflineConfigDataStatistics::getExecutionStatus,
                        List.of(PENDING_EXECUTION.getCode(), GENERATING_TOTAL_TABLE_DATA.getCode(),
                                PENDING_CONFIRMATION_TOTAL_TABLE_DATA.getCode(), TOTAL_TABLE_DATA_CONFIRMATION_FAILED.getCode(),
                                OFFLINE_CONFIRMED.getCode()));
        List<AmazonOfflineConfigDataStatistics> offlineConfigDataStatisticsList = amazonOfflineConfigDataStatisticsService.list(queryWrapper);
        if (CollectionUtils.isEmpty(offlineConfigDataStatisticsList)) {
            throw new BusinessException("没有可配置的测试店铺的数据");
        }
        int successCount = 0;
        int errorCount = 0;
        List<String> errorMessage = new ArrayList<>();
        for (AmazonOfflineConfigDataStatistics offlineConfigDataStatistics : offlineConfigDataStatisticsList) {
            List<String> testAccountList = List.of(dto.getAccountNumbers().split(","));
            AmazonOfflineConfigDataStatisticsVO offlineConfigDataStatisticsVO = offlineConfigDataStatistics.toVO(offlineConfigDataStatistics);
            if (ObjectUtils.isEmpty(offlineConfigDataStatisticsVO)) {
                continue;
            }
            //适用店铺
            List<String> accounts = offlineConfigDataStatisticsVO.getAccounts();

            //排除testAccountList中不存在于accounts的账号
            testAccountList = testAccountList.stream().filter(accounts::contains).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(testAccountList)) {
                errorCount++;
                String message = String.format("【%s】中的适用店铺不存在该测试店铺", offlineConfigDataStatisticsVO.getRuleName());
                errorMessage.add(message);
                continue;
            }

            //权限控制
            CurrentAccountRequest currentAccountRequest = new CurrentAccountRequest();
            ApiResult<List<String>> currentUserAccountsResult = amazonOfflineConfigService.getCurrentUserAccounts(currentAccountRequest);
            List<String> currentUserAccounts = currentUserAccountsResult.getResult();
            if (!currentUserAccountsResult.isSuccess() && CollectionUtils.isNotEmpty(currentUserAccounts)) {
                errorCount++;
                String message = String.format("【%s】没有店铺权限", offlineConfigDataStatisticsVO.getRuleName());
                errorMessage.add(message);
                continue;
            }
            //过滤权限控制
            testAccountList = testAccountList.stream().filter(currentUserAccounts::contains).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(testAccountList)) {
                errorCount++;
                String message = String.format("【%s】没有店铺权限", offlineConfigDataStatisticsVO.getRuleName());
                errorMessage.add(message);
                continue;
            }


            Map<String, Object> timestamps = JSON.parseObject(offlineConfigDataStatistics.getLatestTimeInfo(), new TypeReference<>() {
            });
            if (timestamps == null) {
                timestamps = new HashMap<>();
            }

            LocalDateTime now = LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS);
            timestamps.put(OfflineConfigEnums.ExecutionTime.TEST_ACCOUNT_CONFIG_TIME.getName(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(now));
            String testAccountJson = JSON.toJSONString(Maps.of("account", testAccountList));
            offlineConfigDataStatistics.setTestAccounts(testAccountJson);
            offlineConfigDataStatistics.setExecutionStatus(PENDING_CONFIRMATION_TEST_ACCOUNT_DETAIL_DATA.getCode());
            offlineConfigDataStatistics.setUpdatedTime(now);
            offlineConfigDataStatistics.setLatestTime(now);
            offlineConfigDataStatistics.setUpdatedBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
            offlineConfigDataStatistics.setLatestTimeInfo(JSON.toJSONString(timestamps));

            //记录日志
            this.saveConfigLogs(offlineConfigDataStatistics);
            amazonOfflineConfigDataStatisticsService.updateById(offlineConfigDataStatistics);
            successCount++;
        }
        vo.setSuccessCount(successCount);
        vo.setFailedCount(errorCount);
        vo.setErrorMessage(errorMessage);
        return vo;
    }

    @Override
    public String downloadCount(AmazonOfflineConfigDataStatisticsSearchDTO searchDTO) {
        searchDTO.setIsDownloadCount(true);
        searchDTO.setPageNum(1);
        searchDTO.setPageSize(Integer.MAX_VALUE);
        IPage<AmazonOfflineConfigDataStatisticsVO> search = this.search(searchDTO);
        if (CollectionUtils.isEmpty(search.getRecords())) {
            throw new BusinessException("过滤后没有可以操作的数据");
        }

        List<Integer> configIds = search.getRecords().stream().map(AmazonOfflineConfigDataStatisticsVO::getConfigId).collect(Collectors.toList());

        Collection<AmazonOfflineConfig> amazonOfflineConfigs = amazonOfflineConfigService.listByIds(configIds);
        if (CollectionUtils.isEmpty(amazonOfflineConfigs)) {
            throw new BusinessException("获取不到下架配置");
        }

        Map<Integer, LocalDateTime> configConfirmTimeMap = amazonOfflineConfigs.stream()
                .filter(config -> config.getId() != null && config.getConfirmedTime() != null)
                .collect(Collectors.toMap(AmazonOfflineConfig::getId, AmazonOfflineConfig::getConfirmedTime));

        int downloadCount = 0;
        for (AmazonOfflineConfigDataStatisticsVO record : search.getRecords()) {
            LocalDateTime time = configConfirmTimeMap.get(record.getConfigId());
            if (ObjectUtils.isEmpty(time)) {
                continue;
            }
            String timeString = time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LambdaUpdateWrapper<AmazonOfflineConfigAccountReport> queryWrapper = new LambdaUpdateWrapper<>();
            queryWrapper.eq(AmazonOfflineConfigAccountReport::getConfigId, record.getConfigId());
            queryWrapper.eq(AmazonOfflineConfigAccountReport::getStatisticsDate, timeString);
            int count = amazonOfflineConfigAccountReportService.count(queryWrapper);
            if (count == 0){
                continue;
            }

            AmazonOfflineConfigListingQueueDownLoadDTO dto = new AmazonOfflineConfigListingQueueDownLoadDTO();
            dto.setConfigIds(List.of(record.getConfigId()));
            dto.setConfirmTime(timeString);
            //发送队列
            sendDownloadMqMsg(AmazonDownloadTypeEnums.OFFLINE_CONFIG_DATA_STATISTICS.getType(),
                    JSON.toJSONString(dto), count);
            //记录下载时间
            this.recordDownloadTime(List.of(record.getId()), OfflineConfigEnums.ExecutionTime.TOTAL_DATA_DOWNLOAD_TIME.getName());
            downloadCount++;
        }

        if (downloadCount == 0){
            throw new BusinessException("过滤后没有可以操作的数据");
        }

        return "导出成功，稍后前往导出日志查看！";
    }

    private void recordDownloadTime(List<Integer> ids, String name) {
        ids.forEach(id -> {
            AmazonOfflineConfigDataStatistics offlineConfigDataStatistics = amazonOfflineConfigDataStatisticsService.getById(id);
            if (ObjectUtils.isEmpty(offlineConfigDataStatistics)) {
                return;
            }
            Map<String, Object> timestamps = JSON.parseObject(offlineConfigDataStatistics.getLatestTimeInfo(), new TypeReference<>() {
            });

            if (timestamps == null) {
                timestamps = new HashMap<>();
            }


            LocalDateTime now = LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS);
            timestamps.put(name, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(now));
            LambdaUpdateWrapper<AmazonOfflineConfigDataStatistics> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AmazonOfflineConfigDataStatistics::getId, id)
                    .set(AmazonOfflineConfigDataStatistics::getLatestTime, now)
                    .set(AmazonOfflineConfigDataStatistics::getUpdatedTime, now)
                    .set(AmazonOfflineConfigDataStatistics::getUpdatedBy, StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName())
                    .set(AmazonOfflineConfigDataStatistics::getLatestTimeInfo, JSON.toJSONString(timestamps));

            if (name.equals(OfflineConfigEnums.ExecutionTime.TEST_ACCOUNT_DATA_DOWNLOAD_TIME.getName())) {
                updateWrapper.set(AmazonOfflineConfigDataStatistics::getExecutionStatus, OfflineConfigEnums.ExecutionStatus.PENDING_CONFIRMATION_TEST_ACCOUNT_DETAIL_DATA.getCode());
                offlineConfigDataStatistics.setExecutionStatus(OfflineConfigEnums.ExecutionStatus.PENDING_CONFIRMATION_TEST_ACCOUNT_DETAIL_DATA.getCode());
                //记录操作日志
                this.saveConfigLogs(offlineConfigDataStatistics);
            }
            amazonOfflineConfigDataStatisticsService.update(updateWrapper);
        });
    }

    @Override
    public IPage<AmazonOfflineConfigDataStatisticsVO> search(AmazonOfflineConfigDataStatisticsSearchDTO searchParam) {
        //权限控制，销售仅可查看自己的数据，组长、主管仅可查看自己和下级数据，平台销售主管和超管可查看全部数据
        isAuth(searchParam);
        if (ObjectUtils.isEmpty(searchParam)) {
            throw new BusinessException("参数错误");
        }
        LambdaQueryWrapper<AmazonOfflineConfigDataStatistics> wrapper = buildSearchWrapper(searchParam);
        Page<AmazonOfflineConfigDataStatistics> page = new Page<>(searchParam.getPageNum(), searchParam.getPageSize());
        IPage<AmazonOfflineConfigDataStatistics> configDataStatisticsIpage = amazonOfflineConfigDataStatisticsService.page(page, wrapper);
        IPage<AmazonOfflineConfigDataStatisticsVO> iPage = new Page<>(configDataStatisticsIpage.getCurrent(), configDataStatisticsIpage.getSize(), configDataStatisticsIpage.getTotal());
        iPage.setPages(configDataStatisticsIpage.getPages());
        iPage.setRecords(configDataStatisticsIpage.getRecords().stream().map(item -> item.toVO(item)).collect(Collectors.toList()));
        return iPage;

    }

    private LambdaQueryWrapper<AmazonOfflineConfigDataStatistics> buildSearchWrapper(AmazonOfflineConfigDataStatisticsSearchDTO searchParam) {
        LambdaQueryWrapper<AmazonOfflineConfigDataStatistics> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(AmazonOfflineConfigDataStatistics::getCreatedTime);
        //下载状态排除总表数据生成中
        if (BooleanUtils.isTrue(searchParam.getIsDownloadCount())) {
            queryWrapper.notIn(AmazonOfflineConfigDataStatistics::getExecutionStatus,
                    List.of(GENERATING_TOTAL_TABLE_DATA.getCode(), PENDING_EXECUTION.getCode()));
        }
        //下载测试店铺数据批量操作时过滤待执行、总表数据生成中、待确认总表数据、总表数据确认失败、待配置测试店铺数据的状态数据。
        if (BooleanUtils.isTrue(searchParam.getIsDownloadTestAccountsDetails())) {
            queryWrapper.notIn(AmazonOfflineConfigDataStatistics::getExecutionStatus,
                    List.of(PENDING_EXECUTION.getCode(), GENERATING_TOTAL_TABLE_DATA.getCode(), PENDING_CONFIRMATION_TOTAL_TABLE_DATA.getCode()
                            , TOTAL_TABLE_DATA_CONFIRMATION_FAILED.getCode(), PENDING_TEST_ACCOUNT_DATA_CONFIGURATION.getCode()));
        }
        if (ObjectUtils.isNotEmpty(searchParam.getIdList())) {
            queryWrapper.in(AmazonOfflineConfigDataStatistics::getId, searchParam.getIdList());
            return queryWrapper;
        }
        if (ObjectUtils.isNotEmpty(searchParam.getType())) {
            queryWrapper.in(AmazonOfflineConfigDataStatistics::getType, searchParam.getType());
        }
        if (ObjectUtils.isNotEmpty(searchParam.getExecutionStatus())) {
            queryWrapper.in(AmazonOfflineConfigDataStatistics::getExecutionStatus, searchParam.getExecutionStatus());
        }
        if (StringUtils.isNotBlank(searchParam.getRuleName())) {
            queryWrapper.like(AmazonOfflineConfigDataStatistics::getRuleName, searchParam.getRuleName());
        }
        if (ObjectUtils.isNotEmpty(searchParam.getAccounts())) {
            List<String> accounts = searchParam.getAccounts();
            String condition = "JSON_OVERLAPS((accounts->'$.account'), '[\"" + StringUtils.join(accounts, "\",\"") + "\"]')";
            queryWrapper.apply(condition);
        }
        if (CollectionUtils.isNotEmpty(searchParam.getUpdatedBy())) {
            queryWrapper.in(AmazonOfflineConfigDataStatistics::getUpdatedBy, searchParam.getUpdatedBy());
        }
        if (StringUtils.isNotBlank(searchParam.getUpdatedTimeFrom()) && StringUtils.isNotBlank(searchParam.getUpdatedTimeTo())) {
            queryWrapper.between(AmazonOfflineConfigDataStatistics::getUpdatedTime, searchParam.getUpdatedTimeFrom(), searchParam.getUpdatedTimeTo());
        }
        if (CollectionUtils.isNotEmpty(searchParam.getCreatedBy())) {
            queryWrapper.in(AmazonOfflineConfigDataStatistics::getCreatedBy, searchParam.getCreatedBy());
        }

        return queryWrapper;
    }

    private void isAuth(AmazonOfflineConfigDataStatisticsSearchDTO searchParam) {
        ApiResult<Boolean> superAdminOrEquivalentResult = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_AMAZON);
        if (!superAdminOrEquivalentResult.isSuccess()) {
            throw new BusinessException(superAdminOrEquivalentResult.getErrorMsg());
        }
        // 平台销售主管和超管可查看全部数据
        if (superAdminOrEquivalentResult.getResult()) {
            return;
        }
        List<String> employeeNos = permissionsHelper.getCurrentPermissionEmployeeNo(SaleChannel.CHANNEL_AMAZON, false);
        searchParam.setCreatedBy(employeeNos);
    }

    @Override
    public void updateExecutionStatus(Integer configId) {
        List<AmazonOfflineConfigDataStatistics> amazonOfflineConfigDataStatistics = amazonOfflineConfigDataStatisticsService.list(
                new LambdaQueryWrapper<AmazonOfflineConfigDataStatistics>().eq(AmazonOfflineConfigDataStatistics::getConfigId, configId)
        );
        if (amazonOfflineConfigDataStatistics.isEmpty()) {
            return;
        }
        try {
            AmazonOfflineConfigDataStatistics offlineConfigDataStatistics = amazonOfflineConfigDataStatistics.get(0);

            // 判断配置是否存在或已确认
            AmazonOfflineConfig config = amazonOfflineConfigService.getById(offlineConfigDataStatistics.getConfigId());
            if (Objects.isNull(config) || config.getConfirmedStatus().equals(2)) {
                return;
            }

            OfflineConfigEnums.ExecutionStatus executionStatus = getByCode(offlineConfigDataStatistics.getExecutionStatus());
            switch (executionStatus) {
                case GENERATING_TOTAL_TABLE_DATA:
                    // 执行下架任务修改日志状态为总表数据生成完成时间
                    if (!offlineConfigDataStatistics.getExecutionStatus().equals(GENERATING_TOTAL_TABLE_DATA.getCode())) {
                        return;
                    }
                    handleAmazonOfflineConfigDataStatisticsData(
                            offlineConfigDataStatistics,
                            1,
                            config.getConfirmedTime(),
                            OfflineConfigEnums.ExecutionStatus.PENDING_CONFIRMATION_TOTAL_TABLE_DATA,
                            OfflineConfigEnums.ExecutionTime.TOTAL_DATA_COMPLETION_TIME,
                            offlineConfigDataStatistics.getAccounts()
                    );
                    break;
                case PENDING_CONFIRMATION_OFFLINE_TEST_ACCOUNT_DATA:
                    // 执行下架任务修改日志状态为测试店铺数据下架完成时间
                    handleAmazonOfflineConfigDataStatisticsData(
                            offlineConfigDataStatistics,
                            3,
                            config.getConfirmedTime(),
                            PENDING_CONFIRMATION_OFFLINE_TEST_ACCOUNT_DATA,
                            OfflineConfigEnums.ExecutionTime.TEST_ACCOUNT_OFFLINE_COMPLETION_TIME,
                            offlineConfigDataStatistics.getTestAccounts()
                    );
                    break;
                case OFFLINE_CONFIRMED:
                    // 执行下架任务修改日志状态为已确认下架时间、修改配置为已确认
                    handleAmazonOfflineConfigDataStatisticsData(
                            offlineConfigDataStatistics,
                            3,
                            config.getConfirmedTime(),
                            OFFLINE_CONFIRMED,
                            OfflineConfigEnums.ExecutionTime.CONFIRMED_DELETION_TIME,
                            offlineConfigDataStatistics.getAccounts()
                    );
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("更新下架配置数据统计状态失败", e);
        }
    }

    @Override
    public ApiResult<String> confirmStatus(AmazonOfflineConfigDataStatisticsConfirmDTO dto) {
        Collection<AmazonOfflineConfigDataStatistics> amazonOfflineConfigDataStatistics = amazonOfflineConfigDataStatisticsService.listByIds(dto.getIdList());

        // 判断禁用配置
        Set<Integer> configSet = amazonOfflineConfigDataStatistics.stream().map(AmazonOfflineConfigDataStatistics::getConfigId).collect(Collectors.toSet());
        LambdaQueryWrapper<AmazonOfflineConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AmazonOfflineConfig::getStatus, 0);
        queryWrapper.in(AmazonOfflineConfig::getId, configSet);
        List<AmazonOfflineConfig> amazonOfflineConfigs = amazonOfflineConfigService.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(amazonOfflineConfigs)) {
            LambdaQueryWrapper<AmazonOfflineConfigDataStatistics> wrapperLambdaQueryWrapper = new LambdaQueryWrapper<>();
            wrapperLambdaQueryWrapper.in(AmazonOfflineConfigDataStatistics::getConfigId, amazonOfflineConfigs.stream().map(AmazonOfflineConfig::getId).collect(Collectors.toSet()));
            wrapperLambdaQueryWrapper.in(AmazonOfflineConfigDataStatistics::getExecutionStatus, List.of(OFFLINE_TEST_ACCOUNT_DATA.getCode(), PENDING_CONFIRMATION_OFFLINE.getCode()));
            List<AmazonOfflineConfigDataStatistics> amazonOfflineConfigDataStatisticsList = amazonOfflineConfigDataStatisticsService.list(wrapperLambdaQueryWrapper);
            if (CollectionUtils.isNotEmpty(amazonOfflineConfigDataStatisticsList)) {
                String ruleNames = amazonOfflineConfigDataStatisticsList.stream().map(AmazonOfflineConfigDataStatistics::getRuleName).collect(Collectors.joining(","));
                return ApiResult.newError(String.format("配置：[%s]，禁用状态不允许执行下架操作！", ruleNames));
            }
        }

        for (AmazonOfflineConfigDataStatistics dataStatistic : amazonOfflineConfigDataStatistics) {
            LocalDateTime now = LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS);
            String format = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(now);
            Map<String, Object> timestamps = JSON.parseObject(dataStatistic.getLatestTimeInfo(), new TypeReference<>() {
            });

            // 获取配置信息
            AmazonOfflineConfig amazonOfflineConfig = amazonOfflineConfigService.getById(dataStatistic.getConfigId());

            // 根据状态判断
            OfflineConfigEnums.ExecutionStatus status = OfflineConfigEnums.ExecutionStatus.getByCode(dataStatistic.getExecutionStatus());
            switch (status) {
                case PENDING_CONFIRMATION_TOTAL_TABLE_DATA:
                case TOTAL_TABLE_DATA_CONFIRMATION_FAILED:
                    // 更新时间
                    timestamps.put(OfflineConfigEnums.ExecutionTime.TOTAL_DATA_CONFIRMATION_TIME.getName(), format);

                    // 待确认总表数据
                    if (dto.getConfirmStatus()) {
                        dataStatistic.setExecutionStatus(PENDING_TEST_ACCOUNT_DATA_CONFIGURATION.getCode());
                    } else {
                        dataStatistic.setExecutionStatus(TOTAL_TABLE_DATA_CONFIRMATION_FAILED.getCode());

                        // 发送钉钉群消息
                        sendDingtalkMessage(String.format("总表数据确认失败（规则名称：%s），请检查重新生成数据!", dataStatistic.getRuleName()));
                    }
                    break;
                case PENDING_CONFIRMATION_TEST_ACCOUNT_DETAIL_DATA:
                case TEST_ACCOUNT_DETAIL_DATA_CONFIRMATION_FAILED:
                    // 更新时间
                    timestamps.put(OfflineConfigEnums.ExecutionTime.TEST_ACCOUNT_DATA_CONFIRMATION_TIME.getName(), format);

                    // 待确认测试店铺明细数据
                    if (dto.getConfirmStatus()) {
                        dataStatistic.setExecutionStatus(OFFLINE_TEST_ACCOUNT_DATA.getCode());
                    } else {
                        dataStatistic.setExecutionStatus(TEST_ACCOUNT_DETAIL_DATA_CONFIRMATION_FAILED.getCode());

                        // 发送钉钉群消息
                        sendDingtalkMessage(String.format("测试店铺明细数据确认失败（规则名称：%s），请检查重新生成数据!", dataStatistic.getRuleName()));
                    }
                    break;
                case OFFLINE_TEST_ACCOUNT_DATA:
                    // 下架测试店铺数据
                    if (dto.getConfirmStatus()) {
                        dataStatistic.setExecutionStatus(PENDING_CONFIRMATION_OFFLINE_TEST_ACCOUNT_DATA.getCode());
                        if (MapUtil.isNotEmpty(timestamps)) {
                            timestamps.put(OfflineConfigEnums.ExecutionTime.TEST_ACCOUNT_OFFLINE_TIME.getName(), format);
                        }

                        // 执行下架任务修改日志状态为测试店铺数据下架完成时间
                        Map<String, List<String>> accountList = JSON.parseObject(dataStatistic.getTestAccounts(), new TypeReference<>() {
                        });
                        for (String account : accountList.get("account")) {
                            AmazonOfflineConfigMessage message = new AmazonOfflineConfigMessage(dataStatistic.getConfigId(), OfflineConfigEnums.OfflineType.getByCode(dataStatistic.getType()).name(), account, LocalDateTime.now(), amazonOfflineConfig.getConfirmedTime());
                            rabbitMqSender.allPublishVHostRabbitTemplateSend(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_OFFLINE_EXECUTE_QUEUE_KEY, JSON.toJSON(message));
                        }
                    }
                    break;
                case PENDING_CONFIRMATION_OFFLINE_TEST_ACCOUNT_DATA:
                case OFFLINE_TEST_ACCOUNT_DATA_CONFIRMATION_FAILED:
                    // 更新时间
                    timestamps.put(OfflineConfigEnums.ExecutionTime.TEST_ACCOUNT_OFFLINE_CONFIRMATION_TIME.getName(), format);

                    // 待确认下架测试店铺数据
                    if (dto.getConfirmStatus()) {
                        dataStatistic.setExecutionStatus(PENDING_CONFIRMATION_OFFLINE.getCode());
                    } else {
                        dataStatistic.setExecutionStatus(OFFLINE_TEST_ACCOUNT_DATA_CONFIRMATION_FAILED.getCode());

                        // 发送钉钉群消息
                        sendDingtalkMessage(String.format("下架测试店铺数据确认失败（规则名称：%s），请检查重新生成数据!", dataStatistic.getRuleName()));
                    }
                    break;
                case PENDING_CONFIRMATION_OFFLINE:
                    // 待确认下架
                    if (dto.getConfirmStatus()) {
                        dataStatistic.setExecutionStatus(OFFLINE_CONFIRMED.getCode());
                        // 执行下架任务修改日志状态为已确认下架时间、修改配置为已确认
                        Map<String, List<String>> accountList = JSON.parseObject(dataStatistic.getAccounts(), new TypeReference<>() {
                        });
                        for (String account : accountList.get("account")) {
                            AmazonOfflineConfigMessage message = new AmazonOfflineConfigMessage(dataStatistic.getConfigId(), OfflineConfigEnums.OfflineType.getByCode(dataStatistic.getType()).name(), account, LocalDateTime.now(), amazonOfflineConfig.getConfirmedTime());
                            rabbitMqSender.allPublishVHostRabbitTemplateSend(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_OFFLINE_EXECUTE_QUEUE_KEY, JSON.toJSON(message));
                        }
                    }
                    break;
                default:
                    break;
            }

            // 保存日志
            dataStatistic.setUpdatedBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
            dataStatistic.setUpdatedTime(now);
            dataStatistic.setLatestTime(now);
            if (MapUtil.isNotEmpty(timestamps)) {
                dataStatistic.setLatestTimeInfo(JSON.toJSONString(timestamps));
            }
            if (StringUtils.isNotBlank(dto.getRemark())) {
                dataStatistic.setRemark(dto.getRemark());
            }
            saveConfigLogs(dataStatistic);

            // 更新下架统计数据
            baseMapper.updateById(dataStatistic);
        }
        return ApiResult.newSuccess();
    }

    @Override
    public void updateAndSaveConfigLog(Boolean confirm, AmazonOfflineConfig updateConfig) {
        // 保存日志
        if (confirm) {
            AmazonOfflineConfigDataStatistics configDataStatistics = new AmazonOfflineConfigDataStatistics();
            configDataStatistics.setConfigId(updateConfig.getId());
            configDataStatistics.setExecutionStatus(OfflineConfigEnums.ExecutionStatus.PENDING_EXECUTION.getCode());
            configDataStatistics.setType(updateConfig.getType());
            configDataStatistics.setRuleName(updateConfig.getRuleName());
            configDataStatistics.setAccounts(updateConfig.getAccounts());
            configDataStatistics.setTestAccounts(null);
            configDataStatistics.setRule(updateConfig.getRule());
            configDataStatistics.setUpdatedBy(updateConfig.getUpdatedBy());
            configDataStatistics.setUpdatedTime(updateConfig.getUpdatedTime());
            saveConfigLogs(configDataStatistics);

            // 更新下架统计数据
            LambdaUpdateWrapper<AmazonOfflineConfigDataStatistics> updateWrapper = new LambdaUpdateWrapper<AmazonOfflineConfigDataStatistics>()
                    .eq(AmazonOfflineConfigDataStatistics::getConfigId, updateConfig.getId());
            baseMapper.update(configDataStatistics, updateWrapper);
        }
    }

    @Override
    public void updateAndSaveConfigLog(AmazonOfflineConfigDataStatistics dataStatistics) {
        // 保存日志
        saveConfigLogs(dataStatistics);

        // 更新下架统计数据
        baseMapper.updateById(dataStatistics);
    }

    /**
     * 处理配置统计数据状态信息
     *
     * @param offlineConfigDataStatistics
     * @param status
     * @param confirmedTime
     * @param executionStatus
     * @param executionTimeStatus
     * @param accounts
     */
    private void handleAmazonOfflineConfigDataStatisticsData(AmazonOfflineConfigDataStatistics offlineConfigDataStatistics, Integer status, LocalDateTime confirmedTime, OfflineConfigEnums.ExecutionStatus executionStatus, OfflineConfigEnums.ExecutionTime executionTimeStatus, String accounts) {
        Map<String, List<String>> accountMap = JSON.parseObject(accounts, new TypeReference<>() {
        });

        // 1、根据最新时间聚合明细表店铺数据
        List<String> accountList = amazonOfflineConfigAccountReportService.aggregateTotalTable(offlineConfigDataStatistics.getConfigId(), status, confirmedTime);
        log.info("配置：{},类型：{},任务完总店铺数：{},已完成店铺数：{}", offlineConfigDataStatistics.getRuleName(), executionStatus.getDesc(), accountMap.get("account").size(), accountList.size());
        if (CollectionUtils.isEmpty(accountList) || accountList.size() != accountMap.get("account").size()) {
            return;
        }

        // 2、修改状态为已确认
        if (OfflineConfigEnums.ExecutionStatus.OFFLINE_CONFIRMED.equals(executionStatus)) {
            LambdaUpdateWrapper<AmazonOfflineConfig> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AmazonOfflineConfig::getId, offlineConfigDataStatistics.getConfigId());
            updateWrapper.set(AmazonOfflineConfig::getConfirmedStatus, 2);
            amazonOfflineConfigService.update(updateWrapper);
        }

        // 3、保存日志
        Map<String, Object> timestamps = JSON.parseObject(offlineConfigDataStatistics.getLatestTimeInfo(), new TypeReference<>() {
        });
        LocalDateTime now = LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS);
        timestamps.put(executionTimeStatus.getName(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(now));
        offlineConfigDataStatistics.setExecutionStatus(executionStatus.getCode());
        offlineConfigDataStatistics.setLatestTime(now);
        offlineConfigDataStatistics.setLatestTimeInfo(JSON.toJSONString(timestamps));
        this.saveConfigLogs(offlineConfigDataStatistics);

        // 4、更新下架数据统计状态和最新时间
        amazonOfflineConfigDataStatisticsService.updateById(offlineConfigDataStatistics);
    }

    /**
     * 保存日志
     *
     * @param config
     */
    private void saveConfigLogs(AmazonOfflineConfigDataStatistics config) {
        // 获取旧的配置
        List<AmazonOfflineConfigDataStatistics> amazonOfflineConfigDataStatistics = amazonOfflineConfigDataStatisticsService.list(
                new LambdaQueryWrapper<AmazonOfflineConfigDataStatistics>().eq(AmazonOfflineConfigDataStatistics::getConfigId, config.getConfigId())
        );
        if (CollectionUtils.isEmpty(amazonOfflineConfigDataStatistics)) {
            return;
        }
        AmazonOfflineConfigDataStatisticsConvertDO oldConvertDO = AmazonOfflineConfigDataStatisticsConvertDO.reconvert(amazonOfflineConfigDataStatistics.get(0));
        AmazonOfflineConfigDataStatisticsConvertDO newCovertDO = AmazonOfflineConfigDataStatisticsConvertDO.reconvert(config);

        // 日志列表
        List<AmazonMarketingConfigLog> configLogs = AmazonConfigLogUtil.generateLog(newCovertDO, oldConvertDO, Long.valueOf(config.getConfigId()), AmazonMarketingLogTypeEnum.OFFLINE_DATA_STATISTICS);
        if (CollectionUtils.isNotEmpty(configLogs)) {
            amazonMarketingConfigLogService.saveBatch(configLogs);
        }
    }

    /**
     * 发送钉钉消息
     */
    public void sendDingtalkMessage(String message) {
        try {
            DingTalkUtil.sendPurchaseMsg(message, "下架数据确认失败钉钉消息推送", TOKEN, SECRET, false);
        } catch (Exception e) {
            throw new RuntimeException("发送钉钉消息失败");
        }
    }
}
