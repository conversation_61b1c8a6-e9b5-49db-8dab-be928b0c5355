package com.estone.erp.publish.tidb.publishtidb.domain.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 亚马逊链接管理配置查询请求对象
 * 根据需求文档4.1 查询条件设计实现
 * 
 * <AUTHOR>
 * @since 2024-07-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AmazonLinkManagementConfigRequest {

    private List<String> ids;

    /**
     * 规则名称 - 支持模糊查询
     */
    private String ruleName;

    /**
     * 店铺列表 - 多选，根据用户权限动态加载
     */
    private List<String> accounts;

    /**
     * 配置类型
     */
    private String configType;


    /**
     * 启用状态 - 单选，枚举值：null(全部)、1(启用)、0(禁用)
     */
    private Integer status;


    /**
     * 创建时间范围 - 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间范围 - 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;

    /**
     * 创建人
     */
    private String createBy;

    private List<String> createByList;
}