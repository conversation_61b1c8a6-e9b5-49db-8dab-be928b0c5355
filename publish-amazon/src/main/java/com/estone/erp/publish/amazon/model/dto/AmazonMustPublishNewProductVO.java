package com.estone.erp.publish.amazon.model.dto;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.SaleSuperiorReidsUtils;
import com.estone.erp.publish.amazon.model.AmazonMustPublishNewProduct;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 必刊登新品
 * <AUTHOR>
 * @date 2023-10-24 10:52
 */
@Data
public class AmazonMustPublishNewProductVO {


    /**
     * id
     */
    private Integer id;

    /**
     * spu
     */
    private String spu;

    /**
     * 产品类型 0:单属性, 1:多属性
     */
    private Integer productType;

    /**
     * 主管id
     */
    private String supervisorId;

    /**
     * 销售
     */
    private String saleId;

    /**
     * 销售组长
     */
    private String saleLeader;

    /**
     * 销售主管
     */
    private String saleManager;

    /**
     * 站点详情
     */
    private List<AmazonMustPublishNewSpuSiteInfoDO> siteInfos;

    /**
     * 类目路径名称
     */
    private String categoryPathName;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 文案审核状态
     */
    private Integer textAuditStatus;

    /**
     * 审核人
     **/
    private String auditBy;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 推送时间
     */
    private Date createdTime;

    /**
     * 进入单品时间
     */
    private Date spuCreatedTime;


    public static AmazonMustPublishNewProductVO conventToVO(AmazonMustPublishNewProduct record) {
        AmazonMustPublishNewProductVO vo = new AmazonMustPublishNewProductVO();
        vo.setId(record.getId());
        vo.setSpu(record.getSpu());
        vo.setProductType(record.getProductType());
        vo.setSupervisorId(record.getSupervisorId());
        vo.setCategoryPathName(record.getCategoryPathName());
        vo.setSaleId(record.getSaleId());
        vo.setSaleLeader(SaleSuperiorReidsUtils.getSaleSuperior(record.getSaleId(), true));
        vo.setSaleManager(SaleSuperiorReidsUtils.getSaleSuperior(vo.getSaleLeader(), true));
        vo.setSiteInfos(JSON.parseArray(record.getSiteInfo(), AmazonMustPublishNewSpuSiteInfoDO.class));
        vo.setRemarks(record.getRemarks());
        vo.setAuditStatus(record.getAuditStatus());
        vo.setTextAuditStatus(record.getTextAuditStatus());
        vo.setAuditBy(record.getAuditBy());
        vo.setAuditTime(record.getAuditTime());
        vo.setCreatedTime(record.getCreatedTime());
        vo.setSpuCreatedTime(record.getSpuCreatedTime());
        return vo;

    }
}
