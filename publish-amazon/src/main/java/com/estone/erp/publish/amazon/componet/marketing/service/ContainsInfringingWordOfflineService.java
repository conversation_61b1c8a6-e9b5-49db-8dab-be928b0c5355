package com.estone.erp.publish.amazon.componet.marketing.service;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.alert.logger.PublishLogger;
import com.estone.erp.common.alert.logger.PublishLoggerFactory;
import com.estone.erp.common.alert.policy.DefaultAlertPolicy;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.amazon.componet.marketing.AmazonOfflineConfigHandlerService;
import com.estone.erp.publish.amazon.model.AmazonAccountRelation;
import com.estone.erp.publish.amazon.model.AmazonAccountRelationExample;
import com.estone.erp.publish.amazon.mq.model.AmazonOfflineConfigMessage;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.system.erpCommon.module.WordValidateResult;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.AmazonOfflineConfigVO;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.enums.OfflineConfigEnums;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.enums.OfflineQueueEnums;
import com.estone.erp.publish.tidb.publishtidb.domain.offline.rule.ContainsInfringingWordRuleDO;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonOfflineConfigListingQueue;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.NestedQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
public class ContainsInfringingWordOfflineService extends AbstractOfflineConfigService implements AmazonOfflineConfigHandlerService {
    private final PublishLogger logger = PublishLoggerFactory.getLogger(ContainsInfringingWordOfflineService.class);
    @Autowired
    private AmazonAccountRelationService amazonAccountRelationService;

    @Override
    public String getOfflineType() {
        return OfflineConfigEnums.OfflineType.CONTAINS_INFRINGING_WORD.name();
    }

    @Override
    public ApiResult<String> executeOfflineConfig(AmazonOfflineConfigMessage message) {
        try {
            // 实现链接包含侵权词下架逻辑
            Integer configId = message.getConfigId();
            String accountNumber = message.getAccountNumber();
            ApiResult<AmazonOfflineConfigVO> configVOApiResult = amazonOfflineConfigService.editConfig(configId);
            if (!configVOApiResult.isSuccess()) {
                return ApiResult.newError("查询配置失败：" + configId);
            }
            AmazonOfflineConfigVO configVO = configVOApiResult.getResult();
            if (configVO == null) {
                return ApiResult.newError("配置不存在");
            }
            message.setConfirmedTime(configVO.getConfig().getConfirmedTime());
            executeContainsInfringingWord(accountNumber, configVO, message);
            logger.info("店铺:{},执行包含侵权词下架", accountNumber);
        } catch (Exception e) {
            logger.errorForPolicy("店铺:{},执行下架配置【{}】异常,e:{}", getOfflineType(), new DefaultAlertPolicy(), message.getAccountNumber(), message.getConfigId(), e.getMessage(), e);

        }
        return ApiResult.newSuccess("侵权词下架处理完成");
    }

    private void executeContainsInfringingWord(String accountNumber, AmazonOfflineConfigVO configVO, AmazonOfflineConfigMessage message) {
        ContainsInfringingWordRuleDO containsInfringingWordRule = configVO.getContainsInfringingWordRule();
        String infringingWords = containsInfringingWordRule.getInfringingWords().toLowerCase();
        String[] infringingWordsArr = infringingWords.split(",");
        // 获取白名单品牌
        Set<String> whiteListBrand = getWhiteListBrand(accountNumber);
        // 移除白名单品牌
        List<String> infringingWordList = Arrays.stream(infringingWordsArr)
                .filter(word -> !whiteListBrand.contains(word.trim()))
                .map(String::toLowerCase)
                .distinct().collect(Collectors.toList());

        List<Predicate<AmazonAsinSaleCountDO>> predicateList = transferToSaleCountCompareRules(containsInfringingWordRule.getSaleCount());
        logger.info("店铺:{},白名单品牌:{}, 侵权词:{}, 开始执行包含侵权词下架", accountNumber, whiteListBrand.size(), infringingWordList.size());
        // 构建查询条件
        EsAmazonProductListingRequest request = buildEsAmazonProductListingRequest(accountNumber, containsInfringingWordRule);
        List<String> brandTrademark = containsInfringingWordRule.getBrandTrademark();
        Consumer<BoolQueryBuilder> func = boolQueryBuilder -> {
            if (CollectionUtils.isNotEmpty(brandTrademark)) {
                BoolQueryBuilder shouldBoolQuery = QueryBuilders.boolQuery();
                shouldBoolQuery.should(QueryBuilders.boolQuery().filter(QueryBuilders.termsQuery("infringementWordInfos.originWord", infringingWordList)));
                shouldBoolQuery.should(QueryBuilders.boolQuery().filter(QueryBuilders.termsQuery("infringementWordInfos.trademarkIdentification", brandTrademark)));
                shouldBoolQuery.minimumShouldMatch(1);
                NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("infringementWordInfos", shouldBoolQuery, ScoreMode.Total);
                boolQueryBuilder.filter(nestedQuery);
            } else {
                BoolQueryBuilder infringementWordBoolQuery = QueryBuilders.boolQuery();
                infringementWordBoolQuery.filter(QueryBuilders.termsQuery("infringementWordInfos.originWord", infringingWordList));
                NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("infringementWordInfos", infringementWordBoolQuery, ScoreMode.Total);
                boolQueryBuilder.filter(nestedQuery);
            }
        };
        request.setFields(new String[]{"id", "accountNumber", "site", "parentAsin", "sonAsin", "sellerSku", "mainSku",
                "articleNumber", "itemStatus", "isOnline", "itemType", "skuStatus", "skuDataSource", "categoryId",
                "order_num_total", "normalSale", "openDate", "firstOpenDate", "trademarkIdentification", "infringementWordInfos",
                "infringementWord", "infringementObj", "infringementTypename", "forbidChannel", "normalSale"});

        esAmazonProductListingService.scrollQueryExecutorTask(request, func, listings -> {
            try {
                listingHandler(listings, infringingWordList, containsInfringingWordRule, predicateList, configVO, message);
            } catch (Exception e) {
                logger.error("店铺:{},执行下架配置【{}-{}】统计链接异常,e:{}", accountNumber, configVO.getConfig().getId(), configVO.getConfig().getRuleName(), e.getMessage(), e);
            }
        });

        // 查询店铺统计日期内待下架数量
        Integer waitOffLinkTotal = amazonOfflineConfigListingQueueService.getAccountOffCountByStatus(accountNumber, message.getConfirmedTime(), message.getScheduleTime(), OfflineQueueEnums.QueueStatusType.PENDING_OFFLINE.getCode(), OfflineConfigEnums.OfflineType.CONTAINS_INFRINGING_WORD.getCode());

        // 获取店铺在售链接数据
        Long onlineListingNum = esAmazonProductListingService.getOnlineListingNum(List.of(accountNumber));
        Integer offlinePercentage = configVO.getConfig().getOfflinePercentage();
        double limitRatio = offlinePercentage / 100.0;
        // 下架链接总数量：在线 * limitRatio 向下取整
        double canOffLink = onlineListingNum * limitRatio;
        int needOfflineTotal = (int) Math.floor(canOffLink);

        // 记录店铺处理报告
        addAccountReport(message, offlinePercentage, Math.toIntExact(onlineListingNum), waitOffLinkTotal, needOfflineTotal);

        // 检查下架数据记录表状态流转，
        amazonOfflineConfigDataStatisticsService.updateExecutionStatus(message.getConfigId());

        if (needOfflineTotal <= 0) {
            return;
        }
        int taskTaskCode = OfflineConfigEnums.OfflineType.CONTAINS_INFRINGING_WORD.getCode();
        // 按上架时间排序正序(旧->新),修改对应条数的待下架链接
        if (waitOffLinkTotal <= needOfflineTotal) {
            amazonOfflineConfigListingQueueService.updateOffLinkStatusOrderOpenTimeLimit(accountNumber, message.getConfirmedTime(), message.getScheduleTime(), taskTaskCode, OfflineQueueEnums.QueueStatusType.CAN_OFFLINE_STATUS.getCode(), waitOffLinkTotal);
        } else {
            amazonOfflineConfigListingQueueService.updateOffLinkStatusOrderOpenTimeLimit(accountNumber, message.getConfirmedTime(), message.getScheduleTime(), taskTaskCode, OfflineQueueEnums.QueueStatusType.CAN_OFFLINE_STATUS.getCode(), needOfflineTotal);
        }
    }

    private void listingHandler(List<EsAmazonProductListing> listings, List<String> infringingWordList, ContainsInfringingWordRuleDO rule, List<Predicate<AmazonAsinSaleCountDO>> predicateList, AmazonOfflineConfigVO configVO, AmazonOfflineConfigMessage message) {
        if (CollectionUtils.isEmpty(listings)) {
            return;
        }
        List<EsAmazonProductListing> infringementWordList = getInfringementWordList(listings, infringingWordList, rule);
        if (CollectionUtils.isEmpty(infringementWordList)) {
            return;
        }
        // 过滤下架定时公共规则
        List<EsAmazonProductListing> matchingList = filterPublishConfig(infringementWordList);
        if (CollectionUtils.isEmpty(matchingList)) {
            return;
        }

        List<String> sonAsinList = matchingList.stream().map(EsAmazonProductListing::getSonAsin).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, AmazonAsinSaleCountDO> sonAsinSaleCountMap = esAmazonProductListingService.getSonAsinSaleCount(sonAsinList);

        List<EsAmazonProductListing> waitOfflineListings = matchingList.stream().filter(listing -> {
            String sonAsin = listing.getSonAsin();
            AmazonAsinSaleCountDO amazonAsinSaleCountDO = sonAsinSaleCountMap.get(sonAsin);
            if (amazonAsinSaleCountDO == null) {
                return false;
            }
            return predicateList.stream().anyMatch(predicate -> predicate.test(amazonAsinSaleCountDO));
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(waitOfflineListings)) {
            return;
        }

        List<AmazonOfflineConfigListingQueue> addQueueList = new ArrayList<>(waitOfflineListings.size());
        BiConsumer<EsAmazonProductListing, AmazonOfflineConfigListingQueue> executeFunc = (listing, queue) -> {
            queue.setStatus(OfflineQueueEnums.QueueStatusType.PENDING_OFFLINE.getCode());
            queue.setTaskType(OfflineConfigEnums.OfflineType.CONTAINS_INFRINGING_WORD.getCode());
            setPublishQueueFiled(queue, listing, sonAsinSaleCountMap, message);
            Map<String, ContainsInfringingWordRuleDO> ruleData = new HashMap<>();
            ContainsInfringingWordRuleDO listingRule = rule.matchListingRule(listing, sonAsinSaleCountMap.get(listing.getSonAsin()));
            ruleData.put("rule", rule);
            ruleData.put("listing", listingRule);
            queue.setRuleInfo(JSON.toJSONString(ruleData));
        };

        List<AmazonOfflineConfigListingQueue> queues = amazonOfflineConfigListingQueueService.addListingToOffLinkQueue(waitOfflineListings, configVO, executeFunc);
        addQueueList.addAll(queues);
        if (CollectionUtils.isNotEmpty(addQueueList)) {
            amazonOfflineConfigListingQueueService.saveBatch(addQueueList, 300);
        }
    }

    private List<EsAmazonProductListing> getInfringementWordList(List<EsAmazonProductListing> listings, List<String> infringingWordList, ContainsInfringingWordRuleDO containsInfringingWordRule) {
        List<String> brandTrademark = containsInfringingWordRule.getBrandTrademark();
        String excludeWords = containsInfringingWordRule.getExcludeWords().toLowerCase();
        List<String> excludeWordList = Arrays.asList(excludeWords.split(","));

        List<EsAmazonProductListing> offLinkList = new ArrayList<>();
        listings.forEach(listing -> {
            List<WordValidateResult> infringementWordInfos = listing.getInfringementWordInfos();
            if (CollectionUtils.isNotEmpty(brandTrademark)) {
                List<String> trademarkWords = infringementWordInfos.stream()
                        .filter(wordValidateResult -> CollectionUtils.isNotEmpty(wordValidateResult.getTrademarkIdentification()))
                        .filter(wordValidateResult -> {
                            return brandTrademark.stream().anyMatch(trademark -> wordValidateResult.getTrademarkIdentification().contains(trademark));
                        })
                        .map(WordValidateResult::getOriginWord)
                        .map(String::toLowerCase)
                        .filter(word -> !excludeWordList.contains(word.trim()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(trademarkWords)) {
                    listing.setInfringingBrandWord(String.join(",", trademarkWords));
                    offLinkList.add(listing);
                    return;
                }
            }

            List<String> listingWords = infringementWordInfos.stream()
                    .map(WordValidateResult::getOriginWord)
                    .filter(StringUtils::isNotBlank)
                    .filter(word -> {
                        String replaced = word.replaceAll("-", "").replaceAll("\\.", "");
                        String lowerCaseWord = replaced.toLowerCase();
                        return infringingWordList.contains(lowerCaseWord);
                    })
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(listingWords)) {
                listing.setInfringingBrandWord(String.join(",", listingWords));
                offLinkList.add(listing);
            }
        });
        return offLinkList;

    }

    /**
     * 获取白名单品牌
     * 1. 店铺配置品牌
     * 2. 参数白名单
     */
    private Set<String> getWhiteListBrand(String accountNumber) {
        Set<String> whiteListBrand = new HashSet<>();
        // 获取店铺配置品牌
        List<String> accountConfigurationBrands = getAccountConfigurationBrand(accountNumber);
        if (CollectionUtils.isNotEmpty(accountConfigurationBrands)) {
            whiteListBrand.addAll(accountConfigurationBrands);
        }
        // 参数白名单
        String whiteList = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "amazon_param", "brand_word_white_list", 10);
        if (StringUtils.isNotBlank(whiteList)) {
            String[] whiteListArr = whiteList.toLowerCase().split(",");
            whiteListBrand.addAll(Arrays.asList(whiteListArr));
        }
        return whiteListBrand;
    }

    /**
     * 获取店铺配置品牌
     *
     * @param accountNumber
     * @return
     */
    private List<String> getAccountConfigurationBrand(String accountNumber) {
        AmazonAccountRelationExample example = new AmazonAccountRelationExample();
        example.createCriteria().andAccountNumberEqualTo(accountNumber);
        example.setFiledColumns("account_number,brand,record_brand,auth_brand");
        List<AmazonAccountRelation> accountRelationList = amazonAccountRelationService.selectFiledColumnsByExample(example);
        if (CollectionUtils.isEmpty(accountRelationList)) {
            return null;
        }
        AmazonAccountRelation accountRelation = accountRelationList.get(0);
        String brand = accountRelation.getBrand();
        String recordBrand = accountRelation.getRecordBrand();
        String authBrand = accountRelation.getAuthBrand();

        Function<String, List<String>> brandFunc = data -> {
            if (StringUtils.isBlank(data)) {
                return new ArrayList<>();
            }
            String lowerCase = data.toLowerCase();
            List<String> brandList = Arrays.asList(lowerCase.split(","));
            return brandList.stream().map(String::trim).distinct().collect(Collectors.toList());
        };

        List<String> dataBrandList = new ArrayList<>();

        List<String> brandList = brandFunc.apply(brand);
        List<String> recordBrandList = brandFunc.apply(recordBrand);
        List<String> authBrandList = brandFunc.apply(authBrand);
        dataBrandList.addAll(brandList);
        dataBrandList.addAll(recordBrandList);
        dataBrandList.addAll(authBrandList);
        return dataBrandList.stream().distinct().collect(Collectors.toList());
    }
} 