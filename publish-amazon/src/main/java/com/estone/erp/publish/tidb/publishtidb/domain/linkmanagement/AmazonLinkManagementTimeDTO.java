package com.estone.erp.publish.tidb.publishtidb.domain.linkmanagement;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * Amazon链接管理配置 - 规则JSON字段DTO
 * 对应设计文档3.4.2 rule字段结构
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Data
public class AmazonLinkManagementTimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 执行频率：day-每日，week-每周，month-每月
     */
    private String exeFrequency;

    /**
     * 执行日期
     */
    private List<String> executeDate;
    /**
     * 执行时间，格式如：09:00
     */
    private String exeTime;

    /**
     * 策略开始生效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime strategyStartTime;

    /**
     * 策略结束时间，NULL表示永久有效
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime strategyEndTime;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}