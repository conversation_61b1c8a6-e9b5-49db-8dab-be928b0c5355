package com.estone.erp.publish;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.amazon.componet.publish.util.JsonFeedSampler;
import com.estone.erp.publish.amazon.componet.publish.util.JsonFeedSamplerFactory;
import com.estone.erp.publish.amazon.componet.validation.AmazonTemplateValidationContext;
import com.estone.erp.publish.amazon.enums.AmazonTemplateValidationEnum;
import com.estone.erp.publish.amazon.model.dto.AmazonTemplateValidationDO;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.lucene.search.spell.LevenshteinDistance;
import org.junit.Test;

import java.io.File;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Path;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class ThemeTest {

    // 最多允许10个merchantId同时执行
    private static final int MAX_CONCURRENT_MERCHANTS = 10;
    // 存储每个merchantId的令牌桶
    private static final Map<String, Semaphore> MERCHANT_TOKENS = new ConcurrentHashMap<>();
    // 线程池
    private static final ExecutorService executorService = Executors.newFixedThreadPool(20);
    // 用于提交任务的线程池
    private static final ExecutorService submitExecutor = Executors.newFixedThreadPool(5);


    @Test
    public void modifyExcelAndPreserveHeaderTest() {
        String originalFilePath = "C:\\Users\\<USER>\\Desktop\\6001514407_30000166052_非半托管商品_1751305024346_1HrH2k11.xlsx";
        String newFilePath = "C:\\Users\\<USER>\\Desktop\\modified_with_header.xlsx";
        // 假设您的文件有2行表头
        int headRowNumber = 2;

        // 1. 读取: 创建监听器并执行读取操作
        ExcelDataListener listener = new ExcelDataListener();
        EasyExcel.read(originalFilePath, listener).sheet().headRowNumber(headRowNumber).doRead();

        // 从监听器获取表头和数据
        Map<Integer, List<String>> headerRows = listener.getHeaderRows();
        List<Map<Integer, String>> dataList = listener.getDataList();

        log.info("成功读取到 {} 行表头和 {} 行数据。", headerRows.size(), dataList.size());
        List<List<String>> headers = headerRows.values().stream().collect(Collectors.toList());
        // 2. 修改: 在内存中修改数据
        // 示例：将第一列（索引为0）的数据末尾加上 "_MODIFIED"
        for (Map<Integer, String> rowData : dataList) {
            String value = rowData.get(0); // 获取第一列的数据
            if (value != null) {
                rowData.put(0, value + "_MODIFIED");
            }
        }
        log.info("数据修改完成。");

        // 3. 写入: 将原始表头和修改后的数据写入新文件
        EasyExcel.write(newFilePath)
                .head(headers) // <-- 关键步骤：传入原始表头
                .sheet("Sheet1")
                .doWrite(dataList);

        log.info("已将修改后的数据连同原始表头一起写入到新文件: {}", newFilePath);
    }

    @Test
    public void testRemoveTitleDuplicateWord() {
        LocalDateTime creatingDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).minusDays(2);
        log.error(creatingDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        String title = "Apple for banana for Apple cherry for banana Apple and of and for";
        String newTitle = removeTitleDuplicateWord(title);
        log.info("title:{}", newTitle);

        AmazonTemplateValidationContext validationContext = new AmazonTemplateValidationContext();
        validationContext.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.CAN_NOT_SKU_STATUS, true));
        validationContext.addValidationData(new AmazonTemplateValidationDO(AmazonTemplateValidationEnum.SITE_BAN, true));
        validationContext.addValidationData(AmazonTemplateValidationDO.failOf(AmazonTemplateValidationEnum.CAN_NOT_PUBLISH_SPECIAL_TAG_ACCOUNT,
                "非特供店铺不能刊登特供标签产品，过滤后无可刊登SKU", null));

        String message = validationContext.getValidations().stream().map(AmazonTemplateValidationDO::getErrorMsg).findFirst().orElse("非特供店铺，不能上架AMZ特供产品");

        log.info("message:{}", message);

    }

    /**
     * 检查标题重复词汇，重复的词出现3次时保留前面2个
     * 排除词汇（duplicate_word配置）不进行去重处理，但会移除末尾所有连续的排除词汇
     *
     * @param title
     * @return
     */
    public static String removeTitleDuplicateWord(String title) {
        if (title == null || title.trim().isEmpty()) {
            return title;
        }
        Set<String> duplicateWordSet = new HashSet<>();
        String systemParamValue = "and,for,in";
        if (StringUtils.isNotBlank(systemParamValue)) {
            duplicateWordSet.addAll(CommonUtils.splitList(systemParamValue, ","));
        }
        String[] words = title.trim().split("\\s+");
        Map<String, Integer> wordCount = new HashMap<>();
        List<String> resultWords = new ArrayList<>();

        for (String word : words) {
            String lowerCaseWord = word.toLowerCase();

            // 如果词汇在排除集合中，直接保留，不进行去重处理
            if (duplicateWordSet.contains(lowerCaseWord)) {
                resultWords.add(word);
                continue;
            }

            int count = wordCount.getOrDefault(lowerCaseWord, 0);

            // 如果该词汇出现次数少于2次，则保留
            if (count < 2) {
                resultWords.add(word);
                wordCount.put(lowerCaseWord, count + 1);
            }
            // 如果已经出现2次或更多，则跳过（即第3次及以后的出现会被移除）
        }

        // 移除末尾所有连续的排除词汇（从后往前查找非排除词汇的位置）
        int lastValidIndex = resultWords.size() - 1;
        while (lastValidIndex >= 0) {
            String word = resultWords.get(lastValidIndex);
            if (duplicateWordSet.contains(word.toLowerCase())) {
                lastValidIndex--;
            } else {
                break;
            }
        }

        // 如果找到了有效位置，则截取到该位置；否则返回空列表
        if (lastValidIndex >= 0) {
            resultWords = resultWords.subList(0, lastValidIndex + 1);
        } else {
            resultWords.clear();
        }

        return String.join(" ", resultWords);
    }


    /**
     * 一个通用的监听器，用于在一次读取中同时捕获表头和所有数据行。
     */
    public static class ExcelDataListener extends AnalysisEventListener<Map<Integer, String>> {
        private Map<Integer, List<String>> headerRows = new HashMap<>();
        private final List<Map<Integer, String>> dataList = new ArrayList<>();

        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            headMap.forEach((k, v) -> {
                List<String> orDefault = headerRows.getOrDefault(k, new ArrayList<>());
                orDefault.add(v);
                headerRows.put(k, orDefault);
            });
        }

        @Override
        public void invoke(Map<Integer, String> data, AnalysisContext context) {
            // 收集每一行数据
            dataList.add(data);
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            log.info("文件读取完成！");
        }

        // Getter 方法，用于在外部获取结果
        public Map<Integer, List<String>> getHeaderRows() {
            return headerRows;
        }

        public List<Map<Integer, String>> getDataList() {
            return dataList;
        }
    }


    @Test
    public void levenshteinExample() {
        LevenshteinDistance levenshteinDistance = new LevenshteinDistance();
        String text1 = "musical instruments";
        String text2 = "musical instrument toy\n" +
                "brass and woodwind instruments\n" +
                "misc other\n" +
                "percussion instruments\n" +
                "toys and games\n" +
                "instrument parts and accessories";


        String[] split = text2.split("\n");
        Arrays.stream(split).forEach(text -> {
            float distance1 = levenshteinDistance.getDistance(text1, text);
            double similarity1 = 1.0 - (double) distance1 / Math.max(text1.length(), text.length());
            System.out.println(String.format("'%s' 和 '%s' 的编辑距离是: %f", text1, text, similarity1));

        });

    }

    @Test
    public void samplerTest() {
        JsonFeedSamplerFactory jsonFeedSamplerFactory = new JsonFeedSamplerFactory();
        JsonFeedSampler jsonFeedSampler = jsonFeedSamplerFactory.getFeedJsonSampler(20);
        for (int i = 0; i < 100; i++) {
            boolean sample = jsonFeedSampler.sample(i);
            if (sample) {
                log.info("sample:{}", i);
            }
        }

    }

    @Test
    public void test() {
//        BigDecimal usd = ListingPriceData.priceNumberFormat("USD", 9.9d);
//        log.info("usd:{}", usd);
//
//        // 示例测试
//        System.out.println("USD, 9.9   -> " + ListingPriceData.priceNumberFormat("USD", 9.99));    // 预期: 9.90
//        System.out.println("USD, 9.82   -> " + ListingPriceData.priceNumberFormat("USD", 9.88));    // 预期: 9.90
//        System.out.println("USD, 9.79  -> " + ListingPriceData.priceNumberFormat("USD", 9.79));   // 预期: 9.99
//        System.out.println("USD, 9.995 -> " + ListingPriceData.priceNumberFormat("USD", 9.995));  // 预期: 10.00
//        System.out.println("USD, 9.994 -> " + ListingPriceData.priceNumberFormat("USD", 9.994));  // 预期: 9.99
//        System.out.println("JPY, 123.456 -> " + ListingPriceData.priceNumberFormat("JPY", 123.456)); // 预期: 123
//        System.out.println("JPY, 123.789 -> " + ListingPriceData.priceNumberFormat("JPY", 123.789)); // 预期: 124

        String str1 = "For EM5 DSLR Pouc";
        String str2 = "For DSLR Pouch";

        System.out.println("字符串 '" + str1 + "' 是否包含数字? " + containsDigit(str1));
        System.out.println("字符串 '" + str2 + "' 是否包含数字? " + containsDigit(str2));


        Map<String, Set<String>> merchantIdAsinMap = new HashMap<>();
        for (String merchantId : List.of("A", "B", "c")) {
            List.of("1", "2", "2", "3", "3", "4", "4", "5", "9", "10").forEach(asin -> {
                merchantIdAsinMap.computeIfAbsent(merchantId, k -> Sets.newHashSet()).add(asin);
            });
        }

        log.info("merchantIdAsinMap:{}", merchantIdAsinMap);


    }

    public static boolean containsDigit(String s) {
        // 首先检查字符串是否为null或空
        if (s == null || s.isEmpty()) {
            return false;
        }
        // 使用Stream API检查是否包含任何数字字符
        return s.chars().anyMatch(Character::isDigit);
    }


    @Test
    public void testDownloadFile() {
        File excelFile = getExcelFile("http://10.100.1.200:8888/productInfo/2025-06/20-09-44-47-153/Ozon-1741410-2025-06-209730886741202013180.xlsx");
        log.info("excelFile:{}", excelFile);
        if (excelFile == null) {
            return;
        }
    }

    private File getExcelFile(String url) {
        try {
            HttpClient client = HttpClient
                    .newBuilder()
                    .version(HttpClient.Version.HTTP_1_1)
                    .followRedirects(HttpClient.Redirect.NORMAL)
                    .connectTimeout(Duration.ofSeconds(20))
                    .build();

            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(Duration.ofMinutes(2))
                    .header("Content-Type", "application/json")
                    .GET().build();

            // 时间戳
            File tempFile = File.createTempFile("" + LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")), ".xlsx");
            HttpResponse<Path> response = client.send(request, HttpResponse.BodyHandlers.ofFile(tempFile.toPath()));
            if (response.statusCode() != 200) {
                throw new RuntimeException("请求失败，状态码：" + response.statusCode());
            }
            if (response.body() == null) {
                throw new RuntimeException("请求失败，返回结果为空");
            }
            if (!tempFile.exists()) {
                throw new RuntimeException("请求失败，文件不存在");
            }
            return tempFile;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void sampleTest() {
        try {
            // 初始化全局令牌池
            Semaphore globalSemaphore = new Semaphore(MAX_CONCURRENT_MERCHANTS);
            CountDownLatch submitLatch = new CountDownLatch(5); // 假设5个线程提交任务

            // 多个线程同时提交任务
            for (int t = 0; t < 5; t++) {
                final int threadId = t;
                submitExecutor.submit(() -> {
                    try {
                        // 每个线程提交20个任务
                        for (int i = 0; i < 20; i++) {
                            final int taskId = threadId * 20 + i;
                            final String merchantId = "merchant_" + (taskId % 5);

                            // 获取或创建merchantId对应的令牌桶
                            Semaphore merchantSemaphore = MERCHANT_TOKENS.computeIfAbsent(
                                    merchantId, k -> new Semaphore(1));

                            // 尝试获取全局令牌和商户令牌
                            if (globalSemaphore.tryAcquire(5, TimeUnit.SECONDS)) {
                                if (merchantSemaphore.tryAcquire(5, TimeUnit.SECONDS)) {
                                    // 成功获取到令牌后提交任务
                                    submitTask(merchantId, taskId, globalSemaphore, merchantSemaphore);
                                } else {
                                    // 获取商户令牌失败，释放全局令牌
                                    globalSemaphore.release();
                                    System.out.println("商户[" + merchantId + "]繁忙，任务[" + taskId + "]放弃执行");
                                }
                            } else {
                                System.out.println("系统繁忙，任务[" + taskId + "]放弃执行");
                            }

                            // 模拟任务提交间隔
                            Thread.sleep(100);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        submitLatch.countDown();
                    }
                });
            }

            // 等待所有提交线程完成
            submitLatch.await();
            
            // 关闭线程池
            submitExecutor.shutdown();
            executorService.shutdown();
            executorService.awaitTermination(1, TimeUnit.HOURS);
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 清理资源
            MERCHANT_TOKENS.clear();
        }
    }

    private void submitTask(String merchantId, int taskId, Semaphore globalSemaphore,
                            Semaphore merchantSemaphore) {
        executorService.submit(() -> {
            try {
                System.out.println(String.format("商户[%s]的任务[%d]开始执行 - 当前时间: %d",
                        merchantId, taskId, System.currentTimeMillis()));

                // 模拟任务执行
                Thread.sleep(2000);

                System.out.println(String.format("商户[%s]的任务[%d]执行完成 - 当前时间: %d",
                        merchantId, taskId, System.currentTimeMillis()));

            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                // 释放令牌
                merchantSemaphore.release();
                globalSemaphore.release();
            }
        });
    }

    @Test
    public void matchProductType() {
        // KEYCHAIN
        String text_en = "Having the most applicable product type for a product enhances both the seller and customer experiences; therefore, we have amended the Product Type of your submission from KEYCHAIN to CHARM. To remove the warning attached to your product, simply accept our suggested product type and resubmit the SKU. Alternatively, if the original product type you selected is correct, you can change the SKU attributes such as title, product description and bullet points, so that your product matches the original product type more closely. If you feel both the product type you had selected and the original SKU attributes are correct, please contact Seller Support/Selling Partner Support.";
        // CONSUMER_ELECTRONICS
        String text_de = "Wenn Sie den am besten geeigneten Produkttyp für ein Produkt haben, wird sowohl das Verkäufer- als auch das Kundenerlebnis verbessert. Daher haben wir die Produkttyp Ihrer Einreichung von CONSUMER_ELECTRONICS auf ELECTRIC_MOTOR geändert. Um die an Ihrem Produkt angehängte Warnung zu entfernen, akzeptieren Sie einfach unseren empfohlenen Produkttyp und reichen Sie die SKU erneut ein. Wenn der ursprüngliche Produkttyp, den Sie ausgewählt haben, korrekt ist, können Sie alternativ die SKU-Attribute wie Titel, Produktbeschreibung und Aufzählungspunkte ändern, sodass Ihr Produkt deutlicher mit diesem ursprünglichen Produkttyp übereinstimmt. Wenn Sie der Meinung sind, dass sowohl der ausgewählte Produkttyp als auch die ursprünglichen SKU-Attribute korrekt sind, wenden Sie sich bitte an den Service für Verkaufspartner";
        // HOME
        String text_fr = "Le fait de disposer du type de produit le plus approprié pour un produit améliore à la fois l'expérience du vendeur et celle du client ; par conséquent, nous avons modifié Type de produit de votre demande de HOME à MEAL_HOLDER. Pour supprimer l'avertissement joint à votre produit, il vous suffit d'accepter notre type de produit suggéré et d’envoyer à nouveau le SKU. Sinon, si le type de produit d'origine que vous avez sélectionné est correct, vous pouvez modifier les attributs du SKU, tels que le titre, la description du produit et les puces, afin que votre produit corresponde plus clairement au type de produit d'origine. Si vous pensez que le type de produit que vous avez sélectionné et les attributs du SKU d'origine sont corrects, contactez le Support aux partenaires de vente.";


        String keyWord = "[\\\"1Stainless Steel Lunch Box\\\\n2Leak proof Lunch Container\\\\n3Portable Food Carriers\\\\n4Durable Meal Box\\\\n5Double Layer Lunch box\\\",\\\"6Insulated Food Container\\\\n7Office Lunch Solution\\\\n8Student Lunch Container\\\\n9Outdoor Picnics Box\\\\n10Travel Food Storage\\\",\\\"11Thermal Lunch Box\\\\n12Easy to Use Lunch box\\\\n13Heat3333 dddddRetaining Food Jar\\\\n14User Friendly Lunch box\\\\n Lunch Container\\\"]";
        List<String> strings = JSON.parseArray(keyWord, String.class);
        

        List<String> texts = Arrays.asList(text_en, text_de, text_fr);


        // 该正则表达式查找所有由大写字母和下划线组成的单词（长度至少为2）。
        // 我们假设找到的前两个匹配项分别是“from”和“to”的产品类型。
        Pattern pattern = Pattern.compile("([A-Z_]{2,})");

        for (String text : texts) {
            Matcher matcher = pattern.matcher(text);
            List<String> matches = new ArrayList<>();
            while (matcher.find()) {
                matches.add(matcher.group(1));
            }

            if (matches.size() >= 2) {
                String from = matches.get(0);
                String to = matches.get(1);
                System.out.println("----------- Matched Text -----------");
                System.out.println("From: " + from);
                System.out.println("To: " + to);
            }
        }


    }
}
