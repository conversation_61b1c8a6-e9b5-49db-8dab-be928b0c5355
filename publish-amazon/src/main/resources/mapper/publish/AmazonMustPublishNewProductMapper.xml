<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.amazon.mapper.AmazonMustPublishNewProductMapper">
    <resultMap id="BaseResultMap" type="com.estone.erp.publish.amazon.model.AmazonMustPublishNewProduct" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="spu" property="spu" jdbcType="VARCHAR" />
        <result column="supervisor_id" property="supervisorId" jdbcType="VARCHAR" />
        <result column="sale_id" property="saleId" jdbcType="VARCHAR" />
        <result column="published_site" property="publishedSite" jdbcType="VARCHAR" />
        <result column="is_ban" property="isBan" jdbcType="BIT" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="site_info" property="siteInfo" jdbcType="VARCHAR" />
        <result column="category_full_path_code" property="categoryFullPathCode" jdbcType="VARCHAR" />
        <result column="category_path_name" property="categoryPathName" jdbcType="VARCHAR" />
        <result column="product_type" property="productType" jdbcType="INTEGER" />
        <result column="us_publish_status" property="usPublishStatus" jdbcType="INTEGER" />
        <result column="de_publish_status" property="dePublishStatus" jdbcType="INTEGER" />
        <result column="fr_publish_status" property="frPublishStatus" jdbcType="INTEGER" />
        <result column="uk_publish_status" property="ukPublishStatus" jdbcType="INTEGER" />
        <result column="it_publish_status" property="itPublishStatus" jdbcType="INTEGER" />
        <result column="es_publish_status" property="esPublishStatus" jdbcType="INTEGER" />
        <result column="jp_publish_status" property="jpPublishStatus" jdbcType="INTEGER" />
        <result column="ca_publish_status" property="caPublishStatus" jdbcType="INTEGER" />
        <result column="us_is_band" property="usIsBand" jdbcType="BIT" />
        <result column="de_is_band" property="deIsBand" jdbcType="BIT" />
        <result column="fr_is_band" property="frIsBand" jdbcType="BIT" />
        <result column="uk_is_band" property="ukIsBand" jdbcType="BIT" />
        <result column="it_is_band" property="itIsBand" jdbcType="BIT" />
        <result column="es_is_band" property="esIsBand" jdbcType="BIT" />
        <result column="jp_is_band" property="jpIsBand" jdbcType="BIT" />
        <result column="ca_is_band" property="caIsBand" jdbcType="BIT" />
        <result column="remarks" property="remarks" jdbcType="VARCHAR" />
        <result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR" />
        <result column="audit_status" property="auditStatus" jdbcType="INTEGER"/>
        <result column="text_audit_status" property="textAuditStatus" jdbcType="INTEGER"/>
        <result column="audit_by" property="auditBy" jdbcType="VARCHAR"/>
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP"/>
        <result column="spu_created_time" property="spuCreatedTime" jdbcType="TIMESTAMP" />
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" suffix=")" prefixOverrides="and">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")"
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List" >
        id, spu, supervisor_id, sale_id, published_site, is_ban, `status`,
    site_info, category_full_path_code, category_path_name, supervisor_id, product_type, us_publish_status, de_publish_status,
    fr_publish_status, uk_publish_status, it_publish_status, es_publish_status, jp_publish_status,
    ca_publish_status, us_is_band, de_is_band, fr_is_band, uk_is_band, it_is_band, es_is_band,
        jp_is_band,
        ca_is_band,
        remarks,
        last_updated_by,
        audit_status,
        text_audit_status,
        audit_by,
        audit_time,
        spu_created_time,
        created_time,
    updated_time
    </sql>
    <select id="selectByExample" resultMap="BaseResultMap"
            parameterType="com.estone.erp.publish.amazon.model.AmazonMustPublishNewProductExample">
        select
        <if test="distinct">
            distinct
        </if>
        'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from amazon_must_publish_new_product
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from amazon_must_publish_new_product
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey">
        delete from amazon_must_publish_new_product
        where id IN
        <foreach collection="list" item="listItem" open="(" close=")" separator=",">
            #{listItem}
        </foreach>
    </delete>
    <insert id="insert" parameterType="com.estone.erp.publish.amazon.model.AmazonMustPublishNewProduct" >
        <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into amazon_must_publish_new_product (spu, supervisor_id,
        sale_id, published_site, is_ban,
        `status`, site_info, category_full_path_code,
        category_path_name, product_type, us_publish_status, de_publish_status,
        fr_publish_status, uk_publish_status, it_publish_status,
        es_publish_status, jp_publish_status, ca_publish_status,
        us_is_band, de_is_band, fr_is_band, uk_is_band,
        it_is_band, es_is_band, jp_is_band, ca_is_band,
        remarks, last_updated_by, audit_status, text_audit_status, audit_by, audit_time, spu_created_time,
        created_time, updated_time)
        values (#{spu,jdbcType=VARCHAR}, #{supervisorId,jdbcType=VARCHAR},
        #{saleId,jdbcType=VARCHAR}, #{publishedSite,jdbcType=VARCHAR}, #{isBan,jdbcType=BIT},
        #{status,jdbcType=INTEGER}, #{siteInfo,jdbcType=VARCHAR}, #{categoryFullPathCode,jdbcType=VARCHAR},
        #{categoryPathName,jdbcType=VARCHAR}, #{productType,jdbcType=INTEGER}, #{usPublishStatus,jdbcType=INTEGER}, #{dePublishStatus,jdbcType=INTEGER},
        #{frPublishStatus,jdbcType=INTEGER}, #{ukPublishStatus,jdbcType=INTEGER}, #{itPublishStatus,jdbcType=INTEGER},
        #{esPublishStatus,jdbcType=INTEGER}, #{jpPublishStatus,jdbcType=INTEGER}, #{caPublishStatus,jdbcType=INTEGER},
        #{usIsBand,jdbcType=BIT}, #{deIsBand,jdbcType=BIT}, #{frIsBand,jdbcType=BIT}, #{ukIsBand,jdbcType=BIT},
        #{itIsBand,jdbcType=BIT}, #{esIsBand,jdbcType=BIT}, #{jpIsBand,jdbcType=BIT}, #{caIsBand,jdbcType=BIT},
        #{remarks,jdbcType=VARCHAR}, #{lastUpdatedBy,jdbcType=VARCHAR},
        #{auditStatus,jdbcType=INTEGER}, #{textAuditStatus,jdbcType=INTEGER}, #{auditBy,jdbcType=VARCHAR},
        #{auditTime,jdbcType=TIMESTAMP},
        #{spuCreatedTime,jdbcType=TIMESTAMP},#{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP})
    </insert>
    <select id="countByExample" parameterType="com.estone.erp.publish.amazon.model.AmazonMustPublishNewProductExample"
            resultType="java.lang.Integer">
        select count(*) from amazon_must_publish_new_product
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into amazon_must_publish_new_product (spu, supervisor_id, sale_id, published_site,
        is_ban, `status`, site_info,
        category_full_path_code, category_path_name,product_type,
        us_publish_status, de_publish_status, fr_publish_status,
        uk_publish_status, it_publish_status, es_publish_status,
        jp_publish_status, ca_publish_status, us_is_band,
        de_is_band, fr_is_band, uk_is_band, it_is_band,
        es_is_band, jp_is_band, ca_is_band, remarks,
        last_updated_by, audit_status, text_audit_status, audit_by, audit_time, spu_created_time, created_time,
        updated_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.spu,jdbcType=VARCHAR}, #{item.supervisorId,jdbcType=VARCHAR},
            #{item.saleId,jdbcType=INTEGER}, #{item.publishedSite,jdbcType=VARCHAR},
            #{item.isBan,jdbcType=BIT}, #{item.status,jdbcType=INTEGER}, #{item.siteInfo,jdbcType=VARCHAR},
            #{item.categoryFullPathCode,jdbcType=VARCHAR}, #{item.categoryPathName,jdbcType=VARCHAR},
            #{item.productType,jdbcType=INTEGER},
            #{item.usPublishStatus,jdbcType=INTEGER}, #{item.dePublishStatus,jdbcType=INTEGER},
            #{item.frPublishStatus,jdbcType=INTEGER},
            #{item.ukPublishStatus,jdbcType=INTEGER}, #{item.itPublishStatus,jdbcType=INTEGER},
            #{item.esPublishStatus,jdbcType=INTEGER},
            #{item.jpPublishStatus,jdbcType=INTEGER}, #{item.caPublishStatus,jdbcType=INTEGER},
            #{item.usIsBand,jdbcType=BIT},
            #{item.deIsBand,jdbcType=BIT}, #{item.frIsBand,jdbcType=BIT}, #{item.ukIsBand,jdbcType=BIT},
            #{item.itIsBand,jdbcType=BIT},
            #{item.esIsBand,jdbcType=BIT}, #{item.jpIsBand,jdbcType=BIT}, #{item.caIsBand,jdbcType=BIT},
            #{item.remarks,jdbcType=VARCHAR},
            #{item.lastUpdatedBy,jdbcType=VARCHAR},
            #{item.auditStatus,jdbcType=INTEGER}, #{item.textAuditStatus,jdbcType=INTEGER},
            #{item.auditBy,jdbcType=VARCHAR},
            #{item.auditTime,jdbcType=TIMESTAMP},
            #{item.spuCreatedTime,jdbcType=TIMESTAMP},
            #{item.createdTime,jdbcType=TIMESTAMP},
            #{item.updatedTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <select id="listSpuBySpu" resultType="java.lang.String">
        select spu from amazon_must_publish_new_product where spu in
        <foreach collection="spu" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <update id="updateByExampleSelective" parameterType="map">
        update amazon_must_publish_new_product
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=INTEGER},
            </if>
            <if test="record.spu != null">
                spu = #{record.spu,jdbcType=VARCHAR},
            </if>
            <if test="record.supervisorId != null" >
                supervisor_id = #{record.supervisorId,jdbcType=VARCHAR},
            </if>
            <if test="record.saleId != null">
                sale_id = #{record.saleId,jdbcType=VARCHAR},
            </if>
            <if test="record.publishedSite != null">
                published_site = #{record.publishedSite,jdbcType=VARCHAR},
            </if>
            <if test="record.isBan != null">
                is_ban = #{record.isBan,jdbcType=BIT},
            </if>
            <if test="record.status != null">
                `status` = #{record.status,jdbcType=INTEGER},
            </if>
            <if test="record.siteInfo != null">
                site_info = #{record.siteInfo,jdbcType=VARCHAR},
            </if>
            <if test="record.categoryFullPathCode != null" >
                category_full_path_code = #{record.categoryFullPathCode,jdbcType=VARCHAR},
            </if>
            <if test="record.categoryPathName != null" >
                category_path_name = #{record.categoryPathName,jdbcType=VARCHAR},
            </if>
            <if test="record.productType != null">
                product_type = #{record.productType,jdbcType=INTEGER},
            </if>
            <if test="record.usPublishStatus != null">
                us_publish_status = #{record.usPublishStatus,jdbcType=INTEGER},
            </if>
            <if test="record.dePublishStatus != null">
                de_publish_status = #{record.dePublishStatus,jdbcType=INTEGER},
            </if>
            <if test="record.frPublishStatus != null">
                fr_publish_status = #{record.frPublishStatus,jdbcType=INTEGER},
            </if>
            <if test="record.ukPublishStatus != null">
                uk_publish_status = #{record.ukPublishStatus,jdbcType=INTEGER},
            </if>
            <if test="record.itPublishStatus != null">
                it_publish_status = #{record.itPublishStatus,jdbcType=INTEGER},
            </if>
            <if test="record.esPublishStatus != null">
                es_publish_status = #{record.esPublishStatus,jdbcType=INTEGER},
            </if>
            <if test="record.jpPublishStatus != null">
                jp_publish_status = #{record.jpPublishStatus,jdbcType=INTEGER},
            </if>
            <if test="record.caPublishStatus != null">
                ca_publish_status = #{record.caPublishStatus,jdbcType=INTEGER},
            </if>
            <if test="record.usIsBand != null">
                us_is_band = #{record.usIsBand,jdbcType=BIT},
            </if>
            <if test="record.deIsBand != null">
                de_is_band = #{record.deIsBand,jdbcType=BIT},
            </if>
            <if test="record.frIsBand != null">
                fr_is_band = #{record.frIsBand,jdbcType=BIT},
            </if>
            <if test="record.ukIsBand != null">
                uk_is_band = #{record.ukIsBand,jdbcType=BIT},
            </if>
            <if test="record.itIsBand != null">
                it_is_band = #{record.itIsBand,jdbcType=BIT},
            </if>
            <if test="record.esIsBand != null">
                es_is_band = #{record.esIsBand,jdbcType=BIT},
            </if>
            <if test="record.jpIsBand != null">
                jp_is_band = #{record.jpIsBand,jdbcType=BIT},
            </if>
            <if test="record.caIsBand != null">
                ca_is_band = #{record.caIsBand,jdbcType=BIT},
            </if>
            <if test="record.remarks != null">
                remarks = #{record.remarks,jdbcType=VARCHAR},
            </if>
            <if test="record.lastUpdatedBy != null">
                last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
            </if>
            <if test="record.auditStatus != null">
                audit_status = #{record.auditStatus,jdbcType=INTEGER},
            </if>
            <if test="record.textAuditStatus != null">
                text_audit_status = #{record.textAuditStatus,jdbcType=INTEGER},
            </if>
            <if test="record.auditBy != null">
                audit_by = #{record.auditBy,jdbcType=VARCHAR},
            </if>
            <if test="record.auditTime != null">
                audit_time = #{record.auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.spuCreatedTime != null">
                spu_created_time = #{record.spuCreatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.createdTime != null">
                created_time = #{record.createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updatedTime != null">
                updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.estone.erp.publish.amazon.model.AmazonMustPublishNewProduct">
        update amazon_must_publish_new_product
        <set>
            <if test="spu != null">
                spu = #{spu,jdbcType=VARCHAR},
            </if>
            <if test="supervisorId != null" >
                supervisor_id = #{supervisorId,jdbcType=VARCHAR},
            </if>
            <if test="saleId != null">
                sale_id = #{saleId,jdbcType=VARCHAR},
            </if>
            <if test="publishedSite != null">
                published_site = #{publishedSite,jdbcType=VARCHAR},
            </if>
            <if test="isBan != null">
                is_ban = #{isBan,jdbcType=BIT},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="siteInfo != null">
                site_info = #{siteInfo,jdbcType=VARCHAR},
            </if>
            <if test="categoryFullPathCode != null" >
                category_full_path_code = #{categoryFullPathCode,jdbcType=VARCHAR},
            </if>
            <if test="categoryPathName != null" >
                category_path_name = #{categoryPathName,jdbcType=VARCHAR},
            </if>
            <if test="productType != null">
                product_type = #{productType,jdbcType=INTEGER},
            </if>
            <if test="usPublishStatus != null">
                us_publish_status = #{usPublishStatus,jdbcType=INTEGER},
            </if>
            <if test="dePublishStatus != null">
                de_publish_status = #{dePublishStatus,jdbcType=INTEGER},
            </if>
            <if test="frPublishStatus != null">
                fr_publish_status = #{frPublishStatus,jdbcType=INTEGER},
            </if>
            <if test="ukPublishStatus != null">
                uk_publish_status = #{ukPublishStatus,jdbcType=INTEGER},
            </if>
            <if test="itPublishStatus != null">
                it_publish_status = #{itPublishStatus,jdbcType=INTEGER},
            </if>
            <if test="esPublishStatus != null">
                es_publish_status = #{esPublishStatus,jdbcType=INTEGER},
            </if>
            <if test="jpPublishStatus != null">
                jp_publish_status = #{jpPublishStatus,jdbcType=INTEGER},
            </if>
            <if test="caPublishStatus != null">
                ca_publish_status = #{caPublishStatus,jdbcType=INTEGER},
            </if>
            <if test="usIsBand != null">
                us_is_band = #{usIsBand,jdbcType=BIT},
            </if>
            <if test="deIsBand != null">
                de_is_band = #{deIsBand,jdbcType=BIT},
            </if>
            <if test="frIsBand != null">
                fr_is_band = #{frIsBand,jdbcType=BIT},
            </if>
            <if test="ukIsBand != null">
                uk_is_band = #{ukIsBand,jdbcType=BIT},
            </if>
            <if test="itIsBand != null">
                it_is_band = #{itIsBand,jdbcType=BIT},
            </if>
            <if test="esIsBand != null">
                es_is_band = #{esIsBand,jdbcType=BIT},
            </if>
            <if test="jpIsBand != null">
                jp_is_band = #{jpIsBand,jdbcType=BIT},
            </if>
            <if test="caIsBand != null">
                ca_is_band = #{caIsBand,jdbcType=BIT},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="lastUpdatedBy != null">
                last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus,jdbcType=INTEGER},
            </if>
            <if test="textAuditStatus != null">
                text_audit_status = #{textAuditStatus,jdbcType=INTEGER},
            </if>
            <if test="auditBy != null">
                audit_by = #{auditBy,jdbcType=VARCHAR},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="spuCreatedTime != null">
                spu_created_time = #{spuCreatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="batchUpdateSale">
        update amazon_must_publish_new_product set sale_id = #{saleId}, last_updated_by = #{userName}, updated_time = now() where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
    <update id="batchUpdateRemarks">
        update amazon_must_publish_new_product set remarks = #{remarks}, last_updated_by = #{userName}, updated_time = now() where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="batchUpdateByPrimaryKeySelective" parameterType="java.util.List">
        <foreach collection="records" item="item" separator=";">
            update amazon_must_publish_new_product
            <set>
                <if test="item.spu != null">
                    spu = #{item.spu,jdbcType=VARCHAR},
                </if>
                <if test="item.supervisorId != null" >
                    supervisor_id = #{item.supervisorId,jdbcType=VARCHAR},
                </if>
                <if test="item.saleId != null">
                    sale_id = #{item.saleId,jdbcType=VARCHAR},
                </if>
                <if test="item.publishedSite != null">
                    published_site = #{item.publishedSite,jdbcType=VARCHAR},
                </if>
                <if test="item.isBan != null">
                    is_ban = #{item.isBan,jdbcType=BIT},
                </if>
                <if test="item.status != null">
                    `status` = #{item.status,jdbcType=INTEGER},
                </if>
                <if test="item.siteInfo != null">
                    site_info = #{item.siteInfo,jdbcType=VARCHAR},
                </if>
                <if test="item.categoryFullPathCode != null" >
                    category_full_path_code = #{item.categoryFullPathCode,jdbcType=VARCHAR},
                </if>
                <if test="item.categoryPathName != null" >
                    category_path_name = #{item.categoryPathName,jdbcType=VARCHAR},
                </if>
                <if test="item.productType != null">
                    product_type = #{item.productType,jdbcType=INTEGER},
                </if>
                <if test="item.usPublishStatus != null">
                    us_publish_status = #{item.usPublishStatus,jdbcType=INTEGER},
                </if>
                <if test="item.dePublishStatus != null">
                    de_publish_status = #{item.dePublishStatus,jdbcType=INTEGER},
                </if>
                <if test="item.frPublishStatus != null">
                    fr_publish_status = #{item.frPublishStatus,jdbcType=INTEGER},
                </if>
                <if test="item.ukPublishStatus != null">
                    uk_publish_status = #{item.ukPublishStatus,jdbcType=INTEGER},
                </if>
                <if test="item.itPublishStatus != null">
                    it_publish_status = #{item.itPublishStatus,jdbcType=INTEGER},
                </if>
                <if test="item.esPublishStatus != null">
                    es_publish_status = #{item.esPublishStatus,jdbcType=INTEGER},
                </if>
                <if test="item.jpPublishStatus != null">
                    jp_publish_status = #{item.jpPublishStatus,jdbcType=INTEGER},
                </if>
                <if test="item.caPublishStatus != null">
                    ca_publish_status = #{item.caPublishStatus,jdbcType=INTEGER},
                </if>
                <if test="item.usIsBand != null">
                    us_is_band = #{item.usIsBand,jdbcType=BIT},
                </if>
                <if test="item.deIsBand != null">
                    de_is_band = #{item.deIsBand,jdbcType=BIT},
                </if>
                <if test="item.frIsBand != null">
                    fr_is_band = #{item.frIsBand,jdbcType=BIT},
                </if>
                <if test="item.ukIsBand != null">
                    uk_is_band = #{item.ukIsBand,jdbcType=BIT},
                </if>
                <if test="item.itIsBand != null">
                    it_is_band = #{item.itIsBand,jdbcType=BIT},
                </if>
                <if test="item.esIsBand != null">
                    es_is_band = #{item.esIsBand,jdbcType=BIT},
                </if>
                <if test="item.jpIsBand != null">
                    jp_is_band = #{item.jpIsBand,jdbcType=BIT},
                </if>
                <if test="item.caIsBand != null">
                    ca_is_band = #{item.caIsBand,jdbcType=BIT},
                </if>
                <if test="item.remarks != null">
                    remarks = #{item.remarks,jdbcType=VARCHAR},
                </if>
                <if test="item.lastUpdatedBy != null">
                    last_updated_by = #{item.lastUpdatedBy,jdbcType=VARCHAR},
                </if>
                <if test="item.auditStatus != null">
                    audit_status = #{item.auditStatus,jdbcType=INTEGER},
                </if>
                <if test="item.textAuditStatus != null">
                    text_audit_status = #{item.textAuditStatus,jdbcType=INTEGER},
                </if>
                <if test="item.auditBy != null">
                    audit_by = #{item.auditBy,jdbcType=VARCHAR},
                </if>
                <if test="item.auditTime != null">
                    audit_time = #{item.auditTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.spuCreatedTime != null">
                    spu_created_time = #{item.spuCreatedTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.createdTime != null">
                    created_time = #{item.createdTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updatedTime != null">
                    updated_time = #{item.updatedTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="batchAuditPass">
        update amazon_must_publish_new_product set audit_status = 2, text_audit_status = 2,audit_by = #{userName},
        audit_time = now() where id
        in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="batchAuditImagePass">
        update amazon_must_publish_new_product set audit_status = 2, audit_by = #{userName}, audit_time = now() where id
        in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="batchAuditTextPass">
        update amazon_must_publish_new_product set text_audit_status = 2, audit_by = #{userName}, audit_time = now()
        where id
        in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
</mapper>