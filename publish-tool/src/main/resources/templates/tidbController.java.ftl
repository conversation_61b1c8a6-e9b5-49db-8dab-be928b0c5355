package ${package.Controller};


import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import ${package.Entity}.${entity};
import ${package.Service}.${table.serviceName};
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RestController;
<#if superControllerClassPackage??>
import ${superControllerClassPackage};
</#if>
import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
* <p>
* ${table.comment!} 前端控制器
* </p>
*
* <AUTHOR>
* @since ${date}
*/
@Slf4j
@RestController
@RequestMapping("<#if package.ModuleName?? && package.ModuleName != "">/${package.ModuleName}</#if>/<#if controllerMappingHyphenStyle??>${controllerMappingHyphen}<#else>${table.entityPath}</#if>")
<#if kotlin>
class ${table.controllerName}<#if superControllerClass??> : ${superControllerClass}()</#if>
<#else>
<#if superControllerClass??>
public class ${table.controllerName} extends ${superControllerClass} {
<#else>
public class ${table.controllerName} {
</#if>
    @Resource
    private ${table.serviceName} ${table.serviceName?uncap_first};

    /**
     * 分页查询
     */
    @PostMapping("/queryPage")
    public CQueryResult<${entity}> queryPage(@RequestBody CQuery<${entity}> query) {
        try {
            return ${table.serviceName?uncap_first}.queryPage(query);
        } catch (Exception e) {
            log.error("queryPage fail", e);
            return CQueryResult.failResult(e.getMessage());
        }
    }


    /**
     * 根据id查询详情
     */
    @GetMapping("/getDetail/{id}")
    public ApiResult<${entity}> getDetailById(@PathVariable Serializable id) {
        ${entity} detail = ${table.serviceName?uncap_first}.getById(id);
        return ApiResult.newSuccess(detail);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    public ApiResult<String> update(@RequestBody ${entity} entity) {
        boolean success = ${table.serviceName?uncap_first}.updateById(entity);
        return ApiResult.newSuccess(success ? "更新成功" : "更新失败");
    }

    /**
     * 新增
     */
    @PostMapping("/save")
    public ApiResult<String> save(@RequestBody ${entity} entity) {
        boolean success = ${table.serviceName?uncap_first}.save(entity);
        return ApiResult.newSuccess(success ? "新增成功" : "新增失败");
    }

    /**
     * 根据id列表删除
     */
    @PostMapping("/delete")
    public ApiResult<String> deleteByIds(@RequestBody List<Serializable> ids) {
        boolean success = ${table.serviceName?uncap_first}.removeByIds(ids);
        return ApiResult.newSuccess(success ? "删除成功" : "删除失败");
    }
}
</#if>
