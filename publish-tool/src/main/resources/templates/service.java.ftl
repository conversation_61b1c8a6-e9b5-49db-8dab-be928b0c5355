package ${package.Service};

import ${package.Entity}.${entity};
import ${superServiceClassPackage};
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;

/**
 * <p>
 * ${table.comment!} 服务类
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
<#if kotlin>
interface ${table.serviceName} : ${superServiceClass}<${entity}>
<#else>
public interface ${table.serviceName} extends ${superServiceClass}<${entity}> {
    /**
     * 分页查询
     * @param query
     * @return
     */
    CQueryResult<${entity}> queryPage(CQuery<${entity}> query);
}
</#if>
