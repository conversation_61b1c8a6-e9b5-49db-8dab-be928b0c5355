<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.fruugo.mapper.FruugoAccountConfigMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.fruugo.model.FruugoAccountConfig" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="update_stock_rule_code" property="updateStockRuleCode" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="last_update_by" property="lastUpdateBy" jdbcType="VARCHAR" />
    <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
    <result column="sync_time" property="syncTime" jdbcType="TIMESTAMP" />
    <result column="account_site" property="accountSite" jdbcType="VARCHAR" />
    <result column="ean_prefix" property="eanPrefix" jdbcType="VARCHAR" />
    <result column="manufacturer" property="manufacturer" jdbcType="VARCHAR" />
    <result column="brand" property="brand" jdbcType="VARCHAR" />
    <result column="default_stock" property="defaultStock" jdbcType="INTEGER" />
    <result column="profit_margin" property="profitMargin" jdbcType="DOUBLE" />
    <result column="platform_rate" property="platformRate" jdbcType="DOUBLE" />
    <result column="currency" property="currency" jdbcType="VARCHAR" />
    <result column="ready_time" property="readyTime" jdbcType="INTEGER" />
    <result column="prod_category_codes" property="prodCategoryCodes" jdbcType="VARCHAR" />
    <result column="prod_category_names" property="prodCategoryNames" jdbcType="VARCHAR" />
    <result column="exclude_label" property="excludeLabel" jdbcType="VARCHAR" />
    <result column="from_weight" property="fromWeight" jdbcType="DOUBLE" />
    <result column="to_weight" property="toWeight" jdbcType="DOUBLE" />
    <result column="sales_type" property="salesType" jdbcType="INTEGER" />
    <result column="from_sales" property="fromSales" jdbcType="INTEGER" />
    <result column="to_sales" property="toSales" jdbcType="INTEGER" />
    <result column="from_input_time" property="fromInputTime" jdbcType="INTEGER" />
    <result column="to_input_time" property="toInputTime" jdbcType="INTEGER" />
    <result column="sku_create_time_type" property="skuCreateTimeType" jdbcType="INTEGER" />
    <result column="sku_create_time_year" property="skuCreateTimeYear" jdbcType="INTEGER" />
    <result column="from_price" property="fromPrice" jdbcType="DOUBLE" />
    <result column="to_price" property="toPrice" jdbcType="DOUBLE" />
    <result column="from_inventory" property="fromInventory" jdbcType="INTEGER" />
    <result column="to_inventory" property="toInventory" jdbcType="INTEGER" />
    <result column="publish_quantity" property="publishQuantity" jdbcType="INTEGER" />
    <result column="min_publish_mount" property="minPublishMount" jdbcType="INTEGER" />
    <result column="publish_time" property="publishTime" jdbcType="TIME" />
    <result column="publish_interval_time" property="publishIntervalTime" jdbcType="INTEGER" />
    <result column="auto_publish_new" property="autoPublishNew" jdbcType="BIT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, `status`, update_stock_rule_code, created_time, last_update_by,
    updated_time, sync_time, account_site, ean_prefix, manufacturer, brand, default_stock,
    profit_margin, platform_rate, currency, ready_time, prod_category_codes, prod_category_names,
    exclude_label, from_weight, to_weight, sales_type, from_sales, to_sales, from_input_time,
    to_input_time, sku_create_time_type, sku_create_time_year, from_price, to_price,
    from_inventory, to_inventory, publish_quantity, min_publish_mount, publish_time,
    publish_interval_time, auto_publish_new
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.fruugo.model.FruugoAccountConfigExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from fruugo_account_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from fruugo_account_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from fruugo_account_config
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.fruugo.model.FruugoAccountConfig" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fruugo_account_config (account_number, `status`, update_stock_rule_code,
      created_time, last_update_by, updated_time, sync_time, account_site, ean_prefix,
      manufacturer, brand, default_stock, profit_margin, platform_rate, currency, ready_time,
      prod_category_codes, prod_category_names, exclude_label, from_weight, to_weight,
      sales_type, from_sales, to_sales, from_input_time, to_input_time, sku_create_time_type,
      sku_create_time_year, from_price, to_price, from_inventory, to_inventory, publish_quantity,
      min_publish_mount, publish_time, publish_interval_time, auto_publish_new)
    values (#{accountNumber,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{updateStockRuleCode,jdbcType=VARCHAR},
      #{createdTime,jdbcType=TIMESTAMP}, #{lastUpdateBy,jdbcType=VARCHAR}, #{updatedTime,jdbcType=TIMESTAMP},
      #{syncTime,jdbcType=TIMESTAMP}, #{accountSite,jdbcType=VARCHAR}, #{eanPrefix,jdbcType=VARCHAR},
      #{manufacturer,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{defaultStock,jdbcType=INTEGER},
      #{profitMargin,jdbcType=DOUBLE}, #{platformRate,jdbcType=DOUBLE}, #{currency,jdbcType=VARCHAR},
      #{readyTime,jdbcType=INTEGER}, #{prodCategoryCodes,jdbcType=VARCHAR}, #{prodCategoryNames,jdbcType=VARCHAR},
      #{excludeLabel,jdbcType=VARCHAR}, #{fromWeight,jdbcType=DOUBLE}, #{toWeight,jdbcType=DOUBLE},
      #{salesType,jdbcType=INTEGER}, #{fromSales,jdbcType=INTEGER}, #{toSales,jdbcType=INTEGER},
      #{fromInputTime,jdbcType=INTEGER}, #{toInputTime,jdbcType=INTEGER}, #{skuCreateTimeType,jdbcType=INTEGER},
      #{skuCreateTimeYear,jdbcType=INTEGER}, #{fromPrice,jdbcType=DOUBLE}, #{toPrice,jdbcType=DOUBLE},
      #{fromInventory,jdbcType=INTEGER}, #{toInventory,jdbcType=INTEGER}, #{publishQuantity,jdbcType=INTEGER},
      #{minPublishMount,jdbcType=INTEGER}, #{publishTime,jdbcType=TIME}, #{publishIntervalTime,jdbcType=INTEGER},
      #{autoPublishNew,jdbcType=BIT})
  </insert><select id="countByExample" parameterType="com.estone.erp.publish.fruugo.model.FruugoAccountConfigExample" resultType="java.lang.Integer" >
    select count(*) from fruugo_account_config
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
    <select id="selectByAccountNumber" resultMap="BaseResultMap">
      select  <include refid="Base_Column_List" />  from fruugo_account_config
      where account_number = #{accountNumber}
    </select>
    <update id="updateByExampleSelective" parameterType="map" >
    update fruugo_account_config
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.updateStockRuleCode != null" >
        update_stock_rule_code = #{record.updateStockRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createdTime != null" >
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdateBy != null" >
        last_update_by = #{record.lastUpdateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedTime != null" >
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.syncTime != null" >
        sync_time = #{record.syncTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accountSite != null" >
        account_site = #{record.accountSite,jdbcType=VARCHAR},
      </if>
      <if test="record.eanPrefix != null" >
        ean_prefix = #{record.eanPrefix,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null" >
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null" >
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.defaultStock != null" >
        default_stock = #{record.defaultStock,jdbcType=INTEGER},
      </if>
      <if test="record.profitMargin != null" >
        profit_margin = #{record.profitMargin,jdbcType=DOUBLE},
      </if>
      <if test="record.platformRate != null" >
        platform_rate = #{record.platformRate,jdbcType=DOUBLE},
      </if>
      <if test="record.currency != null" >
        currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.readyTime != null" >
        ready_time = #{record.readyTime,jdbcType=INTEGER},
      </if>
      <if test="record.prodCategoryCodes != null" >
        prod_category_codes = #{record.prodCategoryCodes,jdbcType=VARCHAR},
      </if>
      <if test="record.prodCategoryNames != null" >
        prod_category_names = #{record.prodCategoryNames,jdbcType=VARCHAR},
      </if>
      <if test="record.excludeLabel != null" >
        exclude_label = #{record.excludeLabel,jdbcType=VARCHAR},
      </if>
      <if test="record.fromWeight != null" >
        from_weight = #{record.fromWeight,jdbcType=DOUBLE},
      </if>
      <if test="record.toWeight != null" >
        to_weight = #{record.toWeight,jdbcType=DOUBLE},
      </if>
      <if test="record.salesType != null" >
        sales_type = #{record.salesType,jdbcType=INTEGER},
      </if>
      <if test="record.fromSales != null" >
        from_sales = #{record.fromSales,jdbcType=INTEGER},
      </if>
      <if test="record.toSales != null" >
        to_sales = #{record.toSales,jdbcType=INTEGER},
      </if>
      <if test="record.fromInputTime != null" >
        from_input_time = #{record.fromInputTime,jdbcType=INTEGER},
      </if>
      <if test="record.toInputTime != null" >
        to_input_time = #{record.toInputTime,jdbcType=INTEGER},
      </if>
      <if test="record.skuCreateTimeType != null" >
        sku_create_time_type = #{record.skuCreateTimeType,jdbcType=INTEGER},
      </if>
      <if test="record.skuCreateTimeYear != null" >
        sku_create_time_year = #{record.skuCreateTimeYear,jdbcType=INTEGER},
      </if>
      <if test="record.fromPrice != null" >
        from_price = #{record.fromPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.toPrice != null" >
        to_price = #{record.toPrice,jdbcType=DOUBLE},
      </if>
      <if test="record.fromInventory != null" >
        from_inventory = #{record.fromInventory,jdbcType=INTEGER},
      </if>
      <if test="record.toInventory != null" >
        to_inventory = #{record.toInventory,jdbcType=INTEGER},
      </if>
      <if test="record.publishQuantity != null" >
        publish_quantity = #{record.publishQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.minPublishMount != null" >
        min_publish_mount = #{record.minPublishMount,jdbcType=INTEGER},
      </if>
      <if test="record.publishTime != null" >
        publish_time = #{record.publishTime,jdbcType=TIME},
      </if>
      <if test="record.publishIntervalTime != null" >
        publish_interval_time = #{record.publishIntervalTime,jdbcType=INTEGER},
      </if>
      <if test="record.autoPublishNew != null" >
        auto_publish_new = #{record.autoPublishNew,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.fruugo.model.FruugoAccountConfig" >
    update fruugo_account_config
    <set >
      <if test="accountNumber != null">
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="updateStockRuleCode != null">
        update_stock_rule_code = #{updateStockRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateBy != null">
        last_update_by = #{lastUpdateBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="syncTime != null">
        sync_time = #{syncTime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountSite != null">
        account_site = #{accountSite,jdbcType=VARCHAR},
      </if>
      <if test="eanPrefix != null">
        ean_prefix = #{eanPrefix,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="defaultStock != null">
        default_stock = #{defaultStock,jdbcType=INTEGER},
      </if>
      <if test="profitMargin != null">
        profit_margin = #{profitMargin,jdbcType=DOUBLE},
      </if>
      <if test="platformRate != null">
        platform_rate = #{platformRate,jdbcType=DOUBLE},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="readyTime != null">
        ready_time = #{readyTime,jdbcType=INTEGER},
      </if>
      <if test="prodCategoryCodes != null">
        prod_category_codes = #{prodCategoryCodes,jdbcType=VARCHAR},
      </if>
      <if test="prodCategoryNames != null">
        prod_category_names = #{prodCategoryNames,jdbcType=VARCHAR},
      </if>
      <if test="excludeLabel != null">
        exclude_label = #{excludeLabel,jdbcType=VARCHAR},
      </if>
      <if test="fromWeight != null">
        from_weight = #{fromWeight,jdbcType=DOUBLE},
      </if>
      <if test="toWeight != null">
        to_weight = #{toWeight,jdbcType=DOUBLE},
      </if>
      <if test="salesType != null">
        sales_type = #{salesType,jdbcType=INTEGER},
      </if>
      <if test="fromSales != null">
        from_sales = #{fromSales,jdbcType=INTEGER},
      </if>
      <if test="toSales != null">
        to_sales = #{toSales,jdbcType=INTEGER},
      </if>
      <if test="fromInputTime != null">
        from_input_time = #{fromInputTime,jdbcType=INTEGER},
      </if>
      <if test="toInputTime != null">
        to_input_time = #{toInputTime,jdbcType=INTEGER},
      </if>
      <if test="skuCreateTimeType != null">
        sku_create_time_type = #{skuCreateTimeType,jdbcType=INTEGER},
      </if>
      <if test="skuCreateTimeYear != null">
        sku_create_time_year = #{skuCreateTimeYear,jdbcType=INTEGER},
      </if>
      <if test="fromPrice != null">
        from_price = #{fromPrice,jdbcType=DOUBLE},
      </if>
      <if test="toPrice != null">
        to_price = #{toPrice,jdbcType=DOUBLE},
      </if>
      <if test="fromInventory != null">
        from_inventory = #{fromInventory,jdbcType=INTEGER},
      </if>
      <if test="toInventory != null">
        to_inventory = #{toInventory,jdbcType=INTEGER},
      </if>
      <if test="publishQuantity != null">
        publish_quantity = #{publishQuantity,jdbcType=INTEGER},
      </if>
      <if test="minPublishMount != null">
        min_publish_mount = #{minPublishMount,jdbcType=INTEGER},
      </if>
      <if test="publishTime != null">
        publish_time = #{publishTime,jdbcType=TIME},
      </if>
      <if test="publishIntervalTime != null">
        publish_interval_time = #{publishIntervalTime,jdbcType=INTEGER},
      </if>
      <if test="autoPublishNew != null">
        auto_publish_new = #{autoPublishNew,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!--更新账号信息-->
  <update id="updateAccountInfoByExample" parameterType="com.estone.erp.publish.fruugo.model.FruugoAccountConfig">
    update fruugo_account_config
    <set>
      account_number = #{accountNumber,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      update_stock_rule_code = #{updateStockRuleCode,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      last_update_by = #{lastUpdateBy,jdbcType=VARCHAR},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      sync_time = #{syncTime,jdbcType=TIMESTAMP},
      account_site = #{accountSite,jdbcType=VARCHAR},
      ean_prefix = #{eanPrefix,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      default_stock = #{defaultStock,jdbcType=INTEGER},
      profit_margin = #{profitMargin,jdbcType=DOUBLE},
      platform_rate = #{platformRate,jdbcType=DOUBLE},
      currency = #{currency,jdbcType=VARCHAR},
      ready_time = #{readyTime,jdbcType=INTEGER},
      prod_category_codes = #{prodCategoryCodes,jdbcType=VARCHAR},
      prod_category_names = #{prodCategoryNames,jdbcType=VARCHAR},
      exclude_label = #{excludeLabel,jdbcType=VARCHAR},
      from_weight = #{fromWeight,jdbcType=DOUBLE},
      to_weight = #{toWeight,jdbcType=DOUBLE},
      sales_type = #{salesType,jdbcType=INTEGER},
      from_sales = #{fromSales,jdbcType=INTEGER},
      to_sales = #{toSales,jdbcType=INTEGER},
      from_input_time = #{fromInputTime,jdbcType=INTEGER},
      to_input_time = #{toInputTime,jdbcType=INTEGER},
      sku_create_time_type = #{skuCreateTimeType,jdbcType=INTEGER},
      sku_create_time_year = #{skuCreateTimeYear,jdbcType=INTEGER},
      from_price = #{fromPrice,jdbcType=DOUBLE},
      to_price = #{toPrice,jdbcType=DOUBLE},
      from_inventory = #{fromInventory,jdbcType=INTEGER},
      to_inventory = #{toInventory,jdbcType=INTEGER},
      publish_quantity = #{publishQuantity,jdbcType=INTEGER},
      min_publish_mount = #{minPublishMount,jdbcType=INTEGER},
      publish_time = #{publishTime,jdbcType=TIME},
      publish_interval_time = #{publishIntervalTime,jdbcType=INTEGER},
      auto_publish_new = #{autoPublishNew,jdbcType=BIT},
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>