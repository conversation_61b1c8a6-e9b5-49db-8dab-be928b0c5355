package com.estone.erp.publish.fruugo.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiR<PERSON>ult;
import com.estone.erp.common.redis.config.RedisClusterTemplate;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.executors.FruugoExecutors;
import com.estone.erp.publish.elasticsearch.model.EsFruugoItem;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsFruugoItemRequest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.service.EsFruugoItemService;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.fruugo.call.FruugoCallService;
import com.estone.erp.publish.fruugo.call.FruugoCallV1Service;
import com.estone.erp.publish.fruugo.call.model.request.CreateProductRequest;
import com.estone.erp.publish.fruugo.call.model.request.FruugoProductResponse;
import com.estone.erp.publish.fruugo.constant.FruugoRedisConstant;
import com.estone.erp.publish.fruugo.enums.FruugoAttributeFlagEnum;
import com.estone.erp.publish.fruugo.enums.FruugoFeedTaskEnum;
import com.estone.erp.publish.fruugo.enums.FruugoStockStatuEnums;
import com.estone.erp.publish.fruugo.handler.FruugoUpdateStockHandler;
import com.estone.erp.publish.fruugo.model.FruugoAccountConfig;
import com.estone.erp.publish.fruugo.model.FruugoAccountConfigExample;
import com.estone.erp.publish.fruugo.model.FruugoItemText;
import com.estone.erp.publish.fruugo.model.FruugoItemTextExample;
import com.estone.erp.publish.fruugo.model.dto.*;
import com.estone.erp.publish.fruugo.service.*;
import com.estone.erp.publish.fruugo.util.FruugoItemUtils;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FruugoItemServiceImpl implements FruugoItemService {

    private final static String SUCCESS_RES = "<ok/>";
    private final Semaphore SYNC_SP = new Semaphore(50);
    @Autowired
    EsFruugoItemService esFruugoItemService;

    @Autowired
    FruugoFeedTaskService fruugoFeedTaskService;

    @Resource
    private SaleAccountService saleAccountService;

    @Resource
    private RedisClusterTemplate redisClusterTemplate;

    @Resource
    private EsSkuBindService skuBindService;

    @Autowired
    private FruugoCallService fruugoCallService;

    @Autowired
    private FruugoUpdateStockHandler updateStockHandler;

    @Resource
    private FruugoAccountConfigService fruugoAccountConfigService;

    @Resource
    private FruugoTemplateService fruugoTemplateService;

    @Resource
    private FruugoCallV1Service fruugoCallV1Service;

    @Resource
    private FruugoSyncItemService fruugoSyncItemService;

    @Resource
    private FruugoCategoryUpdateFlagService fruugoCategoryUpdateFlagService;
    @Resource
    private FruugoItemTextService fruugoItemTextService;
    @Autowired
    private FruugoItemService fruugoItemService;

    @Override
    public void batchUpdateAttribute(FruugoBatchUpdateAttributeDto dto) {
        if (ObjectUtils.isEmpty(dto) || CollectionUtils.isEmpty(dto.getIdList())) {
            throw new BusinessException("请选择数据！");
        }

        List<String> idList = dto.getIdList();
        List<EsFruugoItem> infoByIds = esFruugoItemService.findInfoByIds(idList);

        // 获取所有新店铺账号
        List<String> newAccountNumList = this.getNewAccountNumList(infoByIds);
        Map<String, List<EsFruugoItem>> itemListMap = infoByIds.stream().collect(Collectors.groupingBy(EsFruugoItem::getAccountNumber));
        for (String accountNumber : itemListMap.keySet()) {
            if (!newAccountNumList.contains(accountNumber)) {
                log.info("非新店铺账号：{}", accountNumber);
                continue;
            }
            SaleAccount saleAccount = getAccount(accountNumber);
            try {
                String userName = WebUtils.getUserName();
                SYNC_SP.acquire();
                CompletableFuture.runAsync(() -> {
                    try {
                        DataContextHolder.setUsername(userName);
                        //先同步描述再修改
                        List<EsFruugoItem> esFruugoItems = itemListMap.get(accountNumber);
                        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_FRUUGO, accountNumber);
                        //按5分批
                        List<List<EsFruugoItem>>  esFruugoItemsList = ListUtils.partition(esFruugoItems, 5);
                        esFruugoItemsList.forEach(ItemList -> {
                            this.sysncItemText(saleAccountByAccountNumber, ItemList);
                        });
                        this.updateAttributes(saleAccount, esFruugoItems, dto);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        SYNC_SP.release();
                    }
                }, FruugoExecutors.UPDATE_ATTRIBUTE_POOL);
            } catch (InterruptedException e) {
                log.error("{}:修改属性异常中断", accountNumber, e);
            }
        }
    }


    @Override
    public void batchUpdateManufactureAndBrand(FruugoBatchUpdateManufactureAndBrandDto dto) {
        if (ObjectUtils.isEmpty(dto) || CollectionUtils.isEmpty(dto.getIdList()) && (
                StringUtils.isBlank(dto.getBrand()) || StringUtils.isBlank(dto.getManufacturer()))) {
            throw new BusinessException("请填写品牌或制造商！");
        }

        List<String> idList = dto.getIdList();
        List<EsFruugoItem> infoByIds = esFruugoItemService.findInfoByIds(idList);

        // 获取所有新店铺账号
        List<String> newAccountNumList = this.getNewAccountNumList(infoByIds);

        Map<String, List<EsFruugoItem>> itemListMap = infoByIds.stream().collect(Collectors.groupingBy(EsFruugoItem::getAccountNumber));
        for (String accountNumber : itemListMap.keySet()) {
            if (!newAccountNumList.contains(accountNumber)) {
                continue;
            }
            SaleAccount saleAccount = getAccount(accountNumber);
            try {
                String userName = WebUtils.getUserName();

                SYNC_SP.acquire();
                CompletableFuture.runAsync(() -> {
                    try {
                        DataContextHolder.setUsername(userName);
                        this.updateManufacturerAndBrand(saleAccount, dto.getManufacturer(), dto.getBrand(), itemListMap.get(accountNumber));
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    } finally {
                        SYNC_SP.release();
                    }
                }, FruugoExecutors.UPDATE_MANUFACTURE_POOL);
            } catch (InterruptedException e) {
                log.error("{}:修改制造商异常中断", accountNumber, e);
            }
        }

    }

    private List<String> getNewAccountNumList(List<EsFruugoItem> infoByIds) {
        List<String> accountList = infoByIds.stream()
                .map(EsFruugoItem::getAccountNumber)
                .distinct()
                .collect(Collectors.toList());
        List<SaleAccount> fruugoNewAccounts = getNewSaleAccounts(accountList);
        return fruugoNewAccounts.stream()
                .map(SaleAccount::getAccountNumber)
                .collect(Collectors.toList());
    }

    @Override
    public void sysncItemText(SaleAccountAndBusinessResponse saleAccountByAccountNumber, List<EsFruugoItem> esFruugoItemList) {
        if (CollectionUtils.isEmpty(esFruugoItemList)) {
            return;
        }
        String accountNumber = saleAccountByAccountNumber.getAccountNumber();
        Map<String, String> productEsFruugoItemIdMap = new HashMap<>();
        Map<String, List<String>> productIdAndskuIdsMap = new HashMap<>();
        List<String> exstItemIdS = new ArrayList<>();
        esFruugoItemList.forEach(esFruugoItem -> {
            List<String> skuIdList = null;
            String productId = esFruugoItem.getNewProductId();
            String skuId = esFruugoItem.getSellerSku();
            if (productIdAndskuIdsMap.containsKey(esFruugoItem.getNewProductId())) {
                skuIdList = productIdAndskuIdsMap.get(esFruugoItem.getNewProductId());
                skuIdList.add(esFruugoItem.getSellerSku());
            } else {
                skuIdList = new ArrayList<>();
                skuIdList.add(esFruugoItem.getSellerSku());
                productIdAndskuIdsMap.put(esFruugoItem.getNewProductId(), skuIdList);
            }
            productEsFruugoItemIdMap.put(productId + "_" + skuId, esFruugoItem.getId());
            exstItemIdS.add(esFruugoItem.getId());
        });
        esFruugoItemList.stream().collect(Collectors.groupingBy(EsFruugoItem::getNewProductId));
        ApiResult<String> apiResult = fruugoSyncItemService.sysncNewAccountBatchProductIds(saleAccountByAccountNumber, productIdAndskuIdsMap);
        if (BooleanUtils.isFalse(apiResult.isSuccess()) || StringUtils.isBlank(apiResult.getResult())) {
            log.error("{}:批次同步产品详细信息失败：{}", accountNumber, JSON.toJSONString(apiResult));
            throw new BusinessException("批次同步产品详细信息失败：" + JSON.toJSONString(apiResult));
        }
        FruugoProductResponse fruugoProductResponse = JSON.parseObject(apiResult.getResult(), FruugoProductResponse.class);
        Map<String, FruugoProductResponse.Product> products = fruugoProductResponse.getProducts();
        if (ObjectUtils.isEmpty(products)) {
            log.error("{}:返回体fruugoProductResponse.products为null", accountNumber);
            throw new BusinessException("返回体fruugoProductResponse.products为null");
        }
        List<FruugoItemText> inserFruugoItemTextList = new ArrayList<>();
        List<FruugoItemText> updateFruugoItemTextList = new ArrayList<>();
        Map<String, Long> itemIdAndIdMap = fruugoItemTextService.findExistByItemIdIn(exstItemIdS, accountNumber);
        Timestamp updateTime = new Timestamp(System.currentTimeMillis());
        products.forEach((key, value) -> {
            value.getSkus().forEach((skuKey, skuValue) -> {
                FruugoItemText fruugoItemText = new FruugoItemText();
                String esId = productEsFruugoItemIdMap.get(key + "_" + skuKey);
                fruugoItemText.setSku(skuKey);
                fruugoItemText.setProductId(key);
                fruugoItemText.setSkuText(JSON.toJSONString(skuValue.getSkuDetails().getDetails().getSkuDescriptions().get(0)));
                fruugoItemText.setAccount(accountNumber);
                fruugoItemText.setItemId(esId);
                fruugoItemText.setUpdateTime(updateTime);
                if (itemIdAndIdMap.containsKey(esId)) {
                    fruugoItemText.setId(itemIdAndIdMap.get(esId));
                    updateFruugoItemTextList.add(fruugoItemText);
                } else {
                    fruugoItemText.setCreateTime(updateTime);
                    inserFruugoItemTextList.add(fruugoItemText);
                }
            });
            if (CollectionUtils.isNotEmpty(inserFruugoItemTextList)) {
                fruugoItemTextService.batchInsert(inserFruugoItemTextList);
            }
            if (CollectionUtils.isNotEmpty(updateFruugoItemTextList)) {
                fruugoItemTextService.batchUpdateByPrimaryKeySelective(updateFruugoItemTextList);
            }
        });
    }


    @Override
    public void updateAttributesByCategory(SaleAccount account, List<EsFruugoItem> esFruugoItemList) {
        if (CollectionUtils.isEmpty(esFruugoItemList)) {
            return;
        }
        if (ObjectUtils.isEmpty(account) || ObjectUtils.isEmpty(account.getColBool2()) || !account.getColBool2()) {
            return;
        }
        List<String> categoryList = esFruugoItemList.stream().map(EsFruugoItem::getNewCategory)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, List<Integer>> categoryAndAttributes = new HashMap<>();
        //分批查询
        ListUtils.partition(categoryList, 500).forEach(batchList -> {
            //获取分类对应的属性
            Map<String, List<Integer>> map = fruugoCategoryUpdateFlagService.getCategoryAndAttributes(categoryList);
            if (MapUtils.isNotEmpty(map)) {
                categoryAndAttributes.putAll(map);
            }
        });

        //按100分批
        ListUtils.partition(esFruugoItemList, 100).forEach(batchList -> {
            //创建处理报告
            List<FeedTask> feedTasks = buildUpdateAttributeFeedTask(account.getAccountNumber(), batchList);
            FruugoUpdateInfoParams updateInfoParams = new FruugoUpdateInfoParams();
            updateInfoParams.setCategoryAndAttributes(categoryAndAttributes);
            updateInfoParams.setReadyTimeAttribute(2);
            updateInfoParams.setIsUpdateAttribute(true);
            newAccountUpdateAttribute(account, feedTasks, batchList, updateInfoParams);
        });


    }

    private void newAccountUpdateAttribute(SaleAccount account, List<FeedTask> feedTasks, List<EsFruugoItem> itemList, FruugoUpdateInfoParams params) {
        ApiResult<String> result = null;
        List<FeedTask> exitFeedTasks = new ArrayList<>();
        try {
            List<String> esItemIdList = itemList.stream().map(EsFruugoItem::getId).collect(Collectors.toList());
            FruugoItemTextExample example = new FruugoItemTextExample();
            example.setColumns("id,item_id,sku_text");
            example.createCriteria().andAccountEqualTo(account.getAccountNumber()).andItemIdIn(esItemIdList);
            List<FruugoItemText> fruugoItemTexts = fruugoItemTextService.selectColumnsByExample(example);
            if (CollectionUtils.isEmpty(fruugoItemTexts)) {
                fruugoFeedTaskService.batchFailTask(feedTasks, "未获取到产品的描述数据！");
                return;
            }

            Map<String, FruugoItemText> esIdAndFruugoItemText = fruugoItemTexts.stream().collect(Collectors.toMap(FruugoItemText::getItemId, Function.identity(), (k, v) -> v));

            //过滤出存在描述的itemList
            List<EsFruugoItem> esFruugoItemList = itemList.stream().filter(item ->
                    StringUtils.isNotBlank(item.getNewProductId()) && esIdAndFruugoItemText.containsKey(item.getId())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(esFruugoItemList)) {
                fruugoFeedTaskService.batchFailTask(feedTasks, "未获取到产品的描述数据！");
                return;
            }


            exitFeedTasks = feedTasks.stream().filter(item ->
                            StringUtils.isNotBlank(item.getAttribute5()) && esIdAndFruugoItemText.containsKey(item.getAttribute5()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(exitFeedTasks)) {
                fruugoFeedTaskService.batchFailTask(feedTasks, "未获取到产品的描述数据！");
                return;
            }

            List<FeedTask> notExitFeedTasks = feedTasks.stream().filter(item ->
                            StringUtils.isNotBlank(item.getAttribute5()) && !esIdAndFruugoItemText.containsKey(item.getAttribute5()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(notExitFeedTasks)) {
                fruugoFeedTaskService.batchFailTask(notExitFeedTasks, "未获取到产品的描述数据！");
            }


            Map<String, List<FruugoBatchUpdateInfoDto>> map = esFruugoItemList.stream().map(item -> {
                FruugoItemText fruugoItemText = esIdAndFruugoItemText.get(item.getId());
                String skuText = fruugoItemText.getSkuText();
                FruugoProductResponse.SkuDescriptionItem skuDescriptionItem = JSON.parseObject(skuText, FruugoProductResponse.SkuDescriptionItem.class);
                FruugoBatchUpdateInfoDto fruugoBatchUpdatePriceAndStockDto = new FruugoBatchUpdateInfoDto()
                        .setNewProductId(item.getNewProductId())
                        .setInventory(item.getInventory())
                        .setSellerSku(item.getSellerSku())
                        .setSku(item.getSku())
                        .setAfterPrice(item.getPrice())
                        .setCurrency(item.getCurrency())
                        .setTaxInclusive(item.getTaxInclusive())
                        .setAccount(account.getAccountNumber())
                        .setStatus(item.getStatus())
                        .setId(item.getId())
                        .setText(skuDescriptionItem.getText())
                        .setLanguage(skuDescriptionItem.getLanguage())
                        .setTitle(skuDescriptionItem.getTitle());
                if (ObjectUtils.isNotEmpty(params)) {
                    //获取修改字段
                    getUpdateField(params, item, fruugoBatchUpdatePriceAndStockDto);

                }
                return fruugoBatchUpdatePriceAndStockDto;
            }).collect(Collectors.groupingBy(FruugoBatchUpdateInfoDto::getNewProductId));

            if (MapUtils.isEmpty(map)) {
                fruugoFeedTaskService.batchFailTask(exitFeedTasks, "未找到在线列表相关数据");
            }

            CreateProductRequest createProductRequest = FruugoConvertDto.toCreateProductRequest(map);
//            log.info("店铺:" + account.getAccountNumber() + "请求体:" + JSON.toJSONString(createProductRequest));
            result = fruugoCallV1Service.updateProduct(createProductRequest, account);
        } catch (Exception e) {
            log.error("fruugo部分修改失败：", e);
            fruugoFeedTaskService.batchFailTask(exitFeedTasks, e.getMessage());
            throw new RuntimeException("fruugo部分修改失败：" + e.getMessage());
        }
        // 记录任务执行结果
        if (ObjectUtils.isNotEmpty(result) && result.isSuccess()) {
            String[] s = new String[5];
            s[3] = result.getResult();
            fruugoFeedTaskService.batchRunningTask(exitFeedTasks, result.getResult(), s);
        } else {
            String errorMsg = ObjectUtils.isNotEmpty(result) ? result.getErrorMsg() : "执行结果为空";
            fruugoFeedTaskService.batchFailTask(exitFeedTasks, errorMsg);
        }
    }

    private List<FeedTask> buildUpdateAttributeFeedTask(String accountNumber, List<EsFruugoItem> items) {
        List<FeedTask> feedTasks = new ArrayList<>();
        for (EsFruugoItem item : items) {
            //attribute5为在线列表ID
            FeedTask feedTask = fruugoFeedTaskService.newTask(item.getNewProductId(),
                    accountNumber,
                    FruugoFeedTaskEnum.UPDATE_ATTRIBUTE.getCode(),
                    item.getSku(),
                    null,
                    null,
                    null,
                    null,
                    item.getId());
            feedTasks.add(feedTask);
        }
        return feedTasks;
    }

    @Override
    public void updateManufacturerAndBrand(SaleAccount account, String manufacturer, String brand, List<EsFruugoItem> fruugoItemList) {
        if (CollectionUtils.isEmpty(fruugoItemList)) {
            return;
        }
        if (ObjectUtils.isEmpty(account) || ObjectUtils.isEmpty(account.getColBool2()) || !account.getColBool2()) {
            return;
        }
        //按100分批
        ListUtils.partition(fruugoItemList, 100).forEach(batchList -> {
            //创建处理报告
            List<FeedTask> feedTasks = buildUpdateManufacturerAndBrandFeedTask(account.getAccountNumber(), batchList);
            FruugoUpdateInfoParams updateInfoParams = new FruugoUpdateInfoParams();
            updateInfoParams.setManufacturer(manufacturer);
            updateInfoParams.setBrand(brand);
            newAccountUpdateInfo(account, feedTasks, batchList, updateInfoParams);
        });
    }


    private List<FeedTask> buildUpdateManufacturerAndBrandFeedTask(String accountNumber, List<EsFruugoItem> items) {
        List<FeedTask> feedTasks = new ArrayList<>();
        for (EsFruugoItem item : items) {

            //attribute5为在线列表ID
            FeedTask feedTask = fruugoFeedTaskService.newTask(item.getNewProductId(),
                    accountNumber,
                    FruugoFeedTaskEnum.UPDATE_MANUFACTURER_AND_BRAND.getCode(),
                    item.getSku(),
                    null,
                    null,
                    null,
                    null,
                    item.getId());
            feedTasks.add(feedTask);
        }
        return feedTasks;
    }

    @Override
    public void syncItemByAccount(List<String> accountList) {
        if (CollectionUtils.isEmpty(accountList)) {
            throw new BusinessException("参数为空");
        }
        accountList.forEach(account -> fruugoSyncItemService.sysncItemByNewAccountAndDate(account, null));
    }

    @Override
    public void sysncItemByNewAccountAndSpu(FruugoSyncAccountAndSpuDto dto) {
        if (ObjectUtils.isEmpty(dto)) {
            throw new BusinessException("参数为空");
        }

        List<FruugoSyncAccountAndSpuDto.FruugoSyncAccountAndSpu> fruugoSyncAccountAndLists = new ArrayList<>();
        //优先勾选
        if (CollectionUtils.isNotEmpty(dto.getAccountAndSpuList())) {
            fruugoSyncAccountAndLists = dto.getAccountAndSpuList();
        } else {
            fruugoSyncAccountAndLists = assembleAccountAndProductIds(dto.getAccountList(), dto.getProductIdList());
        }

        if (CollectionUtils.isEmpty(fruugoSyncAccountAndLists)) {
            throw new BusinessException("参数为空");
        }

        fruugoSyncAccountAndLists = fruugoSyncAccountAndLists.stream().distinct().collect(Collectors.toList());


        List<String> accountList = fruugoSyncAccountAndLists.stream()
                .map(FruugoSyncAccountAndSpuDto.FruugoSyncAccountAndSpu::getAccount)
                .distinct()
                .collect(Collectors.toList());

        List<SaleAccount> fruugoNewAccounts = getNewSaleAccounts(accountList);
        List<String> newAccountNumList = fruugoNewAccounts.stream()
                .map(SaleAccount::getAccountNumber)
                .collect(Collectors.toList());

        List<FruugoSyncAccountAndSpuDto.FruugoSyncAccountAndSpu> syncAccountAndSpuDtoList = fruugoSyncAccountAndLists.stream().filter(item ->
                newAccountNumList.contains(item.getAccount()) && StringUtils.isNotBlank(item.getProductId())
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(syncAccountAndSpuDtoList)) {
            throw new BusinessException("只支持新店铺数据");
        }

        String userName = WebUtils.getUserName();
        for (FruugoSyncAccountAndSpuDto.FruugoSyncAccountAndSpu fruugoSyncAccountAndSpuDto : syncAccountAndSpuDtoList) {
            CompletableFuture.runAsync(() -> {
                DataContextHolder.setUsername(userName);
                fruugoSyncItemService.sysncItemByNewAccountAndSpu(fruugoSyncAccountAndSpuDto.getAccount(), fruugoSyncAccountAndSpuDto.getProductId());
            }, FruugoExecutors.SYNC_ITEM_INFO_POOL);
        }

    }

    public static List<FruugoSyncAccountAndSpuDto.FruugoSyncAccountAndSpu> assembleAccountAndProductIds(List<String> accountList, List<String> productIdList) {
        List<FruugoSyncAccountAndSpuDto.FruugoSyncAccountAndSpu> resultList = new ArrayList<>();

        if (accountList == null || productIdList == null) {
            throw new IllegalArgumentException("店铺 和 productId不能为空");
        }

        // 对 productIdList 去重
        Set<String> uniqueProductIds = new HashSet<>(productIdList);
        Iterator<String> productIdIterator = uniqueProductIds.iterator();

        for (String account : accountList) {
            while (productIdIterator.hasNext()) {
                FruugoSyncAccountAndSpuDto.FruugoSyncAccountAndSpu item = new FruugoSyncAccountAndSpuDto.FruugoSyncAccountAndSpu();
                item.setAccount(account);
                item.setProductId(productIdIterator.next());
                resultList.add(item);
            }
        }

        return resultList;
    }

    @Override
    public void batchUpdatePrice(List<FruugoBatchUpdateInfoDto> dto) {
        if (ObjectUtils.isEmpty(dto)) {
            throw new BusinessException("参数为空");
        }

        // 仅保留改后价大于0的数据
        dto = dto.stream().filter(item -> Optional.ofNullable(item.getAfterPrice()).orElse(0.0) > 0).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(dto)) {
            throw new BusinessException("过滤价格为0的数据之后，无可用数据！");
        }

        // 获取所有新店铺账号
        List<String> accountList = dto.stream()
                .map(FruugoBatchUpdateInfoDto::getAccount)
                .distinct()
                .collect(Collectors.toList());
        List<SaleAccount> fruugoNewAccounts = getNewSaleAccounts(accountList);
        List<String> newAccountNumList = fruugoNewAccounts.stream()
                .map(SaleAccount::getAccountNumber)
                .collect(Collectors.toList());

        // 按店铺分组
        Map<String, List<FruugoBatchUpdateInfoDto>> accountPriceInfoMap =
                dto.stream().collect(Collectors.groupingBy(FruugoBatchUpdateInfoDto::getAccount));
        // 遍历每个店铺处理
        for (String accountNum : accountPriceInfoMap.keySet()) {
            if (!newAccountNumList.contains(accountNum)) {
                continue;
            }

            List<FruugoBatchUpdateInfoDto> fruugoBatchUpdatePriceDtos = accountPriceInfoMap.get(accountNum);
            if (CollectionUtils.isEmpty(fruugoBatchUpdatePriceDtos)) {
                continue;
            }

            // 按 productId 分组
            Map<String, List<FruugoBatchUpdateInfoDto>> productPriceInfoMap =
                    fruugoBatchUpdatePriceDtos.stream().filter(pro -> StringUtils.isNotBlank(pro.getNewProductId()))
                            .collect(Collectors.groupingBy(FruugoBatchUpdateInfoDto::getNewProductId));

            if (MapUtils.isEmpty(productPriceInfoMap)) {
                continue;
            }

            // 分批处理，每批最多 100 个 productId
            List<String> productIds = new ArrayList<>(productPriceInfoMap.keySet());
            List<List<String>> batches = ListUtils.partition(productIds, 100);

            for (List<String> batch : batches) {
                // 构建当前批次的 productPriceInfoMap
                Map<String, List<FruugoBatchUpdateInfoDto>> batchProductPriceInfoMap =
                        batch.stream()
                                .collect(Collectors.toMap(key -> key, productPriceInfoMap::get));

                // 生成操作报告
                List<FeedTask> feedTasks = generateFeedTasks(batchProductPriceInfoMap);

                ApiResult<String> result = null;
                try {
                    // 获取店铺账号信息
                    SaleAccountAndBusinessResponse saleAccountByAccountNumber =
                            AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_FRUUGO, accountNum);

                    if (!isValidFruugoShop(saleAccountByAccountNumber)) {
                        fruugoFeedTaskService.batchFailTask(feedTasks, "店铺不存在或者非Fruugo新店铺！");
                        continue;
                    }

                    // 调用 API 进行更新
                    CreateProductRequest createProductRequest = FruugoConvertDto.toCreateProductRequest(batchProductPriceInfoMap);
//                    log.info("updateProduct request: " + createProductRequest);
                    result = fruugoCallV1Service.updateProduct(createProductRequest, saleAccountByAccountNumber);
                } catch (Exception e) {
                    fruugoFeedTaskService.batchFailTask(feedTasks, e.getMessage());
                }

                // 记录操作报告
                if (ObjectUtils.isNotEmpty(result) && result.isSuccess()) {
                    String[] s = new String[5];
                    s[3] = result.getResult();
                    fruugoFeedTaskService.batchRunningTask(feedTasks, null, s);
                } else {
                    String errorMsg = ObjectUtils.isNotEmpty(result) ? result.getErrorMsg() : "返回结果为空";
                    fruugoFeedTaskService.batchFailTask(feedTasks, errorMsg);
                }
            }
        }
    }


    private boolean isValidFruugoShop(SaleAccountAndBusinessResponse saleAccount) {
        return saleAccount != null && BooleanUtils.isTrue(saleAccount.getColBool2());
    }

    private List<FeedTask> generateFeedTasks(Map<String, List<FruugoBatchUpdateInfoDto>> productPriceInfoMap) {
        List<FeedTask> feedTasks = new ArrayList<>();
        productPriceInfoMap.forEach((productId, productPriceInfoList) -> {
            productPriceInfoList.forEach(productPriceInfo -> {
                //attribute5为在线列表ID
                FeedTask newTask = fruugoFeedTaskService.newTask(
                        null,
                        productPriceInfo.getAccount(),
                        FruugoFeedTaskEnum.UPDATE_PRICE.getCode(),
                        productPriceInfo.getSku(),
                        productPriceInfo.getBeforePrice(),
                        productPriceInfo.getAfterPrice().toString(),
                        null,
                        null,
                        productPriceInfo.getId());
                feedTasks.add(newTask);
            });
        });
        return feedTasks;
    }


    @Override
    public List<FruugoBatchCalculatePriceDetailDto> batchCalculatePrice(FruugoBatchCalculatePriceDto dto) {
        if (ObjectUtils.isEmpty(dto) || CollectionUtils.isEmpty(dto.getDetails()) || ObjectUtils.isEmpty(dto.getProfitMargin())) {
            throw new BusinessException("参数为空");
        }
        List<String> accountList = dto.getDetails().stream().map(FruugoBatchCalculatePriceDetailDto::getAccount).distinct().collect(Collectors.toList());
        List<SaleAccount> fruugoNewAccounts = getNewSaleAccounts(accountList);

        List<String> newAccountNumList = fruugoNewAccounts.stream().map(SaleAccount::getAccountNumber).collect(Collectors.toList());


        //获取店铺配置
        FruugoAccountConfigExample fruugoAccountConfigExample = new FruugoAccountConfigExample();
        fruugoAccountConfigExample.createCriteria().andAccountNumberIn(newAccountNumList);
        List<FruugoAccountConfig> fruugoAccountConfigs = fruugoAccountConfigService.selectByExample(fruugoAccountConfigExample);

        if (CollectionUtils.isEmpty(fruugoAccountConfigs)) {
            throw new BusinessException("店铺配置不存在");
        }
        Map<String, FruugoAccountConfig> fruugoAccountConfigMap = fruugoAccountConfigs.stream().collect(Collectors.toMap(FruugoAccountConfig::getAccountNumber, Function.identity()));

        //dto过滤出新店铺数据
        List<FruugoBatchCalculatePriceDetailDto> newAccountDto = dto.getDetails().stream().filter(dtoItem ->
                newAccountNumList.contains(dtoItem.getAccount())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(newAccountDto)) {
            throw new BusinessException("没有新店铺数据");
        }

        for (FruugoBatchCalculatePriceDetailDto fruugoBatchCalculatePriceDto : newAccountDto) {
            try {
                ProductInfoVO skuInfo = ProductUtils.getSkuInfo(fruugoBatchCalculatePriceDto.getSku());
                if (ObjectUtils.isEmpty(skuInfo)) {
                    fruugoBatchCalculatePriceDto.setErrorMessage("获取不到该商品信息");
                }

                FruugoAccountConfig accountConfig = fruugoAccountConfigMap.get(fruugoBatchCalculatePriceDto.getAccount());
                accountConfig.setProfitMargin(dto.getProfitMargin());

                String price = fruugoTemplateService.getPrice(skuInfo.getTagCodes(), accountConfig, fruugoBatchCalculatePriceDto.getSku(), skuInfo.getPackageWeight(), skuInfo.getSaleCost());
                fruugoBatchCalculatePriceDto.setAfterPrice(price);
            } catch (Exception e) {
                fruugoBatchCalculatePriceDto.setErrorMessage(e.getMessage());
            }

        }
        return newAccountDto;
    }

    private List<SaleAccount> getNewSaleAccounts(List<String> accountList) {
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setAccountNumberList(accountList);
        List<SaleAccount> accountInfoList = saleAccountService.getAccountInfoList(request);
        if (CollectionUtils.isEmpty(accountInfoList)) {
            throw new BusinessException("未找到店铺信息");
        }
        List<SaleAccount> fruugoNewAccounts = accountInfoList.stream()
                .filter(account -> Boolean.TRUE.equals(account.getColBool2()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fruugoNewAccounts)) {
            throw new BusinessException("只支持Fruugo新店铺！");
        }
        return fruugoNewAccounts;
    }

    /**
     * 新店铺数据同步
     *
     * @param account
     * @param fruugoProductResponse
     */
    @Override
    public void sysncItemByNewAccount(String account, FruugoProductResponse fruugoProductResponse) {
        if (ObjectUtils.isEmpty(fruugoProductResponse)) {
            throw new BusinessException("返回体fruugoProductResponse为null");
        }
        Map<String, FruugoProductResponse.Product> products = fruugoProductResponse.getProducts();
        if (ObjectUtils.isEmpty(products)) {
            throw new BusinessException("返回体fruugoProductResponse.products为null");
        }
        products.forEach((key, value) -> {
            List<String> skuList = new ArrayList<>();

            if (MapUtils.isEmpty(value.getSkus()) || ObjectUtils.isEmpty(value.getProduct())) {
                throw new BusinessException("返回体fruugoProductResponse.products为null");
            }
            value.getSkus().forEach((sku, skuValue) -> {
                skuList.add(sku);
            });

            //过滤在线列表存在的SKU
            Map<String, EsFruugoItem> exitSkuMap = this.findSkuByAccount(key, skuList, account);
            if (MapUtils.isNotEmpty(exitSkuMap)) {
                //存在做更新
                this.updateEsSku(exitSkuMap, account, value);
            }

            skuList.removeIf(sku -> exitSkuMap.containsKey(key + "-" + sku));
            if (CollectionUtils.isNotEmpty(skuList)) {
                this.saveNewEsSku(skuList, account, key, value);
            }
        });

    }

    private void saveNewEsSku(List<String> skuList, String account, String key, FruugoProductResponse.Product value) {
        skuList.forEach(sku -> {
            EsFruugoItem esFruugoItem = new EsFruugoItem();
            EsFruugoItem item = FruugoConvertDto.toEsFruugoItem(esFruugoItem, value.getSkus().get(sku).getSkuDetails());
            if (ObjectUtils.isEmpty(item)) {
                return;
            }
            item.setAccountNumber(account);
            item.setNewCategory(value.getProduct().getCategory());
            item.setNewProductId(key);
            ProductInfoVO skuInfo = ProductUtils.getSkuInfo(sku);
            FruugoItemUtils.setProductInfo(item, skuInfo);
            item.setCreateDate(new Date());
            item.setLastUpdate(new Date());
            esFruugoItemService.save(item);
        });

    }

    private void updateEsSku(Map<String, EsFruugoItem> exitSkuMap, String account, FruugoProductResponse.Product value) {
        exitSkuMap.forEach((ProductSku, esFruugoItem) -> {
            EsFruugoItem item = FruugoConvertDto.toEsFruugoItem(esFruugoItem, value.getSkus().get(esFruugoItem.getSellerSku()).getSkuDetails());
            if (ObjectUtils.isEmpty(item)) {
                return;
            }
            item.setAccountNumber(account);
            item.setNewCategory(value.getProduct().getCategory());
            item.setLastUpdate(new Date());

            if (StringUtils.isBlank(item.getItemStatus())) {
                ProductInfoVO skuInfo = ProductUtils.getSkuInfo(item.getSku());
                item.setItemStatus(skuInfo.getSkuStatus());
                // 是否促销
                item.setPromotion(skuInfo.getPromotion());
                // 是否新品
                item.setNewState(skuInfo.getNewState());
            }

            esFruugoItemService.save(esFruugoItem);

        });

    }

    @Override
    public Map<String, EsFruugoItem> findSkuByAccount(String productId, List<String> skuList, String account) {
        EsFruugoItemRequest esFruugoItemRequest = new EsFruugoItemRequest();
        esFruugoItemRequest.setSellerSku(skuList);
        esFruugoItemRequest.setAccountNumber(List.of(account));
        if (StringUtils.isNotBlank(productId)) {
            esFruugoItemRequest.setProductId(List.of(productId));
        }
        esFruugoItemRequest.setFields(EsFruugoItemRequest.FULL_FIELDS);
        List<EsFruugoItem> items = esFruugoItemService.listItemByRequest(esFruugoItemRequest);
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyMap();
        }
        return items.stream()
                .collect(Collectors.toMap(
                        item -> item.getNewProductId() + "-" + item.getSellerSku(),
                        Function.identity(),
                        (v1, v2) -> v1
                ));
    }

    /**
     * 同步产品信息
     *
     * @param account
     * @param items
     */
    @Override
    public void syncItemInfo(SaleAccount account, List<EsFruugoItem> items) {
        // 存在则更新
        List<String> ids = items.stream().map(EsFruugoItem::getId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        Map<String, EsFruugoItem> updateItemMap = items.stream().collect(Collectors.toMap(EsFruugoItem::getId, Function.identity()));
        List<EsFruugoItem> exitItems = esFruugoItemService.findInfoByIds(ids);
        exitItems.forEach(item -> {
            // 更新、状态、库存、同步时间
            EsFruugoItem updateItem = updateItemMap.get(item.getId());
            if (StringUtils.isBlank(item.getItemStatus())) {
                ProductInfoVO skuInfo = ProductUtils.getSkuInfo(item.getSku());
                item.setItemStatus(skuInfo.getSkuStatus());
                // 是否促销
                item.setPromotion(skuInfo.getPromotion());
                // 是否新品
                item.setNewState(skuInfo.getNewState());
            }

            String localArticleNumber = item.getSku();
            String articleNumber = updateItem.getSku();
            if (!StringUtils.equalsIgnoreCase(articleNumber, localArticleNumber)) {
                String mergeSku = ProductUtils.getMergeSku(articleNumber);
                if (StringUtils.equalsIgnoreCase(mergeSku, localArticleNumber)) {
                    // 相等使用合并sku并使用原数据的产品信息
                    item.setSku(localArticleNumber);
                }
            }

            item.setStatus(updateItem.getStatus());
            item.setInventory(updateItem.getInventory());
            Date now = new Date();
            item.setSyncDate(now);
            esFruugoItemService.save(item);
        });

        if (exitItems.size() == items.size()) {
            return;
        }

        List<String> exitIds = exitItems.stream().map(EsFruugoItem::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(exitIds)) {
            // 去掉已存在的
            items.removeIf(item -> exitIds.contains(item.getId()));
        }
        //获取sku
        List<String> skuList = items.stream().map(EsFruugoItem::getSku).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }

        //获取sku信息
        Map<String, ProductInfoVO> productInfoMap = getProductInfo(skuList);
        if (MapUtils.isEmpty(productInfoMap)) {
            fruugoFeedTaskService.newFailTask(null, account.getAccountNumber(), FruugoFeedTaskEnum.SYNC_LISTING.getCode(), null, "es查询产品信息为空！");
            return;
        }

        //整合数据
        getModel(items, productInfoMap);
        //入库
        for (EsFruugoItem esFruugoItem : items) {
            // 处理sku
            parseSku(esFruugoItem);
            esFruugoItemService.save(esFruugoItem);
        }
    }

    /**
     * 查询ES整合sku产品信息
     *
     * @param skus
     * @return
     */
    public Map<String, ProductInfoVO> getProductInfo(List<String> skus) {
        Map<String, ProductInfoVO> infoMap = new HashMap<>(skus.size());
        for (String sku : skus) {
            //获取sku信息
            ProductInfoVO skuInfo = ProductUtils.getSkuInfo(sku);
            infoMap.put(sku, skuInfo);
        }
        return infoMap;
    }

    /**
     * 根据es查询的产品信息整合es对象
     *
     * @param itemByXml
     * @param productInfoMap
     */
    public void getModel(List<EsFruugoItem> itemByXml,
                         Map<String, ProductInfoVO> productInfoMap) {
        for (EsFruugoItem esFruugoItem : itemByXml) {
            ProductInfoVO productInfoVO = productInfoMap.get(esFruugoItem.getSku());
            if (productInfoVO != null) {
                FruugoItemUtils.setProductInfo(esFruugoItem, productInfoVO);
            }

            //同步时间
            esFruugoItem.setSyncDate(new Timestamp(System.currentTimeMillis()));
        }
    }


    @Override
    public void syncItemInfos(SaleAccount account, List<String> itemIds) {

    }

    @Override
    public void updateInventory(FruugoUpdateDto fruugoUpdateDto, SaleAccount accountNumber) {
        //查询ES
        EsFruugoItem itemInfo = esFruugoItemService.findInfoByItemId(fruugoUpdateDto.getItemId());
        if (itemInfo == null) {
            fruugoFeedTaskService.newFailTask(null, accountNumber.getAccountNumber(), FruugoFeedTaskEnum.UPDATE_INVENTORY.getCode(), null, "es查询产品信息为空！");
            return;
        }
        if (accountNumber == null) {
            EsSaleAccountRequest request = new EsSaleAccountRequest();
            request.setAccountNumberList(Arrays.asList(itemInfo.getAccountNumber()));
            List<SaleAccount> accountInfoList = saleAccountService.getAccountInfoList(request);
            if (CollectionUtils.isEmpty(accountInfoList)) {
                fruugoFeedTaskService.newFailTask(null, accountNumber.getAccountNumber(), FruugoFeedTaskEnum.UPDATE_INVENTORY.getCode(), null, "es查询账号信息为空！");
                return;
            } else {
                accountNumber = accountInfoList.get(0);
            }
        }
        if (fruugoUpdateDto.getInventory().equals(itemInfo.getInventory())) {
            return;
        }
        DataContextHolder.setUsername(StrConstant.ADMIN);
        //添加处理报告
        FeedTask feedTask = fruugoFeedTaskService.newTask(fruugoUpdateDto.getItemId(),
                accountNumber.getAccountNumber(),
                FruugoFeedTaskEnum.UPDATE_INVENTORY.getCode(),
                itemInfo.getSku(),
                itemInfo.getInventory().toString(),
                fruugoUpdateDto.getInventory().toString());
        //组装入参
        ApiResult<String> result = fruugoCallService.updateInventory(accountNumber, getReq(fruugoUpdateDto));
        if (result.isSuccess()) {
            try {
                Document document = DocumentHelper.parseText(result.getResult());
                String msg = document.getRootElement().getText();
                if (StringUtils.isNotBlank(msg)) {
                    fruugoFeedTaskService.failTask(feedTask, "修改失败！" + msg);
                } else {
                    //修改时间
                    itemInfo.setLastUpdate(new Timestamp(System.currentTimeMillis()));
//                    itemInfo.setInventory(fruugoUpdateDto.getInventory());
                    esFruugoItemService.save(itemInfo);
                    fruugoFeedTaskService.succeedTask(feedTask, "修改成功！");
                }
            } catch (Exception e) {
                fruugoFeedTaskService.failTask(feedTask, "修改失败！返参解析错误，请至平台后台查看是否修改成功！");
                e.printStackTrace();
            }
        }
    }

    @Override
    public void batchUpdateInventory(SaleAccount account, List<EsFruugoItem> fruugoItemList, Integer newInventory) {
        if (CollectionUtils.isEmpty(fruugoItemList) || null == account) {
            return;
        }
        List<FeedTask> feedTasks = new ArrayList<>();
        for (EsFruugoItem item : fruugoItemList) {
            FeedTask feedTask = fruugoFeedTaskService.newTask(item.getItemId(),
                    account.getAccountNumber(),
                    FruugoFeedTaskEnum.UPDATE_INVENTORY.getCode(),
                    item.getSku(),
                    item.getInventory().toString(),
                    newInventory.toString());
            // 修正库存
            item.setInventory(newInventory);
            item.setLastUpdate(new Timestamp(System.currentTimeMillis()));
            feedTasks.add(feedTask);
        }
        updateInventory(account, feedTasks, fruugoItemList);
    }

    @Override
    public ApiResult<String> batchUpdateInventory(List<FruugoUpdateDto> updateItemList) {
        if (CollectionUtils.isEmpty(updateItemList)) {
            return ApiResult.newError("入参不能为空");
        }
        List<String> ids = updateItemList.stream().map(FruugoUpdateDto::getId).collect(Collectors.toList());
        List<EsFruugoItem> items = esFruugoItemService.findInfoByIds(ids);
        if (CollectionUtils.isEmpty(items)) {
            return ApiResult.newError("未查询到对应的item, Ids:" + JSON.toJSON(ids));
        }

        Map<String, Integer> inventoryMap = updateItemList.stream().collect(Collectors.toMap(FruugoUpdateDto::getId, FruugoUpdateDto::getInventory, (o, n) -> n));

        //过滤改前值和改后值一样的数据
        List<EsFruugoItem> inventoryChangedItemList = items.stream().filter(item -> !inventoryMap.get(item.getId()).equals(item.getInventory())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(inventoryChangedItemList)){
            return ApiResult.newSuccess("无需修改库存,请核验数据!");
        }
        Map<String, List<EsFruugoItem>> accountItemMap = inventoryChangedItemList.stream().collect(Collectors.groupingBy(EsFruugoItem::getAccountNumber));
        accountItemMap.forEach((accountNumber, itemList) -> {
            SaleAccount account = getAccount(accountNumber);
            if (account == null) {
                log.error("batchUpdateInventory account not find：{}", accountNumber);
                return;
            }
            //按100分批执行
            ListUtils.partition(itemList, 100).forEach(batchList -> {
                // 处理报告

                if (ObjectUtils.isNotEmpty(account.getColBool2()) && account.getColBool2()) {
                    List<FeedTask> feedTasks = buildNewAccountUpdateInventoryFeedTasks(accountNumber, batchList, inventoryMap);
                    newAccountUpdateInfo(account, feedTasks, batchList, null);
                } else {
                    List<FeedTask> feedTasks = buildUpdateInventoryFeedTasks(accountNumber, batchList, inventoryMap);
                    updateInventory(account, feedTasks, batchList);
                }
            });
        });


        return ApiResult.newSuccess("提交成功，请到处理报告查询结果");
    }

    /**
     * 执行下架操作，调用newAccountUpdateInfo方法
     *
     * @param saleAccount 店铺账号信息
     * @param feedTasks FeedTask列表
     * @param itemList 商品列表
     */
    public List<FeedTask>  executeDeactivateOperation(SaleAccount saleAccount, List<FeedTask> feedTasks, List<EsFruugoItem> itemList) {
        try {
            XxlJobLogger.log("开始执行下架操作，账号: {}, 商品数量: {}", saleAccount.getAccountNumber(), itemList.size());

            // 创建FruugoUpdateInfoParams参数，设置为下架操作
            FruugoUpdateInfoParams updateParams = new FruugoUpdateInfoParams();
            updateParams.setIsOffline(true); // 设置为下架操作

            // 调用FruugoItemService的newAccountUpdateInfo方法执行下架
            // 该方法会处理商品下架的具体逻辑，包括调用Fruugo API
             newAccountUpdateInfo(saleAccount, feedTasks, itemList, updateParams);

            XxlJobLogger.log("下架操作执行完成，账号: {}, 商品数量: {}", saleAccount.getAccountNumber(), itemList.size());

        } catch (Exception e) {
            log.error("执行下架操作时发生异常，账号: {}, 商品数量: {}", saleAccount.getAccountNumber(), itemList.size(), e);
            XxlJobLogger.log("执行下架操作时发生异常，账号: {}, 错误信息: {}", saleAccount.getAccountNumber(), e.getMessage());

            // 将FeedTask标记为失败
            try {
                fruugoFeedTaskService.batchFailTask(feedTasks, "下架操作执行异常: " + e.getMessage());
            } catch (Exception failException) {
                log.error("标记FeedTask失败时发生异常", failException);
            }
        }
        return feedTasks;
    }

    private void newAccountUpdateInfo(SaleAccount account, List<FeedTask> feedTasks, List<EsFruugoItem> itemList, FruugoUpdateInfoParams params) {
        ApiResult<String> result = null;
        try {
            Map<String, List<FruugoBatchUpdateInfoDto>> map = itemList.stream().map(item -> {
                FruugoBatchUpdateInfoDto fruugoBatchUpdatePriceAndStockDto = new FruugoBatchUpdateInfoDto()
                        .setNewProductId(item.getNewProductId())
                        .setInventory(item.getInventory())
                        .setSku(item.getSku())
                        .setSellerSku(item.getSellerSku())
                        .setAfterPrice(item.getPrice())
                        .setCurrency(item.getCurrency())
                        .setTaxInclusive(item.getTaxInclusive())
                        .setAccount(account.getAccountNumber())
                        .setStatus(item.getStatus())
                        .setId(item.getId());
                if (ObjectUtils.isNotEmpty(params)) {
                    //获取修改字段
                    getUpdateField(params, item, fruugoBatchUpdatePriceAndStockDto);

                }
                return fruugoBatchUpdatePriceAndStockDto;
            }).collect(Collectors.groupingBy(FruugoBatchUpdateInfoDto::getNewProductId));

            if (MapUtils.isEmpty(map)) {
                fruugoFeedTaskService.batchFailTask(feedTasks, "未找到在线列表相关数据");
            }

            CreateProductRequest createProductRequest = FruugoConvertDto.toCreateProductRequest(map);
//            log.info("店铺:" + account.getAccountNumber() + "请求体:" + JSON.toJSONString(createProductRequest));
            result = fruugoCallV1Service.updateProduct(createProductRequest, account);
        } catch (Exception e) {
            log.error("fruugo部分修改失败：", e);
            fruugoFeedTaskService.batchFailTask(feedTasks, e.getMessage());
            throw new RuntimeException("fruugo部分修改失败：" + e.getMessage());
        }
        // 记录任务执行结果
        if (ObjectUtils.isNotEmpty(result) && result.isSuccess()) {
            String[] s = new String[5];
            s[3] = result.getResult();
            // 更新
            itemList.forEach(item -> {
                if (item.getInventory() != null && item.getInventory() > 0) {
                    item.setStatus(FruugoStockStatuEnums.INSTOCK.getCode());
                    item.setDeletedDate(null);
                }
            });
            fruugoItemService.updateItems(itemList);
            fruugoFeedTaskService.batchRunningTask(feedTasks, result.getResult(), s);
        } else {
            String errorMsg = ObjectUtils.isNotEmpty(result) ? result.getErrorMsg() : "执行结果为空";
            fruugoFeedTaskService.batchFailTask(feedTasks, errorMsg);
        }
    }

    private void getUpdateField(FruugoUpdateInfoParams params, EsFruugoItem item, FruugoBatchUpdateInfoDto dto) {
        // 1. 制造商和品牌
        Optional.ofNullable(params.getManufacturer()).filter(ObjectUtils::isNotEmpty).ifPresent(dto::setManufacturer);
        Optional.ofNullable(params.getBrand()).filter(ObjectUtils::isNotEmpty).ifPresent(dto::setBrand);

        // 2. 属性更新
        if (BooleanUtils.isTrue(params.getIsUpdateAttribute())) {
            resolveAttributes(params, item, dto);
        }

        // 3. 准备时间
        if (BooleanUtils.isTrue(params.getIsUpdateReadyTime())) {
            dto.setReadyTimeAttribute(params.getReadyTimeAttribute());
        }

        //4.下架
        if (BooleanUtils.isTrue(params.getIsOffline())) {
            item.setStatus(FruugoStockStatuEnums.NOTAVAILABLE.getCode());
            item.setInventory(0);
            item.setDeletedDate(new Date());
            dto.setStockStatus(FruugoStockStatuEnums.NOTAVAILABLE.getCode());
        }
        if (BooleanUtils.isTrue(params.getIsUpdateDiscountPrice())) {
            dto.setDiscountPrice(item.getDiscountPrice());
            dto.setDiscountVatInclusive(BooleanUtils.isTrue(item.getDiscountVatInclusive()));
            Date discountStartDate = item.getDiscountStartDate();
            if (discountStartDate != null) {
                String format = DateUtils.format(discountStartDate, "yyyy-MM-dd");
                dto.setDiscountStartDate(format);
            }
            Date discountEndDate = item.getDiscountEndDate();
            if (discountEndDate != null) {
                String format = DateUtils.format(discountEndDate, "yyyy-MM-dd");
                dto.setDiscountEndDate(format);
            }
        }
    }


    private void resolveAttributes(FruugoUpdateInfoParams params, EsFruugoItem item, FruugoBatchUpdateInfoDto dto) {
        if (MapUtils.isNotEmpty(params.getCategoryAndAttributes())) {
            dto.setAttributes(getAttributeByCategory(params.getCategoryAndAttributes(), item.getNewCategory()));
        } else if (ObjectUtils.isNotEmpty(params.getFruugoBatchUpdateAttributeDto())) {
            dto.setAttributes(getAttributes(params.getFruugoBatchUpdateAttributeDto()));
        }
    }

    private List<FruugoBatchUpdateInfoDto.Attribute> getAttributes(FruugoBatchUpdateAttributeDto dto) {
        List<FruugoBatchUpdateInfoDto.Attribute> attributes = new ArrayList<>();
        if (ObjectUtils.isEmpty(dto)) {
            return attributes;
        }
        if (BooleanUtils.isTrue(dto.getIsNeedCeMark())) {
            attributes.add(createAttribute(FruugoAttributeFlagEnum.CE_MARK, StringUtils.isNotBlank(dto.getCeMark()) ? dto.getCeMark() : ""));
        } else {
            if (StringUtils.isNotBlank(dto.getCeMark())) {
                attributes.add(createAttribute(FruugoAttributeFlagEnum.CE_MARK, dto.getCeMark()));
            }
        }

        if (BooleanUtils.isTrue(dto.getIsNeedSafetyWarnings())) {
            attributes.add(createAttribute(FruugoAttributeFlagEnum.SAFETY_WARNING, StringUtils.isNotBlank(dto.getSafetyWarnings()) ? dto.getSafetyWarnings() : ""));
        } else {
            if (StringUtils.isNotBlank(dto.getSafetyWarnings())) {
                attributes.add(createAttribute(FruugoAttributeFlagEnum.SAFETY_WARNING, dto.getSafetyWarnings()));
            }
        }

        if (BooleanUtils.isTrue(dto.getIsNeedIngredients())) {
            attributes.add(createAttribute(FruugoAttributeFlagEnum.INGREDIENTS, StringUtils.isNotBlank(dto.getIngredients()) ? dto.getIngredients() : ""));
        } else {
            if (StringUtils.isNotBlank(dto.getIngredients())) {
                attributes.add(createAttribute(FruugoAttributeFlagEnum.INGREDIENTS, dto.getIngredients()));
            }
        }
        return attributes;

    }


    private List<FruugoBatchUpdateInfoDto.Attribute> getAttributeByCategory(Map<String, List<Integer>> map, String newCategory) {
        List<FruugoBatchUpdateInfoDto.Attribute> attributes = new ArrayList<>();
        List<Integer> attributeFlags = map.getOrDefault(newCategory, Collections.emptyList());

        // 创建属性并设置默认值
        attributes.add(createAttribute(FruugoAttributeFlagEnum.CE_MARK,
                attributeFlags.contains(FruugoAttributeFlagEnum.CE_MARK.getCode()) ? "1" : ""));
        attributes.add(createAttribute(FruugoAttributeFlagEnum.SAFETY_WARNING,
                attributeFlags.contains(FruugoAttributeFlagEnum.SAFETY_WARNING.getCode())
                        ? "Not suitable for children under 36 months." : ""));
        attributes.add(createAttribute(FruugoAttributeFlagEnum.INGREDIENTS, ""));

        return attributes;
    }

    private FruugoBatchUpdateInfoDto.Attribute createAttribute(FruugoAttributeFlagEnum flagEnum, String value) {
        FruugoBatchUpdateInfoDto.Attribute attribute = new FruugoBatchUpdateInfoDto.Attribute();
        attribute.setName(flagEnum.getFieldName());
        attribute.setValue(value);
        return attribute;
    }

    @Override
    public ApiResult<String> batchUpdateInventory(List<FruugoUpdateDto> fruugoItemList, String accountNumber) {
        SaleAccount account = getAccount(accountNumber);
        if (account == null) {
            return ApiResult.newError("账号为空");
        }
        List<FeedTask> feedTasks = new ArrayList<>();
        for (FruugoUpdateDto item : fruugoItemList) {
            Integer newInventory = item.getInventory();
            FeedTask feedTask = fruugoFeedTaskService.newTask(item.getItemId(),
                    accountNumber,
                    FruugoFeedTaskEnum.UPDATE_INVENTORY.getCode(),
                    item.getSku(),
                    String.valueOf(item.getBeforeInventory()),
                    newInventory.toString());
            feedTasks.add(feedTask);
        }
        String param = buildFruugoUpdateDtoInventoryRequestParam(fruugoItemList);
        ApiResult<String> result = fruugoCallService.updateInventory(account, param);
        // 失败
        if (!result.isSuccess()) {
            fruugoFeedTaskService.batchFailTask(feedTasks, result.getErrorMsg());
            return ApiResult.newError(result.getErrorMsg());
        }
        // 成功
        if (SUCCESS_RES.equals(result.getResult())) {
            fruugoFeedTaskService.batchSuccessTask(feedTasks, result.getResult());
            return ApiResult.newSuccess();
        }
        // 异常
        fruugoFeedTaskService.batchFailTask(feedTasks, result.getResult());
        return ApiResult.newSuccess();
    }

    private List<FeedTask> buildUpdateInventoryFeedTasks(String accountNumber, List<EsFruugoItem> items, Map<String, Integer> updateMap) {
        List<FeedTask> feedTasks = new ArrayList<>();
        for (EsFruugoItem item : items) {
            Integer newInventory = updateMap.get(item.getId());
            if (newInventory == null) {
                continue;
            }

            //attribute5为在线列表ID
            FeedTask feedTask = fruugoFeedTaskService.newTask(item.getItemId(),
                    accountNumber,
                    FruugoFeedTaskEnum.UPDATE_INVENTORY.getCode(),
                    item.getSku(),
                    item.getInventory().toString(),
                    newInventory.toString(),
                    null,
                    null,
                    item.getId());
            // 修正库存
            item.setInventory(newInventory);
            item.setLastUpdate(new Timestamp(System.currentTimeMillis()));
            feedTasks.add(feedTask);
        }
        return feedTasks;
    }

    private List<FeedTask> buildNewAccountUpdateInventoryFeedTasks(String accountNumber, List<EsFruugoItem> items, Map<String, Integer> updateMap) {
        List<FeedTask> feedTasks = new ArrayList<>();
        for (EsFruugoItem item : items) {
            Integer newInventory = updateMap.get(item.getId());
            if (newInventory == null) {
                continue;
            }

            //attribute5为在线列表ID
            FeedTask feedTask = fruugoFeedTaskService.newTask(item.getNewProductId(),
                    accountNumber,
                    FruugoFeedTaskEnum.UPDATE_INVENTORY.getCode(),
                    item.getSku(),
                    item.getInventory().toString(),
                    newInventory.toString(),
                    null,
                    null,
                    item.getId());
            // 修正库存
            item.setInventory(newInventory);
            item.setLastUpdate(new Timestamp(System.currentTimeMillis()));
            feedTasks.add(feedTask);
        }
        return feedTasks;
    }


    private List<FeedTask> buildNewAccountUpdateDiscountPriceFeedTasks(String accountNumber, List<EsFruugoItem> items,  Map<String, FruugoUpdateDiscountPriceDto> idAndUpdateInfoMap) {
        List<FeedTask> feedTasks = new ArrayList<>();
        for (EsFruugoItem item : items) {
            FruugoUpdateDiscountPriceDto fruugoUpdateDiscountPriceDto = idAndUpdateInfoMap.get(item.getId());
            if (fruugoUpdateDiscountPriceDto == null) {
                continue;
            }
            Double newDiscountPrice = fruugoUpdateDiscountPriceDto.getAfterPrice();
            Boolean newDiscountVatInclusive = fruugoUpdateDiscountPriceDto.getDiscountVatInclusive();

            if (newDiscountPrice == null || newDiscountVatInclusive == null) {
                continue;
            }

            //attribute5为在线列表ID
            FeedTask feedTask = fruugoFeedTaskService.newTask(item.getNewProductId(),
                    accountNumber,
                    FruugoFeedTaskEnum.UPDATE_DISCOUNT_PRICE.getCode(),
                    item.getSku(),
                    Optional.ofNullable(item.getDiscountPrice()).map(Object::toString).orElse(""),
                    newDiscountPrice.toString(),
                    null,
                    null,
                    item.getId());
            // 修正折扣价
            item.setDiscountPrice(newDiscountPrice);
            item.setDiscountVatInclusive(newDiscountVatInclusive);
            item.setLastUpdate(new Timestamp(System.currentTimeMillis()));
            item.setDiscountStartDate(fruugoUpdateDiscountPriceDto.getDiscountStartDate());
            item.setDiscountEndDate(fruugoUpdateDiscountPriceDto.getDiscountEndDate());
            feedTasks.add(feedTask);
        }
        return feedTasks;
    }

    private String buildUpdateInventoryRequestParam(List<EsFruugoItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }
        //创建xml对象
        Document doc = DocumentHelper.createDocument();
        //添加根节点
        Element root = doc.addElement("skus");
        items.forEach(update -> {
            //添加节点与节点属性
            Element sku = root.addElement("sku").addAttribute("fruugoSkuId", update.getItemId());
            //添加状态子节点数据
            if (Integer.valueOf(0).equals(update.getInventory())) {
                sku.addElement("availability").addText("OUTOFSTOCK");
            } else {
                sku.addElement("availability").addText("INSTOCK");
            }
            //添加库存子节点
            sku.addElement("itemsInStock").addText(update.getInventory().toString());
        });
        return doc.asXML();
    }

    private String buildFruugoUpdateDtoInventoryRequestParam(List<FruugoUpdateDto> items) {
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }
        //创建xml对象
        Document doc = DocumentHelper.createDocument();
        //添加根节点
        Element root = doc.addElement("skus");
        items.forEach(update -> {
            //添加节点与节点属性
            Element sku = root.addElement("sku").addAttribute("fruugoSkuId", update.getItemId());
            //添加状态子节点数据
            if (Integer.valueOf(0).equals(update.getInventory())) {
                sku.addElement("availability").addText("OUTOFSTOCK");
            } else {
                sku.addElement("availability").addText("INSTOCK");
            }
            //添加库存子节点
            sku.addElement("itemsInStock").addText(update.getInventory().toString());
        });
        return doc.asXML();
    }

    private void updateInventory(SaleAccount account, List<FeedTask> feedTasks, List<EsFruugoItem> itemList) {
        String param = buildUpdateInventoryRequestParam(itemList);
        ApiResult<String> result = fruugoCallService.updateInventory(account, param);
        // 失败
        if (!result.isSuccess()) {
            fruugoFeedTaskService.batchFailTask(feedTasks, result.getErrorMsg());
        }
        // 成功
        if (SUCCESS_RES.equals(result.getResult())) {
            fruugoFeedTaskService.batchSuccessTask(feedTasks, result.getResult());
//            for (EsFruugoItem esFruugoItem : itemList) {
//                try {
//                    esFruugoItemService.save(esFruugoItem);
//                } catch (Exception e) {
//                    log.error("save es fail :",e);
//                }
//            }
            return;
        }
        // 异常
        fruugoFeedTaskService.batchFailTask(feedTasks, result.getResult());
    }

    @Override
    public ApiResult<Map<String, Integer>> checkUpdateStock(List<String> ids) {
        List<EsFruugoItem> itemList = esFruugoItemService.findInfoByIds(ids);
        List<String> skuList = updateStockHandler.listReductionAndCleanForbidSku(itemList);
        if (CollectionUtils.isNotEmpty(skuList)) {
            Map<String, Integer> skuStockMap = updateStockHandler.getSkuStockMap(skuList);
            if (MapUtils.isNotEmpty(skuStockMap)) {
                return ApiResult.of(true, skuStockMap, "存在SKU单品状态为清仓、甩卖，且SKU在Fruugo不禁售，不允许修改库存为0，只允许修改库存为可用库存");
            }
        }
        return ApiResult.newSuccess();
    }

    public String getReq(FruugoUpdateDto fruugoUpdateDto) {
        //创建xml对象
        Document doc = DocumentHelper.createDocument();
        //添加根节点
        Element root = doc.addElement("skus");
        //添加节点与节点属性
        Element sku = root.addElement("sku").addAttribute("fruugoSkuId", fruugoUpdateDto.getItemId());
        //添加状态子节点数据
        sku.addElement("availability").addText("INSTOCK");
        //添加库存子节点
        sku.addElement("itemsInStock").addText(fruugoUpdateDto.getInventory().toString());
        return doc.asXML();
    }

    /**
     * 解析SKU
     * 解析sku SKU优先用sellersku解析，sellersku解析失败则取绑定SKU中的数据
     *
     * @param esFruugoItem
     * @return
     */
    public void parseSku(EsFruugoItem esFruugoItem) {
        /**
         * 首先判断sellerSku是否包含_,如果包含下划线 则是解析成功  GT48294896_YYL->GT48294896
         * 如果不包含_则解析失败此时需从sku绑定表中获取对应关系
         * 上述条件都不满足时则将sku数据设置为空
         */
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (StringUtils.isNotBlank(esFruugoItem.getSellerSku())) {
            esFruugoItem.setSku("");
            //sellerSku不等于空进行解析
            if (esFruugoItem.getSellerSku().contains("_")) {
                //sellerSku包含下划线_ 可以解析该数据
                esFruugoItem.setSku(esFruugoItem.getSellerSku().substring(0, esFruugoItem.getSellerSku().indexOf("_")));
            } else {
                //sellerSku解析不出来
                if (redisClusterTemplate.hExists(FruugoRedisConstant.syncKey + ":skuBind", esFruugoItem.getSellerSku())) {
                    // 先从redis尝试获取缓存
                    esFruugoItem.setSku(redisClusterTemplate.hGet(FruugoRedisConstant.syncKey + ":skuBind", String.class, esFruugoItem.getSellerSku()));
                } else {
                    //从数据库中查询sku绑定
                    EsSkuBind skuBind = skuBindService.getEsSkuBind(esFruugoItem.getSellerSku(), Platform.Fruugo.name());
                    if (skuBind != null) {
                        esFruugoItem.setSku(skuBind.getSku());
                        redisClusterTemplate.hSet(FruugoRedisConstant.syncKey + ":skuBind", esFruugoItem.getSellerSku(), skuBind.getSku());
                    }
                }
            }
        }
    }

    public SaleAccount getAccount(String accountNumber) {
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setAccountNumberList(Collections.singletonList(accountNumber));
        List<SaleAccount> accountInfoList = saleAccountService.getAccountInfoList(request);
        if (CollectionUtils.isNotEmpty(accountInfoList)) {
            return accountInfoList.get(0);
        }
        return null;
    }


    @Override
    public void updateAttributes(SaleAccount saleAccount, List<EsFruugoItem> esFruugoItemList, FruugoBatchUpdateAttributeDto dto) {
        if (CollectionUtils.isEmpty(esFruugoItemList)) {
            return;
        }
        if (ObjectUtils.isEmpty(saleAccount) || ObjectUtils.isEmpty(saleAccount.getColBool2()) || !saleAccount.getColBool2()) {
            return;
        }

        //按100分批
        ListUtils.partition(esFruugoItemList, 100).forEach(batchList -> {
            //创建处理报告
            List<FeedTask> feedTasks = buildUpdateAttributeFeedTask(saleAccount.getAccountNumber(), batchList);
            FruugoUpdateInfoParams updateInfoParams = new FruugoUpdateInfoParams();
            updateInfoParams.setFruugoBatchUpdateAttributeDto(dto);
            updateInfoParams.setIsUpdateAttribute(true);
            newAccountUpdateAttribute(saleAccount, feedTasks, batchList, updateInfoParams);
        });
    }





    @Override
    public Map<String, EsFruugoItem> findProductMapByAccountAndProductId(List<String> productId, String account) {
        EsFruugoItemRequest esFruugoItemRequest = new EsFruugoItemRequest();
        esFruugoItemRequest.setAccountNumber(List.of(account));
        if (CollectionUtils.isNotEmpty(productId)) {
            esFruugoItemRequest.setProductId(productId);
        }
        esFruugoItemRequest.setFields(new String[]{"id", "accountNumber", "sku", "inventory", "newProductId", "status", "errorMsg", "sellerSku"});
        List<EsFruugoItem> items = esFruugoItemService.listItemByRequest(esFruugoItemRequest);
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyMap();
        }
        return items.stream()
                .collect(Collectors.toMap(
                        item -> item.getNewProductId() + "-" + item.getSku(),
                        Function.identity(),
                        (v1, v2) -> v1
                ));
    }


    @Override
    public void batchDeactivateOrDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            throw new BusinessException("参数不能为空");
        }

        List<EsFruugoItem> items = esFruugoItemService.findInfoByIds(idList);
        if (CollectionUtils.isEmpty(items)) {
           throw new BusinessException("未查询到对应的item, Ids:" + JSON.toJSON(idList));
        }

        Map<String, List<EsFruugoItem>> accountItemMap = items.stream().collect(Collectors.groupingBy(EsFruugoItem::getAccountNumber));
        accountItemMap.forEach((accountNumber, itemList) -> {
            SaleAccount account = getAccount(accountNumber);
            if (account == null) {
                log.error("batchUpdateInventory account not find：{}", accountNumber);
                return;
            }
            FruugoUpdateInfoParams params = new FruugoUpdateInfoParams();
            params.setIsOffline(true);
            //按100分批执行
            ListUtils.partition(itemList, 100).forEach(batchList -> {
                // 处理报告
                List<FeedTask> feedTasks = buildDeactivateOrDeleteFeedTask(accountNumber, batchList);
                if (BooleanUtils.isTrue(account.getColBool2())) {
                    newAccountUpdateInfo(account, feedTasks, batchList, params);
                }
            });
        });

    }

    @Override
    public void updateItems(List<EsFruugoItem> itemList) {
        esFruugoItemService.saveAll(itemList);
    }

    private List<FeedTask> buildDeactivateOrDeleteFeedTask(String accountNumber, List<EsFruugoItem> itemList) {
        List<FeedTask> feedTasks = new ArrayList<>();
        for (EsFruugoItem item : itemList) {

            //attribute5为在线列表ID
            FeedTask feedTask = fruugoFeedTaskService.newTask(item.getNewProductId(),
                    accountNumber,
                    FruugoFeedTaskEnum.DEACTIVATE.getCode(),
                    item.getSellerSku(),
                    null,
                    null,
                    null,
                    null,
                    item.getId());
            feedTasks.add(feedTask);
            item.setInventory(0);
        }
        return feedTasks;
    }

    @Override
    public ApiResult<String> batchUpdateDiscountPrice(List<FruugoUpdateDiscountPriceDto> updateItemList) {
        if (CollectionUtils.isEmpty(updateItemList)) {
            return ApiResult.newError("入参不能为空");
        }
        List<String> ids = updateItemList.stream().map(FruugoUpdateDiscountPriceDto::getId).collect(Collectors.toList());
        List<EsFruugoItem> items = esFruugoItemService.findInfoByIds(ids);
        if (CollectionUtils.isEmpty(items)) {
            return ApiResult.newError("未查询到对应的item, Ids:" + JSON.toJSON(ids));
        }

        Map<String, FruugoUpdateDiscountPriceDto> idAndUpdateInfoMap = updateItemList.stream().collect(Collectors.toMap(FruugoUpdateDiscountPriceDto::getId, Function.identity(), (o, n) -> n));

        Map<String, List<EsFruugoItem>> accountItemMap = items.stream().collect(Collectors.groupingBy(EsFruugoItem::getAccountNumber));
        accountItemMap.forEach((accountNumber, itemList) -> {
            SaleAccount account = getAccount(accountNumber);
            if (account == null) {
                log.error("batchUpdateInventory account not find：{}", accountNumber);
                return;
            }
            //按100分批执行
            ListUtils.partition(itemList, 100).forEach(batchList -> {
                // 处理报告
                List<FeedTask> feedTasks = buildNewAccountUpdateDiscountPriceFeedTasks(accountNumber, batchList, idAndUpdateInfoMap);
                FruugoUpdateInfoParams params = new FruugoUpdateInfoParams();
                params.setIsUpdateDiscountPrice(true);
                newAccountUpdateInfo(account, feedTasks, batchList, params);
            });
        });
        return ApiResult.newSuccess("提交成功，请到处理报告查询结果");
    }
}
