package com.estone.erp.publish.fruugo.service.impl;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.fruugo.enums.FruugoOperateLogEnum;
import com.estone.erp.publish.fruugo.enums.FruugoStockRoleEnums;
import com.estone.erp.publish.fruugo.mapper.FruugoAccountConfigMapper;
import com.estone.erp.publish.fruugo.model.*;
import com.estone.erp.publish.fruugo.model.dto.FruugoBatchSetReadyTimeDto;
import com.estone.erp.publish.fruugo.model.dto.FruugoConvertDto;
import com.estone.erp.publish.fruugo.model.vo.FruugoAccountConfigPageVO;
import com.estone.erp.publish.fruugo.model.vo.FruugoAccountConfigVO;
import com.estone.erp.publish.fruugo.service.FruugoAccountConfigService;
import com.estone.erp.publish.fruugo.service.FruugoOperateLogService;
import com.estone.erp.publish.fruugo.util.FruugoOperateLogUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> fruugo_account_config
 * 2023-08-06 20:12:41
 */
@Service("fruugoAccountConfigService")
@Slf4j
public class FruugoAccountConfigServiceImpl implements FruugoAccountConfigService {
    @Resource
    private FruugoAccountConfigMapper fruugoAccountConfigMapper;
    @Autowired
    private FruugoOperateLogService operateLogService;
    @Resource
    private SaleAccountService saleAccountService;
    @Resource
    private PermissionsHelper permissionHelper;
    @Override
    public int countByExample(FruugoAccountConfigExample example) {
        Assert.notNull(example, "example is null!");
        return fruugoAccountConfigMapper.countByExample(example);
    }

    @Override
    public CQueryResult<FruugoAccountConfigPageVO> search(CQuery<FruugoAccountConfigCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        FruugoAccountConfigCriteria query = cquery.getSearch();
        // 权限控制
        checkPermission(query);
        FruugoAccountConfigExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = fruugoAccountConfigMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        example.setOrderByClause("updated_time desc");
        List<FruugoAccountConfig> fruugoAccountConfigs = fruugoAccountConfigMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(fruugoAccountConfigs)){
            CQueryResult<FruugoAccountConfigPageVO> result = new CQueryResult<>();
            result.setTotal(total);
            result.setTotalPages(totalPages);
            result.setRows(Collections.emptyList());
            return result;
        }

        // 组装结果
        List<String> accountNumbers = fruugoAccountConfigs.stream().map(FruugoAccountConfig::getAccountNumber).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, SalesmanAccountDetail> salesmanAccountDetailMap = EsAccountUtils.getSalesmanAccountDetailMapByEs(accountNumbers, SaleChannel.CHANNEL_FRUUGO);

        List<FruugoAccountConfigPageVO> resultList = fruugoAccountConfigs.stream().map(a -> {
            FruugoAccountConfigPageVO vo = FruugoConvertDto.convertFruugoAccountConfigPageVO(a);
            SalesmanAccountDetail salesmanAccountDetail = salesmanAccountDetailMap.get(a.getAccountNumber());
            if (ObjectUtils.isNotEmpty(salesmanAccountDetail)){
                vo.setSaleMan(salesmanAccountDetail.getSalesmanSet().stream().findFirst().orElse(""));
                vo.setSaleLeader(salesmanAccountDetail.getSalesTeamLeaderName());
                vo.setSaleSupervisor(salesmanAccountDetail.getSalesSupervisorName());
            }
            return vo;
        }).collect(Collectors.toList());

        CQueryResult<FruugoAccountConfigPageVO> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(resultList);
        return result;

    }

    /**
     * 数据权限校验
     * 1. 销售仅能查看自己负责的店铺数据，组长和主管可以查看自己和下级店铺数据；平台主管和超级管理员可以查看全部
     * 2. 销售/销售组长/销售主管 多个入参时已最小的为准
     */
    private void checkPermission(FruugoAccountConfigCriteria query) {

        List<String> accounts = permissionHelper.getCurrentUserPermission(query.getAccountNumbers(), query.getSaleSupervisorList(), query.getSaleLeaderList(), query.getSaleManList(), SaleChannel.CHANNEL_FRUUGO);
        query.setAccountNumbers(accounts);

    }

    @Override
    public FruugoAccountConfig selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return fruugoAccountConfigMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<FruugoAccountConfig> selectByExample(FruugoAccountConfigExample example) {
        Assert.notNull(example, "example is null!");
        return fruugoAccountConfigMapper.selectByExample(example);
    }

    @Override
    public int insert(FruugoAccountConfig record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return fruugoAccountConfigMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(FruugoAccountConfig record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return fruugoAccountConfigMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(FruugoAccountConfig record, FruugoAccountConfigExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return fruugoAccountConfigMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return fruugoAccountConfigMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public ApiResult<String> updateAccountConfig(FruugoAccountConfig accountConfig) {
        // 检查 accountSite 是否已存在
        if (StringUtils.isNotBlank(accountConfig.getAccountSite()) && isConfigExists("accountSite", accountConfig.getAccountSite(),accountConfig.getId())) {
            return ApiResult.newError("该店铺后缀已存在配置，请勿重复添加");
        }
        // 检查 eanPrefix 是否已存在
        if (StringUtils.isNotBlank(accountConfig.getEanPrefix()) && isConfigExists("eanPrefix", accountConfig.getEanPrefix(), accountConfig.getId())) {
            return ApiResult.newError("该EAN前缀已存在配置，请勿重复添加");
        }
        //ean前缀历史数据也需要校验重复
        FruugoAccountConfig dbAccountConfig = this.selectByPrimaryKey(accountConfig.getId());
        if (StringUtils.isNotBlank(accountConfig.getEanPrefix()) &&
                (null == dbAccountConfig || dbAccountConfig.getEanPrefix() == null || !accountConfig.getEanPrefix().equals(dbAccountConfig.getEanPrefix()))){
            FruugoOperateLogExample fruugoOperateLogExample = new FruugoOperateLogExample();
            fruugoOperateLogExample.createCriteria().andTypeEqualTo("account_config")
                    .andFieldNameEqualTo("eanPrefix")
                    .andCustomerSql("( `before` = "+ accountConfig.getEanPrefix() +" or `after` = "+ accountConfig.getEanPrefix() + ")");
            List<FruugoOperateLog> fruugoOperateLogs = operateLogService.selectByExample(fruugoOperateLogExample);
            if (CollectionUtils.isNotEmpty(fruugoOperateLogs)){
                return ApiResult.newError("该EAN前缀已存在历史配置中，请勿重复添加");
            }
        }
        String userName = WebUtils.getUserName();
        operateLogService.generateConfigLog(accountConfig, userName);
        accountConfig.setLastUpdateBy(userName);
        accountConfig.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
        fruugoAccountConfigMapper.updateByPrimaryKeySelective(accountConfig);
        return ApiResult.newSuccess("修改成功");
    }

    private boolean isConfigExists(String field, Object value, Integer id) {
        FruugoAccountConfigExample example = new FruugoAccountConfigExample();
        FruugoAccountConfigExample.Criteria criteria = example.createCriteria();

        switch (field) {
            case "accountSite":
                criteria.andAccountSiteEqualTo((String) value).andIdNotEqualTo(id);
                break;
            case "eanPrefix":
                criteria.andEanPrefixEqualTo((String) value).andIdNotEqualTo(id);
                break;
            default:
                throw new IllegalArgumentException("Unsupported field: " + field);
        }

        int total = fruugoAccountConfigMapper.countByExample(example);
        return total > 0;
    }

    @Override
    public List<String> listAccountNumberByStockRule(FruugoStockRoleEnums role) {
        FruugoAccountConfigExample example = new FruugoAccountConfigExample();
        example.createCriteria().andUpdateStockRuleCodeEqualTo(role.name());

        List<FruugoAccountConfig> fruugoAccountConfigs = selectByExample(example);
        if (CollectionUtils.isEmpty(fruugoAccountConfigs)) {
            return new ArrayList<>();
        }
        return fruugoAccountConfigs.stream().map(FruugoAccountConfig::getAccountNumber).collect(Collectors.toList());
    }

    @Override
    public ApiResult<String> syncAccountConfig(List<String> accountNumbers) {
        String[] withFields = {"accountNumber", "accountStatus"};
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(SaleChannel.CHANNEL_FRUUGO);
        request.setAccountStatusList(Collections.singletonList(SaleAccountStastusEnum.NORMAL.getCode()));
        if (CollectionUtils.isNotEmpty(accountNumbers)) {
            request.setAccountNumberList(accountNumbers);
        }

        List<SaleAccount> saleAccounts = saleAccountService.getSaleAccountsEs(request, withFields);

        FruugoAccountConfigExample example = new FruugoAccountConfigExample();
        List<FruugoAccountConfig> dbOzonAccountConfigs = fruugoAccountConfigMapper.selectByExample(example);
        Map<String, FruugoAccountConfig> accountConfigMap = dbOzonAccountConfigs.stream().collect(Collectors.toMap(FruugoAccountConfig::getAccountNumber, Function.identity(), (o1, o2) -> o1));
        List<String> dbAccountNumbers = dbOzonAccountConfigs.stream()
                .map(FruugoAccountConfig::getAccountNumber)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        for (SaleAccount saleAccount : saleAccounts) {
            String accountNumber = saleAccount.getAccountNumber();
            if (StringUtils.isBlank(accountNumber)) {
                continue;
            }
            if (dbAccountNumbers.contains(accountNumber)) {
                FruugoAccountConfig accountConfig = accountConfigMap.get(accountNumber);
                updateAccount(accountConfig, saleAccount);
                continue;
            }
            addAccountConfig(saleAccount);
        }
        return ApiResult.newSuccess();
    }

    private void addAccountConfig(SaleAccount saleAccount) {
        try {
            // 更新
            Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
            FruugoAccountConfig accountConfig = new FruugoAccountConfig();
            accountConfig.setAccountNumber(saleAccount.getAccountNumber());
            accountConfig.setStatus(Integer.valueOf(saleAccount.getAccountStatus()));
            accountConfig.setSyncTime(timestamp);
            accountConfig.setCreatedTime(timestamp);
            accountConfig.setUpdatedTime(timestamp);
            fruugoAccountConfigMapper.insert(accountConfig);
        } catch (Exception e) {
            XxlJobLogger.log("OzonAccountConfig 出错", e);
        }
    }

    private void updateAccount(FruugoAccountConfig accountConfig, SaleAccount saleAccount) {
        Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
        accountConfig.setStatus(Integer.valueOf(saleAccount.getAccountStatus()));
        accountConfig.setSyncTime(timestamp);
        accountConfig.setUpdatedTime(timestamp);
        fruugoAccountConfigMapper.updateByPrimaryKeySelective(accountConfig);
    }

    @Override
    public FruugoAccountConfig getByAccountNumber(String account) {
        return fruugoAccountConfigMapper.selectByAccountNumber(account);
    }

    @Override
    public void batchSetReadyTime(FruugoBatchSetReadyTimeDto dto) {
        dto.getIdList().forEach(id -> {
            FruugoAccountConfig accountConfig = fruugoAccountConfigMapper.selectByPrimaryKey(id);
            if (accountConfig == null) {
                return;
            }
            accountConfig.setReadyTime(dto.getReadyTime());
            String userName = WebUtils.getUserName();
            operateLogService.generateConfigLog(accountConfig, userName);
            accountConfig.setLastUpdateBy(userName);
            accountConfig.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
            fruugoAccountConfigMapper.updateByPrimaryKeySelective(accountConfig);
        });

    }

    /**
     * 保存Fruugo店铺配置
     *
     * @param saveParam 保存参数
     */
    @Override
    @Transactional
    public void saveConfig(FruugoAccountConfigVO saveParam) {


        // 检查 accountSite 是否已存在
        if (StringUtils.isNotBlank(saveParam.getAccountSite()) && isConfigExists("accountSite", saveParam.getAccountSite(), saveParam.getId())) {
            throw new RuntimeException("该店铺后缀已存在配置，请勿重复添加");
        }
        // 检查 eanPrefix 是否已存在
        if (StringUtils.isNotBlank(saveParam.getEanPrefix()) && isConfigExists("eanPrefix", saveParam.getEanPrefix(), saveParam.getId())) {
            throw new RuntimeException("该EAN前缀已存在配置，请勿重复添加");
        }
        //ean前缀历史数据也需要校验重复
        FruugoAccountConfig dbConfig = this.selectByPrimaryKey(saveParam.getId());

        if (null == dbConfig) {
            throw new RuntimeException("该店铺配置不存在");
        }

        if (StringUtils.isNotBlank(saveParam.getEanPrefix()) &&
                (null == dbConfig || dbConfig.getEanPrefix() == null || !saveParam.getEanPrefix().equals(dbConfig.getEanPrefix()))) {
            FruugoOperateLogExample fruugoOperateLogExample = new FruugoOperateLogExample();
            fruugoOperateLogExample.createCriteria().andTypeEqualTo("account_config")
                    .andFieldNameEqualTo("eanPrefix")
                    .andCustomerSql("( `before` = " + saveParam.getEanPrefix() + " or `after` = " + saveParam.getEanPrefix() + ")");
            List<FruugoOperateLog> fruugoOperateLogs = operateLogService.selectByExample(fruugoOperateLogExample);
            if (CollectionUtils.isNotEmpty(fruugoOperateLogs)) {
                throw new RuntimeException("该EAN前缀已存在历史配置中，请勿重复添加");
            }
        }


        String userName = WebUtils.getUserName();
        Timestamp currentTime = Timestamp.valueOf(LocalDateTime.now());
        // 保存旧配置用于日志记录
        FruugoAccountConfigVO oldConfigVO = new FruugoAccountConfigVO();
        BeanUtils.copyProperties(dbConfig, oldConfigVO);
        oldConfigVO.setProdLevel2CategoryNameList(oldConfigVO.getProdLevel2CategoryNameList());

        dbConfig.setAccountSite(saveParam.getAccountSite());
        dbConfig.setEanPrefix(saveParam.getEanPrefix());
        dbConfig.setManufacturer(saveParam.getManufacturer());
        dbConfig.setBrand(saveParam.getBrand());
        dbConfig.setProfitMargin(saveParam.getProfitMargin());
        dbConfig.setPlatformRate(saveParam.getPlatformRate());
        dbConfig.setCurrency(saveParam.getCurrency());
        dbConfig.setDefaultStock(saveParam.getDefaultStock());
        dbConfig.setReadyTime(saveParam.getReadyTime());
        dbConfig.setUpdateStockRuleCode(saveParam.getUpdateStockRuleCode());
        dbConfig.setLastUpdateBy(userName);
        dbConfig.setUpdatedTime(currentTime);

        // ------------------ 自动刊登配置---------------------
        // 可刊登分类选择
        dbConfig.setProdCategoryCodes(saveParam.getProdCategoryCodes());
        // 产品系统类目name
        dbConfig.setProdCategoryNames(saveParam.getProdCategoryNames());

        // 刊登产品限制
        dbConfig.setExcludeLabel(saveParam.getExcludeLabel());
        dbConfig.setFromWeight(saveParam.getFromWeight());
        dbConfig.setToWeight(saveParam.getToWeight());
        dbConfig.setSalesType(saveParam.getSalesType());
        dbConfig.setFromSales(saveParam.getFromSales());
        dbConfig.setToSales(saveParam.getToSales());
        dbConfig.setSkuCreateTimeType(saveParam.getSkuCreateTimeType());
        dbConfig.setSkuCreateTimeYear(saveParam.getSkuCreateTimeYear());
        dbConfig.setFromInputTime(saveParam.getFromInputTime());
        dbConfig.setToInputTime(saveParam.getToInputTime());
        dbConfig.setFromPrice(saveParam.getFromPrice());
        dbConfig.setToPrice(saveParam.getToPrice());
        dbConfig.setFromInventory(saveParam.getFromInventory());
        dbConfig.setToInventory(saveParam.getToInventory());

        // 自动刊登数量设置
        dbConfig.setPublishQuantity(saveParam.getPublishQuantity());
        dbConfig.setMinPublishMount(saveParam.getMinPublishMount());

        // 定时刊登时间设置
        dbConfig.setPublishIntervalTime(saveParam.getPublishIntervalTime());
        dbConfig.setPublishTime(saveParam.getPublishTime());
        dbConfig.setAutoPublishNew(saveParam.getAutoPublishNew());
        fruugoAccountConfigMapper.updateAccountInfoByExample(dbConfig);
        saveParam.setProdLevel2CategoryNameList(saveParam.getProdLevel2CategoryNameList());
        // 店铺配置字段记录日志
        List<FruugoOperateLog> configLogs = FruugoOperateLogUtils.buildUpdateLog(oldConfigVO, saveParam,
                FruugoOperateLogEnum.account_config, "id");
        List<FruugoOperateLog> infoLogs = new ArrayList<>(configLogs);

        // 处理区间字段日志
        List<FruugoOperateLog> sectionLogs = FruugoOperateLogUtils.handleSectionLog(oldConfigVO, saveParam);
        infoLogs.addAll(sectionLogs);

        // 处理特殊日志
        FruugoOperateLogUtils.tranSpecialLog(infoLogs);

        // 批量保存日志
        operateLogService.batchInsert(infoLogs);
    }
}