package com.estone.erp.publish.fruugo.call.model.request;

import com.estone.erp.publish.fruugo.util.FruugoItemUtils;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class FruugoGetAllProductResponse {
    private List<Product> products;

    @Data
    public static class Product {
        private String productId;
        private String manufacturer;
        private String brand;
        private List<Sku> skus;

        @Data
        public static class Sku {
            private String skuId;
            private List<Code> codes;
            private SupplyData supplyData;
            private String category;
            private String title;
            private String packageWeight;
            private String language;
            private String color;
            private String size;
            private List<NormalPrice> normalPrices;
            private List<DiscountPrice> discountPrices;
            private String skuQualityStatus;
            private List<String> errors;
            private Map<String, String> attributes;

            @Data
            public static class Code {
                private String codevalue;
                private String codetype;
            }

            @Data
            public static class SupplyData {
                private String stockStatus;
                private int stockValue;
                private int leadTime;
            }

            @Data
            public static class NormalPrice {
                private double normalPriceWithoutVAT;
                private String currency;
            }

            @Data
            public static class DiscountPrice {
                // "discountPrices":[{"discountPriceWithoutVAT":60.47,"discountPriceStartDate":"Jul 24, 2025, 4:55:33 AM","currency":"USD"}]
                private double discountPriceWithoutVAT;
                private String currency;
                // Jul 24, 2025, 4:55:33 AM 需要手动转为 yyyy-MM-dd
                private String discountPriceStartDate;
                private String discountPriceEndDate;

                /**
                 * 获取格式化后的折扣开始日期（yyyy-MM-dd 格式）
                 * @return 格式化后的日期字符串，如果原始日期为空或转换失败则返回 null
                 */
                public String getFormattedDiscountPriceStartDate() {
                    return FruugoItemUtils.convertFruugoDateToYMD(this.discountPriceStartDate);
                }

                /**
                 * 获取格式化后的折扣结束日期（yyyy-MM-dd 格式）
                 * @return 格式化后的日期字符串，如果原始日期为空或转换失败则返回 null
                 */
                public String getFormattedDiscountPriceEndDate() {
                    return FruugoItemUtils.convertFruugoDateToYMD(this.discountPriceEndDate);
                }
            }
        }
    }
}
