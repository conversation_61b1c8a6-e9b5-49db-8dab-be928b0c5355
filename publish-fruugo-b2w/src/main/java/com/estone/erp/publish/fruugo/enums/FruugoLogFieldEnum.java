package com.estone.erp.publish.fruugo.enums;

/**
 * Fruugo操作日志字段名称枚举
 * 
 * <AUTHOR>
 * @date 2023/08/06
 * @description 参考AmazonLogFieldEnum实现的Fruugo日志字段枚举
 */
public enum FruugoLogFieldEnum {

    PRODUCT_WEIGHT("productWeight", "产品重量限制"),
    SALE_PRICE("salePrice", "销售成本价"),
    SALES_SECTION("salesSection", "指定销量区间"),
    PRODUCT_INVENTORY("productInventory", "库存限制"),
    PRODUCT_INPUT_TIME("productInputTime", "产品录入时间"),

    ;

    /**
     * 字段英文名
     */
    private final String fieldEn;
    
    /**
     * 字段中文名
     */
    private final String fieldCn;

    /**
     * 构造函数
     * 
     * @param fieldEn 字段英文名
     * @param fieldCn 字段中文名
     */
    FruugoLogFieldEnum(String fieldEn, String fieldCn) {
        this.fieldEn = fieldEn;
        this.fieldCn = fieldCn;
    }

    /**
     * 获取字段英文名
     * 
     * @return 字段英文名
     */
    public String getFieldEn() {
        return fieldEn;
    }

    /**
     * 获取字段中文名
     * 
     * @return 字段中文名
     */
    public String getFieldCn() {
        return fieldCn;
    }
}
