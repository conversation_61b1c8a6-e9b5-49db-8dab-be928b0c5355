package com.estone.erp.publish.fruugo.call;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.fruugo.call.model.request.CreateProductRequest;
import com.estone.erp.publish.fruugo.call.model.request.GetProductRequest;
import com.estone.erp.publish.fruugo.call.model.request.GetStatusRequest;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.http.HttpHeaders;
import java.net.http.HttpResponse;
import java.util.UUID;

/**
 * @Description: Fruugo请求V1服务
 * <AUTHOR>
 * @Date 2024/11/1 下午3:36
 */
@Slf4j
@Service
public class FruugoCallV1Service {

    @Autowired
    private FruugoHttpClient httpClientWrapper;

    private final String FRUUGO_CORRELATION_PREFIX = "publish-";


    public ApiResult<String> queryProductInfo(GetProductRequest request, SaleAccountAndBusinessResponse saleAccount) {
        validateRequest(request, saleAccount);
        try {
            HttpResponse<String> response = httpClientWrapper.sendPostRequest(
                    "/v1/products/query",
                    saleAccount.getAccountNumber(),
                    saleAccount.getColStr1(),
                    FRUUGO_CORRELATION_PREFIX+"queryProductInfo-" + UUID.randomUUID(),
                    request
            );
            if (response.statusCode() == 200) {
                return ApiResult.newSuccess(response.body());
            }
            return ApiResult.newError(JSON.toJSONString(response.body()));
        } catch (Exception e) {
            return ApiResult.newError("获取产品状态失败: " + e.getMessage());
        }
    }
    public ApiResult<String> getProductStatus(GetStatusRequest request, SaleAccountAndBusinessResponse saleAccount) {
        validateRequest(request, saleAccount);

        try {
            HttpResponse<String> response = httpClientWrapper.sendPostRequest(
                    "/v1/products/status",
                    saleAccount.getAccountNumber(),
                    saleAccount.getColStr1(),
                    FRUUGO_CORRELATION_PREFIX+"getProductStatus-" + UUID.randomUUID(),
                    request
            );
            if (response.statusCode() == 200) {
                return ApiResult.newSuccess(response.body());
            }
            return ApiResult.newError(JSON.toJSONString(response.body()));
        } catch (Exception e) {
            return ApiResult.newError("获取产品状态失败: " + e.getMessage());
        }
    }


    /**
     * 同步产品列表
     *
     * @param request     产品查询请求
     * @param saleAccount 销售账号信息
     * @return 包含产品信息的API结果
     */
    public <T> ApiResult<String> getProductAll(T request, SaleAccountAndBusinessResponse saleAccount) {
//        validateRequest(request, saleAccount);
        try {
            HttpResponse<String> response = httpClientWrapper.sendPostRequest(
                    "/v1/products/all",
                    saleAccount.getAccountNumber(),
                    saleAccount.getColStr1(),
                    FRUUGO_CORRELATION_PREFIX+"getProductAll-" + UUID.randomUUID(),
                    request
            );
            HttpHeaders headers = response.headers();
            String correlationId = headers.firstValue("X-Correlation-ID").orElse("");
            if (response.statusCode() == 202) {
                return ApiResult.newSuccess(correlationId);
            }
            return ApiResult.newError(JSON.toJSONString(response.body()));
        } catch (Exception e) {
            return ApiResult.newError("获取产品失败: " + e.getMessage());
        }
    }

    /**
     * 创建新产品
     *
     * @param request     创建产品的请求内容
     * @param saleAccount 销售账号信息
     * @return API调用结果
     */
    public ApiResult<String> createOrUpdateProduct(CreateProductRequest request, SaleAccountAndBusinessResponse saleAccount) {
        validateRequest(request, saleAccount);
//        log.info("店铺：{}，请求体{}", saleAccount.getAccountNumber(), JSON.toJSONString(request));

        try {
            HttpResponse<String> response = httpClientWrapper.sendPostRequest(
                    "/v1/products",
                    saleAccount.getAccountNumber(),
                    saleAccount.getColStr1(),
                    FRUUGO_CORRELATION_PREFIX+"createProduct-" + UUID.randomUUID(),
                    request
            );
            HttpHeaders headers = response.headers();
            String correlationId = headers.firstValue("X-Correlation-ID").orElse("");
            if (response.statusCode() == 204) {
                return ApiResult.newSuccess(correlationId);
            }
            return ApiResult.newError(JSON.toJSONString(response.body()));
        } catch (Exception e) {
            return ApiResult.newError("创建产品失败: " + e.getMessage());
        }
    }

    /**
     * 创建新产品
     *
     * @param request     创建产品的请求内容
     * @param saleAccount 销售账号信息
     * @return API调用结果
     */
    public ApiResult<String> updateProductAll(CreateProductRequest request, SaleAccountAndBusinessResponse saleAccount) {
        validateRequest(request, saleAccount);
//        log.info("店铺：{}，请求体{}", saleAccount.getAccountNumber(), JSON.toJSONString(request));

        try {
            HttpResponse<String> response = httpClientWrapper.sendPostRequest(
                    "/v1/products",
                    saleAccount.getAccountNumber(),
                    saleAccount.getColStr1(),
                    FRUUGO_CORRELATION_PREFIX+"updateProduct-" + UUID.randomUUID(),
                    request
            );
            HttpHeaders headers = response.headers();
            String correlationId = headers.firstValue("X-Correlation-ID").orElse("");
            if (response.statusCode() == 204) {
                return ApiResult.newSuccess(correlationId);
            }
            return ApiResult.newError(JSON.toJSONString(response.body()));
        } catch (Exception e) {
            return ApiResult.newError("创建产品失败: " + e.getMessage());
        }
    }

    /**
     * 更新产品
     *
     * @param request     创建产品的请求内容
     * @param saleAccount 销售账号信息
     * @return API调用结果
     */
    public ApiResult<String> updateProduct(CreateProductRequest request, SaleAccountAndBusinessResponse saleAccount) {
        validateRequest(request, saleAccount);
        try {
            HttpResponse<String> response = httpClientWrapper.sendPostRequest(
                    "/v1/products/partial",
                    saleAccount.getAccountNumber(),
                    saleAccount.getColStr1(),
                    FRUUGO_CORRELATION_PREFIX+"updateProduct-" + UUID.randomUUID(),
                    request
            );
            if (response.statusCode() == 204) {
                HttpHeaders headers = response.headers();
                String correlationId = headers.firstValue("X-Correlation-ID").orElse("");
                return ApiResult.newSuccess(correlationId);
            }
            return ApiResult.newError(JSON.toJSONString(response.body()));
        } catch (Exception e) {
            return ApiResult.newError("创建产品失败: " + e.getMessage());
        }
    }

    /**
     * 更新产品
     *
     * @param request     创建产品的请求内容
     * @param saleAccount 销售账号信息
     * @return API调用结果
     */
    public ApiResult<String> updateProduct(CreateProductRequest request, SaleAccount saleAccount) {
        validateRequest(request, saleAccount);
        try {
//            return EnvironmentSupplierWrapper.execute(() -> {
//                HttpResponse<String> response = httpClientWrapper.sendPostRequest(
//                        "/v1/products/partial",
//                        saleAccount.getAccountNumber(),
//                        saleAccount.getColStr1(),
//                        FRUUGO_CORRELATION_PREFIX + "updateProduct-" + UUID.randomUUID(),
//                        request
//                );
//                if (response.statusCode() == 204) {
//                    HttpHeaders headers = response.headers();
//                    String correlationId = headers.firstValue("X-Correlation-ID").orElse("");
//                    return ApiResult.newSuccess(correlationId);
//                }
//                return ApiResult.newError(JSON.toJSONString(response.body()));
//            }, () -> {
//                return ApiResult.newSuccess("测试环境不允许修改商品");
//            });
            HttpResponse<String> response = httpClientWrapper.sendPostRequest(
                    "/v1/products/partial",
                    saleAccount.getAccountNumber(),
                    saleAccount.getColStr1(),
                    FRUUGO_CORRELATION_PREFIX + "updateProduct-" + UUID.randomUUID(),
                    request
            );
            if (response.statusCode() == 204) {
                HttpHeaders headers = response.headers();
                String correlationId = headers.firstValue("X-Correlation-ID").orElse("");
                return ApiResult.newSuccess(correlationId);
            }
            return ApiResult.newError(JSON.toJSONString(response.body()));
        } catch (Exception e) {
            return ApiResult.newError("创建产品失败: " + e.getMessage());
        }
    }

    /**
     * 验证请求和账户参数的完整性
     */
    private <T> void validateRequest(T request, SaleAccountAndBusinessResponse saleAccount) {
        if (request == null) {
            throw new IllegalArgumentException("请求不能为空");
        }
        if (saleAccount == null || saleAccount.getAccountNumber() == null || saleAccount.getColStr1() == null) {
            throw new IllegalArgumentException("账户信息不完整");
        }
    }

    private <T> void validateRequest(T request, SaleAccount saleAccount) {
        if (request == null) {
            throw new IllegalArgumentException("请求不能为空");
        }
        if (saleAccount == null || saleAccount.getAccountNumber() == null || saleAccount.getColStr1() == null) {
            throw new IllegalArgumentException("账户信息不完整");
        }
    }
}

