package com.estone.erp.publish.fruugo.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.fruugo.enums.FruugoStockRoleEnums;
import com.estone.erp.publish.fruugo.model.FruugoAccountConfig;
import com.estone.erp.publish.fruugo.model.FruugoAccountConfigCriteria;
import com.estone.erp.publish.fruugo.model.FruugoAccountConfigExample;
import com.estone.erp.publish.fruugo.model.dto.FruugoBatchSetReadyTimeDto;
import com.estone.erp.publish.fruugo.model.vo.FruugoAccountConfigPageVO;
import com.estone.erp.publish.fruugo.model.vo.FruugoAccountConfigVO;

import java.util.List;

/**
 * <AUTHOR> fruugo_account_config
 * 2023-08-06 20:12:41
 */
public interface FruugoAccountConfigService {
    int countByExample(FruugoAccountConfigExample example);

    CQueryResult<FruugoAccountConfigPageVO> search(CQuery<FruugoAccountConfigCriteria> cquery);

    List<FruugoAccountConfig> selectByExample(FruugoAccountConfigExample example);

    FruugoAccountConfig selectByPrimaryKey(Integer id);

    int insert(FruugoAccountConfig record);

    int updateByPrimaryKeySelective(FruugoAccountConfig record);

    int updateByExampleSelective(FruugoAccountConfig record, FruugoAccountConfigExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    ApiResult<String> updateAccountConfig(FruugoAccountConfig accountConfig);

    /**
     * 根据库存调整规则获取对应的店铺
     * @param role 库存规则
     * @return
     */
    List<String> listAccountNumberByStockRule(FruugoStockRoleEnums role);

    /**
     * 同步
     * @param accountNumbers
     * @return
     */
    ApiResult<String> syncAccountConfig(List<String> accountNumbers);

    FruugoAccountConfig getByAccountNumber(String account);

    /**
     * 批量设置准备时间
     * @param dto
     */
    void batchSetReadyTime(FruugoBatchSetReadyTimeDto dto);

    /**
     * 保存Fruugo店铺配置
     * @param saveParam 保存参数
     */
    void saveConfig(FruugoAccountConfigVO saveParam);
}