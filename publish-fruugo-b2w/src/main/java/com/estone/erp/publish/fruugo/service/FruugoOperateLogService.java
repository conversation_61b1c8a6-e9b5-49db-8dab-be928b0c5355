package com.estone.erp.publish.fruugo.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.fruugo.model.FruugoAccountConfig;
import com.estone.erp.publish.fruugo.model.FruugoOperateLog;
import com.estone.erp.publish.fruugo.model.FruugoOperateLogCriteria;
import com.estone.erp.publish.fruugo.model.FruugoOperateLogExample;

import java.util.List;

/**
 * <AUTHOR> fruugo_operate_log
 * 2023-08-06 20:59:14
 */
public interface FruugoOperateLogService {
    int countByExample(FruugoOperateLogExample example);

    CQueryResult<FruugoOperateLog> search(CQuery<FruugoOperateLogCriteria> cquery);

    List<FruugoOperateLog> selectByExample(FruugoOperateLogExample example);

    FruugoOperateLog selectByPrimaryKey(Integer id);

    int insert(FruugoOperateLog record);

    int updateByPrimaryKeySelective(FruugoOperateLog record);

    int updateByExampleSelective(FruugoOperateLog record, FruugoOperateLogExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    void generateConfigLog(FruugoAccountConfig accountConfig, String userName);

    /**
     * 批量插入操作日志
     * @param records 日志记录列表
     */
    void batchInsert(List<FruugoOperateLog> records);
}