package com.estone.erp.publish.fruugo.compont;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.PublishRoleEnum;
import com.estone.erp.publish.common.enums.SalesTypeEnum;
import com.estone.erp.publish.common.enums.SkuCreateTimeTypeEnum;
import com.estone.erp.publish.common.executors.FruugoExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch.model.EsFruugoItem;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsFruugoItemRequest;
import com.estone.erp.publish.elasticsearch.service.EsFruugoItemService;
import com.estone.erp.publish.fruugo.enums.FruugoTimePublishEnums;
import com.estone.erp.publish.fruugo.mapper.FruugoTimePublishQueueMapper;
import com.estone.erp.publish.fruugo.model.FruugoAccountConfig;
import com.estone.erp.publish.fruugo.model.FruugoTimePublishQueue;
import com.estone.erp.publish.fruugo.model.FruugoTimePublishQueueExample;
import com.estone.erp.publish.fruugo.service.FruugoTemplateService;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.model.TemplateRecord;
import com.estone.erp.publish.platform.service.TemplateRecordService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SkuListAndCode;
import com.estone.erp.publish.system.product.bean.SonSkuFewInfo;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEsRequest;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.system.product.util.SingleItemEsUtils;
import com.estone.erp.publish.system.scheduler.util.QueueStatus;
import com.estone.erp.publish.system.scheduler.util.RecordStatus;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.*;

/**
 * Fruugo店铺自动刊登辅助类
 *
 * <AUTHOR>
 * @date 2025-01-25
 */
@Slf4j
@Component
public class FruugoAutoPublishHelper {

    @Resource
    private TemplateRecordService templateRecordService;

    @Resource
    private FruugoTimePublishQueueMapper queueMapper;
    @Resource
    private FruugoTemplateService fruugoTemplateService;
    @Resource
    private EsFruugoItemService esFruugoItemService;

    /**
     * 获取对应时间产品系统编辑完成的产品（通过SPU映射）
     *
     * @param spuToCodeMap SPU到编码的映射
     * @return 过滤后的SPU到编码的映射
     */
    public Map<String, SkuListAndCode> getSpuToCodeMapForNewProduct(Map<String, SkuListAndCode> spuToCodeMap) {
        Map<String, SkuListAndCode> spuToCodeNewMap = new HashMap<>(0);
        // 查询输入的产品信息
        Set<String> spus = spuToCodeMap.keySet();
        List<String> spuList = new ArrayList<>(spus);

        SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
        List<SingleItemEs> allSingleItemEsList = new ArrayList<>();

        List<String> needRemoveErrorSpuList = new ArrayList<>();
        // 分批处理SPU列表，每批1000条
        CommonUtils.batchResolve(spuList, 1000, batchSpus -> {
            try {
                // 查询ES
                SingleItemEsRequest criteria = new SingleItemEsRequest();
                criteria.setSkuList(batchSpus);
                List<SingleItemEs> batchResult = singleItemEsService.getSingleItemEsList(criteria);
                if (batchResult != null) {
                    allSingleItemEsList.addAll(batchResult);
                }
            } catch (Exception e) {
                XxlJobLogger.log("调用产品ES报错，批次大小：" + batchSpus.size() + "，错误信息：" + e.getMessage());
                needRemoveErrorSpuList.addAll(batchSpus);
            }
        });

        // 由于报错的spu获取不到sonSkuFewInfos,因此需要移除报错spu，避免后续自动刊登系统限制条件直接成立.
        if (CollectionUtils.isNotEmpty(needRemoveErrorSpuList) && MapUtils.isNotEmpty(spuToCodeMap)) {
            XxlJobLogger.log("spuToCodeMap移除查询es报错spu：" + needRemoveErrorSpuList);
            needRemoveErrorSpuList.forEach(spuToCodeMap::remove);
        }

        spuToCodeMap = SingleItemEsUtils.getProductInfo(spuToCodeMap, allSingleItemEsList);

        // 避免报错时改到
        spuToCodeNewMap.putAll(spuToCodeMap);
        return spuToCodeNewMap;
    }

    /**
     * 过滤Fruugo禁售和废弃状态的产品
     */
    public List<ProductInfo> filterForbiddenAndItemStatus(String spu) {
        if (StringUtils.isBlank(spu)) {
            return Collections.emptyList();
        }
        try {
            return fruugoTemplateService.findProductInfo(spu);
        } catch (Exception e) {
            String msg = String.format("过滤Fruugo禁售和废弃状态的产品[%s]异常:", spu);
            XxlJobLogger.log(msg + e.getMessage());
            log.error(msg, e);
        }

        return Collections.emptyList();
    }

    /**
     * 生成记录
     *
     * @param account
     * @param msg
     * @param createBy
     */
    private void generateReport(String account, String msg, String createBy) {
        if (org.apache.commons.lang.StringUtils.isNotBlank(msg) && msg.length() > 65535) {
            msg = msg.substring(0, 65535);
        }
        TemplateRecord record = new TemplateRecord();
        record.setSellerId(account);
        record.setRemark(String.format("Fruugo店铺自动刊登产品定时任务：%s", msg));
        record.setStatus(RecordStatus.FAIL.getCode());
        record.setSaleChannel(SaleChannel.CHANNEL_FRUUGO);
        record.setCreatedBy(createBy);
        record.setCreationDate(new Timestamp(System.currentTimeMillis()));
        templateRecordService.insert(record);
    }

    public void createTimingQueue(Map<String, SkuListAndCode> spuToCodeMap, List<FruugoAccountConfig> fruugoAccountConfigList, int publishRole) {
        if (MapUtils.isEmpty(spuToCodeMap) || CollectionUtils.isEmpty(fruugoAccountConfigList)) {
            return;
        }
        String createBy = "admin";

        for (FruugoAccountConfig fruugoAccountConfig : fruugoAccountConfigList) {
            FruugoExecutors.executeAutoQueuePublish(() -> {
                String account = fruugoAccountConfig.getAccountNumber();
                try {
                    XxlJobLogger.log("执行店铺自动刊登，account:{}, spuSize:{}", account, spuToCodeMap.size());
                    ResponseJson resp = execPublish(createBy, fruugoAccountConfig, spuToCodeMap, publishRole);
                    if (!resp.isSuccess()) {
                        // 生成一条处理报告提示
                        generateReport(account, resp.getMessage(), createBy);
                    }
                } catch (Exception e) {
                    // 生成一条处理报告提示
                    generateReport(account, e.getMessage(), createBy);
                    log.error(String.format("店铺%s 自动刊登报错", account), e);
                }
            });
        }
    }


    public ResponseJson execPublish(String createBy, FruugoAccountConfig accountConfig, Map<String, SkuListAndCode> spuToCodeMap, int publishRole) {
        if (org.apache.commons.lang3.StringUtils.isBlank(createBy)) {
            createBy = "admin";
        }
        String accountNumber = accountConfig.getAccountNumber();
        ResponseJson resp = new ResponseJson();
        resp.setStatus(StatusCode.FAIL);

        XxlJobLogger.log(String.format("自动刊登账号%s", accountNumber));

        // 产品分类
        String prodCategoryIds = accountConfig.getProdCategoryCodes();
        // 每天最大刊登数量
        Integer maxPublishNum = accountConfig.getPublishQuantity();
        // 自动刊登间隔时间（分钟）
        Integer publishIntervalTime = accountConfig.getPublishIntervalTime();
        // 每分钟刊登SPU数量
        Optional<Integer> minPublishMount = Optional.ofNullable(accountConfig.getMinPublishMount());
        // 每天首次刊登时间
        Time time = accountConfig.getPublishTime();

        if (org.apache.commons.lang3.StringUtils.isBlank(prodCategoryIds) || null == maxPublishNum || null == time || (minPublishMount.isEmpty() && null == publishIntervalTime)) {
            log.info(String.format("账号%s 配置参数为空!", accountNumber));
            resp.setMessage(String.format("账号%s 配置参数为空!", accountNumber));
            return resp;
        }

        //筛选 可刊登的spu
        Set<String> spuSet = new HashSet<>(accountConfig.getPublishQuantity());
        findSpus(spuSet, accountConfig, spuToCodeMap, publishRole);
        if (spuSet.isEmpty()) {
            resp.setMessage(String.format("自动刊登账号%s 没有合适的产品刊登", accountNumber));
            log.error(String.format("自动刊登账号%s 没有合适的产品刊登", accountNumber));
            return resp;
        }
        XxlJobLogger.log(String.format("自动刊登账号%s 合适的产品刊登spuSize:%s", accountNumber, spuSet.size()));
        Calendar calendarTime = Calendar.getInstance();
        calendarTime.setTime(time);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, calendarTime.get(Calendar.HOUR_OF_DAY));
        calendar.set(Calendar.MINUTE, calendarTime.get(Calendar.MINUTE));
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Timestamp publishTime = new Timestamp(calendar.getTimeInMillis());

        //设置当前用户
        DataContextHolder.setUsername(createBy);

        List<FruugoTimePublishQueue> timePublishQueues = new ArrayList<>();
        List<String> spuList = new ArrayList<>(spuSet);

        for (int i = 0; i < spuList.size(); i++) {
            Timestamp currentDate = new Timestamp(System.currentTimeMillis());
            FruugoTimePublishQueue queue = new FruugoTimePublishQueue();
            timePublishQueues.add(queue);
            queue.setAccountNumber(accountNumber);
            queue.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
            queue.setStatus(QueueStatus.WAITING.getCode());
            queue.setArticleNumber(spuList.get(i));
            queue.setPublishStatus(null);
            queue.setCreatedBy(createBy);
            queue.setCreatedTime(currentDate);
            //第一个是选择的时间 i = 0;
            //  根据刊登角色确定
            if (publishRole == PublishRoleEnum.ADMIN.getPublishRole() && minPublishMount.isPresent()) {
                int result = (int) Math.floor((double) i / minPublishMount.get());
                queue.setPublishTime(new Timestamp(publishTime.getTime() + 60L * 1000 * result));
            } else {
                queue.setPublishTime(new Timestamp(publishTime.getTime() + publishIntervalTime.longValue() * 60 * 1000 * i));
            }
            queue.setUpdateTime(currentDate);
            queue.setTitle(null);
            queue.setExtra(null);
            // 是否由店铺自动刊登配置产生
            queue.setGeneratedByAutoPublish(1);
        }
        queueMapper.batchInsert(timePublishQueues);
        resp.setStatus(StatusCode.SUCCESS);
        return resp;
    }

    /**
     * 筛选合适的SPU进行刊登
     *
     * @param spuSet              SPU集合（输出参数）
     * @param fruugoAccountConfig Fruugo账号配置
     * @param spuToCodeMap        SPU到编码的映射
     * @param publishRole         刊登角色
     */
    public void findSpus(Set<String> spuSet, FruugoAccountConfig fruugoAccountConfig, Map<String, SkuListAndCode> spuToCodeMap, Integer publishRole) {
        String accountNumber = fruugoAccountConfig.getAccountNumber();
        List<String> codes = CommonUtils.splitList(fruugoAccountConfig.getProdCategoryCodes(), ",");
        Integer maxPublishNum = fruugoAccountConfig.getPublishQuantity();

        if (maxPublishNum == null) {
            maxPublishNum = 100; // 默认最大刊登数量
        }

        List<String> spuList = new ArrayList<>(spuToCodeMap.keySet());
        Collections.shuffle(spuList); // 随机打乱顺序

        for (String spu : spuList) {
            try {
                if (spuSet.size() >= maxPublishNum) {
                    // 达到最大刊登数量，停止筛选
                    break;
                }

                SkuListAndCode skuListAndCode = spuToCodeMap.get(spu);
                List<String> sonSkuList = skuListAndCode.getSkuList();
                String code = skuListAndCode.getCode();
                List<SonSkuFewInfo> sonSkuFewInfos = skuListAndCode.getSonSkuFewInfos();

                // 检查类目是否匹配店铺配置选择的刊登类目
                if (CollectionUtils.isNotEmpty(codes) && !codes.contains(code)) {
                    log.debug(String.format("自动刊登账号%s spu%s 类目不匹配%s", accountNumber, spu, code));
                    continue;
                }

                // 检查是否存在店铺在线列表已在线的SKU或等待刊登的队列中的SPU
                boolean exist = checkIsExistEsItemOrPublishQueue(accountNumber, spu, sonSkuList);
                if (exist) {
                    log.debug(String.format("自动刊登账号%s spu%s 已经存在店铺在线列表已在线的SKU或等待刊登的队列中的SPU", accountNumber, spu));
                    continue;
                }

                // 系统刊登时，过滤不满足店铺配置限制的spu
                if (PublishRoleEnum.ADMIN.getPublishRole() == publishRole) {
                    Boolean isFilterSpu = checkAccountRelationRestrict(fruugoAccountConfig, sonSkuFewInfos);
                    if (isFilterSpu) {
                        log.debug(String.format("自动刊登账号%s spu%s 不满足店铺配置限制条件,%s", accountNumber, spu, JSON.toJSONString(sonSkuFewInfos)));
                        continue;
                    }
                }

                // 过滤Fruugo禁售和停产、存档、废弃状态的产品
                List<ProductInfo> filteredProductInfos = filterForbiddenAndItemStatus(spu);

                if (CollectionUtils.isEmpty(filteredProductInfos)) {
                    log.debug(String.format("自动刊登账号%s spu%s 不满足过滤Fruugo禁售和停产禁售和停产、存档、废弃状态的产品不为空", accountNumber, spu));
                    continue;
                }

                // 可以刊登
                spuSet.add(spu);

            } catch (Exception e) {
                log.error(String.format("%s处理spu%s数据校验异常", accountNumber, spu), e);
            }
        }
    }


    /**
     * 检查是否存在店铺在线列表已在线的SKU或等待刊登的队列中的SPU
     */
    private boolean checkIsExistEsItemOrPublishQueue(String accountNumber, String spu, List<String> sonSkuList) {
        try {

            // 检查是否存在店铺在线列表已在线的SKU
            EsFruugoItemRequest esFruugoItemRequest = new EsFruugoItemRequest();
            esFruugoItemRequest.setSku(sonSkuList);
            esFruugoItemRequest.setAccountNumber(List.of(accountNumber));
            List<EsFruugoItem> existItems = esFruugoItemService.listItemByRequest(esFruugoItemRequest);
            if (CollectionUtils.isNotEmpty(existItems)) {
                return true;
            }

            // 检查是否已经存在等待刊登的队列
            FruugoTimePublishQueueExample example = new FruugoTimePublishQueueExample();
            example.createCriteria()
                    .andAccountNumberEqualTo(accountNumber)
                    .andArticleNumberEqualTo(spu)
                    .andStatusIn(List.of(FruugoTimePublishEnums.WAITING.getCode(), FruugoTimePublishEnums.PROCESSING.getCode()));
            List<FruugoTimePublishQueue> existingTemplates = queueMapper.selectByExample(example);
            return CollectionUtils.isNotEmpty(existingTemplates);

        } catch (Exception e) {
            log.error(String.format("检查账号%s SPU%s是否存在店铺在线列表已在线的SKU和等待刊登的队列中的SPU时异常", accountNumber, spu), e);
            return true; // 异常时认为已发布，避免重复刊登
        }
    }

    /**
     * 检查账号关联限制
     */
    private Boolean checkAccountRelationRestrict(FruugoAccountConfig fruugoAccountConfig, List<SonSkuFewInfo> sonSkuFewInfos) {
        if (CollectionUtils.isEmpty(sonSkuFewInfos)) {
            return false;
        }

        // 检查是否所有SKU都不满足店铺配置限制
        return sonSkuFewInfos.stream().noneMatch(sonSkuFewInfo ->
                satisfiesAccountRelationRestrict(fruugoAccountConfig, sonSkuFewInfo));
    }

    /**
     * 检查单个SKU是否满足账号关联限制
     */
    private boolean satisfiesAccountRelationRestrict(FruugoAccountConfig fruugoAccountConfig, SonSkuFewInfo sonSkuFewInfo) {
        // 检查排除标签限制
        if (!satisfiesExcludeLabelRestrict(fruugoAccountConfig.getExcludeLabel(), sonSkuFewInfo)) {
            return false;
        }

        // 检查重量限制
        if (!satisfiesWeightRestrict(fruugoAccountConfig.getFromWeight(), fruugoAccountConfig.getToWeight(), sonSkuFewInfo)) {
            return false;
        }

        // 检查销售成本价限制
        if (!satisfiesPriceRestrict(fruugoAccountConfig.getFromPrice(), fruugoAccountConfig.getToPrice(), sonSkuFewInfo)) {
            return false;
        }

        // 检查销量限制
        if (!satisfiesSalesRestrict(fruugoAccountConfig.getSalesType(), fruugoAccountConfig.getFromSales(),
                fruugoAccountConfig.getToSales(), sonSkuFewInfo)) {
            return false;
        }

        // 检查库存限制
        if (!satisfiesInventoryRestrict(fruugoAccountConfig.getFromInventory(), fruugoAccountConfig.getToInventory(), sonSkuFewInfo)) {
            return false;
        }


        // 检查产品录入时间限制
        if (!satisfiesInputTimeRestrict(fruugoAccountConfig.getSkuCreateTimeType(),
                fruugoAccountConfig.getFromInputTime(), fruugoAccountConfig.getToInputTime(),
                fruugoAccountConfig.getSkuCreateTimeYear(), sonSkuFewInfo)) {
            return false;
        }

        return true;
    }


    /**
     * 检查产品录入时间限制
     *
     * @param skuCreateTimeType SKU创建时间类型
     * @param fromInputTime     录入时间起始月份
     * @param toInputTime       录入时间结束月份
     * @param skuCreateTimeYear SKU创建年份
     * @param sonSkuFewInfo     子SKU信息
     * @return true-满足限制，false-不满足限制
     */
    private boolean satisfiesInputTimeRestrict(Integer skuCreateTimeType,
                                               Integer fromInputTime, Integer toInputTime, Integer skuCreateTimeYear, SonSkuFewInfo sonSkuFewInfo) {

        Long inSingleTime = sonSkuFewInfo.getInSingleTime();

        // 按月份限制
        if (skuCreateTimeType != null && skuCreateTimeType.intValue() == SkuCreateTimeTypeEnum.MONTH.getCode()
                && fromInputTime != null && toInputTime != null) {
            if (inSingleTime == null) {
                log.debug(String.format("sku%s 产品录入时间限制,进入单品时间为空", sonSkuFewInfo.getSonSku()));
                return false;
            }

            Date currentDate = new Date();
            Date lastTime = new Date(currentDate.getYear(), currentDate.getMonth(), currentDate.getDate(), 23, 59, 59);
            Date fromTime = DateUtils.addMonths(lastTime, -fromInputTime);
            Date zeroTime = new Date(currentDate.getYear(), currentDate.getMonth(), currentDate.getDate(), 0, 0, 0);
            Date toTime = DateUtils.addMonths(zeroTime, -toInputTime);

            return DateUtils.betweenStartTimeAndEndTime(new Date(inSingleTime), toTime, fromTime);
        }

        // 按年份限制
        if (skuCreateTimeType != null && skuCreateTimeType.intValue() == SkuCreateTimeTypeEnum.YEAR.getCode()
                && skuCreateTimeYear != null) {
            if (inSingleTime == null) {
                log.debug(String.format("sku%s 产品录入时间限制,进入单品时间为空", sonSkuFewInfo.getSonSku()));
                return false;
            }

            Date startDate = com.estone.erp.publish.common.util.DateUtils.getMinDateOfMonth(skuCreateTimeYear, 1);
            Date endDate = com.estone.erp.publish.common.util.DateUtils.getMaxDateOfMonth(skuCreateTimeYear, 12);
            Calendar calendar = Calendar.getInstance();
            int currentYear = calendar.get(Calendar.YEAR);
            if (skuCreateTimeYear == currentYear) {
                endDate = new Date();
            }

            return DateUtils.betweenStartTimeAndEndTime(new Date(inSingleTime), startDate, endDate);
        }

        return true;
    }

    /**
     * 检查库存限制
     * 起始区间与结束区间可以为空，区间为空认为条件成立
     * 当起始区间与结束区间都不为空时需都满足条件
     *
     * @param fromInventory 最小库存
     * @param toInventory   最大库存
     * @param sonSkuFewInfo 子SKU信息
     * @return true-满足限制，false-不满足限制
     */
    private boolean satisfiesInventoryRestrict(Integer fromInventory, Integer toInventory, SonSkuFewInfo sonSkuFewInfo) {
        // 如果两个区间都为空，认为条件成立
        if (fromInventory == null && toInventory == null) {
            return true;
        }

        Integer availableStock = sonSkuFewInfo.getAvailableStock();
        if (availableStock == null) {
            log.debug(String.format("sku%s 库存限制-库存为空", sonSkuFewInfo.getSonSku()));
            return false;
        }

        // 检查各个条件
        boolean satisfiesFromInventory = (fromInventory == null) || (availableStock >= fromInventory);
        boolean satisfiesToInventory = (toInventory == null) || (availableStock < toInventory);

        boolean isInRange = satisfiesFromInventory && satisfiesToInventory;

        if (!isInRange) {
            log.debug(String.format("sku%s 库存限制-不满足区间[%s-%s]，实际库存：%s",
                    sonSkuFewInfo.getSonSku(), fromInventory, toInventory, availableStock));
        }
        return isInRange;
    }


    /**
     * 检查销售成本价限制
     * 起始区间与结束区间可以为空，区间为空认为条件成立
     * 当起始区间与结束区间都不为空时需都满足条件
     *
     * @param fromPrice     最小价格
     * @param toPrice       最大价格
     * @param sonSkuFewInfo 子SKU信息
     * @return true-满足限制，false-不满足限制
     */
    private boolean satisfiesPriceRestrict(Double fromPrice, Double toPrice, SonSkuFewInfo sonSkuFewInfo) {
        Double price = sonSkuFewInfo.getSaleCost();
        if (price == null) {
            log.debug(String.format("sku%s 销售成本价限制-价格为空", sonSkuFewInfo.getSonSku()));
            return false;
        }

        // 检查各个条件
        boolean satisfiesFromPrice = (fromPrice == null) || (Double.doubleToLongBits(price) >= Double.doubleToLongBits(fromPrice));
        boolean satisfiesToPrice = (toPrice == null) || (Double.doubleToLongBits(price) < Double.doubleToLongBits(toPrice));

        boolean isInRange = satisfiesFromPrice && satisfiesToPrice;

        if (!isInRange) {
            log.debug(String.format("sku%s 销售成本价限制-不满足区间[%s-%s]，实际价格：%s",
                    sonSkuFewInfo.getSonSku(), fromPrice, toPrice, price));
        }
        return isInRange;
    }

    /**
     * 检查销量限制
     * 起始区间与结束区间可以为空，区间为空认为条件成立
     * 当起始区间与结束区间都不为空时需都满足条件
     *
     * @param salesType     销量类型
     * @param fromSales     最小销量
     * @param toSales       最大销量
     * @param sonSkuFewInfo 子SKU信息
     * @return true-满足限制，false-不满足限制
     */
    private boolean satisfiesSalesRestrict(Integer salesType, Integer fromSales, Integer toSales, SonSkuFewInfo sonSkuFewInfo) {
        // 如果销量类型为空，认为条件成立
        if (salesType == null) {
            return true;
        }

        // 如果两个区间都为空，认为条件成立
        if (fromSales == null && toSales == null) {
            return true;
        }

        // 获取对应销量类型的销量数据和描述
        SalesData salesData = getSalesDataByType(salesType, sonSkuFewInfo);
        if (salesData == null) {
            return true; // 未知销量类型，认为条件成立
        }

        // 验证销量区间
        return validateSalesRange(salesData, fromSales, toSales, sonSkuFewInfo.getSonSku());
    }

    /**
     * 根据销量类型获取对应的销量数据
     *
     * @param salesType     销量类型
     * @param sonSkuFewInfo 子SKU信息
     * @return 销量数据对象，包含销量值和描述
     */
    private SalesData getSalesDataByType(Integer salesType, SonSkuFewInfo sonSkuFewInfo) {
        if (SalesTypeEnum.ORDER_LAST_1D_COUNT.getCode() == salesType) {
            return new SalesData(sonSkuFewInfo.getOneSalesNum(), "24H销量");
        } else if (SalesTypeEnum.ORDER_LAST_7D_COUNT.getCode() == salesType) {
            return new SalesData(sonSkuFewInfo.getSevenSalesNum(), "7天销量");
        } else if (SalesTypeEnum.ORDER_LAST_14D_COUNT.getCode() == salesType) {
            return new SalesData(sonSkuFewInfo.getFourteenSalesNum(), "14天销量");
        } else if (SalesTypeEnum.ORDER_LAST_30D_COUNT.getCode() == salesType) {
            return new SalesData(sonSkuFewInfo.getThirtySalesNum(), "30天销量");
        } else if (SalesTypeEnum.ORDER_LAST_60D_COUNT.getCode() == salesType) {
            return new SalesData(sonSkuFewInfo.getSixtySalesNum(), "60天销量");
        } else if (SalesTypeEnum.ORDER_LAST_90D_COUNT.getCode() == salesType) {
            return new SalesData(sonSkuFewInfo.getNinetySalesNum(), "90天销量");
        }
        return null;
    }

    /**
     * 验证销量是否在指定区间内
     *
     * @param salesData 销量数据
     * @param fromSales 最小销量
     * @param toSales   最大销量
     * @param sonSku    子SKU编码
     * @return true-满足限制，false-不满足限制
     */
    private boolean validateSalesRange(SalesData salesData, Integer fromSales, Integer toSales, String sonSku) {
        Double salesNum = salesData.getSalesNum();
        String salesDesc = salesData.getSalesDesc();

        if (salesNum == null) {
            salesNum = 0d;
        }

        int salesIntNum = salesNum.intValue();
        log.debug(String.format("sku%s 指定销量区间[%s-%s]限制,%s：%s",
                sonSku, fromSales, toSales, salesDesc, salesNum));

        // 检查各个条件
        boolean satisfiesFromSales = (fromSales == null) || (salesIntNum >= fromSales);
        boolean satisfiesToSales = (toSales == null) || (salesIntNum < toSales);
        return satisfiesFromSales && satisfiesToSales;
    }

    /**
     * 销量数据内部类
     */
    private static class SalesData {
        private final Double salesNum;
        private final String salesDesc;

        public SalesData(Double salesNum, String salesDesc) {
            this.salesNum = salesNum;
            this.salesDesc = salesDesc;
        }

        public Double getSalesNum() {
            return salesNum;
        }

        public String getSalesDesc() {
            return salesDesc;
        }
    }


    /**
     * 检查排除标签限制
     *
     * @param excludeLabel  排除标签配置
     * @param sonSkuFewInfo 子SKU信息
     * @return true-满足限制，false-不满足限制
     */
    private boolean satisfiesExcludeLabelRestrict(String excludeLabel, SonSkuFewInfo sonSkuFewInfo) {
        if (org.apache.commons.lang3.StringUtils.isBlank(excludeLabel)) {
            return true;
        }

        if (org.apache.commons.lang3.StringUtils.isBlank(sonSkuFewInfo.getTag())) {
            return true;
        }

        List<String> excludeLabelList = com.estone.erp.publish.common.util.CommonUtils.splitList(excludeLabel, ",");
        List<String> tagList = com.estone.erp.publish.common.util.CommonUtils.splitList(sonSkuFewInfo.getTag(), ",");

        boolean hasExcludedTag = tagList.stream().anyMatch(excludeLabelList::contains);
        if (hasExcludedTag) {
            log.debug(String.format("sku%s 不满足店铺配置的排除标签", sonSkuFewInfo.getSonSku()));
            return false;
        }

        return true;
    }

    /**
     * 检查重量限制
     * 起始区间与结束区间可以为空，当满足一个条件时则条件成立，区间为空也认为条件成立
     *
     * @param fromWeight    最小重量
     * @param toWeight      最大重量
     * @param sonSkuFewInfo 子SKU信息
     * @return true-满足限制，false-不满足限制
     */
    private boolean satisfiesWeightRestrict(Double fromWeight, Double toWeight, SonSkuFewInfo sonSkuFewInfo) {
        // 取产品系统预估包裹重量，若预估包裹重量为空，则用SKU净重+包材重量+搭配包材重量+面单3g
        double weight = Optional.ofNullable(sonSkuFewInfo.getPackageWeight()).orElse(ProductUtils.calcSkuWeight(sonSkuFewInfo));
        // 检查各个条件
        boolean satisfiesFromWeight = (fromWeight == null) || (Double.doubleToLongBits(weight) >= Double.doubleToLongBits(fromWeight));
        boolean satisfiesToWeight = (toWeight == null) || (Double.doubleToLongBits(weight) < Double.doubleToLongBits(toWeight));

        boolean isInRange = satisfiesFromWeight && satisfiesToWeight;

        if (!isInRange) {
            log.debug(String.format("spu%s 重量限制-不满足区间[%s-%s]，实际重量：%s",
                    sonSkuFewInfo.getSonSku(), fromWeight, toWeight, weight));
        }
        return isInRange;

    }
}
