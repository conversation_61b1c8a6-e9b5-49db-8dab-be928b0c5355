package com.estone.erp.publish.fruugo.model;

import com.estone.erp.common.annotation.NeedToLog;
import lombok.Data;

import java.io.Serializable;
import java.sql.Time;
import java.sql.Timestamp;

@Data
public class FruugoAccountConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id database column fruugo_account_config.id
     */
    private Integer id;

    /**
     * 店铺名称 database column fruugo_account_config.account_number
     */
    private String accountNumber;

    /**
     * 店铺状态 database column fruugo_account_config.status
     */
    private Integer status;

    /**
     * 修改库存规则 database column fruugo_account_config.update_stock_rule_code
     */
    @NeedToLog
    private String updateStockRuleCode;

    /**
     * 创建时间 database column fruugo_account_config.created_time
     */
    private Timestamp createdTime;

    /**
     * 最后修改人 database column fruugo_account_config.last_update_by
     */
    private String lastUpdateBy;

    /**
     * 修改时间 database column fruugo_account_config.updated_time
     */
    private Timestamp updatedTime;

    /**
     * 同步时间 database column fruugo_account_config.sync_time
     */
    private Timestamp syncTime;

    /**
     * 店铺后缀 database column fruugo_account_config.account_site
     */
    @NeedToLog
    private String accountSite;

    /**
     * EAN 前缀 database column fruugo_account_config.ean_prefix
     */
    @NeedToLog
    private String eanPrefix;

    /**
     * 制造商名称 database column fruugo_account_config.manufacturer
     */
    @NeedToLog
    private String manufacturer;

    /**
     * 品牌名称 database column fruugo_account_config.brand
     */
    @NeedToLog
    private String brand;

    /**
     * 默认库存 database column fruugo_account_config.default_stock
     */
    @NeedToLog
    private Integer defaultStock;

    /**
     * 毛利率 database column fruugo_account_config.profit_margin
     */
    @NeedToLog
    private Double profitMargin;

    /**
     * 平台费率 database column fruugo_account_config.platform_rate
     */
    @NeedToLog
    private Double platformRate;

    /**
     * 刊登币种 database column fruugo_account_config.currency
     */
    @NeedToLog
    private String currency;

    /**
     * 准备时间 database column fruugo_account_config.ready_time
     */
    @NeedToLog
    private Integer readyTime;

    /**
     * 产品系统叶子类目code，多个逗号拼接 database column fruugo_account_config.prod_category_codes
     */
    @NeedToLog
    private String prodCategoryCodes;

    /**
     * 产品系统二级三级类目名json database column fruugo_account_config.prod_category_names
     */
    @NeedToLog
    private String prodCategoryNames;

    /**
     * 排除产品标签 database column fruugo_account_config.exclude_label
     */
    @NeedToLog
    private String excludeLabel;

    /**
     * 产品重量限制区间>= database column fruugo_account_config.from_weight
     */
    @NeedToLog
    private Double fromWeight;

    /**
     * 产品重量限制区间< database column fruugo_account_config.to_weight
     */
    @NeedToLog
    private Double toWeight;

    /**
     * 销量类型：1为24H销量 2为7天销量 3为14天销量 4为30天销量 5为60天销量 6为90天销量 database column fruugo_account_config.sales_type
     */
    @NeedToLog
    private Integer salesType;

    /**
     * 指定销量区间>= database column fruugo_account_config.from_sales
     */
    @NeedToLog
    private Integer fromSales;

    /**
     * 指定销量区间< database column fruugo_account_config.to_sales
     */
    @NeedToLog
    private Integer toSales;

    /**
     * 最近月份：产品录入时间区间（当前时间月份减配置时间 sku_create_time_type 值为1），时间倒推 database column fruugo_account_config.from_input_time
     */
    @NeedToLog
    private Integer fromInputTime;

    /**
     * 最近月份：产品录入时间区间，值大于from_input_time，时间倒推更小（当前时间月份减配置时间 sku_create_time_type 值为1） database column fruugo_account_config.to_input_time
     */
    @NeedToLog
    private Integer toInputTime;

    /**
     * sku录入时间类型 1：月份 2：年份 database column fruugo_account_config.sku_create_time_type
     */
    @NeedToLog
    private Integer skuCreateTimeType;

    /**
     * sku录入时间年份( sku_create_time_type 值为2) database column fruugo_account_config.sku_create_time_year
     */
    @NeedToLog
    private Integer skuCreateTimeYear;

    /**
     * 销售成本价区间>= database column fruugo_account_config.from_price
     */
    @NeedToLog
    private Double fromPrice;

    /**
     * 销售成本价区间< database column fruugo_account_config.to_price
     */
    @NeedToLog
    private Double toPrice;

    /**
     * 库存限制区间>= database column fruugo_account_config.from_inventory
     */
    @NeedToLog
    private Integer fromInventory;

    /**
     * 库存限制区间< database column fruugo_account_config.to_inventory
     */
    @NeedToLog
    private Integer toInventory;

    /**
     * 每天可刊登最大产品数量 database column fruugo_account_config.publish_quantity
     */
    @NeedToLog
    private Integer publishQuantity;

    /**
     * 每分钟刊登数量 database column fruugo_account_config.min_publish_mount
     */
    @NeedToLog
    private Integer minPublishMount;

    /**
     * 自动刊登时间（每天几点） database column fruugo_account_config.publish_time
     */
    @NeedToLog
    private Time publishTime;

    /**
     * 自动刊登间隔时间（分钟） database column fruugo_account_config.publish_interval_time
     */
    @NeedToLog
    private Integer publishIntervalTime;

    /**
     * 自动上架新品 database column fruugo_account_config.auto_publish_new
     */
    @NeedToLog
    private Boolean autoPublishNew;
}