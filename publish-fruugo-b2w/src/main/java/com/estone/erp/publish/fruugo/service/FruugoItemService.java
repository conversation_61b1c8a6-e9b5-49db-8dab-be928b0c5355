package com.estone.erp.publish.fruugo.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.elasticsearch.model.EsFruugoItem;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.fruugo.call.model.request.FruugoProductResponse;
import com.estone.erp.publish.fruugo.model.dto.*;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;

import java.util.List;
import java.util.Map;

public interface FruugoItemService {

    /**
     * 通过账号和sku查询商品信息
     * @param skuList
     * @param account
     * @return
     */
    Map<String, EsFruugoItem> findSkuByAccount(String productId,List<String> skuList, String account);

    void syncItemInfo(SaleAccount account, List<EsFruugoItem> itemByXml);

    void syncItemInfos(SaleAccount account, List<String> itemIds);

    void updateInventory(FruugoUpdateDto fruugoUpdateDto, SaleAccount accountNumber);

    void batchUpdateInventory(SaleAccount account, List<EsFruugoItem> fruugoItemList, Integer newInventory);

    ApiResult<String> batchUpdateInventory(List<FruugoUpdateDto> fruugoItemList);

    List<FeedTask>  executeDeactivateOperation(SaleAccount saleAccount, List<FeedTask> feedTasks, List<EsFruugoItem> itemList);

    ApiResult<String> batchUpdateInventory(List<FruugoUpdateDto> fruugoItemList, String accountNumber);

    ApiResult<Map<String, Integer>> checkUpdateStock(List<String> ids);

    void sysncItemByNewAccount(String account, FruugoProductResponse fruugoProductResponse);

    List<FruugoBatchCalculatePriceDetailDto> batchCalculatePrice(FruugoBatchCalculatePriceDto dto);

    void batchUpdatePrice(List<FruugoBatchUpdateInfoDto> dto);

    void sysncItemByNewAccountAndSpu(FruugoSyncAccountAndSpuDto dto);

    void syncItemByAccount(List<String> accountList);


    /**
     *
     * 修改制造商和品牌信息
     * @param account
     * @param fruugoItemList
     */
    void updateManufacturerAndBrand(SaleAccount account,String manufacturer,String brand, List<EsFruugoItem> fruugoItemList);


    /**
     * 修改商品属性根据类目
     * @param saleAccount
     * @param esFruugoItemList
     */
    void updateAttributesByCategory(SaleAccount saleAccount, List<EsFruugoItem> esFruugoItemList);


    /**
     * 同步商品描述
     * @param saleAccountByAccountNumber
     * @param esFruugoItemList
     */
    void sysncItemText(SaleAccountAndBusinessResponse saleAccountByAccountNumber, List<EsFruugoItem> esFruugoItemList);


    /**
     * 修改商品属性
     * @param saleAccount
     * @param esFruugoItemList
     * @param dto
     */
    void updateAttributes(SaleAccount saleAccount, List<EsFruugoItem> esFruugoItemList, FruugoBatchUpdateAttributeDto dto);

    /**
     * 获取店铺信息
     * @param accountNumber
     * @return
     */
    SaleAccount getAccount(String accountNumber);

    /**
     * 批量勾选修改制造商和品牌信息
     * @param dto
     */
    void batchUpdateManufactureAndBrand(FruugoBatchUpdateManufactureAndBrandDto dto);

    /**
     * 批量勾选修改商品属性
     * @param dto
     */
    void batchUpdateAttribute(FruugoBatchUpdateAttributeDto dto);


    /***
     * 根据商品productId和账号查询商品信息
     * @param productId
     * @param account
     * @return
     */
     Map<String, EsFruugoItem> findProductMapByAccountAndProductId(List<String> productId, String account);


    /**
     * 批量删除下架商品
     * @param idList
     */
    void batchDeactivateOrDelete(List<String> idList);

    void updateItems(List<EsFruugoItem> itemList);

    ApiResult<String> batchUpdateDiscountPrice(List<FruugoUpdateDiscountPriceDto> updateItemList);
}
