package com.estone.erp.publish.fruugo.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.common.enums.PublishRoleEnum;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.fruugo.compont.FruugoAutoPublishHelper;
import com.estone.erp.publish.fruugo.enums.FruugoSkuCreateTimeTypeEnum;
import com.estone.erp.publish.fruugo.model.FruugoAccountConfig;
import com.estone.erp.publish.fruugo.model.FruugoAccountConfigExample;
import com.estone.erp.publish.fruugo.service.FruugoAccountConfigService;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SkuListAndCode;
import com.estone.erp.publish.system.product.request.ProductNewSpuRequest;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;


/**
 * ES-12745【Fruugo】自动刊登产品定时任务
 * <p>
 * 自动刊登产品定时任务
 * 筛选是否刊登产品为是的店铺，根据店铺配置的自动刊登配置规则,选中的SPU生成队列等待刊登
 * 先选择店铺:
 * 选择店铺配置:是否刊登产品为是的店铺
 * 再选择产品
 * 根据店铺自动刊登配置的规则,均为且关系
 * 备注:选中的产品过滤以下产品
 * 1-过滤非店铺配置选择的刊登类目的产品
 * 2-过滤Fruugo禁售的SPU
 * 3-过滤单品状态为停产,存档,废弃的产品
 * 4-过滤重复,店铺在线列表已在线的SKU和等待刊登的队列中的SPU，结束状态的队列才允许重新生成，随机从以上选中的数据每天最大刊登数量X,按照定时刊登上架时间确定刊登开始时间,按照上架时间间隔或每分钟刊登SPU数量，来生成队列等待刊登。
 * 注：排除产品标签、产品重量限制，需过滤到子SKU维度，刊登时需再次校验排除产品标签、产品重量限制区间、是否符合配置，若不符合则不进行刊登，自动刊登时需走刊登的校验和拦截逻辑
 */
@Slf4j
@Component
public class FruugoAutoPublishNewJobHandler extends AbstractJobHandler {

    private static final String timePattern = "yyyy-MM-dd";


    /**
     * 产品系统最早录入时间
     */
    private static final String PRODUCT_EARLY_TIME = DateUtils.formatMinDateTime("2019-09-19");

    /**
     * 产品系统最早录入年份
     */
    private static final int PRODUCT_EARLY_YEAR = 2019;

    @Resource
    private FruugoAccountConfigService fruugoAccountConfigService;

    @Resource
    private FruugoAutoPublishHelper fruugoAutoPublishHelper;

    public FruugoAutoPublishNewJobHandler() {
        super(FruugoAutoPublishNewJobHandler.class.getName());
    }

    /**
     * 内部参数类
     */
    @Data
    public static class InnerParams {
        /**
         * 指定店铺账号列表
         */
        private List<String> accountNumbers;

    }

    @Override
    @XxlJob("FruugoAutoPublishNewJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("Fruugo自动刊登START");
        InnerParams innerParams = passParam(param, InnerParams.class);

        // 查询符合条件的Fruugo店铺配置
        FruugoAccountConfigExample example = new FruugoAccountConfigExample();
        FruugoAccountConfigExample.Criteria criteria = example.createCriteria()
                .andAutoPublishNewEqualTo(true)  // 开启自动刊登新品
                .andStatusEqualTo(Integer.valueOf(SaleAccountStastusEnum.NORMAL.getCode()))  // 店铺状态正常
                .andSkuCreateTimeTypeIsNotNull();  // 配置了SKU录入时间类型

        if (CollectionUtils.isNotEmpty(innerParams.getAccountNumbers())) {
            criteria.andAccountNumberIn(innerParams.getAccountNumbers());
        }

        // 查询店铺配置
        List<FruugoAccountConfig> accountConfigs = fruugoAccountConfigService.selectByExample(example);
        if (CollectionUtils.isEmpty(accountConfigs)) {
            XxlJobLogger.log("没有需要自动上架产品的Fruugo账号");
            log.error("没有需要自动上架产品的Fruugo账号");
            return ReturnT.SUCCESS;
        }


        // 刊登产品录入时间范围计算，然后根据可刊登范围分组调用产品系统接口
        Date time = new Date();
        // 当前年
        int nowYear = Calendar.getInstance().get(Calendar.YEAR);
        Map<Integer, List<FruugoAccountConfig>> yearAccountConfigsMap = new HashMap<>();
        Map<String, List<FruugoAccountConfig>> monthAccountConfigsMap = new HashMap<>();
        Map<Integer, Set<String>> yearKeyProductRangeMap = new HashMap<>();
        Map<String, Set<String>> monthKeyProductRangeMap = new HashMap<>();

        for (FruugoAccountConfig accountConfig : accountConfigs) {
            XxlJobLogger.log("开始执行店铺：{}", accountConfig.getAccountNumber());

            if (accountConfig.getSkuCreateTimeType().equals(FruugoSkuCreateTimeTypeEnum.YEAR.getCode())) {
                // 年
                Integer year = accountConfig.getSkuCreateTimeYear();
                if (year == null || year < PRODUCT_EARLY_YEAR || year > nowYear) {
                    // 大于当前年 或者小于产品系统最早录入年份不用刊登
                    XxlJobLogger.log(year + " 大于当前年 或者小于产品系统最早录入年份不用刊登: " + accountConfig.getAccountNumber());
                    continue;
                }

                yearAccountConfigsMap.computeIfAbsent(year, k -> new ArrayList<>()).add(accountConfig);

            } else if (accountConfig.getSkuCreateTimeType().equals(FruugoSkuCreateTimeTypeEnum.MONTH.getCode())) {
                //月
                String startMonthTime = com.estone.erp.publish.common.util.DateUtils.dateToString(com.estone.erp.common.util.DateUtils.addMonths(time, -accountConfig.getToInputTime()));
                String endMonthTime = com.estone.erp.publish.common.util.DateUtils.dateToString(com.estone.erp.common.util.DateUtils.addMonths(time, -accountConfig.getFromInputTime()));
                String monthKey = startMonthTime + "_to_" + endMonthTime;
                XxlJobLogger.log("accountNumber:{}， month:{}", accountConfig.getAccountNumber(), monthKey);
                monthAccountConfigsMap.computeIfAbsent(monthKey, k -> new ArrayList<>()).add(accountConfig);
            }
        }

        // 处理时间范围
        Map<String, SearchProductTimeRange> searchProductTimeMap = handleSearchProductTime(yearAccountConfigsMap.keySet(), yearKeyProductRangeMap, monthAccountConfigsMap.keySet(), monthKeyProductRangeMap);

        // 处理月份配置的店铺
        Map<String, Map<String, SkuListAndCode>> rangeMonthAndSpuToCodeMap = handleMonthSearchProductSpu(
                monthKeyProductRangeMap, searchProductTimeMap);

        for (String key : monthAccountConfigsMap.keySet()) {
            Map<String, SkuListAndCode> spuToCodeMap = new HashMap<>();
            for (String range : monthKeyProductRangeMap.get(key)) {
                spuToCodeMap.putAll(rangeMonthAndSpuToCodeMap.get(range));
            }

            // 处理产品信息
            spuToCodeMap = fruugoAutoPublishHelper.getSpuToCodeMapForNewProduct(spuToCodeMap);

            // 创建队列
            fruugoAutoPublishHelper.createTimingQueue(spuToCodeMap,
                    monthAccountConfigsMap.get(key), PublishRoleEnum.ADMIN.getPublishRole());
        }

        // 处理年份配置的店铺
        if (MapUtils.isEmpty(yearAccountConfigsMap)) {
            XxlJobLogger.log("Fruugo自动刊登END");
            return ReturnT.SUCCESS;
        }

        Map<Integer, Map<String, SkuListAndCode>> rangeYearAndSpuToCodeMap = handleYearSearchProductSpu(
                yearKeyProductRangeMap, searchProductTimeMap, rangeMonthAndSpuToCodeMap);
        for (Integer key : yearAccountConfigsMap.keySet()) {
            Map<String, SkuListAndCode> spuToCodeMap = rangeYearAndSpuToCodeMap.get(key);

            // 处理产品信息
            spuToCodeMap = fruugoAutoPublishHelper.getSpuToCodeMapForNewProduct(spuToCodeMap);

            // 创建队列
            fruugoAutoPublishHelper.createTimingQueue(spuToCodeMap,
                    yearAccountConfigsMap.get(key), PublishRoleEnum.ADMIN.getPublishRole());
        }

        XxlJobLogger.log("Fruugo自动刊登END");
        return ReturnT.SUCCESS;
    }

    /**
     * 搜索产品时间范围类
     */
    @Getter
    @Setter
    public static class SearchProductTimeRange {
        private String beginTime;
        private String endTime;
    }

    /**
     * 处理月份搜索产品SPU
     */
    private Map<String, Map<String, SkuListAndCode>> handleMonthSearchProductSpu(
            Map<String, Set<String>> monthKeyProductRangeMap,
            Map<String, SearchProductTimeRange> searchProductTimeMap) {

        Map<String, Map<String, SkuListAndCode>> rangeMonthAndSpuToCodeMap = new HashMap<>();

        if (!MapUtils.isEmpty(monthKeyProductRangeMap)) {
            for (String key : monthKeyProductRangeMap.keySet()) {
                for (String range : monthKeyProductRangeMap.get(key)) {
                    SearchProductTimeRange searchProductTimeRange = searchProductTimeMap.get(range);
                    Map<String, SkuListAndCode> spuToCodeMap = new HashMap<>();
                    String stringDateBegin = searchProductTimeRange.getBeginTime();
                    String endTime = searchProductTimeRange.getEndTime();

                    ProductNewSpuRequest request = new ProductNewSpuRequest();
                    request.setBeginTime(stringDateBegin);
                    request.setEndTime(endTime);
                    ResponseJson rsp = ProductUtils.getNewProductBySingItem(request);

                    if (!rsp.isSuccess()) {
                        XxlJobLogger.log(String.format("获取开始时间：%s，结束时间：%s 新品spu失败:", stringDateBegin, endTime) + rsp.getMessage());
                        continue;
                    }

                    spuToCodeMap = (Map<String, SkuListAndCode>) rsp.getBody().get(ProductUtils.resultKey);
                    if (MapUtils.isEmpty(spuToCodeMap)) {
                        XxlJobLogger.log(stringDateBegin + "----" + endTime + "没有已编辑的spu");
                        XxlJobLogger.log(JSON.toJSONString(rsp));
                    }

                    rangeMonthAndSpuToCodeMap.put(range, spuToCodeMap);
                }
            }
        }

        return rangeMonthAndSpuToCodeMap;
    }

    /**
     * 处理年份搜索产品SPU
     */
    private Map<Integer, Map<String, SkuListAndCode>> handleYearSearchProductSpu(
            Map<Integer, Set<String>> yearKeyProductRangeMap,
            Map<String, SearchProductTimeRange> searchProductTimeMap,
            Map<String, Map<String, SkuListAndCode>> rangeMonthAndSpuToCodeMap) {

        // 当前年
        int nowYear = Calendar.getInstance().get(Calendar.YEAR);
        Map<Integer, Map<String, SkuListAndCode>> rangeYearAndSpuToCodeMap = new HashMap<>();

        if (MapUtils.isEmpty(yearKeyProductRangeMap)) {
            XxlJobLogger.log("查询产品系统接口年范围是空，请检查：" + JSON.toJSONString(yearKeyProductRangeMap.keySet()));
        } else {
            for (Integer key : yearKeyProductRangeMap.keySet()) {
                Map<String, SkuListAndCode> skuListAndCodeMap = new HashMap<>();
                boolean isExistSkuMap = key.intValue() == nowYear;

                for (String range : yearKeyProductRangeMap.get(key)) {
                    SearchProductTimeRange searchProductTimeRange = searchProductTimeMap.get(range);
                    Map<String, SkuListAndCode> spuToCodeMap = new HashMap<>();

                    if (isExistSkuMap && rangeMonthAndSpuToCodeMap.containsKey(range)) {
                        spuToCodeMap = rangeMonthAndSpuToCodeMap.get(range);
                    } else {
                        String stringDateBegin = searchProductTimeRange.getBeginTime();
                        String endTime = searchProductTimeRange.getEndTime();

                        ProductNewSpuRequest request = new ProductNewSpuRequest();
                        request.setBeginTime(stringDateBegin);
                        request.setEndTime(endTime);
                        ResponseJson rsp = ProductUtils.getNewProductBySingItem(request);

                        if (!rsp.isSuccess()) {
                            XxlJobLogger.log(String.format("获取开始时间：%s，结束时间：%s 新品spu失败:", stringDateBegin, endTime) + rsp.getMessage());
                            continue;
                        }

                        spuToCodeMap = (Map<String, SkuListAndCode>) rsp.getBody().get(ProductUtils.resultKey);
                        if (MapUtils.isEmpty(spuToCodeMap)) {
                            XxlJobLogger.log(stringDateBegin + "----" + endTime + "没有已编辑的spu");
                            XxlJobLogger.log(JSON.toJSONString(rsp));
                        }
                    }

                    skuListAndCodeMap.putAll(spuToCodeMap);
                }

                rangeYearAndSpuToCodeMap.put(key, skuListAndCodeMap);
            }
        }

        return rangeYearAndSpuToCodeMap;
    }

    /**
     * 处理时间范围
     *
     * @param yearSet
     * @param yearKeyProductRangeMap
     * @param monthSet
     * @param monthKeyProductRangeMap
     * @return
     */
    private Map<String, SearchProductTimeRange> handleSearchProductTime(Set<Integer> yearSet, Map<Integer, Set<String>> yearKeyProductRangeMap, Set<String> monthSet, Map<String, Set<String>> monthKeyProductRangeMap) {
        Map<String, SearchProductTimeRange> searchProductTimeMap = new HashMap<>();

        // 当前年，2019 年需要特殊处理
        // 优先处理月
        for (String monthRange : monthSet) {
            Set<String> rangeSet = new HashSet<>();
            String startKey = StringUtils.substringBefore(monthRange, "_to_");
            String endKey = StringUtils.substringAfter(monthRange, "_to_");
            Date rangeBeginDate = DateUtils.stringToDate(startKey, timePattern);
            Date rangeEndDate = DateUtils.stringToDate(endKey, timePattern);
            int betweenMonths = com.estone.erp.common.util.DateUtils.betweenMonths(rangeBeginDate, rangeEndDate);
            int j = 0;
            while (betweenMonths > 1) {
                Date startDate = com.estone.erp.common.util.DateUtils.addMonths(rangeEndDate, -(j + 1));
                Date endDate = j > 0 ? com.estone.erp.common.util.DateUtils.addMonths(rangeEndDate, -j) : rangeEndDate;
                String key = DateUtils.dateToString(startDate) + "_to_" + DateUtils.dateToString(endDate);
                rangeSet.add(key);
                SearchProductTimeRange searchProductTimeRange = new SearchProductTimeRange();
                searchProductTimeRange.setBeginTime(DateUtils.formatMinDateTime(DateUtils.dateToString(startDate, timePattern)));
                searchProductTimeRange.setEndTime(DateUtils.formatMaxDateTime(DateUtils.dateToString(endDate, timePattern)));
                searchProductTimeMap.put(key, searchProductTimeRange);
                betweenMonths--;
                j++;
            }
            monthKeyProductRangeMap.put(monthRange, rangeSet);
        }

        for (Integer yearInt : yearSet) {
            Set<String> rangeSet = new HashSet<>();
            Calendar calendar = Calendar.getInstance();
            int currentYear = calendar.get(Calendar.YEAR);
            if (yearInt.intValue() == currentYear) {
                Date time = new Date();
                int i = calendar.get(Calendar.MONTH);
                int j = 0;
                Date currentYearMinTime = DateUtils.getFirstDayOfMonth(currentYear, 1);
                while (i >= 0) {
                    Date startDate = com.estone.erp.common.util.DateUtils.addMonths(time, -(j + 1));
                    Date endDate = j > 0 ? com.estone.erp.common.util.DateUtils.addMonths(time, -j) : time;
                    if (startDate.before(currentYearMinTime)) {
                        startDate = currentYearMinTime;
                    }
                    String key = DateUtils.dateToString(startDate) + "_to_" + DateUtils.dateToString(endDate);
                    SearchProductTimeRange searchProductTimeRange = new SearchProductTimeRange();
                    searchProductTimeRange.setBeginTime(DateUtils.formatMinDateTime(DateUtils.dateToString(startDate, timePattern)));
                    searchProductTimeRange.setEndTime(DateUtils.formatMaxDateTime(DateUtils.dateToString(endDate, timePattern)));
                    searchProductTimeMap.put(key, searchProductTimeRange);
                    rangeSet.add(key);
                    i--;
                    j++;
                }

            } else {
                int i = 1;
                String beginTime = null;
                if (yearInt.intValue() == PRODUCT_EARLY_YEAR) {
                    // 2019
                    beginTime = PRODUCT_EARLY_TIME;
                    i = 9;
                }
                while (i <= 12) {
                    Date startDate = DateUtils.getMinDateOfMonth(yearInt, i);
                    Date endDate = DateUtils.getMaxDateOfMonth(yearInt, i);
                    String key = DateUtils.dateToString(startDate) + "_to_" + DateUtils.dateToString(endDate);
                    SearchProductTimeRange searchProductTimeRange = new SearchProductTimeRange();
                    searchProductTimeRange.setBeginTime((StringUtils.isNotBlank(beginTime) && i == 9) ? PRODUCT_EARLY_TIME : DateUtils.formatMinDateTime(DateUtils.dateToString(startDate, timePattern)));
                    searchProductTimeRange.setEndTime(DateUtils.formatMaxDateTime(DateUtils.dateToString(endDate, timePattern)));
                    searchProductTimeMap.put(key, searchProductTimeRange);
                    rangeSet.add(key);
                    i++;
                }
            }
            yearKeyProductRangeMap.put(yearInt, rangeSet);
        }
        return searchProductTimeMap;
    }

}
