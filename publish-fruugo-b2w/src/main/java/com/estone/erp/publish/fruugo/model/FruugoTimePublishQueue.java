package com.estone.erp.publish.fruugo.model;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class FruugoTimePublishQueue implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    private Integer id;

    /**
     * 模板ID
     */
    private Integer templateId;

    /**
     * 店铺
     */
    private String accountNumber;

    /**
     * 数据来源
     */
    private Integer skuDataSource;

    /**
     * 货号
     */
    private String articleNumber;

    /**
     * 标题
     */
    private String title;

    /**
     * 状态 1 等待中、2 暂停中、3 处理中、4 结束
     */
    private Integer status;

    /**
     * 刊登状态 对应模板刊登状态
     */
    private Integer publishStatus;

    /**
     * 扩展数据
     */
    private String extra;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Timestamp createdTime;

    /**
     * 刊登时间
     */
    private Timestamp publishTime;

    /**
     * 修改时间
     */
    private Timestamp updateTime;

    /**
     * 是否由店铺自动刊登配置产生，0为否，1为是
     */
    private Integer generatedByAutoPublish;
}