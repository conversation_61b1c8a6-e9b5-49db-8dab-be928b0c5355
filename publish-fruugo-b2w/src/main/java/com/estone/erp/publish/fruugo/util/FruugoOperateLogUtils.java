package com.estone.erp.publish.fruugo.util;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.enums.SalesTypeEnum;
import com.estone.erp.publish.common.enums.SkuCreateTimeTypeEnum;
import com.estone.erp.publish.fruugo.enums.FruugoLogFieldEnum;
import com.estone.erp.publish.fruugo.enums.FruugoOperateLogEnum;
import com.estone.erp.publish.fruugo.model.FruugoOperateLog;
import com.estone.erp.publish.fruugo.model.vo.FruugoAccountConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Fruugo操作日志工具类
 *
 * <AUTHOR>
 * @date 2023/08/06
 * @description 参考AmazonOperateLogUtils实现的Fruugo日志工具类
 */
@Slf4j
public class FruugoOperateLogUtils {

    /**
     * 忽略字段
     */
    private static List<String> filterKey = Arrays.asList("createdBy", "creationDate", "syncTime", "accountCountry",
            "accountStatus", "createTime", "createDate", "updateBy", "updatedBy", "lastUpdatedBy", "updateTime", "updateDate",
            "lastUpdateDate", "amazonSellerSkuRuleList", "accountLevel", "prodCategoryNames", "fromPrice",
            "toPrice", "lastUpdateBy", "createBy", "amazonCalcPriceRuleList", "amazonShippingCostModelList",
            "labelList", "isCompleteConfig", "onlineItemNum", "fromWeight", "toWeight", "salesType", "fromSales",
            "toSales", "fromInventory", "toInventory", "fromInputTime", "toInputTime", "productCategoryNames",
            "prodLevel2CategoryNameList", "attributeNameRoute", "attributeNameType", "updatedTime", "createdTime",
            "authBrand", "recordBrand", "titleRule", "accountOfflineConfig", "manufacturer", "manufacturerAddress",
            "manufacturerCn", "manufacturerEmail", "manufacturerEn", "manufacturerTel", "merchantId");
    /**
     * 空值标识
     */
    private static final String NULL_VALUE = "null";

    /**
     * 产品分类代码字段名
     */
    private static final String PROD_CATEGORY_CODES = "prodCategoryCodes";

    /**
     * 全选分类标识
     */
    private static final String SELECT_ALL_CATEGORY = "all";
    /**
     * 分类
     */
    private static final String CATEGORY_CODE = "prodCategoryCodes";

    /**
     * ProductTypeTemplate 分类
     */
    private static final String PRODUCT_TYPE_CATEGORY_CODE = "productCategoryCodes";
    /**
     * 构建更新操作日志
     *
     * @param oldBean        旧对象
     * @param newBean        新对象
     * @param operateLogEnum 操作日志枚举
     * @param businessId     业务ID字段名
     * @return 操作日志列表
     */
    public static List<FruugoOperateLog> buildUpdateLog(Object oldBean, Object newBean,
                                                        FruugoOperateLogEnum operateLogEnum, String businessId) {
        return buildUpdateLog(oldBean, newBean, operateLogEnum, businessId, filterKey::contains);
    }

    /**
     * 构建更新操作日志（带字段过滤）
     *
     * @param oldBean              旧对象
     * @param newBean              新对象
     * @param operateLogEnum       操作日志枚举
     * @param businessId           业务ID字段名
     * @param filterFiledPredicate 字段过滤条件
     * @return 操作日志列表
     */
    public static List<FruugoOperateLog> buildUpdateLog(Object oldBean, Object newBean,
                                                        FruugoOperateLogEnum operateLogEnum, String businessId,
                                                        Predicate<String> filterFiledPredicate) {
        if (oldBean == null || newBean == null) {
            return new ArrayList<>();
        }

        Map<String, String> oldFieldMap = getFieldMap(oldBean, false, businessId);
        // 新对象忽略null 取的是传入修改的数据 null 数据库忽略修改
        Map<String, String> newFieldMap = getFieldMap(newBean, true, businessId);

        List<FruugoOperateLog> fruugoOperateLogList = new ArrayList<>();
        for (Map.Entry<String, String> entry : oldFieldMap.entrySet()) {
            String key = entry.getKey();
            // 忽略的字段不比较 新对象不存在 为null忽略
            if (filterFiledPredicate.test(key)) {
                continue;
            }

            // 相等不记录日志
            if (entry.getValue().equals(newFieldMap.get(key))) {
                continue;
            }

            // 为空也会保存，均为空不修改
            if (isEmpty(entry.getValue()) && isEmpty(newFieldMap.get(key))) {
                continue;
            }

            // 记录修改日志
            FruugoOperateLog fruugoOperateLog = new FruugoOperateLog();
            fruugoOperateLogList.add(fruugoOperateLog);

            if (StringUtils.isNotBlank(businessId)) {
                try {
                    String businessIdValue = oldFieldMap.get(businessId);
                    fruugoOperateLog.setBusinessId(Integer.valueOf(businessIdValue));
                } catch (Exception e) {
                    log.warn("设置业务ID失败: {}", e.getMessage());
                }
            }

            String accountNumberKey = "accountNumber";
            fruugoOperateLog.setAccountNumber(newFieldMap.get(accountNumberKey));
            fruugoOperateLog.setType(operateLogEnum.name());
            fruugoOperateLog.setFieldName(key);
            fruugoOperateLog.setBefore(entry.getValue());
            fruugoOperateLog.setAfter(newFieldMap.get(key));
            fruugoOperateLog.setCreateBy(WebUtils.getUserName());
            fruugoOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
        }
        return fruugoOperateLogList;
    }

    /**
     * 构建新增操作日志
     *
     * @param bean           新增对象
     * @param operateLogEnum 操作日志枚举
     * @param businessId     业务ID
     * @return 操作日志
     */
    public static FruugoOperateLog buildAddLog(Object bean, FruugoOperateLogEnum operateLogEnum, Integer businessId) {
        FruugoOperateLog fruugoOperateLog = new FruugoOperateLog();
        fruugoOperateLog.setType(operateLogEnum.name());
        fruugoOperateLog.setBusinessId(businessId);
        fruugoOperateLog.setFieldName("新增配置");
        fruugoOperateLog.setBefore("");
        fruugoOperateLog.setAfter("新增");
        fruugoOperateLog.setMessage(JSON.toJSONString(bean));
        fruugoOperateLog.setCreateBy(WebUtils.getUserName());
        fruugoOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
        return fruugoOperateLog;
    }

    /**
     * 构建删除操作日志
     *
     * @param bean           删除对象
     * @param operateLogEnum 操作日志枚举
     * @param businessId     业务ID
     * @return 操作日志
     */
    public static FruugoOperateLog buildDelLog(Object bean, FruugoOperateLogEnum operateLogEnum, Integer businessId) {
        FruugoOperateLog fruugoOperateLog = new FruugoOperateLog();
        fruugoOperateLog.setType(operateLogEnum.name());
        fruugoOperateLog.setBusinessId(businessId);
        fruugoOperateLog.setFieldName("删除配置");
        fruugoOperateLog.setBefore("删除");
        fruugoOperateLog.setAfter("");
        fruugoOperateLog.setMessage(JSON.toJSONString(bean));
        fruugoOperateLog.setCreateBy(WebUtils.getUserName());
        fruugoOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
        return fruugoOperateLog;
    }

    private static FruugoOperateLog generateSessionLog(String oldFromValue, String oldToValue, String newFromValue, String newToValue, Integer id, String type) {
        if (!org.apache.commons.lang.StringUtils.equalsIgnoreCase(oldFromValue, newFromValue)
                || !org.apache.commons.lang.StringUtils.equalsIgnoreCase(oldToValue, newToValue)) {
            FruugoOperateLog fruugoOperateLog = new FruugoOperateLog();
            fruugoOperateLog.setType(FruugoOperateLogEnum.account_config.name());
            fruugoOperateLog.setBusinessId(id);
            fruugoOperateLog.setFieldName(type);
            if (NULL_VALUE.equals(oldFromValue) && NULL_VALUE.equals(oldToValue)) {
                fruugoOperateLog.setBefore("新增");
            } else {
                fruugoOperateLog.setBefore(oldFromValue + "——" + oldToValue);
            }
            if (NULL_VALUE.equals(newFromValue) && NULL_VALUE.equals(newToValue)) {
                fruugoOperateLog.setAfter("删除");
            } else {
                fruugoOperateLog.setAfter(newFromValue + "——" + newToValue);
            }
            fruugoOperateLog.setCreateBy(WebUtils.getUserName());
            fruugoOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));

            return fruugoOperateLog;
        }

        return null;
    }
    /**
     * 处理店铺配置区间字段日志
     *
     * @param oldValue 旧配置
     * @param newValue 新配置
     * @return 区间字段日志列表
     */
    public static List<FruugoOperateLog> handleSectionLog(FruugoAccountConfigVO oldValue, FruugoAccountConfigVO newValue) {
        List<FruugoOperateLog> fruugoOperateLogs = new ArrayList<>();

        // 产品重量限制区间
        Double oldFromWeight = oldValue.getFromWeight();
        Double oldToWeight = oldValue.getToWeight();
        Double newFromWeight = newValue.getFromWeight();
        Double newToWeight = newValue.getToWeight();
        FruugoOperateLog log1 = generateSessionLog(String.valueOf(oldFromWeight), String.valueOf(oldToWeight), String.valueOf(newFromWeight),
                String.valueOf(newToWeight), newValue.getId(), FruugoLogFieldEnum.PRODUCT_WEIGHT.getFieldEn());
        if (null != log1) {
            fruugoOperateLogs.add(log1);
        }

        // 销售成本价区间
        Double oldFromPrice = oldValue.getFromPrice();
        Double oldToPrice = oldValue.getToPrice();
        Double newFromPrice = newValue.getFromPrice();
        Double newToPrice = newValue.getToPrice();
        FruugoOperateLog log2 = generateSessionLog(String.valueOf(oldFromPrice), String.valueOf(oldToPrice), String.valueOf(newFromPrice),
                String.valueOf(newToPrice), newValue.getId(), FruugoLogFieldEnum.SALE_PRICE.getFieldEn());
        if (null != log2) {
            fruugoOperateLogs.add(log2);
        }

        // 库存限制区间
        Integer oldFromInventory = oldValue.getFromInventory();
        Integer oldToInventory = oldValue.getToInventory();
        Integer newFromInventory = newValue.getFromInventory();
        Integer newToInventory = newValue.getToInventory();
        FruugoOperateLog log3 = generateSessionLog(String.valueOf(oldFromInventory), String.valueOf(oldToInventory), String.valueOf(newFromInventory),
                String.valueOf(newToInventory), newValue.getId(), FruugoLogFieldEnum.PRODUCT_INVENTORY.getFieldEn());
        if (null != log3) {
            fruugoOperateLogs.add(log3);
        }

        // 产品录入时间区间
        Integer oldSkuCreateTimeType = oldValue.getSkuCreateTimeType();
        Integer newSkuCreateTimeType = newValue.getSkuCreateTimeType();
        if (SkuCreateTimeTypeEnum.MONTH.getCode().equals(oldSkuCreateTimeType) || SkuCreateTimeTypeEnum.MONTH.getCode().equals(newSkuCreateTimeType)) {
            Integer oldFromInputTime = oldValue.getFromInputTime();
            Integer oldToInputTime = oldValue.getToInputTime();
            Integer newFromInputTime = newValue.getFromInputTime();
            Integer newToInputTime = newValue.getToInputTime();
            FruugoOperateLog log4 = generateSessionLog(String.valueOf(oldFromInputTime), String.valueOf(oldToInputTime), String.valueOf(newFromInputTime),
                    String.valueOf(newToInputTime), newValue.getId(), FruugoLogFieldEnum.PRODUCT_INPUT_TIME.getFieldEn());
            if (null != log4) {
                fruugoOperateLogs.add(log4);
            }
        }

        // 指定销量区间
        Integer oldFromSales = oldValue.getFromSales();
        Integer oldToSales = oldValue.getToSales();
        Integer newFromSales = newValue.getFromSales();
        Integer newToSales = newValue.getToSales();
        Integer oldSalesType = oldValue.getSalesType();
        Integer newSalesType = newValue.getSalesType();
        if (!org.apache.commons.lang.StringUtils.equalsIgnoreCase(String.valueOf(oldFromSales), String.valueOf(newFromSales))
                || !org.apache.commons.lang.StringUtils.equalsIgnoreCase(String.valueOf(oldToSales), String.valueOf(newToSales))) {
            FruugoOperateLog fruugoOperateLog = new FruugoOperateLog();
            fruugoOperateLog.setType(FruugoOperateLogEnum.account_config.name());
            fruugoOperateLog.setBusinessId(newValue.getId());
            fruugoOperateLog.setFieldName(FruugoLogFieldEnum.SALES_SECTION.getFieldEn());
            if (null == oldFromSales && null == oldToSales && null == oldSalesType) {
                fruugoOperateLog.setBefore("新增");
            } else {
                fruugoOperateLog.setBefore((oldSalesType == null ? "" : SalesTypeEnum.getNameByCode(oldSalesType) + "：") + oldFromSales + "——" + oldToSales);
            }
            if (null == newFromSales && null == newToSales && null == newSalesType) {
                fruugoOperateLog.setAfter("删除");
            } else {
                fruugoOperateLog.setAfter((newSalesType == null ? "" : SalesTypeEnum.getNameByCode(newSalesType) + "：") + newFromSales + "——" + newToSales);
            }
            fruugoOperateLog.setCreateBy(WebUtils.getUserName());
            fruugoOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));

            fruugoOperateLogs.add(fruugoOperateLog);
        }

        return fruugoOperateLogs;
    }

    /**
     * 处理特殊日志
     *
     * @param configLogs 配置日志列表
     */
    public static void tranSpecialLog(List<FruugoOperateLog> configLogs) {
        if (CollectionUtils.isEmpty(configLogs)) {
            return;
        }

        Iterator<FruugoOperateLog> it = configLogs.iterator();
        while (it.hasNext()) {
            try {
                FruugoOperateLog operateLog = it.next();
                String fieldName = operateLog.getFieldName();
                String after = operateLog.getAfter();

                if (CATEGORY_CODE.equals(fieldName) && !SELECT_ALL_CATEGORY.equals(after)) {
                    changeCategoryLog(operateLog);
                    // 可能只是字符顺序发生了变化 实际未改变
                    if (org.apache.commons.lang.StringUtils.isBlank(operateLog.getBefore()) && org.apache.commons.lang.StringUtils.isBlank(operateLog.getAfter())) {
                        it.remove();
                    }
                } else if (PRODUCT_TYPE_CATEGORY_CODE.equals(fieldName) && !SELECT_ALL_CATEGORY.equals(after)) {
                    changeCategoryLog(operateLog);
                    // 可能只是字符顺序发生了变化 实际未改变
                    if (org.apache.commons.lang.StringUtils.isBlank(operateLog.getBefore()) && org.apache.commons.lang.StringUtils.isBlank(operateLog.getAfter())) {
                        it.remove();
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 获取对象字段映射
     *
     * @param bean       对象
     * @param ignoreNull 是否忽略null值
     * @param businessId 业务ID字段名
     * @return 字段映射
     */
    private static Map<String, String> getFieldMap(Object bean, boolean ignoreNull, String businessId) {
        Map<String, String> fieldMap = new HashMap<>();
        List<Field> fieldsList = FieldUtils.getAllFieldsList(bean.getClass());
        for (Field field : fieldsList) {
            if (filterKey.contains(field.getName())) {
                continue;
            }

            try {
                field.setAccessible(true);
                Object val = field.get(bean);

                if (ignoreNull && val == null) {
                    continue;
                }

                fieldMap.put(field.getName(), val == null ? "" : val.toString());
            } catch (Exception e) {
                log.error("获取字段值失败: {}", e.getMessage(), e);
            }
        }
        return fieldMap;
    }

    /**
     * 判断对象是否为空
     *
     * @param obj 对象
     * @return 是否为空
     */
    public static boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        }
        if (obj instanceof List) {
            return ((List<?>) obj).isEmpty();
        }
        if (obj instanceof String) {
            return ((String) obj).trim().isEmpty();
        }
        return false;
    }

    /**
     * 分类日志特殊处理
     *
     * @param operateLog 操作日志
     */
    private static void changeCategoryLog(FruugoOperateLog operateLog) {
        String before = operateLog.getBefore();
        List<String> beforeCategoryIds = splitList(before, ",");

        String after = operateLog.getAfter();
        List<String> afterCategoryIds = splitList(after, ",");

        // 之前有 且不存在修改之后中则为删除
        List<String> delCategoryIds = beforeCategoryIds.stream()
                .filter(o -> !afterCategoryIds.contains(o)).collect(Collectors.toList());

        // 之前无 且存在修改之后中则为新增
        List<String> addCategoryIds = afterCategoryIds.stream()
                .filter(o -> !beforeCategoryIds.contains(o)).collect(Collectors.toList());

        List<String> afterArr = new ArrayList<>();
        StringBuilder delStringBuffer = new StringBuilder();

        if (CollectionUtils.isNotEmpty(delCategoryIds)) {
            delStringBuffer.append("删除：").append(String.join(",", delCategoryIds));
            String delStr = delStringBuffer.toString();
            afterArr.add(delStr);
        }

        StringBuilder addStringBuffer = new StringBuilder();
        if (CollectionUtils.isNotEmpty(addCategoryIds)) {
            addStringBuffer.append("增加：").append(String.join(",", addCategoryIds));
            String addStr = addStringBuffer.toString();
            afterArr.add(addStr);
        }

        operateLog.setBefore(before);
        operateLog.setAfter(CollectionUtils.isNotEmpty(afterArr) ? JSON.toJSONString(afterArr) : "");
    }

    /**
     * 分割字符串为列表
     *
     * @param str       字符串
     * @param delimiter 分隔符
     * @return 字符串列表
     */
    private static List<String> splitList(String str, String delimiter) {
        if (StringUtils.isBlank(str)) {
            return new ArrayList<>();
        }
        return Arrays.stream(str.split(delimiter))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }
}
