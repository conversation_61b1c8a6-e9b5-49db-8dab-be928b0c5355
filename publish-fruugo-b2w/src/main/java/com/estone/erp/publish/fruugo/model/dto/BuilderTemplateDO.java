package com.estone.erp.publish.fruugo.model.dto;

import lombok.*;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BuilderTemplateDO {

    /**
     * 发布类型
     */
    private Integer publishType;

    /**
     * 店铺
     */
    private String accountNumber;

    /**
     * 货号
     */
    private String articleNumber;

    /**
     * 数据来源
     */
    private Integer skuDataSource;

    /**
     * 类目：优先Admin范本,再类目映射
     */
    private Integer categoryId;

    /**
     * 模板id
     */
    private Integer templateId;

    /**
     * 是否由店铺自动刊登配置产生，0为否，1为是
     */
    private Integer generatedByAutoPublish;
}