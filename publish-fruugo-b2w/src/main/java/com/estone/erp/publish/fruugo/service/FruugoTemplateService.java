package com.estone.erp.publish.fruugo.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.fruugo.model.FruugoAccountConfig;
import com.estone.erp.publish.fruugo.model.FruugoTemplate;
import com.estone.erp.publish.fruugo.model.FruugoTemplateCriteria;
import com.estone.erp.publish.fruugo.model.FruugoTemplateExample;
import com.estone.erp.publish.fruugo.model.dto.BuilderTemplateDO;
import com.estone.erp.publish.fruugo.model.dto.FruugoBatchGenerateEanNumDto;
import com.estone.erp.publish.fruugo.model.dto.FruugoBatchPublishDto;
import com.estone.erp.publish.fruugo.model.dto.FruugoTemplateEnableDto;
import com.estone.erp.publish.fruugo.model.vo.FruugoTemPlatePageVO;
import com.estone.erp.publish.fruugo.model.vo.FruugoTemplateInfoVO;
import com.estone.erp.publish.fruugo.mq.param.FruugoPlatformMqMessage;
import com.estone.erp.publish.system.pmssalePublicData.model.PublishSpuModelDO;
import com.estone.erp.publish.system.pmssalePublicData.model.PublishSpuModelRequest;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.response.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 2024-11-20 11:28:30
 */
public interface FruugoTemplateService {
    int countByExample(FruugoTemplateExample example);

    CQueryResult<FruugoTemPlatePageVO> search(CQuery<FruugoTemplateCriteria> cquery);

    FruugoTemplateInfoVO getById(Integer id, Boolean isParent);

    List<FruugoTemplate> selectByExample(FruugoTemplateExample example);

    FruugoTemplate selectByPrimaryKey(Integer id, Boolean isParent);

    int insert(FruugoTemplate record,String tableName);

    int updateByPrimaryKeySelective(FruugoTemplate record,String tableName);

    int updateByExampleSelective(FruugoTemplate record, FruugoTemplateExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    FruugoTemplate save(FruugoTemplateInfoVO templateInfoVO, String tableName);

    void copyTemplateByPrimaryKey(List<Integer> ids);

    List<String> checkExitAdminTemplate(List<String> spuList);

    FruugoTemplateInfoVO getTemplateInfoVO(BuilderTemplateDO builderTemplateDO);

    String getPrice(String tags, FruugoAccountConfig fruugoAccountConfig, String sku, Double weight, BigDecimal cost);

    void enableFruugoTemplate(FruugoTemplateEnableDto dto);

    void checkTemplateInfo(FruugoTemplateInfoVO templateInfoVO);

    ApiResult<?> saveAndPublish(FruugoTemplateInfoVO templateInfoVO);

    void replaceImage(FruugoTemplateInfoVO templateInfoVO);

    void updatePublishStatus(List<Integer> ids, boolean isSuccess, String result);

    Map<String, List<String>> uploadTemplateImage(MultipartFile[] files, String articleNumber);

    Map<String, String> generateEAN(FruugoBatchGenerateEanNumDto dto);

    ApiResult<PageResult<PublishSpuModelDO>> getPublishSpuModelPage(PublishSpuModelRequest request);

    void batchPublish(FruugoBatchPublishDto dto);

    List<Integer> listIdByStatusAndId(List<Integer> ids, List<Integer> code);


    void checkExitOnlineList(FruugoTemplateInfoVO templateInfoVO);

    void dealSaveProductErrorMsg(FruugoPlatformMqMessage msg, String body);

    void dealSaveProductMsg(FruugoPlatformMqMessage msg, String body);

    void dealUpdateErrorProductMsg(FruugoPlatformMqMessage msg, String body);

    //修改准备时间
    void updateReadyTime(String accountNumber, List<FruugoTemplate> fruugoTemplateList, Integer readyTime);

    /**
     * 过滤停产、废弃、存档状态SKU，过滤froougo平台禁售的SKU
     *
     * @param articleNumber
     * @return
     */
    List<ProductInfo> findProductInfo(String articleNumber);
}