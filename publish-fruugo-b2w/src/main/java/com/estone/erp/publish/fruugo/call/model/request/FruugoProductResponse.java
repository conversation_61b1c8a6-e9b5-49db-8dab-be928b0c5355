package com.estone.erp.publish.fruugo.call.model.request;

import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class FruugoProductResponse {

    private Map<String, Product> products;

    @Data
    public static class Product {
        private ProductInfo product;
        private Map<String, Sku> skus;
        private Errors errors;
    }

    @Data
    public static class ProductInfo {
        private String productId;
        private String brand;
        private String manufacturer;
        private String category;
    }

    @Data
    public static class Sku {
        private SkuDetails skuDetails;
    }

    @Data
    public static class SkuDetails {
        private String skuId;
        private List<Gtin> gtins;
        private SkuDescription details;
        private SupplyInfo supplyInfo;
        private List<PricingInfo> pricingInfo;
        private Double packageWeight;
        private Double volume;
        private String status;
    }

    @Data
    public static class Gtin {
        private String codeType;
        private String code;
    }

    @Data
    public static class SkuDescription {
        private List<SkuDescriptionItem> skuDescriptions;
        private List<Media> media;
    }

    @Data
    public static class SkuDescriptionItem {
        private String language;
        private String title;
        private String text;
        private List<Attribute> attributes;
    }

    @Data
    public static class Attribute {
        private String name;
        private String value;
    }

    @Data
    public static class Media {
        private String description;
        private String type;
    }

    @Data
    public static class SupplyInfo {
        private String stockStatus;
        private Integer stockQuantity;
        private Integer leadTime;
    }

    @Data
    public static class PricingInfo {
        private String currency;
        private NormalPrice normalPrice;
        private DiscountPrice discountPrice;
    }

    @Data
    public static class NormalPrice {
        private Double price;
        private Boolean vatInclusive;
    }

    @Data
    public static class Errors {
        private Map<String, SkuError> skus;
        private String error;
    }

    @Data
    public static class SkuError {
        private String error;
    }

    @Data
    public static class DiscountPrice {
        private Double price;
        private Boolean vatInclusive;
        private String startDate;
        private String endDate;
    }
}
