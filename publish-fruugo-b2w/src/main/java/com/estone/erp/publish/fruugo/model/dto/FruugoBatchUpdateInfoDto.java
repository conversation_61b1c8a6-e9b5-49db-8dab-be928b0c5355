package com.estone.erp.publish.fruugo.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Accessors(chain = true)
@Data
public class FruugoBatchUpdateInfoDto {

    private String id;
    private String account;
    private String newProductId;
    private String sku;
    private String sellerSku;
    private String beforePrice;
    private Double afterPrice;
    private String status;

    private String currency;
    private Boolean taxInclusive;
    private Integer inventory;

    private String manufacturer;
    private String brand;

    /**
     * 库存状态
     */
    private String stockStatus;

    /**
     * 准备时间
     */
    private Integer readyTimeAttribute;

    /**
     * 属性
     */
    private List<Attribute> attributes;

    /**
     * 描述语言的ISO代码
     */
    private String language;

    /**
     * 产品的标题
     */
    private String title;

    /**
     * 产品的详细描述
     */
    private String text;

    /**
     *促销价格
     */
    private Double discountPrice;

    /**
     * 促销价格是否含税，0-否，1-是
     */
    private Boolean discountVatInclusive;

    /**
     * 促销开始时间,格式：YYYY-MM-DD
     */
    private String discountStartDate;

    /**
     * 促销结束时间,格式：YYYY-MM-DD
     */
    private String discountEndDate;


    @Data
    public static class Attribute {
        private String name;
        private String value;
    }

}
