package com.estone.erp.publish.fruugo.model.vo;

import com.estone.erp.publish.fruugo.model.FruugoAccountConfig;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class FruugoAccountConfigVO extends FruugoAccountConfig {


    /**
     * 产品系统二级分类名
     */
    private List<String> prodLevel2CategoryNameList;

    public List<String> getProdLevel2CategoryNameList() {
        if (StringUtils.isNotEmpty(this.getProdCategoryNames())) {
            prodLevel2CategoryNameList = new ArrayList<>();
            String[] prodLevel2CategoryNames = this.getProdCategoryNames().split("@@@");
            for (String prodCategoryName : prodLevel2CategoryNames) {
                prodLevel2CategoryNameList.add(prodCategoryName.contains(">") ? StringUtils.substringBefore(prodCategoryName, ">") : prodCategoryName);
            }
        }
        if (CollectionUtils.isNotEmpty(prodLevel2CategoryNameList)) {
            prodLevel2CategoryNameList = prodLevel2CategoryNameList.stream().distinct().collect(Collectors.toList());
        }
        return prodLevel2CategoryNameList;
    }
    // 待补充下个版本的下架队列
}
