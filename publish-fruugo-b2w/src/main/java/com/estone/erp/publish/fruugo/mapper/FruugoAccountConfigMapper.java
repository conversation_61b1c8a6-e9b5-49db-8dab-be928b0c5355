package com.estone.erp.publish.fruugo.mapper;

import com.estone.erp.publish.fruugo.model.FruugoAccountConfig;
import com.estone.erp.publish.fruugo.model.FruugoAccountConfigExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FruugoAccountConfigMapper {
    int countByExample(FruugoAccountConfigExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    int insert(FruugoAccountConfig record);

    FruugoAccountConfig selectByPrimaryKey(Integer id);

    List<FruugoAccountConfig> selectByExample(FruugoAccountConfigExample example);

    int updateByExampleSelective(@Param("record") FruugoAccountConfig record, @Param("example") FruugoAccountConfigExample example);

    int updateByPrimaryKeySelective(FruugoAccountConfig record);

    /**
     * 更新账号信息
     * @param record 账号配置对象
     * @return 更新记录数
     */
    int updateAccountInfoByExample(FruugoAccountConfig record);

    FruugoAccountConfig selectByAccountNumber(String account);
}