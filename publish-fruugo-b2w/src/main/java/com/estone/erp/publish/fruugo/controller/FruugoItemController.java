package com.estone.erp.publish.fruugo.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.FruugoExecutors;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch.model.EsFruugoItem;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsFruugoItemRequest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.service.EsFruugoItemService;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.fruugo.model.dto.*;
import com.estone.erp.publish.fruugo.service.FruugoItemService;
import com.estone.erp.publish.fruugo.service.FruugoSyncItemService;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/fruugoItem")
public class FruugoItemController {


    @Autowired
    FruugoItemService fruugoItemService;

    @Autowired
    private FruugoSyncItemService fruugoSyncItemService;

    @Resource
    private SaleAccountService saleAccountService;

    @Autowired
    private EsFruugoItemService esFruugoItemService;

    @Resource
    private PermissionsHelper permissionsHelper;
    /**
     * 批量下架
     */
    @PostMapping("/batchDeactivateOrDelete")
    public ApiResult<String> batchDeactivateOrDelete(@RequestBody List<String> idList) {
        fruugoItemService.batchDeactivateOrDelete(idList);
        return ApiResult.newSuccess("开始下架，请在操作报告查看结果");
    }


    /**
     * 批量修改属性：ce标识，安全警告，原材料
     */
    @PostMapping("/batchUpdateAttribute")
    public ApiResult<?> batchUpdateAttribute(@RequestBody FruugoBatchUpdateAttributeDto dto) {
        fruugoItemService.batchUpdateAttribute(dto);
        return ApiResult.newSuccess("开始修改，请在操作报告查看结果");
    }


    /**
     * 批量修改品牌和制造商
     * @param dto
     * @return
     */
    @PostMapping("/batchUpdateManufactureAndBrand")
    public ApiResult<?> batchUpdateManufactureAndBrand(@RequestBody FruugoBatchUpdateManufactureAndBrandDto dto) {
        fruugoItemService.batchUpdateManufactureAndBrand(dto);
        return ApiResult.newSuccess("开始修改，请在操作报告查看结果");
    }

    @PostMapping("/syncItemByAccount")
    public ApiResult<?> syncItemByAccount(@RequestBody List<String> accountList) {
        fruugoItemService.syncItemByAccount(accountList);
        return ApiResult.newSuccess("开始同步，请在操作报告查看结果");
    }

    /**
     * 通过productID和店铺同步数据
     */
    @PostMapping("/syncAccountAndSpu")
    public ApiResult<?> syncAccountAndSpu(@RequestBody FruugoSyncAccountAndSpuDto dto) {
        fruugoItemService.sysncItemByNewAccountAndSpu(dto);
        return ApiResult.newSuccess("开始同步，请在操作报告查看结果");
    }


    /**
     * 批量计算价格
     *
     * @param dto
     * @return
     */
    @PostMapping("/batchCalculatePrice")
    public ApiResult<List<FruugoBatchCalculatePriceDetailDto>> batchCalculatePrice(@RequestBody FruugoBatchCalculatePriceDto dto) {
        List<FruugoBatchCalculatePriceDetailDto> result = fruugoItemService.batchCalculatePrice(dto);
        return ApiResult.newSuccess(result);
    }


    /**
     * 批量修改价格
     */
    @PostMapping("/batchUpdatePrice")
    public ApiResult<?> batchUpdatePrice(@RequestBody List<FruugoBatchUpdateInfoDto> dto) {
        fruugoItemService.batchUpdatePrice(dto);
        return ApiResult.newSuccess();
    }

    /**
     * 批量修改折扣
     */
    @PostMapping("/batchUpdateDiscountPrice")
    public ApiResult<?> batchUpdateDiscountPrice(@RequestBody List<FruugoUpdateDiscountPriceDto> dto) {
        return fruugoItemService.batchUpdateDiscountPrice(dto);
    }

    /**
     * 同步所有账号
     *
     * @return
     */
    @GetMapping("/syncAll")
    public ApiResult<?> syncAll() {
        /*权限控制----start*/
//        ApiResult<Boolean> superAdminOrSupervisor = NewUsermgtUtils.isSuperAdminOrSupervisor(SaleChannel.CHANNEL_FRUUGO);
//        if (!superAdminOrSupervisor.isSuccess()) {
//            return ApiResult.newError(superAdminOrSupervisor.getErrorMsg());
//        }
//        if (!superAdminOrSupervisor.getResult()) {
//            return ApiResult.newError("该账号暂无全量同步去权限！");
//        }
        /*权限控制----end*/
//        String syncKey = FruugoRedisConstant.syncKey;
//        String syncDate = RedisUtils.get(syncKey);
//        if (StringUtils.isNotBlank(syncDate)) {
//            return ApiResult.newError(String.format("一小时内只能同步一次全量账号，请在 " + syncDate + " 之后重试"));
//        } else {
//            //下一次同步时间
//            Long syncDateNext = System.currentTimeMillis() + FruugoRedisConstant.oneHour;
//            //插入Redis
//            RedisUtils.set(syncKey, DateUtil.formatDateTime(new Date(syncDateNext)), FruugoRedisConstant.oneHour, TimeUnit.MILLISECONDS);
//        }
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(SaleChannel.CHANNEL_FRUUGO);
        List<SaleAccount> accountInfoList = saleAccountService.getAccountInfoList(request);
        for (SaleAccount account : accountInfoList) {
            FruugoExecutors.executeSyncProduct(() -> {
                if (account != null) {
                    if (ObjectUtils.isNotEmpty(account.getColBool2()) && account.getColBool2()) {
                        fruugoSyncItemService.sysncItemByNewAccountAndDate(account.getAccountNumber(), null);
                    } else {
                        fruugoSyncItemService.syncItemByAccount(account);
                    }
                }
            });
        }
        return ApiResult.newSuccess("后台已开始同步店铺产品，请到处理报告中查看结果");
    }

    /**
     * 同步账号
     *
     * @return
     */
    @PostMapping("/syncAccount")
    public ApiResult<?> syncAccount(@RequestBody ApiRequestParam<String> requestParam) {
        CQuery<EsFruugoItemRequest> cquery = requestParam.getArgsValue(new TypeReference<CQuery<EsFruugoItemRequest>>() {
        });
        EsFruugoItemRequest esFruugoItemRequest = cquery.getSearch();
        if (CollectionUtils.isEmpty(esFruugoItemRequest.getAccountNumber())) {
            return ApiResult.newError("请选择店铺同步");
        }
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(SaleChannel.CHANNEL_FRUUGO);
        request.setAccountNumberList(esFruugoItemRequest.getAccountNumber());
        List<SaleAccount> accountInfoList = saleAccountService.getAccountInfoList(request);
        for (SaleAccount account : accountInfoList) {
            FruugoExecutors.executeSyncProduct(() -> {
                if (account != null) {
                    fruugoSyncItemService.syncItemByAccount(account);
                }
            });
        }
        return ApiResult.newSuccess("后台已开始同步店铺产品，请到处理报告中查看结果");
    }

    /**
     * 修改库存
     *
     * @return
     */
    @PostMapping("/updateInventory")
    public ApiResult<String> updateInventory(@RequestBody ApiRequestParam<String> requestParam) {
        CQuery<List<FruugoUpdateDto>> cquery = requestParam.getArgsValue(new TypeReference<CQuery<List<FruugoUpdateDto>>>() {
        });
        return fruugoItemService.batchUpdateInventory(cquery.getSearch());
    }

    /**
     * 查询在线列表
     *
     * @param requestParam
     * @return
     */
    @PostMapping("/getItemPage")
    public ApiResult<?> getItemPage(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        CQuery<EsFruugoItemRequest> cquery = requestParam.getArgsValue(new TypeReference<CQuery<EsFruugoItemRequest>>() {
        });
        Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
        EsFruugoItemRequest esFruugoItemRequest = cquery.getSearch();
        Asserts.isTrue(esFruugoItemRequest != null, ErrorCode.PARAM_EMPTY_ERROR);

        ApiResult<String> permissionResult = checkAuth(esFruugoItemRequest);
        esFruugoItemRequest.setSalesman(null);
        if (!permissionResult.isSuccess()) {
            return permissionResult;
        }

        // 页面大小和起始页
        int pageSize = cquery.getLimit();
        int pageIndex = cquery.getOffset();

        // 查询esesFruugoItemService
        Page<EsFruugoItem> allPage = esFruugoItemService.page(esFruugoItemRequest, pageSize, pageIndex);

        List<EsFruugoItem> esFruugoItemList = allPage.getContent();
        handelPageExtend(esFruugoItemList);

        CQueryResult<EsFruugoItem> result = new CQueryResult<>();
        result.setTotal(allPage.getTotalElements());
        result.setTotalPages(allPage.getTotalPages());
        result.setRows(esFruugoItemList);
        return ApiResult.newSuccess(result);
    }


    @GetMapping("downloadField")
    private ApiResult<Map<String, String>> downloadField() {
        Map<String, String> resultMap = Maps.newLinkedHashMap();
        Class<EsFruugoItem> esFruugoItemClass = EsFruugoItem.class;
        Field[] declaredFields = esFruugoItemClass.getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (null != excelProperty) {
                String[] value = excelProperty.value();
                resultMap.put(field.getName(), value[0]);
            }
        }
        return ApiResult.newSuccess(resultMap);
    }

    @PostMapping("download")
    private void downloadExcel(@RequestBody ApiRequestParam<String> requestParam, HttpServletResponse response) throws Exception {
        CQuery<EsFruugoItemRequest> cquery = requestParam.getArgsValue(new TypeReference<CQuery<EsFruugoItemRequest>>() {
        });
        Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
        EsFruugoItemRequest esFruugoItemRequest = cquery.getSearch();
        Asserts.isTrue(esFruugoItemRequest != null, ErrorCode.PARAM_EMPTY_ERROR);

        ApiResult<String> permissionResult = checkAuth(esFruugoItemRequest);
        if (!permissionResult.isSuccess()) {
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(ApiResult.newError(permissionResult.getErrorMsg())));
            return;
        }

        // 下载数量限制
        int downloadLimitSize = 500000;
        List<String> downloadFiles = esFruugoItemRequest.getDownloadFiles();
        ExcelWriter excelWriter = null;
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
        int pageIndex = 0;
        int pageSize = 10000;
        long total = 0;
        do {
            // 分页去数据库查询数据 这里可以去数据库查询每一页的数据
            Page<EsFruugoItem> allPage = esFruugoItemService.page(esFruugoItemRequest, pageSize, pageIndex);
            if (CollectionUtils.isEmpty(allPage.getContent())) {
                break;
            }
            if (allPage.getTotalElements() > downloadLimitSize) {
                total = allPage.getTotalElements();
                break;
            }
            if (excelWriter == null) {
                excelWriter = EasyExcel.write(response.getOutputStream(), EsFruugoItem.class)
                        .includeColumnFiledNames(downloadFiles)
                        .build();
            }
            List<EsFruugoItem> content = allPage.getContent();
            if (CollectionUtils.isNotEmpty(downloadFiles) && downloadFiles.contains("salesman")) {
                List<String> accountNumberList = content.stream().map(EsFruugoItem::getAccountNumber).distinct().collect(Collectors.toList());
                Map<String, Set<String>> salesmanAccountMap = EsAccountUtils.getSalesmanAccountMapByEs(accountNumberList, SaleChannel.CHANNEL_FRUUGO);
                for (EsFruugoItem esFruugoItem : content) {
                    Set<String> saleNames = salesmanAccountMap.get(esFruugoItem.getAccountNumber());
                    esFruugoItem.setSalesman(CollectionUtils.isEmpty(saleNames) ? "" : new ArrayList<>(saleNames).get(0));
                }
            }
            excelWriter.write(content, writeSheet);
            pageIndex++;
            total += content.size();
        } while (total < 500000);
        if (total == 0) {
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(ApiResult.newError("当前筛选条件没有符合条件的结果")));
            return;
        }
        if (total > downloadLimitSize) {
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(ApiResult.newError(String.format("导出数据超过最大%s行", downloadLimitSize))));
            return;
        }

        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("monitorListing" + LocalDateTime.now(), "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        excelWriter.finish();
    }

    @PostMapping("checkUpdateStock")
    public ApiResult<Map<String, Integer>> checkUpdateStock(@RequestBody List<String> ids) {
        return fruugoItemService.checkUpdateStock(ids);
    }

    private ApiResult<String> checkAuth(EsFruugoItemRequest esFruugoItemRequest) {

        List<String> currentUserPermission = permissionsHelper.getCurrentUserPermission(esFruugoItemRequest.getAccountNumber(), null, null,
                esFruugoItemRequest.getSalesman(), SaleChannel.CHANNEL_FRUUGO, true, "0");
        esFruugoItemRequest.setSalesman(null);
        esFruugoItemRequest.setAccountNumber(currentUserPermission);
//        // 权限控制
//        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_FRUUGO);
//        if (!superAdminOrEquivalent.isSuccess()) {
//            return ApiResult.newError(superAdminOrEquivalent.g etErrorMsg());
//        }
//
//        List<String> authorAccountList = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(esFruugoItemRequest.getAccountNumber())) {
//            authorAccountList.addAll(esFruugoItemRequest.getAccountNumber());
//        }
//        //销售
//        List<String> salemanas = esFruugoItemRequest.getSalesman();
//
//        if (CollectionUtils.isEmpty(esFruugoItemRequest.getAccountNumber()) && !superAdminOrEquivalent.getResult()) {
//            // 查询销售对应店铺列表
//            ApiResult<List<String>> authorAccountListResult = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_FRUUGO, false);
//            if (!authorAccountListResult.isSuccess()) {
//                return ApiResult.newError(authorAccountListResult.getErrorMsg());
//            }
//
//            if (CollectionUtils.isEmpty(authorAccountListResult.getResult())) {
//                return ApiResult.newError("未查询到可用店铺列表！");
//            }
//            authorAccountList.addAll(authorAccountListResult.getResult());
//        }
//
//        EsSaleAccountRequest request = new EsSaleAccountRequest();
//        //有选择销售
//        if (CollectionUtils.isNotEmpty(salemanas)) {
//            List<String> saleIds = new ArrayList<>();
//            for (String sale : salemanas) {
//                //根据销售查询销售信息，获取employeeId
//                ApiResult<NewUser> userByNo = NewUsermgtUtils.getUserByNo(sale);
//                if (!userByNo.isSuccess()) {
//                    //不抛错误，只打印日志
//                    log.error("获取员工信息异常：" + userByNo.getErrorMsg());
//                } else {
//                    saleIds.add(userByNo.getResult().getEmployeeId().toString());
//                }
//            }
//
//            if (CollectionUtils.isEmpty(saleIds)) {
//                return ApiResult.newError("选择的销售查不到用户信息！");
//            }
//            request.setSaleIds(saleIds);
//            List<SaleAccount> accountInfoList = saleAccountService.getAccountInfoList(request);
//            if (CollectionUtils.isEmpty(accountInfoList)) {
//                return ApiResult.newError("选择的销售没有权限！");
//            }
//            //销售管理的店铺
//            List<String> saleList = accountInfoList.stream().map(t -> t.getAccountNumber()).collect(Collectors.toList());
//            if (CollectionUtils.isEmpty(authorAccountList)) {
//                authorAccountList = saleList;
//            } else {
//                authorAccountList.retainAll(saleList);
//            }
//            if (CollectionUtils.isEmpty(authorAccountList)) {
//                return ApiResult.newError("选择的销售没有权限！");
//            }
//        }
//
//        esFruugoItemRequest.setAccountNumber(authorAccountList);
        return ApiResult.newSuccess();
    }

    /**
     * 处理扩展信息
     *
     * @param esFruugoItemList
     */
    private void handelPageExtend(List<EsFruugoItem> esFruugoItemList) {
        if (CollectionUtils.isEmpty(esFruugoItemList)) {
            return;
        }

        List<String> accountNumberList = esFruugoItemList.stream().map(EsFruugoItem::getAccountNumber).distinct().collect(Collectors.toList());
        Map<String, Set<String>> salesmanAccountMap = EsAccountUtils.getSalesmanAccountMapByEs(accountNumberList, SaleChannel.CHANNEL_FRUUGO);
        for (EsFruugoItem esFruugoItem : esFruugoItemList) {
            Set<String> saleNames = salesmanAccountMap.get(esFruugoItem.getAccountNumber());
            esFruugoItem.setSalesman(CollectionUtils.isEmpty(saleNames) ? "" : new ArrayList<>(saleNames).get(0));
        }
    }
}

