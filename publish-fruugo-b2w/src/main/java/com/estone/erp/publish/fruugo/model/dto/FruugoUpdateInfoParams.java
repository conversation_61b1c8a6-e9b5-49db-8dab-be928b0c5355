package com.estone.erp.publish.fruugo.model.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.fruugo.model.dto
 * @Author: sj
 * @CreateTime: 2025-02-28  11:49
 * @Description: TODO
 */

@Data
public class FruugoUpdateInfoParams {
    /**
     * 制造商
     */
    private String manufacturer;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 分类对应的属性
     */
    private Map<String, List<Integer>> categoryAndAttributes;

    /**
     * 是否修改准备时间
     */
    private Boolean isUpdateReadyTime;
    /**
     * 准备时间
     */
    private Integer readyTimeAttribute;

    /**
     *是否修改属性
     */
    private Boolean isUpdateAttribute;

    /**
     * 属性值
     */
    private FruugoBatchUpdateAttributeDto fruugoBatchUpdateAttributeDto;

    /**
     * 语言
     */
    private String language;

    /**
     * 描述
     */
    private String text;

    /**
     * 标题
     */
    private String title;


    /**
     * 是否下架
     */
    private Boolean isOffline;

    /**
     * 是否修改折扣价
     */
    private Boolean isUpdateDiscountPrice;

}
