package com.estone.erp.publish.fruugo.enums;

public enum FruugoFeedTaskEnum {
    UPDATE_INVENTORY("UPDATE_INVENTORY", "修改库存"),
    SYNC_LISTING("SYNC_LISTING", "同步listing"),
    UPLOAD_LISTING("UPLOAD_LISTING", "上传listing"),

    /**
     * 修改价格
     */
    UPDATE_PRICE("UPDATE_PRICE", "修改价格"),

    /**
     * 修改制造商和品牌
     */
    UPDATE_MANUFACTURER_AND_BRAND("UPDATE_MANUFACTURER_AND_BRAND", "修改制造商和品牌"),

    /**
     * 修改准备时间
     */
    UPDATE_READY_TIME("UPDATE_READY_TIME", "修改准备时间"),
    /**
     * 修改属性
     */
    UPDATE_ATTRIBUTE("UPDATE_ATTRIBUTE", "修改属性"),

    /**
     * 下架
     */
    DEACTIVATE("DEACTIVATE", "下架"),

    /**
     * 修改折扣价
     */
    UPDATE_DISCOUNT_PRICE("UPDATE_DISCOUNT_PRICE", "修改折扣价"),

    ;



    private String code;

    private String type;

    private FruugoFeedTaskEnum(String code, String type) {
        this.code = code;
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public String getType() {
        return type;
    }
}
