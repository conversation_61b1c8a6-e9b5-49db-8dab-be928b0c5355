package com.estone.erp.publish.fruugo.model.dto;


import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.elasticsearch.model.EsFruugoItem;
import com.estone.erp.publish.fruugo.call.model.request.CreateProductRequest;
import com.estone.erp.publish.fruugo.call.model.request.FruugoGetAllProductResponse;
import com.estone.erp.publish.fruugo.call.model.request.FruugoProductResponse;
import com.estone.erp.publish.fruugo.enums.FruugoAttributeFlagEnum;
import com.estone.erp.publish.fruugo.enums.FruugoStockStatuEnums;
import com.estone.erp.publish.fruugo.model.FruugoAccountConfig;
import com.estone.erp.publish.fruugo.model.FruugoTemplate;
import com.estone.erp.publish.fruugo.model.vo.FruugoAccountConfigPageVO;
import com.estone.erp.publish.fruugo.model.vo.FruugoTemPlatePageVO;
import com.estone.erp.publish.fruugo.model.vo.FruugoTemplateInfoVO;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class FruugoConvertDto {

    public static CreateProductRequest toCreateProductRequest(Map<String, List<FruugoBatchUpdateInfoDto>> batchUpdatePriceDtoMap) {
        if (MapUtils.isEmpty(batchUpdatePriceDtoMap)) {
            return null;
        }
        CreateProductRequest createProductRequest = new CreateProductRequest();
        List<CreateProductRequest.Products> productsList = new ArrayList<>();
        for (String productId : batchUpdatePriceDtoMap.keySet()) {
            CreateProductRequest.Products products = new CreateProductRequest.Products();
            CreateProductRequest.Products.Product product = new CreateProductRequest.Products.Product();
            product.setProductId(productId);

            List<CreateProductRequest.Products.Sku> skus = new ArrayList<>();
            for (FruugoBatchUpdateInfoDto dto : batchUpdatePriceDtoMap.get(productId)) {
                CreateProductRequest.Products.Sku sku = new CreateProductRequest.Products.Sku();
                sku.setSkuId(dto.getSellerSku());
                //库存状态
                CreateProductRequest.Products.Sku.SupplyInfo supplyInfo = new CreateProductRequest.Products.Sku.SupplyInfo();
                if (StringUtils.isBlank(dto.getStockStatus()) && (ObjectUtils.isEmpty(dto.getInventory()) || dto.getInventory() == 0)) {
                    supplyInfo.setStockStatus(FruugoStockStatuEnums.OUTOFSTOCK.getCode());
                } else if (StringUtils.isNotBlank(dto.getStockStatus())) {
                    supplyInfo.setStockStatus(dto.getStockStatus());
                } else {
                    supplyInfo.setStockStatus(FruugoStockStatuEnums.INSTOCK.getCode());
                }
                supplyInfo.setStockQuantity(dto.getInventory());

                //准备时间
                supplyInfo.setLeadTime(dto.getReadyTimeAttribute());

                sku.setSupplyInfo(supplyInfo);
                //价格
                List<CreateProductRequest.Products.Sku.PricingInfo> pricingInfoList = new ArrayList<>();
                CreateProductRequest.Products.Sku.PricingInfo pricingInfo = new CreateProductRequest.Products.Sku.PricingInfo();
                CreateProductRequest.Products.Sku.NormalPrice normalPrice = new CreateProductRequest.Products.Sku.NormalPrice();
                normalPrice.setPrice(dto.getAfterPrice());
                normalPrice.setVatInclusive(dto.getTaxInclusive());
                pricingInfo.setNormalPrice(normalPrice);
                pricingInfo.setCurrency(dto.getCurrency());
                if (dto.getDiscountPrice() != null) {
                    CreateProductRequest.Products.Sku.DiscountPrice discountPrice = new CreateProductRequest.Products.Sku.DiscountPrice();
                    discountPrice.setPrice(dto.getDiscountPrice());
                    discountPrice.setVatInclusive(dto.getDiscountVatInclusive());
                    discountPrice.setStartDate(dto.getDiscountStartDate());
                    discountPrice.setEndDate(dto.getDiscountEndDate());
                    pricingInfo.setDiscountPrice(discountPrice);
                }
                pricingInfoList.add(pricingInfo);
                sku.setPricingInfo(pricingInfoList);
                skus.add(sku);

                //制造商品牌
                if (StringUtils.isNotBlank(dto.getManufacturer())) {
                    product.setManufacturer(dto.getManufacturer());
                }
                if (StringUtils.isNotBlank(dto.getBrand())) {
                    product.setBrand(dto.getBrand());
                }

                //属性
                if (CollectionUtils.isNotEmpty(dto.getAttributes())) {
                    CreateProductRequest.Products.Sku.Details details = new CreateProductRequest.Products.Sku.Details();
                    List<CreateProductRequest.Products.Sku.SkuDescription> skuDescriptions = new ArrayList<>();
                    CreateProductRequest.Products.Sku.SkuDescription skuDescription = new CreateProductRequest.Products.Sku.SkuDescription();
                    List<CreateProductRequest.Products.Sku.Attribute> attributes = new ArrayList<>();
                    dto.getAttributes().forEach(attribute -> {
                        CreateProductRequest.Products.Sku.Attribute attribute1 = new CreateProductRequest.Products.Sku.Attribute();
                        attribute1.setName(attribute.getName());
                        attribute1.setValue(attribute.getValue());
                        attributes.add(attribute1);
                    });
                    skuDescription.setLanguage(dto.getLanguage());
                    skuDescription.setTitle(dto.getTitle());
                    skuDescription.setText(dto.getText());
                    skuDescription.setAttributes(attributes);
                    skuDescriptions.add(skuDescription);
                    details.setSkuDescriptions(skuDescriptions);
                    sku.setDetails(details);
                }

            }
            products.setProduct(product);
            products.setSkus(skus);
            productsList.add(products);
        }
        createProductRequest.setProducts(productsList);
        return createProductRequest;

    }


    public static FruugoTemPlatePageVO convertFruugoTemPlatePageVO(FruugoTemplate fruugoTemplate) {
        if (fruugoTemplate == null) {
            return null;
        }
        FruugoTemPlatePageVO fruugoTemPlatePageVO = new FruugoTemPlatePageVO();
        fruugoTemPlatePageVO.setId(fruugoTemplate.getId());
        fruugoTemPlatePageVO.setAccountNumber(fruugoTemplate.getAccountNumber());
        fruugoTemPlatePageVO.setArticleNumber(fruugoTemplate.getArticleNumber());
        fruugoTemPlatePageVO.setProductType(fruugoTemplate.getProductType());
        fruugoTemPlatePageVO.setStatus(fruugoTemplate.getStatus());
        fruugoTemPlatePageVO.setSystemCategory(fruugoTemplate.getSystemCategory());
        fruugoTemPlatePageVO.setRemark(fruugoTemplate.getRemark());
        fruugoTemPlatePageVO.setCategoryName(fruugoTemplate.getCategoryName());
        fruugoTemPlatePageVO.setProductImages(fruugoTemplate.getProductImages());
        fruugoTemPlatePageVO.setCreatedBy(fruugoTemplate.getCreatedBy());
        fruugoTemPlatePageVO.setCreateTime(fruugoTemplate.getCreateTime());
        fruugoTemPlatePageVO.setUpdateBy(fruugoTemplate.getUpdateBy());
        fruugoTemPlatePageVO.setUpdateTime(fruugoTemplate.getUpdateTime());
        fruugoTemPlatePageVO.setTemplateStatus(fruugoTemplate.getTemplateStatus());
        fruugoTemPlatePageVO.setTitle(fruugoTemplate.getTitle());

        String productImages = fruugoTemplate.getProductImages();
        if (StringUtils.isNotBlank(productImages)) {
            List<FruugoTemplateSkuImageDto> fruugoTemplateSkuImageDtos = JSON.parseArray(productImages, FruugoTemplateSkuImageDto.class);
            if (CollectionUtils.isNotEmpty(fruugoTemplateSkuImageDtos)) {
                fruugoTemPlatePageVO.setProductImages(fruugoTemplateSkuImageDtos.get(0).getMainImageUrl());
            }
        }

        return fruugoTemPlatePageVO;
    }


    public static FruugoAccountConfigPageVO convertFruugoAccountConfigPageVO(FruugoAccountConfig accountConfig) {
        if (accountConfig == null) {
            return null;
        }
        FruugoAccountConfigPageVO fruugoAccountConfigPageVO = new FruugoAccountConfigPageVO();
        fruugoAccountConfigPageVO.setId(accountConfig.getId());
        fruugoAccountConfigPageVO.setAccountNumber(accountConfig.getAccountNumber());
        fruugoAccountConfigPageVO.setStatus(accountConfig.getStatus());
        fruugoAccountConfigPageVO.setUpdateStockRuleCode(accountConfig.getUpdateStockRuleCode());
        fruugoAccountConfigPageVO.setCreatedTime(accountConfig.getCreatedTime());
        fruugoAccountConfigPageVO.setLastUpdateBy(accountConfig.getLastUpdateBy());
        fruugoAccountConfigPageVO.setUpdatedTime(accountConfig.getUpdatedTime());
        fruugoAccountConfigPageVO.setSyncTime(accountConfig.getSyncTime());
        fruugoAccountConfigPageVO.setAccountSite(accountConfig.getAccountSite());
        fruugoAccountConfigPageVO.setEanPrefix(accountConfig.getEanPrefix());
        fruugoAccountConfigPageVO.setManufacturer(accountConfig.getManufacturer());
        fruugoAccountConfigPageVO.setBrand(accountConfig.getBrand());
        fruugoAccountConfigPageVO.setDefaultStock(accountConfig.getDefaultStock());
        fruugoAccountConfigPageVO.setProfitMargin(accountConfig.getProfitMargin());
        fruugoAccountConfigPageVO.setPlatformRate(accountConfig.getPlatformRate());
        fruugoAccountConfigPageVO.setCurrency(accountConfig.getCurrency());
        fruugoAccountConfigPageVO.setReadyTime(accountConfig.getReadyTime());
        return fruugoAccountConfigPageVO;
    }


    public static FruugoTemplateInfoVO toFruugoTemplateInfoVO(FruugoTemplate template) {
        if (template == null) {
            return null;
        }
        FruugoTemplateInfoVO fruugoTemplateInfoVO = new FruugoTemplateInfoVO();
        fruugoTemplateInfoVO.setId(template.getId());
        fruugoTemplateInfoVO.setAccountNumber(template.getAccountNumber());
        fruugoTemplateInfoVO.setArticleNumber(template.getArticleNumber());
        fruugoTemplateInfoVO.setProductType(template.getProductType());
        fruugoTemplateInfoVO.setStatus(template.getStatus());
        fruugoTemplateInfoVO.setSystemCategory(template.getSystemCategory());
        fruugoTemplateInfoVO.setState(template.getState());
        fruugoTemplateInfoVO.setProhibitions(template.getProhibitions());
        fruugoTemplateInfoVO.setRemark(template.getRemark());
        fruugoTemplateInfoVO.setTags(template.getTags());
        fruugoTemplateInfoVO.setCategoryId(template.getCategoryId());
        fruugoTemplateInfoVO.setCategoryName(template.getCategoryName());
        fruugoTemplateInfoVO.setCategoryAttributeConfigs(template.getCategoryAttributeConfigs());
        fruugoTemplateInfoVO.setManufacture(template.getManufacture());
        fruugoTemplateInfoVO.setBrand(template.getBrand());
        fruugoTemplateInfoVO.setSpecifications(template.getSpecifications());
        // 规格详情
        if (StringUtils.isNotBlank(template.getSpecificationDetails())) {
            fruugoTemplateInfoVO.setDetails(JSON.parseArray(template.getSpecificationDetails(), FruugoTemplateProductDetailDto.class));
        }
        // 商品图片
        fruugoTemplateInfoVO.setProductImages(JSON.parseArray(template.getProductImages(), FruugoTemplateSkuImageDto.class));
        fruugoTemplateInfoVO.setCreatedBy(template.getCreatedBy());
        fruugoTemplateInfoVO.setCreateTime(template.getCreateTime());
        fruugoTemplateInfoVO.setUpdateBy(template.getUpdateBy());
        fruugoTemplateInfoVO.setUpdateTime(template.getUpdateTime());
        fruugoTemplateInfoVO.setQueueId(template.getQueueId());
        fruugoTemplateInfoVO.setCeMark(template.getCeMark());
        fruugoTemplateInfoVO.setSafetyWarnings(template.getSafetyWarnings());
        fruugoTemplateInfoVO.setIngredients(template.getIngredients());
        fruugoTemplateInfoVO.setProductId(template.getProductId());
        return fruugoTemplateInfoVO;
    }


    public static FruugoTemplate toFruugoTemplate(FruugoTemplateInfoVO templateInfoVO) {
        if (templateInfoVO == null) {
            return null;
        }
        FruugoTemplate fruugoTemplate = new FruugoTemplate();
        fruugoTemplate.setId(templateInfoVO.getId());
        fruugoTemplate.setAccountNumber(templateInfoVO.getAccountNumber());
        fruugoTemplate.setArticleNumber(templateInfoVO.getArticleNumber());
        fruugoTemplate.setProductType(templateInfoVO.getProductType());
        fruugoTemplate.setStatus(templateInfoVO.getStatus());
        fruugoTemplate.setSystemCategory(templateInfoVO.getSystemCategory());
        fruugoTemplate.setState(templateInfoVO.getState());
        fruugoTemplate.setProhibitions(templateInfoVO.getProhibitions());
        fruugoTemplate.setRemark(templateInfoVO.getRemark());
        fruugoTemplate.setTags(templateInfoVO.getTags());
        fruugoTemplate.setCategoryId(templateInfoVO.getCategoryId());
        fruugoTemplate.setCategoryName(templateInfoVO.getCategoryName());
        fruugoTemplate.setCategoryAttributeConfigs(templateInfoVO.getCategoryAttributeConfigs());
        fruugoTemplate.setManufacture(templateInfoVO.getManufacture());
        fruugoTemplate.setBrand(templateInfoVO.getBrand());
        fruugoTemplate.setSpecifications(templateInfoVO.getSpecifications());
        fruugoTemplate.setSpecificationDetails(JSON.toJSONString(templateInfoVO.getDetails()));
        fruugoTemplate.setProductImages(JSON.toJSONString(templateInfoVO.getProductImages()));
        fruugoTemplate.setCreatedBy(templateInfoVO.getCreatedBy());
        fruugoTemplate.setCreateTime(templateInfoVO.getCreateTime());
        fruugoTemplate.setUpdateBy(templateInfoVO.getUpdateBy());
        fruugoTemplate.setUpdateTime(templateInfoVO.getUpdateTime());
        fruugoTemplate.setQueueId(templateInfoVO.getQueueId());
        fruugoTemplate.setCeMark(templateInfoVO.getCeMark());
        fruugoTemplate.setSafetyWarnings(templateInfoVO.getSafetyWarnings());
        fruugoTemplate.setIngredients(templateInfoVO.getIngredients());
        fruugoTemplate.setProductId(templateInfoVO.getProductId());

        if (CollectionUtils.isNotEmpty(templateInfoVO.getDetails())) {
            fruugoTemplate.setTitle(templateInfoVO.getDetails().get(0).getTitle());
        }
        return fruugoTemplate;
    }


    public static CreateProductRequest toCreateProductRequest(FruugoTemplateInfoVO templateInfoVO) {
        CreateProductRequest createProductRequest = new CreateProductRequest();
        List<CreateProductRequest.Products> productsList = new ArrayList<>();
        CreateProductRequest.Products products = new CreateProductRequest.Products();

        CreateProductRequest.Products.Product product = new CreateProductRequest.Products.Product();
        product.setProductId(templateInfoVO.getArticleNumber());
        product.setBrand(templateInfoVO.getBrand());
        product.setManufacturer(templateInfoVO.getManufacture());
        product.setCategory(templateInfoVO.getCategoryName());

        products.setProduct(product);

        List<CreateProductRequest.Products.Sku> skus = new ArrayList<>();
        Map<String, FruugoTemplateSkuImageDto> productsImageMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(templateInfoVO.getProductImages())) {
            productsImageMap = templateInfoVO.getProductImages().stream()
                    .collect(Collectors.toMap(FruugoTemplateSkuImageDto::getSku, Function.identity()));
        }
        if (CollectionUtils.isNotEmpty(templateInfoVO.getDetails())) {
            for (FruugoTemplateProductDetailDto detail : templateInfoVO.getDetails()) {
                CreateProductRequest.Products.Sku sku = new CreateProductRequest.Products.Sku();
                sku.setSkuId(detail.getSellerSku());

                List<CreateProductRequest.Products.Sku.Gtin> gtins = new ArrayList<>();
                CreateProductRequest.Products.Sku.Gtin gtin = new CreateProductRequest.Products.Sku.Gtin();
                gtin.setCodeType(detail.getGtinName());
                gtin.setCode(detail.getGtinValue());
                gtins.add(gtin);
                sku.setGtins(gtins);

                CreateProductRequest.Products.Sku.Details details = new CreateProductRequest.Products.Sku.Details();
                List<CreateProductRequest.Products.Sku.SkuDescription> skuDescriptions = new ArrayList<>();
                CreateProductRequest.Products.Sku.SkuDescription skuDescription = new CreateProductRequest.Products.Sku.SkuDescription();
                skuDescription.setLanguage("en");
                skuDescription.setTitle(detail.getTitle());
                skuDescription.setText(detail.getDescription());
                List<CreateProductRequest.Products.Sku.Attribute> attributes = new ArrayList<>();
                for (FruugoTemplateProductDetailDto.Attribute attribute : detail.getAttributes()) {
                    CreateProductRequest.Products.Sku.Attribute attribute1 = new CreateProductRequest.Products.Sku.Attribute();
                    attribute1.setName(attribute.getName());
                    attribute1.setValue(attribute.getValue());
                    attributes.add(attribute1);
                }
                skuDescription.setAttributes(attributes);
                skuDescriptions.add(skuDescription);
                details.setSkuDescriptions(skuDescriptions);
                List<CreateProductRequest.Products.Sku.Media> mediaList = new ArrayList<>();
                FruugoTemplateSkuImageDto fruugoTemplateSkuImageDto = productsImageMap.get(detail.getSku());
                if (ObjectUtils.isNotEmpty(fruugoTemplateSkuImageDto) && CollectionUtils.isNotEmpty(fruugoTemplateSkuImageDto.getImageUrls())) {
                    for (String imageUrl : fruugoTemplateSkuImageDto.getImageUrls()) {
                        CreateProductRequest.Products.Sku.Media media = new CreateProductRequest.Products.Sku.Media();
                        media.setDescription(detail.getSku());
                        media.setUrl(imageUrl);
                        media.setType("IMAGE");
                        mediaList.add(media);
                        if (mediaList.size() > 4) {
                            break;
                        }
                    }
                }
                details.setMedia(mediaList);
                sku.setDetails(details);


                CreateProductRequest.Products.Sku.SupplyInfo supplyInfo = new CreateProductRequest.Products.Sku.SupplyInfo();
                supplyInfo.setStockStatus(detail.getInventoryAttribute());
                supplyInfo.setStockQuantity(detail.getInventory());
//                supplyInfo.setLeadTime(detail.getReadyTimeAttribute());
                supplyInfo.setRestockDate(detail.getReorderTime());
                sku.setSupplyInfo(supplyInfo);

                List<CreateProductRequest.Products.Sku.PricingInfo> pricingInfoList = new ArrayList<>();
                CreateProductRequest.Products.Sku.PricingInfo pricingInfo = new CreateProductRequest.Products.Sku.PricingInfo();
                pricingInfo.setCurrency(detail.getCurrency());
                CreateProductRequest.Products.Sku.NormalPrice normalPrice = new CreateProductRequest.Products.Sku.NormalPrice();
                normalPrice.setPrice(Double.valueOf(detail.getPrice()));
                normalPrice.setVatInclusive(detail.getTaxInclusive() == 1);
                pricingInfo.setNormalPrice(normalPrice);
                if (StringUtils.isNotEmpty(detail.getActionPrice())){
                    CreateProductRequest.Products.Sku.DiscountPrice discountPrice = new CreateProductRequest.Products.Sku.DiscountPrice();
                    discountPrice.setPrice(Double.valueOf(detail.getActionPrice()));
                    discountPrice.setVatInclusive(detail.getActionPriceTaxInclusive() == 1);
                    discountPrice.setStartDate(detail.getActionPriceStartTime());
                    discountPrice.setEndDate(detail.getActionPriceEndTime());
                    pricingInfo.setDiscountPrice(discountPrice);
                }
                pricingInfoList.add(pricingInfo);
                sku.setPricingInfo(pricingInfoList);

                sku.setPackageWeight(detail.getWeight().intValue());

                sku.setVolume(Double.valueOf(detail.getVolume()));
                skus.add(sku);

            }

        }

        products.setSkus(skus);
        productsList.add(products);
        createProductRequest.setProducts(productsList);
        return createProductRequest;
    }


    public static CreateProductRequest toCreateProductRequest(List<FruugoTemplateInfoVO> fruugoTemplateInfoVOS) {
        CreateProductRequest createProductRequest = new CreateProductRequest();
        List<CreateProductRequest.Products> productsList = new ArrayList<>();

        // 遍历每一个 FruugoTemplateInfoVO
        for (FruugoTemplateInfoVO templateInfoVO : fruugoTemplateInfoVOS) {
            CreateProductRequest.Products products = new CreateProductRequest.Products();

            CreateProductRequest.Products.Product product = new CreateProductRequest.Products.Product();
            product.setProductId(templateInfoVO.getProductId());
            product.setBrand(templateInfoVO.getBrand());
            product.setManufacturer(templateInfoVO.getManufacture());
            product.setCategory(templateInfoVO.getCategoryName());

            products.setProduct(product);

            List<CreateProductRequest.Products.Sku> skus = new ArrayList<>();
            Map<String, FruugoTemplateSkuImageDto> productsImageMap = new HashMap<>();

            // 如果产品图片不为空，生成图片映射
            if (CollectionUtils.isNotEmpty(templateInfoVO.getProductImages())) {
                productsImageMap = templateInfoVO.getProductImages().stream()
                        .collect(Collectors.toMap(FruugoTemplateSkuImageDto::getSku, Function.identity()));
            }

            // 如果产品详情不为空，处理每一个产品详情
            if (CollectionUtils.isNotEmpty(templateInfoVO.getDetails())) {
                for (FruugoTemplateProductDetailDto detail : templateInfoVO.getDetails()) {
                    CreateProductRequest.Products.Sku sku = new CreateProductRequest.Products.Sku();
                    sku.setSkuId(detail.getSellerSku());

                    List<CreateProductRequest.Products.Sku.Gtin> gtins = new ArrayList<>();
                    CreateProductRequest.Products.Sku.Gtin gtin = new CreateProductRequest.Products.Sku.Gtin();
                    gtin.setCodeType(detail.getGtinName());
                    gtin.setCode(detail.getGtinValue());
                    gtins.add(gtin);
                    sku.setGtins(gtins);

                    CreateProductRequest.Products.Sku.Details details = new CreateProductRequest.Products.Sku.Details();
                    List<CreateProductRequest.Products.Sku.SkuDescription> skuDescriptions = new ArrayList<>();
                    CreateProductRequest.Products.Sku.SkuDescription skuDescription = new CreateProductRequest.Products.Sku.SkuDescription();
                    skuDescription.setLanguage("en");
                    skuDescription.setTitle(detail.getTitle());
                    skuDescription.setText(detail.getDescription());

                    List<CreateProductRequest.Products.Sku.Attribute> attributes = new ArrayList<>();
                    for (FruugoTemplateProductDetailDto.Attribute attribute : detail.getAttributes()) {
                        CreateProductRequest.Products.Sku.Attribute attribute1 = new CreateProductRequest.Products.Sku.Attribute();
                        attribute1.setName(attribute.getName());
                        attribute1.setValue(attribute.getValue());
                        attributes.add(attribute1);
                    }
                    attributes.add(createAttribute(FruugoAttributeFlagEnum.CE_MARK, StringUtils.isNotBlank(templateInfoVO.getCeMark()) ? templateInfoVO.getCeMark() : ""));
                    attributes.add(createAttribute(FruugoAttributeFlagEnum.SAFETY_WARNING, StringUtils.isNotBlank(templateInfoVO.getSafetyWarnings()) ? templateInfoVO.getSafetyWarnings() : ""));
                    attributes.add(createAttribute(FruugoAttributeFlagEnum.INGREDIENTS, StringUtils.isNotBlank(templateInfoVO.getIngredients()) ? templateInfoVO.getIngredients() : ""));

                    skuDescription.setAttributes(attributes);
                    skuDescriptions.add(skuDescription);
                    details.setSkuDescriptions(skuDescriptions);

                    // 添加媒体信息（图片）
                    List<CreateProductRequest.Products.Sku.Media> mediaList = new ArrayList<>();
                    FruugoTemplateSkuImageDto fruugoTemplateSkuImageDto = productsImageMap.get(detail.getSku());
                    if (ObjectUtils.isNotEmpty(fruugoTemplateSkuImageDto) && CollectionUtils.isNotEmpty(fruugoTemplateSkuImageDto.getImageUrls())) {
                        for (String imageUrl : fruugoTemplateSkuImageDto.getImageUrls()) {
                            CreateProductRequest.Products.Sku.Media media = new CreateProductRequest.Products.Sku.Media();
                            media.setDescription(detail.getSku());
                            media.setUrl(imageUrl);
                            media.setType("IMAGE");
                            mediaList.add(media);
                            if (mediaList.size() > 4) {
                                break;
                            }
                        }
                    }
                    details.setMedia(mediaList);
                    sku.setDetails(details);

                    // 设置供应信息
                    CreateProductRequest.Products.Sku.SupplyInfo supplyInfo = new CreateProductRequest.Products.Sku.SupplyInfo();
                    supplyInfo.setStockStatus(detail.getInventoryAttribute());
                    supplyInfo.setStockQuantity(detail.getInventory());
                    if (ObjectUtils.isNotEmpty(detail.getReadyTimeAttribute())) {
                        supplyInfo.setLeadTime(detail.getReadyTimeAttribute());
                    }
                    supplyInfo.setRestockDate(detail.getReorderTime());
                    sku.setSupplyInfo(supplyInfo);

                    // 设置定价信息
                    List<CreateProductRequest.Products.Sku.PricingInfo> pricingInfoList = new ArrayList<>();
                    CreateProductRequest.Products.Sku.PricingInfo pricingInfo = new CreateProductRequest.Products.Sku.PricingInfo();
                    pricingInfo.setCurrency(detail.getCurrency());
                    CreateProductRequest.Products.Sku.NormalPrice normalPrice = new CreateProductRequest.Products.Sku.NormalPrice();
                    normalPrice.setPrice(Double.valueOf(detail.getPrice()));
                    normalPrice.setVatInclusive(detail.getTaxInclusive() == 1);
                    pricingInfo.setNormalPrice(normalPrice);

                    CreateProductRequest.Products.Sku.DiscountPrice discountPrice = new CreateProductRequest.Products.Sku.DiscountPrice();
                    if (StringUtils.isNotBlank(detail.getActionPrice())) {
                        discountPrice.setPrice(Double.valueOf(detail.getActionPrice()));
                        discountPrice.setVatInclusive(detail.getActionPriceTaxInclusive() == 1);
                        discountPrice.setStartDate(detail.getActionPriceStartTime());
                        discountPrice.setEndDate(detail.getActionPriceEndTime());
                        pricingInfo.setDiscountPrice(discountPrice);
                    }
                    pricingInfoList.add(pricingInfo);
                    sku.setPricingInfo(pricingInfoList);

                    // 设置重量和体积
                    sku.setPackageWeight(detail.getWeight().intValue());
                    sku.setVolume(Double.valueOf(detail.getVolume()));

                    // 添加 SKU 到列表
                    skus.add(sku);
                }
            }

            // 设置 SKU 信息并将产品添加到产品列表
            products.setSkus(skus);
            productsList.add(products);
        }

        // 设置最终的产品列表并返回
        createProductRequest.setProducts(productsList);
        return createProductRequest;
    }

    private static CreateProductRequest.Products.Sku.Attribute createAttribute(FruugoAttributeFlagEnum flagEnum, String value) {
        CreateProductRequest.Products.Sku.Attribute attribute = new CreateProductRequest.Products.Sku.Attribute();
        attribute.setName(flagEnum.getFieldName());
        attribute.setValue(value);
        return attribute;
    }


    public static EsFruugoItem toEsFruugoItem(EsFruugoItem esFruugoItem, FruugoProductResponse.SkuDetails skuDetails) {
        if (ObjectUtils.isEmpty(skuDetails)) {
            return null;
        }
        if (ObjectUtils.isEmpty(esFruugoItem)) {
            esFruugoItem.setCreateDate(new Date());
        }
        String sellerSkuId = skuDetails.getSkuId();
        esFruugoItem.setSellerSku(sellerSkuId);
        int lastUnderscoreIndex = sellerSkuId.lastIndexOf("_");
        String skuId = lastUnderscoreIndex != -1 ? sellerSkuId.substring(0, lastUnderscoreIndex) : sellerSkuId;
        esFruugoItem.setSku(skuId);
        esFruugoItem.setNewSkuStatus(skuDetails.getStatus());

        FruugoProductResponse.SkuDescription details = skuDetails.getDetails();

        //标题
        if (details != null) {
            List<FruugoProductResponse.SkuDescriptionItem> skuDescriptions = details.getSkuDescriptions();
            if (skuDescriptions != null && !skuDescriptions.isEmpty()) {
                FruugoProductResponse.SkuDescriptionItem skuDescriptionItem = skuDescriptions.get(0);
                if (skuDescriptionItem != null) {
                    esFruugoItem.setTitle(skuDescriptionItem.getTitle());
                }
            }
        }

        //库存状态
        FruugoProductResponse.SupplyInfo supplyInfo = skuDetails.getSupplyInfo();
        if (null != supplyInfo) {
            esFruugoItem.setStatus(supplyInfo.getStockStatus());
            esFruugoItem.setInventory(supplyInfo.getStockQuantity());
        }

        //价格
        List<FruugoProductResponse.PricingInfo> pricingInfoList = skuDetails.getPricingInfo();
        if (CollectionUtils.isNotEmpty(pricingInfoList)) {
            FruugoProductResponse.PricingInfo pricingInfo = pricingInfoList.get(0);
            esFruugoItem.setPrice(pricingInfo.getNormalPrice().getPrice());
            esFruugoItem.setCurrency(pricingInfo.getCurrency());
            esFruugoItem.setTaxInclusive(pricingInfo.getNormalPrice().getVatInclusive());

            FruugoProductResponse.DiscountPrice discountPrice = pricingInfo.getDiscountPrice();
            if (discountPrice != null) {
                esFruugoItem.setDiscountPrice(discountPrice.getPrice());
                esFruugoItem.setDiscountVatInclusive(discountPrice.getVatInclusive());
                String startDate = discountPrice.getStartDate();
                if (StringUtils.isNotBlank(startDate)) {
                    Date date = DateUtils.parseDate(startDate, "yyyy-MM-dd");
                    esFruugoItem.setDiscountStartDate(date);
                }
                String endDate = discountPrice.getEndDate();
                if (StringUtils.isNotBlank(endDate)) {
                    Date date = DateUtils.parseDate(endDate, "yyyy-MM-dd");
                    esFruugoItem.setDiscountEndDate(date);
                }
            }
        }

        //库存信息
        esFruugoItem.setNewInventoryInfo(JSON.toJSONString(supplyInfo));
        //售价信息
        esFruugoItem.setNewPriceInfo(JSON.toJSONString(pricingInfoList));

        Date now = new Date();
        esFruugoItem.setSyncDate(now);
        return esFruugoItem;
    }


    public static void toEsFruugoItem(EsFruugoItem esFruugoItem, FruugoGetAllProductResponse.Product.Sku sku) {
        if (ObjectUtils.isEmpty(esFruugoItem)) {
            esFruugoItem.setCreateDate(new Date());
        }
        String sellerSkuId = sku.getSkuId();
        esFruugoItem.setSellerSku(sellerSkuId);
        int lastUnderscoreIndex = sellerSkuId.lastIndexOf("_");
        String skuId = lastUnderscoreIndex != -1 ? sellerSkuId.substring(0, lastUnderscoreIndex) : sellerSkuId;
        esFruugoItem.setSku(skuId);
        esFruugoItem.setNewSkuStatus(sku.getSkuQualityStatus());
        esFruugoItem.setTitle(sku.getTitle());
        esFruugoItem.setNewCategory(sku.getCategory());
        esFruugoItem.setNewPriceInfo(JSON.toJSONString(sku.getNormalPrices()));
        if (CollectionUtils.isNotEmpty(sku.getErrors())) {
            esFruugoItem.setErrorMsg(String.join(",", sku.getErrors()));
        } else {
            esFruugoItem.setErrorMsg("");
        }
        esFruugoItem.setNewInventoryInfo(JSON.toJSONString(sku.getSupplyData()));
        if (null != sku.getSupplyData()) {
            esFruugoItem.setInventory(sku.getSupplyData().getStockValue());
            esFruugoItem.setStatus(sku.getSupplyData().getStockStatus());
        }
        if (CollectionUtils.isNotEmpty(sku.getNormalPrices())) {
            esFruugoItem.setPrice(sku.getNormalPrices().get(0).getNormalPriceWithoutVAT());
            esFruugoItem.setCurrency(sku.getNormalPrices().get(0).getCurrency());
        }
        List<FruugoGetAllProductResponse.Product.Sku.DiscountPrice> discountPrices = sku.getDiscountPrices();
        if (CollectionUtils.isNotEmpty(discountPrices)) {
            FruugoGetAllProductResponse.Product.Sku.DiscountPrice discountPrice = discountPrices.get(0);
            esFruugoItem.setDiscountPrice(discountPrice.getDiscountPriceWithoutVAT());
        }
        esFruugoItem.setSyncDate(new Date());
        esFruugoItem.setLastUpdate(new Date());

    }
}
