package com.estone.erp.publish.fruugo.util;

import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.elasticsearch.model.EsFruugoItem;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ComposeSku;
import com.estone.erp.publish.system.product.bean.SuiteSku;
import com.estone.erp.publish.system.product.enums.ComposeCheckStepEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Locale;
import java.util.Map;

/**
 * @Auther yucm
 * @Date 2022/9/7
 */
@Slf4j
public class FruugoItemUtils {

    /**
     * Fruugo API 返回的日期格式：Jul 24, 2025, 4:55:33 AM
     */
    private static final DateTimeFormatter FRUUGO_INPUT_FORMATTER =
            DateTimeFormatter.ofPattern("MMM dd, yyyy, h:mm:ss a", Locale.ENGLISH);

    /**
     * 目标输出格式：yyyy-MM-dd
     */
    private static final DateTimeFormatter OUTPUT_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 设置产品信息
     * @param esFruugoItem
     * @param productInfoVO
     */
    public static void setProductInfo(EsFruugoItem esFruugoItem, ProductInfoVO productInfoVO) {
        try {
            // 废弃状态sku取合并sku信息
            if (null != productInfoVO && StringUtils.isNotBlank(productInfoVO.getSonSku()) && StringUtils.equalsIgnoreCase(SkuStatusEnum.DISCARD.getCode(), productInfoVO.getSkuStatus())) {
                String mergeSku = ProductUtils.getMergeSku(productInfoVO.getSonSku());
                if (!StringUtils.equalsIgnoreCase(mergeSku, productInfoVO.getSonSku())) {
                    productInfoVO = ProductUtils.getSkuInfo(mergeSku);
                    esFruugoItem.setSku(mergeSku);
                }
            }
            if (null == productInfoVO || StringUtils.isBlank(productInfoVO.getSonSku())) {
                // 匹配组合套装
                if (matchComposeProduct(esFruugoItem)) {
                    return;
                }
            }
            esFruugoItem.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
            // 禁售平台
            esFruugoItem.setForbidChannel(CommonUtils.splitList(productInfoVO.getForbidChannel(), ","));
            // 禁售类型
            esFruugoItem.setInfringementTypeNames(CommonUtils.splitList(productInfoVO.getInfringementTypeName(),"|"));
            // 禁售原因
            esFruugoItem.setInfringementObjs(CommonUtils.splitList(productInfoVO.getInfringementObj(), "|"));
            // 禁售站点
            esFruugoItem.setProhibitionSites(productInfoVO.getProhibitionSiteWithPlatformDefaultSite());
            // 单品状态
            esFruugoItem.setItemStatus(productInfoVO.getSkuStatus());
            // 产品标签
            esFruugoItem.setTag(productInfoVO.getTagNames());
            // 特殊标签
            esFruugoItem.setSpecialTag(CommonUtils.splitIntList(productInfoVO.getSpecialGoodsCode(), ","));
            // 类别中文名
            esFruugoItem.setCategory(productInfoVO.getCategoryCnName());
            // 是否促销
            esFruugoItem.setPromotion(productInfoVO.getPromotion());
            // 是否新品
            esFruugoItem.setNewState(productInfoVO.getNewState());
        }catch (Exception e) {
            log.error(String.format("同步listing数据获取产品SKu信息: %s, ", esFruugoItem.getSku()), e);
        }
    }

    /**
     * 匹配是否是组合套装，匹配上则用组合套装的产品信息
     * 规则:
     * 1、优先匹配组合SKU数据，若存在于组合SKU中，则取组合数据；不存在与组合SKU中，则继续判断2
     * 2、匹配套装SKU数据，若存在于套装SKU中，需通过组合套装映射表，获取对应的组合SPU，
     * 及对应的组合SPU数据状态；无组合映射关系则取套装状态即可
     * 3、不存在于套装SKU和组合SKU，则匹配管理单品数据
     *
     * @param item  listing
     */
    public static boolean matchComposeProduct(EsFruugoItem item) {
        String articleNumber = item.getSku();
        // 组合产品
        ComposeSku composeProduct = ProductUtils.getComposeProduct(articleNumber);
        if (composeProduct != null) {
            setProductInfoByCompose(item, composeProduct);
            return true;
        }
        // 非组合产品的查询一遍组合套装映射表
        Map<String, String> composeSkuSuitMap = ProductUtils.getComposeSkuBySuit(Collections.singletonList(articleNumber));
        if (MapUtils.isEmpty(composeSkuSuitMap)
                || StringUtils.isBlank(composeSkuSuitMap.get(articleNumber))) {
            // 套装产品
            SuiteSku suiteSku = ProductUtils.getSaleSuiteInfoBySonSku(articleNumber);
            if (suiteSku == null) {
                return false;
            }
            item.setSku(suiteSku.getSuiteSku());
            item.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
            item.setItemStatus(SingleItemEnum.getEnNameByCode(suiteSku.getItemStatus()));
            ComposeCheckStepEnum stepEnum = Boolean.TRUE.equals(suiteSku.getIsEnable()) ? ComposeCheckStepEnum.NORMAL : ComposeCheckStepEnum.DISCARD;
            item.setComposeStatus(stepEnum.getCode());
            // 禁售平台
            item.setForbidChannel(suiteSku.getForbidChannels());
            // 禁售类型
            item.setInfringementTypeNames(suiteSku.getInfringementTypeNames());
            // 禁售原因
            item.setInfringementObjs(suiteSku.getInfringementObjs());
            // 禁售站点
            item.setProhibitionSites(suiteSku.getProhibitionPlatSites());
            return true;
        }
        // 套装映射的组合产品
        ComposeSku composeProductRef = ProductUtils.getComposeProduct(composeSkuSuitMap.get(articleNumber));
        if (composeProductRef != null) {
            setProductInfoByCompose(item, composeProductRef);
            return true;
        }
        return false;
    }

    private static void setProductInfoByCompose(EsFruugoItem item, ComposeSku composeProduct) {
        item.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
        item.setSku(composeProduct.getComposeSku());
        item.setComposeStatus(composeProduct.getCheckStep());
        // 类别中文名
        String categoryName = StringUtils.isNotBlank(composeProduct.getCategoryName()) ? StrUtil.strAddComma(composeProduct.getCategoryName().replaceAll(">", ",")) : null;
        item.setCategory(categoryName);
        // 禁售平台
        item.setForbidChannel(composeProduct.getForbidChannels());
        // 禁售类型
        item.setInfringementTypeNames(composeProduct.getInfringementTypeNames());
        // 禁售原因
        item.setInfringementObjs(composeProduct.getInfringementObjs());
        // 禁售站点
        item.setProhibitionSites(composeProduct.getProhibitionPlatSites());
        // 单品状态
        item.setItemStatus(SingleItemEnum.getEnNameByCode(composeProduct.getComposeStatus()));
        // 产品标签code
        item.setTag(composeProduct.getTagCode());
        // 特殊标签
        item.setSpecialTag(null);
        // 是否促销
        item.setPromotion(0);
        // 是否新品
        item.setNewState(false);
    }

    /**
     * 将 Fruugo 日期字符串转换为 yyyy-MM-dd 格式
     *
     * @param fruugoDateStr Fruugo API 返回的日期字符串，格式如：Jul 24, 2025, 4:55:33 AM
     * @return 转换后的日期字符串，格式为 yyyy-MM-dd，如果输入为空或转换失败则返回 null
     */
    public static String convertFruugoDateToYMD(String fruugoDateStr) {
        if (fruugoDateStr == null || fruugoDateStr.trim().isEmpty()) {
            return null;
        }

        try {
            LocalDateTime dateTime = LocalDateTime.parse(fruugoDateStr, FRUUGO_INPUT_FORMATTER);
            return dateTime.format(OUTPUT_FORMATTER);
        } catch (Exception e) {
            // 可以根据需要添加日志记录
            System.err.println("日期格式转换失败: " + fruugoDateStr + ", 错误: " + e.getMessage());
            return null;
        }
    }

}
