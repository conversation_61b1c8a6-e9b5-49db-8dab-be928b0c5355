package com.estone.erp.publish.fruugo.service.impl;

import com.estone.erp.common.annotation.NeedToLog;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.fruugo.mapper.FruugoOperateLogMapper;
import com.estone.erp.publish.fruugo.model.FruugoAccountConfig;
import com.estone.erp.publish.fruugo.model.FruugoOperateLog;
import com.estone.erp.publish.fruugo.model.FruugoOperateLogCriteria;
import com.estone.erp.publish.fruugo.model.FruugoOperateLogExample;
import com.estone.erp.publish.fruugo.service.FruugoAccountConfigService;
import com.estone.erp.publish.fruugo.service.FruugoOperateLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> fruugo_operate_log
 * 2023-08-06 20:59:14
 */
@Service("fruugoOperateLogService")
@Slf4j
public class FruugoOperateLogServiceImpl implements FruugoOperateLogService {
    @Resource
    private FruugoOperateLogMapper fruugoOperateLogMapper;
    @Autowired
    private FruugoAccountConfigService fruugoAccountConfigService;

    @Override
    public int countByExample(FruugoOperateLogExample example) {
        Assert.notNull(example, "example is null!");
        return fruugoOperateLogMapper.countByExample(example);
    }

    @Override
    public CQueryResult<FruugoOperateLog> search(CQuery<FruugoOperateLogCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        FruugoOperateLogCriteria query = cquery.getSearch();
        FruugoOperateLogExample example = query.getExample();
        example.setOrderByClause("create_date,id desc");
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = fruugoOperateLogMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<FruugoOperateLog> fruugoOperateLogs = fruugoOperateLogMapper.selectByExample(example);
        // 组装结果
        CQueryResult<FruugoOperateLog> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(fruugoOperateLogs);
        return result;
    }

    @Override
    public FruugoOperateLog selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return fruugoOperateLogMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<FruugoOperateLog> selectByExample(FruugoOperateLogExample example) {
        Assert.notNull(example, "example is null!");
        return fruugoOperateLogMapper.selectByExample(example);
    }

    @Override
    public int insert(FruugoOperateLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return fruugoOperateLogMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(FruugoOperateLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return fruugoOperateLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(FruugoOperateLog record, FruugoOperateLogExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return fruugoOperateLogMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return fruugoOperateLogMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public void generateConfigLog(FruugoAccountConfig updateConfig, String userName) {
        if(null == updateConfig || null == updateConfig.getId()) {
            return;
        }
        FruugoAccountConfig dbAccountConfig = fruugoAccountConfigService.selectByPrimaryKey(updateConfig.getId());
        if(null == dbAccountConfig) {
            return;
        }
        try {
            Class<FruugoAccountConfig> clazz = FruugoAccountConfig.class;
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                if(!field.isAnnotationPresent(NeedToLog.class)) {
                    continue;
                }
                NeedToLog annotation = field.getAnnotation(NeedToLog.class);
                String description = annotation.value();
                field.setAccessible(true);
                //改前改后不一致才记录日志
                if (ObjectUtils.isNotEmpty(field.get(updateConfig)) && field.get(updateConfig).equals(field.get(dbAccountConfig))) {
                    continue;
                }
                FruugoOperateLog operateLog = new FruugoOperateLog();
                operateLog.setType("account_config");
                operateLog.setBusinessId(updateConfig.getId());
                operateLog.setAccountNumber(dbAccountConfig.getAccountNumber());
                operateLog.setCreateBy(userName);
                operateLog.setCreateDate(Timestamp.valueOf(LocalDateTime.now()));
                operateLog.setFieldName(field.getName());
                String beforeValue = field.get(dbAccountConfig) == null ? "" : field.get(dbAccountConfig).toString();
                operateLog.setBefore(beforeValue);
                operateLog.setAfter(ObjectUtils.isNotEmpty(field.get(updateConfig)) ? field.get(updateConfig).toString() : "");
                if (StringUtils.isBlank( operateLog.getBefore()) && StringUtils.isBlank( operateLog.getAfter())){
                    continue;
                }
                this.insert(operateLog);
            }
        }catch (Exception e) {
            log.error("构建日志报错" + e.getMessage(), e);
        }
    }

    @Override
    public void batchInsert(List<FruugoOperateLog> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        // 由于FruugoOperateLogMapper没有batchInsert方法，我们使用循环插入
        for (FruugoOperateLog record : records) {
            this.insert(record);
        }
    }
}