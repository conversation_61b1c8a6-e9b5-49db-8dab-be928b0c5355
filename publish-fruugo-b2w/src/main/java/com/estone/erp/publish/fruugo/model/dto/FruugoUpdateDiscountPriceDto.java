package com.estone.erp.publish.fruugo.model.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
public class FruugoUpdateDiscountPriceDto implements Serializable {


    private static final long serialVersionUID = -6177951740246172133L;

    private String id;

    /**
     * 产品ID（fruugoSkuId）
     */
    private String itemId;

    private String accountNumber;

    private String sku;
    /**
     * 改后价格
     */
    private Double afterPrice;

    /**
     * 改前价格
     */
    private Double beforePrice;

    /**
     * 是否打折
     */
    private Boolean discountVatInclusive;

    /**
     * 开始时间 yyyy-MM-dd
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date discountStartDate;

    /**
     * 结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date discountEndDate;

}
