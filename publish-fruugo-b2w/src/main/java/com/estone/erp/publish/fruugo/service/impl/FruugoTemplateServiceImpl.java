package com.estone.erp.publish.fruugo.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.constant.CurrencyConstant;
import com.estone.erp.common.constant.PublishCommonConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.POIUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.base.pms.enums.PicturePlatEnum;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.cos.TencentCos;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.executors.FruugoExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch.model.EsFruugoItem;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsFruugoItemRequest;
import com.estone.erp.publish.elasticsearch.service.EsFruugoItemService;
import com.estone.erp.publish.fruugo.compont.publish.TemplatePublishExecutor;
import com.estone.erp.publish.fruugo.compont.publish.param.BatchPublishParam;
import com.estone.erp.publish.fruugo.compont.publish.param.TemplatePublishParam;
import com.estone.erp.publish.fruugo.enums.*;
import com.estone.erp.publish.fruugo.mapper.FruugoTemplateMapper;
import com.estone.erp.publish.fruugo.model.*;
import com.estone.erp.publish.fruugo.model.dto.*;
import com.estone.erp.publish.fruugo.model.vo.FruugoTemPlatePageVO;
import com.estone.erp.publish.fruugo.model.vo.FruugoTemplateInfoVO;
import com.estone.erp.publish.fruugo.mq.param.AutoPublishMessage;
import com.estone.erp.publish.fruugo.mq.param.FruugoPlatformMqMessage;
import com.estone.erp.publish.fruugo.service.*;
import com.estone.erp.publish.fruugo.util.FruuugoTemplateDateUtils;
import com.estone.erp.publish.fruugo.util.ProductImgUtils;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.model.CategoryMapping;
import com.estone.erp.publish.platform.model.CategoryMappingExample;
import com.estone.erp.publish.platform.service.CategoryMappingService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.Logistics.LogisticsClient;
import com.estone.erp.publish.system.Logistics.request.CalcShippingCostBySkuParam;
import com.estone.erp.publish.system.Logistics.response.ShippingDetailsResponse;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.pmssalePublicData.model.PublishSpuModelDO;
import com.estone.erp.publish.system.pmssalePublicData.model.PublishSpuModelRequest;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SpuInfo;
import com.estone.erp.publish.system.product.bean.SpuOfficial;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.bean.forbidden.Sites;
import com.estone.erp.publish.system.product.response.PageResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024-11-20 11:28:30
 */
@Service("fruugoTemplateService")
@Slf4j
public class FruugoTemplateServiceImpl implements FruugoTemplateService {
    @Resource
    private FruugoTemplateMapper fruugoTemplateMapper;

    @Resource
    private PermissionsHelper permissionHelper;

    @Resource
    private CategoryMappingService categoryMappingService;

    @Resource
    private FruugoAccountConfigService fruugoAccountConfigService;



    @Resource
    private FruugoFeedTaskService fruugoFeedTaskService;


    @Resource
    private TemplatePublishExecutor templatePublishExecutor;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private EsFruugoItemService esFruugoItemService;

    @Resource
    private FruugoTimePublishQueueService fruugoTimePublishQueueService;

    @Resource
    private FruugoSyncItemService fruugoSyncItemService;

    @Resource
    private FruugoItemService fruugoItemService;

    @Resource
    private FruugoBatchPublishUpdateService fruugoBatchPublishUpdateService;


    private static LogisticsClient logisticsClient = SpringUtils.getBean(LogisticsClient.class);

    @Override
    public int countByExample(FruugoTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return fruugoTemplateMapper.countByExample(example);
    }

    @Override
    public CQueryResult<FruugoTemPlatePageVO> search(CQuery<FruugoTemplateCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        FruugoTemplateCriteria query = cquery.getSearch();
        Assert.notNull(query.getIsParent(), "未设置isParent参数");

        String platform = SaleChannel.CHANNEL_FRUUGO;
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils
                .isSuperAdminOrEquivalent(platform);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
        }
        // 人员权限
        Pair<Boolean, List<String>> employeePair = PermissionsHelper.getDefaultOrAuthorEmployeePair(platform, query.getCreatedBy(),
                query.getCreateByList(), query.getAccountNumber(), CommonUtils.splitList(query.getAccountNumberList(), ","));
        if (BooleanUtils.isTrue(employeePair.getLeft())) {
            query.setCreateByList(employeePair.getRight());
        }

        FruugoTemplateExample example = query.getExample();
        String tableName = query.getIsParent() ? FruugoTemplateTableEnum.FRUUGO_TEMPLATE_MODEL.getTableName() : FruugoTemplateTableEnum.FRUUGO_TEMPLATE.getTableName();
        example.setTableName(tableName);
        example.setColumns(FruugoTemplateExample.pageFields);
        example.setOrderByClause("create_time desc");
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = fruugoTemplateMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<FruugoTemplate> fruugoTemplates = fruugoTemplateMapper.selectByExample(example);
        List<FruugoTemPlatePageVO> FruugoTemPlatePageVOList = fruugoTemplates.stream().map(FruugoConvertDto::convertFruugoTemPlatePageVO).collect(Collectors.toList());
        // 组装结果
        CQueryResult<FruugoTemPlatePageVO> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(FruugoTemPlatePageVOList);
        return result;
    }

    @Override
    public FruugoTemplate selectByPrimaryKey(Integer id, Boolean isParent) {
        Assert.notNull(id, "id is null!");
        Assert.notNull(isParent, "isParent is null!");
        String tableName = isParent ? FruugoTemplateTableEnum.FRUUGO_TEMPLATE_MODEL.getTableName() : FruugoTemplateTableEnum.FRUUGO_TEMPLATE.getTableName();
        FruugoTemplate fruugoTemplate = fruugoTemplateMapper.selectByPrimaryKey(id, tableName);
        return fruugoTemplate;
    }

    @Override
    public FruugoTemplateInfoVO getById(Integer id, Boolean isParent) {
        Assert.notNull(id, "id is null!");
        Assert.notNull(isParent, "isParent is null!");
        FruugoTemplate fruugoTemplate = this.selectByPrimaryKey(id, isParent);
        List<String> fruugoImages = FmsUtils.getFruugoImages(fruugoTemplate.getArticleNumber());
        FruugoTemplateInfoVO fruugoTemplateInfoVO = FruugoConvertDto.toFruugoTemplateInfoVO(fruugoTemplate);
        fruugoTemplateInfoVO.setImagePool(fruugoImages);
        return fruugoTemplateInfoVO;
    }

    @Override
    public List<FruugoTemplate> selectByExample(FruugoTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return fruugoTemplateMapper.selectByExample(example);
    }

    @Override
    public int insert(FruugoTemplate record, String tableName) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        record.setCreatedBy(StringUtils.isNotBlank(record.getCreatedBy()) ? record.getCreatedBy() : WebUtils.getUserName());
        record.setTableName(tableName);
        return fruugoTemplateMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(FruugoTemplate record, String tableName) {
        Assert.notNull(record, "record is null!");
        record.setTableName(tableName);
        // 默认加修改时间和修改人
        return fruugoTemplateMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(FruugoTemplate record, FruugoTemplateExample example) {
        Assert.notNull(record, "record is null!");
        Assert.notNull(example.getTableName(), "tableName is null!");
        // 默认加修改时间和修改人
        return fruugoTemplateMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "dto is null!");
        return fruugoTemplateMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public FruugoTemplate save(FruugoTemplateInfoVO templateInfoVO, String tableName) {
        Assert.notNull(templateInfoVO, "templateInfoVO is null!");
        Assert.notNull(tableName, "tableName is null!");
        FruugoTemplate template = FruugoConvertDto.toFruugoTemplate(templateInfoVO);
        template.setTableName(tableName);
        List<FruugoTemplateProductDetailDto> details = templateInfoVO.getDetails();
        if (CollectionUtils.isNotEmpty(details)) {
            template.setTitle(details.get(0).getTitle());
        }
        if (ObjectUtils.isNotEmpty(template.getId())) {
            template.setUpdateBy(WebUtils.getUserName());
            template.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            template.setTableName(tableName);
            template.setTemplateStatus(1);
            fruugoTemplateMapper.updateByPrimaryKeySelective(template);
            return template;
        }
        template.setStatus(FruugoTemplateStatusEnums.WAIT.getCode());
        template.setCreatedBy(WebUtils.getUserName());
        template.setCreateTime(new Timestamp(System.currentTimeMillis()));
        template.setTemplateStatus(1);
        template.setUpdateBy(WebUtils.getUserName());
        template.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        fruugoTemplateMapper.insert(template);
        return template;
    }


    @Override
    public void copyTemplateByPrimaryKey(List<Integer> ids) {
        // 查询模板集合
        FruugoTemplateExample example = new FruugoTemplateExample();
        example.createCriteria().andIdIn(ids);
        example.setTableName(FruugoTemplateTableEnum.FRUUGO_TEMPLATE.getTableName());
        List<FruugoTemplate> temuTemplates = fruugoTemplateMapper.selectByExample(example);

        if (CollectionUtils.isNotEmpty(temuTemplates)) {
            List<FruugoTemplate> templateList = temuTemplates.stream().map(item -> {
                item.setAccountNumber(null);
                item.setStatus(FruugoTemplateStatusEnums.WAIT.getCode());
                item.setCreatedBy(WebUtils.getUserName());
                item.setCreateTime(new Timestamp(System.currentTimeMillis()));
                item.setUpdateBy(WebUtils.getUserName());
                item.setUpdateTime(new Timestamp(System.currentTimeMillis()));
                String specificationDetails = item.getSpecificationDetails();
                if (StringUtils.isNotBlank(specificationDetails)) {
                    List<FruugoTemplateProductDetailDto> fruugoTemplateProductDetailDtos = JSON.parseArray(item.getSpecificationDetails(), FruugoTemplateProductDetailDto.class);
                    if (CollectionUtils.isNotEmpty(fruugoTemplateProductDetailDtos)) {
                        item.setSpecificationDetails(JSON.toJSONString(fruugoTemplateProductDetailDtos.stream().peek(detail -> {
                            detail.setGtinName(null);
                            detail.setGtinValue(null);
                        }).collect(Collectors.toList())));
                    }

                }
                return item;
            }).collect(Collectors.toList());

            fruugoTemplateMapper.batchInsert(templateList);
        }

    }

    @Override
    public List<String> checkExitAdminTemplate(List<String> spuList) {
        return fruugoTemplateMapper.selectAccountNumberBySpuList(spuList);
    }


    @Override
    public FruugoTemplateInfoVO getTemplateInfoVO(BuilderTemplateDO builderTemplateDO) {

        List<ProductInfo> productInfoList = this.findProductInfo(builderTemplateDO.getArticleNumber());
        if (ObjectUtils.isEmpty(builderTemplateDO.getPublishType())) {
            throw new BusinessException("刊登类型不能为空");
        }
        return this.getFruugoTemplateInfoVO(builderTemplateDO, productInfoList);
    }

    private FruugoTemplateInfoVO getFruugoTemplateInfoVO(BuilderTemplateDO builderTemplateDO, List<ProductInfo> productInfoList) {
        String accountNumber = builderTemplateDO.getAccountNumber();
        String articleNumber = builderTemplateDO.getArticleNumber();
        FruugoTemplateInfoVO fruugoTemplateInfoVO = new FruugoTemplateInfoVO();

        //获取店铺配置
        FruugoAccountConfigExample fruugoAccountConfigExample = new FruugoAccountConfigExample();
        fruugoAccountConfigExample.createCriteria().andAccountNumberEqualTo(accountNumber);
        List<FruugoAccountConfig> fruugoAccountConfigs = fruugoAccountConfigService.selectByExample(fruugoAccountConfigExample);
        if (CollectionUtils.isEmpty(fruugoAccountConfigs)) {
            throw new BusinessException("店铺配置不存在");
        }
        FruugoAccountConfig fruugoAccountConfig = fruugoAccountConfigs.get(0);

        //获取范本信息
        FruugoTemplateExample fruugoTemplateExample = new FruugoTemplateExample();
        FruugoTemplateExample.Criteria criteria = fruugoTemplateExample.createCriteria().andArticleNumberEqualTo(articleNumber).andTemplateStatusEqualTo(1);
        //批量刊登取模板数据,其他取范本数据
        if (ObjectUtils.isNotEmpty(builderTemplateDO.getTemplateId())
                && builderTemplateDO.getPublishType().equals(FruugoPublishModeEnum.BATCH_PUBLISH.getCode())) {
            criteria.andIdEqualTo(builderTemplateDO.getTemplateId());
            fruugoTemplateExample.setTableName(FruugoTemplateTableEnum.FRUUGO_TEMPLATE.getTableName());
        } else {
            fruugoTemplateExample.setTableName(FruugoTemplateTableEnum.FRUUGO_TEMPLATE_MODEL.getTableName());
            criteria.andTemplateStatusEqualTo(1);
        }
        List<FruugoTemplate> fruugoTemplateList = fruugoTemplateMapper.selectByExample(fruugoTemplateExample);
        FruugoTemplate fruugoTemplate;
        if (CollectionUtils.isNotEmpty(fruugoTemplateList)) {
            fruugoTemplate = fruugoTemplateList.get(0);
        } else {
            fruugoTemplate = null;
        }


        List<String> codes = CommonUtils.splitList(fruugoAccountConfig.getProdCategoryCodes(), ",");
        for (ProductInfo productInfo : productInfoList) {
            String code = StringUtils.substringAfterLast(productInfo.getFullpathcode(), "_");
            // 检查类目是否匹配店铺配置选择的刊登类目
            if (CollectionUtils.isNotEmpty(codes) && !codes.contains(code)) {
                throw new BusinessException("类目不属于店铺配置中的可刊登分类");
            }
        }


        // 由店铺自动刊登配置产生的，刊登时需再次校验排除产品标签、产品重量限制区间、是否符合配置，若不符合则不进行刊登
        if (Integer.valueOf(1).equals(builderTemplateDO.getGeneratedByAutoPublish())) {
            // 根据店铺自动刊登配置，过滤不符合排除产品标签、产品重量限制区间条件的产品
            filterProductsByAutoPublishConfig(productInfoList, fruugoAccountConfig);
        }

        //基础信息，禁售信息
        CompletableFuture<Void> basicInfoFuture = CompletableFuture.runAsync(() -> {
            basicInfo(productInfoList, fruugoTemplateInfoVO, accountNumber, articleNumber);
        }, FruugoExecutors.GENERATE_TEMPLATE_POOL);

        //平台分类，批量刊登取模板，其他优先范本再映射
        CompletableFuture<Void> matchCategoryNameFuture = CompletableFuture.runAsync(() -> {
            String categoryName = this.matchCategoryName(articleNumber, fruugoTemplate);
            fruugoTemplateInfoVO.setCategoryName(categoryName);
        }, FruugoExecutors.GENERATE_TEMPLATE_POOL);

        //获取商品详情
        CompletableFuture<Void> productDetailFuture = CompletableFuture.runAsync(() -> {
            productDetail(productInfoList, fruugoTemplateInfoVO, fruugoTemplate, builderTemplateDO, fruugoAccountConfig);
        }, FruugoExecutors.GENERATE_TEMPLATE_POOL);

        //获取商品图片
        CompletableFuture<Void> productImagesFuture = CompletableFuture.runAsync(() -> {
            productImages(productInfoList, fruugoTemplateInfoVO, articleNumber, fruugoTemplate, builderTemplateDO);
        }, FruugoExecutors.GENERATE_TEMPLATE_POOL);

        CompletableFuture.allOf(basicInfoFuture, matchCategoryNameFuture, productDetailFuture, productImagesFuture).join();

        return fruugoTemplateInfoVO;
    }


    /**
     * 根据店铺自动刊登配置，过滤不符合排除产品标签、产品重量限制区间条件的产品
     */
    private void filterProductsByAutoPublishConfig(List<ProductInfo> productInfoList, FruugoAccountConfig accountConfig) {


        // 1. 过滤排除标签
        productInfoList.removeIf(productInfo -> {
            String excludeLabel = accountConfig.getExcludeLabel();
            String tag = productInfo.getTag();
            if (StringUtils.isBlank(excludeLabel) || StringUtils.isBlank(tag)) {
                return false;
            }
            List<String> excludeLabelList = CommonUtils.splitList(excludeLabel, ",");
            List<String> tagList = CommonUtils.splitList(tag, ",");
            return tagList.stream().anyMatch(excludeLabelList::contains);
        });

        // 2. 过滤重量区间
        Double fromWeight = accountConfig.getFromWeight();
        Double toWeight = accountConfig.getToWeight();
        productInfoList.removeIf(productInfo -> {
            // 取产品系统预估包裹重量，若预估包裹重量为空，则用SKU净重+包材重量+搭配包材重量+面单3g
            double weight = Optional.ofNullable(productInfo.getPackageWeight()).orElse(ProductUtils.calcSkuWeight(productInfo));
            boolean satisfiesFromWeight = (fromWeight == null) || (Double.doubleToLongBits(weight) >= Double.doubleToLongBits(fromWeight));
            boolean satisfiesToWeight = (toWeight == null) || (Double.doubleToLongBits(weight) < Double.doubleToLongBits(toWeight));
            return !(satisfiesFromWeight && satisfiesToWeight);
        });
    }

    private void productImages(List<ProductInfo> productInfoList, FruugoTemplateInfoVO fruugoTemplateInfoVO, String articleNumber, FruugoTemplate fruugoTemplate, BuilderTemplateDO builderTemplateDO) {
        if (fruugoTemplate != null && builderTemplateDO.getPublishType().equals(FruugoPublishModeEnum.BATCH_PUBLISH.getCode())) {
            String productImages = fruugoTemplate.getProductImages();
            if (StringUtils.isBlank(productImages)) {
                throw new BusinessException("模板商品图片为空,请禁用");
            }
            List<FruugoTemplateSkuImageDto> productImagesList = JSON.parseArray(productImages, FruugoTemplateSkuImageDto.class);
            fruugoTemplateInfoVO.setProductImages(productImagesList);
            return;
        }
        List<FruugoTemplateSkuImageDto> productImages = new ArrayList<>();
        List<String> fruugoImages = FmsUtils.getFruugoImages(articleNumber);
        if (CollectionUtils.isEmpty(fruugoImages)) {
            return;
        }
        fruugoTemplateInfoVO.setImagePool(fruugoImages);
        for (ProductInfo productInfo : productInfoList) {
            FruugoTemplateSkuImageDto fruugoTemplateSkuImageDto = new FruugoTemplateSkuImageDto();
            fruugoTemplateSkuImageDto.setSku(productInfo.getSonSku());
            //获取主图，与sku匹配
            String mainImage = FruuugoTemplateDateUtils.matchMainImage(productInfo.getSonSku(), fruugoImages);
            fruugoTemplateSkuImageDto.setMainImageUrl(mainImage);
            //获取附图
            List<String> imageUrl = FruuugoTemplateDateUtils.matchAttachImage(fruugoImages, articleNumber);
            fruugoTemplateSkuImageDto.setImageUrls(imageUrl);
            productImages.add(fruugoTemplateSkuImageDto);
        }
        fruugoTemplateInfoVO.setProductImages(productImages);

    }

    private void productDetail(List<ProductInfo> productInfoList, FruugoTemplateInfoVO fruugoTemplateInfoVO, FruugoTemplate fruugoTemplate, BuilderTemplateDO builderTemplateDO, FruugoAccountConfig fruugoAccountConfig) {

        //品牌
        String brand = fruugoAccountConfig.getBrand();
        if (StringUtils.isBlank(brand)) {
            throw new BusinessException("店铺配置品牌为空");
        }
        fruugoTemplateInfoVO.setBrand(brand.trim());
        //制造商
        String manufacturer = fruugoAccountConfig.getManufacturer();
        if (StringUtils.isBlank(manufacturer)) {
            throw new BusinessException("店铺配置制造商为空");
        }
        fruugoTemplateInfoVO.setManufacture(manufacturer.trim());
        //店铺后缀
        String accountSite = fruugoAccountConfig.getAccountSite();
        if (StringUtils.isBlank(accountSite)) {
            throw new BusinessException("店铺配置店铺后缀为空");
        }
        //productID
        String productId = builderTemplateDO.getArticleNumber() + accountSite;
        fruugoTemplateInfoVO.setProductId(productId);

        //币种
        String currency = fruugoAccountConfig.getCurrency();
        if (StringUtils.isBlank(currency)) {
            throw new BusinessException("店铺配置币种为空");
        }
        fruugoTemplateInfoVO.setAccountSite(accountSite.trim());

        //ce标识，警告信息， Ingredients
        if (ObjectUtils.isNotEmpty(fruugoTemplate)) {
            fruugoTemplateInfoVO.setCeMark(fruugoTemplate.getCeMark());
            fruugoTemplateInfoVO.setSafetyWarnings(fruugoTemplate.getSafetyWarnings());
            fruugoTemplateInfoVO.setIngredients(fruugoTemplate.getIngredients());
        }


        //规格供应信息
        Map<String, BigDecimal> costMap = productInfoList.stream().collect(Collectors.toMap(ProductInfo::getSonSku, ProductInfo::getCost));
        List<FruugoTemplateProductDetailDto> details = new ArrayList<>();

        //如果是批量刊登获取模板数据，如果是定时刊登获取范本的属性
        if (ObjectUtils.isNotEmpty(fruugoTemplate) && builderTemplateDO.getPublishType().equals(FruugoPublishModeEnum.BATCH_PUBLISH.getCode())) {
            List<FruugoTemplateProductDetailDto> detailDtos = JSON.parseArray(fruugoTemplate.getSpecificationDetails(), FruugoTemplateProductDetailDto.class);
            for (FruugoTemplateProductDetailDto detailDto : detailDtos) {
                FruugoTemplateProductDetailDto fruugoTemplateProductDetailDto = new FruugoTemplateProductDetailDto();
                int lastIndex = detailDto.getSellerSku().lastIndexOf("_");
                fruugoTemplateProductDetailDto.setSellerSku(detailDto.getSellerSku().substring(0, lastIndex) + "_" + accountSite);
                fruugoTemplateProductDetailDto.setSku(detailDto.getSku());
                fruugoTemplateProductDetailDto.setAttributes(detailDto.getAttributes());
                fruugoTemplateProductDetailDto.setInventory(detailDto.getInventory());
                fruugoTemplateProductDetailDto.setInventoryAttribute(detailDto.getInventoryAttribute());
                fruugoTemplateProductDetailDto.setReadyTimeAttribute(detailDto.getReadyTimeAttribute());
                fruugoTemplateProductDetailDto.setReorderTime(detailDto.getReorderTime());
                fruugoTemplateProductDetailDto.setCurrency(currency);
                fruugoTemplateProductDetailDto.setActionCurrency(currency);
                fruugoTemplateProductDetailDto.setTaxInclusive(detailDto.getTaxInclusive());
                fruugoTemplateProductDetailDto.setWeight(detailDto.getWeight());
                fruugoTemplateProductDetailDto.setVolume(detailDto.getVolume());
                fruugoTemplateProductDetailDto.setTitle(detailDto.getTitle());
                fruugoTemplateProductDetailDto.setDescription(detailDto.getDescription());
                this.getGtin(fruugoTemplateProductDetailDto, detailDto.getSku(), fruugoAccountConfig);
                //计算价格
                BigDecimal cost = costMap.get(detailDto.getSku());
                if (ObjectUtils.isEmpty(cost)) {
                    throw new BusinessException("获取不到成本");
                }
                String price = this.getPrice(fruugoTemplateInfoVO.getTags(), fruugoAccountConfig, detailDto.getSku(), detailDto.getWeight(), cost);
                fruugoTemplateProductDetailDto.setPrice(price);

                details.add(fruugoTemplateProductDetailDto);
            }

        } else {
            //如果范本属性不为空，就是用范本属性
            Map<String, List<FruugoTemplateProductDetailDto.Attribute>> attributesMap = new HashMap<>();
            if (ObjectUtils.isNotEmpty(fruugoTemplate)) {
                List<FruugoTemplateProductDetailDto> detailDtos = JSON.parseArray(fruugoTemplate.getSpecificationDetails(), FruugoTemplateProductDetailDto.class);
                attributesMap = detailDtos.stream().collect(Collectors.toMap(FruugoTemplateProductDetailDto::getSku, FruugoTemplateProductDetailDto::getAttributes));
            }

            for (ProductInfo productInfo : productInfoList) {
                FruugoTemplateProductDetailDto fruugoTemplateProductDetailDto = new FruugoTemplateProductDetailDto();
                fruugoTemplateProductDetailDto.setSellerSku(productInfo.getSonSku() + "_" + accountSite);
                fruugoTemplateProductDetailDto.setSku(productInfo.getSonSku());
                //生成一个布尔变量是否单体，productInfoList的size()==1，就是单体
                boolean isSingle = productInfoList.size() == 1;
                //解析产品属性
                if (ObjectUtils.isEmpty(fruugoTemplate)) {
                    List<FruugoTemplateProductDetailDto.Attribute> attributes = this.resolveAttrMap(productInfo.getSaleAtts(), isSingle);
                    fruugoTemplateProductDetailDto.setAttributes(attributes);
                } else {
                    if (MapUtils.isNotEmpty(attributesMap)) {
                        fruugoTemplateProductDetailDto.setAttributes(attributesMap.getOrDefault(productInfo.getSonSku(), null));
                    } else {
                        fruugoTemplateProductDetailDto.setAttributes(null);
                    }
                }


                //库存
                fruugoTemplateProductDetailDto.setInventory(fruugoAccountConfig.getDefaultStock());

                //库存附属信息
                fruugoTemplateProductDetailDto.setInventoryAttribute("INSTOCK");
                fruugoTemplateProductDetailDto.setReadyTimeAttribute(fruugoAccountConfig.getReadyTime());

                //币种
                fruugoTemplateProductDetailDto.setCurrency(currency);
                fruugoTemplateProductDetailDto.setActionCurrency(currency);

                //是否含税
                fruugoTemplateProductDetailDto.setTaxInclusive(0);

                //重量
                fruugoTemplateProductDetailDto.setWeight(productInfo.getPackageWeight());
                //体积
                this.getProductVolume(fruugoTemplateProductDetailDto, productInfo);

                //标题和描述
                this.getTitleAndDesc(fruugoTemplateProductDetailDto, productInfo);

                //GTIN
                this.getGtin(fruugoTemplateProductDetailDto, productInfo.getSonSku(), fruugoAccountConfig);

                //计算价格
                String price = this.getPrice(fruugoTemplateInfoVO.getTags(), fruugoAccountConfig, productInfo.getSonSku(), productInfo.getPackageWeight(), productInfo.getCost());
                fruugoTemplateProductDetailDto.setPrice(price);
                details.add(fruugoTemplateProductDetailDto);
            }
        }
        fruugoTemplateInfoVO.setDetails(details);

    }

    @Override
    public String getPrice(String tags, FruugoAccountConfig fruugoAccountConfig, String sku, Double weight, BigDecimal cost) {
        //物流方式
        String shippingMethodCode = null;
        if (StringUtils.isNotBlank(tags) && tags.contains("电")) {
            //云途挂号（带电 ） ：THZXR_D
            shippingMethodCode = "THZXR_D";
        } else {
            //云途挂号（普货）：THPHR_P
            shippingMethodCode = "THPHR_P";
        }
        CalcShippingCostBySkuParam param = new CalcShippingCostBySkuParam();
        param.setArticlNumber(sku);
        param.setCountryName("瑞士");
        param.setWeight(weight);
        param.setShippingMethodCode(shippingMethodCode);
        param.setSaleChannel(SaleChannel.CHANNEL_FRUUGO);
        param.setReturnNum(true);

        ApiResult<List<ShippingDetailsResponse>> listApiResult = logisticsClient.calculatorShippingCost(param);
        if (!listApiResult.isSuccess()) {
            throw new BusinessException("计算物流价格失败:" + listApiResult.getErrorMsg());
        }
        if (CollectionUtils.isEmpty(listApiResult.getResult())) {
            throw new BusinessException("计算物流价格失败,请求物流系统计算结果为空");
        }
        ShippingDetailsResponse shippingDetailsResponse = listApiResult.getResult().get(0);

        log.info("销售成本为:{}", cost);
        //运费
        BigDecimal calValue = BigDecimal.valueOf(shippingDetailsResponse.getPrice());
        log.info("计算物流价格结果为:{}", shippingDetailsResponse.getPrice());

        if (ObjectUtils.isEmpty(fruugoAccountConfig.getPlatformRate())
                || ObjectUtils.isEmpty(fruugoAccountConfig.getProfitMargin())
                || ObjectUtils.isEmpty(fruugoAccountConfig.getCurrency())) {
            throw new BusinessException("店铺配置平台费率和毛利率和币种不能为空");
        }
        //平台费率和毛利率
        BigDecimal platformRate = BigDecimal.valueOf(fruugoAccountConfig.getPlatformRate());
        BigDecimal profitMargin = BigDecimal.valueOf(fruugoAccountConfig.getProfitMargin());

        //计算价格，售价=(销售成本价 + 运费) / (1 - 平台费率 - 毛利率)
        BigDecimal salePrice = calValue.add(cost).divide(BigDecimal.ONE.subtract(platformRate).subtract(profitMargin), 2, RoundingMode.UP);
        log.info("计算售价结果为:{}", salePrice);

        //获取汇率
        ApiResult<Double> rateResult = PriceCalculatedUtil.getExchangeRate(CurrencyConstant.CNY, fruugoAccountConfig.getCurrency());
        if (!rateResult.isSuccess() || ObjectUtils.isEmpty(rateResult.getResult())) {
            throw new BusinessException(String.format("%s获取汇率失败：%s to %s", CurrencyConstant.CNY, fruugoAccountConfig.getCurrency(), rateResult.getErrorMsg()));
        }
        BigDecimal exchangeRate = BigDecimal.valueOf(rateResult.getResult());
        log.info("获取汇率结果为:{}", exchangeRate);

        String lastPrice = String.valueOf(salePrice.multiply(exchangeRate).setScale(2, RoundingMode.UP));
        return lastPrice;
    }


    private void getGtin(FruugoTemplateProductDetailDto fruugoTemplateProductDetailDto, String sku, FruugoAccountConfig fruugoAccountConfig) {
        if (StringUtils.isBlank(fruugoAccountConfig.getEanPrefix())) {
            return;
        }
        fruugoTemplateProductDetailDto.setGtinName("EAN");

        String gtinValue = FruuugoTemplateDateUtils.generateGtin("EAN", fruugoAccountConfig.getEanPrefix(), sku);
        fruugoTemplateProductDetailDto.setGtinValue(gtinValue);
    }

    /**
     * 标题：输入框，必填，限制1-150字符，默认取SKU在产品系统中通用文案的标题，
     * 优先级：长标题>短标题>sku，将SKU对应的color和size加到标题最后，
     * 超出限制时进行截取（加完color和size后，超出限制再截取）
     *
     * @param fruugoTemplateProductDetailDto
     * @param productInfo
     */
    private void getTitleAndDesc(FruugoTemplateProductDetailDto fruugoTemplateProductDetailDto, ProductInfo productInfo) {
        List<SpuOfficial> spuOfficialTitles = ProductUtils.getSpuOfficialTitles(Lists.newArrayList(productInfo.getMainSku()));
        if (CollectionUtils.isEmpty(spuOfficialTitles)) {
            log.error("{}:获取标题描述信息为空", productInfo.getMainSku());
            return;
        }
        SpuOfficial spuOfficial = spuOfficialTitles.get(0);
        FruuugoTemplateDateUtils.setTitleAndDesc(spuOfficial, fruugoTemplateProductDetailDto, productInfo);
    }

    private void getProductVolume(FruugoTemplateProductDetailDto fruugoTemplateProductDetailDto, ProductInfo productInfo) {
        if (productInfo.getLength() != null && productInfo.getWide() != null && productInfo.getHeight() != null) {
            BigDecimal length = productInfo.getLength();
            BigDecimal width = productInfo.getWide();
            BigDecimal height = productInfo.getHeight();
            fruugoTemplateProductDetailDto.setVolume(length.multiply(width).multiply(height).setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        }
    }

    private List<FruugoTemplateProductDetailDto.Attribute> resolveAttrMap(String saleAtts, boolean isSingle) {
        List<FruugoTemplateProductDetailDto.Attribute> attributeList = new ArrayList<>();
        if (isSingle && StringUtils.isBlank(saleAtts)) {
            FruugoTemplateProductDetailDto.Attribute attribute = new FruugoTemplateProductDetailDto.Attribute();
            attribute.setName("Colour");
            attribute.setValue("Multicolor");
            attributeList.add(attribute);
            return attributeList;
        }

        if (StringUtils.isBlank(saleAtts)) {
            return attributeList;
        }

        JSONArray attributeArray = JSON.parseArray(saleAtts);

        // 用来记录没有 'color' 和 'size' 的属性
        List<FruugoTemplateProductDetailDto.Attribute> tempAttributes = new ArrayList<>();
        boolean hasColor = false;
        boolean hasSize = false;

        for (int i = 0; i < attributeArray.size(); i++) {
            JSONObject attributeJson = attributeArray.getJSONObject(i);
            String enName = attributeJson.getString("enName");
            String enValue = attributeJson.getString("enValue");

            if (StringUtils.isNotBlank(enName) && StringUtils.isNotBlank(enValue)) {
                FruugoTemplateProductDetailDto.Attribute attribute = new FruugoTemplateProductDetailDto.Attribute();

                // 如果是 color 或 size，直接添加到 attributeList
                if ("color".equalsIgnoreCase(enName)) {
                    attribute.setName("Colour");
                    attribute.setValue(enValue);
                    attributeList.add(attribute);
                    hasColor = true;
                } else if ("size".equalsIgnoreCase(enName)) {
                    attribute.setName("Size");
                    attribute.setValue(enValue);
                    attributeList.add(attribute);
                    hasSize = true;
                } else {
                    // 如果是其他属性，存储起来以供后续使用
                    attribute.setName(enName);
                    attribute.setValue(enValue);
                    tempAttributes.add(attribute);
                }
            }
        }

        // 如果没有 'color' 或 'size'，则使用其他属性填充
        if (!hasColor || !hasSize) {
            // 优先顺序：color -> size -> MANUFACTURER
            String[] priorityNames = {"color", "size", "MANUFACTURER"};
            int attributeIndex = 0;

            // 遍历其他属性，按顺序填充
            for (int j = 0; j < tempAttributes.size() && attributeIndex < priorityNames.length; j++) {
                FruugoTemplateProductDetailDto.Attribute tempAttribute = tempAttributes.get(j);

                // 只填充没有的属性
                if (!hasColor && "color".equalsIgnoreCase(priorityNames[attributeIndex])) {
                    tempAttribute.setName("Colour");
                    attributeList.add(tempAttribute);
                    hasColor = true;
                } else if (!hasSize && "size".equalsIgnoreCase(priorityNames[attributeIndex])) {
                    tempAttribute.setName("Size");
                    attributeList.add(tempAttribute);
                    hasSize = true;
                } else if ("MANUFACTURER".equalsIgnoreCase(priorityNames[attributeIndex])) {
                    tempAttribute.setName("MANUFACTURER");
                    attributeList.add(tempAttribute);
                }
                attributeIndex++;
            }
        }

        return attributeList;
    }


    private void basicInfo(List<ProductInfo> productInfoList, FruugoTemplateInfoVO fruugoTemplateInfoVO, String accountNumber, String articleNumber) {
        //店铺
        fruugoTemplateInfoVO.setAccountNumber(accountNumber);

        //商品编码
        fruugoTemplateInfoVO.setArticleNumber(articleNumber);
        //产品类型
        fruugoTemplateInfoVO.setProductType(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
        //产品状态，产品标签
        fruugoTemplateInfoVO.setState(productInfoList.stream()
                .map(ProductInfo::getItemStatus)
                .map(SkuStatusEnum::buildName)
                .distinct().collect(Collectors.joining(",")));
        fruugoTemplateInfoVO.setTags(productInfoList.stream()
                .map(ProductInfo::getTag).distinct()
                .collect(Collectors.joining(",")));


        SpuInfo spuInfo = this.getSpu(articleNumber);

        if (ObjectUtils.isNotEmpty(spuInfo)) {
            //系统分类
            fruugoTemplateInfoVO.setSystemCategory(spuInfo.getCategoryPath());

            //刊登备注
            fruugoTemplateInfoVO.setRemark(spuInfo.getPostRemark());
        }


        //禁售信息
        Map<String, List<String>> forbiddenInfoMap = new HashMap<>();
        for (ProductInfo productinfo : productInfoList) {
            List<SalesProhibitionsVo> salesProhibitionsVos = productinfo.getSalesProhibitionsVos();
            if (CollectionUtils.isNotEmpty(salesProhibitionsVos)) {
                Map<String, List<SalesProhibitionsVo>> salesProhibitionMap = salesProhibitionsVos.stream()
                        .collect(Collectors.groupingBy(SalesProhibitionsVo::getPlat));
                if (MapUtils.isEmpty(salesProhibitionMap)) {
                    continue;
                }
                salesProhibitionMap.forEach((platform, siteInfos) -> {
                    List<String> sites = siteInfos.stream().map(SalesProhibitionsVo::getSites)
                            .filter(CollectionUtils::isNotEmpty)
                            .flatMap(Collection::stream)
                            .map(Sites::getSite)
                            .filter(StringUtils::isNotBlank)
                            .distinct().collect(Collectors.toList());
                    forbiddenInfoMap.put(platform, sites);
                });
            }
            fruugoTemplateInfoVO.setProhibitions(JSON.toJSONString(forbiddenInfoMap));
        }
    }

    private String matchCategoryName(String articleNumber, FruugoTemplate adminFruugoTemplate) {
        //优先匹配范本
        if (ObjectUtils.isNotEmpty(adminFruugoTemplate)) {
            return adminFruugoTemplate.getCategoryName();
        }
        List<ProductInfo> productInfos = ProductUtils.findProductInfos(List.of(articleNumber));
        if (CollectionUtils.isEmpty(productInfos)) {
            return null;
        }
        ProductInfo productInfo = productInfos.get(0);
        String fullPathCode = productInfo.getFullpathcode();
        // 按路径拆分组合匹配
        CategoryMappingExample example = new CategoryMappingExample();
        example.createCriteria()
                .andSystemCategoryIdEqualTo(fullPathCode)
                .andApplyStateEqualTo(ApplyStatusEnum.YES.getCode())
                .andPlatformEqualTo(Platform.Fruugo.name());
        List<CategoryMapping> categoryMappings = categoryMappingService.selectByExample(example);
        if (CollectionUtils.isNotEmpty(categoryMappings)) {
            return categoryMappings.get(0).getPlatformCategoryName();
        }
        return null;
    }


    /**
     * 根据商品编码获取spu信息
     *
     * @param articleNumber
     * @return
     */
    private SpuInfo getSpu(String articleNumber) {
        ResponseJson spuInfoResult = ProductUtils.findSpuInfo(articleNumber);
        if (!spuInfoResult.isSuccess()) {
            throw new RuntimeException(spuInfoResult.getMessage());
        }
        List<SpuInfo> spuInfos = (List<SpuInfo>) spuInfoResult.getBody().get(ProductUtils.resultKey);
        SpuInfo spuInfo = null;
        if (CollectionUtils.isNotEmpty(spuInfos)) {
            spuInfo = spuInfos.get(0);
        }
        return spuInfo;
    }


    /**
     * 过滤停产、废弃、存档状态SKU，过滤froougo平台禁售的SKU
     *
     * @param articleNumber
     * @return
     */
    public List<ProductInfo> findProductInfo(String articleNumber) {
        List<ProductInfo> productInfoList = ProductUtils.findProductInfos(Lists.newArrayList(articleNumber));
        if (CollectionUtils.isEmpty(productInfoList)) {
            throw new NoSuchElementException("查询不到产品信息:" + articleNumber);
        }

        // 过滤sku状态 停产、存档、废弃
        List<ProductInfo> filterBadStatusProducts = productInfoList.stream()
                .filter(productInfo -> StringUtils.isNotBlank(productInfo.getItemStatus()))
                .filter(productInfo -> !PublishCommonConstant.INTERCEPT_EN_STATE_LIST.contains(productInfo.getItemStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterBadStatusProducts)) {
            throw new BusinessException("无可刊登状态SKU");
        }

        // 过滤fruugo禁售的SKU
        List<ProductInfo> productInfos = filterBadStatusProducts.stream().filter(productInfo -> {
            List<SalesProhibitionsVo> salesProhibitionsVos = productInfo.getSalesProhibitionsVos();
            if (CollectionUtils.isEmpty(salesProhibitionsVos)) {
                return true;
            }
            boolean forbidden = salesProhibitionsVos.stream().anyMatch(salesProhibitionsVo -> SaleChannelEnum.FRUUGO.getChannelName().equals(salesProhibitionsVo.getPlat()));
            return !forbidden;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productInfos)) {
            throw new BusinessException("包含禁售平台SKU,请检查");
        }
        return productInfos;
    }


    @Override
    public void enableFruugoTemplate(FruugoTemplateEnableDto dto) {
        if (CollectionUtils.isEmpty(dto.getIds())) {
            throw new BusinessException("请选择范本");
        }
        dto.setUsername(WebUtils.getUserName());
        dto.setCreateTime(new Timestamp(System.currentTimeMillis()));
        fruugoTemplateMapper.updateModelStatus(dto);
    }


    @Override
    public void checkTemplateInfo(FruugoTemplateInfoVO templateInfoVO) {
        //校验重复刊登
        FruuugoTemplateDateUtils.checkLock(templateInfoVO.getAccountNumber(), templateInfoVO.getArticleNumber());
        //检查SPU当天是否存在刊登中和刊登成功
        this.checkPubilshStatus(templateInfoVO.getId(), templateInfoVO.getArticleNumber(), templateInfoVO.getAccountNumber());
        //校验模板数据
        this.validateTemplateInfo(templateInfoVO);
        //校验在线列表是否存在,
        this.checkExitOnlineList(templateInfoVO);
    }

    private void validateTemplateInfo(FruugoTemplateInfoVO templateInfoVO) {
        if (CollectionUtils.isEmpty(templateInfoVO.getDetails())) {
            throw new BusinessException("商品详情不能为空");
        }
        if (StringUtils.isBlank(templateInfoVO.getCategoryName())) {
            throw new BusinessException("类目不能为空");
        }
        if (templateInfoVO.getDetails().size() > 100) {
            throw new BusinessException("Sku不能超过100个！");
        }
        List<FruugoTemplateProductDetailDto> details = templateInfoVO.getDetails();

        for (FruugoTemplateProductDetailDto detail : details) {
            if (StringUtils.isBlank(detail.getSku())) {
                throw new BusinessException("SKU不能为空");
            }
            if (StringUtils.isBlank(detail.getSellerSku())) {
                throw new BusinessException("Seller不能为空");
            }
            if (StringUtils.isBlank(detail.getTitle())) {
                throw new BusinessException("标题不能为空");
            }
            if (StringUtils.isBlank(detail.getDescription())) {
                throw new BusinessException("描述不能为空");
            }
            if (StringUtils.isBlank(detail.getPrice())) {
                throw new BusinessException("价格不能为空");
            }
            if (Double.parseDouble(detail.getPrice()) <= 0) {
                throw new BusinessException("价格不能小于0");
            }
            if (CollectionUtils.isEmpty(detail.getAttributes())) {
                throw new BusinessException("属性不能为空");
            }
            if (StringUtils.isBlank(detail.getGtinName()) || StringUtils.isBlank(detail.getGtinValue())) {
                throw new BusinessException("GTIN不能为空");
            }
            if (ObjectUtils.isEmpty(detail.getInventory()) || detail.getInventory() <= 0) {
                throw new BusinessException("库存不能为空或不能小于等于0");
            }
            if (StringUtils.isBlank(detail.getCurrency())) {
                throw new BusinessException("币种不能为空");
            }
            if (StringUtils.isBlank(detail.getVolume())) {
                throw new BusinessException("体积不能为空");
            }
            //校验sku是否存在
            ApiResult<Boolean> result = ProductUtils.isExistArticleNumber(detail.getSku());
            if (result.isSuccess() && !result.getResult()) {
                throw new BusinessException("sku :" + detail.getSku() + "商品不存在");
            }

        }
        if (CollectionUtils.isEmpty(templateInfoVO.getProductImages())) {
            throw new BusinessException("商品图片不能为空");
        } else if (templateInfoVO.getProductImages().size() > 100) {
            throw new BusinessException("Sku不能超过100个！");
        } else if (templateInfoVO.getProductImages().size() != templateInfoVO.getDetails().size()) {
            throw new BusinessException("商品图片和sku数量不一致");
        } else {
            List<FruugoTemplateSkuImageDto> productImages = templateInfoVO.getProductImages();
            for (FruugoTemplateSkuImageDto productImage : productImages) {
                if (StringUtils.isBlank(productImage.getMainImageUrl()) || CollectionUtils.isEmpty(productImage.getImageUrls())) {
                    throw new BusinessException("存在Sku图片为空");
                }
            }
        }
    }

    private void checkPubilshStatus(Integer id, String articleNumber, String accountNumber) {
        FruugoTemplateExample fruugoTemplateExample = new FruugoTemplateExample();
        FruugoTemplateExample.Criteria criteria = fruugoTemplateExample.createCriteria()
                .andArticleNumberEqualTo(articleNumber)
                .andAccountNumberEqualTo(accountNumber)
                .andStatusIn(List.of(FruugoTemplateStatusEnums.PUBLISHING.getCode(), FruugoTemplateStatusEnums.SUCCESS.getCode()))
                .andCreateTimeGreaterThan(Timestamp.from(LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant()));
        if (ObjectUtils.isNotEmpty(id)) {
            criteria.andIdNotEqualTo(id);
        }
        fruugoTemplateExample.setTableName(FruugoTemplateTableEnum.FRUUGO_TEMPLATE.getTableName());
        int count = fruugoTemplateMapper.countByExample(fruugoTemplateExample);
        if (count > 0) {
            throw new BusinessException("SPU在店铺存在刊登中、刊登成功的模板");
        }
    }


    @Override
    public ApiResult<?> saveAndPublish(FruugoTemplateInfoVO templateInfoVO) {
        try {
            //校验模板
            this.checkTemplateInfo(templateInfoVO);
            //检查侵权词
            Map<String, String> stringIntegerMap = FruuugoTemplateDateUtils.checkInfringmentWord(templateInfoVO);
            if (MapUtils.isNotEmpty(stringIntegerMap) && (stringIntegerMap.containsKey("品牌") || stringIntegerMap.containsKey("属性"))) {
                StringBuilder errorMsg = new StringBuilder("存在侵权词：");
                if (stringIntegerMap.containsKey("品牌")) {
                    errorMsg.append("品牌：").append(stringIntegerMap.get("品牌"));
                }
                if (stringIntegerMap.containsKey("属性")) {
                    errorMsg.append("属性：").append(stringIntegerMap.get("属性"));
                }
                throw new BusinessException(errorMsg.toString());
            }
            //过滤侵权词
            FruuugoTemplateDateUtils.filterInfringementWord(templateInfoVO);
            //保存模板
            FruugoTemplate fruugoTemplate = this.save(templateInfoVO, FruugoTemplateTableEnum.FRUUGO_TEMPLATE.getTableName());

            TemplatePublishParam templatePublishParam = new TemplatePublishParam();
            templatePublishParam.setUser(WebUtils.getUserName());
            templatePublishParam.setType(FruugoPublishModeEnum.TEMPLATE.getCode());
            templatePublishParam.setTemplateId(fruugoTemplate.getId());
            CompletableFuture.runAsync(() -> {
                templatePublishExecutor.execute(templatePublishParam);
            }, FruugoExecutors.UPLOAD_LISTING_POOL);
        } catch (Exception e) {
            log.error("刊登失败", e);
            String errorMsg;
            if (e instanceof CompletionException) {
                errorMsg = e.getCause().getMessage();
            } else {
                errorMsg = e.getMessage();
            }
            throw new RuntimeException("刊登失败" + errorMsg);
        } finally {
            FruuugoTemplateDateUtils.deleteLock(templateInfoVO.getAccountNumber(), templateInfoVO.getArticleNumber());
        }
        return ApiResult.newSuccess();
    }


    @Override
    public void replaceImage(FruugoTemplateInfoVO templateInfoVO) {
        List<FruugoTemplateSkuImageDto> productImages = templateInfoVO.getProductImages();
        if (CollectionUtils.isEmpty(productImages)) {
            return;
        }
        for (FruugoTemplateSkuImageDto productImage : productImages) {
            List<String> imageUrls = new ArrayList<>();
            if (StringUtils.isNotEmpty(productImage.getMainImageUrl())) {
                imageUrls.add(productImage.getMainImageUrl());
            }
            if (CollectionUtils.isNotEmpty(productImage.getImageUrls())) {
                imageUrls.addAll(productImage.getImageUrls());
            }
            List<String> tengcentImageUrls = this.toTengcentImageUrls(imageUrls);
            productImage.setImageUrls(tengcentImageUrls);
        }
    }

    private List<String> toTengcentImageUrls(List<String> imageUrls) {
        List<String> tengcentImageUrls = null;
        try {
            tengcentImageUrls = new ArrayList<>();
//            Map<String, String> copyBeautyImageMap = new HashMap<>();
            // 查询是否存在美工图 美工图替换路径
            List<String> checkBeautyImages = imageUrls.stream()
                    .filter(url -> StringUtils.startsWith(url, ProductImgUtils.amazonPrefix))
                    .map(ProductImgUtils::getImagePath)
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(checkBeautyImages)) {
                //拼接腾讯云前缀
                List<String> copyImageUrls = checkBeautyImages.stream()
                        .map(path -> TencentCos.Original_Image.getPrefixDomain() + path).collect(Collectors.toList());
                tengcentImageUrls.addAll(copyImageUrls);
            }

            //模板上传图片上传腾讯云
            List<String> checkTemplateImages = imageUrls.stream()
                    .filter(url -> StringUtils.startsWith(url, ProductImgUtils.fruugoTemplate))
                    .map(ProductImgUtils::getImagePath)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(checkTemplateImages)) {
                List<String> uploadImagesUrls = ProductImgUtils.uploadImagToTencent(checkTemplateImages, TencentCos.Fruugo_Image, ProductImgUtils.fruugoTemplate);
                tengcentImageUrls.addAll(uploadImagesUrls);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return tengcentImageUrls;
    }

    @Override
    public void updatePublishStatus(List<Integer> ids, boolean isSuccess, String correlationId) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        FruugoTemplate fruugoTemplate = new FruugoTemplate();
        fruugoTemplate.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        fruugoTemplate.setStatus(isSuccess ? FruugoTemplateStatusEnums.PUBLISHING.getCode() : FruugoTemplateStatusEnums.FAIL.getCode());
        fruugoTemplate.setCorrelationId(correlationId);

        FruugoTemplateExample fruugoTemplateExample = new FruugoTemplateExample();
        fruugoTemplateExample.createCriteria().andIdIn(ids);
        fruugoTemplateExample.setTableName(FruugoTemplateTableEnum.FRUUGO_TEMPLATE.getTableName());
        this.updateByExampleSelective(fruugoTemplate, fruugoTemplateExample);
    }

    private FeedTask savePublishOperationReport(FruugoTemplate fruugoTemplate, String userName) {
        DataContextHolder.setUsername(userName);
        //添加处理报告
        FeedTask feedTask = fruugoFeedTaskService.newTask(String.valueOf(fruugoTemplate.getId()),
                fruugoTemplate.getAccountNumber(),
                FruugoFeedTaskEnum.UPLOAD_LISTING.getCode(),
                fruugoTemplate.getArticleNumber());
        return feedTask;
    }


    @Override
    public Map<String, List<String>> uploadTemplateImage(MultipartFile[] files, String articleNumber) {
        if (null == files || StringUtils.isBlank(articleNumber)) {
            throw new IllegalArgumentException("参数为空");
        }

        String[] extensions = {"jpg", "jpeg", "png", "gif"};
        StringBuilder errorMsg = new StringBuilder();
        Map<String, List<String>> imagesMap = new HashMap<>();
        for (MultipartFile file : files) {
            String extension = POIUtils.getFileExtensionName(file.getOriginalFilename());
            if (ArrayUtils.contains(extensions, extension)) {
                // 使用 FmsUtils 替代 pictureUploadService.uploadPublicPicture
                ApiResult<String> apiResult = FmsUtils.uploadTemplate(articleNumber, file, PicturePlatEnum.FRUUGO_TEMPLATE_PLAT.getName());
                if (!apiResult.isSuccess()) {
                    errorMsg.append(file.getName()).append(",");
                } else {
                    List<String> upload = imagesMap.getOrDefault("upload", new ArrayList<>());
                    upload.add(apiResult.getResult());
                    imagesMap.put("upload", upload);
                }
            }
        }
        if (StringUtils.isNotBlank(errorMsg.toString())) {
            throw new RuntimeException(errorMsg.toString());
        }

        return imagesMap;
    }


    @Override
    public Map<String, String> generateEAN(FruugoBatchGenerateEanNumDto dto) {
        if (ObjectUtils.isEmpty(dto) || CollectionUtils.isEmpty(dto.getSkuList()) || StringUtils.isBlank(dto.getAccount())) {
            throw new BusinessException("参数为空");
        }
        //获取店铺配置
        FruugoAccountConfig fruugoAccountConfig = fruugoAccountConfigService.getByAccountNumber(dto.getAccount());
        if (ObjectUtils.isEmpty(fruugoAccountConfig) || StringUtils.isBlank(fruugoAccountConfig.getEanPrefix())) {
            throw new BusinessException("店铺配置为空或者获取不到EAN前缀");
        }
        HashMap<String, String> map = new HashMap<>();
        for (String sku : dto.getSkuList()) {
            //校验sku是否存在
            ApiResult<Boolean> result = ProductUtils.isExistArticleNumber(sku);
            if (result.isSuccess() && !result.getResult()) {
                throw new BusinessException("sku :" + sku + "商品不存在");
            }
            String ean = FruuugoTemplateDateUtils.generateGtin("EAN", fruugoAccountConfig.getEanPrefix(), sku);
            map.put(sku, ean);
        }
        return map;
    }


    @Override
    public void batchPublish(FruugoBatchPublishDto dto) {
        if (ObjectUtils.isEmpty(dto) || CollectionUtils.isEmpty(dto.getIds()) || StringUtils.isBlank(dto.getAccountNumber())) {
            throw new BusinessException("参数为空");
        }
        List<Integer> code = List.of(FruugoTemplateStatusEnums.PUBLISHING.getCode(), FruugoTemplateStatusEnums.SUCCESS.getCode());
        List<Integer> ids = this.listIdByStatusAndId(dto.getIds(), code);
        dto.getIds().removeAll(ids);
        if (CollectionUtils.isEmpty(dto.getIds())) {
            throw new BusinessException("没有可发布的数据");
        }
        //修改状态为刊登中
        dto.getIds().forEach(id -> {
            FruugoTemplate fruugoTemplate = selectByPrimaryKey(id, false);
            fruugoTemplate.setStatus(FruugoTemplateStatusEnums.PUBLISHING.getCode());
            fruugoTemplate.setUpdateBy(WebUtils.getUserName());
            fruugoTemplate.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            this.updateByPrimaryKeySelective(fruugoTemplate, FruugoTemplateTableEnum.FRUUGO_TEMPLATE.getTableName());

        });
        //发送MQ
        createAndSendMqMsg(dto);
    }

    private void createAndSendMqMsg(FruugoBatchPublishDto dto) {
        for (Integer id : dto.getIds()) {
            BatchPublishParam batchPublishParam = new BatchPublishParam();
            batchPublishParam.setType(FruugoPublishModeEnum.BATCH_PUBLISH.getCode());
            batchPublishParam.setUser(WebUtils.getUserName());
            batchPublishParam.setTemplateId(id);
            batchPublishParam.setAccountNumber(dto.getAccountNumber());
            AutoPublishMessage publishMessage = new AutoPublishMessage(FruugoPublishModeEnum.BATCH_PUBLISH.getCode(), JSON.toJSONString(batchPublishParam));
            rabbitTemplate.convertAndSend(PublishMqConfig.FRUUGO_API_DIRECT_EXCHANGE, PublishQueues.FRUUGO_AUTO_PUBLISH_QUEUE_KEY, publishMessage);

        }
    }

    @Override
    public ApiResult<PageResult<PublishSpuModelDO>> getPublishSpuModelPage(PublishSpuModelRequest request) {
        if (request == null) {
            return ApiResult.newSuccess();
        }
        PageResult<PublishSpuModelDO> result = new PageResult<>();
        int spuSiteTotal = fruugoTemplateMapper.getPublishSpuModelPageTotal(request);
        int totalPages = (int) Math.ceil((double) spuSiteTotal / request.getPageSize());
        result.setTotal(spuSiteTotal);
        result.setCurrent(request.getPageIndex());
        result.setPages(totalPages);
        if (request.getPageIndex() > totalPages) {
            result.setSize(0);
            result.setRecords(Collections.emptyList());
            return ApiResult.newSuccess(result);
        }

        int offset = (request.getPageIndex() - 1) * request.getPageSize();
        List<FruugoTemplate> temuAdminTemplates = fruugoTemplateMapper.getPublishSpuModelPage(request, offset, request.getPageSize());
        List<PublishSpuModelDO> spuModelDOS = temuAdminTemplates.stream().map(template -> {
            PublishSpuModelDO modelDO = new PublishSpuModelDO();
            modelDO.setSpu(template.getArticleNumber());
            modelDO.setPlatform(SaleChannelEnum.FRUUGO.getChannelName());
            modelDO.setSite(request.getSite());
            modelDO.setSkuDataSource(SkuDataSourceEnum.transferCodeBySpu(null, template.getArticleNumber()));
            Boolean isEnable = template.getRemark().contains("1");
            modelDO.setIsEnable(isEnable);
            return modelDO;
        }).collect(Collectors.toList());
        result.setSize(spuModelDOS.size());
        result.setRecords(spuModelDOS);
        return ApiResult.newSuccess(result);
    }


    @Override
    public List<Integer> listIdByStatusAndId(List<Integer> ids, List<Integer> code) {
        FruugoTemplateExample fruugoTemplateExample = new FruugoTemplateExample();
        FruugoTemplateExample.Criteria criteria = fruugoTemplateExample.createCriteria();
        if (CollectionUtils.isNotEmpty(ids)) {
            criteria.andIdIn(ids);
        }
        if (CollectionUtils.isNotEmpty(code)) {
            criteria.andStatusIn(code);
        }
        fruugoTemplateExample.setTableName(FruugoTemplateTableEnum.FRUUGO_TEMPLATE.getTableName());
        List<FruugoTemplate> fruugoTemplates = fruugoTemplateMapper.selectByExample(fruugoTemplateExample);
        if (CollectionUtils.isEmpty(fruugoTemplates)) {
            return Collections.emptyList();
        }
        return fruugoTemplates.stream().map(FruugoTemplate::getId).collect(Collectors.toList());
    }

    @Override
    public void checkExitOnlineList(FruugoTemplateInfoVO templateInfoVO) {
        List<FruugoTemplateProductDetailDto> details = templateInfoVO.getDetails();
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        List<String> skuList = details.stream().map(FruugoTemplateProductDetailDto::getSku).collect(Collectors.toList());
        EsFruugoItemRequest esFruugoItemRequest = new EsFruugoItemRequest();
        esFruugoItemRequest.setSku(skuList);
        esFruugoItemRequest.setAccountNumber(List.of(templateInfoVO.getAccountNumber()));
        List<EsFruugoItem> items = esFruugoItemService.listItemByRequest(esFruugoItemRequest);
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        List<String> onLineSkus = items.stream().map(EsFruugoItem::getSku).collect(Collectors.toList());

        details.removeIf(detail -> onLineSkus.contains(detail.getSku()));
        if (CollectionUtils.isEmpty(details)) {
            throw new BusinessException("当前模板下的商品都已存在该店铺的在线列表");
        }
    }


    @Override
    public void dealSaveProductErrorMsg(FruugoPlatformMqMessage msg, String body) {
        //修改模板状态
        String correlationId = msg.getCorrelationId();
        if (StringUtils.isBlank(correlationId)) {
            return;
        }
        if (ObjectUtils.isEmpty(msg.getPayload()) || ObjectUtils.isEmpty(msg.getPayload().getMerchantProductId())) {
            return;
        }
        String merchantProductId = msg.getPayload().getMerchantProductId();
        updateTemplateStatus(correlationId, merchantProductId, FruugoTemplateStatusEnums.FAIL.getCode());
        //修改操作 报告与定时队列状态
        updateOperationErrorMsgStatus(correlationId, merchantProductId, body);
    }

    @Override
    public void dealSaveProductMsg(FruugoPlatformMqMessage msg, String body) {
        String correlationId = msg.getCorrelationId();
        if (StringUtils.isBlank(correlationId) || ObjectUtils.isEmpty(msg.getPayload())) {
            return;
        }
        String merchantProductId = msg.getPayload().getMerchantProductId();
        if (StringUtils.isBlank(merchantProductId)) {
            return;
        }
        //刊登结果状态
        Boolean productCreated = false;
        if (msg.getPayload().getProductCreated()) {
            productCreated = true;
        }

        Integer templateStatus = productCreated ? FruugoTemplateStatusEnums.SUCCESS.getCode() : FruugoTemplateStatusEnums.FAIL.getCode();
        //修改模板状态
        FruugoTemplate fruugoTemplate = updateTemplateStatus(correlationId, merchantProductId, templateStatus);
        if (ObjectUtils.isEmpty(fruugoTemplate)) {
            log.error("修改模板状态失败,未找到相关模板，关联id为:{}", correlationId);
            throw new BusinessException("修改模板状态失败,未找到相关模板，关联id为:" + correlationId);
        }

        Integer feedTaskStatus = productCreated ? FeedTaskResultStatusEnum.SUCCESS.getResultStatus() : FeedTaskResultStatusEnum.FAIL.getResultStatus();

        String errorMsgStr = productCreated ? null : body;

        //修改处理报告状态
        List<FeedTask> listByExample = getFeedTasksByCorrelationId(correlationId, merchantProductId, fruugoTemplate.getId());
        if (CollectionUtils.isNotEmpty(listByExample)) {
            FeedTask feedTask = listByExample.get(0);
            feedTask.setResultStatus(feedTaskStatus);
            updateFeedTaskStatus(feedTask, errorMsgStr);

            //如果存在队列需修改队列数据
            if (StringUtils.isNotBlank(feedTask.getAttribute3())) {
                String queueId = feedTask.getAttribute3();
                this.updateTimePublishQueueStatus(queueId, FruugoTemplateStatusEnums.FAIL.getCode());
            }
        } else {
            log.error("修改处理报告状态失败,未找到相关处理报告，关联id为:{},merchantProductId:{}", correlationId, merchantProductId);
            throw new BusinessException("修改处理报告状态失败,未找到相关处理报告，关联id为:" + correlationId + ",merchantProductId:" + merchantProductId);
        }

        //生成范本或者修改范本
        if (productCreated) {
            FruugoTemplateExample fruugoTemplateExample = new FruugoTemplateExample();
            fruugoTemplateExample.createCriteria().andArticleNumberEqualTo(fruugoTemplate.getArticleNumber())
                    .andCategoryNameEqualTo(fruugoTemplate.getCategoryName());
            fruugoTemplateExample.setTableName(FruugoTemplateTableEnum.FRUUGO_TEMPLATE_MODEL.getTableName());
            List<FruugoTemplate> fruugoTemplates = this.selectByExample(fruugoTemplateExample);
            if (CollectionUtils.isEmpty(fruugoTemplates)) {
                //添加
                fruugoTemplate.setTemplateStatus(1);
                fruugoTemplate.setTableName(FruugoTemplateTableEnum.FRUUGO_TEMPLATE_MODEL.getTableName());
                this.insert(fruugoTemplate, fruugoTemplate.getTableName());
            } else {
                FruugoTemplate adminTemplate = fruugoTemplates.get(0);
                fruugoTemplate.setTableName(FruugoTemplateTableEnum.FRUUGO_TEMPLATE_MODEL.getTableName());
                this.updateByPrimaryKeySelective(adminTemplate, fruugoTemplate.getTableName());
            }

            //异步同步在线列表
            CompletableFuture.runAsync(() -> {
                fruugoSyncItemService.sysncItemByNewAccountAndSpu(fruugoTemplate.getAccountNumber(), fruugoTemplate.getProductId());
            }, FruugoExecutors.SYNC_ITEM_INFO_POOL);
        }


    }

    private void updateOperationErrorMsgStatus(String correlationId, String merchantProductId, String body) {
        List<FeedTask> listByExample = getFeedTasksByCorrelationId(correlationId, merchantProductId, null);
        if (CollectionUtils.isNotEmpty(listByExample)) {
            FeedTask feedTask = listByExample.get(0);
            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
            updateFeedTaskStatus(feedTask, body);

            //如果存在队列需修改队列数据
            if (StringUtils.isNotBlank(feedTask.getAttribute3())) {
                String queueId = feedTask.getAttribute3();
                this.updateTimePublishQueueStatus(queueId, FruugoTemplateStatusEnums.FAIL.getCode());
            }

        }
    }


    private List<FeedTask> getFeedTasksByCorrelationId(String correlationId, String merchantProductId, Integer templateId) {
        FeedTaskExample feedTaskExample = new FeedTaskExample();
        FeedTaskExample.Criteria criteria = feedTaskExample.createCriteria();
        criteria.andTaskTypeEqualTo(FruugoFeedTaskEnum.UPLOAD_LISTING.getCode())
                .andAttribute4EqualTo(correlationId);

        if (StringUtils.isNotBlank(merchantProductId)) {
            criteria.andAssociationIdEqualTo(merchantProductId);
        }
        if (ObjectUtils.isNotEmpty(templateId)) {
            criteria.andAttribute5EqualTo(String.valueOf(templateId));
        }
        log.info("根据关联id查询FeedTask,关联id为:{},merchantProductId:{},模板ID{}", correlationId, merchantProductId, templateId);
        List<FeedTask> listByExample = fruugoFeedTaskService.getListByExample(feedTaskExample);
        return listByExample;
    }

    private void updateFeedTaskStatus(FeedTask feedTask, String errorMsgStr) {
        feedTask.setResultMsg(errorMsgStr);
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setTableIndex();
        fruugoFeedTaskService.updateByPrimaryKeySelective(feedTask);
    }

    private void updateTimePublishQueueStatus(String queueId, Integer code) {
        FruugoTimePublishQueue fruugoTimePublishQueue = new FruugoTimePublishQueue();
        fruugoTimePublishQueue.setId(Integer.valueOf(queueId));
        fruugoTimePublishQueue.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        fruugoTimePublishQueue.setPublishStatus(code);
        fruugoTimePublishQueue.setStatus(FruugoTimePublishEnums.END.getCode());
        fruugoTimePublishQueueService.updateByPrimaryKeySelective(fruugoTimePublishQueue);

    }

    private FruugoTemplate updateTemplateStatus(String correlationId, String merchantProductId, Integer code) {
        FruugoTemplateExample fruugoTemplateExample = new FruugoTemplateExample();
        fruugoTemplateExample.createCriteria()
                .andCorrelationIdEqualTo(correlationId)
                .andProductIdEqualTo(merchantProductId);
        fruugoTemplateExample.setTableName(FruugoTemplateTableEnum.FRUUGO_TEMPLATE.getTableName());
        List<FruugoTemplate> fruugoTemplates = this.selectByExample(fruugoTemplateExample);
        if (CollectionUtils.isNotEmpty(fruugoTemplates)) {
            FruugoTemplate fruugoTemplate = fruugoTemplates.get(0);
            fruugoTemplate.setTableName(FruugoTemplateTableEnum.FRUUGO_TEMPLATE.getTableName());
            fruugoTemplate.setStatus(code);
            fruugoTemplate.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            this.updateByPrimaryKeySelective(fruugoTemplate, FruugoTemplateTableEnum.FRUUGO_TEMPLATE.getTableName());
            return fruugoTemplate;
        }
        return null;
    }


    @Override
    public void dealUpdateErrorProductMsg(FruugoPlatformMqMessage msg, String body) {
        String correlationId = msg.getCorrelationId();
        if (StringUtils.isBlank(correlationId) || ObjectUtils.isEmpty(msg.getPayload()) ||
                StringUtils.isBlank(msg.getPayload().getMerchantProductId())) {
            return;
        }

        List<FeedTask> listByExample = getFeedTasksByCorrelationId(correlationId, null, null);
        if (CollectionUtils.isNotEmpty(listByExample)) {
            fruugoFeedTaskService.batchFailTask(listByExample, body);
        }
    }


    @Override
    public void updateReadyTime(String accountNumber, List<FruugoTemplate> fruugoTemplateList, Integer readyTime) {
        if (CollectionUtils.isEmpty(fruugoTemplateList)) {
            return;
        }
        //按100分批处理
        List<List<FruugoTemplate>> batchList = Lists.partition(fruugoTemplateList, 100);
        batchList.forEach(fruugoTemplates -> {
            //获取在线列表比对sellerSku
            List<String> productIds = fruugoTemplates.stream().map(FruugoTemplate::getArticleNumber).collect(Collectors.toList());
            Map<String, EsFruugoItem> map = fruugoItemService.findProductMapByAccountAndProductId(productIds, accountNumber);

            fruugoTemplates.forEach(fruugoTemplate -> {
                //创建处理报告
                FeedTask feedTask = savePublishUpdateReport(fruugoTemplate);
                try {
                    //提交创建修改保存中间表
                    FruugoTemplateInfoVO templateInfoVO = FruugoConvertDto.toFruugoTemplateInfoVO(fruugoTemplate);
                    List<FruugoTemplateProductDetailDto> details = templateInfoVO.getDetails();
                    for (FruugoTemplateProductDetailDto detail : details) {
                        EsFruugoItem esFruugoItem = map.get(templateInfoVO.getArticleNumber() + "-" + detail.getSku());
                        if (ObjectUtils.isEmpty(esFruugoItem)) {
                            throw new BusinessException("获取不到在线列表数据");
                        }
                        detail.setSellerSku(esFruugoItem.getSellerSku());
                        //修改准备时间为null
                        if (ObjectUtils.isEmpty(readyTime)) {
                            detail.setReadyTimeAttribute(null);
                        } else {
                            detail.setReadyTimeAttribute(readyTime);
                        }
                    }
                    //替换图片为腾讯云链接
                    this.replaceImage(templateInfoVO);
                    //保存中间表
                    this.saveBatchPublishUpdate(templateInfoVO);
                } catch (Exception e) {
                    fruugoFeedTaskService.failTask(feedTask, e.getMessage());
                }
            });
        });

    }

    private void saveBatchPublishUpdate(FruugoTemplateInfoVO template) {
        FruugoBatchPublishUpdate fruugoBatchPublish = new FruugoBatchPublishUpdate();
        fruugoBatchPublish.setAccount(template.getAccountNumber());
        fruugoBatchPublish.setTemplateId(template.getId());
        fruugoBatchPublish.setPublishStatus(0);
        fruugoBatchPublish.setCreateTime(new Timestamp(System.currentTimeMillis()));
        fruugoBatchPublish.setPublishInfo(JSON.toJSONString(template));
        fruugoBatchPublishUpdateService.insert(fruugoBatchPublish);
    }


    private FeedTask savePublishUpdateReport(FruugoTemplate fruugoTemplate) {
        //添加处理报告,attribute5存模板id
        String[] s = new String[5];
        s[4] = String.valueOf(fruugoTemplate.getId());
        FeedTask feedTask = fruugoFeedTaskService.newTask(fruugoTemplate.getArticleNumber(),
                fruugoTemplate.getAccountNumber(),
                FruugoFeedTaskEnum.UPDATE_READY_TIME.getCode(),
                null,
                s);
        return feedTask;
    }
}