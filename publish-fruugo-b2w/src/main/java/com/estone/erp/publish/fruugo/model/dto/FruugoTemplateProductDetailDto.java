package com.estone.erp.publish.fruugo.model.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
@Data
public class FruugoTemplateProductDetailDto {

    /**
     *sellerSKU
     */
    private String sellerSku;

    /**
     * sku
     */
    private String sku;


    /**
     * gtinValue
     */
    private String gtinValue;

    /**
     * 库存
     */
    private Integer inventory;


    /**
     * 价格
     */
    private String price;

    /**
     * 重量（g）
     */
    private Double weight;

    /**
     * 体积(cm3)
     */
    private String volume;

    /**
     * 标题
     */
    private String title;

    /**
     * 描述
     */
    private String description;


    /**
     * GTIN名称
     */
    private String gtinName;

    /**
     * 库存附属信息
     */
    private String inventoryAttribute;

    /**
     * 准备时间
     */
    private Integer readyTimeAttribute;

    /**
     * 重新订货时间
     */
    private String reorderTime;

    /**
     * 币种
     */
    private String currency;

    /**
     * 是否含税，0-否，1-是
     */
    private Integer taxInclusive;

    /**
     * 属性
     */
    private List<Attribute> attributes;

    /**
     * 促销币种
     */
    private String actionCurrency;

    /**
     *促销价格
     */
    private String actionPrice;

    /**
     * 促销价格是否含税，0-否，1-是
     */
    private Integer actionPriceTaxInclusive;

    /**
     * 促销开始时间,格式：YYYY-MM-DD
     */
    private String actionPriceStartTime;

    /**
     * 促销结束时间,格式：YYYY-MM-DD
     */
    private String actionPriceEndTime;


    @Data
    public static class Attribute {
        private String name;
        private String value;
    }


}


