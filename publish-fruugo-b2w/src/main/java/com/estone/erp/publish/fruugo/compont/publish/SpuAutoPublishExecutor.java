package com.estone.erp.publish.fruugo.compont.publish;

import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.fruugo.compont.publish.param.SpuPublishParam;
import com.estone.erp.publish.fruugo.compont.template.FruugoTemplateBuilderHandler;
import com.estone.erp.publish.fruugo.enums.FruugoPublishModeEnum;
import com.estone.erp.publish.fruugo.enums.FruugoTemplateStatusEnums;
import com.estone.erp.publish.fruugo.enums.FruugoTemplateTableEnum;
import com.estone.erp.publish.fruugo.enums.FruugoTimePublishEnums;
import com.estone.erp.publish.fruugo.model.FruugoTemplate;
import com.estone.erp.publish.fruugo.model.FruugoTimePublishQueue;
import com.estone.erp.publish.fruugo.model.dto.BuilderTemplateDO;
import com.estone.erp.publish.fruugo.model.dto.FruugoConvertDto;
import com.estone.erp.publish.fruugo.model.vo.FruugoTemplateInfoVO;
import com.estone.erp.publish.fruugo.service.FruugoFeedTaskService;
import com.estone.erp.publish.fruugo.service.FruugoTemplateService;
import com.estone.erp.publish.fruugo.service.FruugoTimePublishQueueService;
import com.estone.erp.publish.fruugo.util.FruuugoTemplateDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.concurrent.CompletionException;

@Slf4j
@Component
public class SpuAutoPublishExecutor extends PublishExecutor<SpuPublishParam> {

    @Resource
    private FruugoTemplateBuilderHandler fruugoTemplateBuilderHandler;

    @Resource
    private FruugoTimePublishQueueService timePublishQueueService;
    @Resource
    private FruugoTemplateService fruugoTemplateService;

    @Resource
    private FruugoFeedTaskService fruugoFeedTaskService;

    @Override
    protected FruugoTemplate getTemplateData(SpuPublishParam param) throws BusinessException {
        try {
            DataContextHolder.setUsername(param.getUser());

            //生成模板
            BuilderTemplateDO builderTemplateDO = new BuilderTemplateDO();
            builderTemplateDO.setPublishType(param.getPublishType());
            builderTemplateDO.setTemplateId(param.getTemplateId());
            builderTemplateDO.setSkuDataSource(param.getSkuDataSource());
            builderTemplateDO.setAccountNumber(param.getAccountNumber());
            builderTemplateDO.setArticleNumber(param.getSpu());
            builderTemplateDO.setGeneratedByAutoPublish(param.getGeneratedByAutoPublish());

            FruugoTemplateInfoVO templateInfoVO = fruugoTemplateBuilderHandler.builderTemplate(builderTemplateDO);
            if (ObjectUtils.isEmpty(templateInfoVO)){
                throw new BusinessException("生成模板为空");
            }

            //校验模板
            fruugoTemplateService.checkTemplateInfo(templateInfoVO);

            FruugoTemplate templateModel = FruugoConvertDto.toFruugoTemplate(templateInfoVO);
            templateModel.setCreatedBy(DataContextHolder.getUsername());
            templateModel.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
            templateModel.setUpdateBy(DataContextHolder.getUsername());
            templateModel.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
            templateModel.setStatus(FruugoTemplateStatusEnums.PUBLISHING.getCode());
            templateModel.setTemplateStatus(1);
            fruugoTemplateService.insert(templateModel, FruugoTemplateTableEnum.FRUUGO_TEMPLATE.getTableName());

            if (param.getPublishType().equals(FruugoPublishModeEnum.TIME_PUBLISH.getCode())){
                templateModel.setQueueId(param.getTimePublishQueueId());
                // 更新定时刊登信息
                Integer queueId = param.getTimePublishQueueId();
                FruugoTimePublishQueue queue = new FruugoTimePublishQueue();
                queue.setId(queueId);
                queue.setTemplateId(templateModel.getId());
                queue.setTitle(templateModel.getTitle());
                queue.setPublishStatus(FruugoTemplateStatusEnums.PUBLISHING.getCode());
                queue.setStatus(FruugoTimePublishEnums.PROCESSING.getCode());
                queue.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
                timePublishQueueService.updateByPrimaryKeySelective(queue);
            }
            return templateModel;
        } catch (Exception e) {
            log.error("执行定时刊登任务失败", e);
            String errorMsg = null;
            if (e instanceof CompletionException) {
                errorMsg = e.getCause().getMessage();
            } else {
                errorMsg = e.getMessage();
            }
            failFeedTask(param.getTemplateId(), param.getAccountNumber(), param.getSpu(),errorMsg);
            if (param.getPublishType().equals(FruugoPublishModeEnum.TIME_PUBLISH.getCode())){
               this.updatePublishStatus(param,errorMsg);
            }
        }finally {
            FruuugoTemplateDateUtils.deleteLock(param.getAccountNumber(), param.getSpu());
        }
        return null;

    }



    private void updatePublishStatus(SpuPublishParam param, String message) {
        FruugoTimePublishQueue queue = new FruugoTimePublishQueue();
        queue.setId(param.getTimePublishQueueId());
        queue.setStatus(FruugoTimePublishEnums.END.getCode());
        queue.setPublishStatus(FruugoTemplateStatusEnums.FAIL.getCode());
        queue.setExtra(message);
        queue.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
        timePublishQueueService.updateByPrimaryKeySelective(queue);

    }
}