package com.estone.erp.publish.fruugo.compont.publish;

import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.fruugo.compont.publish.param.TemplatePublishParam;
import com.estone.erp.publish.fruugo.model.FruugoTemplate;
import com.estone.erp.publish.fruugo.model.dto.FruugoConvertDto;
import com.estone.erp.publish.fruugo.model.vo.FruugoTemplateInfoVO;
import com.estone.erp.publish.fruugo.service.FruugoTemplateService;
import com.estone.erp.publish.fruugo.util.FruuugoTemplateDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.CompletionException;

@Slf4j
@Component
public class TemplatePublishExecutor extends PublishExecutor<TemplatePublishParam> {

    @Resource
    private FruugoTemplateService fruugoTemplateService;

    @Override
    protected FruugoTemplate getTemplateData(TemplatePublishParam param) throws BusinessException {
        String accountNumber = null;
        String spu = null;
        Integer templateId = param.getTemplateId();
        try {
            DataContextHolder.setUsername(param.getUser());
            FruugoTemplate fruugoTemplate = fruugoTemplateService.selectByPrimaryKey(templateId, false);
            //检查侵权词
            FruugoTemplateInfoVO fruugoTemplateInfoVO = FruugoConvertDto.toFruugoTemplateInfoVO(fruugoTemplate);
            if (null == fruugoTemplateInfoVO) {
                throw new BusinessException(String.format("templateId为%s的模板被删除", templateId));
            }
            accountNumber = fruugoTemplateInfoVO.getAccountNumber();
            spu = fruugoTemplateInfoVO.getArticleNumber();
            Map<String, String> stringIntegerMap = FruuugoTemplateDateUtils.checkInfringmentWord(fruugoTemplateInfoVO);
            if (MapUtils.isNotEmpty(stringIntegerMap) && (stringIntegerMap.containsKey("品牌") || stringIntegerMap.containsKey("属性"))) {
                StringBuilder errorMsg = new StringBuilder("存在侵权词：");
                if (stringIntegerMap.containsKey("品牌")) {
                    errorMsg.append("品牌：" + stringIntegerMap.get("品牌"));
                }
                if (stringIntegerMap.containsKey("属性")) {
                    errorMsg.append("属性：" + stringIntegerMap.get("属性"));
                }
                throw new BusinessException(errorMsg.toString());
            }
            //校验模板
            fruugoTemplateService.checkTemplateInfo(fruugoTemplateInfoVO);
            // 检查产品类目是否匹配店铺配置选择的刊登类目
            checkProductCanPublishCategory(fruugoTemplateInfoVO.getAccountNumber(), fruugoTemplateInfoVO.getDetails());
            return fruugoTemplate;
        } catch (Exception e) {
            log.error("执行刊登任务失败", e);
            String errorMsg = null;
            if (e instanceof CompletionException) {
                errorMsg = e.getCause().getMessage();
            } else {
                errorMsg = e.getMessage();
            }
            failFeedTask(param.getTemplateId(), accountNumber, spu, errorMsg);
            fruugoTemplateService.updatePublishStatus(Collections.singletonList(templateId), false, null);
        }
        return null;
    }
}