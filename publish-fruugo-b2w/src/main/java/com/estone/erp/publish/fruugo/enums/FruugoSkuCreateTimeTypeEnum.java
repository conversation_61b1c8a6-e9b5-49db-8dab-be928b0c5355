package com.estone.erp.publish.fruugo.enums;

import lombok.Getter;

/**
 * Fruugo店铺配置sku录入时间类型枚举
 * sku录入时间类型：1 月、2 年
 * 
 * <AUTHOR>
 * @date 2025-01-25
 */
@Getter
public enum FruugoSkuCreateTimeTypeEnum {
    MONTH(1, "月"),
    YEAR(2, "年"),
    ;

    private Integer code;
    private String name;

    private FruugoSkuCreateTimeTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public boolean isTrue(Integer val) {
        return this.code.equals(val);
    }
}
