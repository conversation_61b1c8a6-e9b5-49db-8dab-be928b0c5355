package com.estone.erp.publish.fruugo.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.executors.FruugoExecutors;
import com.estone.erp.publish.fruugo.model.FruugoAccountConfig;
import com.estone.erp.publish.fruugo.model.FruugoAccountConfigCriteria;
import com.estone.erp.publish.fruugo.model.dto.FruugoBatchSetReadyTimeDto;
import com.estone.erp.publish.fruugo.model.vo.FruugoAccountConfigPageVO;
import com.estone.erp.publish.fruugo.model.vo.FruugoAccountConfigVO;
import com.estone.erp.publish.fruugo.service.FruugoAccountConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> fruugo_account_config
 * 2023-08-06 20:12:41
 */
@Slf4j
@RestController
@RequestMapping("fruugoAccountConfig")
public class FruugoAccountConfigController {
    @Resource
    private FruugoAccountConfigService fruugoAccountConfigService;

    @PostMapping
    public ApiResult<?> postFruugoAccountConfig(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchFruugoAccountConfig":
                    CQuery<FruugoAccountConfigCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<FruugoAccountConfigCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<FruugoAccountConfigPageVO> results = fruugoAccountConfigService.search(cquery);
                    return results;
                case "addFruugoAccountConfig":
                    FruugoAccountConfig fruugoAccountConfig = requestParam.getArgsValue(new TypeReference<FruugoAccountConfig>() {});
                    fruugoAccountConfigService.insert(fruugoAccountConfig);
                    return ApiResult.newSuccess(fruugoAccountConfig);
                }
        }
        return ApiResult.newSuccess();
    }

    /**
     * 修改店铺配置
     * @param accountConfig ac
     */
    @PostMapping("update")
    public ApiResult<String> updateAccountConfig(@RequestBody FruugoAccountConfig accountConfig) {
        try {
            return fruugoAccountConfigService.updateAccountConfig(accountConfig);
        } catch (Exception e) {
            log.error("updateAccountConfig error", e);
            return ApiResult.newError("修改店铺配置错误:"+e.getMessage());
        }
    }

    /**
     * 同步店铺配置
     */
    @PostMapping("sync")
    public ApiResult<String> syncAccountConfig(@RequestBody List<String> accountNumbers) {
        FruugoExecutors.executeSyncAccountConfig(() -> {
            fruugoAccountConfigService.syncAccountConfig(accountNumbers);
        });
        return ApiResult.newSuccess("同步成功");
    }

    /**
     * 批量设置准备时间
     */
    @PostMapping("batchSetReadyTime")
    public ApiResult<String> batchSetReadyTime(@RequestBody @Valid FruugoBatchSetReadyTimeDto dto) {
        fruugoAccountConfigService.batchSetReadyTime(dto);
        return ApiResult.newSuccess("设置成功");
    }

    /**
     * 根据ID获取Fruugo店铺配置信息(最新)
     *
     * @param id 配置ID
     * @return 店铺配置信息
     */
    @GetMapping("/getConfigInfoById")
    public ApiResult<FruugoAccountConfigVO> getConfigInfoById(@RequestParam(name = "id", defaultValue = "-1") Integer id) {
        // 验证用户登录状态
        String userName = WebUtils.getUserName();
        if (StringUtils.isEmpty(userName)) {
            return ApiResult.newError("请重新登陆");
        }
        // 查询配置信息
        FruugoAccountConfig dbConfig = fruugoAccountConfigService.selectByPrimaryKey(id);
        FruugoAccountConfigVO vo = new FruugoAccountConfigVO();

        if (dbConfig == null) {
            // 如果没有找到配置，返回空的配置对象
            return ApiResult.newSuccess(vo);
        }
        BeanUtils.copyProperties(dbConfig, vo);

        return ApiResult.newSuccess(vo);
    }

    /**
     * 保存Fruugo店铺配置
     *
     * @param saveParam 保存参数
     * @return 保存结果
     */
    @PostMapping("/saveConfig")
    public ApiResult<?> saveConfig(@RequestBody FruugoAccountConfigVO saveParam) {
        if (saveParam == null) {
            return ApiResult.newError("请求参数为空");
        }

        // 验证用户登录状态
        String userName = WebUtils.getUserName();
        if (StringUtils.isBlank(userName)) {
            return ApiResult.newError("登录失效");
        }

        // 调用Service层的saveConfig方法
        fruugoAccountConfigService.saveConfig(saveParam);

        return ApiResult.newSuccess();
    }

}