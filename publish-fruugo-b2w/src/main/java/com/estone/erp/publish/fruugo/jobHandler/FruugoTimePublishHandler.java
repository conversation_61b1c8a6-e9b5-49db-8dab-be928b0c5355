package com.estone.erp.publish.fruugo.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.fruugo.compont.publish.param.SpuPublishParam;
import com.estone.erp.publish.fruugo.enums.FruugoPublishModeEnum;
import com.estone.erp.publish.fruugo.model.FruugoTimePublishQueue;
import com.estone.erp.publish.fruugo.mq.param.AutoPublishMessage;
import com.estone.erp.publish.fruugo.service.FruugoTimePublishQueueService;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * fruugo 定时刊登
 */
@Slf4j
@Component
public class FruugoTimePublishHandler extends AbstractJobHandler {

    @Resource
    private FruugoTimePublishQueueService fruugoTimePublishQueueService;

    @Resource
    private RabbitTemplate rabbitTemplate;

    public FruugoTimePublishHandler() {
        super(FruugoTimePublishHandler.class.getName());
    }

    @Data
    static class InnerParam {
        private List<String> accountNumbers;
        private List<String> spuList;
    }
    @Override
    @XxlJob("FruugoTimePublishHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        List<String> accountNumbers = new ArrayList<>();
        List<String> spuList = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(innerParam)) {
            if (CollectionUtils.isNotEmpty(innerParam.getAccountNumbers())){
                accountNumbers = innerParam.getAccountNumbers();
            }
            if (CollectionUtils.isNotEmpty(innerParam.getSpuList())){
                spuList= innerParam.getSpuList();
            }
        }

        List<FruugoTimePublishQueue> canPublishList = fruugoTimePublishQueueService.getCanPublishList(accountNumbers, spuList);
        if (CollectionUtils.isEmpty(canPublishList)){
            XxlJobLogger.log("没有需要定时刊登的数据");
            return ReturnT.SUCCESS;
        }

        for (FruugoTimePublishQueue fruugoTimePublishQueue : canPublishList) {
            SpuPublishParam spuPublishParam = new SpuPublishParam();
            spuPublishParam.setPublishType(FruugoPublishModeEnum.TIME_PUBLISH.getCode());
            spuPublishParam.setSpu(fruugoTimePublishQueue.getArticleNumber());
            spuPublishParam.setUser(fruugoTimePublishQueue.getCreatedBy());
            spuPublishParam.setAccountNumber(fruugoTimePublishQueue.getAccountNumber());
            spuPublishParam.setSkuDataSource(fruugoTimePublishQueue.getSkuDataSource());
            spuPublishParam.setTimePublishQueueId(fruugoTimePublishQueue.getId());
            spuPublishParam.setGeneratedByAutoPublish(fruugoTimePublishQueue.getGeneratedByAutoPublish());
            AutoPublishMessage publishMessage = new AutoPublishMessage(FruugoPublishModeEnum.TIME_PUBLISH.getCode(), JSON.toJSONString(spuPublishParam));
            rabbitTemplate.convertAndSend(PublishMqConfig.FRUUGO_API_DIRECT_EXCHANGE, PublishQueues.FRUUGO_AUTO_PUBLISH_QUEUE_KEY, publishMessage);
        }


        return ReturnT.SUCCESS;


    }
}
