package com.estone.erp.publish.fruugo.compont.publish;

import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.fruugo.compont.publish.param.BatchPublishParam;
import com.estone.erp.publish.fruugo.compont.template.FruugoTemplateBuilderHandler;
import com.estone.erp.publish.fruugo.enums.FruugoPublishModeEnum;
import com.estone.erp.publish.fruugo.enums.FruugoTemplateStatusEnums;
import com.estone.erp.publish.fruugo.enums.FruugoTemplateTableEnum;
import com.estone.erp.publish.fruugo.model.FruugoTemplate;
import com.estone.erp.publish.fruugo.model.dto.BuilderTemplateDO;
import com.estone.erp.publish.fruugo.model.dto.FruugoConvertDto;
import com.estone.erp.publish.fruugo.model.vo.FruugoTemplateInfoVO;
import com.estone.erp.publish.fruugo.service.FruugoFeedTaskService;
import com.estone.erp.publish.fruugo.service.FruugoTemplateService;
import com.estone.erp.publish.fruugo.util.FruuugoTemplateDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.concurrent.CompletionException;

@Slf4j
@Component
public class BatchTemplatePublishExecutor extends PublishExecutor<BatchPublishParam> {

    @Resource
    private FruugoFeedTaskService fruugoFeedTaskService;

    @Resource
    private FruugoTemplateService fruugoTemplateService;

    @Resource
    private FruugoTemplateBuilderHandler fruugoTemplateBuilderHandler;

    @Override
    protected FruugoTemplate getTemplateData(BatchPublishParam param) throws BusinessException {
        DataContextHolder.setUsername(param.getUser());
        FruugoTemplate fruugoTemplate = fruugoTemplateService.selectByPrimaryKey(param.getTemplateId(), false);
        fruugoTemplate.setAccountNumber(param.getAccountNumber());
        if (ObjectUtils.isEmpty(fruugoTemplate)){
            failFeedTask(param.getTemplateId(), param.getAccountNumber(), "", "批量刊登查询模板数据为空");
            return null;
        }
        FruugoTemplate model = null;
        try {
            //校验模板
            fruugoTemplateService.checkTemplateInfo(FruugoConvertDto.toFruugoTemplateInfoVO(fruugoTemplate));
            //批量刊登需要重新生成模板
            BuilderTemplateDO builderTemplateDO = new BuilderTemplateDO();
            builderTemplateDO.setPublishType(FruugoPublishModeEnum.BATCH_PUBLISH.getCode());
            builderTemplateDO.setTemplateId(param.getTemplateId());
            builderTemplateDO.setSkuDataSource(fruugoTemplate.getProductType());
            builderTemplateDO.setAccountNumber(param.getAccountNumber());
            builderTemplateDO.setArticleNumber(fruugoTemplate.getArticleNumber());
            FruugoTemplateInfoVO templateInfoVO = fruugoTemplateBuilderHandler.builderTemplate(builderTemplateDO);
            //修改模板数据
            model = FruugoConvertDto.toFruugoTemplate(templateInfoVO);
            model.setId(fruugoTemplate.getId());
            model.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            fruugoTemplateService.updateByPrimaryKeySelective(model, FruugoTemplateTableEnum.FRUUGO_TEMPLATE.getTableName());
            return model;
        } catch (Exception e) {
            log.error("批量刊登模板数据异常:{}", e);
            String errorMsg = null;
            if (e instanceof CompletionException) {
                errorMsg = e.getCause().getMessage();
            } else {
                errorMsg = e.getMessage();
            }
            fruugoTemplate.setStatus(FruugoTemplateStatusEnums.FAIL.getCode());
            fruugoTemplate.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            fruugoTemplate.setUpdateBy(DataContextHolder.getUsername());
            fruugoTemplateService.updateByPrimaryKeySelective(fruugoTemplate, FruugoTemplateTableEnum.FRUUGO_TEMPLATE.getTableName());

            failFeedTask(param.getTemplateId(), param.getAccountNumber(), fruugoTemplate.getArticleNumber(), errorMsg);

           return null;
        }finally {
            FruuugoTemplateDateUtils.deleteLock(param.getAccountNumber(), fruugoTemplate.getArticleNumber());
        }

    }

}