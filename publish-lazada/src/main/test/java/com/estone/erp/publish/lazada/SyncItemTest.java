package com.estone.erp.publish.lazada;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.PublishLazadaApplication;
import com.estone.erp.publish.elasticsearch2.model.EsLazadaItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsLazadaItemRequest;
import com.estone.erp.publish.elasticsearch2.service.EsLazadaItemService;
import com.estone.erp.publish.lazada.constant.LazadaPublishRouteApi;
import com.estone.erp.publish.lazada.enums.LazadaGetProductFilterEnum;
import com.estone.erp.publish.lazada.lazop.api.LazopClient;
import com.estone.erp.publish.lazada.lazop.api.LazopRequest;
import com.estone.erp.publish.lazada.lazop.api.LazopResponse;
import com.estone.erp.publish.lazada.lazop.model.LazadaProductPull;
import com.estone.erp.publish.lazada.lazop.util.Constants;
import com.estone.erp.publish.lazada.lazop.util.LazopResult;
import com.estone.erp.publish.lazada.model.LazadaNoSaleOrderOfflineLog;
import com.estone.erp.publish.lazada.model.LazadaNoSaleOrderOfflineLogExample;
import com.estone.erp.publish.lazada.service.LazadaNoSaleOrderOfflineLogService;
import com.estone.erp.publish.lazada.util.LazadaHttpClientUtil;
import com.estone.erp.publish.lazada.util.LazadaPullPage;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.estone.erp.publish.elasticsearch2.model.beanrequest.EsLazadaItemRequest.TASK_JOB_FIELDS;

/**
 * <AUTHOR>
 * @date 2024-01-30 10:41
 */
@Slf4j
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PublishLazadaApplication.class)
public class SyncItemTest {
//
//    @Autowired
//    private LazadaGetProductsCall lazadaGetProductsCall;

    @Autowired
    private EsLazadaItemService esLazadaItemService;
    @Autowired
    private LazadaNoSaleOrderOfflineLogService lazadaNoSaleOrderOfflineLogService;


    @Test
    public void checkDownloadItem() {
        int total = 0;
        int startId = 0;
        int updateTotal = 0;

        while (true) {
            LazadaNoSaleOrderOfflineLogExample example = new LazadaNoSaleOrderOfflineLogExample();
            example.createCriteria().andIdGreaterThan(startId);
            example.setOrderByClause("id asc");
            example.setLimit(500);
            List<LazadaNoSaleOrderOfflineLog> lazadaNoSaleOrderOfflineLogs = lazadaNoSaleOrderOfflineLogService.selectByExample(example);
            if (CollectionUtils.isEmpty(lazadaNoSaleOrderOfflineLogs)) {
                break;
            }
            startId = lazadaNoSaleOrderOfflineLogs.get(lazadaNoSaleOrderOfflineLogs.size() - 1).getId();
            total += lazadaNoSaleOrderOfflineLogs.size();


            List<Long> itemIds = lazadaNoSaleOrderOfflineLogs.stream().map(LazadaNoSaleOrderOfflineLog::getItemId).collect(Collectors.toList());
            EsLazadaItemRequest esLazadaItemRequest = new EsLazadaItemRequest();
            // 总销量为0或空
            esLazadaItemRequest.setFromOrderNumTotal(1);
            if (CollectionUtils.isNotEmpty(itemIds)) {
                esLazadaItemRequest.setItemIdList(itemIds);
            }
            esLazadaItemRequest.setFields(TASK_JOB_FIELDS);
            List<EsLazadaItem> esLazadaItems = esLazadaItemService.listItemExample(esLazadaItemRequest);
            if (CollectionUtils.isEmpty(esLazadaItems)) {
                continue;
            }
            Map<String, EsLazadaItem> esItemMap = esLazadaItems.stream().collect(Collectors.toMap(EsLazadaItem::getId, Function.identity()));

            for (LazadaNoSaleOrderOfflineLog offlineLog : lazadaNoSaleOrderOfflineLogs) {
                String key = offlineLog.getAccountNumber() + "_" + offlineLog.getItemId() + "_" + offlineLog.getSkuId();
                EsLazadaItem esLazadaItem = esItemMap.get(key);
                if (esLazadaItem == null) {
                    continue;
                }
                offlineLog.setStatus(esLazadaItem.getStatus());
                offlineLog.setOrderTotalNumber(esLazadaItem.getOrderNumTotal());
                lazadaNoSaleOrderOfflineLogService.updateByPrimaryKeySelective(offlineLog);
                updateTotal++;
            }
            log.info("startId:{},total:{}, update:{}", startId, total, updateTotal);
        }
    }

    @Test
    public void getItemTotalCount() {

        String accountNumber = "<EMAIL>-MY";
        esLazadaItemService.getItemOrderNumTotal(accountNumber, Set.of());

    }

    @Test
    @SneakyThrows
    public void syncAllItems() {
        String accountNumber = "<EMAIL>-MY";
        String clientId = "114329";
        String appSecret = "yNN5kyJWqVyG7JtLXNFFP5k9gZXRtYG8";
        String accessToken = "50000000928sz2r7jCSxpuhaakwkHvewtE6bETdo18277148yVGifKnxmJTnH2u";


//        SaleAccountAndBusinessResponse account = new SaleAccountAndBusinessResponse();
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.LAZADA.getChannelName(), "<EMAIL>-VN");

        account.setAccountNumber(accountNumber);
        account.setClientId(clientId);
        account.setClientSecret(appSecret);
        account.setAccessToken(accessToken);
//        LazopClient client = LazadaHttpClientUtil.createClient(account.getAccountNumber());
        syncAllProduct(account);
    }

    @Test
    @SneakyThrows
    public void syncItem() {
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannelEnum.LAZADA.getChannelName(), "<EMAIL>-MY");
        LazopClient client = LazadaHttpClientUtil.createClient(account.getAccountNumber());
        LazopRequest request = new LazopRequest();
        request.setApiName(LazadaPublishRouteApi.getProductItem);
        request.setHttpMethod(Constants.METHOD_GET);
        request.setAccountNumber(account.getAccountNumber());
        request.addApiParameter("item_id", "**********");
//        request.addApiParameter("seller_sku", bean.getSellerSku());
        LazopResponse response = LazadaHttpClientUtil.execute(client, request, account.getAccessToken(),3);
        log.info("response:{}", response.getBody());
        if (response.isSuccess()) {
            JSONObject resultData = JSON.parseObject(response.getBody());
            LazadaProductPull productPull = JSON.parseObject(resultData.getString("data"), LazadaProductPull.class);
            log.info("productPull:{}", productPull);
        }
        TimeUnit.SECONDS.sleep(60000);
    }

    @Test
    @SneakyThrows
    public void syncItemBySellerSku() {
        String accountNumber = "<EMAIL>-MY";
        String clientId = "114329";
        String appSecret = "yNN5kyJWqVyG7JtLXNFFP5k9gZXRtYG8";
        String accessToken = "50000000928sz2r7jCSxpuhaakwkHvewtE6bETdo18277148yVGifKnxmJTnH2u";


//        SaleAccountAndBusinessResponse account = new SaleAccountAndBusinessResponse();
//        account.setAccountNumber(accountNumber);
//        account.setClientId(clientId);
//        account.setClientSecret(appSecret);
//        account.setAccessToken(accessToken);

        SaleAccountAndBusinessResponse account = AccountUtils
                .getSaleAccountByAccountNumber(SaleChannelEnum.LAZADA.getChannelName(),
                        "<EMAIL>-TH");
        LazopClient client = LazadaHttpClientUtil.createClient(account.getAccountNumber());


        //3.单个商品
//        LazopRequest request = new LazopRequest();
//        request.setApiName(LazadaPublishRouteApi.getProductItem);
//        request.setHttpMethod(Constants.METHOD_GET);
//        request.setAccountNumber(account.getAccountNumber());
//        request.addApiParameter("item_id", "**********");
//        request.addApiParameter("seller_sku", "4NB604239-W_GT");
//        LazopResponse response = LazadaHttpClientUtil.execute(client, request, account.getAccessToken(),3);

//        log.info("response:{}", response.getBody());

        int pageNo = 1;
        int currentIndex = 0;
        while (true) {
            // 获取可升级的全球
            LazopRequest request = new LazopRequest();
            request.setHttpMethod(Constants.METHOD_GET);
            request.setApiName(LazadaPublishRouteApi.getUpgradableGlobalPlusProductList);
            request.addApiParameter("type", "global");
//        request.addApiParameter("country", "VN");
            request.addApiParameter("pageNo", Integer.toString(pageNo));
            request.addApiParameter("pageSize", Integer.toString(50));
            request.addApiParameter("currentIndex", Integer.toString(currentIndex));
            LazopResponse response = LazadaHttpClientUtil.execute(client, request, account.getAccessToken(),3);
            JSONObject parseObject = JSON.parseObject(response.getBody());
            pageNo++;
            currentIndex = parseObject.getIntValue("current_index");

            log.info("response:{}", response.getBody());

//
        }


//        // 获取获取推荐价格
//        RecommendPriceRequestBO requestBO = new RecommendPriceRequestBO();
//        requestBO.setItem_id(2808644163L);
//        RecommendPriceRequestBO.SkuItem skuItem = new RecommendPriceRequestBO.SkuItem();
//        skuItem.setItem_id(2808644163L);
//        skuItem.setSeller_sku("13AC1300601-GN-D_HN");
//        skuItem.setSku_id(13453068671L);
//        skuItem.setPackage_height("80.0");
//        skuItem.setPackage_length("80.0");
//        skuItem.setPackage_width("80.0");
//        skuItem.setPackage_weight("8.0");
//        requestBO.setSkus(List.of(skuItem));
//
//
//        LazopRequest request = new LazopRequest();
//        request.setHttpMethod(Constants.METHOD_GET);
//        request.setApiName(LazadaPublishRouteApi.getRecommendPrice);
//        request.addApiParameter("type", "single");
//        request.addApiParameter("payload", JSON.toJSONString(requestBO));
//        LazopResponse response = LazadaHttpClientUtil.execute(client, request, "50000000928sz2r7jCSxpuhaakwkHvewtE6bETdo18277148yVGifKnxmJTnH2u", 3);

        // 升级商品

        // 创建API请求
//
//        SemiProductUpgradeRequestBO semiProductUpgradeRequestBO = new SemiProductUpgradeRequestBO();
//        semiProductUpgradeRequestBO.setItem_id(3391120139L);
//        // 通过_分割accountNumber,并获取最后于一个元素
//        String[] accountNumberSplit = accountNumber.split("-");
//        semiProductUpgradeRequestBO.setCountry(List.of(accountNumberSplit[accountNumberSplit.length - 1]));
//
//        // 组装sku列表
//        SemiProductUpgradeRequestBO.Sku sku = new SemiProductUpgradeRequestBO.Sku();
//        sku.setItem_id(3391120139L);
//        sku.setSeller_sku("4NB1100018-C_GT");
//        sku.setSku_id(18228162804l);
//        sku.setPackage_height("1.0");
//        sku.setPackage_length("1.0");
//        sku.setPackage_width("1.0");
//        sku.setPackage_weight("0.07");
//
//        List<SemiProductUpgradeRequestBO.CountryInfo> countryInfoList = new ArrayList<>();
//        SemiProductUpgradeRequestBO.CountryInfo countryInfo = new SemiProductUpgradeRequestBO.CountryInfo();
//        countryInfo.setMarket("LAZADA_MY");
//        countryInfo.setQuantity(10);
//        countryInfo.setNo_postage_price("26.95");
//        countryInfo.setPrice("28.05");
//        countryInfo.setCurrency("MYR");
//        countryInfoList.add(countryInfo);
//        sku.setCountry_info(countryInfoList);
//        List<SemiProductUpgradeRequestBO.Sku> skuList = List.of(sku);
//        semiProductUpgradeRequestBO.setSkus(skuList);
//
//
//        LazopRequest request = new LazopRequest();
//        request.setHttpMethod(Constants.METHOD_GET);
//        request.setApiName(LazadaPublishRouteApi.semiProductUpgrade);
//        request.addApiParameter("payload", JSON.toJSONString(semiProductUpgradeRequestBO));
//        LazopResponse response = LazadaHttpClientUtil.execute(client, request, account.getAccessToken(), 3);

//        syncAllProduct(account);

//        LazadaSyncItemParam syncItemParam = new LazadaSyncItemParam();
//        syncItemParam.setSyncItemType(LazadaSyncItemTypeEnum.FULL_SYNC);
//        syncItemParam.setDay(15);
//
//        try {
//            //返回状态与此参数匹配的产品。可能的值包括所有(all)，有效(live)，无效(inactive)，已删除(deleted)，图像缺失(image-missing,)，未决(pending)，拒绝(rejected)，售罄(sold-out)。必选
//            ApiResult<List<EsLazadaItem>> apiResult = lazadaGetProductsCall.getProducts(
//                    syncItemParam,
//                    account,
//                    Arrays.asList(LazadaGetProductFilterEnum.LIVE.getFilter(), LazadaGetProductFilterEnum.SOLD_OUT.getFilter()));
//
//            List<EsLazadaItem> result = apiResult.getResult();
//            EsLazadaItem items = result.stream()
//                    .filter(item -> "5AC601750_WX1".equals(item.getSellerSku()))
//                    .findFirst().orElse(null);
//
//            log.info("item:{}",items);
//            log.info("result item size:{}", result.size());
//
//        }catch (Exception e){
//            log.error(String.format("account %s sync error", account.getAccountNumber()), e);
//        }
//        Thread.currentThread().join();
    }

    private void syncAllProduct(SaleAccountAndBusinessResponse account) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.ISO_8601_WITH_OFFSET);
        ZoneId asiaZone = ZoneId.of("Asia/Shanghai");
        // 按每月时间滚动
        ZonedDateTime createBefore = ZonedDateTime.now();
        LocalDateTime createAfterDateTime = LocalDateTime.of(createBefore.getYear(), createBefore.getMonth(), 1, 0,0,0);
        ZonedDateTime createAfter = ZonedDateTime.of(createAfterDateTime, asiaZone);

        LazadaPullPage lazadaPullPage = new LazadaPullPage();
        LazopClient client = LazadaHttpClientUtil.createClient(account.getAccountNumber());

        LazopRequest request = new LazopRequest();
        request.setApiName(LazadaPublishRouteApi.getProducts);
        request.setHttpMethod(Constants.METHOD_GET);
        request.setAccountNumber(account.getAccountNumber());
        request.addApiParameter("filter", LazadaGetProductFilterEnum.LIVE.getFilter());
        request.addApiParameter("offset", lazadaPullPage.getOffsetStr());
        request.addApiParameter("limit", "50");
        request.addApiParameter("options", "1");

//        String create_before = createBefore.format(dateTimeFormatter);
//        request.addApiParameter("create_before", create_before);
//        String create_after = createAfter.format(dateTimeFormatter);

        int total = 0;
        while (true) {
            LazopResponse response =  LazadaHttpClientUtil.execute(client, request, account.getAccessToken(),5);
            if (!response.isSuccess()) {
                log.error("fail:{}", JSON.toJSONString(response));
                break;
            }
            LazopResult<JSONObject> lazopResult = JSON.parseObject(response.getBody(), new TypeReference<>() {});
            JSONObject data = lazopResult.getData();
            Long total_products = data.getLong("total_products");
            if (total_products == null) {
                log.info("null");
                break;
            }
            List<LazadaProductPull> listingPulls = JSON.parseArray(data.getString("products"), LazadaProductPull.class);
            total += listingPulls.size();
            lazadaPullPage.setPage(lazadaPullPage.getPage() + 1);
            request.addApiParameter("offset", lazadaPullPage.getOffsetStr());

            JSONObject skuJson = listingPulls.stream()
                    .map(LazadaProductPull::getSkus)
                    .flatMap(Collection::stream)
                    .filter(sku -> "5AC601750_WX1".equals(sku.getString("SellerSku")))
                    .findFirst().orElse(null);
//
            log.info("item:{}",skuJson);
            log.info("total_products: {},current:{}, offset:{} ",total_products, total, lazadaPullPage.getOffsetStr());
//            OptionalLong optionalLong = listingPulls.stream().map(LazadaProductPull::getCreatedTime).mapToLong(Timestamp::getTime).min();
//            if (optionalLong.isPresent()) {
//                long asLong = optionalLong.getAsLong();
//                Instant instant = Instant.ofEpochMilli(asLong);
//                ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(instant, asiaZone);
//                String before = zonedDateTime.format(dateTimeFormatter);
//                if (lastBeforeTime.equals(before)) {
//                    return;
//                }
//                log.info("--->before:{}", before);
//                request.addApiParameter("update_before", before);
//            }
        }
        log.info("end:{}",total);
    }
}
