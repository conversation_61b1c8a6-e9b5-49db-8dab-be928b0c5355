package com.estone.erp.publish.lazada.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.executors.LazadaExecutors;
import com.estone.erp.publish.elasticsearch.util.CheckOrderSalesTimeUtils;
import com.estone.erp.publish.elasticsearch2.model.EsLazadaItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsLazadaItemRequest;
import com.estone.erp.publish.elasticsearch2.service.EsLazadaItemService;
import com.estone.erp.publish.lazada.bo.LazadaItemBo;
import com.estone.erp.publish.lazada.bo.semiProduct.DeactivateProductDO;
import com.estone.erp.publish.lazada.call.LazadaItemDeactivateProductCall;
import com.estone.erp.publish.lazada.component.SyncProductInfoBulkProcessor;
import com.estone.erp.publish.lazada.enums.LazadaGetProductFilterEnum;
import com.estone.erp.publish.lazada.enums.LazadaSkuStatus;
import com.estone.erp.publish.lazada.model.LazadaAccountConfigCriteria;
import com.estone.erp.publish.lazada.model.LazadaAccountConfigExample;
import com.estone.erp.publish.lazada.model.LazadaNoSaleOrderOfflineLog;
import com.estone.erp.publish.lazada.model.LazadaNoSaleOrderOfflineLogExample;
import com.estone.erp.publish.lazada.service.LazadaAccountConfigService;
import com.estone.erp.publish.lazada.service.LazadaNoSaleOrderOfflineLogService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static com.estone.erp.publish.elasticsearch2.model.beanrequest.EsLazadaItemRequest.TASK_JOB_FIELDS;

/**
 * ES-9273 lazada 上架90天无销量下架
 *
 * <AUTHOR>
 * @date 2024-07-30 下午3:23
 */
@Slf4j
@Component
public class LazadaOfflineNoSaleOrderItemJobHandler extends AbstractJobHandler {
    @Resource
    private LazadaAccountConfigService lazadaAccountConfigService;
    @Resource
    private EsLazadaItemService esLazadaItemService;
    @Resource
    private LazadaNoSaleOrderOfflineLogService lazadaNoSaleOrderOfflineLogService;
    @Resource
    private LazadaItemDeactivateProductCall lazadaItemDeactivateProductCall;
    @Autowired
    private SyncProductInfoBulkProcessor syncProductInfoBulkProcessor;

    public LazadaOfflineNoSaleOrderItemJobHandler() {
        super(LazadaOfflineNoSaleOrderItemJobHandler.class.getName());
    }

    @Data
    private static class InnerParam {
        private List<String> accountNumbers;
        private List<Long> itemIds;
        private Integer rangeDays;
        private String type;
    }

    @Override
    @XxlJob("LazadaOfflineNoSaleOrderItemJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            XxlJobLogger.log("参数不能为空");
            return ReturnT.SUCCESS;
        }
        if ("refresh_remark".equals(innerParam.getType())) {
            refreshRemark();
            return ReturnT.SUCCESS;
        }
        
        LazadaAccountConfigExample accountConfigExample = new LazadaAccountConfigExample();
        LazadaAccountConfigExample.Criteria criteria = accountConfigExample.createCriteria();
        if (CollectionUtils.isNotEmpty(innerParam.getAccountNumbers())) {
            criteria.andAccountNumberIn(innerParam.getAccountNumbers());
        }
        criteria.andAccountStatusEqualTo(LazadaAccountConfigCriteria.accountStatusEnable);
        // 查询启用的店铺
        List<String> accountConfigList = lazadaAccountConfigService.selectAccountNumberByExample(accountConfigExample);
        if (CollectionUtils.isEmpty(accountConfigList)) {
            XxlJobLogger.log("没有启用的店铺");
            return ReturnT.SUCCESS;
        }

        Integer rangeDays = innerParam.getRangeDays();
        LocalDateTime startTime = LocalDateTime.of(LocalDateTime.now().toLocalDate().minusDays(rangeDays), LocalTime.MIN);
        XxlJobLogger.log("开始下架{}天无销量商品,店铺数量:{},时间范围:{}", rangeDays, accountConfigList.size(), startTime);
        for (String accountNumber : accountConfigList) {
            LazadaExecutors.executeUpdateAccountItem(() -> {
                try {
                    // 按店铺统计待下架链接
                    offlineNoSaleOrderItem(accountNumber, innerParam.getItemIds(), rangeDays);
                } catch (Exception e) {
                    XxlJobLogger.log("店铺{}下架90天无销量商品失败,原因{}", accountNumber, e.getMessage(), e);
                }
            });
        }
        return ReturnT.SUCCESS;
    }

    private void refreshRemark() {
        int startId = 0;
        int limit = 1000;
        while (true) {
            LazadaNoSaleOrderOfflineLogExample example = new LazadaNoSaleOrderOfflineLogExample();
            example.setOrderByClause("id asc");
            example.setLimit(limit);
            example.createCriteria()
                    .andIdGreaterThan(startId)
                    .andOfflineStatusEqualTo(1);
            List<LazadaNoSaleOrderOfflineLog> logList = lazadaNoSaleOrderOfflineLogService.selectByExample(example);
            if (CollectionUtils.isEmpty(logList)) {
                break;
            }

            for (LazadaNoSaleOrderOfflineLog offlineLog : logList) {
                String esId = offlineLog.getAccountNumber() + "_" + offlineLog.getItemId() + "_" + offlineLog.getSkuId();
                DeactivateProductDO deactivateProductDO = new DeactivateProductDO();
                deactivateProductDO.setId(esId);
                deactivateProductDO.setOffShelfRemark("365天无销量商品下架");
                deactivateProductDO.setProductStatus(LazadaGetProductFilterEnum.INACTIVE.getFilter());
                deactivateProductDO.setOffShelfDate(offlineLog.getItemCreatedTime());
                deactivateProductDO.setUpdateDate(offlineLog.getUpdatedTime());
                syncProductInfoBulkProcessor.updateExtension(deactivateProductDO);
            }
        }

    }

    private void offlineNoSaleOrderItem(String accountNumber, List<Long> itemIds, Integer rangeDays) {
        EsLazadaItemRequest esLazadaItemRequest = new EsLazadaItemRequest();
        esLazadaItemRequest.setAccountNumber(accountNumber);
        esLazadaItemRequest.setStatus(LazadaSkuStatus.ACTIVE.getValue());
        // 上架时长超过rangeDays天 不算当天
        LocalDateTime startTime = LocalDateTime.of(LocalDateTime.now().toLocalDate().minusDays(rangeDays), LocalTime.MIN);
        esLazadaItemRequest.setToCreateDate(LocalDateTimeUtil.format(startTime));
        // 总销量为0或空
        esLazadaItemRequest.setZeroOrEmptyOrderNumTotal(true);
        if (CollectionUtils.isNotEmpty(itemIds)) {
            esLazadaItemRequest.setItemIdList(itemIds);
        }
        esLazadaItemRequest.setSort("itemId");
        esLazadaItemRequest.setFields(TASK_JOB_FIELDS);

        String remark = rangeDays + "天无销量商品下架";
        int executorTaskCount = esLazadaItemService.scrollQueryExecutorTask(esLazadaItemRequest, items -> {
            executeOfflineNoSaleOrderItem(accountNumber, remark, items);
        });
        XxlJobLogger.log("店铺{}下架90天无销量商品数量{}", accountNumber, executorTaskCount);
    }

    private void executeOfflineNoSaleOrderItem(String accountNumber, String remark, List<EsLazadaItem> items) {
        List<List<LazadaItemBo>> deactivateList = groupListByItemId(accountNumber, items);
        if (CollectionUtils.isEmpty(deactivateList)) {
            return;
        }
        // 批量下架
        CountDownLatch countDownLatch = new CountDownLatch(deactivateList.size());
        for (List<LazadaItemBo> itemList : deactivateList) {
            //异步
            LazadaExecutors.executeUpdateItem(() -> {
                try {
                    DataContextHolder.setUsername("admin");
                    lazadaItemDeactivateProductCall.executeDeactivateProduct(accountNumber, itemList, remark, apiResult -> {
                        addOfflineNoSaleOrderItemLog(itemList, apiResult);
                    });
                } catch (Exception e) {
                    log.error("账号【{}】，ItemId【{}】下架出错。error:{},", accountNumber, itemList.get(0).getItemId(), e.getMessage(), e);
                    XxlJobLogger.log(String.format("账号【%s】，ItemId【%s】下架出错。", accountNumber, itemList.get(0).getItemId()), e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private void addOfflineNoSaleOrderItemLog(List<LazadaItemBo> itemList, ApiResult<String> apiResult) {
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        List<LazadaNoSaleOrderOfflineLog> addList = itemList.stream().map(item -> {
            LazadaNoSaleOrderOfflineLog offlineLog = new LazadaNoSaleOrderOfflineLog();
            offlineLog.setAccountNumber(item.getAccountNumber());
            offlineLog.setItemId(item.getItemId());
            offlineLog.setSkuId(item.getSkuId());
            offlineLog.setSellerSku(item.getSellerSku());
            offlineLog.setStatus(item.getStatus());
            offlineLog.setOrderTotalNumber(item.getOrderTotalNumber());
            offlineLog.setItemCreatedTime(item.getCreateDate());
            offlineLog.setOfflineStatus(apiResult.isSuccess() ? 1 : 0);
            offlineLog.setRemark(apiResult.getErrorMsg());
            offlineLog.setCreatedTime(now);
            offlineLog.setUpdatedTime(now);
            return offlineLog;
        }).collect(Collectors.toList());
        int addCount = lazadaNoSaleOrderOfflineLogService.saveBatch(addList);
        XxlJobLogger.log("下架商品数量:{}, 执行结果：{}, 更新条数：{}", itemList.size(), JSON.toJSONString(apiResult), addCount);

    }

    private List<List<LazadaItemBo>> groupListByItemId(String accountNumber, List<EsLazadaItem> items) {
        List<List<LazadaItemBo>> deactivateList = new ArrayList<>();

        //按itemId分组
        Map<Long, List<EsLazadaItem>> itemsMap = items.stream().collect(Collectors.groupingBy(EsLazadaItem::getItemId));

        // 统计销量
        List<Long> itemIdList = new ArrayList<>();
        Map<Long, Double> itemOrderNumTotalMap = esLazadaItemService.getItemOrderNumTotal(accountNumber, itemsMap.keySet());
        itemsMap.forEach((itemId, itemList) -> {
            // 检查该店铺item是否有销量
            Double itemOrderNumTotal = itemOrderNumTotalMap.get(itemId);
            if (itemOrderNumTotal != null && itemOrderNumTotal > 0) {
                XxlJobLogger.log("item:{},存在总销量:{}", itemId, itemOrderNumTotal);
                return;
            }
            itemIdList.add(itemId);
        });
        if (CollectionUtils.isEmpty(itemIdList)) {
            return deactivateList;
        }

        // 获取所有在线的子sku
        Map<Long, List<EsLazadaItem>> allOnlineItemMap = getAllOnlineItem(accountNumber, itemIdList);
        for (Long itemId : itemIdList) {
            // 获取该itemId下所有在线的子sku
            List<EsLazadaItem> allOnlineItem = allOnlineItemMap.getOrDefault(itemId, Collections.emptyList());
            boolean match = allOnlineItem.stream().allMatch(item -> CheckOrderSalesTimeUtils.checkSaleTotalCountWriteTime(SaleChannel.CHANNEL_LAZADA + "_" + item.getAccountNumber() + "_" + item.getSku()));
            if (!match) {
                XxlJobLogger.log("item:{},不满足下架条件,存在销量/总销量未更新", itemId);
                continue;
            }

            List<LazadaItemBo> itemBos = allOnlineItem.stream().map(item -> {
                LazadaItemBo lazadaItemBo = new LazadaItemBo();
                lazadaItemBo.setEsId(item.getId());
                lazadaItemBo.setAccountNumber(item.getAccountNumber());
                lazadaItemBo.setSite(item.getSite());
                lazadaItemBo.setItemId(item.getItemId());
                lazadaItemBo.setParentSku(item.getSpu());
                lazadaItemBo.setSkuId(item.getSkuId());
                lazadaItemBo.setArticleNumber(item.getSku());
                lazadaItemBo.setSellerSku(item.getSellerSku());
                lazadaItemBo.setStatus(item.getStatus());
                lazadaItemBo.setOrderTotalNumber(item.getOrderNumTotal());
                lazadaItemBo.setCreateDate(Timestamp.from(item.getCreateDate().toInstant()));
                return lazadaItemBo;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(itemBos)) {
                deactivateList.add(itemBos);
            }
        }
        return deactivateList;
    }

    private Map<Long, List<EsLazadaItem>> getAllOnlineItem(String accountNumber, List<Long> itemIds) {
        EsLazadaItemRequest esLazadaItemRequest = new EsLazadaItemRequest();
        esLazadaItemRequest.setAccountNumber(accountNumber);
        esLazadaItemRequest.setStatus(LazadaSkuStatus.ACTIVE.getValue());
        esLazadaItemRequest.setItemIdList(itemIds);
        esLazadaItemRequest.setFields(TASK_JOB_FIELDS);
        List<EsLazadaItem> esLazadaItems = esLazadaItemService.listItemExample(esLazadaItemRequest);
        return esLazadaItems.stream().collect(Collectors.groupingBy(EsLazadaItem::getItemId));
    }
}
