package com.estone.erp.publish.lazada.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.StockTypeEnum;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.executors.LazadaExecutors;
import com.estone.erp.publish.elasticsearch2.model.EsLazadaItem;
import com.estone.erp.publish.elasticsearch2.model.LazadaMultiWarehouseInventoryDO;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsLazadaItemRequest;
import com.estone.erp.publish.lazada.bo.LazadaItemBo;
import com.estone.erp.publish.lazada.cache.LazadaAccountCache;
import com.estone.erp.publish.lazada.call.LazadaCommonCall;
import com.estone.erp.publish.lazada.call.LazadaItemSyncSuperCall;
import com.estone.erp.publish.lazada.call.LazadaUpdateItemPriceQuantityCall;
import com.estone.erp.publish.lazada.component.LazadaQuantityCheckComponent;
import com.estone.erp.publish.lazada.enums.LazadaFeedTaskEnum;
import com.estone.erp.publish.lazada.model.LazadaAccountConfig;
import com.estone.erp.publish.lazada.model.LazadaAccountConfigCriteria;
import com.estone.erp.publish.lazada.model.LazadaAccountConfigExample;
import com.estone.erp.publish.lazada.service.LazadaAccountConfigService;
import com.estone.erp.publish.lazada.service.LazadaItemEsService;
import com.estone.erp.publish.lazada.util.SystemParamUtil;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.bean.StockObj;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

/**
 * 名称：调库存定时任务（周，一，二，五，六，日）
 * <AUTHOR>
 * @date 2021/1/13 11:47
 * @description 产品系统可用库存为0，修改在线listing库存为0
 * <p>
 * lazada
 * 定时任务：一天一次
 * 定时时间：每天早上七点
 * 定时任务内容：
 * 1-除停产，存档，休假状态的子SKU
 * 2-在线列表子SKU，可用库存为0，在线列表库存不为0，则调整在线列表库存为0.
 * 3-在线列表子SKU，可用库存>10，在线列表库存小于1000，则调整在线列表库存为9999.
 * 可用库存小于等于10，则调整在线列表库存为 可用/2，向上取整
 * 备注：该任务排除配置的店铺，和lisitng创建时间为X天内的。（适用于二三点）
 * 4-参数配置
 * 1）不调库存店铺：配置不调库存的店铺，权限开放给主管和超级管理员。
 * 2）配置时间：创建时间X时间内不进行调库存，X配置为参数。
 */
@Slf4j
@Component
public class LazadaEveryDayUpdateStockHandler extends AbstractJobHandler {
    @Resource
    private LazadaItemSyncSuperCall lazadaItemSyncSuperCall;
    @Getter
    @Setter
    public static class InnerParam {
        //店鋪账号
        private List<String> accountNumberList;
        //线程池数量
        private int threadNum = 5;

        /**
         * sku 列表
         */
        private List<String> skuList;

        /**
         * @see StockTypeEnum
         */
        private Integer stockType = StockTypeEnum.SYSTEM_STOCK.getType();
    }

    @Autowired
    private Environment environment;
    @Autowired
    private LazadaAccountConfigService lazadaAccountConfigService;
    @Autowired
    private LazadaCommonCall lazadaCommonCall;
    @Autowired
    private LazadaUpdateItemPriceQuantityCall lazadaUpdateItemPriceQuantityCall;
    @Resource
    private LazadaQuantityCheckComponent lazadaQuantityCheckComponent;
    @Autowired
    private LazadaItemEsService lazadaItemEsService;

    public LazadaEveryDayUpdateStockHandler() {
        super(LazadaEveryDayUpdateStockHandler.class.getName());
    }

    @Override
    @XxlJob("LazadaEveryDayUpdateStockHandler")
    public ReturnT<String> run(String param) throws Exception {
        log.info("每天更新listing可用库存 start");
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            return ReturnT.FAIL;
        }
        List<String> skuList = innerParam.getSkuList();
        Integer stockType = innerParam.getStockType();
        boolean flag = StockTypeEnum.checkValue(stockType);
        if (!flag) {
            XxlJobLogger.log("库存类型参数错误！");
            return ReturnT.FAIL;
        }

        String[] activeProfiles = environment.getActiveProfiles();
        if (activeProfiles.length == 0 || !ArrayUtils.contains(activeProfiles, "prod")) {
            if (CollectionUtils.isEmpty(innerParam.getAccountNumberList())) {
                return new ReturnT<>(500, "非生产环境，必须配置店铺数据才能执行！");
            }
        }

        ReturnT<String> returnT = lazadaQuantityCheckComponent.checkQuantity();
        if (returnT.getCode() != 200) {
            return returnT;
        }

        // 获取账号配置自动调整库存不是false的账号
        LazadaAccountConfigExample lazadaAccountConfigExample = new LazadaAccountConfigExample();
        lazadaAccountConfigExample.createCriteria()
                .andIsAutoUpdateStockEqualTo(true)
                // 启用状态
                .andAccountStatusEqualTo(LazadaAccountConfigCriteria.accountStatusEnable);
        List<LazadaAccountConfig> accountList = lazadaAccountConfigService.selectByExample(lazadaAccountConfigExample);
        List<String> accountNumbers = accountList.stream()
                .map(LazadaAccountConfig::getAccountNumber)
                .distinct()
                .collect(Collectors.toList());

        List<String> accountNumberList;
        if (CollectionUtils.isNotEmpty(innerParam.getAccountNumberList())) {
            accountNumberList = innerParam.getAccountNumberList().stream().filter(accountNumbers::contains).collect(Collectors.toList());
        } else {
            accountNumberList = accountNumbers;
        }
        //上架时间之内
        int day = SystemParamUtil.getChangeZeroDay();
        LocalDateTime dateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).minusDays(day);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String time = dateTimeFormatter.format(dateTime);
        XxlJobLogger.log("{}到当前时间的数据不处理", time);
        XxlJobLogger.log("本次处理{}个店铺", accountNumberList.size());

        //线程池数量
        int threadNum = innerParam.getThreadNum();
        //控制线程池数量
        final Semaphore sp = new Semaphore(threadNum);//创建Semaphore信号量，初始化许可大小为5
        for (String accountNumber : accountNumberList) {
            //请求获得许可，如果有可获得的许可则继续往下执行，许可数减1。否则进入阻塞状态
            sp.acquire();
            XxlJobLogger.log("处理店铺:{}", accountNumber);
            LazadaExecutors.executeUpdateItem(() -> {
                try {
                    execListingData(accountNumber, time, skuList, stockType);
                } catch (Exception e) {
                    log.error(String.format("账号%s,更新出错:", accountNumber), e);
                } finally {
                    //执行完之后，释放许可，许可数加1
                    sp.release();
                }
            });
        }
        //判断是否还有活跃线程，有的话线程休眠1分钟
        while (LazadaExecutors.UPDATE_ITEM_POOL.getActiveCount() > 0) {
            log.info("线程还未执行完，休眠5秒");
            Thread.sleep(5000);
        }
        log.info("每天更新listing可用库存 end");
        return ReturnT.SUCCESS;
    }

    private void execListingData(String accountNumber, String ltDay, List<String> skuList, Integer stockType) {
        //获取账号
        SaleAccountAndBusinessResponse account = LazadaAccountCache.getCacheLazadaAccount(accountNumber);
        if (account == null) {
            log.info("账号{} 获取授权信息为空！", accountNumber);
            return;
        }
        // 除停产存档
        List<String> skuStatusList = Arrays.asList(SkuStatusEnum.STOP.getCode(), SkuStatusEnum.ARCHIVED.getCode(), SkuStatusEnum.PENDING.getCode());
        EsLazadaItemRequest request = new EsLazadaItemRequest();
        request.setAccounts(Collections.singletonList(accountNumber));
        request.setSkuStatusNotInList(skuStatusList);
        request.setToSpecialFromTime(ltDay);
        request.setFields(EsLazadaItemRequest.TASK_JOB_FIELDS);
        if (CollectionUtils.isNotEmpty(skuList)) {
            request.setSkuList(skuList);
        }
        int page = 0;
        int size = 500;
        while (true) {
            Page<EsLazadaItem> esLazadaItems = lazadaItemEsService.pageList(request, page, size);
            if (CollectionUtils.isEmpty(esLazadaItems.getContent())) {
                break;
            }
            List<LazadaItemBo> updateStock = new ArrayList<>();
            for (EsLazadaItem esLazadaItem : esLazadaItems.getContent()) {
                try {
                    StockObj stockObjByType = SkuStockUtils.getStockObjByType(esLazadaItem.getSku(), stockType);
                    Integer stock = stockObjByType.getResultStock();
                    if (stock == null) {
                        XxlJobLogger.log("{}-{},未查询到可用库存", accountNumber, esLazadaItem.getSku());
                        continue;
                    }
                    // 在线列表子SKU，可用库存为0，在线列表库存不为0，则调整在线列表库存为0.
                    if (stock == 0 && esLazadaItem.getQuantity() != 0) {
                        LazadaItemBo itemBO = createItemBO(esLazadaItem, 0, stockObjByType);
                        updateStock.add(itemBO);
                    }
                    // 在线列表子SKU，可用库存>10，在线列表库存小于1000，则调整在线列表库存为9999
                    if (stock > 10 && esLazadaItem.getQuantity() < 1000) {
                        LazadaItemBo itemBO = createItemBO(esLazadaItem, 9999, stockObjByType);
                        updateStock.add(itemBO);
                    }
                    // 可用库存小于等于10，则调整在线列表库存为 可用/2，向上取整
                    if (stock <= 10 && esLazadaItem.getQuantity() < 10) {
                        BigDecimal newStock = new BigDecimal(stock).divide(new BigDecimal(2), RoundingMode.UP);
                        if (!esLazadaItem.getQuantity().equals(newStock.intValue())) {
                            LazadaItemBo itemBO = createItemBO(esLazadaItem, newStock.intValue(), stockObjByType);
                            updateStock.add(itemBO);
                        }
                    }
                } catch (Exception e) {
                    log.error("调整库存异常,param:{}", JSON.toJSONString(esLazadaItem), e);
                    XxlJobLogger.log("调整库存异常,account:{},sku:{},error:{}", accountNumber, esLazadaItem.getSku(), e.getMessage());
                }
            }
            if (CollectionUtils.isNotEmpty(updateStock)) {
                updateInfo(accountNumber, account, updateStock);
            }
            page ++;
        }
    }

    private LazadaItemBo createItemBO(EsLazadaItem sourceItem, Integer newStock, StockObj stockObj) {
        LazadaItemBo item = new LazadaItemBo();
        item.setEsId(sourceItem.getId());
        item.setAccountNumber(sourceItem.getAccountNumber());
        item.setSellerSku(sourceItem.getSellerSku());
        item.setItemId(sourceItem.getItemId());
        item.setSkuId(sourceItem.getSkuId());
        item.setArticleNumber(sourceItem.getSku());
        XxlJobLogger.log("本次处理sku{}，修改前：{}，修改后：{}", sourceItem.getSku(), sourceItem.getQuantity(), newStock);
        log.info("本次处理sku{}，修改前：{}，修改后：{}", sourceItem.getSku(), sourceItem.getQuantity(), newStock);
        item.setUpdateBeforeValue(sourceItem.getQuantity().toString());
        item.setQuantity(newStock);
        item.setSellableStock(newStock);
        item.setUpdateAfterValue(newStock.toString());
        item.setUpdateDate(new Timestamp(System.currentTimeMillis()));
        item.setOffShelfDate(item.getUpdateDate());
        item.setStockObj(stockObj);
        // 判断库存是否为多仓库
        List<LazadaMultiWarehouseInventoryDO> multiWarehouseInventories = Optional.ofNullable(sourceItem.getMultiWarehouseInventories()).orElse(Collections.emptyList());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(multiWarehouseInventories)) {
            Map<String, List<LazadaMultiWarehouseInventoryDO>> warehouseMap = multiWarehouseInventories.stream()
                    .filter(warehouseInventoryDO -> StringUtils.isNotEmpty(warehouseInventoryDO.getWarehouseName()))
                    .collect(Collectors.groupingBy(LazadaMultiWarehouseInventoryDO::getWarehouseName));
            List<LazadaMultiWarehouseInventoryDO> multiWarehouseInventoryDOS = warehouseMap.get("Warehouse");
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(multiWarehouseInventoryDOS)) {
                item.setUpdateBeforeValue(multiWarehouseInventoryDOS.get(0).getQuantity().toString());
            }
        }
        return item;
    }

    private void updateInfo(String accountNumber, SaleAccountAndBusinessResponse account, List<LazadaItemBo> itemList) {
        List<List<LazadaItemBo>> pagingList = Lists.partition(itemList, 20);
        DataContextHolder.setUsername(StrConstant.ADMIN);
        //用来存放异常信息，如果为空则正常
        Set<String> msg = new HashSet<>();
        for (List<LazadaItemBo> list : pagingList) {
            try {
                msg.clear();
                lazadaUpdateItemPriceQuantityCall.updateItem(accountNumber, list, msg, LazadaFeedTaskEnum.UPDATE_STOCK);
                if (CollectionUtils.isNotEmpty(msg)) {
                    log.info("账号{},库存修改出现问题:{}", accountNumber, msg.toString());
                }

                // 同步一下
                lazadaItemSyncSuperCall.syncLazadaItem(accountNumber, list);
            } catch (Exception e) {
                log.error(String.format("账号【%s】，库存改出错", accountNumber), e);
                XxlJobLogger.log(String.format("账号【%s】,库存改出错：%s", accountNumber, e.getMessage()));
            }
        }
    }
}
