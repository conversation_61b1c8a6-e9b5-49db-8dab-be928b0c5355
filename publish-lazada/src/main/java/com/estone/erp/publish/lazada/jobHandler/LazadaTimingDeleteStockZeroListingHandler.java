package com.estone.erp.publish.lazada.jobHandler;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.publish.common.executors.LazadaExecutors;
import com.estone.erp.publish.elasticsearch2.model.EsLazadaItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsLazadaItemRequest;
import com.estone.erp.publish.lazada.bo.LazadaItemBo;
import com.estone.erp.publish.lazada.call.LazadaItemRemoveCall;
import com.estone.erp.publish.lazada.model.LazadaAccountConfig;
import com.estone.erp.publish.lazada.model.LazadaAccountConfigCriteria;
import com.estone.erp.publish.lazada.model.LazadaAccountConfigExample;
import com.estone.erp.publish.lazada.model.LazadaItem;
import com.estone.erp.publish.lazada.service.LazadaAccountConfigService;
import com.estone.erp.publish.lazada.service.LazadaItemEsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/10/6 9:42
 * @description
任务内容：针对自动刊登的SKU，则后缀为_Z，结尾的，库存为0的listing，30天后自动删除。
任务执行时间：每天晚上12:00
执行频率：一天一次
 */
@Slf4j
@Component
//@JobHandler("LazadaTimingDeleteStockZeroListingHandler")
public class LazadaTimingDeleteStockZeroListingHandler extends AbstractJobHandler {

    @Autowired
    private LazadaItemEsService lazadaItemEsService;

    @Autowired
    private LazadaAccountConfigService lazadaAccountConfigService;

    @Resource
    private LazadaItemRemoveCall lazadaItemRemoveCall;

    public LazadaTimingDeleteStockZeroListingHandler() {
        super(LazadaTimingDeleteStockZeroListingHandler.class.getName());
    }

    @Override
    @XxlJob("LazadaTimingDeleteStockZeroListingHandler")
    public ReturnT<String> run(String param) throws Exception {
        log.info("删除库存为零sku start");

        //查询后缀为：_Z 、库存为0、下架日期超过30天的数据进行删除。
        LocalDateTime dateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).minusDays(30);
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String time = dateTimeFormatter.format(dateTime);

        // 获取账号配置自动调整库存为true的店铺列表且状态为启用
        LazadaAccountConfigExample lazadaAccountConfigExample = new LazadaAccountConfigExample();
        lazadaAccountConfigExample.createCriteria()
                .andAccountStatusEqualTo(LazadaAccountConfigCriteria.accountStatusEnable);
        List<LazadaAccountConfig> accountList = lazadaAccountConfigService.selectByExample(lazadaAccountConfigExample);
        if (CollectionUtils.isEmpty(accountList)) {
            return ReturnT.SUCCESS;
        }

        for (LazadaAccountConfig accountConfig : accountList) {
            EsLazadaItemRequest request = new EsLazadaItemRequest();
            request.setAccounts(Collections.singletonList(accountConfig.getAccountNumber()));
            request.setSellableStock(0);
            request.setSellerSkuLike("*_Z");
            request.setLteOffShelfDate(time);
            request.setFields(new String[]{"id", "itemId", "skuId", "accountNumber", "sellerSku", "spu", "sku"});
            request.setSort("skuId");
            request.setOrder("asc");
            Long lastSkuId = null;
            while (true) {
                if (lastSkuId != null) {
                    request.setLastSkuId(lastSkuId);
                }
                Page<EsLazadaItem> esLazadaItems = lazadaItemEsService.pageList(request, 0, 100);
                if (CollectionUtils.isEmpty(esLazadaItems.getContent())) {
                    log.info("break:{}", lastSkuId);
                    break;
                }
                List<LazadaItemBo> lazadaItemBos = esLazadaItems.getContent().stream().map(esLazadaItem -> {
                    LazadaItemBo lazadaItemBo = BeanUtil.copyProperties(esLazadaItem, LazadaItemBo.class);
                    lazadaItemBo.setEsId(esLazadaItem.getId());
                    lazadaItemBo.setParentSku(esLazadaItem.getSpu());
                    lazadaItemBo.setArticleNumber(esLazadaItem.getSku());
                    return lazadaItemBo;
                }).collect(Collectors.toList());

                Map<String, List<LazadaItemBo>> maps = lazadaItemBos.stream().collect(Collectors.groupingBy(LazadaItem::getAccountNumber));
                //用来存放异常信息，如果为空则正常
                Set<String> msg = new CopyOnWriteArraySet<>();
                CountDownLatch countDownLatch = new CountDownLatch(maps.size());
                for (Map.Entry<String, List<LazadaItemBo>> entry : maps.entrySet()) {
                    LazadaExecutors.executeRemoveItem(() -> {
                        try {
                            lazadaItemRemoveCall.itemRemove(msg, entry.getValue());
                        } catch (Exception e) {
                            log.error(String.format("账号【%s】，删除出错。", entry.getKey()), e);
                            msg.add(String.format("账号【%s】，删除出错。", entry.getKey()));
                        } finally {
                            countDownLatch.countDown();
                        }
                    });
                }
                try {
                    countDownLatch.await();
                } catch (InterruptedException e) {
                    log.error("批量删除线程中断异常", e);
                }
                lastSkuId = lazadaItemBos.get(lazadaItemBos.size() - 1).getSkuId();

            }
            log.info("删除库存为零sku end lastSkuId:{}", lastSkuId);
        }
        return ReturnT.SUCCESS;
    }
}
