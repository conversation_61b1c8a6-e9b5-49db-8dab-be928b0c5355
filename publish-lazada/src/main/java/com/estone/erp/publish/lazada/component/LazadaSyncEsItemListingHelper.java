package com.estone.erp.publish.lazada.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.elasticsearch2.model.EsLazadaItem;
import com.estone.erp.publish.elasticsearch2.model.LazadaMultiWarehouseInventoryDO;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsLazadaItemRequest;
import com.estone.erp.publish.elasticsearch2.service.EsLazadaItemService;
import com.estone.erp.publish.lazada.bo.LazadaSyncItemParam;
import com.estone.erp.publish.lazada.bo.semiProduct.SemiProductDO;
import com.estone.erp.publish.lazada.call.LazadaWarehouseCall;
import com.estone.erp.publish.lazada.enums.*;
import com.estone.erp.publish.lazada.lazop.model.LazadaProductPull;
import com.estone.erp.publish.lazada.model.LazadaTemplateExample;
import com.estone.erp.publish.lazada.service.LazadaTemplateService;
import com.estone.erp.publish.lazada.util.SkuUtil;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ComposeSku;
import com.estone.erp.publish.system.product.bean.SuiteSku;
import com.estone.erp.publish.system.product.enums.ComposeCheckStepEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * Lazada 同步item到 Es工具类
 *
 * <AUTHOR>
 * @date 2022-08-05 10:33
 */
@Slf4j
@Component
public class LazadaSyncEsItemListingHelper {

    @Autowired
    private FeedTaskService feedTaskService;

    @Autowired
    private LazadaGrossHelper lazadaGrossHelper;

    @Autowired
    private EsLazadaItemService esLazadaItemService;

    @Autowired
    private SyncProductInfoBulkProcessor syncProductInfoBulkProcessor;

    @Resource
    private LazadaTemplateService lazadaTemplateService;
    /**
     * 处理api返回的数据
     *
     * @param account       账号
     * @param productPulls  同步回来的数据源
     * @param syncItemParam 同步参数
     */
    public List<EsLazadaItem> resolveProductPulls(SaleAccountAndBusinessResponse account, List<LazadaProductPull> productPulls, LazadaSyncItemParam syncItemParam) {
        // 1如果是同步选择数据, 数据为空不处理直接返回
        if (LazadaSyncItemTypeEnum.CHOOICE_DATA_SYNC.equals(syncItemParam.getSyncItemType()) && CollectionUtils.isEmpty(productPulls)) {
            return new ArrayList<>(0);
        }
        // 2如果是增量同步账号，数据为空不处理直接返回
        if (LazadaSyncItemTypeEnum.NON_FULL_SYNC.equals(syncItemParam.getSyncItemType()) && CollectionUtils.isEmpty(productPulls)) {
            return new ArrayList<>(0);
        }

        // 获取店铺所属库存信息
        Map<String, String> accountWarehouseNameMap = LazadaWarehouseCall.getWarehouseBySellerId(account);

        // 3如果是全量同步账号， 不管数据是否为空 继续往下执行 只要数据不为空都可以往下执行，或者是全量同步账号都可以往下执行
        String site = LazadaSiteEnum.getSiteByAccount(account);
        // 因为lazada结果分页问题用skuSet去重
        Set<Long> skuSet = new HashSet<>();
        List<String> articleNumbers = new ArrayList<>();
        List<EsLazadaItem> esLazadaItems = new ArrayList<>();
        productPulls.forEach(prod -> {
            List<EsLazadaItem> skuItems = resolveProductPull(prod, skuSet, articleNumbers, account, site);
            if (CollectionUtils.isEmpty(skuItems)) {
                return;
            }

            // 维护库存信息
            updateMultiWarehouseInventories(skuItems, accountWarehouseNameMap);
            esLazadaItems.addAll(skuItems);
        });
        return esLazadaItems;
    }

    /**
     * 更新库存信息
     *
     * @param skuItems
     * @param accountWarehouseNameMap
     */
    private static void updateMultiWarehouseInventories(List<EsLazadaItem> skuItems, Map<String, String> accountWarehouseNameMap) {
        skuItems.forEach(item -> {
            JSONObject skuJson = JSON.parseObject(item.getSkuJson());
            List<LazadaMultiWarehouseInventoryDO> multiWarehouseInventories = JSON.parseObject(
                    skuJson.getString("multiWarehouseInventories"),
                    new TypeReference<List<LazadaMultiWarehouseInventoryDO>>() {
                    }
            );

            // 如果为空或数量不足，则填充或补齐仓库信息
            if (CollectionUtils.isEmpty(multiWarehouseInventories) || multiWarehouseInventories.size() < accountWarehouseNameMap.size()) {
                if (CollectionUtils.isEmpty(multiWarehouseInventories)) {
                    // 为空时，直接填充所有仓库信息
                    multiWarehouseInventories = accountWarehouseNameMap.entrySet().stream()
                            .map(entry -> {
                                LazadaMultiWarehouseInventoryDO inventoryDO = new LazadaMultiWarehouseInventoryDO();
                                inventoryDO.setWarehouseCode(entry.getKey());
                                inventoryDO.setQuantity(0);
                                return inventoryDO;
                            })
                            .collect(Collectors.toList());
                } else {
                    // 数量不足时，补充缺失的仓库
                    Map<String, LazadaMultiWarehouseInventoryDO> warehouseCodeMap = multiWarehouseInventories.stream()
                            .filter(inventory -> StringUtils.isNotEmpty(inventory.getWarehouseCode()))
                            .collect(Collectors.toMap(LazadaMultiWarehouseInventoryDO::getWarehouseCode, inventory -> inventory));
                    accountWarehouseNameMap.keySet().stream()
                            .filter(code -> !warehouseCodeMap.containsKey(code))
                            .map(code -> {
                                LazadaMultiWarehouseInventoryDO inventoryDO = new LazadaMultiWarehouseInventoryDO();
                                inventoryDO.setWarehouseCode(code);
                                inventoryDO.setQuantity(0);
                                return inventoryDO;
                            })
                            .forEach(multiWarehouseInventories::add);
                }
            }

            // 更新仓库名称并设置回 item
            multiWarehouseInventories.forEach(inventory -> {
                String warehouseName = accountWarehouseNameMap.get(inventory.getWarehouseCode());
                if (StringUtils.isNotBlank(warehouseName)) {
                    inventory.setWarehouseName(warehouseName);
                    if (inventory.getWarehouseCode().equals("dropshipping")) {
                        inventory.setWarehouseName("Warehouse");
                    }
                }
            });
            item.setMultiWarehouseInventories(multiWarehouseInventories);
        });
    }

    public List<EsLazadaItem> resolveProductPull(LazadaProductPull prod, Set<Long> skuSet, List<String> articleNumbers, SaleAccountAndBusinessResponse account, String site) {
        List<EsLazadaItem> skuList = prod.resolveEsSkus();
        if (CollectionUtils.isEmpty(skuList)) {
            return skuList;
        }
        // 获取来源，避免同步时升级全球商品异常
        String source = prod.getAttributes().getString("source");
        String name = prod.getAttributes().getString("name");
        String brand = prod.getAttributes().getString("brand");
        Integer globalPlusProductStatus = 0;
        if (prod.getBizSupplement() != null) {
            globalPlusProductStatus = prod.getBizSupplement().getInteger("globalPlusProductStatus");
        }

        for (EsLazadaItem bean : skuList) {
            if (skuSet.contains(bean.getSkuId())) {
                continue;
            }
            skuSet.add(bean.getSkuId());
            String esId = account.getAccountNumber() + "_" + prod.getItemId() + "_" + bean.getSkuId();
            bean.setId(esId);
            bean.setAccountNumber(account.getAccountNumber());
            bean.setSite(site);
            bean.setItemId(prod.getItemId());
            bean.setPrimaryCategory(prod.getPrimaryCategory());
            bean.setTitle(name);
            bean.setBrand(brand);
            bean.setAttributes(prod.getAttributes().toJSONString());
            bean.setCreateDate(prod.getCreatedTime());
            bean.setUpdateDate(prod.getUpdatedTime());
            bean.setSyncDate(new Date());
            bean.setSource(source);
            bean.setSemiStatus(globalPlusProductStatus == 1);
            bean.setProductStatus(prod.getProductStatus());
            if (CollectionUtils.isNotEmpty(prod.getRejectReason())) {
                bean.setRejectReason(JSON.toJSONString(prod.getRejectReason()));
            }

            String articleNumber = SkuUtil.getArticleNumberBySellerSku(bean.getSellerSku());
            articleNumber = StrUtil.strTrimToUpperCase(articleNumber);
            //子sku
            bean.setSku(articleNumber);
            if (!articleNumbers.contains(articleNumber)) {
                articleNumbers.add(articleNumber);
            }
        }
        return skuList;
    }


    /**
     * 同步Listing
     *
     * @param account       账号
     * @param items         平台同步回来的数据
     * @param feedTasks     处理报告
     * @param syncItemParam 同步入参
     */
    public void syncListing(SaleAccountAndBusinessResponse account, List<EsLazadaItem> items, List<FeedTask> feedTasks, LazadaSyncItemParam syncItemParam) {
        // 1如果是同步选择数据, 数据为空不处理直接返回
        if (LazadaSyncItemTypeEnum.CHOOICE_DATA_SYNC.equals(syncItemParam.getSyncItemType()) && CollectionUtils.isEmpty(items)) {
            feedTaskService.batchUpdateFeedTaskToFinish(feedTasks,
                    FeedTaskStatusEnum.FINISH.getTaskStatus(),
                    FeedTaskResultStatusEnum.SUCCESS.getResultStatus(),
                    "手动同步listing，同步回来的listing为空，不处理。");
            return;
        }
        if (LazadaSyncItemTypeEnum.NON_FULL_SYNC.equals(syncItemParam.getSyncItemType()) && CollectionUtils.isEmpty(items)) {
            feedTaskService.batchUpdateFeedTaskToFinish(feedTasks,
                    FeedTaskStatusEnum.FINISH.getTaskStatus(),
                    FeedTaskResultStatusEnum.SUCCESS.getResultStatus(),
                    "增量同步，同步回来的listing为空，不处理。");
            return;
        }

        if (CollectionUtils.isEmpty(items)) {
            feedTaskService.batchUpdateFeedTaskToFinish(feedTasks,
                    FeedTaskStatusEnum.FINISH.getTaskStatus(),
                    FeedTaskResultStatusEnum.SUCCESS.getResultStatus(),
                    syncItemParam.getSyncItemType().getName() + "同步回来的listing为空，不处理。");
            return;
        }

        // 批量计算毛利率
        List<List<EsLazadaItem>> partition = Lists.partition(items, 100);
        for (List<EsLazadaItem> lazadaItemList : partition) {
            lazadaGrossHelper.setItemGross(lazadaItemList);
        }
        List<String> errorMsg = new ArrayList<>();
        // 保存获得的数据
        List<List<EsLazadaItem>> itemPartition = Lists.partition(items, 500);
        for (List<EsLazadaItem> itemList : itemPartition) {
            try {
                saveAndUpdateToEsHandler(itemList);
            } catch (Exception e) {
                log.error("处理同步信息失败,", e);
                errorMsg.add(e.getMessage());
            }
        }
        if (errorMsg.isEmpty()) {
            feedTaskService.batchUpdateFeedTaskToFinish(feedTasks,
                    FeedTaskStatusEnum.FINISH.getTaskStatus(),
                    FeedTaskResultStatusEnum.SUCCESS.getResultStatus(),
                    syncItemParam.getSyncItemType().getName() + "同步完成");
        } else {
            feedTasks.get(0).setAttribute2(String.join("||", errorMsg));
            feedTaskService.batchUpdateFeedTaskToFinish(feedTasks,
                    FeedTaskStatusEnum.FINISH.getTaskStatus(),
                    FeedTaskResultStatusEnum.FAIL.getResultStatus(),
                    "新增存在部分失败");
        }
    }

    /**
     * 处理平台同步回来的数据
     * 更新或新增到ES
     * @param itemList  待更新保存数据
     */
    public void saveAndUpdateToEsHandler(List<EsLazadaItem> itemList) {
        // 更新已经存在ES中的listing
        List<EsLazadaItem> filterExistItems = filterAndUpdateExistItems(itemList);
        if (CollectionUtils.isEmpty(filterExistItems)) {
            log.info("更新完毕");
            return;
        }
        Map<String, ProductInfoVO> productMap = Maps.newHashMap();
        for (EsLazadaItem item : filterExistItems) {
            // 查询产品信息
            ProductInfoVO productInfoVO = productMap.get(item.getSku());
            if (ObjectUtils.isEmpty(productInfoVO)) {
                productInfoVO = ProductUtils.getSkuInfo(item.getSku());
                productMap.put(item.getSku(), productInfoVO);
            }
            setProductInfo(item, productInfoVO);

            getAndSavePublishRole(item);
            esLazadaItemService.save(item);
        }
    }

    /**
     * 更新已经存在ES中的listing
     * 只更新平台返回的内容
     * @param itemList  itemList
     * @return 过滤已存在的item
     */
    private List<EsLazadaItem> filterAndUpdateExistItems(List<EsLazadaItem> itemList) {
        List<String> esIds = itemList.stream().map(EsLazadaItem::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(esIds)) {
            return itemList;
        }

        EsLazadaItemRequest itemRequest = new EsLazadaItemRequest();
        itemRequest.setIdList(esIds);
        itemRequest.setFields(new String[]{"id", "publishRole", "sku", "spu"});
        List<EsLazadaItem> existItems = esLazadaItemService.listItemExample(itemRequest);
        if (CollectionUtils.isEmpty(existItems)) {
            return itemList;
        }

        Map<String, EsLazadaItem> updateItemMap = itemList.stream().collect(Collectors.toMap(EsLazadaItem::getId, Function.identity(), (o1, o2) -> o1));
        // 已存在的则更新平台信息
        existItems.forEach(item -> {
            EsLazadaItem esLazadaItem = updateItemMap.get(item.getId());
            // 设置为原来的刊登角色
            esLazadaItem.setPublishRole(item.getPublishRole());
            syncProductInfoBulkProcessor.updatePlatformData(esLazadaItem);
        });

        // 过滤存在的
        List<String> existIds = existItems.stream()
                .filter(item-> StringUtils.isNotBlank(item.getSku()) && StringUtils.isNotBlank(item.getSpu()))
                .map(EsLazadaItem::getId).collect(Collectors.toList());
        return itemList.stream().filter(item -> !existIds.contains(item.getId())).collect(Collectors.toList());
    }

    /**
     * 更新Lazada平台返回属性
     *
     * @param source 更新源
     * @param target 更新目标
     */
    private void updateLazadaProperties(EsLazadaItem source, EsLazadaItem target) {
        Assert.notNull(source, "Source must not be null");
        Assert.notNull(target, "Target must not be null");

        target.setTitle(source.getTitle());
        target.setPrimaryCategory(source.getPrimaryCategory());
        target.setBrand(source.getBrand());
        target.setUrl(source.getUrl());
        target.setColorFamily(source.getColorFamily());
        target.setPackageLength(source.getPackageLength());
        target.setPackageWidth(source.getPackageWidth());
        target.setPackageHeight(source.getPackageHeight());
        target.setPackageWeight(source.getPackageWeight());
        target.setPackageContent(source.getPackageContent());
        target.setRateOfMargin(source.getRateOfMargin());
        target.setPrice(source.getPrice());
        target.setSpecialPrice(source.getSpecialPrice());
        target.setSellableStock(source.getSellableStock());
        target.setQuantity(source.getQuantity());
        target.setAttributes(source.getAttributes());
        target.setStatus(source.getStatus());
        target.setProductStatus(source.getProductStatus());
        target.setTaxClass(source.getTaxClass());
        target.setImages(source.getImages());
        target.setSkuJson(source.getSkuJson());
        target.setGross(source.getGross());
        target.setCalePrice(source.getCalePrice());
        target.setGrossUpdateDate(source.getGrossUpdateDate());
        target.setSpecialFromDate(source.getSpecialFromDate());
        target.setSpecialToDate(source.getSpecialToDate());
        target.setSpecialFromTime(source.getSpecialFromTime());
        target.setSpecialToTime(source.getSpecialToTime());
        target.setSyncDate(source.getSyncDate());
    }


    public void getAndSavePublishRole(EsLazadaItem item) {
        String accountNumber = item.getAccountNumber();
        String sku = item.getSku(), spu = item.getSpu();
        if (StringUtils.isBlank(accountNumber)) {
            return;
        }

        int systemPublishNums = 0;
        // 查询刊登角色为“系统刊登”并且刊登成功的模板
        if (StringUtils.isNotBlank(sku) || StringUtils.isNotBlank(spu)) {
            List<String> articleNumbers = Stream.of(sku, spu)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            LazadaTemplateExample example = new LazadaTemplateExample();
            example.setTable(TemplateTableEnum.LAZADA_TEMPLATE.getName());
            example.createCriteria()
                    .andArticleNumberIn(articleNumbers)
                    .andTemplateStatusIn(List.of(TemplateStatusEnum.PUBLISH_SUCCESS.getCode(), TemplateStatusEnum.PUBLISH_END.getCode()))
                    .andPublishRoleEqualTo(LazadaPublishQueueEnums.PublishRole.SYSTEM.getCode())
                    .andAccountNumberLike("%" + accountNumber + "%");
            systemPublishNums = lazadaTemplateService.selectByExample(example).size();
        }

        item.setPublishRole(systemPublishNums > 0 ? LazadaPublishQueueEnums.PublishRole.SYSTEM.getCode() : LazadaPublishQueueEnums.PublishRole.SALES.getCode());

    }
    /**
     * 设置产品系统属性
     *
     * @param item          item
     * @param productInfoVO product
     */
    public void setProductInfo(EsLazadaItem item, ProductInfoVO productInfoVO) {
        Assert.notNull(item, "item must not be null");
        if (null == productInfoVO || StringUtils.isBlank(productInfoVO.getSonSku())) {
            // 匹配组合套装
            if (matchComposeProduct(item)) {
                esLazadaItemService.save(item);
                return;
            }
        }

        // 废弃状态sku取合并sku信息
        if(StringUtils.isNotBlank(productInfoVO.getSonSku()) && StringUtils.equalsIgnoreCase(SkuStatusEnum.DISCARD.getCode(), productInfoVO.getSkuStatus())) {
            String mergeSku = ProductUtils.getMergeSku(productInfoVO.getSonSku());
            if(!StringUtils.equalsIgnoreCase(mergeSku, productInfoVO.getSonSku())) {
                productInfoVO = ProductUtils.getSkuInfo(mergeSku);
                item.setSku(StrUtil.strTrimToUpperCase(mergeSku));
            }
        }
        item.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
        item.setSpu(productInfoVO.getMainSku());
        item.setCategoryId(productInfoVO.getCatId());
        item.setCategoryIdPath(productInfoVO.getCategoryId());
        item.setCategoryCnName(productInfoVO.getCategoryCnName());
        // 禁售平台
        item.setForbidChannel(CommonUtils.splitList(productInfoVO.getForbidChannel(), ","));
        // 禁售类型
        item.setInfringementTypeNames(CommonUtils.splitList(productInfoVO.getInfringementTypeName(), "|"));
        // 禁售原因
        item.setInfringementObjs(CommonUtils.splitList(productInfoVO.getInfringementObj(), "|"));
        // 禁售站点
        item.setProhibitionSites(productInfoVO.getProhibitionSiteWithPlatformDefaultSite());
        // 单品状态
        item.setSkuStatus(productInfoVO.getSkuStatus());
        // 产品标签code
        item.setTagCodes(CommonUtils.splitList(productInfoVO.getTagCodes(), ","));
        // 特殊标签
        item.setSpecialGoodsCode(CommonUtils.splitIntList(productInfoVO.getSpecialGoodsCode(), ","));
        // 是否促销
        item.setPromotion(productInfoVO.getPromotion());
        // 是否新品
        item.setNewState(productInfoVO.getNewState());
    }

    /**
     * 匹配是否是组合套装，匹配上则用组合套装的产品信息
     * 规则:
     * 1、优先匹配组合SKU数据，若存在于组合SKU中，则取组合数据；不存在与组合SKU中，则继续判断2
     * 2、匹配套装SKU数据，若存在于套装SKU中，需通过组合套装映射表，获取对应的组合SPU，
     * 及对应的组合SPU数据状态；无组合映射关系则取套装状态即可
     * 3、不存在于套装SKU和组合SKU，则匹配管理单品数据
     *
     * @param item  listing
     */
    public boolean matchComposeProduct(EsLazadaItem item) {
        String articleNumber = item.getSku();
//        log.info("[matchComposeProduct]店铺：{},当前articleNumber：{}",item.getAccountNumber(), articleNumber);
        // 组合产品
        ComposeSku composeProduct = ProductUtils.getComposeProduct(articleNumber);
        if (composeProduct != null) {
            setProductInfoByCompose(item, composeProduct);
            return true;
        }
        // 非组合产品的查询一遍组合套装映射表
        Map<String, String> composeSkuSuitMap = ProductUtils.getComposeSkuBySuit(Collections.singletonList(articleNumber));
        if (MapUtils.isEmpty(composeSkuSuitMap)
                || StringUtils.isBlank(composeSkuSuitMap.get(articleNumber))) {
            // 套装产品
            SuiteSku suiteSku = ProductUtils.getSaleSuiteInfoBySonSku(articleNumber);
            if (suiteSku == null) {
                return false;
            }
            item.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
            item.setSkuStatus(SingleItemEnum.getEnNameByCode(suiteSku.getItemStatus()));
            ComposeCheckStepEnum stepEnum = Boolean.TRUE.equals(suiteSku.getIsEnable()) ? ComposeCheckStepEnum.NORMAL : ComposeCheckStepEnum.DISCARD;
            item.setComposeStatus(stepEnum.getCode());
            // 禁售平台
            item.setForbidChannel(suiteSku.getForbidChannels());
            // 禁售类型
            item.setInfringementTypeNames(suiteSku.getInfringementTypeNames());
            // 禁售原因
            item.setInfringementObjs(suiteSku.getInfringementObjs());
            // 禁售站点
            item.setProhibitionSites(suiteSku.getProhibitionPlatSites());
            return true;
        }
        // 套装映射的组合产品
        ComposeSku composeProductRef = ProductUtils.getComposeProduct(composeSkuSuitMap.get(articleNumber));
        if (composeProductRef != null) {
            setProductInfoByCompose(item, composeProductRef);
            return true;
        }
        return false;
    }

    private void setProductInfoByCompose(EsLazadaItem item, ComposeSku composeProduct) {
        item.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
        item.setSpu(composeProduct.getComposeSku());
        item.setComposeStatus(composeProduct.getCheckStep());
        item.setCategoryId(composeProduct.getCategoryId());
        // 类别中文名
        String categoryName = StringUtils.isNotBlank(composeProduct.getCategoryName()) ? StrUtil.strAddComma(composeProduct.getCategoryName().replaceAll(">", ",")) : null;
        item.setCategoryCnName(categoryName);
        // 禁售平台
        item.setForbidChannel(composeProduct.getForbidChannels());
        // 禁售类型
        item.setInfringementTypeNames(composeProduct.getInfringementTypeNames());
        // 禁售原因
        item.setInfringementObjs(composeProduct.getInfringementObjs());
        // 禁售站点
        item.setProhibitionSites(composeProduct.getProhibitionPlatSites());
        // 单品状态
        item.setSkuStatus(SingleItemEnum.getEnNameByCode(composeProduct.getComposeStatus()));
        // 产品标签code
        item.setTagCodes(CommonUtils.splitList(composeProduct.getTagCode(), ","));
        // 特殊标签
        item.setSpecialGoodsCode(null);
        // 是否促销
        item.setPromotion(0);
        // 是否新品
        item.setNewState(false);
    }
}
