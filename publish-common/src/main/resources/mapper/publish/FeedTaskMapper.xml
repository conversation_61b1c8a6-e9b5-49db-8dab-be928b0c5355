<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.system.feedTask.mapper.FeedTaskMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.system.feedTask.model.FeedTask" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="association_id" property="associationId" jdbcType="VARCHAR" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="article_number" property="articleNumber" jdbcType="LONGVARCHAR" />
    <result column="task_type" property="taskType" jdbcType="VARCHAR" />
    <result column="platform" property="platform" jdbcType="VARCHAR" />
    <result column="task_status" property="taskStatus" jdbcType="INTEGER" />
    <result column="result_status" property="resultStatus" jdbcType="INTEGER" />
    <result column="result_msg" property="resultMsg" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="run_time" property="runTime" jdbcType="TIMESTAMP" />
    <result column="finish_time" property="finishTime" jdbcType="TIMESTAMP" />
    <result column="attribute1" property="attribute1" jdbcType="VARCHAR" />
    <result column="attribute2" property="attribute2" jdbcType="VARCHAR" />
    <result column="attribute3" property="attribute3" jdbcType="VARCHAR" />
    <result column="attribute4" property="attribute4" jdbcType="VARCHAR" />
    <result column="attribute5" property="attribute5" jdbcType="VARCHAR" />
    <result column="attribute6" property="attribute6" jdbcType="VARCHAR" />
    <result column="attribute7" property="attribute7" jdbcType="VARCHAR" />
    <result column="attribute8" property="attribute8" jdbcType="VARCHAR" />
    <result column="attribute9" property="attribute9" jdbcType="VARCHAR" />
    <result column="attribute10" property="attribute10" jdbcType="VARCHAR" />
    <result column="attribute11" property="attribute11" jdbcType="VARCHAR" />
    <result column="attribute12" property="attribute12" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, association_id, account_number, article_number, task_type, platform, task_status, 
    result_status, result_msg, created_by, create_time, run_time, finish_time
     , attribute1,
     attribute2, attribute3, attribute4, attribute5,
     attribute6,attribute7,attribute8,attribute9,attribute10,attribute11,attribute12
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.system.feedTask.model.FeedTaskExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <choose>
      <when test="customColumn != null and customColumn != ''">
        ${customColumn}
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from
      feed_task${tableIndex}
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from feed_task${tableIndex}
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.estone.erp.publish.system.feedTask.model.FeedTask" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into
    feed_task${tableIndex}
    (association_id, account_number, article_number,
      task_type, platform, task_status, 
      result_status, result_msg, created_by, 
      create_time, run_time, finish_time, 
      attribute1, attribute2, attribute3,
      attribute4, attribute5,attribute6,attribute7,attribute8,attribute9,attribute10,attribute11,attribute12)
    values (#{associationId,jdbcType=VARCHAR}, #{accountNumber,jdbcType=VARCHAR}, #{articleNumber,jdbcType=LONGVARCHAR},
      #{taskType,jdbcType=VARCHAR}, #{platform,jdbcType=VARCHAR}, #{taskStatus,jdbcType=INTEGER}, 
      #{resultStatus,jdbcType=INTEGER}, #{resultMsg,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{runTime,jdbcType=TIMESTAMP}, #{finishTime,jdbcType=TIMESTAMP}, 
      #{attribute1,jdbcType=VARCHAR}, #{attribute2,jdbcType=VARCHAR}, #{attribute3,jdbcType=VARCHAR}, 
      #{attribute4,jdbcType=VARCHAR}, #{attribute5,jdbcType=VARCHAR},
      #{attribute6,jdbcType=VARCHAR}, #{attribute7,jdbcType=VARCHAR},
      #{attribute8,jdbcType=VARCHAR}, #{attribute9,jdbcType=VARCHAR}, #{attribute10,jdbcType=VARCHAR}, #{attribute11,jdbcType=VARCHAR}, #{attribute12,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.estone.erp.publish.system.feedTask.model.FeedTask" useGeneratedKeys="true">
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into
      feed_task${tableIndex}
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="associationId != null" >
        association_id,
      </if>
      <if test="accountNumber != null" >
        account_number,
      </if>
      <if test="articleNumber != null" >
        article_number,
      </if>
      <if test="taskType != null" >
        task_type,
      </if>
      <if test="platform != null" >
        platform,
      </if>
      <if test="taskStatus != null" >
        task_status,
      </if>
      <if test="resultStatus != null" >
        result_status,
      </if>
      <if test="resultMsg != null" >
        result_msg,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="runTime != null" >
        run_time,
      </if>
      <if test="finishTime != null" >
        finish_time,
      </if>
      <if test="attribute1 != null" >
        attribute1,
      </if>
      <if test="attribute2 != null" >
        attribute2,
      </if>
      <if test="attribute3 != null" >
        attribute3,
      </if>
      <if test="attribute4 != null" >
        attribute4,
      </if>
      <if test="attribute5 != null" >
        attribute5,
      </if>
      <if test="attribute6 != null" >
        attribute6,
      </if>
      <if test="attribute7 != null" >
        attribute7,
      </if>
      <if test="attribute8 != null" >
        attribute8,
      </if>
      <if test="attribute9 != null" >
        attribute9,
      </if>
      <if test="attribute10 != null" >
        attribute10,
      </if>
      <if test="attribute11 != null" >
        attribute11,
      </if>
      <if test="attribute12 != null" >
        attribute12,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="associationId != null" >
        #{associationId,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null" >
        #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        #{articleNumber,jdbcType=LONGVARCHAR},
      </if>
      <if test="taskType != null" >
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="platform != null" >
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null" >
        #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="resultStatus != null" >
        #{resultStatus,jdbcType=INTEGER},
      </if>
      <if test="resultMsg != null" >
        #{resultMsg,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="runTime != null" >
        #{runTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finishTime != null" >
        #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attribute1 != null" >
        #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null" >
        #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="attribute3 != null" >
        #{attribute3,jdbcType=VARCHAR},
      </if>
      <if test="attribute4 != null" >
        #{attribute4,jdbcType=VARCHAR},
      </if>
      <if test="attribute5 != null" >
        #{attribute5,jdbcType=VARCHAR},
      </if>
      <if test="attribute6 != null" >
        #{attribute6,jdbcType=VARCHAR},
      </if>
      <if test="attribute7 != null" >
        #{attribute7,jdbcType=VARCHAR},
      </if>
      <if test="attribute8 != null" >
        #{attribute8,jdbcType=VARCHAR},
      </if>
      <if test="attribute9 != null" >
        #{attribute9,jdbcType=VARCHAR},
      </if>
      <if test="attribute10 != null" >
        #{attribute10,jdbcType=VARCHAR},
      </if>
      <if test="attribute11 != null" >
        #{attribute11,jdbcType=VARCHAR},
      </if>
      <if test="attribute12 != null" >
        #{attribute12,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <!--批量新增  <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER"> SELECT LAST_INSERT_ID() </selectKey>  -->
  <insert id="batchInsertSelective" useGeneratedKeys="true" keyProperty="id">
    insert into
    feed_task${tableIndex}
    (association_id, account_number, article_number,
    task_type, platform, task_status,
    result_status, result_msg, created_by,
    create_time, run_time, finish_time,
    attribute1, attribute2, attribute3,
    attribute4, attribute5,
    attribute6,attribute7,attribute8,attribute9,attribute10,attribute11,attribute12)
    values
    <foreach collection="list" item="item" separator=",">
      (
      #{item.associationId,jdbcType=VARCHAR}, #{item.accountNumber,jdbcType=VARCHAR}, #{item.articleNumber,jdbcType=LONGVARCHAR},
      #{item.taskType,jdbcType=VARCHAR}, #{item.platform,jdbcType=VARCHAR}, #{item.taskStatus,jdbcType=INTEGER},
      #{item.resultStatus,jdbcType=INTEGER}, #{item.resultMsg,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.runTime,jdbcType=TIMESTAMP}, #{item.finishTime,jdbcType=TIMESTAMP},
      #{item.attribute1,jdbcType=VARCHAR}, #{item.attribute2,jdbcType=VARCHAR}, #{item.attribute3,jdbcType=VARCHAR},
      #{item.attribute4,jdbcType=VARCHAR}, #{item.attribute5,jdbcType=VARCHAR}, #{item.attribute6,jdbcType=VARCHAR},
      #{item.attribute7,jdbcType=VARCHAR}, #{item.attribute8,jdbcType=VARCHAR}, #{item.attribute9,jdbcType=VARCHAR},
      #{item.attribute10,jdbcType=VARCHAR}, #{item.attribute11,jdbcType=VARCHAR}, #{item.attribute12,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.system.feedTask.model.FeedTaskExample" resultType="java.lang.Integer" >
    select count(*) from feed_task${tableIndex}
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update
    feed_task${example.tableIndex}
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.associationId != null" >
        association_id = #{record.associationId,jdbcType=VARCHAR},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.taskType != null" >
        task_type = #{record.taskType,jdbcType=VARCHAR},
      </if>
      <if test="record.platform != null" >
        platform = #{record.platform,jdbcType=VARCHAR},
      </if>
      <if test="record.taskStatus != null" >
        task_status = #{record.taskStatus,jdbcType=INTEGER},
      </if>
      <if test="record.resultStatus != null" >
        result_status = #{record.resultStatus,jdbcType=INTEGER},
      </if>
      <if test="record.resultMsg != null" >
        result_msg = #{record.resultMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.runTime != null" >
        run_time = #{record.runTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.finishTime != null" >
        finish_time = #{record.finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.attribute1 != null" >
        attribute1 = #{record.attribute1,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute2 != null" >
        attribute2 = #{record.attribute2,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute3 != null" >
        attribute3 = #{record.attribute3,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute4 != null" >
        attribute4 = #{record.attribute4,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute5 != null" >
        attribute5 = #{record.attribute5,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute6 != null">
        attribute6 = #{record.attribute6,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute7 != null">
        attribute7 = #{record.attribute7,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute8 != null">
        attribute8 = #{record.attribute8,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute9 != null">
        attribute9 = #{record.attribute9,jdbcType=VARCHAR},
      </if>
      <if test="record.attribute10 != null">
        attribute10 = #{record.attribute10,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.system.feedTask.model.FeedTask" >
    update
    feed_task${tableIndex}
    <set >
      <if test="associationId != null" >
        association_id = #{associationId,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=LONGVARCHAR},
      </if>
      <if test="taskType != null" >
        task_type = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="platform != null" >
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null" >
        task_status = #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="resultStatus != null" >
        result_status = #{resultStatus,jdbcType=INTEGER},
      </if>
      <if test="resultMsg != null" >
        result_msg = #{resultMsg,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="runTime != null" >
        run_time = #{runTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finishTime != null" >
        finish_time = #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attribute1 != null" >
        attribute1 = #{attribute1,jdbcType=VARCHAR},
      </if>
      <if test="attribute2 != null" >
        attribute2 = #{attribute2,jdbcType=VARCHAR},
      </if>
      <if test="attribute3 != null" >
        attribute3 = #{attribute3,jdbcType=VARCHAR},
      </if>
      <if test="attribute4 != null" >
        attribute4 = #{attribute4,jdbcType=VARCHAR},
      </if>
      <if test="attribute5 != null" >
        attribute5 = #{attribute5,jdbcType=VARCHAR},
      </if>
      <if test="attribute6 != null">
        attribute6 = #{attribute6,jdbcType=VARCHAR},
      </if>
      <if test="attribute7 != null">
        attribute7 = #{attribute7,jdbcType=VARCHAR},
      </if>
      <if test="attribute8 != null">
        attribute8 = #{attribute8,jdbcType=VARCHAR},
      </if>
      <if test="attribute9 != null">
        attribute9 = #{attribute9,jdbcType=VARCHAR},
      </if>
      <if test="attribute10 != null">
        attribute10 = #{attribute10,jdbcType=VARCHAR},
      </if>
      <if test="attribute11 != null">
        attribute11 = #{attribute11,jdbcType=VARCHAR},
      </if>
      <if test="attribute12 != null">
        attribute12 = #{attribute12,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!--批量更新-->
  <update id="batchUpdateFeedTask">
    <foreach collection="list" item="item" separator=";" close=";">
      update
      feed_task${item.tableIndex}
      <set >
        <if test="item.associationId != null" >
          association_id = #{item.associationId,jdbcType=VARCHAR},
        </if>
        <if test="item.accountNumber != null" >
          account_number = #{item.accountNumber,jdbcType=VARCHAR},
        </if>
        <if test="item.articleNumber != null" >
          article_number = #{item.articleNumber,jdbcType=LONGVARCHAR},
        </if>
        <if test="item.taskType != null" >
          task_type = #{item.taskType,jdbcType=VARCHAR},
        </if>
        <if test="item.platform != null" >
          platform = #{item.platform,jdbcType=VARCHAR},
        </if>
        <if test="item.taskStatus != null" >
          task_status = #{item.taskStatus,jdbcType=INTEGER},
        </if>
        <if test="item.resultStatus != null" >
          result_status = #{item.resultStatus,jdbcType=INTEGER},
        </if>
        <if test="item.resultMsg != null" >
          result_msg = #{item.resultMsg,jdbcType=VARCHAR},
        </if>
        <if test="item.createdBy != null" >
          created_by = #{item.createdBy,jdbcType=VARCHAR},
        </if>
        <if test="item.createTime != null" >
          create_time = #{item.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.runTime != null" >
          run_time = #{item.runTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.finishTime != null" >
          finish_time = #{item.finishTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.attribute1 != null" >
          attribute1 = #{item.attribute1,jdbcType=VARCHAR},
        </if>
        <if test="item.attribute2 != null" >
          attribute2 = #{item.attribute2,jdbcType=VARCHAR},
        </if>
        <if test="item.attribute3 != null" >
          attribute3 = #{item.attribute3,jdbcType=VARCHAR},
        </if>
        <if test="item.attribute4 != null" >
          attribute4 = #{item.attribute4,jdbcType=VARCHAR},
        </if>
        <if test="item.attribute5 != null" >
          attribute5 = #{item.attribute5,jdbcType=VARCHAR},
        </if>
        <if test="item.attribute6 != null">
          attribute6 = #{item.attribute6,jdbcType=VARCHAR},
        </if>
        <if test="item.attribute7 != null">
          attribute7 = #{item.attribute7,jdbcType=VARCHAR},
        </if>
        <if test="item.attribute8 != null">
          attribute8 = #{item.attribute8,jdbcType=VARCHAR},
        </if>
        <if test="item.attribute9 != null">
          attribute9 = #{item.attribute9,jdbcType=VARCHAR},
        </if>
        <if test="item.attribute10 != null">
          attribute10 = #{item.attribute10,jdbcType=VARCHAR},
        </if>
        <if test="item.attribute11 != null">
          attribute11 = #{item.attribute11,jdbcType=VARCHAR},
        </if>
        <if test="item.attribute12 != null">
          attribute12 = #{item.attribute12,jdbcType=VARCHAR},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <delete id="batchDelete">
    delete from feed_task${tableIndex}
    where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <delete id="deleteByTypeAndTime">
    delete from feed_task${tableIndex}
    where task_type = #{type} and create_time <![CDATA[ < ]]> #{createTime,jdbcType=TIMESTAMP}
  </delete>

  <!--自定义删除对应平台超过 expireDay 天数的数据-->
  <delete id="deleteCreateDateExpired">
    delete from feed_task${tableIndex}
    where create_time &lt;= date_sub(NOW(), interval ${expireDay} day)
  </delete>

  <delete id="batchDeleteAmazonDownloadImageLog">
    delete from feed_task${tableIndex}
    where task_type = #{record.taskType,jdbcType=VARCHAR}
    and create_time <![CDATA[ <= ]]> DATE_FORMAT (#{record.createTime},'%Y-%m-%d %H:%i:%s')
  </delete>

  <select id="getAmazonDownloadImageExecutingCount" parameterType="com.estone.erp.publish.system.feedTask.model.FeedTask" resultType="java.lang.Integer">
    select COUNT(id) FROM
    feed_task${tableIndex}
    where task_type = #{record.taskType,jdbcType=VARCHAR}
    and task_status = #{record.taskStatus,jdbcType=INTEGER}
  </select>

  <update id="updateAmazonDownloadTaskStatusExpired" parameterType="com.estone.erp.publish.system.feedTask.model.FeedTask">
    UPDATE feed_task${tableIndex} set
    task_status = #{record.taskStatus,jdbcType=INTEGER}
    where task_type = #{record.taskType,jdbcType=VARCHAR}
      and task_status = 3
    <!--and attribute3 is null -->
    and finish_time <![CDATA[ <= ]]> DATE_FORMAT (#{record.finishTime},'%Y-%m-%d %H:%i:%s')
</update>

<update id="updateAmazonDownloadTaskStatusFile" parameterType="com.estone.erp.publish.system.feedTask.model.FeedTask">
  UPDATE feed_task${tableIndex} set
  task_status = #{record.taskStatus,jdbcType=INTEGER},
  result_msg = #{record.resultMsg,jdbcType=VARCHAR},
  finish_time = #{record.finishTime,jdbcType=TIMESTAMP},
  result_status = #{record.resultStatus,jdbcType=INTEGER}
  where task_type = #{record.taskType,jdbcType=VARCHAR}
  and task_status = 2
  and run_time <![CDATA[ <= ]]> DATE_FORMAT (#{record.runTime},'%Y-%m-%d %H:%i:%s')
  <!--<if test="record.attribute3 != null" >
    and attribute3 = #{record.attribute3,jdbcType=VARCHAR}
  </if>-->
  </update>

  <delete id="batchDeleteDownloadImage">
    delete from feed_task${tableIndex}
    where task_status = 1
    and id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <select id="customSelect" resultType="map" parameterType="java.lang.String" >
	${_parameter}
  </select>

  <select id="selectShopeeUpdateDaysToShipData" resultMap="BaseResultMap">
    SELECT
        id,
        account_number,
        article_number,
        attribute2,
        attribute4,
        attribute5,
        create_time,
        finish_time,
        association_id
    FROM
        feed_task_3
    WHERE
        created_by = 'admin'
      AND task_type = 'update_days_to_ship'
      AND create_time > '2024-01-16 00:00:00'
      and create_time <![CDATA[ <= ]]> '2024-01-19 12:00:00'
      AND task_status = 3
      AND result_status = 1
      and attribute4 > 3
      and attribute5 = 2
      AND id > #{gtId}
    ORDER BY id ASC
      LIMIT #{pageSize}
  </select>
</mapper>