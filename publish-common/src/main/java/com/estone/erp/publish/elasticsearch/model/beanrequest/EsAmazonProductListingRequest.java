package com.estone.erp.publish.elasticsearch.model.beanrequest;

import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import lombok.Data;
import org.apache.commons.lang3.tuple.Triple;

import java.util.List;
import java.util.Map;

/**
 * @Description: ${description}
 * @Author: yjy
 * @Date: 2021/1/7 16:52
 * @Version: 1.0.0
 */
@Data
public class EsAmazonProductListingRequest {

    /**
     *  id
     */
    private String id;

    /**
     * gtId
     */
    private String gtId;

    /**
     * 账号
     */
    private String accountNumber;

    /**
     * 账号等级
     */
    private String accountLevel;

    /**
     * 站点
     */
    private String site;

    /**
     * 父asin码
     */
    private String parentAsin;

    /**
     * 子asin码
     */
    private String sonAsin;

    /**
     * 平台sku
     */
    private String sellerSku;

    /**
     * 主sku
     */
    private String mainSku;

    /**
     * 主skuList
     */
    private List<String> mainSkuList;

    /**
     * 组合sku
     */
    private String composeSku;

    /**
     * 组合skuList
     */
    private List<String> composeSkuList;

    /**
     * 货号
     */
    private String articleNumber;

    /**
     * SKU数据来源(1.产品系统;2.数据分析系统(试卖);3.冠通;4.探雅)
     */
    private Integer skuDataSource;

    /**
     * 在售 true下架 false
     */
    private Boolean isOnline;

    /**
     * 名称（产品）
     */
    private String itemName;

    /**
     * 标题和描述包含的侵权词
     */
    private Boolean isInfringementWord;

    /**
     * 标题和描述包含的侵权词
     */
    private String infringementWord;

    /**
     * 标题和描述包含的侵权词
     */
    private List<String> infringementWords;

    /**
     * 禁售平台(逗号拼接)
     */
    private String forbidChannel;

    /**
     * 风险等级
     */
    private Integer riskLevelId;

    private List<Integer> riskLevelIdList;

    /**
     * 单品状态
     */
    private String skuStatus;

    /**
     * 产品标签code
     */
    private String tagCode;

    /**
     * 产品标签code
     */
    private String tagCodes;

    /**
     * 产品标签
     */
    private List<String> tagCodeList;

    /**
     * 特殊标签
     */
    private String specialGoodsCode;

    /**
     * 特殊标签
     */
    private List<String> specialGoodsCodes;

    /**
     * 价格
     */
    private Double price;

    /**
     * 平台库存数量是否为0
     */
    private Boolean quantityIsZero;

    /**
     * 平台库存数量
     */
    private Integer quantity;
    private Integer quantityGt;  //平台库存数量 > n
    private Integer quantityLt;  //平台库存数量 < n

    /**
     * 是否爆款(是：1  否：2)
     */
    private String isPopular;

    /**
     * 是否存在运费模板(是：1  否：2)
     */
    private Boolean isExistShippingGroup;

    /**
     * Amazon跟卖删除
     */
    private Boolean isFollowSellDelete;

    /**
     * 跟卖标志(是：1  否：2)
     */
    private String followSaleFlag;

    /**
     * 类别id
     */
    private String categoryId;

    /**
     * 最新上架时间
     */
    private String startOpenDate;

    /**
     * 最新上架时间
     */
    private String endOpenDate;


    private String endFirstOrOpenDate;
    /**
     * 第一次上架时间
     */
    private String startFirstOpenDate;

    /**
     * 第一次上架时间
     */
    private String endFirstOpenDate;

    /**
     * 最新下线时间
     */
    private String startOfflineDate;

    /**
     * 最新下线时间
     */
    private String endOfflineDate;

    /**
     * 是否存在最新下架时间
     */
    private Boolean isExistOfflineDate;

    /**
     * 第一次下架时间
     */
    private String startFirstOfflineDate;

    /**
     * 第一次下架时间
     */
    private String endFirstOfflineDate;

    /**
     * 同步时间
     */
    private String startSyncDate;

    /**
     * 同步时间
     */
    private String endSyncDate;

    /**
     * 修改时间
     */
    private String startUpdateDate;

    /**
     * 修改时间
     */
    private String endUpdateDate;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 订单系统7天销量
     */
    private Integer from7SaleQuantity;
    private Integer to7SaleQuantity;

    /**
     * 存储订单系统的30天销量
     */
    private Integer from30SaleQuantity;
    private Integer to30SaleQuantity;

    /**
     * es查询那个销量区间 order_24H_count, order_last_7d_count, order_last_14d_count, order_last_30d_count
     */
    private String saleQuantityBean;

    /**
     * 销量是否为空 1为空 0不为空
     */
    private Boolean isSaleQuantityNull;

    /**
     * es销量区间 24H 7D 14D 30D
     */
    private Long fromSaleQuantity;
    private Long toSaleQuantity;

    /**
     * 存储selersku大写
     * 属性2
     */
    private String attribute2;

    /**
     * id
     */
    List<String> idList;

    /**
     * amazon账号集合
     */
    private List<String> accountNumberList;

    /**
     * amazon账号等级集合
     */
    private List<String> accountLevelList;

    /**
     * 货号集合
     */
    private List<String> articleNumberList;

    /**
     * 平台sku集合
     */
    private List<String> sellerSkuList;

    /**
     * 平台sku集合(大写)
     */
    private List<String> attribute2List;

    /**
     * 子asin码集合
     */
    private List<String> sonAsinList;

    /**
     * 父asin码集合
     */
    private List<String> parentAsinList;

    /**
     * 查询当前的 父asin 并将 父asin 传入到 子asin 中查询
     * 目的就是查询出单体和变体
     *
     * 如果传入的是子asin,则会将子asin查出来，功能类似 sonAsinList 字段
     */
    private List<String> parentOrSonAsinList;

    /**
     * 站点集合
     */
    private List<String> siteList;

    /**
     * 单品状态集合
     */
    private List<String> skuStatusList;


    /**
     * 单品状态集合(not in)
     */
    private List<String> notInSkuStatusList;

    /**
     * 禁售平台集合
     */
    private List<String>  forbidChannelList;

    /**
     * item状态
     */
    private String itemStatus;

    private List<String> itemStatusList;

    /**
     * 排序字段
     */
    private String orderBy = "createDate";

    /**
     * 正序还是倒序，正序ASC,倒序DESC
     */
    private String sequence;

    /**
     * 在线列表数据
     */
    private List<EsAmazonProductListing> esAmazonProductListingList;

    /**
     * 不包含的 sellerSku 集合
     */
    private List<String> notContainSellerSkuList;

    /**
     * 引流数据
     */
    private Map<String, Boolean> map;

    /**
     * 总价区间
     */
    private Double fromTotalPrice;
    private Double toTotalPrice;

    /**
     * 利润率区间
     */
    private Double fromGrossProfitRate;
    private Double toGrossProfitRate;

    /**
     * 利润区间
     */
    private Double fromGrossProfit;
    private Double toGrossProfit;

    /**
     * 是否存在利润(是：1  否：0)
     */
    private Boolean isExistGrossProfit;

    /**
     * 销售ID集合
     */
    private List<Integer> saleManList;


    /**
     * 侵权类型 改为禁售类型
     */
    private String infringementTypename;

    /**
     * 改为禁售类型列表
     */
    private List<String> infringementTypenames;

    /**
     * 侵权对象 改为禁售原因
     */
    private String infringementObj;

    /**
     * 禁售原因列表
     */
    private List<String> infringementObjs;

    /**
     * 备注
     */
    private String attribute3;
    private List<String> attribute3List;
//    private String infringementRemark;

    /**
     * 可售站点
     */
    private String normalSale;

    /**
     * 列表导出字段
     */
    private List<String> downFields;
    /**
     * 导出列表需要查询的字段
     */
    private List<String> downListingDataNeedFields;

    /**
     * 是否存在父asin(是：1  否：0)
     */
    private Boolean isExistParentAsion;

    /**
     * 账号状态
     */
    private List<String> accountStatusList;

    /**
     * 异常状态
     */
    private List<String> exceptionStatusList;

    /**
     * 刊登角色
     */
    private List<Integer> publishRoleList;

    /**
     * 品牌
     */
    private String brandName;

    /**
     * 品牌
     */
    private String notBrandName;
    private List<String> notBrandNameList;

    /**
     * 是否存在品牌(是：1  否：0)
     */
    private Boolean isExistBrandName;

    /**
     * 是否存在conditionType(是：1  否：0)
     */
    private Boolean isExistConditionType;

    /**
     * 是否存在ItemType(是：1  否：0)
     */
    private Boolean isExistItemType;

    /**
     * ItemType
     */
    private Integer ItemType;

    /**
     * 刊登角色
     */
    private List<Integer> itemTypeList;

    /**
     * 排除的ItemType
     */
    private List<Integer> excludeItemTypeList;

    /**
     * issuesSeverity
     */
    private String issuesSeverity;

    private Integer fromFulfillmentLatency;  //平台库备货期
    private Integer toFulfillmentLatency;  //平台备货期

    /**
     * 是否促销
     */
    private Boolean isPromotion;

    /**
     * 是否新品
     */
    private Boolean isNewState;

    /**
     * 商标词标识
     */
    private List<String> trademarkIdentification;

    /**
     * 侵权类型 （侵权，违禁词， 商标词）
     */
    private String infringementWordType;

    private String starUpdateInfringementTime;

    /**
     * 是否FBA true 是 false 否
     */
    private Boolean fba;

    /**
     * 不包含的sonAsin集合
     */
    private List<String> excludeSonAsinList;


    /**
     * searchAfter
     */
    private List<Object> searchAfter;

    /**
     * 下架备注
     */
    private String offlineRemark;
    /**
     * 下架备注全文匹配
     */
    private Boolean offlineRemarkFullMatch;

    /**
     * 是否存在GPSR
     */
    private Boolean existGpsr;

    /**
     * 过滤店铺
     */
    private List<String> excludeAccountNumber;

    /**
     * 自定义销售区间查询
     * filed form to
     */
    private Triple<String, Integer, Integer> customSaleNumberFileRangeQuery;

    /**
     * 可以设置需要查询的字段，不设置则取下方默认字段
     */
    private String [] fields = {"accountNumber","site","parentAsin","sonAsin","sellerSku","articleNumber","skuDataSource",
            "isOnline","forbidChannel","skuStatus","mainImage","price","quantity","salePrice","saleStartDate",
            "saleEndDate","attribute2","openDate","offlineDate", "itemName","infringementTypename","infringementObj",
            "normalSale", "specialGoodsCode", "fulfillmentLatency", "order_last_30d_count", "order_num_total", "reportOpenDate", "issuesSeverity",
            "riskLevelId"
    };


    /**
     * 在线列表查询字段
     */
    public final static  String [] allFields = {"accountNumber","site","parentAsin","sonAsin","sellerSku","mainSku",
            "articleNumber","skuDataSource","itemStatus","isOnline","itemName","forbidChannel","skuStatus","tagCodes",
            "specialGoodsCode","mainImage","price","grossProfitRate","grossProfit","quantity","saleQuantity","salePrice",
            "saleStartDate","saleEndDate","isPopular","merchantShippingGroup","shippingCost","totalPrice","brandName",
            "categoryId","reportOpenDate","openDate","firstOpenDate","offlineDate","firstOfflineDate","syncDate",
            "createdBy","createDate","updateDate","updatedBy","attribute1","attribute2","attribute3","attribute4",
            "attribute5","order_24H_count","order_last_7d_count","order_last_14d_count","order_last_30d_count",
            "order_num_total","order_days_within_30d","infringementTypename","infringementObj","normalSale",
            "publishRole","infringingBrandWord","fulfillmentLatency","composeStatus","promotion","newState",
            "itemSummariesStastus","conditionType","itemType","trademarkIdentification","infringementWordInfos",
            "updateInfringementTime", "riskLevelId", "existGpsr"
            };

}
