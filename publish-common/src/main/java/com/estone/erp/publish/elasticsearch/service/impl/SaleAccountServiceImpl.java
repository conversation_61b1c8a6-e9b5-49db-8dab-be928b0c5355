package com.estone.erp.publish.elasticsearch.service.impl;

import com.alibaba.excel.util.BooleanUtils;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.dao.SaleAccountRepository;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountPageRequest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther yucm
 * @Date 2021/7/20
 */
@Slf4j
@Service
public class SaleAccountServiceImpl implements SaleAccountService {


    public static String[] excludeFields = new String[]{"syncTime","tokenExpireTime"};

    /**
     * 分页数量
     * */
    public static int SINGLEITEM_LISTING_COUNT = 1000;

    @Resource
    private SaleAccountRepository saleAccountRepository;

    @Override
    public List<String> getAccountList(EsSaleAccountRequest request) {
        if(null == request) {
            return null;
        }

        List<SaleAccount> saleAccountList = getSaleAccountsEs(request, "accountNumber");
        List<String> accountNumberList = saleAccountList.stream().map(SaleAccount::getAccountNumber).distinct().collect(Collectors.toList());
        return accountNumberList;
    }

    @Override
    public List<String> getSaleIdAndAccount(EsSaleAccountRequest request) {
        List<String> saleIdAccountList = new ArrayList<>();

        Map<String, List<String>> saleAccountMap = getAccountAndSaleIdMap(request);

        if (MapUtils.isEmpty(saleAccountMap)) {
            return new ArrayList<>();
        }

        for (Map.Entry<String, List<String>> entry : saleAccountMap.entrySet()) {
            String account = entry.getKey();
            List<String> saleManIdList = entry.getValue();
            for (String saleId : saleManIdList) {
                if (request.getSaleIds().contains(saleId)) {
                    saleIdAccountList.add(saleId + "&" + account);
                }
            }
        }

        return saleIdAccountList;
    }

    @Override
    public Map<String, List<String>> getAccountAndSaleIdMap(EsSaleAccountRequest request) {
        if (null == request) {
            return null;
        }

        List<SaleAccount> saleAccountList = getSaleAccountsEs(request, "accountNumber", "salesperson");
        Map<String, List<String>> saleAccountMap = saleAccountList.stream()
                .filter(o -> o.getSalesperson() != null)
                .collect(Collectors.toMap(SaleAccount::getAccountNumber, o -> List.of(o.getSalesperson().split(",")), (key1 , key2)-> key2));

        return saleAccountMap;
    }

    @Override
    public Map<String, List<String>> filterFrozenStoreAndSip(String saleChannel, List<String> accountGroupNames) {
        Map<String, List<String>> dataMap = new HashMap<>();

        // 过滤冻结店铺和SIP判断
        String[] withFields = {"accountNumber",  "accountStatus"};
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(saleChannel);
        request.setAccountNumberList(accountGroupNames);
        request.setExcludeAccountStatusList(Collections.singletonList(SaleAccountStastusEnum.FROZEN.getCode()));
        List<SaleAccount> saleAccounts = getSaleAccountsEs(request, withFields);
        if (CollectionUtils.isEmpty(saleAccounts)) {
            return dataMap;
        }

        // 过滤冻结店铺和SIP
        List<String> saleAccountNumbers = saleAccounts.stream().map(SaleAccount::getAccountNumber).collect(Collectors.toList());
        dataMap.put("filterAccounts", accountGroupNames.stream().filter(account -> !saleAccountNumbers.contains(account)).collect(Collectors.toList()));
        dataMap.put("execAccounts", accountGroupNames.stream().filter(saleAccountNumbers::contains).collect(Collectors.toList()));
        return dataMap;
    }

    public List<SaleAccount> getSaleAccountsEs(EsSaleAccountRequest request, String... withFields) {
        // 查询条件
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
        if(withFields != null && withFields.length > 0){
            nativeSearchQueryBuilder.withFields(withFields);
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        // 平台
        String saleChannel = request.getSaleChannel();
        if(StringUtils.isNotBlank(saleChannel)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("saleChannel.keyword", saleChannel));
            if(saleChannel.equals(SaleChannel.CHANNEL_SHOPEE)) {
                if (Boolean.TRUE.equals(request.getIsNeedShopeeSip())) {
                    boolQueryBuilder.must(QueryBuilders.termQuery("colBool2", true));
                } else { // Shopee不查sip账号 sip刊登系统不处理
                    boolQueryBuilder.must(QueryBuilders.termQuery("colBool2", false));
                }
            }
        }

        if (request.getOverseasBusiness() != null) {
            if (BooleanUtils.isTrue(request.getOverseasBusiness())) {
                boolQueryBuilder.must(QueryBuilders.termQuery("overseaWarehouse", true));
            } else {
                BoolQueryBuilder boolQueryBuilder1 = QueryBuilders.boolQuery();
                boolQueryBuilder1.should(QueryBuilders.termQuery("overseaWarehouse", false));
                boolQueryBuilder1.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("overseaWarehouse")));
                boolQueryBuilder.must(boolQueryBuilder1);
            }
        }

        //仅仅需要sip 店铺
        if(request.getIsNeedSip() != null && request.getIsNeedSip()){
            boolQueryBuilder.must(QueryBuilders.termQuery("colBool2", true));
        }

        //smt 半托管店铺
        if(request.getIsNeedDistributor() != null && request.getIsNeedDistributor()){
            boolQueryBuilder.must(QueryBuilders.termQuery("colBool3", true));
        }

        //amazon 套账ID
        if (StringUtils.isNotBlank(request.getMerchantId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("merchantId.keyword", request.getMerchantId()));
        }

        // 人员ID
        List<Integer> employeeNos = request.getEmployeeIds();
        if(CollectionUtils.isNotEmpty(employeeNos)) {
            boolQueryBuilder.must(QueryBuilders.boolQuery()
                    .should(QueryBuilders.termsQuery("salesperson", employeeNos))
                    .should(QueryBuilders.termsQuery("customerService", employeeNos))
            );
        }

        // 状态
        List<String> statusList = request.getAccountStatusList();
        if(CollectionUtils.isNotEmpty(statusList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("accountStatus.keyword", statusList));
        }

        // 排除状态
        List<String> excludeStatusList = request.getExcludeAccountStatusList();
        if (CollectionUtils.isNotEmpty(excludeStatusList)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("accountStatus.keyword", excludeStatusList));
        }

        // 异常状态
        List<String> exceptionStatusList = request.getExceptionStatusList();
        if(CollectionUtils.isNotEmpty(exceptionStatusList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("abnormalCause.keyword", exceptionStatusList));
        }

        // 站点
        if(CollectionUtils.isNotEmpty(request.getAccountSites())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("accountSite.keyword", request.getAccountSites()));
        }

        // 销售
        List<String> saleIds = request.getSaleIds();
        if(CollectionUtils.isNotEmpty(saleIds)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("salesperson", saleIds));
        }

        // 主管
        String supervisorId = request.getSupervisorId();
        if(StringUtils.isNotBlank(supervisorId)) {
            boolQueryBuilder.must(QueryBuilders.boolQuery()
                    .should(QueryBuilders.termsQuery("salesSupervisor", supervisorId))
                    .should(QueryBuilders.termsQuery("salesJuniorSupervisor", supervisorId))
            );
        }

        // 账号列表
        List<String> accountNumberList = request.getAccountNumberList();
        if(CollectionUtils.isNotEmpty(accountNumberList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber.keyword", accountNumberList));
        }

        // 账号异常状态
        String exceptionStatus = request.getExceptionStatus();
        if(StringUtils.isNotBlank(exceptionStatus)){
            boolQueryBuilder.must(QueryBuilders.termQuery("abnormalCause.keyword", exceptionStatus));
        }

        // amazon实验账号
        Boolean subscibeMsgStatus = request.getSubscibeMsgStatus();
        if(ObjectUtils.isNotEmpty(subscibeMsgStatus)){
            boolQueryBuilder.must(QueryBuilders.termQuery("colBool1", subscibeMsgStatus));
        }

        // 排序 accountNumber逆序
        NativeSearchQuery query = nativeSearchQueryBuilder.withQuery(boolQueryBuilder)
                .withSort(SortBuilders.fieldSort("accountNumber.keyword").order(SortOrder.ASC))
                .withPageable(PageRequest.of(0, SINGLEITEM_LISTING_COUNT)).build();

        List<SaleAccount> saleAccountList = new ArrayList<>();
        query.setTrackTotalHits(true);
        int retry = 3;
        do {
            try {
                Page<SaleAccount> result = saleAccountRepository.search(query);
                List<SaleAccount> content = result.getContent();
                if(CollectionUtils.isNotEmpty(content)){
                    saleAccountList.addAll(content);
                }
                // 判断是否需要分页
                if (result.getTotalPages() > 1) {
                    for (int i = 1; i < result.getTotalPages(); i++) {
                        NativeSearchQuery pageQuery = nativeSearchQueryBuilder.withPageable(PageRequest.of(i, SINGLEITEM_LISTING_COUNT)).build();
                        saleAccountList.addAll(saleAccountRepository.search(pageQuery).getContent());
                    }
                }
                break;
            }catch (Exception e){
                log.error("查询ES出错：",e);
            }
        }while (--retry > 0);
        return saleAccountList;
    }

    @Override
    public Page<SaleAccount> page(EsSaleAccountPageRequest request, int pageSize, int offset) {

        // 查询条件
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();

        // 查询字段 有默认值 也可以调用者自己设置
        String[] fields = request.getFields();
        if(fields != null && fields.length != 0) {
            nativeSearchQueryBuilder.withFields(fields);
        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        // 平台
        String saleChannel = request.getSaleChannel();
        if(StringUtils.isNotBlank(saleChannel)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("saleChannel.keyword", saleChannel));
        }

        // 状态
        List<String> statusList = request.getAccountStatusList();
        if(CollectionUtils.isNotEmpty(statusList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("accountStatus.keyword", statusList));
        }

        // 账号
        List<String> accountNumberList = request.getAccountNumberList();
        if(CollectionUtils.isNotEmpty(accountNumberList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber.keyword", accountNumberList));
        }

        //创建查询条件构造器
        nativeSearchQueryBuilder.withQuery(boolQueryBuilder);
        //构建分页
        // 每页条数
        pageSize = pageSize == 0 ? 10 : pageSize;
        //es的分页的页码从0开始

        int pageIndex = offset / pageSize;
        pageIndex = pageIndex < 1 ? 0 : pageIndex;

        // 排序 id逆序
        nativeSearchQueryBuilder.withSort(SortBuilders.fieldSort("id").order(SortOrder.DESC));

        nativeSearchQueryBuilder.withPageable(PageRequest.of(pageIndex, pageSize));
        NativeSearchQuery searchQuery = nativeSearchQueryBuilder.build();
        searchQuery.setTrackTotalHits(true);
        return saleAccountRepository.search(searchQuery);
    }


    @Override
    public List<SaleAccount> getAccountInfoList(EsSaleAccountRequest request, String... withFields) {
        return getSaleAccountsEs(request, withFields);
    }

    /**
     * 获取同一套商家ID下所属店铺
     *
     * @param merchantId  商家Id
     * @param saleChannel 平台
     * @return List<String> 店铺
     */
    @Override
    public List<String> getSameMerchantAccountNumber(String merchantId, String saleChannel) {
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setMerchantId(merchantId);
        request.setSaleChannel(saleChannel);
        List<SaleAccount> accounts = getSaleAccountsEs(request, "accountNumber", "merchantId");
        if (CollectionUtils.isEmpty(accounts)) {
            return Collections.emptyList();
        }

        List<String> accountNumbers = accounts.stream()
                .map(SaleAccount::getAccountNumber)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accountNumbers)) {
            return Collections.emptyList();
        }
        return accountNumbers;
    }

    /**
     * 获取商家ID和店铺ID对应关系
     *
     * @param accountNumberList 店铺
     * @param saleChannel       平台
     * @return
     */
    @Override
    public Map<String, String> getAccountAndMerchantIdMap(List<String> accountNumberList, String saleChannel) {
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setAccountNumberList(accountNumberList);
        request.setSaleChannel(saleChannel);
        List<SaleAccount> accounts = getSaleAccountsEs(request, "accountNumber", "merchantId");
        if (CollectionUtils.isEmpty(accounts)) {
            return Collections.emptyMap();
        }

        return accounts.stream()
                .filter(account -> StringUtils.isNotBlank(account.getMerchantId()))
                .filter(account -> StringUtils.isNotBlank(account.getAccountNumber()))
                .collect(Collectors.toMap(SaleAccount::getAccountNumber, SaleAccount::getMerchantId, (key1, key2) -> key2));
    }


    /**
     * 获取员工与下级管理的店铺
     *
     * @param employeeNos
     * @param saleChannel
     * @return
     */
    @Override
    public List<String> getEmployeeManagedAccountNumbers(List<String> employeeNos, String saleChannel) {
        List<NewUser> userList = employeeNos.stream().map(employeeNo -> {
            ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils.subordinateTeamLeaderByEmployeeNo(saleChannel, employeeNo);
            if (listApiResult.isSuccess()) {
                return listApiResult.getResult();
            }
            return null;
        }).filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());


        List<String> employeeIds = userList.stream()
                .map(NewUser::getEmployeeId)
                .map(String::valueOf)
                .distinct()
                .collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(employeeIds)) {
            throw new RuntimeException("选择的销售主管查不到下级！");
        }
        EsSaleAccountRequest saleAccountRequest = new EsSaleAccountRequest();
        saleAccountRequest.setSaleIds(employeeIds);
        saleAccountRequest.setAccountStatusList(Collections.singletonList(SaleAccountStastusEnum.NORMAL.getCode()));
        saleAccountRequest.setSaleChannel(saleChannel);
        List<SaleAccount> accountInfoList = getAccountInfoList(saleAccountRequest);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(accountInfoList)) {
            throw new RuntimeException("选择的销售主管没有权限！");
        }
        // 销售管理的店铺
        return accountInfoList.stream()
                .map(SaleAccount::getAccountNumber)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }
}
