package com.estone.erp.publish.common.executors;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * fruugo线程池管理类
 * 
 */
public class FruugoExecutors {

    public static final ThreadPoolExecutor SYNC_PRODUCT_POOL = ExecutorUtils
            .newFixedThreadPool("fruugo-sync-product-pool", 20);

    public static final ThreadPoolExecutor SYNC_ITEM_POOL = ExecutorUtils.
            newFixedThreadPool("fruugo-sync-item-pool",100);

    public static final ThreadPoolExecutor SYNC_ITEM_INFO_POOL = ExecutorUtils.
            newFixedThreadPool("fruugo-sync-item-info-pool",16);

    public static final ThreadPoolExecutor SYNC_ITEM_DETAIL_POOL = ExecutorUtils
            .newFixedThreadPool("fruugo-sync-item-detail-pool", 50);

    public static final ThreadPoolExecutor UPDATE_ATTRIBUTE_POOL = ExecutorUtils
            .newFixedThreadPool("fruugo-update-attribute-pool", 50);

    public static final ThreadPoolExecutor BATCH_UPLOAD_ITEM_POOL = ExecutorUtils
            .newFixedThreadPool("fruugo-batch-upload-item-pool", 50);

    public static final ThreadPoolExecutor UPDATE_MANUFACTURE_POOL = ExecutorUtils
            .newFixedThreadPool("fruugo-update-manufacture-pool", 50);
    public static final ThreadPoolExecutor SALE_DATA_STATISTICS =  ExecutorUtils.
            newFixedThreadPool("fruugo-sale_data_statistics-pool",10);

    public static final ThreadPoolExecutor SYNC_ACCOUNT_CONFIG =  ExecutorUtils.
            newFixedThreadPool("fruugo-sync_account_config",1);

    public static final ThreadPoolExecutor UPLOAD_LISTING_POOL =  ExecutorUtils.
            newFixedThreadPool("upload_listing_pool",10);

    public static final ThreadPoolExecutor GENERATE_TEMPLATE_POOL =  ExecutorUtils.
            newFixedThreadPool("generate_template_pool",10);

    public static final ThreadPoolExecutor UPDATE_INVENTORY_PRICE_POOL=  ExecutorUtils.
            newFixedThreadPool("update_inventory_price_pool",30);

    public static final ThreadPoolExecutor CHECK_INFRINGEMENT_WORDS_POOL =  ExecutorUtils.
            newFixedThreadPool("check_infringement_words_pool",10);


    public static final ThreadPoolExecutor AUTO_PUBLISH_QUEUE_POOL = ExecutorUtils
            .newFixedThreadPool("auto-publish-queue-pool", 10);

    public static void executeSyncProduct(Runnable runnable) {
        ExecutorUtils.execute(SYNC_PRODUCT_POOL, runnable, "fruugo-product");
    }

    public static void executeSyncItem(Runnable runnable){
        ExecutorUtils.execute(SYNC_ITEM_POOL,runnable,"fruugo-item");
    }

    public static void executeSyncItemInfo(Runnable runnable){
        ExecutorUtils.execute(SYNC_ITEM_INFO_POOL,runnable,"fruugo-item-info");
    }

    public static void executeSyncAccountConfig(Runnable runnable){
        ExecutorUtils.execute(SYNC_ACCOUNT_CONFIG,runnable,"fruugo-sync_account_config");
    }


    /**
     * 自动刊登 处理
     *
     * @param runnable
     */
    public static void executeAutoQueuePublish(Runnable runnable) {
        ExecutorUtils.execute(AUTO_PUBLISH_QUEUE_POOL, runnable, "auto-publish-generate-queue");
    }

}
