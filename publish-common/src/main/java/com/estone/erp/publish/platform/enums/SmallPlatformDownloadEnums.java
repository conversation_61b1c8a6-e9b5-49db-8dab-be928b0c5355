package com.estone.erp.publish.platform.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

public interface SmallPlatformDownloadEnums {


    /**
     * 类型
     */
    enum Type {
        LISTING,
        OFFLINE_LISTING,
        ADMIN_TEMPLATE,
        REG<PERSON>LAR_TEMPLATE,
        UPDATE_STOCK,
        UPDATE_PRICE,
        UPDATE_WEIGHT,
        FEED_TASK,
        ATTRIBUTE_EXPORT,
        ATTRIBUTE_IMPORT,
        CATEGORY_ATTRIBUTE_EXPORT,
        TITLE_AND_DESCPRICTION,
        FEED_TASK_PUBLISH_TEMPLATE,
        UPDATE_TEMPLATE_TITLE_AND_DESC,
        REPORT_PROBLEM,
        ;
    }

    @Getter
    @AllArgsConstructor
    enum Status {
        WAIT(1, "排队中"),

        EXECUTING(2, "执行中"),

        COMPLETE(3, "完成"),

        FAIL(4, "失败"),
        ;

        private final int code;
        private final String name;
    }

}
