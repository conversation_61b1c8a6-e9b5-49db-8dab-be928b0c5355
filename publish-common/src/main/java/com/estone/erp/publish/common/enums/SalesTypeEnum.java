package com.estone.erp.publish.common.enums;

import java.util.ArrayList;
import java.util.List;


public enum SalesTypeEnum {
    ORDER_LAST_1D_COUNT(1, "24H销量", "orderLast1dCount"),
    ORDER_LAST_7D_COUNT(2, "7天销量", "orderLast70dCount"),
    ORDER_LAST_14D_COUNT(3, "14天销量", "orderLast14dCount"),
    ORDER_LAST_30D_COUNT(4, "30天销量", "orderLast30dCount"),
    ORDER_LAST_60D_COUNT(5, "60天销量", "orderLast60dCount"),
    ORDER_LAST_90D_COUNT(6, "90天销量", "orderLast90dCount");

    private int code;
    private String name;
    private String enName;

    SalesTypeEnum(int code, String name, String enName) {
        this.code = code;
        this.name = name;
        this.enName = enName;
    }

    public static String getNameByCode(int code) {
        SalesTypeEnum[] values = SalesTypeEnum.values();
        for (SalesTypeEnum value : values) {
            if (value.getCode() == code) {
                return value.getName();
            }
        }
        return null;
    }

    public static Integer getCodeByEnName(String enName) {
        SalesTypeEnum[] values = SalesTypeEnum.values();
        for (SalesTypeEnum value : values) {
            if (value.getEnName().equals(enName)) {
                return value.getCode();
            }
        }
        return null;
    }


    public static List<Integer> getCodeListByEnNames(List<String> enNameList) {
        SalesTypeEnum[] values = SalesTypeEnum.values();

        List<Integer> statusList = new ArrayList<>();

        for (String enName : enNameList) {
            for (SalesTypeEnum value : values) {
                if (value.getEnName().equals(enName)) {
                    statusList.add(value.getCode());
                }
            }
        }
        return statusList;
    }

    public static String getEnNameByCode(int code) {
        SalesTypeEnum[] values = SalesTypeEnum.values();
        for (SalesTypeEnum value : values) {
            if (value.getCode() == code) {
                return value.getEnName();
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getEnName() {
        return enName;
    }
}
