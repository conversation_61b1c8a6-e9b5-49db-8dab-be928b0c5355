package com.estone.erp.publish.elasticsearch4.util;

import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsOzonItemRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.*;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

public class EsOzonItemUtils {

    public static void setQuery(BoolQueryBuilder boolQueryBuilder, EsOzonItemRequest request) {
        // 账号
        if(StringUtils.isNotBlank(request.getAccountNumber())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("accountNumber", request.getAccountNumber()));
        }
        if(CollectionUtils.isNotEmpty(request.getAccountNumbers())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber", request.getAccountNumbers()));
        }

        // id
        if(StringUtils.isNotBlank(request.getId())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("id", request.getId()));
        }
        if (StringUtils.isNotBlank(request.getGtId())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("id").gt(request.getGtId()));
        }
        if(CollectionUtils.isNotEmpty(request.getIds())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("id", request.getIds()));
        }

        // 查询id大于该值的数据
        String greaterThanId = request.getGreaterThanId();
        if (org.apache.commons.lang.StringUtils.isNotBlank(greaterThanId)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("id").gt(greaterThanId));
        }


        // sellerSku
        if(StringUtils.isNotBlank(request.getSellerSku())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("sellerSku", request.getSellerSku()));
        }
        if(CollectionUtils.isNotEmpty(request.getSellerSkus())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("sellerSku", request.getSellerSkus()));
        }
        // statusCode
        if(StringUtils.isNotBlank(request.getStatusCode())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("statusCode", request.getStatusCode()));
        }
        if(CollectionUtils.isNotEmpty(request.getStatusCodes())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("statusCode", request.getStatusCodes()));
        }
        // sku
        if(StringUtils.isNotBlank(request.getSku())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("sku", request.getSku()));
        }
        if(CollectionUtils.isNotEmpty(request.getSkus())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("sku", request.getSkus()));
        }
        if(StringUtils.isNotBlank(request.getSkuLike())) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("sku", "*" + request.getSkuLike() + "*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }
        // spu
        if(StringUtils.isNotBlank(request.getSpu())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("spu", request.getSpu()));
        }
        if(CollectionUtils.isNotEmpty(request.getSpus())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("spu", request.getSpus()));
        }
        // productId
        if(request.getProductId() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("productId", request.getProductId()));
        }
        if (request.getGteProductId() != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("productId").gte(request.getGteProductId()));
        }
        if(CollectionUtils.isNotEmpty(request.getProductIds())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("productId", request.getProductIds()));
        }
        // name
        if(StringUtils.isNotBlank(request.getName())) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("name", "*" + StringUtils.trim(request.getName()) + "*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }
        // skuStatus
        if(StringUtils.isNotBlank(request.getSkuStatus())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("skuStatus", request.getSkuStatus()));
        }
        if(CollectionUtils.isNotEmpty(request.getSkuStatusList())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("skuStatus", request.getSkuStatusList()));
        }
        if(CollectionUtils.isNotEmpty(request.getExcludeSkuStatus())) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("skuStatus", request.getExcludeSkuStatus()));
        }


        // categoryId
        if(request.getCategoryId() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("productCategoryId", request.getCategoryId()));
        }
        if(CollectionUtils.isNotEmpty(request.getCategoryIds())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("productCategoryId", request.getCategoryIds()));
        }

        // 库存
        if (request.getFromStock() != null && request.getToStock() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("stock")
                    .from(request.getFromStock())
                    .to(request.getToStock()));
        }
        if (request.getFromStock() != null && request.getToStock() == null) {
            boolQueryBuilder.must(new RangeQueryBuilder("stock")
                    .gte(request.getFromStock()));
        }
        if (request.getFromStock() == null && request.getToStock() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("stock")
                    .lte(request.getToStock()));
        }

        String warehouseName = request.getWarehouseName();
        // 指定仓库库存
        if (BooleanUtils.isTrue(request.getIsStockLeftClosedRightOpen())) {
            Long warehouseId = request.getWarehouseId();
            Integer fromWarehouseStock = request.getFromWarehouseStock();
            Integer toWarehouseStock = request.getToWarehouseStock();
            if (null != warehouseId) {
                BoolQueryBuilder stockBool = QueryBuilders.boolQuery();
                stockBool.must(QueryBuilders.termQuery("warehouseStockInfos.warehouseId", warehouseId));
                if (fromWarehouseStock != null && toWarehouseStock != null) {
                    stockBool.must(new RangeQueryBuilder("warehouseStockInfos.present")
                            .from(fromWarehouseStock)
                            .to(toWarehouseStock, false));
                } else if (fromWarehouseStock != null) {
                    stockBool.must(new RangeQueryBuilder("warehouseStockInfos.present")
                            .gte(fromWarehouseStock));
                } else if (toWarehouseStock != null) {
                    stockBool.must(new RangeQueryBuilder("warehouseStockInfos.present")
                            .lt(toWarehouseStock));
                }
                NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("warehouseStockInfos", stockBool, ScoreMode.Total);
                boolQueryBuilder.filter(nestedQuery);
            }
        } else {
            Long warehouseId = request.getWarehouseId();
            Integer fromWarehouseStock = request.getFromWarehouseStock();
            Integer toWarehouseStock = request.getToWarehouseStock();
            if (null != warehouseId) {
                BoolQueryBuilder stockBool = QueryBuilders.boolQuery();
                stockBool.must(QueryBuilders.termQuery("warehouseStockInfos.warehouseId", warehouseId));
                if (fromWarehouseStock != null && toWarehouseStock != null) {
                    stockBool.must(new RangeQueryBuilder("warehouseStockInfos.present")
                            .from(fromWarehouseStock)
                            .to(toWarehouseStock));
                } else if (fromWarehouseStock != null) {
                    stockBool.must(new RangeQueryBuilder("warehouseStockInfos.present")
                            .gte(fromWarehouseStock));
                } else if (toWarehouseStock != null) {
                    stockBool.must(new RangeQueryBuilder("warehouseStockInfos.present")
                            .lte(toWarehouseStock));
                }
                NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("warehouseStockInfos", stockBool, ScoreMode.Total);
                boolQueryBuilder.filter(nestedQuery);
            } else {
                if (StringUtils.isNotBlank(warehouseName)) {
                    BoolQueryBuilder stockBool = QueryBuilders.boolQuery();
                    stockBool.must(QueryBuilders.termQuery("warehouseStockInfos.warehouseName", warehouseName));
                    if (fromWarehouseStock != null && toWarehouseStock != null) {
                        stockBool.must(new RangeQueryBuilder("warehouseStockInfos.present")
                                .from(fromWarehouseStock)
                                .to(toWarehouseStock));
                    } else if (fromWarehouseStock != null) {
                        stockBool.must(new RangeQueryBuilder("warehouseStockInfos.present")
                                .gte(fromWarehouseStock));
                    } else if (toWarehouseStock != null) {
                        stockBool.must(new RangeQueryBuilder("warehouseStockInfos.present")
                                .lte(toWarehouseStock));
                    }
                    NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("warehouseStockInfos", stockBool, ScoreMode.Total);
                    boolQueryBuilder.filter(nestedQuery);
                }
            }
        }

        // 仓库毛利率
        Double grossProfitFrom = request.getGrossProfitFrom();
        Double grossProfitTo = request.getGrossProfitTo();
        if (StringUtils.isNotBlank(warehouseName) && (null != grossProfitFrom || null != grossProfitTo)) {
            BoolQueryBuilder grossProfitBool = QueryBuilders.boolQuery();
            grossProfitBool.must(QueryBuilders.termQuery("esOzonGrossProfits.warehouseName", warehouseName));
            if (grossProfitFrom != null && grossProfitTo != null) {
                grossProfitBool.must(new RangeQueryBuilder("esOzonGrossProfits.grossProfit")
                        .from(grossProfitFrom)
                        .to(grossProfitTo));
            } else if (grossProfitFrom != null) {
                grossProfitBool.must(new RangeQueryBuilder("esOzonGrossProfits.grossProfit")
                        .gte(grossProfitFrom));
            } else {
                grossProfitBool.must(new RangeQueryBuilder("esOzonGrossProfits.grossProfit")
                        .lte(grossProfitTo));
            }

            NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("esOzonGrossProfits", grossProfitBool, ScoreMode.Total);
            boolQueryBuilder.filter(nestedQuery);
        }



        // 毛利率差值绝对值
        Double grossProfitRateDifferenceAbsoluteFrom = request.getGrossProfitRateDifferenceAbsoluteFrom();
        Double grossProfitRateDifferenceAbsoluteTo = request.getGrossProfitRateDifferenceAbsoluteTo();
        if ((null != grossProfitRateDifferenceAbsoluteFrom || null != grossProfitRateDifferenceAbsoluteTo)) {
            BoolQueryBuilder grossProfitRateDifferenceAbsoluteBool = QueryBuilders.boolQuery();
            if (grossProfitRateDifferenceAbsoluteFrom != null && grossProfitRateDifferenceAbsoluteTo != null) {
                grossProfitRateDifferenceAbsoluteBool.must(new RangeQueryBuilder("esOzonGrossProfits.grossProfitRateDifferenceAbsolute")
                        .from(grossProfitRateDifferenceAbsoluteFrom)
                        .to(grossProfitRateDifferenceAbsoluteTo));
            } else if (grossProfitRateDifferenceAbsoluteFrom != null) {
                grossProfitRateDifferenceAbsoluteBool.must(new RangeQueryBuilder("esOzonGrossProfits.grossProfitRateDifferenceAbsolute")
                        .gte(grossProfitRateDifferenceAbsoluteFrom));
            } else {
                grossProfitRateDifferenceAbsoluteBool.must(new RangeQueryBuilder("esOzonGrossProfits.grossProfitRateDifferenceAbsolute")
                        .lte(grossProfitRateDifferenceAbsoluteTo));
            }

            NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("esOzonGrossProfits", grossProfitRateDifferenceAbsoluteBool, ScoreMode.Total);
            boolQueryBuilder.filter(nestedQuery);
        }

        //  价格
        if (request.getFromPrice() != null && request.getToPrice() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("priceNumber")
                    .from(request.getFromPrice())
                    .to(request.getToPrice()));
        }
        if (request.getFromPrice() != null && request.getToPrice() == null) {
            boolQueryBuilder.must(new RangeQueryBuilder("priceNumber")
                    .gte(request.getFromPrice()));
        }
        if (request.getFromPrice() == null && request.getToPrice() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("priceNumber")
                    .lte(request.getToPrice()));
        }

        //  原价
        if (request.getFromOldPrice() != null && request.getToOldPrice() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("oldPriceNumber")
                    .from(request.getFromOldPrice())
                    .to(request.getToOldPrice()));
        }
        if (request.getFromOldPrice() != null && request.getToOldPrice() == null) {
            boolQueryBuilder.must(new RangeQueryBuilder("oldPriceNumber")
                    .gte(request.getFromOldPrice()));
        }
        if (request.getFromOldPrice() == null && request.getToOldPrice() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("oldPriceNumber")
                    .lte(request.getToOldPrice()));
        }


        //  最低价格
        if (request.getFromMinPrice() != null && request.getToMinPrice() != null) {
            //当查询条件为0时兼容字段为空的数据
            if (request.getFromMinPrice().equals(0.0)) {
                BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                BoolQueryBuilder bool1 = QueryBuilders.boolQuery()
                        .must(new RangeQueryBuilder("minPriceNumber").lte(request.getToMinPrice()));
                shouldQuery.should(bool1);
                BoolQueryBuilder bool2 = QueryBuilders.boolQuery()
                        .mustNot(QueryBuilders.existsQuery("minPriceNumber"));
                shouldQuery.should(bool2);
                boolQueryBuilder.must(shouldQuery);
            } else {
                boolQueryBuilder.must(new RangeQueryBuilder("minPriceNumber")
                        .from(request.getFromMinPrice())
                        .to(request.getToMinPrice()));
            }

        }
        if (request.getFromMinPrice() != null && request.getToMinPrice() == null) {
            //当查询条件为0时兼容字段为空的数据
            if (request.getFromMinPrice().equals(0.0)) {
                BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                BoolQueryBuilder bool1 = QueryBuilders.boolQuery()
                        .must(new RangeQueryBuilder("minPriceNumber").gte(request.getFromMinPrice()));
                shouldQuery.should(bool1);
                BoolQueryBuilder bool2 = QueryBuilders.boolQuery()
                        .mustNot(QueryBuilders.existsQuery("minPriceNumber"));
                shouldQuery.should(bool2);
                boolQueryBuilder.must(shouldQuery);
            } else {
                boolQueryBuilder.must(new RangeQueryBuilder("minPriceNumber")
                        .gte(request.getFromMinPrice()));
            }
        }
        if (request.getFromMinPrice() == null && request.getToMinPrice() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("minPriceNumber")
                    .lte(request.getToMinPrice()));
        }

        if(request.getMinPriceNumberUpdateDateFrom() != null){
            boolQueryBuilder.must(QueryBuilders.rangeQuery("minPriceNumberUpdateDate").gte(request.getMinPriceNumberUpdateDateFrom()));
        }
        if(request.getMinPriceNumberUpdateDateTo()!= null){
            boolQueryBuilder.must(QueryBuilders.rangeQuery("minPriceNumberUpdateDate").lte(request.getMinPriceNumberUpdateDateTo()));
        }

        // 货币
        if (StringUtils.isNotBlank(request.getCurrencyCode())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("currencyCode", request.getCurrencyCode()));
        }

        // 重量
        if (request.getFromWeight() != null && request.getToWeight() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("weight")
                    .from(request.getFromWeight())
                    .to(request.getToWeight()));
        }
        if (request.getFromWeight() != null && request.getToWeight() == null) {
            boolQueryBuilder.must(new RangeQueryBuilder("weight")
                    .gte(request.getFromWeight()));
        }
        if (request.getFromWeight() == null && request.getToWeight() != null) {
            boolQueryBuilder.must(new RangeQueryBuilder("weight")
                    .lte(request.getToWeight()));
        }

        // 禁售平台
        List<String> forbidChannelList = request.getForbidChannel();
        if (CollectionUtils.isNotEmpty(forbidChannelList)) {
            boolQueryBuilder.must(new TermsQueryBuilder("forbidChannel", forbidChannelList));
        }

        // 禁售类型
        List<String> infringementTypeNames = request.getInfringementTypeNames();
        if (CollectionUtils.isNotEmpty(infringementTypeNames)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("infringementTypeNames", infringementTypeNames));
        }

        // 禁售原因
        List<String> infringementObjs = request.getInfringementObjs();
        if (CollectionUtils.isNotEmpty(infringementObjs)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("infringementObjs", infringementObjs));
        }

        // 禁售站点
        List<String> prohibitionSites = request.getProhibitionSites();
        if (CollectionUtils.isNotEmpty(prohibitionSites)) {
            // 禁售平台不为空就走过滤，否则模糊查询
            if (CollectionUtils.isNotEmpty(forbidChannelList)) {
                List<String> channelSites = new ArrayList<>();
                for (String channel : forbidChannelList) {
                    List<String> sites = prohibitionSites.stream()
                            .filter(StringUtils::isNotBlank)
                            .map(site -> channel + "_" + site)
                            .collect(Collectors.toList());
                    channelSites.addAll(sites);
                }
                if (CollectionUtils.isNotEmpty(channelSites)) {
                    boolQueryBuilder.must(QueryBuilders.termsQuery("prohibitionSites", channelSites));
                }
            } else {
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                for (String site : prohibitionSites) {
                    boolQuery.should(QueryBuilders.wildcardQuery("prohibitionSites", "*" + site));
                }
                boolQuery.minimumShouldMatch(1);
                boolQueryBuilder.must(boolQuery);
            }
        }

        // 产品标签
        if (CollectionUtils.isNotEmpty(request.getTagCodes())) {
            boolQueryBuilder.must(new TermsQueryBuilder("tagCodes", request.getTagCodes()));
        }

        // 特殊标签
        if (CollectionUtils.isNotEmpty(request.getSpecialGoodsCode())) {
            boolQueryBuilder.must(new TermsQueryBuilder("specialGoodsCode", request.getSpecialGoodsCode()));
        }
        if(CollectionUtils.isNotEmpty(request.getExcludeSpecialTag())) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("specialGoodsCode", request.getExcludeSpecialTag()));
        }

        // 同步时间
        if (StringUtils.isNotBlank(request.getFromSyncDate()) && StringUtils.isNotBlank(request.getToSyncDate())) {
            boolQueryBuilder.must(new RangeQueryBuilder("syncDate")
                    .from(request.getFromSyncDate())
                    .to(request.getToSyncDate()));
        } else {
            if (StringUtils.isNotBlank(request.getFromSyncDate())) {
                boolQueryBuilder.must(new RangeQueryBuilder("syncDate")
                        .gte(request.getFromSyncDate()));
            }
            if (StringUtils.isNotBlank(request.getToSyncDate())) {
                boolQueryBuilder.must(new RangeQueryBuilder("syncDate")
                        .lte(request.getToSyncDate()));
            }
        }
        if (StringUtils.isNotBlank(request.getLtToSyncDate())) {
            boolQueryBuilder.must(new RangeQueryBuilder("syncDate")
                    .lt(request.getLtToSyncDate()));
        }

        // 同步时间
        if (StringUtils.isNotBlank(request.getFromSyncProductDate()) && StringUtils.isNotBlank(request.getToSyncProductDate())) {
            boolQueryBuilder.must(new RangeQueryBuilder("updateSyncProductInfoDate")
                    .from(request.getFromSyncProductDate())
                    .to(request.getToSyncProductDate()));
        }

        // 更新时间区间
        if (StringUtils.isNotBlank(request.getFromUpdateDate()) && StringUtils.isNotBlank(request.getToUpdateDate())) {
            boolQueryBuilder.must(new RangeQueryBuilder("updateDate")
                    .from(request.getFromUpdateDate())
                    .to(request.getToUpdateDate()));
        }


        // 创建时间
        if (StringUtils.isNotBlank(request.getFromCreateDate()) && StringUtils.isNotBlank(request.getToCreateDate())) {
            boolQueryBuilder.must(new RangeQueryBuilder("createDate")
                    .from(request.getFromCreateDate())
                    .to(request.getToCreateDate()));
        }
        if (StringUtils.isNotBlank(request.getCreateDateLessThan())) {
            boolQueryBuilder.must(new RangeQueryBuilder("createDate")
                    .lte(request.getCreateDateLessThan()));
        }

        //是否新品
        Boolean newState = request.getNewState();
        if(newState != null){
            boolQueryBuilder.must(QueryBuilders.termQuery("newState", newState));
        }
        //是否促销
        Boolean isPromotion = request.getIsPromotion();
        if (isPromotion != null) {
            List<Integer> status = BooleanUtils.isTrue(isPromotion) ? Collections.singletonList(1) : Arrays.asList(0, 2);
            boolQueryBuilder.must(QueryBuilders.termsQuery("promotion", status));
        }

        // 数据来源
        if (Objects.nonNull(request.getSkuDataSource())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("skuDataSource", request.getSkuDataSource()));
        }

        // 组合状态
        if (Objects.nonNull(request.getComposeStatus())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("composeStatus", request.getComposeStatus()));
        }

        // 链接标签
        if (Objects.nonNull(request.getLinkTag())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("linkTag", request.getLinkTag()));
        }

        // 排除链接标签
        if (CollectionUtils.isNotEmpty(request.getExcludeLinkTag())) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("linkTag", request.getExcludeLinkTag()));
        }

        // 是否线上物流禁运
        if (Objects.nonNull(request.getLogisticsEmbargo())) {
            if (request.getLogisticsEmbargo()) {
                boolQueryBuilder.must(QueryBuilders.termQuery("linkTag", 1));
            } else {
                boolQueryBuilder.mustNot(QueryBuilders.termQuery("linkTag", 1));
            }
        }
        // 库存同步时间小于
        if (StringUtils.isNotBlank(request.getStockSyncDateLessThan())) {
            boolQueryBuilder.must(new RangeQueryBuilder("stockSyncDate")
                    .lte(request.getStockSyncDateLessThan()));
        }
        // 评分
        if (request.getExistRatingField() != null) {
            if (BooleanUtils.isTrue(request.getExistRatingField())) {
                boolQueryBuilder.must(QueryBuilders.existsQuery("rating"));
            }else {
                boolQueryBuilder.mustNot(QueryBuilders.existsQuery("rating"));
            }
        }
        // 评分同步时间小于
        if (StringUtils.isNotBlank(request.getRatingSyncDateLessThan())) {
            boolQueryBuilder.must(new RangeQueryBuilder("ratingSyncDate")
                    .lte(request.getRatingSyncDateLessThan()));
        }

        // 重量差异大于
        if (request.getGteWeightDifference() != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("weightDifference").gte(request.getGteWeightDifference()));
        }


        // 销量区间

        if (BooleanUtils.isTrue(request.getIsMarketing())) {
            if (Objects.nonNull(request.getOrderLast7dCountFrom())) {
                if (request.getOrderLast7dCountFrom() == 0) {
                    BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                    BoolQueryBuilder bool1 = QueryBuilders.boolQuery();
                    bool1.must(QueryBuilders.rangeQuery("orderLast7dCount").from(request.getOrderLast7dCountFrom()));
                    shouldQuery.should(bool1);
                    BoolQueryBuilder bool2 = QueryBuilders.boolQuery();
                    bool2.mustNot(QueryBuilders.existsQuery("orderLast7dCount"));
                    shouldQuery.should(bool2);
                    boolQueryBuilder.must(shouldQuery);
                } else {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast7dCount").from(request.getOrderLast7dCountFrom()));
                }
            }
            if (Objects.nonNull(request.getOrderLast7dCountTo())) {
                if (request.getOrderLast7dCountFrom() != null && request.getOrderLast7dCountFrom() == 0) {
                    BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                    BoolQueryBuilder bool1 = QueryBuilders.boolQuery();
                    bool1.must(QueryBuilders.rangeQuery("orderLast7dCount").to(request.getOrderLast7dCountFrom(), false));
                    shouldQuery.should(bool1);
                    BoolQueryBuilder bool2 = QueryBuilders.boolQuery();
                    bool2.mustNot(QueryBuilders.existsQuery("orderLast7dCount"));
                    shouldQuery.should(bool2);
                    boolQueryBuilder.must(shouldQuery);
                } else {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast7dCount").to(request.getOrderLast7dCountTo(), false));
                }
            }
            if (Objects.nonNull(request.getOrderLast14dCountFrom())) {
                if (request.getOrderLast14dCountFrom() == 0) {
                    BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                    BoolQueryBuilder bool1 = QueryBuilders.boolQuery();
                    bool1.must(QueryBuilders.rangeQuery("orderLast14dCount").from(request.getOrderLast14dCountFrom()));
                    shouldQuery.should(bool1);
                    BoolQueryBuilder bool2 = QueryBuilders.boolQuery();
                    bool2.mustNot(QueryBuilders.existsQuery("orderLast14dCount"));
                    shouldQuery.should(bool2);
                    boolQueryBuilder.must(shouldQuery);
                } else {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast14dCount").from(request.getOrderLast14dCountFrom()));
                }
            }
            if (Objects.nonNull(request.getOrderLast14dCountTo())) {
                if (request.getOrderLast14dCountFrom() != null && request.getOrderLast14dCountFrom() == 0) {
                    BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                    BoolQueryBuilder bool1 = QueryBuilders.boolQuery();
                    bool1.must(QueryBuilders.rangeQuery("orderLast14dCount").to(request.getOrderLast14dCountTo(), false));
                    shouldQuery.should(bool1);
                    BoolQueryBuilder bool2 = QueryBuilders.boolQuery();
                    bool2.mustNot(QueryBuilders.existsQuery("orderLast14dCount"));
                    shouldQuery.should(bool2);
                    boolQueryBuilder.must(shouldQuery);
                } else {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast14dCount").to(request.getOrderLast14dCountTo(), false));
                }
            }
            if (Objects.nonNull(request.getOrderLast30dCountFrom())) {
                if (request.getOrderLast30dCountFrom() == 0) {
                    BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                    BoolQueryBuilder bool1 = QueryBuilders.boolQuery();
                    bool1.must(QueryBuilders.rangeQuery("orderLast30dCount").from(request.getOrderLast30dCountFrom()));
                    shouldQuery.should(bool1);
                    BoolQueryBuilder bool2 = QueryBuilders.boolQuery();
                    bool2.mustNot(QueryBuilders.existsQuery("orderLast30dCount"));
                    shouldQuery.should(bool2);
                    boolQueryBuilder.must(shouldQuery);
                } else {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast30dCount").from(request.getOrderLast30dCountFrom()));
                }
            }
            if (Objects.nonNull(request.getOrderLast30dCountTo())) {
                if (request.getOrderLast30dCountFrom() != null && request.getOrderLast30dCountFrom() == 0) {
                    BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                    BoolQueryBuilder bool1 = QueryBuilders.boolQuery();
                    bool1.must(QueryBuilders.rangeQuery("orderLast30dCount").to(request.getOrderLast30dCountTo(), false));
                    shouldQuery.should(bool1);
                    BoolQueryBuilder bool2 = QueryBuilders.boolQuery();
                    bool2.mustNot(QueryBuilders.existsQuery("orderLast30dCount"));
                    shouldQuery.should(bool2);
                    boolQueryBuilder.must(shouldQuery);
                } else {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast30dCount").to(request.getOrderLast30dCountTo(), false));
                }
            }
            if (Objects.nonNull(request.getOrderLast90dCountFrom())) {
                if (request.getOrderLast90dCountFrom() == 0) {
                    BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                    BoolQueryBuilder bool1 = QueryBuilders.boolQuery();
                    bool1.must(QueryBuilders.rangeQuery("orderLast90dCount").from(request.getOrderLast90dCountFrom()));
                    shouldQuery.should(bool1);
                    BoolQueryBuilder bool2 = QueryBuilders.boolQuery();
                    shouldQuery.should(bool2);
                    boolQueryBuilder.must(shouldQuery);
                } else {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast90dCount").from(request.getOrderLast90dCountFrom()));
                }
            }
            if (Objects.nonNull(request.getOrderLast90dCountTo())) {
                if (request.getOrderLast90dCountFrom() != null && request.getOrderLast90dCountFrom() == 0) {
                    BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                    BoolQueryBuilder bool1 = QueryBuilders.boolQuery();
                    bool1.must(QueryBuilders.rangeQuery("orderLast90dCount").to(request.getOrderLast90dCountTo(), false));
                    shouldQuery.should(bool1);
                    BoolQueryBuilder bool2 = QueryBuilders.boolQuery();
                    bool2.mustNot(QueryBuilders.existsQuery("orderLast90dCount"));
                    shouldQuery.should(bool2);
                    boolQueryBuilder.must(shouldQuery);
                } else {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast90dCount").to(request.getOrderLast90dCountTo(), false));
                }
            }
            if (Objects.nonNull(request.getOrderNumTotalFrom())) {
                if (request.getOrderNumTotalFrom() == 0) {
                    BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                    BoolQueryBuilder bool1 = QueryBuilders.boolQuery();
                    bool1.must(QueryBuilders.rangeQuery("orderNumTotal").from(request.getOrderNumTotalFrom()));
                    shouldQuery.should(bool1);
                    BoolQueryBuilder bool2 = QueryBuilders.boolQuery();
                    shouldQuery.should(bool2);
                    boolQueryBuilder.must(shouldQuery);
                } else {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderNumTotal").from(request.getOrderNumTotalFrom()));
                }
            }
            if (Objects.nonNull(request.getOrderNumTotalTo())) {
                if (request.getOrderNumTotalFrom() != null && request.getOrderNumTotalFrom() == 0) {
                    BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                    BoolQueryBuilder bool1 = QueryBuilders.boolQuery();
                    bool1.must(QueryBuilders.rangeQuery("orderNumTotal").to(request.getOrderNumTotalTo(), false));
                    shouldQuery.should(bool1);
                    BoolQueryBuilder bool2 = QueryBuilders.boolQuery();
                    bool2.mustNot(QueryBuilders.existsQuery("orderNumTotal"));
                    shouldQuery.should(bool2);
                    boolQueryBuilder.must(shouldQuery);
                } else {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderNumTotal").to(request.getOrderNumTotalTo(), false));
                }
            }
        } else {
            if (!ObjectUtils.isEmpty(request.getOrderLast7dCountTo())) {
                if (request.getOrderLast7dCountTo() == 0) {
                    BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                    BoolQueryBuilder bool1 = QueryBuilders.boolQuery();
                    bool1.must(QueryBuilders.termQuery("orderLast7dCount", 0));
                    shouldQuery.should(bool1);
                    BoolQueryBuilder bool2 = QueryBuilders.boolQuery();
                    bool2.mustNot(QueryBuilders.existsQuery("orderLast7dCount"));
                    shouldQuery.should(bool2);
                    boolQueryBuilder.must(shouldQuery);
                } else {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast7dCount").to(request.getOrderLast7dCountTo()));
                    if (!ObjectUtils.isEmpty(request.getOrderLast7dCountFrom())) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast7dCount").from(request.getOrderLast7dCountFrom()));
                    }
                }
            } else {
                if (!ObjectUtils.isEmpty(request.getOrderLast7dCountFrom())) {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast7dCount").from(request.getOrderLast7dCountFrom()));
                }
            }

            if (!ObjectUtils.isEmpty(request.getOrderLast14dCountTo())) {
                if (request.getOrderLast14dCountTo() == 0) {
                    BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                    BoolQueryBuilder bool1 = QueryBuilders.boolQuery();
                    bool1.must(QueryBuilders.termQuery("orderLast14dCount", 0));
                    shouldQuery.should(bool1);
                    BoolQueryBuilder bool2 = QueryBuilders.boolQuery();
                    bool2.mustNot(QueryBuilders.existsQuery("orderLast14dCount"));
                    shouldQuery.should(bool2);
                    boolQueryBuilder.must(shouldQuery);
                } else {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast14dCount").to(request.getOrderLast14dCountTo()));
                    if (!ObjectUtils.isEmpty(request.getOrderLast14dCountFrom())) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast14dCount").from(request.getOrderLast14dCountFrom()));
                    }
                }
            } else {
                if (!ObjectUtils.isEmpty(request.getOrderLast14dCountFrom())) {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast14dCount").from(request.getOrderLast14dCountFrom()));
                }
            }

            if (!ObjectUtils.isEmpty(request.getOrderLast30dCountTo())) {
                if (request.getOrderLast30dCountTo() == 0) {
                    BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                    BoolQueryBuilder bool1 = QueryBuilders.boolQuery();
                    bool1.must(QueryBuilders.termQuery("orderLast30dCount", 0));
                    shouldQuery.should(bool1);
                    BoolQueryBuilder bool2 = QueryBuilders.boolQuery();
                    bool2.mustNot(QueryBuilders.existsQuery("orderLast30dCount"));
                    shouldQuery.should(bool2);
                    boolQueryBuilder.must(shouldQuery);
                } else {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast30dCount").to(request.getOrderLast30dCountTo()));
                    if (!ObjectUtils.isEmpty(request.getOrderLast30dCountFrom())) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast30dCount").from(request.getOrderLast30dCountFrom()));
                    }
                }
            } else {
                if (!ObjectUtils.isEmpty(request.getOrderLast30dCountFrom())) {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast30dCount").from(request.getOrderLast30dCountFrom()));
                }
            }

            if (!ObjectUtils.isEmpty(request.getOrderLast90dCountTo())) {
                if (request.getOrderLast90dCountTo() == 0) {
                    BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                    BoolQueryBuilder bool1 = QueryBuilders.boolQuery();
                    bool1.must(QueryBuilders.termQuery("orderLast90dCount", 0));
                    shouldQuery.should(bool1);
                    BoolQueryBuilder bool2 = QueryBuilders.boolQuery();
                    bool2.mustNot(QueryBuilders.existsQuery("orderLast90dCount"));
                    shouldQuery.should(bool2);
                    boolQueryBuilder.must(shouldQuery);
                } else {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast90dCount").to(request.getOrderLast90dCountTo()));
                    if (!ObjectUtils.isEmpty(request.getOrderLast90dCountFrom())) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast90dCount").from(request.getOrderLast90dCountFrom()));
                    }
                }
            } else {
                if (!ObjectUtils.isEmpty(request.getOrderLast90dCountFrom())) {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderLast90dCount").from(request.getOrderLast90dCountFrom()));
                }
            }

            if (!ObjectUtils.isEmpty(request.getOrderNumTotalTo())) {
                if (request.getOrderNumTotalTo() == 0) {
                    BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                    BoolQueryBuilder bool1 = QueryBuilders.boolQuery();
                    bool1.must(QueryBuilders.termQuery("orderNumTotal", 0));
                    shouldQuery.should(bool1);
                    BoolQueryBuilder bool2 = QueryBuilders.boolQuery();
                    bool2.mustNot(QueryBuilders.existsQuery("orderNumTotal"));
                    shouldQuery.should(bool2);
                    boolQueryBuilder.must(shouldQuery);
                } else {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderNumTotal").to(request.getOrderNumTotalTo()));
                    if (!ObjectUtils.isEmpty(request.getOrderNumTotalFrom())) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery("orderNumTotal").from(request.getOrderNumTotalFrom()));
                    }
                }
            } else {
                if (!ObjectUtils.isEmpty(request.getOrderNumTotalFrom())) {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery("orderNumTotal").from(request.getOrderNumTotalFrom()));
                }
            }
        }

        if (CollectionUtils.isNotEmpty(request.getOzonSkuList())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("ozonSku", request.getOzonSkuList()));
        }

        if (BooleanUtils.isTrue(request.getRegisteredForEvent())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("registeredForEvent", true));
        }
        if (BooleanUtils.isFalse(request.getRegisteredForEvent())) {
            BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
            shouldQuery.should(QueryBuilders.termQuery("registeredForEvent", false));
            shouldQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("registeredForEvent")));

            boolQueryBuilder.must(shouldQuery);
        }
        if (StringUtils.isNotBlank(request.getLtRegisteredForEventDate())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("registeredForEventDate").lt(request.getLtRegisteredForEventDate()));
        }
        // 获取在线状态是true的或者
        if (Objects.nonNull(request.getOnlineOrIsNot()) && BooleanUtils.isTrue(request.getOnlineOrIsNot())) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("isOnline", false));
        }
        if (Objects.nonNull(request.getIsOnline())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("isOnline", request.getIsOnline()));
        }

        Boolean minPriceNumberIsNull = request.getMinPriceNumberIsNull();
        if(minPriceNumberIsNull != null && minPriceNumberIsNull){
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("minPriceNumber"));
        }
        if(StringUtils.isNotBlank(request.getLikeErrorTexts())) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("errorTexts", "*" + request.getLikeErrorTexts() + "*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

    }

    /**
     * 数据权限校验
     * 1. 销售仅能查看自己负责的店铺数据，组长和主管可以查看自己和下级店铺数据；平台主管和超级管理员可以查看全部
     * 2. 销售/销售组长/销售主管 多个入参时已最小的为准
     */
    public static void checkListingSearchPermission(EsOzonItemRequest request, PermissionsHelper permissionHelper) {
        List<String> accountList = new ArrayList<>();
        List<String> accounts = request.getAccountNumbers();
        if (CollectionUtils.isNotEmpty(accounts)) {
            accountList.addAll(accounts);
        }
        List<String> currentUserPermission = permissionHelper.getCurrentUserPermission(accountList, request.getSaleAccountManager(), request.getSaleAccountLeader(), request.getSaleAccount(), SaleChannel.CHANNEL_OZON);
        request.setAccountNumbers(currentUserPermission);
    }

}
