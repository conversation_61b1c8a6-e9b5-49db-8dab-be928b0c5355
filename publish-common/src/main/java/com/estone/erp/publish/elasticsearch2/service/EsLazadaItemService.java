package com.estone.erp.publish.elasticsearch2.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.elasticsearch2.model.EsLazadaItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsLazadaItemRequest;
import com.estone.erp.publish.feginService.modle.SkuPubilshListingFirstJoinTimeVo;
import org.springframework.data.domain.Page;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.data.elasticsearch.core.query.UpdateResponse;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;

public interface EsLazadaItemService {

    EsLazadaItem findById(String id);

    List<EsLazadaItem> listById(List<String> ids);

    void save(EsLazadaItem item);

    Page<EsLazadaItem> page(EsLazadaItemRequest request, int offset, int limit);

    List<EsLazadaItem> listItemExample(EsLazadaItemRequest request);

    /**
     * 滚动查询执行任务
     *
     * @param request      查询条件
     * @param executorTask 任务
     * @return 查询结果数量
     */
    int scrollQueryExecutorTask(EsLazadaItemRequest request, Consumer<List<EsLazadaItem>> executorTask);

    void deleteByIds(List<String> delIds);

    List<EsLazadaItem> listNeedRecallPrice(EsLazadaItemRequest request);

    AggregatedPage<EsLazadaItem> accountListingStatistics(EsLazadaItemRequest request);

    List<String> distinctSpuPageList(EsLazadaItemRequest request, int pageIndex, int limit);

    List<String> getSkuList(EsLazadaItemRequest request);

    AggregatedPage<EsLazadaItem> selectGroupSellInfo(List<String> skus, String specialFromTime);

    /**
     * 客服系统接口
     * @param itemId
     * @return
     */
    EsLazadaItem getProductToCustomer(String itemId);

    /**
     * 客服系统接口
     * @param esLazadaItemRequest
     * @return
     */
    ApiResult<?> productListToCustomer(EsLazadaItemRequest esLazadaItemRequest);


    /**
     * 按条件统计listing数量
     * @param request query
     * @return countNumber
     */
    Long countAccountListingByQuery(EsLazadaItemRequest request);

    /**
     * 根据文档Id更新
     * @param UpdateRequest UpdateRequest
     */
    UpdateResponse updateRequestByDocId(UpdateQuery UpdateRequest);

    /**
     * 更新南宁库存
     * @param esLazadaItem
     */
    void updateNnStockRequest(EsLazadaItem esLazadaItem);
    /**
     * 根据账号和商品Id查询订单总数
     *
     * @param accountNumber
     * @param itemIds
     * @return
     */
    Map<Long, Double> getItemOrderNumTotal(String accountNumber, Set<Long> itemIds);

    /**
     * 获取lazada商品第一次上架时间
     * @param articleNumberList
     * @return
     */

    List<SkuPubilshListingFirstJoinTimeVo> getLazadaFirstJoinTime(List<String> articleNumberList);

    /**
     * 修改传递的字段 不传不改 传null会修改
     *
     * @param json
     * @param id
     */
    void updateRequest(String json, String id);
}
