package com.estone.erp.publish.elasticsearch2.service.impl;

import com.estone.erp.common.elasticsearch.util.ElasticSearchHelper;
import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.common.model.api.ApiR<PERSON>ult;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch2.dao.EsLazadaItemRepository;
import com.estone.erp.publish.elasticsearch2.model.EsLazadaItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsLazadaItemRequest;
import com.estone.erp.publish.elasticsearch2.service.EsLazadaItemService;
import com.estone.erp.publish.feginService.modle.SkuPubilshListingFirstJoinTimeVo;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.product.util.ToCustomerSkuUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.Cardinality;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.elasticsearch.search.collapse.CollapseBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHitSupport;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.SearchScrollHits;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.data.elasticsearch.core.query.UpdateResponse;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-08-08 14:34
 */
@Slf4j
@Service
public class EsLazadaItemServiceImpl implements EsLazadaItemService {
    private static final String indexName = "lazada_item";

    private IndexCoordinates lazadaItemIndexCoordinates = IndexCoordinates.of("lazada_item");

    @Resource
    private EsLazadaItemRepository lazadaItemRepository;

    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate2;
    @Resource
    private RestHighLevelClient restHighLevelClient2;

    @Override
    public EsLazadaItem findById(String id) {
        return lazadaItemRepository.findById(id).orElseGet(() -> null);
    }

    @Override
    public List<EsLazadaItem> listById(List<String> ids) {
        List<EsLazadaItem> items = new ArrayList<>();
        Iterable<EsLazadaItem> iterable = lazadaItemRepository.findAllById(ids);
        for (EsLazadaItem esLazadaItem : iterable) {
            items.add(esLazadaItem);
        }
        return items;
    }

    @Override
    public void save(EsLazadaItem item) {
        Assert.notNull(item, "item must not be null");
        elasticsearchRestTemplate2.save(item);
    }

    @Override
    public Page<EsLazadaItem> page(EsLazadaItemRequest request, int index, int limit) {
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        setQuery(builder, request);

        SortOrder sortOrder = SortOrder.DESC.toString().equals(request.getOrder()) ? SortOrder.DESC : SortOrder.ASC;
        NativeSearchQuery query = queryBuilder.withQuery(builder)
                .withPageable(PageRequest.of(index, limit))
                .withSort(SortBuilders.fieldSort(request.getSort()).order(sortOrder))
                .withFields(request.getFields())
                .build();
        query.setTrackTotalHits(true);
        return lazadaItemRepository.search(query);
    }

    @Override
    public List<EsLazadaItem> listItemExample(EsLazadaItemRequest request) {
        if (request == null) {
            return Collections.emptyList();
        }
        List<EsLazadaItem> resultList = new ArrayList<>();
        // 查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        setQuery(boolQueryBuilder, request);
        String[] queryFields = request.getFields();
        if(ArrayUtils.isEmpty(queryFields)){
            queryBuilder.withQuery(boolQueryBuilder);
        }else{
            queryBuilder.withQuery(boolQueryBuilder)
                    .withFields(queryFields);
        }
        //创建查询条件构造器
        NativeSearchQuery searchQuery = queryBuilder.withPageable(PageRequest.of(0, 500)).build();
        /*int i = 0;
        ScrolledPage<EsLazadaItem> scroll = (ScrolledPage<EsLazadaItem>) c2ElasticsearchTemplate
                .startScroll(1000*60*2, searchQuery, EsLazadaItem.class);
        while (scroll.hasContent()) {
            i++;
            resultList.addAll(scroll.getContent());
            scroll = (ScrolledPage<EsLazadaItem>) c2ElasticsearchTemplate.continueScroll(scroll.getScrollId(), 1000*60*2,
                    EsLazadaItem.class);
        }
        // 最后释放查询
        c2ElasticsearchTemplate.clearScroll(scroll.getScrollId());*/

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        resultList = ElasticSearchHelper.
                scrollQuery(elasticsearchRestTemplate2, 10 * 60 * 1000,
                        searchQuery, EsLazadaItem.class,
                        lazadaItemIndexCoordinates);
        stopWatch.stop();
        long totalTimeMillis = stopWatch.getTotalTimeMillis();
        if(totalTimeMillis > 3000L){
            log.warn("查询ES->EsLazadaItem 条数{}耗时{}ms", resultList.size(), totalTimeMillis);
        }
        return resultList;
    }

    /**
     * 滚动查询执行任务
     *
     * @param request      查询条件
     * @param executorTask 任务
     * @return 查询结果数量
     */
    @Override
    public int scrollQueryExecutorTask(EsLazadaItemRequest request, Consumer<List<EsLazadaItem>> executorTask) {
        // 查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        setQuery(boolQueryBuilder, request);

        SortOrder sortOrder = SortOrder.DESC.toString().equals(request.getOrder()) ? SortOrder.DESC : SortOrder.ASC;
        NativeSearchQuery searchQuery = queryBuilder
                .withFields(request.getFields())
                .withQuery(boolQueryBuilder)
                .withPageable(PageRequest.of(0, 1000))
                .withSort(SortBuilders.fieldSort(request.getSort()).order(sortOrder))
                .build();
        String scrollId = null;
        List<String> scrollIdList = new ArrayList<>();
        long scrollTimeInMillis = 10 * 60 * 1000;
        int totalCount = 0;
        while (true) {
            SearchScrollHits<EsLazadaItem> searchScrollHits = null;
            if (scrollId == null) {
                searchScrollHits = elasticsearchRestTemplate2.searchScrollStart(scrollTimeInMillis, searchQuery, EsLazadaItem.class, lazadaItemIndexCoordinates);
            } else {
                searchScrollHits = elasticsearchRestTemplate2.searchScrollContinue(scrollId, scrollTimeInMillis, EsLazadaItem.class, lazadaItemIndexCoordinates);
            }
            scrollId = searchScrollHits.getScrollId();
            scrollIdList.add(scrollId);
            if (!searchScrollHits.hasSearchHits()) {
                elasticsearchRestTemplate2.searchScrollClear(scrollIdList);
                break;
            }
            List<EsLazadaItem> dataList = (List) SearchHitSupport.unwrapSearchHits(searchScrollHits);
            if (CollectionUtils.isEmpty(dataList)) {
                break;
            }
            totalCount += dataList.size();
            // 执行任务
            executorTask.accept(dataList);
        }
        return totalCount;
    }


    @Override
    public void deleteByIds(List<String> delIds) {
        for (String id : delIds) {
            lazadaItemRepository.deleteById(id);
        }
    }


    @Override
    public List<EsLazadaItem> listNeedRecallPrice(EsLazadaItemRequest request) {
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        if (CollectionUtils.isNotEmpty(request.getAccounts())) {
            builder.must(QueryBuilders.termsQuery("accountNumber",request.getAccounts()));
        }
        if (StringUtils.isNotBlank(request.getStatus())) {
            builder.must(QueryBuilders.termQuery("status", request.getStatus()));
        }
        if (CollectionUtils.isNotEmpty(request.getSkuNotInList())) {
            builder.mustNot(QueryBuilders.termsQuery("sku", request.getSkuNotInList()));
        }
        if (request.getGrossLessThan() != null && request.getRateOfMarginLessThan() != null) {
            BoolQueryBuilder bool = new BoolQueryBuilder();
            bool.should(new RangeQueryBuilder("gross").lt(request.getGrossLessThan()));
            bool.should(new RangeQueryBuilder("rateOfMargin").lt(request.getRateOfMarginLessThan()));
            bool.minimumShouldMatch(1);
            builder.must(bool);
        }

        if (request.getGrossGreaterThan() != null && request.getRateOfMarginGreaterThan() != null) {
            BoolQueryBuilder bool = new BoolQueryBuilder();
            bool.should(new RangeQueryBuilder("gross").gt(request.getGrossGreaterThan()));
            bool.should(new RangeQueryBuilder("rateOfMargin").gt(request.getRateOfMarginGreaterThan()));
            bool.minimumShouldMatch(1);
            builder.must(bool);
        }
        String[] queryFields = request.getFields();
        if(ArrayUtils.isEmpty(queryFields)){
            queryBuilder.withQuery(builder);
        }else{
            queryBuilder.withQuery(builder)
                    .withFields(queryFields);
        }
        List<EsLazadaItem> resultList = new ArrayList<>();
        //创建查询条件构造器
        NativeSearchQuery searchQuery = queryBuilder.withPageable(PageRequest.of(0, 500)).build();
        /*ScrolledPage<EsLazadaItem> scroll = (ScrolledPage<EsLazadaItem>) c2ElasticsearchTemplate
                .startScroll(1000*60*2, searchQuery, EsLazadaItem.class);
        while (scroll.hasContent()) {
            resultList.addAll(scroll.getContent());
            scroll = (ScrolledPage<EsLazadaItem>) c2ElasticsearchTemplate.continueScroll(scroll.getScrollId(), 1000*60*2,
                    EsLazadaItem.class);
        }
        // 最后释放查询
        c2ElasticsearchTemplate.clearScroll(scroll.getScrollId());*/

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        resultList = ElasticSearchHelper.
                scrollQuery(elasticsearchRestTemplate2, 10 * 60 * 1000,
                        searchQuery, EsLazadaItem.class,
                        lazadaItemIndexCoordinates);
        stopWatch.stop();
        long totalTimeMillis = stopWatch.getTotalTimeMillis();
        if(totalTimeMillis > 3000L){
            log.warn("查询ES->EsLazadaItem 条数{}耗时{}ms", resultList.size(), totalTimeMillis);
        }
        return resultList;
    }

    @Override
    public AggregatedPage<EsLazadaItem> accountListingStatistics(EsLazadaItemRequest request) {
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        // 同步时间区间
        if (request.getFromSpecialFromTime() != null && request.getToSpecialFromTime() != null) {
            builder.must(new RangeQueryBuilder("specialFromTime")
                    .from(request.getFromSpecialFromTime())
                    .to(request.getToSpecialFromTime()));
        }
        //按账号分组
        String[] fields = new String[]{"site","specialFromTime"};
        TermsAggregationBuilder accountBuilder = AggregationBuilders.terms("group_by_account").field("accountNumber").size((1<<31)-1)
                .subAggregation(AggregationBuilders.topHits("top").fetchSource(fields, Strings.EMPTY_ARRAY).size(1));

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(builder)
                .addAggregation(accountBuilder)
                .withPageable(PageRequest.of(0, 1))
                .build();
        query.setTrackTotalHits(true);
        return  (AggregatedPage) lazadaItemRepository.search(query);
    }

    @Override
    public List<String> distinctSpuPageList(EsLazadaItemRequest request, int pageIndex, int limit) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        setQuery(boolQueryBuilder, request);
        CollapseBuilder cb = new CollapseBuilder("spu");
        int offset = pageIndex * limit;
        // 超50W就不返回了
        int maxResultWindows = 500000;
        if (offset > maxResultWindows) {
            return Collections.emptyList();
        }
       /* SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .collapse(cb)
                .from(offset)
                .size(limit)
                .sort(SortBuilders.fieldSort("createDate").order(SortOrder.DESC))
                .fetchSource("spu", null);
        SearchResponse response = c2TransportClient.prepareSearch("lazada_item")
                .setSource(sourceBuilder)
                .get();

        List<String> list = new ArrayList<>();
        SearchHits shList = response.getHits();
        for (SearchHit searchHit : shList) {
            JSONObject resultJson = JSON.parseObject(searchHit.getSourceAsString());
            String spu = resultJson.getString("spu");
            if (StringUtils.isNotBlank(spu)) {
                list.add(spu);
            }
        }*/

        List<String> list = new ArrayList<>();
        NativeSearchQueryBuilder query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withFields("spu")
                .withCollapseField("spu")
                .withSort(SortBuilders.fieldSort("createDate").order(SortOrder.DESC))
                .withPageable(PageRequest.of(offset,limit));

        NativeSearchQuery nativeSearchQuery = query.build();
        nativeSearchQuery.setTrackTotalHits(true);
        org.springframework.data.elasticsearch.core.SearchHits<EsLazadaItem> searchHits = elasticsearchRestTemplate2.search(nativeSearchQuery, EsLazadaItem.class);
        for (org.springframework.data.elasticsearch.core.SearchHit<EsLazadaItem> searchHit : searchHits) {
            if (null != searchHit.getContent() && null != searchHit.getContent().getCategoryId()) {
                list.add(searchHit.getContent().getSpu());
            }
        }
        return list;
    }

    @Override
    public List<String> getSkuList(EsLazadaItemRequest request) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        setQuery(boolQueryBuilder, request);
        List<String> list = new ArrayList<>();
        String[] fields = {"sku"};
        TermsAggregationBuilder skuBuilder = AggregationBuilders.terms("sku_group").field("sku").size((1 << 31) - 1)
                .subAggregation(AggregationBuilders.topHits("top").fetchSource(fields, Strings.EMPTY_ARRAY).size(1));
        NativeSearchQueryBuilder query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withFields("sku")
                .addAggregation(skuBuilder)
                .withSort(SortBuilders.fieldSort("sku").order(SortOrder.DESC))
                .withPageable(PageRequest.of(0,1));

        NativeSearchQuery nativeSearchQuery = query.build();
        nativeSearchQuery.setTrackTotalHits(true);
        org.springframework.data.elasticsearch.core.SearchHits<EsLazadaItem> searchHits = elasticsearchRestTemplate2.search(nativeSearchQuery, EsLazadaItem.class);
        Aggregations aggregations = searchHits.getAggregations();
        if (aggregations == null) {
            return list;
        }
        ParsedStringTerms skuGroup = aggregations.get("sku_group");
        if (org.apache.commons.collections.CollectionUtils.isEmpty(skuGroup.getBuckets())) {
            return list;
        }
        skuGroup.getBuckets().forEach(bucket -> {
            String key = (String)bucket.getKey();
            list.add(key);
        });
      /*  for (org.springframework.data.elasticsearch.core.SearchHit<EsLazadaItem> searchHit : searchHits) {
            if (null != searchHit.getContent() && null != searchHit.getContent().getSku()) {
                list.add(searchHit.getContent().getSku());
            }
        }*/
        return list;
    }

    @Override
    public AggregatedPage<EsLazadaItem> selectGroupSellInfo(List<String> skus, String specialFromTime) {
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        // 同步时间区间
        if (StringUtils.isNotBlank(specialFromTime)) {
            builder.must(new RangeQueryBuilder("specialFromTime")
                    .from(null)
                    .to(specialFromTime));
        }
        if (CollectionUtils.isNotEmpty(skus)) {
            builder.must(QueryBuilders.termsQuery("sku", skus));
        }
        builder.must(new RangeQueryBuilder("sellableStock").gt(0));
        //按账号分组
        TermsAggregationBuilder accountBuilder = AggregationBuilders.terms("group_by_site").field("site").size((1<<31)-1)
                .subAggregation(AggregationBuilders.terms("group_by_article").field("sku").size((1<<31)-1));

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(builder)
                .addAggregation(accountBuilder)
                .withPageable(PageRequest.of(0, 1))
                .build();
        query.setTrackTotalHits(true);
        return  (AggregatedPage) lazadaItemRepository.search(query);
    }

    /**
     * 构建查询
     *
     * @param builder boolQuery
     * @param request request
     */
    private void setQuery(BoolQueryBuilder builder, EsLazadaItemRequest request) {
        // id
        if (CollectionUtils.isNotEmpty(request.getIdList())) {
            builder.must(new TermsQueryBuilder("id", request.getIdList()));
        }
        // 大于id
        if (StringUtils.isNotEmpty(request.getGreaterThanId())) {
            builder.must(new RangeQueryBuilder("id").gt(request.getGreaterThanId()));
        }

        if (CollectionUtils.isNotEmpty(request.getExcludeIds())) {
            builder.mustNot(new TermsQueryBuilder("id", request.getExcludeIds()));
        }

        // globalItemId
        if (CollectionUtils.isNotEmpty(request.getGlobalItemIds())) {
            builder.must(new TermsQueryBuilder("globalItemId", request.getGlobalItemIds()));
        }

        // itemId
        if (CollectionUtils.isNotEmpty(request.getItemIdList())) {
            builder.must(new TermsQueryBuilder("itemId", request.getItemIdList()));
        }
        if (request.getItemId() != null) {
            builder.must(new TermQueryBuilder("itemId", request.getItemId()));
        }
        // sku
        if (CollectionUtils.isNotEmpty(request.getSkuList())) {
            builder.must(new TermsQueryBuilder("sku", request.getSkuList()));
        }
        if (CollectionUtils.isNotEmpty(request.getExcludeSkuList())) {
            builder.mustNot(new TermsQueryBuilder("sku", request.getExcludeSkuList()));
        }
        // skuId
        if (CollectionUtils.isNotEmpty(request.getSkuIdList())) {
            builder.must(new TermsQueryBuilder("skuId", request.getSkuIdList()));
        }
        // sellerSku
        if (CollectionUtils.isNotEmpty(request.getSellerSkuList())) {
            builder.must(new TermsQueryBuilder("sellerSku", request.getSellerSkuList()));
        }

        // 店铺账号
        if (CollectionUtils.isNotEmpty(request.getAccounts())) {
            builder.must(new TermsQueryBuilder("accountNumber", request.getAccounts()));
        }
        if (StringUtils.isNotBlank(request.getAccountNumber())) {
            builder.must(new TermQueryBuilder("accountNumber", request.getAccountNumber()));
        }
        // 站点
        if (StringUtils.isNotBlank(request.getSite())) {
            builder.must(new TermQueryBuilder("site", request.getSite()));
        }

        // 店铺SKU
        if (StringUtils.isNotBlank(request.getShopSku())) {
            builder.must(QueryBuilders.termsQuery("shopSku", CommonUtils.splitList(request.getShopSku(),",")));
        }
        // 库存区间
        if (request.getStock() != null) {
            builder.must(QueryBuilders.termQuery("quantity", request.getStock()));
        }


        if (request.getSellableStock() != null) {
            builder.must(QueryBuilders.termQuery("sellableStock", request.getSellableStock()));
        }
        if (request.getLtSellableStock() != null) {
            builder.must(new RangeQueryBuilder("sellableStock")
                    .lt(request.getLtSellableStock()));
        }
        if (request.getGtSellableStock() != null) {
            builder.must(new RangeQueryBuilder("sellableStock")
                    .gt(request.getGtSellableStock()));
        }

        // 默认仓库库存区间查询
        if (request.getStockDefaultWarehouse() != null || request.getFromStockDefaultWarehouse() != null || request.getToStockDefaultWarehouse() != null) {
            BoolQueryBuilder nestedBool = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("multiWarehouseInventories.warehouseName", "Warehouse"));

            // 精确匹配
            if (request.getStockDefaultWarehouse() != null) {
                nestedBool.must(QueryBuilders.termQuery("multiWarehouseInventories.quantity", request.getStockDefaultWarehouse()));
            }
            // 区间查询
            if (request.getFromStockDefaultWarehouse() != null && request.getToStockDefaultWarehouse() != null) {
                nestedBool.must(new RangeQueryBuilder("multiWarehouseInventories.quantity")
                        .from(request.getFromStockDefaultWarehouse())
                        .to(request.getToStockDefaultWarehouse()));
            }
            // 大于等于
            else if (request.getFromStockDefaultWarehouse() != null) {
                nestedBool.must(new RangeQueryBuilder("multiWarehouseInventories.quantity")
                        .gte(request.getFromStockDefaultWarehouse()));
            }
            // 小于等于
            else if (request.getToStockDefaultWarehouse() != null) {
                nestedBool.must(new RangeQueryBuilder("multiWarehouseInventories.quantity")
                        .lte(request.getToStockDefaultWarehouse()));
            }

            builder.must(QueryBuilders.nestedQuery(
                    "multiWarehouseInventories",
                    nestedBool,
                    ScoreMode.None
            ));
        }

        // 3PF仓库库存区间查询
        if (request.getStock3PFWarehouse() != null || request.getFromStock3PFWarehouse() != null || request.getToStock3PFWarehouse() != null) {
            BoolQueryBuilder nestedBool = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("multiWarehouseInventories.warehouseName", "3PF"));

            // 精确匹配
            if (request.getStock3PFWarehouse() != null) {
                nestedBool.must(QueryBuilders.termQuery("multiWarehouseInventories.quantity", request.getStock3PFWarehouse()));
            }
            // 区间查询
            if (request.getFromStock3PFWarehouse() != null && request.getToStock3PFWarehouse() != null) {
                nestedBool.must(new RangeQueryBuilder("multiWarehouseInventories.quantity")
                        .from(request.getFromStock3PFWarehouse())
                        .to(request.getToStock3PFWarehouse()));
            }
            // 大于等于
            else if (request.getFromStock3PFWarehouse() != null) {
                nestedBool.must(new RangeQueryBuilder("multiWarehouseInventories.quantity")
                        .gte(request.getFromStock3PFWarehouse()));
            }
            // 小于等于
            else if (request.getToStock3PFWarehouse() != null) {
                nestedBool.must(new RangeQueryBuilder("multiWarehouseInventories.quantity")
                        .lte(request.getToStock3PFWarehouse()));
            }

            builder.must(QueryBuilders.nestedQuery(
                    "multiWarehouseInventories",
                    nestedBool,
                    ScoreMode.None
            ));
        }

        // 仓库查询（按code或name）
        if (StringUtils.isNotBlank(request.getWarehouseCode()) || StringUtils.isNotBlank(request.getWarehouseName())) {
            BoolQueryBuilder nestedBool = QueryBuilders.boolQuery();

            // 仓库code查询
            if (StringUtils.isNotBlank(request.getWarehouseCode())) {
                nestedBool.must(QueryBuilders.termQuery("multiWarehouseInventories.warehouseCode", request.getWarehouseCode()));
            }

            // 仓库名称查询
            if (StringUtils.isNotBlank(request.getWarehouseName())) {
                nestedBool.must(QueryBuilders.termQuery("multiWarehouseInventories.warehouseName", request.getWarehouseName()));
            }


            // 特殊处理 页面的库存区间
            if (request.getFromStock() != null && request.getToStock() != null) {
                nestedBool.must(new RangeQueryBuilder("multiWarehouseInventories.quantity")
                        .from(request.getFromStock())
                        .to(request.getToStock()));
            } else if (request.getFromStock() != null) {
                nestedBool.must(new RangeQueryBuilder("multiWarehouseInventories.quantity")
                        .gte(request.getFromStock()));
            } else if (request.getToStock() != null) {

                nestedBool.must(new RangeQueryBuilder("multiWarehouseInventories.quantity")
                        .lte(request.getToStock()));
            }

            builder.must(QueryBuilders.nestedQuery(
                    "multiWarehouseInventories",
                    nestedBool,
                    ScoreMode.None
            ));
        } else {
            if (request.getFromStock() != null && request.getToStock() != null) {
                builder.must(new RangeQueryBuilder("quantity")
                        .from(request.getFromStock())
                        .to(request.getToStock()));
            }
            if (request.getFromStock() != null && request.getToStock() == null) {
                builder.must(new RangeQueryBuilder("quantity")
                        .gte(request.getFromStock()));
            }
            if (request.getFromStock() == null && request.getToStock() != null) {
                builder.must(new RangeQueryBuilder("quantity")
                        .lte(request.getToStock()));
            }
        }
        // 毛利区间
        if (request.getGross() != null) {
            builder.must(QueryBuilders.termQuery("gross", request.getGross()));
        }
        if (request.getFromGross() != null && request.getToGross() != null) {
            builder.must(new RangeQueryBuilder("gross")
                    .from(request.getFromGross())
                    .to(request.getToGross()));
        }
        if (request.getFromGross() != null && request.getToGross() == null) {
            builder.must(new RangeQueryBuilder("gross")
                    .gte(request.getFromGross()));
        }
        if (request.getFromGross() == null && request.getToGross() != null) {
            builder.must(new RangeQueryBuilder("gross")
                    .lte(request.getToGross()));
        }

        // 毛利率区间
        if (request.getRateOfMargin() != null) {
            builder.must(QueryBuilders.termQuery("rateOfMargin", request.getRateOfMargin()));
        }
        if (request.getFromRateOfMargin() != null && request.getToRateOfMargin() != null) {
            builder.must(new RangeQueryBuilder("rateOfMargin")
                    .from(request.getFromRateOfMargin())
                    .to(request.getToRateOfMargin()));
        }
        if (request.getFromRateOfMargin() != null && request.getToRateOfMargin() == null) {
            builder.must(new RangeQueryBuilder("rateOfMargin")
                    .gte(request.getFromRateOfMargin()));
        }
        if (request.getFromRateOfMargin() == null && request.getToRateOfMargin() != null) {
            builder.must(new RangeQueryBuilder("rateOfMargin")
                    .lte(request.getToRateOfMargin()));
        }
        if (request.getRateOfMarginLessThan() != null) {
            BoolQueryBuilder bool = new BoolQueryBuilder();
            bool.should(new RangeQueryBuilder("rateOfMargin").lt(request.getRateOfMarginLessThan()));
            bool.minimumShouldMatch(1);
            builder.must(bool);
        }
        if (request.getRateOfMarginGreaterThan() != null) {
            BoolQueryBuilder bool = new BoolQueryBuilder();
            bool.should(new RangeQueryBuilder("rateOfMargin").gt(request.getRateOfMarginGreaterThan()));
            bool.minimumShouldMatch(1);
            builder.must(bool);
        }

        // 状态
        if (StringUtils.isNotBlank(request.getStatus())) {
            builder.must(QueryBuilders.termQuery("status", request.getStatus()));
        }

        // 站点
        if (StringUtils.isNotBlank(request.getSite())) {
            builder.must(QueryBuilders.termsQuery("site", CommonUtils.splitList(request.getSite(),",")));
        }

        // sellerSku
        if (StringUtils.isNotBlank(request.getSellerSku())) {
            builder.must(QueryBuilders.termsQuery("sellerSku", CommonUtils.splitList(request.getSellerSku(),",")));
        }
        // sellerSku not like
        if (StringUtils.isNotBlank(request.getSellerSkuNotLike())) {
            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.should(QueryBuilders.wildcardQuery("sellerSku", request.getSellerSkuNotLike()));
            builder.mustNot(boolQueryBuilder);
        }
        if (StringUtils.isNotBlank(request.getSellerSkuLike())) {
            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.should(QueryBuilders.wildcardQuery("sellerSku", request.getSellerSkuLike()));
            builder.must(boolQueryBuilder);
        }

        // spu
        if (StringUtils.isNotBlank(request.getSpu())) {
            builder.must(QueryBuilders.termsQuery("spu",  CommonUtils.splitList(request.getSpu(),",")));
        }

        // sku
        if (StringUtils.isNotBlank(request.getSku())) {
            builder.must(QueryBuilders.termsQuery("sku", CommonUtils.splitList(request.getSku(),",")));
        }

        // > skuId
        if (Objects.nonNull(request.getLastSkuId())) {
            builder.must(new RangeQueryBuilder("skuId")
                    .gt(request.getLastSkuId()));        }

        // 排除sku
        if (CollectionUtils.isNotEmpty(request.getSkuNotInList())) {
            builder.mustNot(QueryBuilders.termsQuery("sku", request.getSkuNotInList()));
        }

        // 单品状态
        if (CollectionUtils.isNotEmpty(request.getSkuStatus())) {
            builder.must(new TermsQueryBuilder("skuStatus", request.getSkuStatus()));
        }

        if (CollectionUtils.isNotEmpty(request.getSkuStatusNotInList())) {
            builder.mustNot(new TermsQueryBuilder("skuStatus", request.getSkuStatusNotInList()));
        }

        // 产品类目
        if (CollectionUtils.isNotEmpty(request.getCategoryId())) {
            builder.must(QueryBuilders.termsQuery("categoryId", request.getCategoryId()));
        }

        // 禁售平台
        List<String> forbidChannelList = request.getForbidChannel();
        if (CollectionUtils.isNotEmpty(forbidChannelList)) {
            builder.must(new TermsQueryBuilder("forbidChannel", forbidChannelList));
        }

        // 禁售类型
        List<String> infringementTypeNames = request.getInfringementTypeNames();
        if (CollectionUtils.isNotEmpty(infringementTypeNames)) {
            builder.must(QueryBuilders.termsQuery("infringementTypeNames", infringementTypeNames));
        }

        // 禁售原因
        List<String> infringementObjs = request.getInfringementObjs();
        if (CollectionUtils.isNotEmpty(infringementObjs)) {
            builder.must(QueryBuilders.termsQuery("infringementObjs", infringementObjs));
        }

        // 禁售站点
        List<String> prohibitionSites = request.getProhibitionSites();
        if (CollectionUtils.isNotEmpty(prohibitionSites)) {
            // 禁售平台不为空就走过滤，否则模糊查询
            if (CollectionUtils.isNotEmpty(forbidChannelList)) {
                List<String> channelSites = new ArrayList<>();
                for (String channel : forbidChannelList) {
                    List<String> sites = prohibitionSites.stream()
                            .filter(StringUtils::isNotBlank)
                            .map(site -> channel + "_" + site)
                            .collect(Collectors.toList());
                    channelSites.addAll(sites);
                }
                if (CollectionUtils.isNotEmpty(channelSites)) {
                    builder.must(QueryBuilders.termsQuery("prohibitionSites", channelSites));
                }
            } else {
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                for (String site : prohibitionSites) {
                    boolQuery.should(QueryBuilders.wildcardQuery("prohibitionSites", "*" + site));
                }
                boolQuery.minimumShouldMatch(1);
                builder.must(boolQuery);
            }
        }

        // 产品标签
        if (CollectionUtils.isNotEmpty(request.getTagCodes())) {
            builder.must(new TermsQueryBuilder("tagCodes", request.getTagCodes()));
        }

        // 特殊标签
        if (CollectionUtils.isNotEmpty(request.getSpecialGoodsCode())) {
            builder.must(new TermsQueryBuilder("specialGoodsCode", request.getSpecialGoodsCode()));
        }

        //标题
        String titleLike = request.getTitleLike();
        if(StringUtils.isNotBlank(titleLike)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("title", "* " + titleLike + " *"));
            boolQuery.should(QueryBuilders.wildcardQuery("title", "* " + titleLike));
            boolQuery.should(QueryBuilders.wildcardQuery("title", titleLike + " *"));
            boolQuery.should(QueryBuilders.termQuery("title", StringUtils.trim(titleLike)));
            boolQuery.minimumShouldMatch(1);
            builder.must(boolQuery);
        }

        // 同步时间
        if (request.getFromSyncDate() != null && request.getToSyncDate() != null) {
            builder.must(new RangeQueryBuilder("syncDate")
                    .from(request.getFromSyncDate())
                    .to(request.getToSyncDate()));
        }

        if (StringUtils.isNotBlank(request.getLtSyncDate())) {
            builder.must(new RangeQueryBuilder("syncDate")
                    .lte(request.getLtSyncDate()));
        }

        // 同步时间区间
        if (request.getFromSpecialFromTime() != null && request.getToSpecialFromTime() != null) {
            builder.must(new RangeQueryBuilder("specialFromTime")
                    .from(request.getFromSpecialFromTime())
                    .to(request.getToSpecialFromTime()));
        }


        // 创建时间
        if (StringUtils.isNotBlank(request.getFromCreateDate()) && StringUtils.isNotBlank(request.getToCreateDate())) {
            builder.must(new RangeQueryBuilder("createDate")
                    .from(request.getFromCreateDate())
                    .to(request.getToCreateDate()));
        }
        if (StringUtils.isNotBlank(request.getFromCreateDate()) && StringUtils.isBlank(request.getToCreateDate())) {
            builder.must(new RangeQueryBuilder("createDate")
                    .from(request.getFromCreateDate()));
        }
        if (StringUtils.isBlank(request.getFromCreateDate()) && StringUtils.isNotBlank(request.getToCreateDate())) {
            builder.must(new RangeQueryBuilder("createDate")
                    .to(request.getToCreateDate()));
        }


        // 毛利/毛利率更新时间区间
        if (request.getFromGrossUpdateDate() != null && request.getToGrossUpdateDate() != null) {
            builder.must(new RangeQueryBuilder("grossUpdateDate")
                    .from(request.getFromGrossUpdateDate())
                    .to(request.getToGrossUpdateDate()));
        }

        // 不再毛利/毛利率更新时间区间
        if (request.getMustNotFromSemiGrossUpdateDate() != null && request.getMustNotToSemiGrossUpdateDate() != null) {
            builder.mustNot(new RangeQueryBuilder("semiGrossUpdateDate")
                    .from(request.getMustNotFromSemiGrossUpdateDate())
                    .to(request.getMustNotToSemiGrossUpdateDate()));
        }

        // 销量
        addRangeQuery(builder, "order24HCount", request.getFromOrder24HCount(), request.getToOrder24HCount());
        addRangeQuery(builder, "orderLast7dCount", request.getFromOrderLast7dCount(), request.getToOrderLast7dCount());
        addRangeQuery(builder, "orderLast14dCount", request.getFromOrderLast14dCount(), request.getToOrderLast14dCount());
        addRangeQuery(builder, "orderLast30dCount", request.getFromOrderLast30dCount(), request.getToOrderLast30dCount());
        addRangeQuery(builder, "orderLast60dCount", request.getFromOrderLast60dCount(), request.getToOrderLast60dCount());
        addRangeQuery(builder, "orderNumTotal", request.getFromOrderNumTotal(), request.getToOrderNumTotal());

        if (Boolean.TRUE.equals(request.getZeroOrEmptyOrderNumTotal())) {
            // 无销量或者总销量为0
            BoolQueryBuilder shouldBoolQuery = QueryBuilders.boolQuery();
            shouldBoolQuery.should(QueryBuilders.boolQuery().must(QueryBuilders.rangeQuery("orderNumTotal").to(0)));
            shouldBoolQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("orderNumTotal")));
            builder.must(shouldBoolQuery);
        }

        //是否新品
        Boolean newState = request.getNewState();
        if(newState != null){
            builder.must(QueryBuilders.termQuery("newState", newState));
        }
        //是否促销
        Boolean isPromotion = request.getIsPromotion();
        if (isPromotion != null) {
            List<Integer> status = BooleanUtils.isTrue(isPromotion) ? Collections.singletonList(1) : Arrays.asList(0, 2);
            builder.must(QueryBuilders.termsQuery("promotion", status));
        }
        // 数据来源
        if (Objects.nonNull(request.getSkuDataSource())) {
            builder.must(QueryBuilders.termQuery("skuDataSource", request.getSkuDataSource()));
        }

        // 组合状态
        if (Objects.nonNull(request.getComposeStatus())) {
            builder.must(QueryBuilders.termQuery("composeStatus", request.getComposeStatus()));
        }
        // 是否半托管商品
        if (Objects.nonNull(request.getIsSemiItem())) {
            builder.filter(QueryBuilders.termQuery("semiStatus", request.getIsSemiItem()));
        }
        // 商品来源
        if (StringUtils.isNotBlank(request.getSource())) {
            builder.filter(QueryBuilders.termQuery("source", request.getSource()));
        }
        // 促销价
        if (request.getFromSpecialPrice() != null && request.getToSpecialPrice() != null) {
            builder.must(new RangeQueryBuilder("specialPrice")
                    .from(request.getFromSpecialPrice())
                    .to(request.getToSpecialPrice()));
        }
        if (request.getFromSpecialPrice() != null && request.getToSpecialPrice() == null) {
            builder.must(new RangeQueryBuilder("specialPrice")
                    .gte(request.getFromSpecialPrice()));
        }
        if (request.getFromSpecialPrice() == null && request.getToSpecialPrice() != null) {
            builder.must(new RangeQueryBuilder("specialPrice")
                    .lte(request.getToSpecialPrice()));
        }
        // 币种
        if (StringUtils.isNotBlank(request.getCurrency())) {
            builder.must(QueryBuilders.termQuery("currency", request.getCurrency()));
        }
        // 重量
        if (request.getFromPackageWeight() != null && request.getToPackageWeight() != null) {
            builder.must(new RangeQueryBuilder("packageWeight")
                    .from(request.getFromPackageWeight())
                    .to(request.getToPackageWeight()));
        }
        if (request.getFromPackageWeight() != null && request.getToPackageWeight() == null) {
            builder.must(new RangeQueryBuilder("packageWeight")
                    .gte(request.getFromPackageWeight()));
        }
        if (request.getFromPackageWeight() == null && request.getToPackageWeight() != null) {
            builder.must(new RangeQueryBuilder("packageWeight")
                    .lte(request.getToPackageWeight()));
        }

        // 划线价
        if (request.getFromPrice() != null && request.getToPrice() != null) {
            builder.must(new RangeQueryBuilder("price")
                    .from(request.getFromPrice())
                    .to(request.getToPrice()));
        }
        if (request.getFromPrice() != null && request.getToPrice() == null) {
            builder.must(new RangeQueryBuilder("price")
                    .gte(request.getFromPrice()));
        }
        if (request.getFromPrice() == null && request.getToPrice() != null) {
            builder.must(new RangeQueryBuilder("price")
                    .lte(request.getToPrice()));
        }

        // 建议不含邮价
        if (request.getFromNoPostagPrice() != null && request.getToNoPostagPrice() != null) {
            builder.must(new RangeQueryBuilder("noPostagePrice")
                    .from(request.getFromNoPostagPrice())
                    .to(request.getToNoPostagPrice()));
        }
        if (request.getFromNoPostagPrice() != null && request.getToNoPostagPrice() == null) {
            builder.must(new RangeQueryBuilder("noPostagePrice")
                    .gte(request.getFromNoPostagPrice()));
        }
        if (request.getFromNoPostagPrice() == null && request.getToNoPostagPrice() != null) {
            builder.must(new RangeQueryBuilder("noPostagePrice")
                    .lte(request.getToNoPostagPrice()));
        }

        // 半托管毛利
        if (request.getFromSemiGross() != null && request.getToSemiGross() != null) {
            builder.must(new RangeQueryBuilder("semiGross")
                    .from(request.getFromSemiGross())
                    .to(request.getToSemiGross()));
        }
        if (request.getFromSemiGross() != null && request.getToSemiGross() == null) {
            builder.must(new RangeQueryBuilder("semiGross")
                    .gte(request.getFromSemiGross()));
        }
        if (request.getFromSemiGross() == null && request.getToSemiGross() != null) {
            builder.must(new RangeQueryBuilder("semiGross")
                    .lte(request.getToSemiGross()));
        }

        // 半托管毛利率
        if (request.getFromSemiRateOfMargin() != null && request.getToSemiRateOfMargin() != null) {
            builder.must(new RangeQueryBuilder("semiRateOfMargin")
                    .from(request.getFromSemiRateOfMargin())
                    .to(request.getToSemiRateOfMargin()));
        }
        if (request.getFromSemiRateOfMargin() != null && request.getToSemiRateOfMargin() == null) {
            builder.must(new RangeQueryBuilder("semiRateOfMargin")
                    .gte(request.getFromSemiRateOfMargin()));
        }
        if (request.getFromSemiRateOfMargin() == null && request.getToSemiRateOfMargin() != null) {
            builder.must(new RangeQueryBuilder("semiRateOfMargin")
                    .lte(request.getToSemiRateOfMargin()));
        }

        // 半托管同步时间
        if (StringUtils.isNotBlank(request.getFromCanUpgradeSemiItemSyncDate()) && StringUtils.isNotBlank(request.getToCanUpgradeSemiItemSyncDate())) {
            builder.must(new RangeQueryBuilder("canUpgradeSemiItemSyncDate")
                    .from(request.getFromCanUpgradeSemiItemSyncDate())
                    .to(request.getToCanUpgradeSemiItemSyncDate()));
        }
        if (StringUtils.isNotBlank(request.getFromCanUpgradeSemiItemSyncDate()) && StringUtils.isBlank(request.getToCanUpgradeSemiItemSyncDate())) {
            builder.must(new RangeQueryBuilder("canUpgradeSemiItemSyncDate")
                    .gte(request.getFromCanUpgradeSemiItemSyncDate()));
        }
        if (StringUtils.isBlank(request.getFromCanUpgradeSemiItemSyncDate()) && StringUtils.isNotBlank(request.getToCanUpgradeSemiItemSyncDate())) {
            builder.must(new RangeQueryBuilder("canUpgradeSemiItemSyncDate")
                    .lte(request.getToCanUpgradeSemiItemSyncDate()));
        }

        // 半托管状态
        if (Objects.nonNull(request.getSemiStatus())) {
            builder.must(QueryBuilders.termQuery("semiStatus", request.getSemiStatus()));
        }
        // 可升级半托管状态
        if (StringUtils.isNotBlank(request.getSemiUpgradeStatus())) {
            builder.must(QueryBuilders.termQuery("semiUpgradeStatus", request.getSemiUpgradeStatus()));
        }

        // 刊登角色
        if (Objects.nonNull(request.getPublishRole())) {
            builder.must(QueryBuilders.termQuery("publishRole", request.getPublishRole()));
        }

        // nnStock  南宁库存
        if ( Objects.nonNull(request.getNnStockGte())) {
            builder.must(new RangeQueryBuilder("nnStock")
                    .gte(request.getNnStockGte()));
        }
        if ( Objects.nonNull(request.getNnStockLte())) {
            builder.must(new RangeQueryBuilder("nnStock")
                    .lte(request.getNnStockLte()));
        }
        Boolean existNnSkuStock = request.getIsExistNnStock();
        if (Objects.nonNull(existNnSkuStock) && BooleanUtils.isTrue(existNnSkuStock)) {
            builder.must(QueryBuilders.existsQuery("nnStock"));
        }else if (Objects.nonNull(existNnSkuStock) && BooleanUtils.isFalse(existNnSkuStock)){
            builder.mustNot(QueryBuilders.existsQuery("nnStock"));
        }
    }

    @Override
    public EsLazadaItem getProductToCustomer(String itemId){
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("itemId", itemId));

        queryBuilder.addAggregation(AggregationBuilders.sum("sellableStock").field("sellableStock"));
        queryBuilder.addAggregation(AggregationBuilders.sum("quantity").field("quantity"));
        NativeSearchQuery query = queryBuilder.withQuery(boolQueryBuilder)
                .withFields("spu","sellableStock","quantity")
                .build();
        query.setTrackTotalHits(true);
        SearchHits<EsLazadaItem> searchHits = elasticsearchRestTemplate2.search(query,
                EsLazadaItem.class,lazadaItemIndexCoordinates);
        if(Objects.nonNull(searchHits)){
            Aggregations aggregations = searchHits.getAggregations();
            ParsedSum sellableStock = aggregations.get("sellableStock");
            ParsedSum quantity = aggregations.get("quantity");
            Integer stock=sellableStock!=null?(int)sellableStock.getValue():null;
            Integer q=quantity!=null?(int)quantity.getValue():null;
            List<EsLazadaItem> list = (List<EsLazadaItem>) SearchHitSupport.unwrapSearchHits(searchHits);
            if(CollectionUtils.isNotEmpty(list)){
                EsLazadaItem esLazadaItem = list.get(0);
                esLazadaItem.setSellableStock(stock);
                esLazadaItem.setQuantity(q);
                return esLazadaItem;
            }
        }
        return null;
    }

    private void addRangeQuery(BoolQueryBuilder boolQueryBuilder, String fieldName, Integer from, Integer to) {
        if (from != null || to != null) {
            if (from != null && from == 0) {
                BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery(fieldName);
                rangeQuery.from(from);
                if (to != null) {
                    rangeQuery.to(to);
                }
                // 范围在 [from, to] 之内
                shouldQuery.should(rangeQuery);
                // 或者字段不存在
                shouldQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery(fieldName)));
                boolQueryBuilder.must(shouldQuery);
            } else {
                RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(fieldName);
                if (from != null) {
                    rangeQueryBuilder.from(from);
                }
                if (to != null) {
                    rangeQueryBuilder.to(to);
                }
                boolQueryBuilder.must(rangeQueryBuilder);
            }
        }
    }

    @Override
    public ApiResult<?> productListToCustomer(EsLazadaItemRequest esLazadaItemRequest) {
        List<EsLazadaItem> list = new ArrayList<>();
        long start = System.currentTimeMillis();
        try {
            if (esLazadaItemRequest != null) {
                NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

                String accountNumber = esLazadaItemRequest.getAccountNumber();
                if (org.apache.commons.lang.StringUtils.isNotBlank(accountNumber)) {
                    boolQueryBuilder.must(QueryBuilders.termQuery("accountNumber", accountNumber));
                }
                String sellerSku = esLazadaItemRequest.getSellerSku();
                if (org.apache.commons.lang.StringUtils.isNotBlank(sellerSku)) {
                    boolQueryBuilder.must(QueryBuilders.termQuery("sellerSku", sellerSku));
                }
                String shopSku = esLazadaItemRequest.getShopSku();
                if (org.apache.commons.lang.StringUtils.isNotBlank(shopSku)) {
                    boolQueryBuilder.must(QueryBuilders.termQuery("shopSku", shopSku));
                }
                String spu = esLazadaItemRequest.getSpu();
                if (org.apache.commons.lang.StringUtils.isNotEmpty(spu)) {
                    boolQueryBuilder.must(QueryBuilders.termQuery("spu", spu));
                }
                Long itemId = esLazadaItemRequest.getItemId();
                if (Objects.nonNull(itemId)) {
                    boolQueryBuilder.must(QueryBuilders.termQuery("itemId", itemId));
                }
                String title = esLazadaItemRequest.getTitle();
                if (org.apache.commons.lang.StringUtils.isNotEmpty(title)) {
                    boolQueryBuilder.must(QueryBuilders.wildcardQuery("title", "*" + title + "*"));
                }
                int page = 0;
                int limit = 50;
                if (esLazadaItemRequest.getOffset() != null) {
                    page = esLazadaItemRequest.getOffset() / esLazadaItemRequest.getLimit();
                    limit = esLazadaItemRequest.getLimit();
                }
                Pageable pageable = PageRequest.of(page, limit);
                SortBuilder sortBuilder = null;
                boolean saleCount = org.apache.commons.lang.StringUtils.equals(esLazadaItemRequest.getSort(), "orderLast60dCount");
                if (saleCount) {
                    sortBuilder = SortBuilders.fieldSort("orderLast60dCount").order(SortOrder.fromString("desc"));
                } else {
                    sortBuilder = SortBuilders.fieldSort("createDate").order(SortOrder.fromString("desc"));
                }
                // 创建查询对象
                NativeSearchQuery searchQuery = queryBuilder.withQuery(boolQueryBuilder)// 查询条件对象
                        .withPageable(pageable)// 从0页开始查，每页1000个结果
                        .withFields("accountNumber", "images", "shopSku", "sellerSku", "spu", "sku", "skuStatus", "specialPrice",
                                "price", "title", "orderLast60dCount", "quantity", "sellableStock", "createDate","site","itemId","url"
                        ).withSort(sortBuilder)
                        .build();
                searchQuery.setTrackTotalHits(true);
                PageInfo<EsLazadaItem> esLazadaItemPageInfo = ElasticSearchHelper.queryPage(elasticsearchRestTemplate2,searchQuery, EsLazadaItem.class);
                long totalPages = esLazadaItemPageInfo.getTotalPages();
                List<EsLazadaItem> contentList = esLazadaItemPageInfo.getContents();
                if (saleCount && org.apache.commons.collections.CollectionUtils.isNotEmpty(contentList)) {
                    contentList.get(0).setSaleTop(true);
                }
                boolean flag=  totalPages > page + 1;
                for (EsLazadaItem esLazadaItem : contentList) {
                    esLazadaItem.setMore(flag);
                    esLazadaItem.setActionUrl(esLazadaItem.getUrl());
                    String articleNumber = esLazadaItem.getSku();
                    if(org.apache.commons.lang.StringUtils.isNotBlank(articleNumber)){
                        esLazadaItem.setQuantity(ToCustomerSkuUtils.getSkuStockQuantity(articleNumber));
                    }
                }

                list = contentList;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("从ES产品数据失败{}", e.getMessage());
        }
        long end = System.currentTimeMillis();
        log.info("从EsLazadaItem获取数据耗时{}ms", (end - start));
        return ApiResult.newSuccess(list);
//        return null;
    }

    /**
     * 按条件统计listing数量
     * @param request query
     * @return countNumber
     */
    @Override
    public Long countAccountListingByQuery(EsLazadaItemRequest request) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        setQuery(boolQueryBuilder, request);
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .addAggregation(AggregationBuilders.cardinality("total").field("itemId"))
                .build();
        SearchHits<EsLazadaItem> search = elasticsearchRestTemplate2.search(query, EsLazadaItem.class, lazadaItemIndexCoordinates);
        Aggregations aggregations = search.getAggregations();
        Cardinality valueCount = aggregations.get("total");
        return valueCount.getValue();
    }

    @Override
    public UpdateResponse updateRequestByDocId(UpdateQuery updateQuery) {
        return elasticsearchRestTemplate2.update(updateQuery, lazadaItemIndexCoordinates);
    }

    @Override
    public void updateNnStockRequest(EsLazadaItem esLazadaItem){
        if(esLazadaItem == null || StringUtils.isBlank(esLazadaItem.getId())){
            return;
        }
        try {
            UpdateRequest updateRequest = new UpdateRequest(lazadaItemIndexCoordinates.getIndexName(), esLazadaItem.getId());
            updateRequest.doc(XContentType.JSON,
                    "nnStock", esLazadaItem.getNnStock(),
                    "nnStockUpdateDate", DateUtils.getCurrentTimeStr(null)
            );
            org.elasticsearch.action.update.UpdateResponse updateResponse = restHighLevelClient2.update(updateRequest, RequestOptions.DEFAULT);
        }catch (Exception e) {
            log.error(lazadaItemIndexCoordinates.getIndexName() + esLazadaItem.getId() + "修改失败" + e.getMessage(), e);
            throw new RuntimeException(lazadaItemIndexCoordinates.getIndexName() + esLazadaItem.getId() + "修改失败" + e.getMessage());
        }
    }

    @Override
    public Map<Long, Double> getItemOrderNumTotal(String accountNumber, Set<Long> itemIds) {
        Map<Long, Double> itemOrderNumTotal = new HashMap<>();
        try {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.filter(QueryBuilders.termQuery("accountNumber", accountNumber));
            boolQueryBuilder.filter(QueryBuilders.termsQuery("itemId", itemIds));
            boolQueryBuilder.filter(QueryBuilders.termQuery("status", "active"));
            NativeSearchQuery query = new NativeSearchQueryBuilder()
                    .withQuery(boolQueryBuilder)
                    .addAggregation(AggregationBuilders.terms("by_itemId").field("itemId")
                            .subAggregation(AggregationBuilders.sum("total").field("orderNumTotal")))
                    .build();
            SearchHits<EsLazadaItem> search = elasticsearchRestTemplate2.search(query, EsLazadaItem.class, lazadaItemIndexCoordinates);
            Aggregations aggregations = search.getAggregations();
            if (aggregations == null) {
                return itemOrderNumTotal;
            }
            ParsedTerms byItemId = aggregations.get("by_itemId");
            if (byItemId == null) {
                return itemOrderNumTotal;
            }
            for (Terms.Bucket bucket : byItemId.getBuckets()) {
                Long key = (Long) bucket.getKey();
                if (itemOrderNumTotal.containsKey(key)) {
                    continue;
                }

                ParsedSum sum = bucket.getAggregations().get("total");
                itemOrderNumTotal.put(key, sum.getValue());
            }
        } catch (Exception e) {
            log.error("从ES获取订单数量失败{}", e.getMessage());
            return itemOrderNumTotal;
        }
        return itemOrderNumTotal;

    }


    @Override
    public List<SkuPubilshListingFirstJoinTimeVo> getLazadaFirstJoinTime(List<String> articleNumberList) {
        long now = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(articleNumberList)) {
            return List.of();
        }
        List<SkuPubilshListingFirstJoinTimeVo> skuPubilshListingFirstJoinTimeVoList = new ArrayList<>();
        for (String sku : articleNumberList) {
            NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("sku", sku.toUpperCase()));
            queryBuilder
                    .withQuery(boolQueryBuilder)
                    .withPageable(PageRequest.of(0, 10000))
                    .withFields("sku", "createDate")
                    .withSort(SortBuilders.fieldSort("createDate").order(SortOrder.ASC))
                    .build();
            NativeSearchQuery searchQuery = queryBuilder.build();
            searchQuery.setTrackTotalHits(true);
            Page<EsLazadaItem> search = lazadaItemRepository.search(searchQuery);
            if (CollectionUtils.isNotEmpty(search.getContent())) {
                EsLazadaItem esLazadaItem = search.getContent().get(0);
                SkuPubilshListingFirstJoinTimeVo skuPubilshListingFirstJoinTimeVo = new SkuPubilshListingFirstJoinTimeVo();
                skuPubilshListingFirstJoinTimeVo.setPlatform(SaleChannelEnum.LAZADA.getChannelName());
                skuPubilshListingFirstJoinTimeVo.setSonSku(sku);
                Timestamp startTime = null;
                if (null != esLazadaItem.getCreateDate()) {
                    startTime = new Timestamp(esLazadaItem.getCreateDate().getTime());
                }
                skuPubilshListingFirstJoinTimeVo.setFirstTime(startTime);
                skuPubilshListingFirstJoinTimeVoList.add(skuPubilshListingFirstJoinTimeVo);
            }
        }
        long timeEs = System.currentTimeMillis() - now;
        log.info("/publishListing/getFirstJoinTime查询ES->lazada_item,耗时->{}ms,sku数量->{}", timeEs, articleNumberList.size());
        return skuPubilshListingFirstJoinTimeVoList;
    }

    @Override
    public void updateRequest(String json, String id) {
        if (StringUtils.isBlank(json) || StringUtils.isBlank(id)) {
            return;
        }
        try {
            UpdateRequest updateRequest = new UpdateRequest(indexName, id);
            updateRequest.doc(json, XContentType.JSON);
            org.elasticsearch.action.update.UpdateResponse updateResponse = restHighLevelClient2.update(updateRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error(indexName + id + "修改失败" + e.getMessage(), e);
            throw new RuntimeException(indexName + id + "修改失败" + e.getMessage());
        }
    }
}
