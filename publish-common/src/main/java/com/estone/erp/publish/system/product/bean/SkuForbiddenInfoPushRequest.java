package com.estone.erp.publish.system.product.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SkuForbiddenInfoPushRequest {
    /**
     * 子SKU
     */
    private String sonSku;

    /**
     * 禁售类型
     */
    private String infringementTypeName;

    /**
     * 禁售原因
     */
    private String infringementObj;


}
