package com.estone.erp.publish.system.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.PagingUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.base.pms.model.Country;
import com.estone.erp.publish.common.util.RetryUtil;
import com.estone.erp.publish.system.account.modle.FrozenAccountVO;
import com.estone.erp.publish.system.account.modle.SalesmanAccountInfoRequest;
import com.estone.erp.publish.system.order.modle.*;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 对接订单系统工具类
 *
 * @Auther yucm
 * @Date 2020/12/23
 */
@Slf4j
public class OrderUtils {

    private static final Cache<String, Set<String>> ASIN_LOCAL_CACHE = CacheBuilder.newBuilder()
            .expireAfterAccess(2, TimeUnit.HOURS).build();


    private static OrderClient orderClient;

    static {
        try {
            orderClient = SpringUtils.getBean(OrderClient.class);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        }
    }

    /**
     * 根据sku 账号获取订单信息
     *
     * @param customerOrderBySkuRequests
     * @return
     */
    public static ApiResult<List<CustomerOrderBySkuResponse>> getCustomerOrderBySku(List<CustomerOrderBySkuRequest> customerOrderBySkuRequests) {
        if (CollectionUtils.isEmpty(customerOrderBySkuRequests)) {
            return ApiResult.newError("请求不可以为空!");
        }

        List<CustomerOrderBySkuResponse> results = null;
        try {
            ApiRequestParam<String> requestParam = new ApiRequestParam();
            requestParam.setArgs(JSON.toJSONString(customerOrderBySkuRequests));

            String json = orderClient.getCustomerOrderBySku(requestParam);
            results = JSON.parseObject(json, new TypeReference<List<CustomerOrderBySkuResponse>>() {
            });
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(String.format("请求失败:%s", e.getMessage()));
        }

        return ApiResult.newSuccess(results);
    }

    /**
     * 获取所有国家
     *
     * @return
     */
    public static ApiResult<List<Country>> getCountryList() {
        try {
            String json = orderClient.getCountryList();
            ApiResult<List<Country>> apiResult = JSON.parseObject(json, new TypeReference<ApiResult<List<Country>>>() {
            });
            if (apiResult.isSuccess()) {
                return apiResult;
            } else {
                return ApiResult.newError(String.format("请求订单系统报错:%s", apiResult.getErrorMsg()));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(String.format("请求订单系统失败:%s", e.getMessage()));
        }
    }

    /**
     * 推送账号状态到账号系统更新
     */
    public static ApiResult<String> pushAccountStatus(SalesmanAccountInfoRequest accountInfoRequest) {
        try {
            ApiResult<String> result = orderClient.updateAccountExceptionStatus(accountInfoRequest);
            log.info("push account status,param:{}, result:{}", accountInfoRequest.toString(), JSON.toJSONString(result));
            return result;
        } catch (Exception e) {
            log.error("推送账号状态更新异常：param:{}", accountInfoRequest, e);
        }
        return ApiResult.newError("fail");
    }

    /**
     * 获取walmart temu 销量
     *
     * @param day
     * @param accountNumber
     * @return
     */
    public static ApiResult<WalmartTemuSaleVolume> selectWalmartTemuSaleVolume(Integer day, String accountNumber) {
        if (null == day || StringUtils.isBlank(accountNumber)) {
            return ApiResult.newError("day and accountNumber 不可以为空！");
        }

        try {
            String json = orderClient.selectWalmartTemuSaleVolume(day, accountNumber);
            ApiResult<WalmartTemuSaleVolume> apiResult = JSON.parseObject(json, new TypeReference<ApiResult<WalmartTemuSaleVolume>>() {
            });
            if (apiResult.isSuccess()) {
                return apiResult;
            } else {
                return ApiResult.newError(String.format("请求订单系统报错:%s", apiResult.getErrorMsg()));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(String.format("请求订单系统失败:%s", e.getMessage()));
        }
    }

    /**
     * 获取谷仓库龄
     *
     * @param request
     * @return
     */
    public static ApiResult<List<GoodcangInventoryAgeResponse>> getGoodcangInventoryAge(GoodcangInventoryAgeRequest request) {
        try {
            ApiResult<List<GoodcangInventoryAgeResponse>> apiResult = orderClient.getGoodcangInventoryAge(request);
            return apiResult;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(String.format("请求订单系统失败:%s", e.getMessage()));
        }
    }

    /**
     * 获取谷仓库龄
     *
     * @param saleChannel
     * @return
     */
    public static ApiResult<List<FrozenAccountVO>> getFreezeAccountToPublish(String saleChannel) {
        try {
            return orderClient.getFreezeAccountToPublish(saleChannel);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(String.format("请求订单系统失败:%s", e.getMessage()));
        }
    }

    /**
     * 根据子asin查询订单FBA库存管理存在的asin（asin过多需分页）
     *
     * @param sonAsinList 子asin
     * @return FBA asin
     */
    public static ApiResult<String> getFbaInventoryAsin(List<String> sonAsinList) {
        if (CollectionUtils.isEmpty(sonAsinList)) {
            return ApiResult.newError("参数为空");
        }
        try {
            Map<String, String> body = new HashMap<>(1);
            body.put("asins", StringUtils.join(sonAsinList, ","));
            return orderClient.getFbaInventoryAsin(body);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 根据asin查询订单FBA存在的asin（如果调用报错，会将报错的asin返回）
     *
     * @param sonAsinList 子asin
     * @return FBA asin
     */
    public static List<String> getFBAExistAsins(List<String> sonAsinList) {
        List<String> existSonAsinList = new ArrayList<>();
        if (CollectionUtils.isEmpty(sonAsinList)) {
            return existSonAsinList;
        }
        List<List<String>> lists = PagingUtils.newPagingList(sonAsinList, 1000);
        for (List<String> list : lists) {
            try {
                ApiResult<String> asinResult = getFbaInventoryAsin(list);
                if (!asinResult.isSuccess()) {
                    log.error(String.format("调用订单系统查询asin报错：%s，该asin：%s", asinResult.getErrorMsg(), StringUtils.join(list, ",")));
                    XxlJobLogger.log(String.format("调用订单系统查询asin报错：%s，该asin：%s", asinResult.getErrorMsg(), StringUtils.join(list, ",")));
                    existSonAsinList.addAll(list);
                    continue;
                }
                if (StringUtils.isNotBlank(asinResult.getResult())) {
                    existSonAsinList.addAll(CommonUtils.splitList(asinResult.getResult(), ","));
                }
            } catch (Exception e) {
                log.error(String.format("调用订单系统查询asin报错：%s，该asin：%s", e.getMessage(), StringUtils.join(list, ",")));
                XxlJobLogger.log(String.format("调用订单系统查询asin报错：%s，该asin：%s", e.getMessage(), StringUtils.join(list, ",")));
                existSonAsinList.addAll(list);
            }
        }

        return existSonAsinList;
    }

    /**
     * 根据asin查询存在可售数量或在途数量的FBA asin（如果调用报错，会将报错的asin返回）
     *
     * @param sonAsinList 子asin
     * @return 存在可售数量或在途数量的FBA asin
     */
    public static List<String> checkSellableAndOnWayQuantity(List<String> sonAsinList) {
        List<String> existSonAsinList = new ArrayList<>();
        List<List<String>> lists = PagingUtils.newPagingList(sonAsinList, 1000);
        for (List<String> list : lists) {
            try {
                ApiResult<List<String>> asinResult = orderClient.checkSellableAndOnWayQuantity(list);
                if (!asinResult.isSuccess()) {
                    log.error(String.format("调用订单系统查询存在可售数量或在途数量的FBA asin报错：%s，该asin：%s", asinResult.getErrorMsg(), StringUtils.join(list, ",")));
                    existSonAsinList.addAll(list);
                    continue;
                }
                if (CollectionUtils.isNotEmpty(asinResult.getResult())) {
                    existSonAsinList.addAll(asinResult.getResult());
                }
            } catch (Exception e) {
                log.error(String.format("调用订单系统查询存在可售数量或在途数量的FBA asin报错：%s，该asin：%s", e.getMessage(), StringUtils.join(list, ",")));
                existSonAsinList.addAll(list);
            }
        }

        return existSonAsinList;
    }

    /**
     * 获取所有asin
     *
     * @return
     */
    public static ApiResult<List<AsinInfoResponse>> getAllAsinInfo() {
        try {
            return orderClient.getAllAsinInfo();
        } catch (Exception e) {
            log.error("订单接口获取所有asin失败 " + e.getMessage(), e);
            return ApiResult.newError("订单接口获取所有asin失败 " + e.getMessage());
        }
    }


    /**
     * 获取亚马逊Asin当天销量
     *
     * @return
     */
    public static ApiResult<List<AsinSalesVolume>> getAmazonAsinTodaySaleVolume(List<String> asinList) {
        if (CollectionUtils.isEmpty(asinList)) {
            return ApiResult.newSuccess(new ArrayList<>());
        }
        try {
            return RetryUtil.doRetry(() -> orderClient.publishAmazonSalesVolume(asinList), 3);
        } catch (Exception e) {
            log.error("调用获取亚马逊Asin当天销量失败 " + e.getMessage(), e);
            return ApiResult.newError("调用获取亚马逊Asin当天销量失败 " + e.getMessage());
        }
    }

    /**
     * ASIN 是否为FBA asin
     *
     * @param asin
     * @return
     */
    public static Boolean isFbaAsin(String asin) {
        if (StringUtils.isBlank(asin)) {
            return false;
        }
        Set<String> fbaAsinSet = getAllAsinCodeCache();
        return fbaAsinSet.contains(asin);
    }

    public static Set<String> getAllAsinCodeCache() {
        Set<String> asinCode = ASIN_LOCAL_CACHE.getIfPresent("FBA_ASIN_CODE");
        if (CollectionUtils.isNotEmpty(asinCode)) {
            return asinCode;
        }
        ApiResult<List<AsinInfoResponse>> allAsinInfo = getAllAsinInfo();
        if (allAsinInfo.isSuccess()) {
            Set<String> asinCodes = allAsinInfo.getResult().stream()
                    .map(AsinInfoResponse::getAsin)
                    .distinct()
                    .collect(Collectors.toSet());
            ASIN_LOCAL_CACHE.put("FBA_ASIN_CODE", asinCodes);
            return asinCodes;
        }
        throw new RuntimeException("获取订单所有asin异常");
    }


    /**
     * 刷新亚马逊店铺账号ean前缀
     *
     * @return
     */
    public static ApiResult<String> refreshAccountEanPrefix(String accountNumber, String oldPrefixStr) {
        if (StringUtils.isEmpty(accountNumber)) {
            return ApiResult.newError("账号参数为空");
        }
        try {
            return orderClient.refreshAccountEanPrefix(accountNumber, oldPrefixStr);
        } catch (Exception e) {
            log.error("刷新亚马逊店铺账号ean前缀失败 " + e.getMessage(), e);
            return ApiResult.newError("刷新亚马逊店铺账号ean前缀失败 " + e.getMessage());
        }
    }

    /**
     * 新增亚马逊店铺账号ean前缀
     *
     * @return
     */
    public static ApiResult<String> addAccountEanPrefix(String accountNumber, Integer eanPrefixAmount) {
        if (StringUtils.isEmpty(accountNumber)) {
            return ApiResult.newError("账号参数为空");
        }
        try {
            return orderClient.addAccountEanPrefix(accountNumber, eanPrefixAmount);
        } catch (Exception e) {
            log.error("新增亚马逊店铺账号ean前缀 " + e.getMessage(), e);
            return ApiResult.newError("新增亚马逊店铺账号ean前缀 " + e.getMessage());
        }
    }

    /**
     * 获取FBM当天出单ASIN
     *
     * @return
     */
    public static ApiResult<List<String>> getTodaySoldFbmAsin() {
        try {
            return orderClient.todaySoldFbmAsin();
        } catch (Exception e) {
            return ApiResult.newError("获取FBM当天出单ASIN " + e.getMessage());
        }
    }


    /**
     * 根据店铺返回shopee店铺绩效订单未完成率和逾期出货率数据
     *
     * @return
     */
    public static ApiResult<List<ShopPerformance>> getShopPerformance(List<String> accounts) {
        try {
            return orderClient.getShopPerformance(accounts);
        } catch (Exception e) {
            return ApiResult.newError("根据店铺返回shopee店铺绩效订单未完成率和逾期出货率数据 " + e.getMessage());
        }
    }

    /**
     * 标记店铺状态为疑似冻结
     *
     * @return
     */
    public static ApiResult<String> freezeAccount(String merchantId) {
        try {
            return orderClient.freezeAccount(merchantId);
        } catch (Exception e) {
            return ApiResult.newError("标记店铺状态为疑似冻结 " + e.getMessage());
        }
    }

    /**
     * 获取walmart当天出单ASIN
     *
     * @return
     */
    public static ApiResult<List<WalmartSaleQuantity>> getTodaySoldWalmartAsin() {
        try {
            return orderClient.todaySoldWalmartAsin();
        } catch (Exception e) {
            return ApiResult.newError("获取walmart当天出单异常 " + e.getMessage());
        }
    }

}
