package com.estone.erp.publish.system.product.bean;

import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.esProduct.bean.SpecialGoods;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 子SKU 少量信息
 *
 * @Auther yucm
 * @Date 2021/11/11
 */
@Data
public class SonSkuFewInfo {

    /**
     * 子SKU
     */
    private String sonSku;

    /**
     * 单品状态
     */
    private String itemStatus;

    /**
     * 标签
     */
    private String tag;

    /**
     * 英文标签
     */
    private String enTag;

    /**
     * 产品重量
     */
    private Double productWeight;

    /**
     * 包材重量
     */
    private BigDecimal packingWeight;

    /**
     * 搭配包材重量
     */
    private BigDecimal matchWeight;

    /**
     * 预估包装重量
     */
    private Double packageWeight;

    /**
     * 一天销量总数
     */
    private Double oneSalesNum;

    /**
     * 七天销量总数
     */
    private Double sevenSalesNum;

    /**
     * 14天销量总数
     */
    private Double fourteenSalesNum;

    /**
     * 30天销量
     */
    private Double thirtySalesNum;

    /**
     * 60天销量总数
     */
    private Double sixtySalesNum;

    /**
     * 90天销量
     */
    private Double ninetySalesNum;

    /**
     * 产品录入时间
     */
    private Long inSingleTime;

    /**
     * 销售成本价
     */
    //private Double cost;

    /**
     * 销售成本价
     */
    private Double saleCost;

    /**
     * 可用库存
     */
    private Integer availableStock;

    /**
     * 特殊标签集合
     */
    private List<SpecialGoods> specialGoodsList;

    /**
     * 特殊标签类型集合
     */
    private List<Integer> specialTypeList;


    /**
     * 全部禁售信息
     */
    private String infringementSaleProhibition;

    /**
     * 禁售平台集合
     */
    private List<String> saleForbiddenList;

    /**
     * 禁售平台站点
     */
    private List<SalesProhibitionsVo> salesProhibitionsVos;

    /**
     * sku侵权信息
     */
    private Map<String,List<String>> platSiteMap;
}
