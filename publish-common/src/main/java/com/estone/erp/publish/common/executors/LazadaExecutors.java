package com.estone.erp.publish.common.executors;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * Lazada线程池管理类
 *
 */
public class LazadaExecutors {

    public static final int MAX_POLL_SIZE_TEN = 10;

    public static final int MAX_POLL_SIZE_TWENTY = 100;
    /**
     * 最大同步产品的线程数
     */
    public static final int MAX_SYNC_PRODUCT_THREADS = 30;

    public static final int MAX_SYNC_PRODUCT_DETAIL_THREADS = 50;

    public static final int MAX_POLL_100 = 100;


    public static final ThreadPoolExecutor SYNC_PRODUCT_POOL =
            ExecutorUtils.newFixedThreadPool("lazada-product-pool", MAX_SYNC_PRODUCT_THREADS);

    public static final ThreadPoolExecutor SYNC_PRODUCT_DETAIL_POOL =
            ExecutorUtils.newFixedThreadPool("lazada-product-detail-pool", MAX_SYNC_PRODUCT_DETAIL_THREADS);

    public static final ThreadPoolExecutor EXECUTE_SYNC_PRODUCT_EXTENSION_POOL =
            ExecutorUtils.newFixedThreadPool("lazada-product-extension-pool", MAX_SYNC_PRODUCT_DETAIL_THREADS);

    public static final ThreadPoolExecutor EXECUTE_SYNC_SEMI_PRODUCT_POOL =
            ExecutorUtils.newFixedThreadPool("lazada-semi-product-pool", MAX_SYNC_PRODUCT_DETAIL_THREADS);

    public static final ThreadPoolExecutor SEMI_PRODUCT_UPGRADE_POOL =
            ExecutorUtils.newFixedThreadPool("lazada-semi-product-upgrade-pool", MAX_SYNC_PRODUCT_DETAIL_THREADS);

    public static final ThreadPoolExecutor SEMI_PRODUCT_UPDATE_NNSTOCK_POOL =
            ExecutorUtils.newFixedThreadPool("lazada-semi-product-update-nnstock-pool", MAX_POLL_SIZE_TEN);

    public static final ThreadPoolExecutor SEMI_PRODUCT_ACCOUNT_UPGRADE_POOL =
            ExecutorUtils.newFixedThreadPool("lazada-semi-product-account-upgrade-pool", MAX_SYNC_PRODUCT_DETAIL_THREADS);

    public static final ThreadPoolExecutor REMOVE_ITEM_POOL =
            ExecutorUtils.newFixedThreadPool("lazada-remove-item-pool", MAX_POLL_SIZE_TEN);

    public static final ThreadPoolExecutor UPDATE_ITEM_POOL =
            ExecutorUtils.newFixedThreadPool("lazada-update-item-pool", MAX_SYNC_PRODUCT_DETAIL_THREADS);

    public static final ThreadPoolExecutor UPDATE_ACCOUNT_ITEM_POOL =
            ExecutorUtils.newFixedThreadPool("lazada-update-account-item-pool", MAX_SYNC_PRODUCT_DETAIL_THREADS);

    public static final ThreadPoolExecutor SYNC_UNQUALIFIED_SKU_POOL =
            ExecutorUtils.newFixedThreadPool("lazada-unqualified-sku-pool", MAX_POLL_SIZE_TWENTY);

    public static final ThreadPoolExecutor CUSTOM_QUERY_POOL =
            ExecutorUtils.newFixedThreadPool("lazada-custom-query-pool", MAX_SYNC_PRODUCT_THREADS);

    public static final ThreadPoolExecutor PUBLISH_POOL =
            ExecutorUtils.newFixedThreadPool("lazada-publish-pool", MAX_SYNC_PRODUCT_DETAIL_THREADS);

    public static final ThreadPoolExecutor ADD_ITEM_POOL =
            ExecutorUtils.newFixedThreadPool("lazada-add-item-pool", MAX_POLL_SIZE_TWENTY);

    public static final ThreadPoolExecutor SKU_SELL_AMOUNT_POOL =
            ExecutorUtils.newFixedThreadPool("lazada_sku_sell_amount_pool", MAX_POLL_SIZE_TWENTY);

    public static final ThreadPoolExecutor AUTO_PUBLISH_POOL =
            ExecutorUtils.newFixedThreadPool("lazada-auto_publish_pool", MAX_POLL_SIZE_TWENTY);

    public static final ThreadPoolExecutor PRODUCT_ACTIVE_POOL =
            ExecutorUtils.newFixedThreadPool("lazada_product_active_pool", MAX_POLL_SIZE_TWENTY);

    public static final ThreadPoolExecutor UPLOAD_IMAGE_POOL =
            ExecutorUtils.newFixedThreadPool("lazada_upload_image_pool", MAX_POLL_SIZE_TWENTY);

    public static final ThreadPoolExecutor UPLOAD_COS_IMAGE_POOL =
            ExecutorUtils.newFixedThreadPool("lazada_upload_cos_image_pool", MAX_POLL_SIZE_TWENTY);

    public static final ThreadPoolExecutor TRANSFER_DB2ES_POOL =
            ExecutorUtils.newFixedThreadPool("transfer_db2es_pool", MAX_SYNC_PRODUCT_THREADS);

    public static final ThreadPoolExecutor CLEAR_LISTING_POOL =
            ExecutorUtils.newFixedThreadPool("lazada-clear-listing-pool", 17);

    public static final ThreadPoolExecutor AUTO_PRICE_POOL =
            ExecutorUtils.newFixedThreadPool("lazada-auto-price", 12);

    public static final ThreadPoolExecutor SALE_UPDATE_POOL =
            ExecutorUtils.newFixedThreadPool("sale_update_pool", 12);

    public static final ThreadPoolExecutor SALE_DATA_STATISTICS = ExecutorUtils.newFixedThreadPool("lazada-sale_data_statistics", 10);
    private static final ExecutorService SYN_INFRINGEMENT_WORD_POOL = ExecutorUtils.newFixedThreadPool("lazada_syn_infringement_word", 30);
    public static final ThreadPoolExecutor CALC_SEMI_GROSS_POOL =
            ExecutorUtils.newFixedThreadPool("lazada_calc_semi_gross_pool", MAX_POLL_SIZE_TWENTY);

    public static final ThreadPoolExecutor UPDATE_DEFAULT_VALUE =
            ExecutorUtils.newFixedThreadPool("lazada-update-default-value", 5);

    public static final ThreadPoolExecutor CHECK_INFRINGEMENT_POOL = ExecutorUtils
            .newFixedThreadPool("lazada-check-infringement-pool", 5);


    public static final ThreadPoolExecutor PUBLISH_PRODUCT_POOL = ExecutorUtils
            .newFixedThreadPool("lazada-publish-product-pool", 10);
    public static final ThreadPoolExecutor CONFIG_PUBLISH_PRODUCT_POOL = ExecutorUtils
            .newFixedThreadPool("lazada-config-publish-pool", 20);

    public static final ThreadPoolExecutor UPDATE_SPU_IMAGE_POOL = ExecutorUtils
            .newFixedThreadPool("lazada-update-spu-image-pool", 20);

    public static final ThreadPoolExecutor UPDATE_SKU_IMAGE_POOL = ExecutorUtils
            .newFixedThreadPool("lazada-update-sku-image-pool", 20);


    /**
     * 执行新增产品任务
     * @param runnable runnable
     */
    public static void executeAddItem(Runnable runnable) {
        ExecutorUtils.execute(ADD_ITEM_POOL, runnable, "lazada-add-item");
    }


    /**
     * 执行同步产品任务
     * @param runnable runnable
     */
    public static void executeSyncProduct(Runnable runnable) {
        ExecutorUtils.execute(SYNC_PRODUCT_POOL, runnable, "lazada-product");
    }
    /**
     * 执行处理listing任务
     * @param runnable runnable
     */
    public static void executeSyncProductDetail(Runnable runnable) {
        ExecutorUtils.execute(SYNC_PRODUCT_DETAIL_POOL, runnable, "lazada-product-detail");
    }

    /**
     * 执行处理同步半托管扩展数据任务
     * @param runnable runnable
     */
    public static void executeSyncProductExtension(Runnable runnable) {
        ExecutorUtils.execute(EXECUTE_SYNC_PRODUCT_EXTENSION_POOL, runnable, "lazada-product-extension");
    }

    /**
     * 执行处理同步半托管产品数据任务
     * @param runnable runnable
     */
    public static void executeSyncSemiProduct(Runnable runnable) {
        ExecutorUtils.execute(EXECUTE_SYNC_SEMI_PRODUCT_POOL, runnable, "lazada-semi-product-extension");
    }

    /**
     * 升级半托管产品任务
     * @param runnable runnable
     */
    public static void executeSemiProductUpgradePool(Runnable runnable) {
        ExecutorUtils.execute(SEMI_PRODUCT_UPGRADE_POOL, runnable, "lazada-semi-product-upgrade-extension");
    }

    public static void executeSemiProductUpdateNnStockPool(Runnable runnable) {
        ExecutorUtils.execute(SEMI_PRODUCT_UPDATE_NNSTOCK_POOL, runnable, "lazada-semi-product-update-nnstock");
    }

    /**
     * 升级半托管产品账号任务
     *
     * @param runnable runnable
     */
    public static void executeSemiProductAccountUpgradePool(Runnable runnable) {
        ExecutorUtils.execute(SEMI_PRODUCT_ACCOUNT_UPGRADE_POOL, runnable, "lazada-semi-product-account-upgrade-extension");
    }


    /**
     * 执行自定义任务
     * @param runnable
     */
    public static void executeCustomQuery(Runnable runnable) {
        ExecutorUtils.execute(CUSTOM_QUERY_POOL, runnable, "lazada-custom-query");
    }

    /**
     * 删除产品线程
     * @param runnable
     */
    public static void executeRemoveItem(Runnable runnable) {
        ExecutorUtils.execute(REMOVE_ITEM_POOL, runnable, "lazada-remove-item");
    }

    /**
     * 执行产品更新线程
     *
     * @param runnable
     */
    public static void executeUpdateAccountItem(Runnable runnable) {
        ExecutorUtils.execute(UPDATE_ACCOUNT_ITEM_POOL, runnable, "lazada-update-account-item");
    }

    /**
     * 执行产品更新线程
     * @param runnable
     */
    public static void executeUpdateItem(Runnable runnable) {
        ExecutorUtils.execute(UPDATE_ITEM_POOL, runnable, "lazada-update-item");
    }

    /**
     * 用于执行 针对PMS系统停产，存档的SKU, 库存修改为0
     * @param runnable
     */
    public static void executeUnqualifiedSku(Runnable runnable) {
        ExecutorUtils.execute(SYNC_UNQUALIFIED_SKU_POOL, runnable, "lazada-unqualified-sku");
    }

    /**
     * 执行刊登任务结果查询
     * @param runnable
     */
    public static void executePublish(Runnable runnable) {
        ExecutorUtils.execute(PUBLISH_POOL, runnable, "lazada-publish");
    }

    /**
     * 执行lazada店铺sku数量统计
     * @param runnable
     */
    public static void executeSkuSellAmount(Runnable runnable) {
        ExecutorUtils.execute(SKU_SELL_AMOUNT_POOL, runnable, "lazada_sku_sell_amount");
    }

    /**
     * 执行自动刊登线程
     * @param runnable
     */
    public static void executeAutoPublish(Runnable runnable) {
        ExecutorUtils.execute(AUTO_PUBLISH_POOL, runnable, "lazada-auto_publish");

    }

    /**
     * 执行产品重新激活
     * @param runnable
     */
    public static void executeProductActive(Runnable runnable) {
        ExecutorUtils.execute(PRODUCT_ACTIVE_POOL, runnable, "product_active_pool");
    }

    /**
     * 图片上传
     * @param runnable
     */
    public static void executeUploadImage(Runnable runnable) {
        ExecutorUtils.execute(UPLOAD_IMAGE_POOL, runnable, "upload_image_pool");
    }

    /**
     * DB迁移
     * @param runnable
     */
    public static void executeTransferDB2ES(Runnable runnable) {
        ExecutorUtils.execute(TRANSFER_DB2ES_POOL, runnable, "transfer_db2es_pool");
    }


    /**
     * 清除listing
     * @param runnable
     */
    public static void executeClearListing(Runnable runnable) {
        ExecutorUtils.execute(CLEAR_LISTING_POOL, runnable, "lazada-clear-listing-pool");
    }

    /**
     * 执行自动调价
     * @param runnable
     */
    public static void executeAutoPrice(Runnable runnable) {
        ExecutorUtils.execute(AUTO_PRICE_POOL, runnable, "lazada-auto-price");
    }

    /**
     * 页面更新
     * @param runnable
     */
    public static void executeSaleUpdate(Runnable runnable) {
        ExecutorUtils.execute(SALE_UPDATE_POOL, runnable, "sale_update_pool");
    }
    /**
     * 同步侵权词
     * @param runnable
     */
    public static void exeSynInfringementWord(Runnable runnable) {
        ExecutorUtils.execute(SYN_INFRINGEMENT_WORD_POOL, runnable, "syn_infringement_word_pool");
    }


    /**
     * 执行lazada店铺sku数量统计
     *
     * @param runnable
     */
    public static void executeCalcSemiGrossPool(Runnable runnable) {
        ExecutorUtils.execute(CALC_SEMI_GROSS_POOL, runnable, "lazada_calc_semi_gross_pool");
    }

    public static void executeConfigPublish(Runnable runnable) {
        ExecutorUtils.execute(CONFIG_PUBLISH_PRODUCT_POOL, runnable, "lazada-config-publish-product");
    }


    public static void publishProduct(Runnable runnable) {
        ExecutorUtils.execute(PUBLISH_PRODUCT_POOL, runnable, "lazada-publish-product");
    }


    public static void executeUpdateSpuImage(Runnable runnable) {
        ExecutorUtils.execute(UPDATE_SPU_IMAGE_POOL, runnable, "lazada-update-spu-image-pool");
    }


    public static void executeUpdateSkuImage(Runnable runnable) {
        ExecutorUtils.execute(UPDATE_SKU_IMAGE_POOL, runnable, "lazada-update-sku-image-pool");
    }
}
