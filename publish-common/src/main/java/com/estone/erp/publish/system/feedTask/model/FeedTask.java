package com.estone.erp.publish.system.feedTask.model;

import com.estone.erp.publish.system.feedTask.service.impl.FeedTaskServiceImpl;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class FeedTask implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID database column feed_task.id
     */
    private Long id;

    /**
     * 关联ID database column feed_task.association_id
     */
    private String associationId;

    /**
     * 账号 database column feed_task.account_number
     */
    private String accountNumber;

    /**
     * 货号 database column feed_task.article_number
     */
    private String articleNumber;

    /**
     * 任务类型 database column feed_task.task_type
     */
    private String taskType;

    /**
     * 平台 database column feed_task.platform
     */
    private String platform;

    /**
     * 任务状态（1 等待中 2 执行中 3 完成） database column feed_task.task_status
     */
    private Integer taskStatus;

    /**
     * 结果状态（1 成功 2 失败） database column feed_task.result_status
     */
    private Integer resultStatus;

    /**
     * 结果信息 database column feed_task.result_msg
     */
    private String resultMsg;

    /**
     * 创建人 database column feed_task.created_by
     */
    private String createdBy;

    /**
     * 任务创建时间 database column feed_task.create_time
     */
    private Timestamp createTime;

    /**
     * 任务运行时间 database column feed_task.run_time
     */
    private Timestamp runTime;

    /**
     * 任务完成时间 database column feed_task.finish_time
     */
    private Timestamp finishTime;

    /**
     * 属性(预留) database column feed_task.attribute1
     * shopee 只能用来做规则名称
     */
    private String attribute1;

    /**
     * 属性 database column feed_task.attribute2
     * shopee 属性2只能用来产品id
     */
    private String attribute2;

    /**
     * 属性 database column feed_task.attribute3
     */
    private String attribute3;

    /**
     * 属性 database column feed_task.attribute4
     */
    private String attribute4;

    /**
     * 属性 database column feed_task.attribute5
     */
    private String attribute5;

    /**
     * 属性 database column feed_task.attribute6
     * lazada -- 规则名称
     */
    private String attribute6;

    /**
     * 属性 database column feed_task.attribute7
     */
    private String attribute7;

    /**
     * 属性 database column feed_task.attribute8
     */
    private String attribute8;

    /**
     * 属性 database column feed_task.attribute9
     */
    private String attribute9;

    /**
     * 属性 database column feed_task.attribute10
     */
    private String attribute10;

    /**
     * 属性11 database column feed_task.attribute11
     */
    private String attribute11;

    /**
     * 属性12 database column feed_task.attribute12
     */
    private String attribute12;


    private String tableIndex;

    public void setTableIndex() {
        String platform = this.platform;
        String feedTableIndex = FeedTaskServiceImpl.PLATFORM_FEED_SUFFIX_MAP.get(platform);
        if (StringUtils.isNotBlank(feedTableIndex)) {
            this.tableIndex = feedTableIndex;
        }
    }
}