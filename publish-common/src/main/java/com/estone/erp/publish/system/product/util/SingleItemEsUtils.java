package com.estone.erp.publish.system.product.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.bean.SkuListAndCode;
import com.estone.erp.publish.system.product.bean.SonSkuFewInfo;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.enums.SingleSourceEnum;
import com.estone.erp.publish.system.product.esProduct.bean.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * es产品转换ProductInfo工具类
 * @Auther yjy
 * @Date 2020/11/24
 */
@Slf4j
public class SingleItemEsUtils {

    public static final String  PRODUCT_PACKING_MATERIAL_KEY = "PRODUCT_PACKING_MATERIAL_";
    private static final Integer LONG_TITLE_TOTAL = 6;
    private static final Integer SHORT_TITLE_TOTAL = 12;

    /**
     * 冠通产品基本信息转换
     * @param singleItemEsList
     */
    public static List<ProductInfo> tranSingleItemEsToProductInfo(List<SingleItemEs> singleItemEsList) {
        if(CollectionUtils.isEmpty(singleItemEsList)) {
            return null;
        }
        List<ProductInfo> productInfoList = new ArrayList<>();

        for (SingleItemEs singleItemEs : singleItemEsList) {

            ProductInfo productInfo = new ProductInfo();
            String mainSku = singleItemEs.getMainSku();
            String sonSku = singleItemEs.getSonSku();
            if (StringUtils.isEmpty(mainSku) || StringUtils.isEmpty(sonSku)){
                continue;
            }
            productInfo.setId(singleItemEs.getId());
            productInfo.setName(singleItemEs.getName());
            productInfo.setMainSku(mainSku);
            productInfo.setSonSku(sonSku);
            productInfo.setItemStatus(getItemStatus(singleItemEs.getItemStatus()));
            productInfo.setCreateAt(singleItemEs.getCreateAt());
            productInfo.setIsSeasonNew(singleItemEs.getIsSeasonNew());
            Date inSingleTime = singleItemEs.getInSingleTime();
            if(inSingleTime != null){
                productInfo.setInSingleTime(inSingleTime.getTime());
            }
            productInfo.setProductWeight(singleItemEs.getProductWeight());
            productInfo.setLength(singleItemEs.getLength());
            productInfo.setWide(singleItemEs.getWide());
            productInfo.setHeight(singleItemEs.getHeight());

            SingleItemExternalInfo singleItemExternalInfo = singleItemEs.getSingleItemExternalInfo();
            if(singleItemExternalInfo != null){
                productInfo.setPackLength(singleItemExternalInfo.getPackLength());
                productInfo.setPackWidth(singleItemExternalInfo.getPackWidth());
                productInfo.setPackHeight(singleItemExternalInfo.getPackHeight());
                // 全部禁售信息：禁售类型，禁售原因，禁售平台站点,风险等级
                productInfo.setInfringementSaleProhibition(singleItemExternalInfo.getInfringementSaleProhibition());
            }
            productInfo.setSizeRemark(singleItemEs.getSizeRemark());
            productInfo.setDevEmpName(singleItemEs.getDevEmpName());

            List<SingleItemOfficial> singleItemOfficials = singleItemEs.getSingleItemOfficials();
            if (CollectionUtils.isNotEmpty(singleItemOfficials)){

                String cnCustoms = null;
                String enCustoms = null;
                String mustKeyword = null;
                String titleAtt = null;
                for ( SingleItemOfficial singleItemOfficial : singleItemOfficials){
                    // 标题、描述
                    if (StringUtils.isNotEmpty(singleItemOfficial.getLanguage())){
                        if ("en".equals(singleItemOfficial.getLanguage())){
                            titleAtt = StringUtils.isNotEmpty(singleItemOfficial.getTitle())?singleItemOfficial.getTitle():titleAtt;
                            if (StringUtils.isNotEmpty(titleAtt)) {
                                try {
                                    List<String> titleList = JSON.parseArray(titleAtt, String.class);
                                    if (CollectionUtils.isNotEmpty(titleList)) {
                                        for (String title : titleList) {
                                            if (StringUtils.isNotEmpty(title)) {
                                                productInfo.setTitleEn(title);
                                                break;
                                            } else {
                                                productInfo.setTitleEn(singleItemOfficial.getShortTitle1());
                                            }

                                        }
                                    }
                                }catch (Exception e){
                                    log.error("产品数据标题有问题，子sku :" + sonSku);
                                }
                                setAllLongTitleAndShortTitle(productInfo, singleItemOfficial);
                            }
                            // 描述
                            String mustDescription = singleItemOfficial.getMustDescription();
                            // 新描述
                            String newDescription = null;
                            try{
                                newDescription = singleItemOfficial.getNewDescription();
                            }catch (Exception e) {
                                log.error(sonSku + "转换新描述出错" + e.getMessage(),e);
                            }

                            String packageIncludes = singleItemOfficial.getPackageIncludes();
                            productInfo.setPackageIncludes(packageIncludes);
                            // 优先取新描述 不存在则取旧描述
                            productInfo.setDesEn(StringUtils.isNoneBlank(newDescription) ? newDescription : mustDescription);
                            // 关键字
                            mustKeyword = StringUtils.isNotEmpty(singleItemOfficial.getMustKeyword())?singleItemOfficial.getMustKeyword():mustKeyword;
                        }else if ("cn".equals(singleItemOfficial.getLanguage())){
                            productInfo.setTitleCn(singleItemOfficial.getTitle());
                            productInfo.setDesCn(singleItemOfficial.getMustDescription());
                        }
                    }
                    cnCustoms = StringUtils.isNotEmpty(singleItemOfficial.getCnCustoms())?singleItemOfficial.getCnCustoms():cnCustoms;
                    enCustoms = StringUtils.isNotEmpty(singleItemOfficial.getEnCustoms())?singleItemOfficial.getEnCustoms():enCustoms;
                }

                productInfo.setTitleAtt(titleAtt);
                productInfo.setEnCustoms(enCustoms);
                productInfo.setCnCustoms(cnCustoms);
                productInfo.setMustKeyword(mustKeyword);
            }

            // 分类
            if (null != singleItemEs.getCategory()) {
                Category category = singleItemEs.getCategory();
                productInfo.setCategoryName(category.getCnName());
                productInfo.setCategoryPath(category.getCategoryPath());
                productInfo.setFullpathcode(category.getFullpathcode());
                productInfo.setCategoryId(category.getId());
            }

            // 中文分类全路径
            productInfo.setFullpath(singleItemEs.getCategoryPath());

            productInfo.setSalesProhibition(singleItemEs.getSalesProhibition());

            if (null != singleItemEs.getSingleItemExternalInfo()) {
                productInfo.setSaleAtts(singleItemEs.getSingleItemExternalInfo().getSaleAtts());
            }

            // 主图
            if (null != singleItemEs.getSingleItemPicInfo() && StringUtils.isNotEmpty(singleItemEs.getSingleItemPicInfo().getFirstImage())) {
                productInfo.setFirstImage(singleItemEs.getSingleItemPicInfo().getFirstImage());
            }
            // 包材
            if (null != singleItemEs.getPackingMaterialsCode()) {
                String packingMaterialsCode = PRODUCT_PACKING_MATERIAL_KEY + singleItemEs.getPackingMaterialsCode();
                productInfo.setPackingMaterialsCode(singleItemEs.getPackingMaterialsCode().toString());
                String packingmaterials = PublishRedisClusterUtils.get(packingMaterialsCode);
                if (StringUtils.isNotEmpty(packingmaterials)) {
                    Packingmaterial packingmaterial = JSONObject.parseObject(packingmaterials,Packingmaterial.class);
                    productInfo.setPackingWeight(BigDecimal.valueOf(null == packingmaterial.getWeight()?0.0 :packingmaterial.getWeight()));
                    productInfo.setPackingPrice(BigDecimal.valueOf(null == packingmaterial.getPurchaseprice()?0.0 :packingmaterial.getPurchaseprice()));
                }
            }
            // 搭配包材
            if (null != singleItemEs.getMatchMaterialsCode()) {
                List<String> matchMaterialsCodeList = Arrays.asList(singleItemEs.getMatchMaterialsCode().split(","));
                productInfo.setMatchMaterialsCodeList(new ArrayList<>(matchMaterialsCodeList));
                Double matchWeight =0.0;
                Double matchPrice =0.0;
                for (String s : matchMaterialsCodeList){
                    String matchMaterialsCode = PRODUCT_PACKING_MATERIAL_KEY + s;
                    String packingmaterials = PublishRedisClusterUtils.get(matchMaterialsCode);
                    if (StringUtils.isNotEmpty(packingmaterials)) {
                        Packingmaterial packingmaterial = JSONObject.parseObject(packingmaterials,Packingmaterial.class);

                        matchWeight  = matchWeight + (null == packingmaterial.getWeight()?0.0 :packingmaterial.getWeight());
                        matchPrice = matchPrice + (null == packingmaterial.getPurchaseprice()?0.0 :packingmaterial.getPurchaseprice());
                    }
                }
                productInfo.setMatchWeight(BigDecimal.valueOf(matchWeight));
                productInfo.setMatchPrice(BigDecimal.valueOf(matchPrice));
            }
            // 预估包装重量
            productInfo.setPackageWeight(singleItemEs.getPackageWeight());

            // 探雅 tyJson
            String tyJson = singleItemEs.getTyJson();
            String sonskuS = singleItemEs.getSonSkus();
            if (SingleSourceEnum.TAN_YA.getCode().equals(singleItemEs.getSingleSource())
                    && StringUtils.isNotEmpty(tyJson)
                    && StringUtils.isNotEmpty(sonskuS)){
                try {
                    Map map = JSON.parseObject(sonskuS, Map.class);
                    List tyList = JSON.parseObject(tyJson, List.class);
                    if (CollectionUtils.isNotEmpty(tyList)) {
                        tyList.forEach(m -> {
                            Map mmap = (Map) m;
                            Object sku = mmap.get("sku");
                            String newSonSku = map.get(sku.toString()).toString();
                            if (sonSku.equals(newSonSku)) {
                                productInfo.setColor(mmap.get("color").toString());
                                productInfo.setSize(mmap.get("size").toString());
                            }
                        });
                    }
                }catch (Exception e){
                    log.error("探雅产品数据问题，子sku：" + sonSku);
                }
            }

            productInfo.setCost(singleItemEs.getCost());//产品成本
            if(singleItemEs.getSingleItemAllSupplier() != null){
                Double shippingCost = singleItemEs.getSingleItemAllSupplier().getShippingCost();
                productInfo.setShippingCost(shippingCost);//采购运费
            }
            productInfo.setStandardWeight(singleItemEs.getStandardWeight());//标准重量
            productInfo.setSaleCost(singleItemEs.getSaleCost());
            productInfo.setPostRemark(singleItemEs.getPostRemark());
            productInfo.setTag(singleItemEs.getTag());
            productInfo.setEnTag(singleItemEs.getTagCode());
            productInfo.setSingleSource(singleItemEs.getSingleSource());
            productInfo.setType(singleItemEs.getType());
            productInfo.setPlugSpecification(singleItemEs.getPlugSpecification());
            productInfo.setCustomCode(singleItemEs.getCustomsCode());
            List<SpecialGoods> specialGoodsList = singleItemEs.getSpecialGoodsList();
            if(CollectionUtils.isNotEmpty(specialGoodsList)){
                List<Integer> specialTypeList = specialGoodsList.stream().map(t -> t.getSpecialType())
                        .collect(Collectors.toList());
                productInfo.setSpecialTypeList(specialTypeList);
            }

            productInfoList.add(productInfo);
        }
        return productInfoList;
    }

    /**
     * 根据单品状态Code转换单品状态
     * 单品状态7000草稿，7001待上架，7002正常,7003清仓，7004暂停，7005休假，7006甩卖，7007停产，7008存档
     * New-草稿；Waiting-待上架；Normal-正常；Clearance-清仓；Pending-暂停；Holiday-休假；Reduction-甩卖；Stop-停产；Archived-存档；
     * @param itemStatusCode
     * @return
     */
    public static String getItemStatus(Integer itemStatusCode) {
        if(null == itemStatusCode) {
            return null;
        }
        return SingleItemEnum.getEnNameByCode(itemStatusCode);
    }

    /**
     * 大写SKU找对应实际SKU
     * @param sku
     */
    public static String upperCaseSkuToSku(String prefix, String sku) {
        if(StringUtils.isBlank(prefix) || StringUtils.isBlank(sku)) {
            return sku;
        }

        String realSku = null;
        sku = StringUtils.trim(sku);
        sku = StringUtils.upperCase(sku);

        if(StringUtils.isNotBlank(sku)) {
            realSku = PublishRedisClusterUtils.get(prefix + sku);
        }

        if(StringUtils.isNotBlank(realSku)) {
            return realSku;
        }

        return null;
    }

    /**
     *
     * @param productInfos
     * @return
     */
    public static Map<String, SkuListAndCode> getSpuToCodeMap(List<ProductInfo> productInfos) {
        if(CollectionUtils.isEmpty(productInfos)) {
            return null;
        }

        Map<String, SkuListAndCode> spuToCodeMap = new HashMap<>();

        Map<String, List<ProductInfo>> mainSkuProductsMap = productInfos.stream().collect(Collectors.groupingBy(ProductInfo::getMainSku));
        mainSkuProductsMap.forEach((key, productInfoList)->{
            if(CollectionUtils.isEmpty(productInfoList)) {
                return;
            }

            String fullpathcode = productInfoList.get(0).getFullpathcode();
            List<String> sonSkus = productInfoList.stream().map(ProductInfo::getSonSku).collect(Collectors.toList());

            List<SonSkuFewInfo> sonSkuFewInfos = new ArrayList();
            for (ProductInfo productInfo : productInfoList) {
                SonSkuFewInfo sonSkuFewInfo = new SonSkuFewInfo();
                sonSkuFewInfos.add(sonSkuFewInfo);

                sonSkuFewInfo.setSonSku(productInfo.getSonSku());
                sonSkuFewInfo.setItemStatus(productInfo.getItemStatus());
                sonSkuFewInfo.setSaleForbiddenList(productInfo.getSaleForbiddenList());
                sonSkuFewInfo.setSalesProhibitionsVos(productInfo.getSalesProhibitionsVos());
                sonSkuFewInfo.setEnTag(productInfo.getEnTag());
                sonSkuFewInfo.setSpecialTypeList(productInfo.getSpecialTypeList());
            }

            SkuListAndCode skuListAndCode = new SkuListAndCode();
            skuListAndCode.setCode(StringUtils.substringAfterLast(fullpathcode, "_"));
            skuListAndCode.setSkuList(sonSkus);
            skuListAndCode.setSonSkuFewInfos(sonSkuFewInfos);

            spuToCodeMap.put(key, skuListAndCode);
        });

        return spuToCodeMap;
    }

    /**
     * 补全产品状态禁售等信息
     * @param spuToCodeMap
     * @param productInfos
     * @return
     */
    public static Map<String, SkuListAndCode> getSpuToCodeMap(Map<String, SkuListAndCode> spuToCodeMap, List<ProductInfo> productInfos) {
        if(MapUtils.isEmpty(spuToCodeMap) || CollectionUtils.isEmpty(productInfos)) {
            return spuToCodeMap;
        }

        Map<String, List<ProductInfo>> mainSkuProductsMap = productInfos.stream().collect(Collectors.groupingBy(ProductInfo::getMainSku));

        mainSkuProductsMap.forEach((mainSku, productInfoList)->{
            if(CollectionUtils.isEmpty(productInfoList) || !spuToCodeMap.containsKey(mainSku)) {
                return;
            }

            SkuListAndCode skuListAndCode = spuToCodeMap.get(mainSku);

            List<SonSkuFewInfo> sonSkuFewInfos = new ArrayList();
            for (ProductInfo productInfo : productInfoList) {
                SonSkuFewInfo sonSkuFewInfo = new SonSkuFewInfo();
                sonSkuFewInfos.add(sonSkuFewInfo);

                sonSkuFewInfo.setSonSku(productInfo.getSonSku());
                sonSkuFewInfo.setItemStatus(productInfo.getItemStatus());
                sonSkuFewInfo.setSaleForbiddenList(productInfo.getSaleForbiddenList());
                sonSkuFewInfo.setSalesProhibitionsVos(productInfo.getSalesProhibitionsVos());
                sonSkuFewInfo.setEnTag(productInfo.getEnTag());
            }

            skuListAndCode.setSonSkuFewInfos(sonSkuFewInfos);
        });

        return spuToCodeMap;
    }

    /**
     * 补全产品状态禁售等信息 Amazon自动刊登用
     * @param spuToCodeMap
     * @param singleItemEsList
     * @return
     */
    public static Map<String, SkuListAndCode> getProductInfo(Map<String, SkuListAndCode> spuToCodeMap, List<SingleItemEs> singleItemEsList) {
        if (MapUtils.isEmpty(spuToCodeMap) || CollectionUtils.isEmpty(singleItemEsList)) {
            return spuToCodeMap;
        }

        Map<String, List<SingleItemEs>> mainSkuProductsMap = singleItemEsList.stream().collect(Collectors.groupingBy(SingleItemEs::getMainSku));

        mainSkuProductsMap.forEach((String mainSku, List<SingleItemEs> itemEsList) -> {
            if (CollectionUtils.isEmpty(itemEsList) || !spuToCodeMap.containsKey(mainSku)) {
                return;
            }

            SkuListAndCode skuListAndCode = spuToCodeMap.get(mainSku);

            List<SonSkuFewInfo> sonSkuFewInfos = new ArrayList<>();
            for (SingleItemEs singleItemEs : itemEsList) {
                SonSkuFewInfo sonSkuFewInfo = new SonSkuFewInfo();
                sonSkuFewInfos.add(sonSkuFewInfo);

                sonSkuFewInfo.setSonSku(singleItemEs.getSonSku());
                sonSkuFewInfo.setItemStatus(String.valueOf(singleItemEs.getItemStatus()));
                sonSkuFewInfo.setSaleForbiddenList(getSaleForbiddenList(singleItemEs));
                sonSkuFewInfo.setSalesProhibitionsVos(getSalesProhibitionsVos(singleItemEs));
                sonSkuFewInfo.setSpecialGoodsList(singleItemEs.getSpecialGoodsList());
                sonSkuFewInfo.setEnTag(singleItemEs.getTagCode());
                sonSkuFewInfo.setTag(singleItemEs.getTag());

                sonSkuFewInfo.setProductWeight(singleItemEs.getProductWeight());

                // 包材
                if (null != singleItemEs.getPackingMaterialsCode()) {
                    String packingMaterialsCode = PRODUCT_PACKING_MATERIAL_KEY + singleItemEs.getPackingMaterialsCode();
                    String packingmaterials = PublishRedisClusterUtils.get(packingMaterialsCode);
                    if (StringUtils.isNotEmpty(packingmaterials)) {
                        Packingmaterial packingmaterial = JSONObject.parseObject(packingmaterials, Packingmaterial.class);
                        sonSkuFewInfo.setPackingWeight(BigDecimal.valueOf(null == packingmaterial.getWeight() ? 0.0 : packingmaterial.getWeight()));
                    }
                }
                // 搭配包材
                if (null != singleItemEs.getMatchMaterialsCode()) {
                    List<String> matchMaterialsCodeList = Arrays.asList(singleItemEs.getMatchMaterialsCode().split(","));
                    Double matchWeight = 0.0;
                    for (String s : matchMaterialsCodeList) {
                        String matchMaterialsCode = PRODUCT_PACKING_MATERIAL_KEY + s;
                        String packingmaterials = PublishRedisClusterUtils.get(matchMaterialsCode);
                        if (StringUtils.isNotEmpty(packingmaterials)) {
                            Packingmaterial packingmaterial = JSONObject.parseObject(packingmaterials, Packingmaterial.class);
                            matchWeight = matchWeight + (null == packingmaterial.getWeight() ? 0.0 : packingmaterial.getWeight());
                        }
                    }
                    sonSkuFewInfo.setMatchWeight(BigDecimal.valueOf(matchWeight));
                }
                // 预估包装重量
                sonSkuFewInfo.setPackageWeight(singleItemEs.getPackageWeight());

                sonSkuFewInfo.setSaleCost(null == singleItemEs.getSaleCost() ? null : singleItemEs.getSaleCost().doubleValue());
                sonSkuFewInfo.setInSingleTime(singleItemEs.getInSingleTime().getTime());

                // 30天销量 90天销量 库存
                SingleItemExternalInfo singleItemExternalInfo = singleItemEs.getSingleItemExternalInfo();
                if (null == singleItemExternalInfo) {
                    sonSkuFewInfo.setOneSalesNum(null);
                    sonSkuFewInfo.setSevenSalesNum(null);
                    sonSkuFewInfo.setFourteenSalesNum(null);
                    sonSkuFewInfo.setThirtySalesNum(null);
                    sonSkuFewInfo.setSixtySalesNum(null);
                    sonSkuFewInfo.setNinetySalesNum(null);
                    sonSkuFewInfo.setAvailableStock(null);
                } else {
                    sonSkuFewInfo.setOneSalesNum(singleItemExternalInfo.getOneSalesNum());
                    sonSkuFewInfo.setSevenSalesNum(singleItemExternalInfo.getSevenSalesNum());
                    sonSkuFewInfo.setFourteenSalesNum(singleItemExternalInfo.getFourteenSalesNum());
                    sonSkuFewInfo.setThirtySalesNum(singleItemExternalInfo.getThirtySalesNum());
                    sonSkuFewInfo.setSixtySalesNum(singleItemExternalInfo.getSixtySalesNum());
                    sonSkuFewInfo.setNinetySalesNum(singleItemExternalInfo.getNinetySalesNum());
                    sonSkuFewInfo.setAvailableStock(singleItemExternalInfo.getAvailableStock());
                }
            }

            skuListAndCode.setSonSkuFewInfos(sonSkuFewInfos);
        });

        return spuToCodeMap;
    }

    /**
     * 获取禁售平台集合
     * @param singleItemEs
     * @return
     */
    private static List<String> getSaleForbiddenList(SingleItemEs singleItemEs) {
        List<String> saleForbidList = new ArrayList<>();
        try {
            if (StringUtils.isNotEmpty(singleItemEs.getSalesProhibition())) {
                List<Map> objectList = JSON.parseArray(singleItemEs.getSalesProhibition(), Map.class);
                for (Map<String,String> map : objectList) {
                    if (StringUtils.isNotEmpty(map.get("plat"))) {
                        saleForbidList.add(map.get("plat"));
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return saleForbidList;
    }

    /**
     * 获取禁售平台站点
     * @param singleItemEs
     * @return
     */
    public static List<SalesProhibitionsVo> getSalesProhibitionsVos(SingleItemEs singleItemEs) {
        List<SalesProhibitionsVo> salesProhibitionsVos = new ArrayList<>();
        try {
            if (StringUtils.isNotEmpty(singleItemEs.getSalesProhibition())) {
                salesProhibitionsVos = JSON.parseObject(singleItemEs.getSalesProhibition(), new TypeReference<List<SalesProhibitionsVo>>() {
                });
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return salesProhibitionsVos;
    }

    public static void setAllLongTitleAndShortTitle(ProductInfo productInfo, SingleItemOfficial singleItemOfficial) {
        if (null == productInfo || null == singleItemOfficial) {
            return;
        }
        productInfo.setLongTitle1(singleItemOfficial.getLongTitle1());
        productInfo.setLongTitle2(singleItemOfficial.getLongTitle2());
        productInfo.setLongTitle3(singleItemOfficial.getLongTitle3());
        productInfo.setLongTitle4(singleItemOfficial.getLongTitle4());
        productInfo.setLongTitle5(singleItemOfficial.getLongTitle5());
        productInfo.setLongTitle6(singleItemOfficial.getLongTitle6());
        productInfo.setLongTitle7(singleItemOfficial.getLongTitle7());
        productInfo.setLongTitle8(singleItemOfficial.getLongTitle8());
        productInfo.setShortTitle1(singleItemOfficial.getShortTitle1());
        productInfo.setShortTitle2(singleItemOfficial.getShortTitle2());
        productInfo.setShortTitle3(singleItemOfficial.getShortTitle3());
        productInfo.setShortTitle4(singleItemOfficial.getShortTitle4());
        productInfo.setShortTitle5(singleItemOfficial.getShortTitle5());
        productInfo.setShortTitle6(singleItemOfficial.getShortTitle6());
        productInfo.setShortTitle7(singleItemOfficial.getShortTitle7());
        productInfo.setShortTitle8(singleItemOfficial.getShortTitle8());
        productInfo.setShortTitle9(singleItemOfficial.getShortTitle9());
        productInfo.setShortTitle10(singleItemOfficial.getShortTitle10());
        productInfo.setShortTitle11(singleItemOfficial.getShortTitle11());
        productInfo.setShortTitle12(singleItemOfficial.getShortTitle12());

    }
}
