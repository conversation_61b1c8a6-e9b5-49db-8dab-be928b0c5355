package com.estone.erp.publish.elasticsearch.model;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.component.converter.ListStringFormatConverter;
import com.estone.erp.publish.component.converter.SkuStatusCodeConverter;
import com.estone.erp.publish.component.converter.SpecialTagConverter;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Document(indexName = "fruugo_item", type = "esFruugoListing")
public class EsFruugoItem implements Serializable {

    @Id
    @Field(type = FieldType.Keyword)
    @ExcelIgnore
    private String id;

    @ExcelIgnore
    @Field(type = FieldType.Keyword)
    private String image;

    /**
     * 店铺
     */
    @ExcelProperty(value = "店铺")
    @Field(type = FieldType.Keyword)
    private String accountNumber;

    /**
     * 产品ID（fruugoSkuId）
     */
    @ExcelProperty(value = "id")
    @Field(type = FieldType.Keyword)
    private String itemId;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    @Field(type = FieldType.Keyword)
    private String title;

    /**
     * 平台sku（merchantSkuId）
     */
    @ExcelProperty(value = "sellerSku")
    @Field(type = FieldType.Keyword)
    private String sellerSku;

    /**
     * 商人产品ID（merchantProductId）
     */
    @ExcelIgnore
    @Field(type = FieldType.Keyword)
    private String merchantProductId;

    @ExcelProperty(value = "sku")
    @Field(type = FieldType.Keyword)
    private String sku;

    /**
     * 单品状态
     */
    @ExcelProperty(value = "单品状态", converter = SkuStatusCodeConverter.class)
    @Field(type = FieldType.Keyword)
    private String itemStatus;

    /**
     * 分类
     */
    @ExcelProperty(value = "分类")
    @Field(type = FieldType.Keyword)
    private String category;

    /**
     * 负责销售
     */
    @ExcelProperty(value = "负责销售")
    @Field(type = FieldType.Keyword)
    private String salesman;

    /**
     * 禁售平台
     */
    @ExcelProperty(value = "禁售平台", converter = ListStringFormatConverter.class)
    @Field(type = FieldType.Keyword)
    private List<String> forbidChannel;

    /**
     * 禁售原因
     */
    @ExcelProperty(value = "禁售原因", converter = ListStringFormatConverter.class)
    @Field(type = FieldType.Keyword)
    private List<String> infringementObjs;

    /**
     * 禁售类型
     */
    @ExcelProperty(value = "禁售类型", converter = ListStringFormatConverter.class)
    @Field(type = FieldType.Keyword)
    private List<String> infringementTypeNames;

    /**
     * 平台禁售站点
     */
    @ExcelProperty(value = "禁售站点", converter = ListStringFormatConverter.class)
    @Field(type = FieldType.Keyword)
    private List<String> prohibitionSites;

    /**
     * 标签
     */
    @ExcelProperty(value = "产品标签")
    @Field(type = FieldType.Keyword)
    private String tag;

    /**
     * 特殊标签
     */
    @ExcelProperty(value = "特殊标签", converter = SpecialTagConverter.class)
    @Field(type = FieldType.Keyword)
    private List<Integer> specialTag;

    /**
     * 价格
     */
    @ExcelProperty(value = "价格")
    @Field(type = FieldType.Double)
    private Double price;

    /**
     * 库存
     */
    @ExcelProperty(value = "库存")
    @Field(type = FieldType.Integer)
    private Integer inventory;

    /**
     * 状态
     */
    @ExcelProperty("库存状态")
    @Field(type = FieldType.Keyword)
    private String status;

    /**
     * 最新上架时间
     */
    @ExcelProperty("最新上架时间")
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 下架时间
     */
    @ExcelProperty("下架时间")
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deletedDate;

    /**
     * 同步时间
     */
    @ExcelProperty(value = "同步时间")
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date syncDate;

    /**
     * 最后跟新时间
     */
    @ExcelProperty(value = "修改时间")
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdate;

    /**
     * 促销状态 0 没有  1，是  2，否  特别注意 0 和 2 都是(否)，1是（是）
     */
    @Field(type = FieldType.Keyword)
    private Integer promotion;

    /**
     * 新品状态
     */
    @Field(type = FieldType.Boolean)
    private Boolean newState;


    /**
     * 数据来源
     */
    @Field(type = FieldType.Integer)
    private Integer skuDataSource;

    /**
     * 组合状态
     */
    @Field(type = FieldType.Integer)
    private Integer composeStatus;

//    /**
//     * 新店铺线上ProductID，也是spu，作废不适用
//     */
//    @ExcelIgnore
//    @Field(type = FieldType.Keyword)
//    private String productId;

    /**
     * 新店铺线上ProductID，也是spu
     */
    @ExcelIgnore
    @Field(type = FieldType.Keyword)
    private String newProductId;



//    /**
//     * 新店铺SKU状态，作废不适用
//     */
//    @ExcelIgnore
//    @Field(type = FieldType.Keyword)
//    private String newAccountStatus;


    /**
     * 新店铺SKU状态
     */
    @ExcelIgnore
    @Field(type = FieldType.Keyword)
    private String newSkuStatus;


//    /**
//     * 新店铺线上分类，作废不适用
//     */
//    @ExcelIgnore
//    @Field(type = FieldType.Keyword)
//    private String newAccountCategory;


    /**
     * 新店铺线上分类
     */
    @ExcelIgnore
    @Field(type = FieldType.Keyword)
    private String newCategory;


//    /**
//     * 现店铺售价信息json，作废不适用
//     */
//    @ExcelIgnore
//    @Field(type = FieldType.Text)
//    private String newAccountPriceInfo;


    /**
     * 现店铺售价信息json
     */
    @ExcelIgnore
    @Field(type = FieldType.Keyword)
    private String newPriceInfo;

//    /**
//     * 新店铺库存信息json，作废不适用
//     */
//    @ExcelIgnore
//    @Field(type = FieldType.Text)
//    private String newAccountInventoryInfo;


    /**
     * 新店铺库存信息json
     */
    @ExcelIgnore
    @Field(type = FieldType.Keyword)
    private String newInventoryInfo;

    /**
     * 币种
     */
    @ExcelIgnore
    @Field(type = FieldType.Keyword)
    private String currency;


    /**
     * 是否含税
     */
    @ExcelIgnore
    @Field(type = FieldType.Boolean)
    private Boolean taxInclusive;

    /**
     * 错误信息
     */
    @ExcelIgnore
    @Field(type = FieldType.Keyword,index=false)
    private String errorMsg;

    /**
     * 24H销量
     */
    @ExcelProperty(value = "24H销量")
    @Field(type = FieldType.Long)
    private Long order24HCount;

    /**
     * 7天销量
     */
    @ExcelProperty(value = "7天销量")
    @Field(type = FieldType.Long)
    private Long orderLast7dCount;

    /**
     * 14天销量
     */
    @ExcelProperty(value = "14天销量")
    @Field(type = FieldType.Long)
    private Long orderLast14dCount;

    /**
     * 30天销量
     */
    @ExcelProperty(value = "30天销量")
    @Field(type = FieldType.Long)
    private Long orderLast30dCount;

    /**
     * 60天销量
     */
    @ExcelProperty(value = "60天销量")
    @Field(type = FieldType.Long)
    private Long orderLast60dCount;

    /**
     * 90天销量
     */
    @ExcelProperty(value = "90天销量")
    @Field(type = FieldType.Long)
    private Long orderLast90dCount;

    /**
     * 总销量
     */
    @ExcelProperty(value = "总销量")
    @Field(type = FieldType.Long)
    private Long orderNumTotal;

    @ExcelProperty(value = "折扣价格")
    @Field(type = FieldType.Double)
    private Double discountPrice;

    @ExcelIgnore
    @Field(type = FieldType.Boolean)
    private Boolean discountVatInclusive;
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date discountStartDate;
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date discountEndDate;
}
