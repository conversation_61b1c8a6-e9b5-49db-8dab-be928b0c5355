package com.estone.erp.publish.system.product.bean;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * 产品物流包装信息
 */
@Data
public class ProductLogisticsInfoDO {

    private String spu;
    private String sku;
    private Integer skuDataSource;
    /**
     * 净重
     */
    private Double productWeight;

    /**
     * 长
     */
    private BigDecimal length;

    /**
     * 宽
     */
    private BigDecimal wide;

    /**
     * 高
     */
    private BigDecimal height;

    /**
     * 包裹尺寸-长
     */
    private BigDecimal packLength;

    /**
     * 包裹尺寸-宽
     */
    private BigDecimal packWidth;

    /**
     * 包裹尺寸-高
     */
    private BigDecimal packHeight;


    /**
     * 产品尺寸
     */
    private String sizeRemark;

    /**
     * 包材价格
     */
    private BigDecimal packingPrice;
    /**
     * 包材重量
     */
    private BigDecimal packingWeight;

    /**
     * 搭配包材重量
     */
    private BigDecimal matchWeight;

    /**
     * 预估包装重量
     */
    private Double packageWeight;

    /**
     * 标准重量
     */
    private Double standardWeight;


    /**
     * 优先取SKU的预估包裹重量，若预估包裹重量为空，则按照取SKU的净重+包材+搭配包材+面单3g去计算
     *
     * @return
     */
    public Double getCalcWeight() {
        if (packageWeight != null) {
            return packageWeight;
        }
        Double w1 = Optional.ofNullable(productWeight).orElse(0D);
        BigDecimal w2 = Optional.ofNullable(packingWeight).orElse(BigDecimal.ZERO);
        BigDecimal w3 = Optional.ofNullable(matchWeight).orElse(BigDecimal.ZERO);
        return w1 + w2.doubleValue() + w3.doubleValue() + 3;
    }


}
