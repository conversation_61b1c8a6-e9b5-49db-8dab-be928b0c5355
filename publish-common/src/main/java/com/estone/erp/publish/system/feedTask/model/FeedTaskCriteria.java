package com.estone.erp.publish.system.feedTask.model;

import com.alibaba.excel.util.BooleanUtils;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.system.feedTask.service.impl.FeedTaskServiceImpl;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> feed_task
 * 2019-09-16 11:49:20
 */
@Data
public class FeedTaskCriteria extends FeedTask {
    private static final long serialVersionUID = 1L;

    private Date createDateStart;

    private Date createDateEnd;

    private Date finishDateStart;

    private Date finishDateEnd;

    private String accounts;

    private String feedTaskIds;

    private String associationIds;

    private String skus;

    private List<Long> ids;

    private String attribute1s;

    private String attribute2s;

    //美客多是存sku
    private String attribute3s;

    //美客多是存站点
    private String attribute4s;

    private String attribute5s;

    private List<String> attribute5List;

    //Lazada用于权限控制
    private List<String> accountList;

    // 备注模糊查询
    private String resultMsgLike;

    // 模板编号
    private String templateCodeStr;

    /**
     * 属性6模糊搜搜
     * ozon、lazada 用于规则名称
     */
    private String likeAttribute6;

    private List<String> createByList;

    private Boolean associationIdIsNotNull;
    public FeedTaskExample getExample() {
        FeedTaskExample example = new FeedTaskExample();
        FeedTaskExample.Criteria criteria = example.createCriteria();

        //用于权限控制多个店铺查询的模糊匹配，目前Lazada在用
        if (CollectionUtils.isNotEmpty(this.getAccountList())) {
            criteria.andAccountsListLike(this.getAccountList());
        }
        if (null != this.getId() && 0 != this.getId()) {
            criteria.andIdEqualTo(this.getId());
        }
        if (CollectionUtils.isNotEmpty(this.getIds())) {
            criteria.andIdIn(ids);
        }
        if (StringUtils.isNotBlank(this.getAssociationId())) {
            criteria.andAssociationIdEqualTo(this.getAssociationId());
        }
        if (StringUtils.isNotBlank(this.getAccountNumber())) {
            criteria.andAccountNumberEqualTo(this.getAccountNumber());
        }
        if (StringUtils.isNotBlank(this.getArticleNumber())) {
            criteria.andArticleNumberEqualTo(this.getArticleNumber());
        }
        if (StringUtils.isNotBlank(this.getTaskType())) {
            String[] split = this.getTaskType().split(",");
            if (split.length == 1) {
                criteria.andTaskTypeEqualTo(this.getTaskType());
            } else {
                criteria.andTaskTypeIn(Arrays.asList(split));
            }
        }
        if (StringUtils.isNotBlank(this.getPlatform())) {
            criteria.andPlatformEqualTo(this.getPlatform());
        }
        if (this.getTaskStatus() != null) {
            criteria.andTaskStatusEqualTo(this.getTaskStatus());
        }
        if (this.getResultStatus() != null) {
            criteria.andResultStatusEqualTo(this.getResultStatus());
        }
        if (StringUtils.isNotBlank(this.getResultMsg())) {
            criteria.andResultMsgEqualTo(this.getResultMsg());
        }
        if (StringUtils.isNotBlank(this.getResultMsgLike())) {
            criteria.andResultMsgLike("%" + this.getResultMsgLike() + "%");
        }
        if (StringUtils.isNotBlank(this.getCreatedBy())) {
            criteria.andCreatedByEqualTo(this.getCreatedBy());
        }
        if (CollectionUtils.isNotEmpty(this.getCreateByList())) {
            criteria.andCreatedByIn(this.getCreateByList());
        }
        if (this.getCreateDateStart() != null) {
            criteria.andCreateTimeGreaterThanOrEqualTo(this.getCreateDateStart());
        }
        if (this.getCreateDateEnd() != null) {
            criteria.andCreateTimeLessThanOrEqualTo(this.getCreateDateEnd());
        }
        if (this.getFinishDateStart() != null) {
            criteria.andFinishTimeGreaterThanOrEqualTo(this.getFinishDateStart());
        }
        if (this.getFinishDateEnd() != null) {
            criteria.andFinishTimeLessThanOrEqualTo(this.getFinishDateEnd());
        }
        if (this.getCreateTime() != null) {
            criteria.andCreateTimeEqualTo(this.getCreateTime());
        }
        if (this.getRunTime() != null) {
            criteria.andRunTimeEqualTo(this.getRunTime());
        }
        if (this.getFinishTime() != null) {
            criteria.andFinishTimeEqualTo(this.getFinishTime());
        }
        if (StringUtils.isNotBlank(this.getAttribute1())) {
            criteria.andAttribute1Like("%" + this.getAttribute1() + "%");
        }
        if (StringUtils.isNotBlank(this.getAttribute2())) {
            criteria.andAttribute2EqualTo(this.getAttribute2());
        }
        if (StringUtils.isNotBlank(this.getAttribute3())) {
            criteria.andAttribute3EqualTo(this.getAttribute3());
        }
        if (StringUtils.isNotBlank(this.getAttribute4())) {
            criteria.andAttribute4EqualTo(this.getAttribute4());
        }
        if (StringUtils.isNotBlank(this.getAttribute5())) {
            criteria.andAttribute5EqualTo(this.getAttribute5());
        }
        if (StringUtils.isNotBlank(this.getAttribute11())) {
            criteria.andAttribute11EqualTo(this.getAttribute11());
        }
        if (StringUtils.isNotBlank(this.getAttribute12())) {
            criteria.andAttribute12EqualTo(this.getAttribute12());
        }
        if (StringUtils.isNotBlank(this.getAccounts())) {
            criteria.andAccountNumberIn(CommonUtils.splitList(this.getAccounts(), ","));
        }
        if (StringUtils.isNotBlank(this.getAssociationIds())) {
            criteria.andAssociationIdIn(CommonUtils.splitList(this.getAssociationIds(), ","));
        }
        if (StringUtils.isNotBlank(this.getFeedTaskIds())) {
            criteria.andIdIn(com.estone.erp.common.util.CommonUtils.splitLongList(this.getFeedTaskIds(), ","));
        }
        if (StringUtils.isNotBlank(this.getSkus())) {
            criteria.andArticleNumberIn(CommonUtils.splitList(this.getSkus(), ","));
        }
        if (StringUtils.isNotBlank((this.getAttribute1s()))) {
            criteria.andAttribute1sIn(CommonUtils.splitList(this.getAttribute1s(), ","));
        }
        if (StringUtils.isNotBlank((this.getAttribute2s()))) {
            criteria.andAttribute2sIn(CommonUtils.splitList(this.getAttribute2s(), ","));
        }

        if (StringUtils.isNotBlank((this.getAttribute3s()))) {
            criteria.andAttribute3sIn(CommonUtils.splitList(this.getAttribute3s(), ","));
        }
        if (StringUtils.isNotBlank((this.getAttribute4s()))) {
            criteria.andAttribute4sIn(CommonUtils.splitList(this.getAttribute4s(), ","));
        }
        if (StringUtils.isNotBlank((this.getAttribute5s()))) {
            criteria.andAttribute5In(CommonUtils.splitList(this.getAttribute5s(), ","));
        }
        if (CollectionUtils.isNotEmpty(this.getAttribute5List())) {
            criteria.andAttribute5In(this.getAttribute5List());
        }
        if (StringUtils.isNotBlank(this.getTemplateCodeStr())) {
            String[] templateCode = this.getTemplateCodeStr().split(",");
            criteria.andAttribute1In(Arrays.asList(templateCode));
        }
        if (StringUtils.isNotBlank(this.getLikeAttribute6())) {
            criteria.andAttribute6Like("%" + this.getLikeAttribute6() + "%");
        }
        if (BooleanUtils.isTrue(this.getAssociationIdIsNotNull())) {
            criteria.andAssociationIdIsNotBlank();
        }

        String feedTableIndex = FeedTaskServiceImpl.PLATFORM_FEED_SUFFIX_MAP.get(this.getPlatform());
        if (StringUtils.isNotBlank(feedTableIndex)) {
            example.setTableIndex(feedTableIndex);
        }
        return example;
    }

//    public Date getCreateDateStart() {
//        return createDateStart;
//    }
//
//    public void setCreateDateStart(Date createDateStart) {
//        this.createDateStart = createDateStart;
//    }
//
//    public Date getCreateDateEnd() {
//        return createDateEnd;
//    }
//
//    public void setCreateDateEnd(Date createDateEnd) {
//        this.createDateEnd = createDateEnd;
//    }
//
//    public Date getFinishDateStart() {
//        return finishDateStart;
//    }
//
//    public void setFinishDateStart(Date finishDateStart) {
//        this.finishDateStart = finishDateStart;
//    }
//
//    public Date getFinishDateEnd() {
//        return finishDateEnd;
//    }
//
//    public void setFinishDateEnd(Date finishDateEnd) {
//        this.finishDateEnd = finishDateEnd;
//    }
//
//    public String getAccounts() {
//        return accounts;
//    }
//
//    public void setAccounts(String accounts) {
//        this.accounts = accounts;
//    }
//
//    public String getFeedTaskIds() {
//        return feedTaskIds;
//    }
//
//    public void setFeedTaskIds(String feedTaskIds) {
//        this.feedTaskIds = feedTaskIds;
//    }
//
//    public String getAssociationIds() {
//        return associationIds;
//    }
//
//    public void setAssociationIds(String associationIds) {
//        this.associationIds = associationIds;
//    }
//
//    public String getSkus() {
//        return skus;
//    }
//
//    public void setSkus(String skus) {
//        this.skus = skus;
//    }
//
//    public List<Integer> getIds() {
//        return ids;
//    }
//
//    public void setIds(List<Integer> ids) {
//        this.ids = ids;
//    }


}