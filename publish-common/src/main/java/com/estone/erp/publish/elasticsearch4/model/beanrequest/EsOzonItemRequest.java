package com.estone.erp.publish.elasticsearch4.model.beanrequest;

import lombok.Data;

import java.util.List;


@Data
public class EsOzonItemRequest {

    /**
     * 排序
     */
    private String sequence;
    private String orderBy;

    private Integer pageSize = 200;
    private Integer pageIndex = 0;

    private String [] fields = {
            "id","mainImage","accountNumber","productId","sellerSku","name","categoryId","categoryPath","statusCode","sku","spu","weight","minPriceNumberUpdateDate"
    };

    // 列表页面默认查询字段
    private String [] listingPageFields = {};

    /**
     * 店铺
     */
    private String accountNumber;
    private List<String> accountNumbers;

    // id
    private String id;
    private String gtId;
    private String idStr;

    /**
     * 大于id
     */
    private String greaterThanId;

    private List<String> ids;

    /**
     * sellerSku
     */
    private String sellerSku;
    private List<String> sellerSkus;
    /**
     * 状态
     */
    private String statusCode;
    private List<String> statusCodes;

    /**
     * sku
     */
    private String sku;
    private List<String> skus;
    private String skuLike;

    /**
     * spu
     */
    private String spu;
    private List<String> spus;

    /**
     * 商品编码
     */
    private Long productId;
    private List<Long> productIds;
    private Long gteProductId;
    /**
     * 标题
     */
    private String name;

    /**
     * 单品状态
     */
    private String skuStatus;
    private List<String> skuStatusList;
    private List<String> excludeSkuStatus;

    /**
     * 分类
     */
    private Integer categoryId;
    private List<Integer> categoryIds;

    /**
     * 总库存范围
     */
    private Integer fromStock;
    private Integer toStock;

    /**
     * 库存大小是否为左闭右开
     */
    private Boolean isStockLeftClosedRightOpen;
    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 仓库name
     */
    private String warehouseName;

    /**
     * 指定仓库库存范围
     */
    private Integer fromWarehouseStock;
    private Integer toWarehouseStock;

    /**
     * 价格范围
     */
    private Double fromPrice;
    private Double toPrice;

    /**
     * 价格范围
     */
    private Double fromMinPrice;
    private Double toMinPrice;

    /**
     * 价格范围
     */
    private Double fromOldPrice;
    private Double toOldPrice;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 重量范围
     */
    private Double fromWeight;
    private Double toWeight;


    /**
     * 销售账号
     */
    private List<String> saleAccount;

    /**
     * 销售组长
     */
    private List<String> saleAccountLeader;

    /**
     * 销售主管
     */
    private List<String> saleAccountManager;


    /**
     * 禁售类型
     */
    private List<String> infringementTypeNames;

    /**
     * 禁售原因
     */
    private List<String> infringementObjs;

    /**
     * 禁售平台
     */
    private List<String> forbidChannel;

    /**
     * 禁售站点
     */
    private List<String> prohibitionSites;

    /**
     * 产品标签
     */
    private List<String> tagCodes;


    /**
     * 特殊标签
     */
    private List<Integer> specialGoodsCode;
    private List<Integer> excludeSpecialTag;

    /**
     * 是否新品
     */
    private Boolean newState;

    /**
     * 是否促销
     */
    private Boolean isPromotion;


    /**
     * 数据来源
     */
    private Integer skuDataSource;

    /**
     * 组合状态
     */
    private Integer composeStatus;

    /**
     * 链接标签
     */
    private Integer linkTag;

    /**
     * 是否包含ozon线上物流禁运
     */
    private Boolean logisticsEmbargo;

    /**
     * 排除链接标签
     */
    private List<Integer> excludeLinkTag;

    /**
     * 创建时间区间
     */
    private String fromCreateDate;
    private String toCreateDate;
    private String createDateLessThan;


    /**
     * 修改时间区间
     */
    private String fromUpdateDate;
    private String toUpdateDate;


    /**
     * 同步时间区间
     */
    private String fromSyncDate;
    private String toSyncDate;
    private String ltToSyncDate;

    /**
     * 同步产品时间区间
     */
    private String fromSyncProductDate;
    private String toSyncProductDate;

    /**
     * 库存同步时间小于
     */
    private String stockSyncDateLessThan;

    /**
     * 重量差异大于
     */
    private Double gteWeightDifference;

    /**
     * 导出字段
     */
    private List<String> downloadFiles;

    /**
     * 是否存在评分数据
     */
    private Boolean existRatingField;


    /**
     * 同步评分时间区间
     */
    private String ratingSyncDateLessThan;



    private String downloadType;

    /**
     * 7天销量
     */
    private Boolean isMarketing;
    /**
     * 开始销量是0的时候需要处理
     */
    private Integer orderLast7dCountFrom;
    private Integer orderLast7dCountTo;

    /**
     * 14天销量
     */
    private Integer orderLast14dCountFrom;
    private Integer orderLast14dCountTo;

    /**
     * 30天销量
     */
    private Integer orderLast30dCountFrom;
    private Integer orderLast30dCountTo;

    /**
     * 90天销量
     */
    private Integer orderLast90dCountFrom;
    private Integer orderLast90dCountTo;

    /**
     * 总销量
     */
    private Integer orderNumTotalFrom;
    private Integer orderNumTotalTo;

    /**
     * 利率差值绝对值
     */
    private Double grossProfitRateDifferenceAbsoluteFrom;
    private Double grossProfitRateDifferenceAbsoluteTo;

    /**
     * 利率
     */
    private Double grossProfitRateFrom;
    private Double grossProfitRateTo;

    /**
     * 毛利
     */
    private Double grossProfitFrom;
    private Double grossProfitTo;

    /**
     * 设置 ozonSku
     */
    private List<Long> ozonSkuList;

    /**
     * 是否报名
     */
    private Boolean registeredForEvent;

    /**
     * 同步时间小于这个时间的
     */
    private String ltRegisteredForEventDate;

    /**
     * 获取在线，或者在线字段为null的
     */
    private Boolean onlineOrIsNot;

    /**
     * 获取最小价格字段为null的
     */
    private Boolean minPriceNumberIsNull;

    /**
     * 最小价格更新时间
     */
    private String minPriceNumberUpdateDateFrom;

    /**
     * 最小价格更新时间
     */
    private String minPriceNumberUpdateDateTo;

    /**
     * 是否在线
     */
    private Boolean isOnline;

    /**
     * 错误信息
     */
    private String likeErrorTexts;
}
