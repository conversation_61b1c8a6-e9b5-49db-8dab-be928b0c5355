package com.estone.erp.publish.elasticsearch.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.usermgt_n.EmployeeInfo;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.SaleSuperiorReidsUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.AuthorizedAccountRequest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.model.beanresponse.SaleAccountListResponse;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.constant.ErpUsermgtNRedisConStant;
import com.estone.erp.publish.system.newUsermgt.constant.RoleConstant;
import com.estone.erp.publish.system.newUsermgt.model.LeaderInfo;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.estone.erp.publish.system.newUsermgt.model.SuperEmployeeInfo;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Auther yucm
 * @Date 2021/7/21
 */
@Slf4j
public class EsAccountUtils {

    private static final Cache<String, SuperEmployeeInfo> ACCOUNT_SUPER_EMPLOYEE = CacheBuilder.newBuilder()
            .expireAfterAccess(1, TimeUnit.HOURS).build();

    /**
     * 前端页面调用 支持用户列表 平台账号列表
     *
     * @param request
     * @return
     */
    public static ApiResult<SaleAccountListResponse> getAuthorAccountList(EsSaleAccountRequest request) {
        SaleAccountListResponse account = new SaleAccountListResponse();

        List<Integer> employeeIds = null;

        // 需要用户列表 或者不是超级管理员 需要查询下级用户
        String saleChannel = request.getSaleChannel();
        ApiResult<Boolean> superAdminOrEquivalentResult = NewUsermgtUtils.isSuperAdminOrEquivalent(saleChannel);
        if (!superAdminOrEquivalentResult.isSuccess()) {
            return ApiResult.newError(superAdminOrEquivalentResult.getErrorMsg());
        }

        Boolean superAdminOrEquivalent = superAdminOrEquivalentResult.getResult();
        // 获取销售账户列表
        ApiResult<SaleAccountListResponse> listApiResult = getSaleAccountListResponseApiResult(request, superAdminOrEquivalent, saleChannel, employeeIds, account);
        if (listApiResult != null) {
            return listApiResult;
        }
        ;
        return ApiResult.newSuccess(account);
    }

    /**
     * 获取销售账户列表
     *
     * @param request
     * @param superAdminOrEquivalent
     * @param saleChannel
     * @param employeeIds
     * @param account
     * @return
     */
    private static ApiResult<SaleAccountListResponse> getSaleAccountListResponseApiResult(EsSaleAccountRequest request, Boolean superAdminOrEquivalent, String saleChannel, List<Integer> employeeIds, SaleAccountListResponse account) {
        // 获取当前登录用户信息
        String employeeNo = WebUtils.getUserName();
        if (StringUtils.isBlank(employeeNo)) {
            return ApiResult.newError("查询不到员工信息！");
        }
        ApiResult<NewUser> newUserApiResult = NewUsermgtUtils.getUserByNo(employeeNo);
        if (!newUserApiResult.isSuccess()) {
            return ApiResult.newError(newUserApiResult.getErrorMsg());
        }

        // 判断是否数据支持部门(数据分析组可见全部数据)
        if (ObjectUtils.isNotEmpty(request.getIsDataSupportDepartment()) && request.getIsDataSupportDepartment()) {
            // 判断是否是数据支持部
            if (newUserApiResult.getResult().getRsRoleNames().contains(RoleConstant.DATA_SUPPORT_DEPARTMENT)) {
                superAdminOrEquivalent = Boolean.TRUE;
            }
        }

        // 判断是否特殊处理陈凤松权限,182537为陈凤松的账号
        if (PermissionsHelper.judgeIsSpecialHandleCfsAuth(request.getIsNotSpecialHandleCfsAuth(), saleChannel, employeeNo)) {
            ApiResult<NewUser> newUserResult = NewUsermgtUtils.getUserByNo(employeeNo);
            if (!newUserResult.isSuccess()) {
                return ApiResult.newError(newUserResult.getErrorMsg());
            }
            employeeIds = Collections.singletonList(newUserResult.getResult().getEmployeeId());
        }
        // 判断是否只需要返回数据支持部门的用户
        else if (Objects.nonNull(request.getIsDataSupportDepartmentNeedUsers()) && request.getIsDataSupportDepartmentNeedUsers() && newUserApiResult.getResult().getRsRoleNames().contains(RoleConstant.DATA_SUPPORT_DEPARTMENT)) {
            ApiResult<List<NewUser>> userRoleRes = NewUsermgtUtils.listUserByRoleNames(List.of(RoleConstant.DATA_SUPPORT_DEPARTMENT));
            if (!userRoleRes.isSuccess()) {
                return ApiResult.newError(userRoleRes.getErrorMsg());
            }

            // 包含自己
            List<NewUser> newUserList = userRoleRes.getResult();
            List<String> employeeNos = newUserList.stream().map(NewUser::getEmployeeNo).collect(Collectors.toList());
            if (!employeeNos.contains(employeeNo)) {
                newUserList.add(newUserApiResult.getResult());
            }
            account.setNewUsers(newUserList);
        } else {
            // 是超级管理员且需要用户列表
            if (BooleanUtils.isNotTrue(superAdminOrEquivalent) || BooleanUtils.isTrue(request.getIsNeedUsers())) {
                ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils.listTeamLeaderByEmployeeNo(saleChannel);
                if (!listApiResult.isSuccess()) {
                    return ApiResult.newError(listApiResult.getErrorMsg());
                }

                List<NewUser> newUsers = listApiResult.getResult();
                employeeIds = newUsers.stream().map(NewUser::getEmployeeId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(employeeIds)) {
                    return ApiResult.newError("查询不到员工信息！");
                }
                account.setNewUsers(newUsers);
            }
        }

        // 获取平台账号列表
        Boolean isNeedAccounts = request.getIsNeedAccounts();
        if (BooleanUtils.isTrue(isNeedAccounts)) {
            if (BooleanUtils.isNotTrue(superAdminOrEquivalent) || PermissionsHelper.judgeIsSpecialHandleCfsAuth(request.getIsNotSpecialHandleCfsAuth(), saleChannel, employeeNo)) {
                request.setEmployeeIds(employeeIds);
            }
            // shopee需查子账号
            if (StringUtils.isNotBlank(saleChannel) && saleChannel.equals(SaleChannel.CHANNEL_SHOPEE)) {
                String[] fields = {"accountNumber", "colStr5", "accountStatus", "colBool2"};
                SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);
                List<SaleAccount> saleAccountList = saleAccountService.getAccountInfoList(request, fields);
                if (request.getIsNormalAccount()) {
                    saleAccountList = saleAccountList.stream()
                            .filter(saleAccount -> SaleAccountStastusEnum.NORMAL.getCode().equals(saleAccount.getAccountStatus()) && BooleanUtils.isFalse(saleAccount.getColBool2()))
                            .collect(Collectors.toList());
                }
                List<String> accountNumberList = saleAccountList.stream().map(SaleAccount::getAccountNumber).distinct().collect(Collectors.toList());
                List<String> sonAccountNumberList = saleAccountList.stream().filter(o -> StringUtils.isNotBlank(o.getColStr5()))
                        .map(SaleAccount::getColStr5).distinct().collect(Collectors.toList());
                account.setAccountNames(accountNumberList);
                account.setSonAccounts(sonAccountNumberList);
            } else {
                List<String> accounts = getAccountListByEs(request);
                account.setAccountNames(accounts);
            }
        }
        return null;
    }


    /**
     * 前端页面调用 支持用户列表 平台账号列表
     * 前端入参传入isOnlySelf=true才会被调用
     *
     * @param request
     * @return
     */
    public static ApiResult<SaleAccountListResponse> getAuthorAccountListOnlySelf(EsSaleAccountRequest request) {
        SaleAccountListResponse account = new SaleAccountListResponse();

        List<Integer> employeeIds = new ArrayList<>();

        // 需要用户列表 或者不是超级管理员 需要查询下级用户
        String saleChannel = request.getSaleChannel();
        Boolean isNeedUsers = request.getIsNeedUsers();
        //判断是否超管
        ApiResult<Boolean> superAdmin = NewUsermgtUtils.isSuperAdmin();
        if (!superAdmin.isSuccess()) {
            return ApiResult.newError(superAdmin.getErrorMsg());
        }

        Boolean isAdmin = superAdmin.getResult();
        // 获取销售帐户列表
        ApiResult<SaleAccountListResponse> newError = getSaleAccountListResponseApiResult(request, isAdmin, employeeIds, account, saleChannel);
        if (newError != null) {
            return newError;
        }
        return ApiResult.newSuccess(account);
    }

    /**
     * 获取销售帐户列表
     *
     * @param request
     * @param isAdmin
     * @param employeeIds
     * @param account
     * @param saleChannel
     * @return
     */
    private static ApiResult<SaleAccountListResponse> getSaleAccountListResponseApiResult(EsSaleAccountRequest request, Boolean isAdmin, List<Integer> employeeIds, SaleAccountListResponse account, String saleChannel) {
        // 获取当前登录用户信息
        String employeeNo = WebUtils.getUserName();
        if (StringUtils.isBlank(employeeNo)) {
            return ApiResult.newError("查询不到员工信息！");
        }
        ApiResult<NewUser> newUserApiResult = NewUsermgtUtils.getUserByNo(employeeNo);
        if (!newUserApiResult.isSuccess()) {
            return ApiResult.newError(newUserApiResult.getErrorMsg());
        }

        // 判断是否数据支持部门(数据分析组可见全部数据)
        if (ObjectUtils.isNotEmpty(request.getIsDataSupportDepartment()) && request.getIsDataSupportDepartment()) {
            // 判断是否是数据支持部
            if (newUserApiResult.getResult().getRsRoleNames().contains(RoleConstant.DATA_SUPPORT_DEPARTMENT)) {
                isAdmin = Boolean.TRUE;
            }
        }

        // 判断是否特殊处理陈凤松权限,182537为陈凤松的账号
        if (PermissionsHelper.judgeIsSpecialHandleCfsAuth(request.getIsNotSpecialHandleCfsAuth(), saleChannel, employeeNo)) {
            ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils.subordinateTeamLeaderByEmployeeNo(SaleChannelEnum.ALIEXPRESS.getChannelName(), employeeNo);
            if (!listApiResult.isSuccess()) {
                return ApiResult.newError(listApiResult.getErrorMsg());
            }
            Integer employeeId = listApiResult.getResult().get(0).getEmployeeId();
            employeeIds = Collections.singletonList(employeeId);
        }
        // 判断是否只需要返回数据支持部门的用户
        else if (Objects.nonNull(request.getIsDataSupportDepartmentNeedUsers()) && request.getIsDataSupportDepartmentNeedUsers()) {
            ApiResult<List<NewUser>> userRoleRes = NewUsermgtUtils.listUserByRoleNames(List.of(RoleConstant.DATA_SUPPORT_DEPARTMENT));
            if (!userRoleRes.isSuccess()) {
                return ApiResult.newError(userRoleRes.getErrorMsg());
            }
            account.setNewUsers(userRoleRes.getResult());
        } else {
            //如果不是超管就查自己的
            if (BooleanUtils.isNotTrue(isAdmin)) {
                NewUser newUser = newUserApiResult.getResult();
                if (null != newUser && null != newUser.getEmployeeId()) {
                    Integer employeeId = newUser.getEmployeeId();
                    request.setEmployeeIds(Arrays.asList(employeeId));
                    employeeIds.add(employeeId);
                    account.setNewUsers(Arrays.asList(newUser));
                }
            } else {
                ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils.listTeamLeaderByEmployeeNo(saleChannel);
                if (!listApiResult.isSuccess()) {
                    return ApiResult.newError(listApiResult.getErrorMsg());
                }

                List<NewUser> newUsers = listApiResult.getResult();
                employeeIds = newUsers.stream().map(NewUser::getEmployeeId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(employeeIds)) {
                    return ApiResult.newError("查询不到员工信息！");
                }
                account.setNewUsers(newUsers);
            }
        }

        // 获取平台账号列表
        Boolean isNeedAccounts = request.getIsNeedAccounts();
        if (BooleanUtils.isTrue(isNeedAccounts)) {
            if (BooleanUtils.isNotTrue(isAdmin) || PermissionsHelper.judgeIsSpecialHandleCfsAuth(request.getIsNotSpecialHandleCfsAuth(), saleChannel, employeeNo)) {
                request.setEmployeeIds(employeeIds);
            }
            // shopee需查子账号
            if (StringUtils.isNotBlank(saleChannel) && saleChannel.equals(SaleChannel.CHANNEL_SHOPEE)) {
                String[] fields = {"accountNumber", "colStr5"};
                SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);
                List<SaleAccount> saleAccountList = saleAccountService.getAccountInfoList(request, fields);
                if (request.getIsNormalAccount()) {
                    saleAccountList = saleAccountList.stream().filter(saleAccount -> SaleAccountStastusEnum.NORMAL.getCode().equals(saleAccount.getAccountStatus())).collect(Collectors.toList());
                }
                List<String> accountNumberList = saleAccountList.stream().map(SaleAccount::getAccountNumber).distinct().collect(Collectors.toList());
                List<String> sonAccountNumberList = saleAccountList.stream().filter(o -> StringUtils.isNotBlank(o.getColStr5()))
                        .map(SaleAccount::getColStr5).distinct().collect(Collectors.toList());
                account.setAccountNames(accountNumberList);
                account.setSonAccounts(sonAccountNumberList);
            } else {
                List<String> accounts = getAccountListByEs(request);
                account.setAccountNames(accounts);
            }
        }
        return null;
    }


    /**
     * 获取授权平台账号列表 返回空列表是否需调用方处理异常
     *
     * @param saleChannel 平台
     * @param isOnlySelf  只查自己账号 false 查自己和下级 ; null或者true 只查自己
     * @return
     */
    public static ApiResult<List<String>> getAuthorAccountList(String saleChannel, Boolean isOnlySelf) {
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(saleChannel);

        // false 查自己和下级 ; null或者true 只查自己
        if (BooleanUtils.isFalse(isOnlySelf)) {
            ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils.listTeamLeaderByEmployeeNo(saleChannel);
            if (!listApiResult.isSuccess()) {
                return ApiResult.newError(listApiResult.getErrorMsg());
            }

            List<NewUser> newUsers = listApiResult.getResult();
            List<Integer> employeeIds = newUsers.stream().map(NewUser::getEmployeeId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(employeeIds)) {
                return ApiResult.newError("查询不到员工信息！");
            }
            request.setEmployeeIds(employeeIds);
        } else {
            String employeeNo = WebUtils.getUserName();
            //队列执行存了操作人
            if (StringUtils.isBlank(employeeNo)) {
                employeeNo = DataContextHolder.getUsername();
            }
            if (StringUtils.isBlank(employeeNo)) {
                return ApiResult.newError("查询不到员工信息！");
            }

            ApiResult<NewUser> newUserApiResult = NewUsermgtUtils.getUserByNo(employeeNo);
            if (!newUserApiResult.isSuccess()) {
                return ApiResult.newError(newUserApiResult.getErrorMsg());
            }

            NewUser newUser = newUserApiResult.getResult();
            if (null != newUser && null != newUser.getEmployeeId()) {
                Integer employeeId = newUser.getEmployeeId();
                request.setEmployeeIds(Arrays.asList(employeeId));
            }
        }

        List<String> accountListByEs = null;
        // shopee需查子账号
        if (StringUtils.isNotBlank(saleChannel) && saleChannel.equals(SaleChannel.CHANNEL_SHOPEE)) {
            String[] fields = {"accountNumber", "colStr5"};
            SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);
            List<SaleAccount> saleAccountList = saleAccountService.getAccountInfoList(request, fields);
            List<String> accountNumberList = saleAccountList.stream().map(SaleAccount::getAccountNumber).distinct().collect(Collectors.toList());
            List<String> sonAccountNumberList = saleAccountList.stream().filter(o -> StringUtils.isNotBlank(o.getColStr5()))
                    .map(SaleAccount::getColStr5).distinct().collect(Collectors.toList());
            accountListByEs = accountNumberList;
            accountListByEs.addAll(sonAccountNumberList);
        } else {
            accountListByEs = getAccountListByEs(request);
        }

        return ApiResult.newSuccess(accountListByEs);
    }

    /**
     * Amazon获取授权平台账号列表 （Amazon在线列表查全部账号，其他页面查正常账号）
     *
     * @param saleChannel 平台
     * @param isOnlySelf  只查自己账号 false 查自己和下级 ; null或者true 只查自己
     * @return
     */
    public static ApiResult<List<String>> getAmazonAuthorAccountList(String saleChannel, Boolean isOnlySelf) {
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(saleChannel);
        request.setIsNeedAllAccounts(true);

        // false 查自己和下级 ; null或者true 只查自己
        if (BooleanUtils.isFalse(isOnlySelf)) {
            ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils.listTeamLeaderByEmployeeNo(saleChannel);
            if (!listApiResult.isSuccess()) {
                return ApiResult.newError(listApiResult.getErrorMsg());
            }

            List<NewUser> newUsers = listApiResult.getResult();
            List<Integer> employeeIds = newUsers.stream().map(NewUser::getEmployeeId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(employeeIds)) {
                return ApiResult.newError("查询不到员工信息！");
            }
            request.setEmployeeIds(employeeIds);
        } else {
            String employeeNo = WebUtils.getUserName();
            //队列执行存了操作人
            if (StringUtils.isBlank(employeeNo)) {
                employeeNo = DataContextHolder.getUsername();
            }
            if (StringUtils.isBlank(employeeNo)) {
                return ApiResult.newError("查询不到员工信息！");
            }

            ApiResult<NewUser> newUserApiResult = NewUsermgtUtils.getUserByNo(employeeNo);
            if (!newUserApiResult.isSuccess()) {
                return ApiResult.newError(newUserApiResult.getErrorMsg());
            }

            NewUser newUser = newUserApiResult.getResult();
            if (null != newUser && null != newUser.getEmployeeId()) {
                Integer employeeId = newUser.getEmployeeId();
                request.setEmployeeIds(Arrays.asList(employeeId));
            }
        }

        List<String> accountListByEs = getAccountListByEs(request);
        return ApiResult.newSuccess(accountListByEs);
    }

    /**
     * 匹配有权限
     * 的账号
     *
     * @param authorizedAccountRequest
     * @return
     */
    public static List<String> matchingAuthorizedAccounts(AuthorizedAccountRequest authorizedAccountRequest) {
        if (authorizedAccountRequest == null || StringUtils.isBlank(authorizedAccountRequest.getSaleChannel())) {
            throw new RuntimeException("平台必须输入");
        }

        List<String> authorAccountList = new ArrayList<>();

        String saleChannel = authorizedAccountRequest.getSaleChannel();
        List<String> salemanagerList = authorizedAccountRequest.getSalemanagerList();
        List<String> salemanagerLeaderList = authorizedAccountRequest.getSalemanagerLeaderList();
        List<String> salesSupervisorList = authorizedAccountRequest.getSalesSupervisorList();
        List<String> accountNumbers = authorizedAccountRequest.getAccountNumbers();

        // 判断单前登录人是否超级管理员或者主管（是否有单前平台的所有账号权限）
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(saleChannel);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
        }

        // 是否选择销售
        Boolean isSelctSale = (CollectionUtils.isNotEmpty(salemanagerList) || CollectionUtils.isNotEmpty(salemanagerLeaderList) || CollectionUtils.isNotEmpty(salesSupervisorList)) ? true : false;

        if (CollectionUtils.isEmpty(accountNumbers) && !superAdminOrEquivalent.getResult()) {
//            if (isSelctSale) {
//                isOnlySelf = false;
//            }
//            if (SaleChannel.CHANNEL_OZON.equals(authorizedAccountRequest.getSaleChannel()) && BooleanUtils.isTrue(NewUsermgtUtils.containRole(SaleChannel.CHANNEL_OZON, "主管"))) {
//                isOnlySelf = false;
//            }
//            if (SaleChannel.CHANNEL_TIKTOK.equals(authorizedAccountRequest.getSaleChannel()) && BooleanUtils.isTrue(NewUsermgtUtils.containRole(SaleChannel.CHANNEL_TIKTOK, "主管"))) {
//                isOnlySelf = false;
//            }

            ApiResult<List<String>> authorAccountListResult = EsAccountUtils.getAuthorAccountList(saleChannel, false);
            if (!authorAccountListResult.isSuccess()) {
                throw new RuntimeException(authorAccountListResult.getErrorMsg());
            }
            //查询销售对应店铺列表
            if (CollectionUtils.isEmpty(authorAccountListResult.getResult())) {
                throw new RuntimeException("未查询到可用店铺列表！");
            }
            authorAccountList.addAll(authorAccountListResult.getResult());
        }

        if (isSelctSale) {
            List<String> saleIds = NewUsermgtUtils.matchingAuthorizedSaleIds(authorizedAccountRequest);
            if (CollectionUtils.isEmpty(saleIds)) {
                throw new RuntimeException("选择的销售，组长，主管未查询到用户");
            }

            EsSaleAccountRequest esSaleAccountRequest = new EsSaleAccountRequest();
            esSaleAccountRequest.setSaleIds(saleIds);
            esSaleAccountRequest.setSaleChannel(saleChannel);
            SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);
            List<String> saleList = saleAccountService.getAccountList(esSaleAccountRequest);
            if (CollectionUtils.isEmpty(saleList)) {
                throw new RuntimeException("选择的销售没有账号权限！");
            }

            if (CollectionUtils.isEmpty(authorAccountList)) {
                authorAccountList = saleList;
            } else {
                authorAccountList.retainAll(saleList);
            }
            if (CollectionUtils.isEmpty(authorAccountList)) {
                throw new RuntimeException("选择的销售没有权限！");
            }
        }

        if (CollectionUtils.isEmpty(accountNumbers)) {
            accountNumbers = authorAccountList;
        } else if (CollectionUtils.isNotEmpty(authorAccountList)) {
            accountNumbers.retainAll(authorAccountList);
            if (CollectionUtils.isEmpty(accountNumbers)) {
                throw new RuntimeException("选择的店铺或销售没有权限！");
            }
        }
        if (CollectionUtils.isEmpty(accountNumbers) && !superAdminOrEquivalent.getResult()) {
            throw new RuntimeException("选择的店铺或销售没有权限！");
        }

        return accountNumbers;
    }

    /**
     * 获取当前登录人 有权限账号
     *
     * @param request
     * @return
     */
    public static List<String> getAccountListByEs(EsSaleAccountRequest request) {
        if (null == request) {
            return new ArrayList<>();
        }

        String saleChannel = request.getSaleChannel();
        request.setSaleChannel(saleChannel);

        /**
         * 亚马逊只查正常状态（对应订单正常 疑似冻结）
         */
        Boolean isNeedAllAccounts = request.getIsNeedAllAccounts();
        if (StringUtils.isNotBlank(saleChannel) && saleChannel.equals(SaleChannel.CHANNEL_AMAZON) && (isNeedAllAccounts == null || !isNeedAllAccounts)) {
            List<String> statusList = new ArrayList<>();
            statusList.add(SaleAccountStastusEnum.NORMAL.getCode());
            statusList.add(SaleAccountStastusEnum.EXCEPTION.getCode());
            request.setAccountStatusList(statusList);
        }

        SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);
        List<String> accountNumberList = saleAccountService.getAccountList(request);
        return accountNumberList;
    }

    /**
     * 获取对应平台所有正常账号
     *
     * @param saleChannel
     * @return
     */
    public static List<String> getPlatformNormaLAccountListByEs(String saleChannel) {
        if (StringUtils.isBlank(saleChannel)) {
            return Collections.emptyList();
        }

        List<String> accountStatusList = new ArrayList<>();
        accountStatusList.add(SaleAccountStastusEnum.NORMAL.getCode());

        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(saleChannel);
        request.setAccountStatusList(accountStatusList);

        SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);
        List<String> accountNumberList = saleAccountService.getAccountList(request);
        return accountNumberList;
    }

    /**
     * 获取销售店铺对应关系
     *
     * @param request
     * @return
     */
    public static List<String> getSaleIdAndAccountByEs(EsSaleAccountRequest request) {
        if (null == request) {
            return new ArrayList<>();
        }

        String saleChannel = request.getSaleChannel();
        request.setSaleChannel(saleChannel);

        SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);
        return saleAccountService.getSaleIdAndAccount(request);
    }

    /**
     * 获取店铺对应销售id
     *
     * @param request
     * @return
     */
    public static Map<String, List<String>> getAccountAndSaleIdMapByEs(EsSaleAccountRequest request) {
        if (null == request) {
            return new HashMap<>(16);
        }

        String saleChannel = request.getSaleChannel();
        request.setSaleChannel(saleChannel);

        SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);
        return saleAccountService.getAccountAndSaleIdMap(request);
    }

    /**
     * 获取当前用户相关店铺具体信息- 自己或组员 负责的店铺
     *
     * @param saleChannel
     * @return
     */
    public static ApiResult<List<SaleAccount>> getAccountMerchantByEmployee(String saleChannel, boolean isOnlySelf, Boolean overseasBusiness) {

        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(saleChannel);
        Boolean dataSupportDepartment = NewUsermgtUtils.isDataSupportDepartment();

        if (!superAdminOrEquivalent.isSuccess()) {
            return ApiResult.newError(superAdminOrEquivalent.getErrorMsg());
        }

        SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);

        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(saleChannel);
        if (BooleanUtils.isFalse(overseasBusiness)) {
            request.setOverseasBusiness(overseasBusiness);
        }
        if (BooleanUtils.isTrue(overseasBusiness)) {
            request.setOverseasBusiness(overseasBusiness);
        }
        //正常账号
        List<String> statusList = new ArrayList<>();
        statusList.add(SaleAccountStastusEnum.NORMAL.getCode());
        request.setAccountStatusList(statusList);
        if (!superAdminOrEquivalent.getResult() && !dataSupportDepartment) {
            //不是超管和最高主管取自己
            String employeeNo = WebUtils.getUserName();
            if (StringUtils.isBlank(employeeNo)) {
                return ApiResult.newError("查询不到员工信息！");
            }

            if (isOnlySelf) {
                // 查询自己
                ApiResult<NewUser> newUserApiResult = NewUsermgtUtils.getUserByNo(employeeNo);
                if (!newUserApiResult.isSuccess()) {
                    return ApiResult.newError(newUserApiResult.getErrorMsg());
                }

                NewUser newUser = newUserApiResult.getResult();
                if (null != newUser && null != newUser.getEmployeeId()) {
                    Integer employeeId = newUser.getEmployeeId();
                    request.setSaleIds(Arrays.asList(employeeId.toString()));
                }
            } else {
                ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils.listTeamLeaderByEmployeeNo(saleChannel);
                if (!listApiResult.isSuccess()) {
                    return ApiResult.newError(listApiResult.getErrorMsg());
                }

                List<NewUser> newUsers = listApiResult.getResult();
                List<String> employeeIds = newUsers.stream().map(o -> o.getEmployeeId().toString()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(employeeIds)) {
                    return ApiResult.newError("查询不到员工信息！");
                }
                request.setSaleIds(employeeIds);
            }
        }

        List<SaleAccount> list = saleAccountService.getAccountInfoList(request);

        return ApiResult.newSuccess(list);
    }

    /**
     * 根据店铺账号获取saleAccount信息
     *
     * @param accounts
     * @return
     */
    public static ApiResult<List<SaleAccount>> getSaleAccountsByAccounts(List<String> accounts, String saleChannel) {

        SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);

        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(saleChannel);
        request.setAccountNumberList(accounts);

        List<SaleAccount> list = saleAccountService.getAccountInfoList(request);
        if (CollectionUtils.isEmpty(list)) {
            return ApiResult.newError("获取不到账号信息");
        }
        return ApiResult.newSuccess(list);
    }

    /**
     * 根据店铺账号获取saleAccount信息 (查询需要字段)
     *
     * @param accounts
     * @return
     */
    public static ApiResult<List<SaleAccount>> getSaleAccountsByAccounts(List<String> accounts, String saleChannel, String[] withFields) {
        SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);

        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(saleChannel);
        request.setAccountNumberList(accounts);

        List<SaleAccount> list = saleAccountService.getSaleAccountsEs(request, withFields);
        if (CollectionUtils.isEmpty(list)) {
            return ApiResult.newError("获取不到账号信息");
        }
        return ApiResult.newSuccess(list);
    }


    /**
     * 根据当前用户角色查询对应的数据
     * 超管和最高权限者:查所有
     * 销售:查自己和下级
     * 文案:查自己和下级
     * 编辑主管:查看所有文案
     */
    public static ApiResult<SaleAccountListResponse> getAuthorAccountListByRole(EsSaleAccountRequest request) {
        boolean isOnlySelf = Boolean.TRUE.equals(request.getIsOnlySelf());
        if (isOnlySelf) {
            return getAuthorAccountListOnlySelf(request);
        }
        String saleChannel = request.getSaleChannel();
        boolean isNeedUser = Boolean.TRUE.equals(request.getIsNeedUsers());
        ApiResult<NewUser> userRes = NewUsermgtUtils.getUserByNo(WebUtils.getUserName());
        if (!userRes.isSuccess()) {
            return ApiResult.newError(userRes.getErrorMsg());
        }

        SaleAccountListResponse account = new SaleAccountListResponse();
        NewUser currentUser = userRes.getResult();
        Boolean superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(currentUser, saleChannel);
        List<Integer> employeeIds = null;

        // 超级管理员
        if (superAdminOrEquivalent || isNeedUser) {
            ApiResult<List<NewUser>> listApiResult = NewUsermgtUtils.listTeamLeaderByEmployeeNo(saleChannel);
            if (!listApiResult.isSuccess()) {
                return ApiResult.newError(listApiResult.getErrorMsg());
            }
            List<NewUser> newUsers = listApiResult.getResult();
            employeeIds = newUsers.stream().map(NewUser::getEmployeeId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(employeeIds)) {
                return ApiResult.newError("查询不到员工信息！");
            }
            account.setNewUsers(newUsers);
        }

        // 文案 编辑主管 查看所有文案
        if (NewUsermgtUtils.isWenan(currentUser) && NewUsermgtUtils.isWenanSupervisor(currentUser)) {
            ApiResult<List<NewUser>> userRoleRes = NewUsermgtUtils.listUserByRoleNames(RoleConstant.EDIT_ROLE_NAME_GROUP);
            if (!userRoleRes.isSuccess()) {
                return ApiResult.newError(userRoleRes.getErrorMsg());
            }
            account.setNewUsers(userRoleRes.getResult());
        }

        // 获取平台账号列表
        if (BooleanUtils.isTrue(request.getIsNeedAccounts())) {
            if (BooleanUtils.isNotTrue(superAdminOrEquivalent)) {
                request.setEmployeeIds(employeeIds);
            }
            // shopee需查子账号
            if (StringUtils.isNotBlank(saleChannel) && saleChannel.equals(SaleChannel.CHANNEL_SHOPEE)) {
                String[] fields = {"accountNumber", "colStr5"};
                SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);
                List<SaleAccount> saleAccountList = saleAccountService.getAccountInfoList(request, fields);
                List<String> accountNumberList = saleAccountList.stream().map(SaleAccount::getAccountNumber).distinct().collect(Collectors.toList());
                List<String> sonAccountNumberList = saleAccountList.stream().map(SaleAccount::getColStr5)
                        .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                account.setAccountNames(accountNumberList);
                account.setSonAccounts(sonAccountNumberList);
            } else {
                List<String> accounts = getAccountListByEs(request);
                account.setAccountNames(accounts);
            }
        }
        return ApiResult.newSuccess(account);
    }

    /**
     * 根据账号查询账号对应销售，销售组长，销售主管的工号-姓名
     *
     * @param accountList
     * @param channel
     * @return
     */
    public static Map<String, SalesmanAccountDetail> getSalesmanAccountDetailMapByEs(List<String> accountList, String channel) {
        if (CollectionUtils.isEmpty(accountList) || StringUtils.isBlank(channel)) {
            return Collections.emptyMap();
        }

        Map<String, SalesmanAccountDetail> salesmanAccountDetailMap = new HashMap<>();

        // 查询账号对应销售姓名-工号
        Map<String, Set<String>> salesmanAccountMap = getSalesmanAccountMapByEs(accountList, channel);
        //销售->销售组长->销售主管 缓存到内存中
        Map<String, Map<String, String>> saleAllMap = Maps.newHashMap();
        for (String accountNumber : salesmanAccountMap.keySet()) {
            try {
                SalesmanAccountDetail detail = new SalesmanAccountDetail();
                Set<String> salesmanSet = salesmanAccountMap.get(accountNumber);
                detail.setSalesmanSet(salesmanSet);
                if (CollectionUtils.isEmpty(salesmanSet)) {
                    continue;
                }
                String salesmanName = new ArrayList<>(salesmanSet).get(0);
                if (saleAllMap.containsKey(salesmanName)) {
                    Map<String, String> otherSaleMap = saleAllMap.get(salesmanName);
                    otherSaleMap.forEach((salesTeamLeader, saleSuperior) -> {
                        detail.setSalesTeamLeaderName(salesTeamLeader);
                        detail.setSalesSupervisorName(saleSuperior);
                    });
                } else {
                    String salesTeamLeader = SaleSuperiorReidsUtils.getSaleSuperior(salesmanName, true);
                    detail.setSalesTeamLeaderName(salesTeamLeader);
                    String saleSuperior = SaleSuperiorReidsUtils.getSaleSuperior(detail.getSalesTeamLeaderName(), true);
                    detail.setSalesSupervisorName(saleSuperior);
                    Map<String, String> saleLeaderMap = Maps.newHashMapWithExpectedSize(1);
                    saleLeaderMap.put(salesTeamLeader, saleSuperior);
                    saleAllMap.put(salesmanName, saleLeaderMap);
                }
                salesmanAccountDetailMap.put(accountNumber, detail);
            } catch (Exception e) {
                log.error("根据账号查询账号对应销售，销售组长，销售主管的工号-姓名报错；" + e.getMessage());
            }
        }

        return salesmanAccountDetailMap;
    }

    /**
     * 根据账号查询账号对应销售工号-姓名
     * （账号查用户id，根据用户id查redis获取用户工号-姓名）
     *
     * @param accountList
     * @param channel
     * @return
     */
    public static Map<String, Set<String>> getSalesmanAccountMapByEs(List<String> accountList, String channel) {
        if (CollectionUtils.isEmpty(accountList) || StringUtils.isBlank(channel)) {
            return Collections.emptyMap();
        }

        // 账号去重
        accountList = accountList.stream().distinct().collect(Collectors.toList());

        // 根据账号查询用户id
        SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);
        String[] withFields = {"accountNumber", "salesperson"};
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(channel);
        request.setAccountNumberList(accountList);
        List<SaleAccount> saleAccountList = saleAccountService.getSaleAccountsEs(request, withFields);

        if (CollectionUtils.isEmpty(saleAccountList)) {
            return Collections.emptyMap();
        }
        Map<String, EmployeeInfo> salesmanMap = Maps.newHashMap();
        Map<String, Set<String>> salesmanAccountMap = new HashMap<>();
        for (SaleAccount saleAccount : saleAccountList) {
            try {
                String salesperson = saleAccount.getSalesperson();
                if (StringUtils.isBlank(salesperson)) {
                    continue;
                }

                Set<String> salesmanSet = new HashSet<>();
                List.of(salesperson.split(",")).forEach(salesmanId -> {
                    EmployeeInfo employeeInfo;
                    if (salesmanMap.containsKey(salesmanId)) {
                        employeeInfo = salesmanMap.get(salesmanId);
                    } else {
                        employeeInfo = PublishRedisClusterUtils.hGet(ErpUsermgtNRedisConStant.GET_EMPLOYEE_INFO_BY_EMPLOYEE_ID, new TypeReference<EmployeeInfo>() {
                        }, salesmanId);
                    }
                    if (null == employeeInfo) {
                        return;
                    }

                    String salesmanName = employeeInfo.getName() + "-" + employeeInfo.getEmployeeNo();
                    salesmanSet.add(salesmanName);
                    salesmanMap.put(salesmanId, employeeInfo);
                });

                if (CollectionUtils.isNotEmpty(salesmanSet)) {
                    salesmanAccountMap.put(saleAccount.getAccountNumber(), salesmanSet);
                }

            } catch (Exception e) {
                log.error("根据账号查询账号对应销售工号-姓名报错：" + e.getMessage());
            }
        }

        return salesmanAccountMap;
    }

    /**
     * 根据销售ID获取其管理的(不查下级)店铺
     *
     * @param saleChannel 平台
     * @param saleIds     销售ID
     * @return 店铺信息
     */
    public static List<SaleAccount> getAccountBySaleId(String saleChannel, List<String> saleIds) {
        // 查询销售管理的店铺
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleIds(saleIds);
        request.setSaleChannel(saleChannel);
        //正常账号
        List<String> statusList = new ArrayList<>();
        statusList.add(SaleAccountStastusEnum.NORMAL.getCode());
        request.setAccountStatusList(statusList);
        SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);
        try {
            return saleAccountService.getAccountInfoList(request);
        } catch (Exception e) {
            log.error("查询ES店铺信息异常", e);
        }
        return Collections.emptyList();

    }

    /**
     * 入参是 有可能为admin的 姓名-工号
     *
     * @param saleMan
     * @return
     */
    public static String getSaleId(String saleMan) {
        if (StringUtils.isBlank(saleMan)) {
            return "";
        }
        if (saleMan.contains("-")) {
            return saleMan.split("-")[1];
        } else {
            return saleMan;
        }
    }

    /**
     * 根据账号的salesSet获取到id
     *
     * @param salesmanAccountDetail
     * @return
     */
    public static String getSaleId(SalesmanAccountDetail salesmanAccountDetail) {
        String saleId = "";
        if (salesmanAccountDetail != null) {
            Set<String> salesmanSet = salesmanAccountDetail.getSalesmanSet();
            if (CollectionUtils.isNotEmpty(salesmanSet)) {
                String saleMan = new ArrayList<>(salesmanSet).get(0);
                saleId = getSaleId(saleMan);
            }
        }
        return saleId;
    }

    /**
     * 根据店铺获取订单店铺配置的主管
     *
     * @param accountNumber 店铺
     * @return 主管工号
     */
    public static ApiResult<SuperEmployeeInfo> getAmazonSalesSubSectorLeader(String accountNumber) {
        if (StringUtils.isBlank(accountNumber)) {
            return ApiResult.newError("店铺为空");
        }
        SuperEmployeeInfo superEmployeeIfPresent = ACCOUNT_SUPER_EMPLOYEE.getIfPresent(accountNumber);
        if (superEmployeeIfPresent != null) {
            //log.info("cache {},[{}]", accountNumber, superEmployeeIfPresent);
            return ApiResult.newSuccess(superEmployeeIfPresent);
        }

        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setAccountNumberList(List.of(accountNumber));
        request.setSaleChannel(SaleChannel.CHANNEL_AMAZON);
        SaleAccountService saleAccountService = SpringUtils.getBean(SaleAccountService.class);
        try {
            List<SaleAccount> accountInfoList = saleAccountService.getAccountInfoList(request, "accountNumber", "salesperson");
            if (CollectionUtils.isEmpty(accountInfoList)) {
                return ApiResult.newError(accountNumber + ",订单账户配置查询为空");
            }

            List<String> salesmanIdList = List.of(accountInfoList.get(0).getSalesperson().split(","));
            if (CollectionUtils.isEmpty(salesmanIdList)) {
                return ApiResult.newError(accountNumber + ",订单账户未配置销售");
            }
            String salesmanId = salesmanIdList.get(0);

            EmployeeInfo employeeInfo = PublishRedisClusterUtils.hGet(ErpUsermgtNRedisConStant.GET_EMPLOYEE_INFO_BY_EMPLOYEE_ID, new TypeReference<EmployeeInfo>() {
            }, String.valueOf(salesmanId));
            if (null == employeeInfo) {
                return ApiResult.newError(salesmanId + ", 查询用户信息为空");
            }
            // 当前用户是否为主管
            String positionName = employeeInfo.getPositionName();
            if (StringUtils.isBlank(positionName)) {
                return ApiResult.newError(accountNumber + ",获取用户职位为空:" + JSON.toJSONString(employeeInfo));
            }

//            if (positionName.contains("主管") && StringUtils.containsIgnoreCase(positionName, "amazon")) {
//                SuperEmployeeInfo superEmployeeInfo = new SuperEmployeeInfo();
//                superEmployeeInfo.setSuperEmployeeNo(employeeInfo.getEmployeeNo());
//                superEmployeeInfo.setName(employeeInfo.getName());
//                ACCOUNT_SUPER_EMPLOYEE.put(accountNumber, superEmployeeInfo);
//                return ApiResult.newSuccess(superEmployeeInfo);
//            }

            String employeeNo = employeeInfo.getEmployeeNo();
            ApiResult<SuperEmployeeInfo> superEmployee = getSuperEmployeeV2(employeeNo);
            if (!superEmployee.isSuccess()) {
                return ApiResult.newError(accountNumber + "," + superEmployee.getErrorMsg());
            }
            SuperEmployeeInfo superEmployeeInfo = superEmployee.getResult();
            ACCOUNT_SUPER_EMPLOYEE.put(accountNumber, superEmployeeInfo);
            return ApiResult.newSuccess(superEmployeeInfo);
        } catch (Exception e) {
            log.error("查询店铺主管信息异常", e);
            return ApiResult.newError(accountNumber + ", 查询店铺主管信息异常");
        }
    }

    private static ApiResult<SuperEmployeeInfo> getSuperEmployeeV2(String employeeNo) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("employeeNo", employeeNo);
            params.put("servicePlatform", SaleChannel.CHANNEL_AMAZON);
            LeaderInfo leaderInfo = NewUsermgtUtils.getLeadersByCondition(JSON.toJSONString(params));
            if (ObjectUtils.isNotEmpty(leaderInfo)) {
                List<List<LeaderInfo.Employee>> employeeList = Arrays.asList(leaderInfo.getJuniorExecutives(), leaderInfo.getIntermediateExecutives(), leaderInfo.getPlatformSalesSupervisors());

                // 依次查找主管信息
                LeaderInfo.Employee firstEmployee = employeeList.stream()
                        .filter(list -> list != null && !list.isEmpty())
                        .flatMap(List::stream)
                        .findFirst()
                        .orElse(null);
                //log.info("firstEmployee:{}", JSON.toJSONString(firstEmployee));

                if (ObjectUtils.isNotEmpty(firstEmployee)) {
                    SuperEmployeeInfo superEmployeeInfo = new SuperEmployeeInfo();
                    BeanUtils.copyProperties(firstEmployee, superEmployeeInfo);
                    return ApiResult.newSuccess(superEmployeeInfo);
                }
            }
        } catch (Exception e) {
            log.error("根据条件获取主管信息异常", e);
            throw new RuntimeException(e);
        }
        return ApiResult.newError("获取上级职位数据为空");
    }

    private static ApiResult<SuperEmployeeInfo> getSuperEmployee(String employeeNo, int tryNumber) {
        if (tryNumber == 0) {
            return ApiResult.newError(employeeNo + ",无对应主管");
        }
        ApiResult<SuperEmployeeInfo> saleSuperior = NewUsermgtUtils.getSaleSuperior(employeeNo);
        if (!saleSuperior.isSuccess()) {
            return ApiResult.newError(employeeNo + ":查询主管失败," + saleSuperior.getErrorMsg());
        }
        SuperEmployeeInfo superEmployeeInfo = saleSuperior.getResult();
        String positionName = superEmployeeInfo.getPositionName();
        if (StringUtils.isBlank(positionName)) {
            return ApiResult.newError("获取上级职位异常，" + superEmployeeInfo);
        }
        if (positionName.contains("主管") && StringUtils.containsIgnoreCase(positionName, "amazon")) {
            return ApiResult.newSuccess(superEmployeeInfo);
        }
        String superEmployeeNo = superEmployeeInfo.getSuperEmployeeNo();
        --tryNumber;
        return getSuperEmployee(superEmployeeNo, tryNumber);
    }
}
