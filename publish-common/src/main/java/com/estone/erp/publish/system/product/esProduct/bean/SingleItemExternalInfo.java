package com.estone.erp.publish.system.product.esProduct.bean;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.system.product.bean.forbidden.InfringementSaleProhibitionVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * 单品外部系统字段信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SingleItemExternalInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 产品上架表id
     */
    private Integer singleItemId;

    /**
     * 库位
     */
    private String stockLocation;

    /**
     * 可用库存
     */
    private Integer availableStock;

    /**
     * 调拨库存
     */
    private Integer allocationQuantity;

    /**
     * 所有平台待发总数
     */
    private Double pendingCount;

    /**
     * 所有平台刊登总数
     */
    private Integer publishCount;

    /**
     * 在途+待上架
     */
    private Integer waitingOnWay;

    /**
     * 各平台待发 存json
     */
    private String platPending;

    /**
     * 平台刊登数量
     */
    private String platPublish;

    /**
     * 平台刊登时间
     */
    private String platPublishDate;

    /**
     * 一天销量总数
     */
    private Double oneSalesNum;

    /**
     * 七天销量总数
     */
    private Double sevenSalesNum;

    /**
     * 可用库存天数 =  可用库存/(七天销量总数/7)
     */
    private BigDecimal availableStockDays;

    /**
     * 各平台一天销量
     */
    private String platOneSales;

    /**
     * 各平台七天销量
     */
    private String platSevenSales;

    /**
     * 14天销量总数
     */
    private Double fourteenSalesNum;

    /**
     * 各平台14天销量
     */
    private String platFourteenSales;

    /**
     * 30天销量总数
     */
    private Double thirtySalesNum;

    /**
     * 60天销量总数
     */
    private Double sixtySalesNum;
    /**
     * 各平台三十天销量
     */
    private String platThirtySales;

    /**
     * 90天销量总数
     */
    private Double ninetySalesNum;

    /**
     * 各平台90天销量
     */
    private String platNinetySales;

    /**
     * 安全库存
     */
    private Double safetyStock;

    /**
     * 警戒库存天数
     */
    private Double inventoryDays;

    /**
     * 最小订货量
     */
    private Integer minOrder;

    /**
     * 建议采购下限
     */
    private Double purchaseLimit;

    /**
     * 建议采购天数
     */
    private Double purchaseDays;

    /**
     * 可调整变量
     */
    private Double adjustVariable;

    /**
     * 春节备货日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date springStockingDate;

    /**
     * 备货周期
     */
    private Integer stockingDate;

    /**
     * 状态
     */
    private Boolean status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateAt;

    /**
     * 销售属性
     */
    @ApiModelProperty("销售属性")
    @ApiParam("销售属性")
    private String saleAtt;

    private String saleAtts;

    /**
     * 量词
     */
    @ApiModelProperty("量词")
    @ApiParam("量词")
    private String measureWord;

    /**
     * 新增开发平台站点
     */
    private String platSite;

    /**
     * 新增产品视频链接
     */
    private String productVideo;

    /**
     * 新增退貨在途字段
     */
    private Integer returnOnWayStockQuantity;

    /**
     * 图片url
     */
    private String imgUrls;

    /**
     * 新增独立站SKU标记时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date specialSkuTime;

    /**
     * 新增产品说明书
     */
    private String productInstruction;

    /**
     * 新增是否定制
     */
    @ApiModelProperty("是否定制")
    private Integer isCustomMade;

    /**
     * 新增定制周期最小值
     */
    @ApiModelProperty("定制周期最小值")
    private Integer minCustomMadeCycle;

    /**
     * 新增定制周期最大值
     */
    @ApiModelProperty("定制周期最大值")
    private Integer maxCustomMadeCycle;

    /**
     * 认证资质
     */
    @ApiModelProperty("认证资质")
    private String qualificationName;

    /**
     * 资质附件链接
     */
    @ApiModelProperty("资质附件链接")
    private String qualificationLink;

    /**
     * 是否完成侵权 0:否  1:是
     */
    private Integer finishInfringement;

    /**
     * 侵权审核人
     */
    private String infringementUser;

    /**
     * 侵权类型 改为禁售类型
     */
    private String infringementTypeName;

    /**
     * 侵权对象 改为禁售原因
     */
    private String infringementObj;

    /**
     * 备注
     */
    private String infringementRemark;

    /**
     * 是否侵权审核 0否  1是
     */
    private Integer infringementCheck;

    /**
     * 是否限价产品 若选择是，则需要输入备注信息
     */
    private String fixedPrice;


    /**
     * 是否限价产品 0 否  1 是
     */
    private Integer isFixedPrice;

    /**
     * 是否引流SKU 0 是 1 否
     */
    @ApiModelProperty("是否引流SKU")
    private Integer isDrainageSku;

    /**
     * 新增容量
     */
    private Double capacity;
    /**
     * FBA国内库存
     */
    private Integer fbaInternalStock;

    /**
     * FBA可售库存
     */
    private Integer fbaSaleStock;

    /**
     * FBA待入库数量
     */
    private Integer fbaWaitStockCount;

    private Integer optimizedStock;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * 采集开发id
     */
    private String collectDevId;

    /**
     * 全部禁售信息
     */
    private String infringementSaleProhibition;

    /**
     * 滞销标签: 滞销,短呆滞,长呆滞
     */
    private String unsalableTag;

    /**
     * 包裹长
     */
    @Field(type = FieldType.Float)
    private BigDecimal packLength;

    /**
     * 包裹宽
     */
    @Field(type = FieldType.Float)
    private BigDecimal packWidth;

    /**
     * 包裹高
     */
    @Field(type = FieldType.Float)
    private BigDecimal packHeight;

    private List<InfringementSaleProhibitionVO> infringementSaleProhibitionVOList;

    public List<InfringementSaleProhibitionVO> getAllInfringementSaleProhibitionVOList() {
        if (StringUtils.isNotBlank(getInfringementSaleProhibition())) {
            List<InfringementSaleProhibitionVO> infringementSaleProhibitionVOList = JSON.parseArray(infringementSaleProhibition, InfringementSaleProhibitionVO.class);
            return infringementSaleProhibitionVOList;
        }
        return null;
    }

    /**
     * 处理风险等级
     * @param infringementSaleProhibitionVOList
     * @return
     */
    public List<Integer> getRiskLevelIdList(List<InfringementSaleProhibitionVO> infringementSaleProhibitionVOList) {
        if (CollectionUtils.isEmpty(infringementSaleProhibitionVOList)) {
            return Collections.emptyList();
        }
        Set<Integer> riskLevelIdList = new HashSet<>();
        for (int i = 0; i < infringementSaleProhibitionVOList.size(); i++) {
            InfringementSaleProhibitionVO prohibitionVO = infringementSaleProhibitionVOList.get(i);
            if (null == prohibitionVO || null == prohibitionVO.getRiskLevelId()) {
                continue;
            }
            riskLevelIdList.add(prohibitionVO.getRiskLevelId());
        }
        return riskLevelIdList.isEmpty() ? Collections.emptyList() : new ArrayList<>(riskLevelIdList);
    }

}
