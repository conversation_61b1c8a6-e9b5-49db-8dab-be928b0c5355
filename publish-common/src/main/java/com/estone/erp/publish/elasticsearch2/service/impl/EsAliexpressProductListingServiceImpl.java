package com.estone.erp.publish.elasticsearch2.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.elasticsearch.util.ElasticSearchHelper;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.OnlineStatusEnum;
import com.estone.erp.publish.elasticsearch2.dao.EsAliexpressProductListingRepository;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.feginService.modle.PlatformListingInfo;
import com.estone.erp.publish.feginService.modle.SkuPubilshListingFirstJoinTimeVo;
import com.estone.erp.publish.feginService.modle.SkuPublishListingNum;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.Strings;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.NestedQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.Cardinality;
import org.elasticsearch.search.aggregations.metrics.TopHits;
import org.elasticsearch.search.aggregations.metrics.TopHitsAggregationBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHitSupport;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.SearchScrollHits;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/11/2317:08
 */
@Slf4j
@Service
public class EsAliexpressProductListingServiceImpl implements EsAliexpressProductListingService {

    private IndexCoordinates aliexpressProductListingIndexCoordinates = IndexCoordinates.of("aliexpress_product_listing");
    private static final String indexName = "aliexpress_product_listing";

    @Resource
    private RestHighLevelClient restHighLevelClient2;
    @Resource
    private EsAliexpressProductListingRepository esAliexpressProductListingRepository;
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate2;

    @Override
    public long count() {
        return esAliexpressProductListingRepository.count();
    }

    @Override
    public void save(EsAliexpressProductListing esAliexpressProductListing) {
        if(esAliexpressProductListing != null){
            elasticsearchRestTemplate2.save(esAliexpressProductListing);
        }
    }

    @Override
    public void saveAll(List<EsAliexpressProductListing> esAliexpressAccountProductInfos) {
        if(CollectionUtils.isNotEmpty(esAliexpressAccountProductInfos)){
            elasticsearchRestTemplate2.save(esAliexpressAccountProductInfos);
        }
    }
    @Override
    public void deleteById(String id) {
        esAliexpressProductListingRepository.deleteById(id);
    }

    @Override
    public void deleteByList(List<EsAliexpressProductListing> productListingList) {
        esAliexpressProductListingRepository.deleteAll(productListingList);
    }

    @Override
    public void updateRequest(EsAliexpressProductListing esAliexpressProductListing)  {
        if(esAliexpressProductListing == null || StringUtils.isBlank(esAliexpressProductListing.getId())){
            return;
        }
        try {
            UpdateRequest updateRequest = new UpdateRequest(indexName, esAliexpressProductListing.getId());
            updateRequest.doc(JSON.toJSONString(esAliexpressProductListing), XContentType.JSON);
            UpdateResponse updateResponse = restHighLevelClient2.update(updateRequest, RequestOptions.DEFAULT);
            if(updateResponse == null){
                log.error("es修改返回null");
            }
        }catch (Exception e) {
            log.error(indexName + esAliexpressProductListing.getId() + "修改失败" + e.getMessage(), e);
            throw new RuntimeException(indexName + esAliexpressProductListing.getId() + "修改失败" + e.getMessage());
        }
    }

    @Override
    public void updateRequest(String json, String id)  {
        if(StringUtils.isBlank(json) || StringUtils.isBlank(id)){
            return;
        }
        try {
            UpdateRequest updateRequest = new UpdateRequest(indexName, id);
            updateRequest.doc(json, XContentType.JSON);
            UpdateResponse updateResponse = restHighLevelClient2.update(updateRequest, RequestOptions.DEFAULT);
            if(updateResponse == null){
                log.error("es修改返回null");
            }
        }catch (Exception e) {
            log.error(indexName + id + "修改失败" + e.getMessage(), e);
            throw new RuntimeException(indexName + id + "修改失败" + e.getMessage());
        }
    }

    @Override
    public EsAliexpressProductListing findAllById(String id) {
        return esAliexpressProductListingRepository.findAllById(id);
    }

    private void setQuery(EsAliexpressProductListingRequest esAliexpressProductListingRequest, BoolQueryBuilder boolQueryBuilder){

        //半托管标签是否为空
        Boolean halfCountryExitLabelIsNull = esAliexpressProductListingRequest.getHalfCountryExitLabelIsNull();
        if(halfCountryExitLabelIsNull != null){
            if(halfCountryExitLabelIsNull){
                boolQueryBuilder.mustNot(QueryBuilders.existsQuery("halfCountryExitLabel"));
            }else{
                boolQueryBuilder.must(QueryBuilders.existsQuery("halfCountryExitLabel"));
            }
        }

        String halfCountryExitLabel = esAliexpressProductListingRequest.getHalfCountryExitLabel();
        if(StringUtils.isNotBlank(halfCountryExitLabel)){
            boolQueryBuilder.must(QueryBuilders.matchQuery("halfCountryExitLabel", halfCountryExitLabel));
        }

        if (CollectionUtils.isNotEmpty(esAliexpressProductListingRequest.getGroupIdsList())){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            esAliexpressProductListingRequest.getGroupIdsList().forEach(t->{
                boolQuery.should(QueryBuilders.wildcardQuery("groupIds", "*" + t + "*"));
            });
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        String findTaxType = esAliexpressProductListingRequest.getFindTaxType();
        if(StringUtils.isNotBlank(findTaxType)){
            List<String> strings = Arrays.asList("1", "2");
            if(strings.contains(findTaxType)){
                boolQueryBuilder.must(QueryBuilders.matchQuery("taxType", findTaxType));
            }else{
                boolQueryBuilder.mustNot(QueryBuilders.existsQuery("taxType"));
            }
        }

        Boolean isHasGPSRImg = esAliexpressProductListingRequest.getIsHasGPSRImg();
        if(isHasGPSRImg != null){
            boolQueryBuilder.must(QueryBuilders.matchQuery("isHasGPSRImg", isHasGPSRImg));
        }

        Boolean existIsHasGPSRImg = esAliexpressProductListingRequest.getExistIsHasGPSRImg();
        if(existIsHasGPSRImg != null){
            if(existIsHasGPSRImg){
                boolQueryBuilder.must(QueryBuilders.existsQuery("isHasGPSRImg"));
            }else{
                boolQueryBuilder.mustNot(QueryBuilders.existsQuery("isHasGPSRImg"));
            }
        }

        Boolean isHasPackageImg = esAliexpressProductListingRequest.getIsHasPackageImg();
        if(isHasPackageImg != null){
            boolQueryBuilder.must(QueryBuilders.matchQuery("isHasPackageImg", isHasPackageImg));
        }
        Boolean isHasStockImg = esAliexpressProductListingRequest.getIsHasStockImg();
        if(isHasStockImg != null){
            boolQueryBuilder.must(QueryBuilders.matchQuery("isHasStockImg", isHasStockImg));
        }

        //制造商是否为空
        Boolean manufactureIdIsNull = esAliexpressProductListingRequest.getManufactureIdIsNull();
        if(manufactureIdIsNull != null){
            if(manufactureIdIsNull){
                boolQueryBuilder.mustNot(QueryBuilders.existsQuery("manufactureId"));
            }else{
                boolQueryBuilder.must(QueryBuilders.existsQuery("manufactureId"));
            }
        }

        //欧盟类型
        String msrEuIdType = esAliexpressProductListingRequest.getMsrEuIdType();
        if(StringUtils.isNotBlank(msrEuIdType)){
//            boolQueryBuilder.must(QueryBuilders.matchQuery("msrEuIdType", msrEuIdType));
            boolQueryBuilder.must(QueryBuilders.termsQuery("msrEuIdType", CommonUtils.splitList(msrEuIdType, ",")));
        }

        //平台skuid
        if(CollectionUtils.isNotEmpty(esAliexpressProductListingRequest.getPlatSkuIdList())){
            boolQueryBuilder.must(QueryBuilders.termsQuery("platSkuId", esAliexpressProductListingRequest.getPlatSkuIdList()));
        }

        //产品物流模板标签
        List<String> categoryLabelList = esAliexpressProductListingRequest.getCategoryLabelList();
        if(CollectionUtils.isNotEmpty(categoryLabelList)){
            boolQueryBuilder.must(QueryBuilders.termsQuery("categoryLabel", categoryLabelList));
        }

        //资质
        if(esAliexpressProductListingRequest.getIsHasQualification() != null){
            boolQueryBuilder.must(QueryBuilders.matchQuery("isHasQualification", esAliexpressProductListingRequest.getIsHasQualification()));
        }

        //视频为空
        if(esAliexpressProductListingRequest.getIsHasVideo() != null){
            boolQueryBuilder.must(QueryBuilders.matchQuery("isHasVideo", esAliexpressProductListingRequest.getIsHasVideo()));
        }

        // 是否包含侵权词
        if(BooleanUtils.isTrue(esAliexpressProductListingRequest.getContainInfringementWord())) {
            boolQueryBuilder.must(QueryBuilders.existsQuery("infringementWordList"));
        }else if (BooleanUtils.isFalse(esAliexpressProductListingRequest.getContainInfringementWord())){
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("infringementWordList"));
        }

        //滞销标签
        String unsalableTag = esAliexpressProductListingRequest.getUnsalableTag();
        if(StringUtils.isNotBlank(unsalableTag)){
            boolQueryBuilder.must(QueryBuilders.termsQuery("unsalableTag", CommonUtils.splitList(unsalableTag, ",")));
        }

        //商品质量
        Integer itemShow = esAliexpressProductListingRequest.getItemShow();
        if(itemShow != null){
            boolQueryBuilder.must(QueryBuilders.matchQuery("itemShow", itemShow));
        }

        //查询不存在
        Integer notInItemShow = esAliexpressProductListingRequest.getNotInItemShow();
        if(notInItemShow != null){
            boolQueryBuilder.mustNot(QueryBuilders.matchQuery("itemShow", notInItemShow));
        }

        //是否新品
        Boolean newState = esAliexpressProductListingRequest.getNewState();
        if(newState != null){
            boolQueryBuilder.must(QueryBuilders.matchQuery("newState", newState));
        }
        //是否促销
        Integer promotion = esAliexpressProductListingRequest.getPromotion();
        if(promotion != null){
            if(promotion == 1){
                boolQueryBuilder.must(QueryBuilders.matchQuery("promotion", promotion));
            }else{
                boolQueryBuilder.must(QueryBuilders.termsQuery("promotion", Arrays.asList(0, 2)));
            }
        }
        //是否多属性无图片
        Boolean isMultiNoImg = esAliexpressProductListingRequest.getIsMultiNoImg();
        if(isMultiNoImg != null){
            boolQueryBuilder.must(QueryBuilders.matchQuery("isMultiNoImg", isMultiNoImg));
        }

        //查询 是否包含32国调价
        Boolean isRegionalPrice = esAliexpressProductListingRequest.getIsRegionalPrice();
        if(isRegionalPrice != null){
            boolQueryBuilder.must(QueryBuilders.matchQuery("isRegionPrice", isRegionalPrice));
        }

        //查询 是否Cn中国省份
        Boolean isCnProvinceCategory = esAliexpressProductListingRequest.getIsCnProvinceCategory();
        if(isCnProvinceCategory != null){
            boolQueryBuilder.must(QueryBuilders.matchQuery("isCnProvinceCategory", isCnProvinceCategory));
        }

        String cnProvince = esAliexpressProductListingRequest.getCnProvince();
        if(StringUtils.isNotBlank(cnProvince)){
            if(StringUtils.equalsIgnoreCase(cnProvince, "无省份")){
                boolQueryBuilder.mustNot(QueryBuilders.existsQuery("cnProvince"));
            }else{
                boolQueryBuilder.must(QueryBuilders.matchQuery("cnProvince", cnProvince));
            }
        }

        Boolean isVariant = esAliexpressProductListingRequest.getIsVariant();
        if(isVariant != null){
            boolQueryBuilder.must(QueryBuilders.matchQuery("isVariant", isVariant));
        }
        Boolean isCayType = esAliexpressProductListingRequest.getIsCayType();
        if(isCayType != null){
            boolQueryBuilder.must(QueryBuilders.matchQuery("isCayType", isCayType));
        }

        String freightTemplateIds = esAliexpressProductListingRequest.getFreightTemplateIds();
        if(StringUtils.isNotBlank(freightTemplateIds)){
            boolQueryBuilder.must(QueryBuilders.termsQuery("freightTemplateId", CommonUtils.splitLongList(freightTemplateIds, ",")));
        }

        Long freightTemplateId = esAliexpressProductListingRequest.getFreightTemplateId();
        if(freightTemplateId != null){
            boolQueryBuilder.must(QueryBuilders.matchQuery("freightTemplateId", freightTemplateId));
        }

        String groupBy = esAliexpressProductListingRequest.getGroupBy();
        if(StringUtils.isNotBlank(groupBy)){
            boolQueryBuilder.must(QueryBuilders.matchQuery("groupBy", groupBy));
        }

        String id = esAliexpressProductListingRequest.getId();
        if(StringUtils.isNotBlank(id)){
            boolQueryBuilder.must(QueryBuilders.matchQuery("id", id));
        }

        String idStr = esAliexpressProductListingRequest.getIdStr();
        if(StringUtils.isNotBlank(idStr)){
            List<String> integers = CommonUtils.splitList(idStr, ",");
            boolQueryBuilder.must(QueryBuilders.termsQuery("id", integers));
        }

        List<String> idList = esAliexpressProductListingRequest.getIdList();
        if(CollectionUtils.isNotEmpty(idList)){
            boolQueryBuilder.must(QueryBuilders.termsQuery("id", idList));
        }

        //商品id
        String productIdStr = esAliexpressProductListingRequest.getProductIdStr();
        if(StringUtils.isNotBlank(productIdStr)){
            List<Long> longs = CommonUtils.splitLongList(productIdStr, ",");
            boolQueryBuilder.must(QueryBuilders.termsQuery("productId", longs));
        }

        Long productId = esAliexpressProductListingRequest.getProductId();
        if(productId != null){
            boolQueryBuilder.must(QueryBuilders.matchQuery("productId", productId));
        }

        //查询是否配置上下架用
        List<Long> productIdList = esAliexpressProductListingRequest.getProductIdList();
        if(CollectionUtils.isNotEmpty(productIdList)){
            boolQueryBuilder.must(QueryBuilders.termsQuery("productId", productIdList));
        }

        //查询没有配置的
        List<Long> notInProductIdList = esAliexpressProductListingRequest.getNotInProductIdList();
        if(CollectionUtils.isNotEmpty(notInProductIdList)){
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("productId", notInProductIdList));
        }

        Integer publishRole = esAliexpressProductListingRequest.getPublishRole();
        if(publishRole != null){
            boolQueryBuilder.must(QueryBuilders.matchQuery("publishRole", publishRole));
        }

        Boolean publishRoleIsNull = esAliexpressProductListingRequest.getPublishRoleIsNull();
        if(publishRoleIsNull != null && publishRoleIsNull){
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("publishRole"));
        }

        Boolean skuStatusIsNull = esAliexpressProductListingRequest.getSkuStatusIsNull();
        if(skuStatusIsNull != null && skuStatusIsNull){
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("skuStatus"));
        }

        //货号
        String articleNumberStr = esAliexpressProductListingRequest.getArticleNumberStr();
        if(StringUtils.isNotBlank(articleNumberStr)){
            List<String> strings = CommonUtils.splitList(articleNumberStr, ",");
            boolQueryBuilder.must(QueryBuilders.termsQuery("articleNumber", strings));

        }

        String articleNumber = esAliexpressProductListingRequest.getArticleNumber();
        if(StringUtils.isNotBlank(articleNumber)){
            boolQueryBuilder.must(QueryBuilders.matchQuery("articleNumber", articleNumber));
        }

        // spu
        String spu = esAliexpressProductListingRequest.getSpu();
        if (StringUtils.isNotBlank(spu)) {
            boolQueryBuilder.must(QueryBuilders.matchQuery("spu", spu));
        }

        List<String> spuList = esAliexpressProductListingRequest.getSpuList();
        if(CollectionUtils.isNotEmpty(spuList)){
            boolQueryBuilder.must(QueryBuilders.termsQuery("spu", spuList));
        }

        //账号
        String aliexpressAccountNumber = esAliexpressProductListingRequest.getAliexpressAccountNumber();
        if(StringUtils.isNotBlank(aliexpressAccountNumber)){
            List<String> accountList = CommonUtils.splitList(aliexpressAccountNumber, ",");
            boolQueryBuilder.must(QueryBuilders.termsQuery("aliexpressAccountNumber", accountList));

        }

        //在售状态
        String productStatusType = esAliexpressProductListingRequest.getProductStatusType();
        if(StringUtils.isNotBlank(productStatusType)){
            List<String> strings = CommonUtils.splitList(productStatusType, ",");
            boolQueryBuilder.must(QueryBuilders.termsQuery("productStatusType", strings));
        }

        String skuCode = esAliexpressProductListingRequest.getSkuCode();
        if(StringUtils.isNotBlank(skuCode)){
            boolQueryBuilder.must(QueryBuilders.termsQuery("skuCode", CommonUtils.splitList(skuCode, ",")));
        }

        String skuCodeLike = esAliexpressProductListingRequest.getSkuCodeLike();
        if(StringUtils.isNotBlank(skuCodeLike)){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("skuCode", "*" + skuCodeLike + "*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }
        String skuId = esAliexpressProductListingRequest.getSkuId();
        if(StringUtils.isNotBlank(skuId)){
            if(StringUtils.contains(skuId, ",")){
                boolQueryBuilder.must(QueryBuilders.termsQuery("skuId", CommonUtils.splitList(skuId, ",")));
            }else{
                boolQueryBuilder.must(QueryBuilders.matchQuery("skuId", skuId));
            }
        }

        //发货地
        String deliveryAddress = esAliexpressProductListingRequest.getDeliveryAddress();
        if(StringUtils.isNotBlank(deliveryAddress)){
            boolQueryBuilder.must(QueryBuilders.matchQuery("deliveryAddress", deliveryAddress));
        }

        List<String> deliveryAddressList = esAliexpressProductListingRequest.getDeliveryAddressList();
        if(CollectionUtils.isNotEmpty(deliveryAddressList)){
            boolQueryBuilder.must(QueryBuilders.termsQuery("deliveryAddress", deliveryAddressList));
        }

        //是否海外仓
        Boolean isOverseas = esAliexpressProductListingRequest.getIsOverseas();
        if(null != isOverseas){
            boolQueryBuilder.must(QueryBuilders.matchQuery("isOverseas", isOverseas));
        }

        Long groupId = esAliexpressProductListingRequest.getGroupId();
        if(groupId != null){
            boolQueryBuilder.must(QueryBuilders.matchQuery("groupId", groupId));
        }
        if(StringUtils.isNotBlank(esAliexpressProductListingRequest.getGroupIds())){
            List<Long> integers = CommonUtils.splitLongList(esAliexpressProductListingRequest.getGroupIds(), ",");
            boolQueryBuilder.must(QueryBuilders.termsQuery("groupId", integers));

        }

        String stockStatus = esAliexpressProductListingRequest.getStockStatus();
        if(StringUtils.isNotBlank(stockStatus)){
            if(StringUtils.equalsIgnoreCase(stockStatus, "true")){
                boolQueryBuilder.must(QueryBuilders.rangeQuery("ipmSkuStock").gt(0));

            }else if(StringUtils.equalsIgnoreCase(stockStatus, "false")){
                boolQueryBuilder.must(QueryBuilders.matchQuery("ipmSkuStock", 0));
            }
        }

        Integer fromIpmSkuStock = esAliexpressProductListingRequest.getFromIpmSkuStock();
        if(fromIpmSkuStock != null){
            boolQueryBuilder.must(QueryBuilders.rangeQuery("ipmSkuStock").from(fromIpmSkuStock));
        }

        Integer toIpmSkuStock = esAliexpressProductListingRequest.getToIpmSkuStock();
        if(toIpmSkuStock != null){
            boolQueryBuilder.must(QueryBuilders.rangeQuery("ipmSkuStock").to(toIpmSkuStock));
        }


        String fromCreateTime = esAliexpressProductListingRequest.getFromCreateTime();
        if(StringUtils.isNotBlank(fromCreateTime)){
            boolQueryBuilder.must(QueryBuilders.rangeQuery("createTime").gte(fromCreateTime));
        }

        String toCreateDate = esAliexpressProductListingRequest.getToCreateDate();
        if(StringUtils.isNotBlank(toCreateDate)){
            boolQueryBuilder.must(QueryBuilders.rangeQuery("createTime").lte(toCreateDate));
        }

        String fromViewUpdateDate = esAliexpressProductListingRequest.getFromViewUpdateDate();
        if(StringUtils.isNotBlank(fromViewUpdateDate)){
            boolQueryBuilder.must(QueryBuilders.rangeQuery("viewUpdateDate").gte(fromViewUpdateDate));
        }

        String toViewUpdateDate = esAliexpressProductListingRequest.getToViewUpdateDate();
        if(StringUtils.isNotBlank(toViewUpdateDate)){
            boolQueryBuilder.must(QueryBuilders.rangeQuery("viewUpdateDate").lte(toViewUpdateDate));
        }

        Boolean viewUpdateDateIsNull = esAliexpressProductListingRequest.getViewUpdateDateIsNull();
        if(viewUpdateDateIsNull != null && viewUpdateDateIsNull){
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("viewUpdateDate"));
        }

        Boolean accountIsNull = esAliexpressProductListingRequest.getAccountIsNull();
        if(accountIsNull != null && accountIsNull){
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("aliexpressAccountNumber"));
        }

        Boolean order_last_180d_countIsNull = esAliexpressProductListingRequest.getOrder_last_180d_countIsNull();
        if(order_last_180d_countIsNull != null && order_last_180d_countIsNull){
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("order_last_180d_count_new"));
        }

        // 上架时间超过90天，且180天内无销量链接
        Boolean createDateOver90dCountIsNull180d = esAliexpressProductListingRequest.getCreateDateOver90dCountIsNull180d();
        if (createDateOver90dCountIsNull180d != null && createDateOver90dCountIsNull180d) {
            Date date = com.estone.erp.common.util.DateUtils.addDays(new Date(), -90);
            String createDate = com.estone.erp.common.util.DateUtils.format(date, "yyyy-MM-dd HH:mm:ss");
            boolQueryBuilder.must(QueryBuilders.rangeQuery("gmtCreate").lte(createDate));

            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            BoolQueryBuilder boolQuery1 = QueryBuilders.boolQuery();
            boolQuery1.must(QueryBuilders.matchQuery("order_last_180d_count_new", 0));
            boolQuery.should(boolQuery1);
            BoolQueryBuilder boolQuery2 = QueryBuilders.boolQuery();
            boolQuery2.mustNot(QueryBuilders.existsQuery("order_last_180d_count_new"));
            boolQuery.should(boolQuery2);
            boolQueryBuilder.must(boolQuery);
        }

        Boolean gtmCtreateDateIsNull = esAliexpressProductListingRequest.getGtmCtreateDateIsNull();
        if(gtmCtreateDateIsNull != null && gtmCtreateDateIsNull){
            if(gtmCtreateDateIsNull != null && gtmCtreateDateIsNull){
                boolQueryBuilder.mustNot(QueryBuilders.existsQuery("gmtCreate"));
            }
        }

        String fromGmtCreateDate = esAliexpressProductListingRequest.getFromGmtCreateDate();
        if(StringUtils.isNotBlank(fromGmtCreateDate)){
            boolQueryBuilder.must(QueryBuilders.rangeQuery("gmtCreate").gte(fromGmtCreateDate));
        }
        String toGmtCreateDate = esAliexpressProductListingRequest.getToGmtCreateDate();
        if(StringUtils.isNotBlank(toGmtCreateDate)){
            boolQueryBuilder.must(QueryBuilders.rangeQuery("gmtCreate").lte(toGmtCreateDate));
        }

        String fromGmtModifiedDate = esAliexpressProductListingRequest.getFromGmtModifiedDate();
        if(StringUtils.isNotBlank(fromGmtModifiedDate)){
            boolQueryBuilder.must(QueryBuilders.rangeQuery("gmtModified").gte(fromGmtModifiedDate));
        }

        String toGmtModifiedDate = esAliexpressProductListingRequest.getToGmtModifiedDate();
        if(StringUtils.isNotBlank(toGmtModifiedDate)){
            boolQueryBuilder.must(QueryBuilders.rangeQuery("gmtModified").lte(toGmtModifiedDate));
        }

        String fromLastSynchDate = esAliexpressProductListingRequest.getFromLastSynchDate();
        if(StringUtils.isNotBlank(fromLastSynchDate)){
            boolQueryBuilder.must(QueryBuilders.rangeQuery("lastSyncTime").gte(fromLastSynchDate));
        }

        String toLastSynchDate = esAliexpressProductListingRequest.getToLastSynchDate();
        if(StringUtils.isNotBlank(toLastSynchDate)){
            boolQueryBuilder.must(QueryBuilders.rangeQuery("lastSyncTime").lte(toLastSynchDate));
        }

        String subjectLike = esAliexpressProductListingRequest.getSubjectLike();
        if(StringUtils.isNotBlank(subjectLike)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("subject", "*" + subjectLike + "*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        Integer categoryId = esAliexpressProductListingRequest.getCategoryId();
        if(categoryId != null) {
            boolQueryBuilder.must(QueryBuilders.matchQuery("categoryId", categoryId));
        }

        List<Integer> categoryIdList = esAliexpressProductListingRequest.getCategoryIdList();
        if(CollectionUtils.isNotEmpty(categoryIdList)){
            boolQueryBuilder.must(QueryBuilders.termsQuery("categoryId", categoryIdList));
        }

        String categoryIdStr = esAliexpressProductListingRequest.getCategoryIdStr();
        if(StringUtils.isNotBlank(categoryIdStr)){
            boolQueryBuilder.must(QueryBuilders.termsQuery("categoryId", CommonUtils.splitIntList(categoryIdStr, ",")));
        }

        List<Integer> notInCategoryIdList = esAliexpressProductListingRequest.getNotInCategoryIdList();
        if (CollectionUtils.isNotEmpty(notInCategoryIdList)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("categoryId", notInCategoryIdList));
        }

        Double fromGrossWeight = esAliexpressProductListingRequest.getFromGrossWeight();
        if(fromGrossWeight != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("grossWeight").gte(fromGrossWeight));
        }

        Double toGrossWeight = esAliexpressProductListingRequest.getToGrossWeight();
        if(toGrossWeight != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("grossWeight").lte(toGrossWeight));

        }
        //产品标签
        String tagCodes = esAliexpressProductListingRequest.getTagCodes();
        if (StringUtils.isNotEmpty(tagCodes)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            if(StringUtils.contains(tagCodes, ",")){
                List<String> tagCodeList = CommonUtils.splitList(tagCodes, ",");
                for (String tagCode : tagCodeList) {
                    boolQuery.should(QueryBuilders.wildcardQuery("tagCodes", "*," + tagCode + ",*"));
                }
                boolQuery.minimumShouldMatch(1);
                boolQueryBuilder.must(boolQuery);
            }else{
                boolQuery.should(QueryBuilders.wildcardQuery("tagCodes", "*," + tagCodes + ",*"));
                boolQuery.minimumShouldMatch(1);
                boolQueryBuilder.must(boolQuery);
            }

            boolQuery.should(QueryBuilders.wildcardQuery("tagCodes", "*," + tagCodes + ",*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        //产品标签
        String tagNames = esAliexpressProductListingRequest.getTagNames();
        if (StringUtils.isNotEmpty(tagNames)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            List<String> tagNamesList = CommonUtils.splitList(tagNames, ",");
            for (String tagName : tagNamesList) {
                boolQuery.should(QueryBuilders.wildcardQuery("tagNames", "*," + tagName + ",*"));
            }
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        List<String> tagNamesList = esAliexpressProductListingRequest.getTagNamesList();
        if (CollectionUtils.isNotEmpty(tagNamesList)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String tagName : tagNamesList) {
                boolQuery.should(QueryBuilders.wildcardQuery("tagNames", "*," + tagName + ",*"));
            }
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        //特殊标签
        String specialGoodsCode = esAliexpressProductListingRequest.getSpecialGoodsCode();
        if (StringUtils.isNotEmpty(specialGoodsCode)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            List<String> strings = CommonUtils.splitList(specialGoodsCode, ",");
            for (String string : strings) {
                boolQuery.should(QueryBuilders.wildcardQuery("specialGoodsCode", "*," + string + ",*"));
            }
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        //特殊标签
        String specialGoodsName = esAliexpressProductListingRequest.getSpecialGoodsName();
        if (StringUtils.isNotEmpty(specialGoodsName)) {
            List<String> strings = CommonUtils.splitList(specialGoodsName, ",");
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String string : strings) {
                boolQuery.should(QueryBuilders.wildcardQuery("specialGoodsName", "*," + string + ",*"));
            }
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        //是否存在禁售平台
        String forbidChannel = esAliexpressProductListingRequest.getForbidChannel();
        if (StringUtils.isNotEmpty(forbidChannel)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            List<String> forbidChannelList = CommonUtils.splitList(forbidChannel, ",");
            for (String bidChannel : forbidChannelList) {
                boolQuery.should(QueryBuilders.wildcardQuery("forbidChannel", "*" + bidChannel + "*"));
            }
            boolQuery.should(QueryBuilders.wildcardQuery("forbidChannel", "*," + forbidChannel + ",*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }
        List<String> forbidChannelList = esAliexpressProductListingRequest.getForbidChannelList();
        if (CollectionUtils.isNotEmpty(forbidChannelList)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String bidChannel : forbidChannelList) {
                boolQuery.should(QueryBuilders.wildcardQuery("forbidChannel", "*" + bidChannel + "*"));
            }
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        // 禁售类型
        List<String> infringementTypeNames = esAliexpressProductListingRequest.getInfringementTypeNames();
        if (CollectionUtils.isNotEmpty(infringementTypeNames)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("infringementTypeNames", infringementTypeNames));
        }

        // 禁售原因
        List<String> infringementObjs = esAliexpressProductListingRequest.getInfringementObjs();
        if (CollectionUtils.isNotEmpty(infringementObjs)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("infringementObjs", infringementObjs));
        }

        // 禁售站点
        List<String> prohibitionSites = esAliexpressProductListingRequest.getProhibitionSites();
        if (CollectionUtils.isNotEmpty(prohibitionSites)) {
            // 禁售平台不为空就走过滤，否则模糊查询
            if (CollectionUtils.isNotEmpty(forbidChannelList)) {
                List<String> channelSites = new ArrayList<>();
                for (String channel : forbidChannelList) {
                    List<String> sites = prohibitionSites.stream()
                            .filter(StringUtils::isNotBlank)
                            .map(site -> channel + "_" + site)
                            .collect(Collectors.toList());
                    channelSites.addAll(sites);
                }
                if (CollectionUtils.isNotEmpty(channelSites)) {
                    boolQueryBuilder.must(QueryBuilders.termsQuery("prohibitionSites", channelSites));
                }
            }else {
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                for (String site : prohibitionSites) {
                    boolQuery.should(QueryBuilders.wildcardQuery("prohibitionSites", "*" + site));
                }
                boolQuery.minimumShouldMatch(1);
                boolQueryBuilder.must(boolQuery);
            }
        }

        // 单品状态
        String skuStatus = esAliexpressProductListingRequest.getSkuStatus();
        if (StringUtils.isNotBlank(skuStatus)) {
            List<String> skuStatusList = CommonUtils.splitList(skuStatus, ",");
            boolQueryBuilder.must(QueryBuilders.termsQuery("skuStatus", skuStatusList));
        }
        List<String> skuStatusList = esAliexpressProductListingRequest.getSkuStatusList();
        if (CollectionUtils.isNotEmpty(skuStatusList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("skuStatus", skuStatusList));
        }
        //not in
        List<String> notInSkuStatusList = esAliexpressProductListingRequest.getNotInSkuStatusList();
        if(CollectionUtils.isNotEmpty(notInSkuStatusList)){
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("skuStatus", notInSkuStatusList));
        }

        //24小时销量
        Integer from_order_24H_count = esAliexpressProductListingRequest.getFrom_order_24H_count();
        Integer to_order_24H_count = esAliexpressProductListingRequest.getTo_order_24H_count();
        if(from_order_24H_count != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("order_24H_count").gte(from_order_24H_count));
        }
        if(to_order_24H_count != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("order_24H_count").lte(to_order_24H_count));
        }

        //7天销量
        Integer from_order_last_7d_count = esAliexpressProductListingRequest.getFrom_order_last_7d_count();
        Integer to_order_last_7d_count = esAliexpressProductListingRequest.getTo_order_last_7d_count();
        if(from_order_last_7d_count != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("order_last_7d_count").gte(from_order_last_7d_count));
        }
        if(to_order_last_7d_count != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("order_last_7d_count").lte(to_order_last_7d_count));
        }

        //14天
        Integer from_order_last_14d_count = esAliexpressProductListingRequest.getFrom_order_last_14d_count();
        Integer to_order_last_14d_count = esAliexpressProductListingRequest.getTo_order_last_14d_count();
        if(from_order_last_14d_count != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("order_last_14d_count").gte(from_order_last_14d_count));
        }
        if(to_order_last_14d_count != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("order_last_14d_count").lte(to_order_last_14d_count));
        }

        //30天
        Integer from_order_last_30d_count = esAliexpressProductListingRequest.getFrom_order_last_30d_count();
        Integer to_order_last_30d_count = esAliexpressProductListingRequest.getTo_order_last_30d_count();
        if(from_order_last_30d_count != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("order_last_30d_count").gte(from_order_last_30d_count));
        }
        if(to_order_last_30d_count != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("order_last_30d_count").lte(to_order_last_30d_count));
        }

        //60天
        Integer from_order_last_60d_count = esAliexpressProductListingRequest.getFrom_order_last_60d_count();
        Integer to_order_last_60d_count = esAliexpressProductListingRequest.getTo_order_last_60d_count();
        if(from_order_last_60d_count != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("order_last_60d_count").gte(from_order_last_60d_count));
        }
        if(to_order_last_60d_count != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("order_last_60d_count").lte(to_order_last_60d_count));
        }

        //60天动销率
        Double from_order_days_within_60d_rate = esAliexpressProductListingRequest.getFrom_order_days_within_60d_rate();
        Double to_order_days_within_60d_rate = esAliexpressProductListingRequest.getTo_order_days_within_60d_rate();
        if(from_order_days_within_60d_rate != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("order_days_within_60d_rate").gte(from_order_days_within_60d_rate));
        }
        if(to_order_days_within_60d_rate != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("order_days_within_60d_rate").lte(to_order_days_within_60d_rate));
        }

        //180天
        Integer from_order_last_180d_count = esAliexpressProductListingRequest.getFrom_order_last_180d_count();
        Integer to_order_last_180d_count = esAliexpressProductListingRequest.getTo_order_last_180d_count();
        if(from_order_last_180d_count != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("order_last_180d_count_new").gte(from_order_last_180d_count));
        }
        if(to_order_last_180d_count != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("order_last_180d_count_new").lte(to_order_last_180d_count));
        }

        // 库存扣减方式
        String reduceStrategy = esAliexpressProductListingRequest.getReduceStrategy();
        if (StringUtils.isNotBlank(reduceStrategy)) {
            boolQueryBuilder.must(QueryBuilders.matchQuery("reduceStrategy", reduceStrategy));
        }

        // 数据类型
        Long dataSourceType = esAliexpressProductListingRequest.getDataSourceType();
        if (dataSourceType != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("dataSourceType", dataSourceType));
        }

        List<Long> notInDataSourceTypeList = esAliexpressProductListingRequest.getNotInDataSourceTypeList();
        if(CollectionUtils.isNotEmpty(notInDataSourceTypeList)){
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("dataSourceType", notInDataSourceTypeList));
        }

        // 组合状态
        Integer composeStatus = esAliexpressProductListingRequest.getComposeStatus();
        if (composeStatus != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("composeStatus", composeStatus));
        }

        // 品牌
        if (StringUtils.isNotBlank(esAliexpressProductListingRequest.getBrandEnNameLike())) {
            BoolQueryBuilder brandBool = QueryBuilders.boolQuery();
            brandBool.should(QueryBuilders.wildcardQuery("brand.en.keyword", "*" + esAliexpressProductListingRequest.getBrandEnNameLike() + "*"));
            brandBool.minimumShouldMatch(1);

            NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("brand", brandBool, ScoreMode.Total);
            boolQueryBuilder.filter(nestedQuery);
        }

        //30天浏览量
        Integer fromView30dCount = esAliexpressProductListingRequest.getFrom_view_30d_count();
        Integer toView30dCount = esAliexpressProductListingRequest.getTo_view_30d_count();
        if(fromView30dCount != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("view_30d_count").gte(fromView30dCount));
        }
        if(toView30dCount != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("view_30d_count").lte(toView30dCount));
        }

        //30天曝光量
        Integer fromExposure30dCount = esAliexpressProductListingRequest.getFrom_exposure_30d_count();
        Integer toExposure30dCount = esAliexpressProductListingRequest.getTo_exposure_30d_count();
        if(fromExposure30dCount != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("exposure_30d_count").gte(fromExposure30dCount));
        }
        if(toExposure30dCount != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("exposure_30d_count").lte(toExposure30dCount));
        }

        //7天浏览量
        Integer fromView7dCount = esAliexpressProductListingRequest.getFrom_view_7d_count();
        Integer toView7dCount = esAliexpressProductListingRequest.getTo_view_7d_count();
        if(fromView7dCount != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("view_7d_count").gte(fromView7dCount));
        }
        if(toView7dCount != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("view_7d_count").lte(toView7dCount));
        }

        //7天曝光量
        Integer fromExposure7dCount = esAliexpressProductListingRequest.getFrom_exposure_7d_count();
        Integer toExposure7dCount = esAliexpressProductListingRequest.getTo_exposure_7d_count();
        if(fromExposure7dCount != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("exposure_7d_count").gte(fromExposure7dCount));
        }
        if(toExposure7dCount != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("exposure_7d_count").lte(toExposure7dCount));
        }

        //14天浏览量
        Integer fromView14dCount = esAliexpressProductListingRequest.getFrom_view_14d_count();
        Integer toView14dCount = esAliexpressProductListingRequest.getTo_view_14d_count();
        if(fromView14dCount != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("view_14d_count").gte(fromView14dCount));
        }
        if(toView14dCount != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("view_14d_count").lte(toView14dCount));
        }

        //14天曝光量
        Integer fromExposure14dCount = esAliexpressProductListingRequest.getFrom_exposure_14d_count();
        Integer toExposure14dCount = esAliexpressProductListingRequest.getTo_exposure_14d_count();
        if(fromExposure14dCount != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("exposure_14d_count").gte(fromExposure14dCount));
        }
        if(toExposure14dCount != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("exposure_14d_count").lte(toExposure14dCount));
        }

        //产品在线状态
        if(!Objects.isNull(esAliexpressProductListingRequest.getOnlineStatus())){
            if(!OnlineStatusEnum.ALL.getCode().equals(esAliexpressProductListingRequest.getOnlineStatus())){
                boolQueryBuilder.must(QueryBuilders.matchQuery("onlineStatus", esAliexpressProductListingRequest.getOnlineStatus() ));
            }
        }

        //联盟列表条件

        //移除的产品分组
        List<Long> removeGroupList = esAliexpressProductListingRequest.getRemoveGroupList();
        if(!CollectionUtils.isEmpty(removeGroupList)){
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("groupId", removeGroupList));
        }

        // 侵权词详情对象
        BoolQueryBuilder infringementWordBool = QueryBuilders.boolQuery();
        if(StringUtils.isNotBlank(esAliexpressProductListingRequest.getInfringementWordStr())) {
            infringementWordBool.must(QueryBuilders.termQuery("infringementWordInfos.word", StringUtils.toRootLowerCase(esAliexpressProductListingRequest.getInfringementWordStr())));
//            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//            List<String> strings = CommonUtils.splitList(esAliexpressProductListingRequest.getInfringementWordStr(), ",");
//            for (String string : strings) {
//                boolQuery.should(QueryBuilders.wildcardQuery("infringementWordInfos.word", StringUtils.toRootLowerCase(string)));
//            }
//            boolQuery.minimumShouldMatch(1);
//            infringementWordBool.must(boolQuery);
        }

        if(StringUtils.isNotBlank(esAliexpressProductListingRequest.getInfringementWordType())) {
            infringementWordBool.must(QueryBuilders.termQuery("infringementWordInfos.type", esAliexpressProductListingRequest.getInfringementWordType()));
        }

        if(CollectionUtils.isNotEmpty(esAliexpressProductListingRequest.getInfringementWordIds())) {
            infringementWordBool.must(QueryBuilders.termsQuery("infringementWordInfos.trademarkIdentification", esAliexpressProductListingRequest.getInfringementWordIds()));
        }

        if (CollectionUtils.isNotEmpty(infringementWordBool.filter())
                || CollectionUtils.isNotEmpty(infringementWordBool.must())
                || CollectionUtils.isNotEmpty(infringementWordBool.should())
                || CollectionUtils.isNotEmpty(infringementWordBool.mustNot()) ) {
            NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("infringementWordInfos", infringementWordBool, ScoreMode.Total);
            boolQueryBuilder.filter(nestedQuery);
        }
        if (esAliexpressProductListingRequest.getGteProductId() != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("productId").gte(esAliexpressProductListingRequest.getGteProductId()));
        }
        if (esAliexpressProductListingRequest.getAdminToProductFlag() != null) {
            if (esAliexpressProductListingRequest.getAdminToProductFlag()) {
                boolQueryBuilder.must(QueryBuilders.termQuery("adminToProductFlag", true));
            } else {
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                boolQuery.should(QueryBuilders.termQuery("adminToProductFlag", false));
                boolQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("adminToProductFlag")));
                boolQueryBuilder.must(boolQuery);
            }
        }
        if (CollectionUtils.isNotEmpty(esAliexpressProductListingRequest.getAliexpressAccountNumberList())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("aliexpressAccountNumber", esAliexpressProductListingRequest.getAliexpressAccountNumberList()));
        }
    }

    @Override
    public Page<EsAliexpressProductListing> page(
            EsAliexpressProductListingRequest esAliexpressProductListingRequest, int pageSize, int pageIndex) {

        if(esAliexpressProductListingRequest == null){
            return null;
        }
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        setQuery(esAliexpressProductListingRequest, boolQueryBuilder);

        //排序
        String orderby = esAliexpressProductListingRequest.getOrderBy();
        if (StringUtils.isEmpty(orderby)) {
            orderby = "gmtCreate";
        }

        //创建查询条件构造器
        queryBuilder.withQuery(boolQueryBuilder);
        //构建分页
        // 每页条数
        pageSize = pageSize == 0 ? 10 : pageSize;
        //es的分页的页码从0开始
        pageIndex = pageIndex < 1 ? 0 : pageIndex;

        String sequence = esAliexpressProductListingRequest.getSequence();
        if (StringUtils.isEmpty(sequence) || sequence.equalsIgnoreCase("DESC")) {
            queryBuilder.withSort(SortBuilders.fieldSort(orderby).order(SortOrder.DESC))
                    .withSort(SortBuilders.fieldSort("productId").order(SortOrder.DESC));
        } else {
            queryBuilder.withSort(SortBuilders.fieldSort(orderby).order(SortOrder.ASC))
                    .withSort(SortBuilders.fieldSort("productId").order(SortOrder.ASC));
        }

        queryBuilder.withFields(esAliexpressProductListingRequest.getPageFields()).withPageable(PageRequest.of(pageIndex, pageSize));
        NativeSearchQuery searchQuery = queryBuilder.build();
        searchQuery.setTrackTotalHits(true);

        Page<EsAliexpressProductListing> results = esAliexpressProductListingRepository.search(searchQuery);
        return results;
    }

    @Override
    public List<EsAliexpressProductListing> getEsAliexpressProductListing(
            EsAliexpressProductListingRequest esAliexpressProductListingRequest) {
        if(esAliexpressProductListingRequest == null){
            return null;
        }

        List<EsAliexpressProductListing> esAliexpressProductListing = new ArrayList<>();

        // 查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        setQuery(esAliexpressProductListingRequest, boolQueryBuilder);

        String[] queryFields = esAliexpressProductListingRequest.getQueryFields();
        if(queryFields == null){
            queryBuilder.withQuery(boolQueryBuilder)
                    .withSort(SortBuilders.fieldSort("createTime").order(SortOrder.ASC));
        }else{
            queryBuilder.withQuery(boolQueryBuilder)
                    .withFields(queryFields)
                    .withSort(SortBuilders.fieldSort("createTime").order(SortOrder.ASC));
        }

        //创建查询条件构造器
        NativeSearchQuery searchQuery = queryBuilder.withPageable(PageRequest.of(0, esAliexpressProductListingRequest.getPageSize())).build();
        searchQuery.setTrackTotalHits(true);
       /* int i = 0;
        long start1 = System.currentTimeMillis();
        ScrolledPage<EsAliexpressProductListing> scroll = (ScrolledPage<EsAliexpressProductListing>) elasticsearchRestTemplate2
                .startScroll(1000*60*5, searchQuery, EsAliexpressProductListing.class);
        long o1 = System.currentTimeMillis() - start1;
        if(o1 > 2000L){
            log.info("查询ES->esAiexpressProductListing第{}页耗时{}ms", i, o1);
        }
        while (scroll.hasContent()) {
            i++;
            esAliexpressProductListing.addAll(scroll.getContent());
            long start2 = System.currentTimeMillis();
            scroll = (ScrolledPage<EsAliexpressProductListing>) elasticsearchRestTemplate2.continueScroll(scroll.getScrollId(), 1000*60*5,
                    EsAliexpressProductListing.class);
            long o11 = System.currentTimeMillis() - start2;
            if(o11 > 2000L){
                log.info("查询ES->esAiexpressProductListing第 {}页耗时{}ms", i, o11);
            }
        }
        // 最后释放查询
        c2ElasticsearchTemplate.clearScroll(scroll.getScrollId());*/

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        esAliexpressProductListing = ElasticSearchHelper.
                scrollQuery(elasticsearchRestTemplate2, 10 * 60 * 1000,
                        searchQuery, EsAliexpressProductListing.class,
                        aliexpressProductListingIndexCoordinates);
        stopWatch.stop();
        long totalTimeMillis = stopWatch.getTotalTimeMillis();
        if(totalTimeMillis > 5000L){
            log.warn("查询ES->esAiexpressProductListing第 条数{}耗时{}ms", esAliexpressProductListing.size(), totalTimeMillis);
        }
        return esAliexpressProductListing;
    }

    @Override
    public EsAliexpressProductListing findAllByProductIdAndSkuId(Long productId, String skuId){
        Assert.notNull(productId);
//        Assert.notNull(articleNumber);

        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setProductId(productId);
        request.setSkuId(skuId);

        // 查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        setQuery(request, boolQueryBuilder);
        queryBuilder.withQuery(boolQueryBuilder);

        NativeSearchQuery searchQuery = queryBuilder.build();
        searchQuery.setTrackTotalHits(true);
        Page<EsAliexpressProductListing> search = esAliexpressProductListingRepository.search(searchQuery);
        if(null != search){
            List<EsAliexpressProductListing> content = search.getContent();
            if(CollectionUtils.isNotEmpty(content)){
                return content.get(0);
            }
        }
        return null;
    }

    @Override
    public List<SkuPublishListingNum> getSmtListingNum(List<String> articleNumberList) {
        if (CollectionUtils.isEmpty(articleNumberList)) {
            return null;
        }
        long now = System.currentTimeMillis();
        List<SkuPublishListingNum> skuPublishListingNumList = new ArrayList<>();
        for (String sku : articleNumberList) {
            NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("articleNumber", sku.toUpperCase()))
                    .must(QueryBuilders.matchQuery("productStatusType", "onSelling"))
                    .must(QueryBuilders.matchQuery("onlineStatus", OnlineStatusEnum.ONLINE.getCode() ));//新增默认查询产品在线状态

            queryBuilder
                    .withQuery(boolQueryBuilder)
                    .withPageable(PageRequest.of(0, 10000))
                    .withFields("articleNumber", "gmtCreate")
                    .withSort(SortBuilders.fieldSort("gmtCreate").order(SortOrder.ASC))
                    .build();
            NativeSearchQuery searchQuery = queryBuilder.build();
            searchQuery.setTrackTotalHits(true);
            Page<EsAliexpressProductListing> search = esAliexpressProductListingRepository.search(searchQuery);
            if (null != search && CollectionUtils.isNotEmpty(search.getContent())) {
                EsAliexpressProductListing esAliexpressProductListing = search.getContent().get(0);
                SkuPublishListingNum skuPublishListingNum = new SkuPublishListingNum();
                skuPublishListingNum.setArticleNumber(sku);
                skuPublishListingNum.setSmtNum(search.getContent().size());
                Timestamp smtEarliestTime = null;
                if (null != esAliexpressProductListing.getGmtCreate()) {
                    smtEarliestTime = new Timestamp(esAliexpressProductListing.getGmtCreate().getTime());
                }
                skuPublishListingNum.setSmtEarliestTime(smtEarliestTime);
                skuPublishListingNumList.add(skuPublishListingNum);
            }
        }
        long timeES = System.currentTimeMillis() - now;
        log.info("/publishListing/getSkuListingNum查询ES->aliexpress_product_listing,耗时->{}ms,sku数量->{}", timeES, articleNumberList.size());
        return skuPublishListingNumList;
    }

    @Override
    public List<PlatformListingInfo> getSmtListing(List<String> articleNumberList) {
        if (CollectionUtils.isEmpty(articleNumberList) || articleNumberList.size() > 1000) {
            return null;
        }
        long now = System.currentTimeMillis();
        List<PlatformListingInfo> platformListingInfos = new ArrayList<>();

        // 查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.matchQuery("productStatusType", "onSelling"));

        //货号
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        for (String sku : articleNumberList) {
            boolQuery.must(QueryBuilders.matchQuery("articleNumber", sku.toUpperCase()));
        }
        //新增默认查询产品在线状态
        boolQuery.must(QueryBuilders.matchQuery("onlineStatus", OnlineStatusEnum.ONLINE.getCode() ));
        boolQueryBuilder.must(boolQuery);

        queryBuilder
                .withQuery(boolQueryBuilder)
                .withPageable(PageRequest.of(0, 10000))
                .withFields("articleNumber", "skuCode", "aliexpressAccountNumber", "gmtCreate", "wsOfflineDate")
                .build();
        NativeSearchQuery searchQuery = queryBuilder.build();
        searchQuery.setTrackTotalHits(true);
        Page<EsAliexpressProductListing> search = esAliexpressProductListingRepository.search(searchQuery);
        if (null != search && CollectionUtils.isNotEmpty(search.getContent())) {
            for (EsAliexpressProductListing esProduct : search.getContent()) {
                PlatformListingInfo platformListingInfo = new PlatformListingInfo();
                platformListingInfo.setPlatform("Smt");
                platformListingInfo.setArticleNumber(esProduct.getArticleNumber());
                platformListingInfo.setSellerSku(esProduct.getSkuCode());
                platformListingInfo.setAccountNumber(esProduct.getAliexpressAccountNumber());
                Timestamp smtEarliestTime = null;
                if (null != esProduct.getGmtCreate()) {
                    smtEarliestTime = new Timestamp(esProduct.getGmtCreate().getTime());
                }
                platformListingInfo.setPublishDate(smtEarliestTime);
                if (null != esProduct.getWsOfflineDate()) {
                    platformListingInfo.setOfflineDate(new Timestamp(esProduct.getWsOfflineDate().getTime()));
                }
                platformListingInfos.add(platformListingInfo);
            }
        }
        long timeES = System.currentTimeMillis() - now;
        log.info("/publishListing/getListingInfoBySku查询ES->aliexpress_product_listing,耗时->{}ms,sku数量->{}", timeES, articleNumberList.size());
        return platformListingInfos;
    }

    @Override
    public boolean checkIsSkuHavePublished(String accountNumber,List<String> skuCodeList){
        if(StringUtils.isEmpty(accountNumber) || CollectionUtils.isEmpty(skuCodeList)){
            return false;
        }
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setProductStatusType("onSelling,auditing");
        request.setAliexpressAccountNumber(accountNumber);
        request.setArticleNumberStr(StringUtils.join(skuCodeList, ","));
        request.setQueryFields(new String[]{"id"});
        List<EsAliexpressProductListing> esAliexpressProductListing = this.getEsAliexpressProductListing(request);
        if(CollectionUtils.isNotEmpty(esAliexpressProductListing)){
            return true;
        }
        log.info("重复刊登查询,店铺{},货号{}", accountNumber, StringUtils.join(skuCodeList, ","));
        return false;
    }

    @Override
    public void updateMissingProductInfoListing() {
        Map<String, ProductInfoVO> map = new HashMap<>(20000);
        EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
        request.setQueryFields(null);
        request.setSkuStatusIsNull(true);
        request.setQueryFields(new String[]{"id"});
        List<EsAliexpressProductListing> res = getEsAliexpressProductListing(request);

        XxlJobLogger.log("当前更新数量：{}", res.size());
            res.forEach(listing -> {
                try {
                    EsAliexpressProductListing esAliexpressProductListing = findAllById(listing.getId());
                    if (ObjectUtils.isEmpty(esAliexpressProductListing) ||StringUtils.isEmpty(esAliexpressProductListing.getArticleNumber())){
                        return;
                    }
                    String articleNumber = esAliexpressProductListing.getArticleNumber();
                    ProductInfoVO productInfoVO = map.get(articleNumber);
                    if (ObjectUtils.isEmpty(productInfoVO)) {
                        productInfoVO = ProductUtils.getSkuInfo(esAliexpressProductListing.getArticleNumber());
                        map.put(esAliexpressProductListing.getArticleNumber(), productInfoVO);
                    }
                    if (!ObjectUtils.isEmpty(productInfoVO)) {
                        esAliexpressProductListing.setSpu(productInfoVO.getMainSku());
                        //禁售平台(逗号拼接)
                        esAliexpressProductListing.setForbidChannel(productInfoVO.getForbidChannel());
                        //单品状态
                        esAliexpressProductListing.setSkuStatus(productInfoVO.getSkuStatus());
                        //产品标签code
                        esAliexpressProductListing.setTagCodes(productInfoVO.getTagCodes());
                        //产品标签
                        esAliexpressProductListing.setTagNames(productInfoVO.getTagNames());
                        //特殊标签
                        esAliexpressProductListing.setSpecialGoodsCode(productInfoVO.getSpecialGoodsCode());
                        //特殊标签
                        esAliexpressProductListing.setSpecialGoodsName(productInfoVO.getSpecialGoodsName());
                        //类别id
                        esAliexpressProductListing.setProCategoryId(productInfoVO.getCategoryId());
                        //类别中文名
                        esAliexpressProductListing.setProCategoryCnName(productInfoVO.getCategoryCnName());
                        esAliexpressProductListing.setLastEditTime(new Date());
                        save(esAliexpressProductListing);
                    } else {
                        log.info("当前sku：{} 未查询到数据", articleNumber);
                    }
                } catch (Exception e) {
                    log.error("循环报错：", e);
                }
            });
        }

    @Override
    public Set<Integer> listAccountBrandCategory(String accountNumber) {
        Set<Integer> categorySet = Sets.newHashSet();
        int page = 0;
        int size = 1000;
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("aliexpressAccountNumber", accountNumber));
        //新增默认查询产品在线状态
        boolQueryBuilder.must(QueryBuilders.matchQuery("onlineStatus", OnlineStatusEnum.ONLINE.getCode() ));
        while (true) {
            int start = page * size;
            /*SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                    .query(boolQueryBuilder)
                    .collapse(new CollapseBuilder("categoryId"))
                    .sort(SortBuilders.fieldSort("createTime").order(SortOrder.ASC))
                    .from(start)
                    .size(size)
                    .fetchSource(new String[]{"categoryId"}, null);
            SearchResponse response = elasticsearchRestTemplate2.suggest(searchRequest)
                    .setSource(sourceBuilder)
                    .get();

            SearchHit[] hits = response.getHits().getHits();
            for (SearchHit hit : hits) {
                Map<String, Object> source = hit.getSource();
                Integer categoryId = MapUtils.getInteger(source, "categoryId");
                if (categoryId != null) {
                    categorySet.add(categoryId);
                }
            }*/

            NativeSearchQueryBuilder query = new NativeSearchQueryBuilder()
                    .withQuery(boolQueryBuilder)
                    .withFields("categoryId")
                    .withCollapseField("categoryId")
                    .withSort(SortBuilders.fieldSort("createTime").order(SortOrder.ASC))
                    .withPageable(PageRequest.of(start,size));
            NativeSearchQuery nativeSearchQuery = query.build();
            nativeSearchQuery.setTrackTotalHits(true);
            SearchHits<EsAliexpressProductListing> searchHits = elasticsearchRestTemplate2.search(nativeSearchQuery, EsAliexpressProductListing.class);
            if (searchHits.getSearchHits().size() == 0) {
                break;
            }
            for (org.springframework.data.elasticsearch.core.SearchHit<EsAliexpressProductListing> searchHit : searchHits) {
                if (null != searchHit.getContent() && null != searchHit.getContent().getCategoryId()) {
                    categorySet.add(searchHit.getContent().getCategoryId());
                }
            }
            page++;
        }
        return categorySet;
    }

    @Override
    public Set<String> getAllAccountSet(){
        Set<String> accountSet = new HashSet<>();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        //按账号分组
        String[] fields = {"aliexpressAccountNumber"};
        TermsAggregationBuilder createdByBuilder = AggregationBuilders.terms("aliexpressAccountNumber_aggs").field("aliexpressAccountNumber").size((1<<31)-1)
                .subAggregation(AggregationBuilders.topHits("top").fetchSource(fields, Strings.EMPTY_ARRAY).size(1));

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withFields(fields)
                .addAggregation(createdByBuilder)
                .withPageable(PageRequest.of(0, 1))
                .build();
        query.setTrackTotalHits(true);
        AggregatedPage<EsAliexpressProductListing> searchResult = (AggregatedPage) esAliexpressProductListingRepository.search(query);
        if (searchResult != null && searchResult.getAggregations() != null) {
            Terms terms = searchResult.getAggregations().get("aliexpressAccountNumber_aggs");
            if (terms != null) {
                for (Terms.Bucket bucket : terms.getBuckets()) {
                    accountSet.add(bucket.getKeyAsString());
                }
            }
        }
        return accountSet;
    }

    @Override
    public List<JSONObject> repeatProductByAccount(String account){

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        //在售状态
        String productStatusType = "onSelling,auditing";
        if(StringUtils.isNotBlank(productStatusType)){
            List<String> strings = CommonUtils.splitList(productStatusType, ",");
            boolQueryBuilder.must(QueryBuilders.termsQuery("productStatusType", strings));
        }

        boolQueryBuilder.must(QueryBuilders.termQuery("aliexpressAccountNumber", account));
		//新增默认查询产品在线状态
        boolQueryBuilder.must(QueryBuilders.matchQuery("onlineStatus", OnlineStatusEnum.ONLINE.getCode()));

        String[] fields = {"aliexpressAccountNumber", "productId", "skuCode", "articleNumber"};
        TermsAggregationBuilder createdByBuilder = AggregationBuilders.terms("skuCode_aggs").field("skuCode").size((1<<31)-1)
                .subAggregation(AggregationBuilders.topHits("top").fetchSource(fields, Strings.EMPTY_ARRAY).size(1));

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withFields(fields)
                .addAggregation(createdByBuilder)
                .withPageable(PageRequest.of(0, 1))
                .build();
        query.setTrackTotalHits(true);
        AggregatedPage<EsAliexpressProductListing> search = (AggregatedPage) esAliexpressProductListingRepository.search(query);
        List<JSONObject> jsonObjectList = new ArrayList<>();
        if (null != search) {
            Aggregations aggregations = search.getAggregations();
            if(aggregations == null){
                return jsonObjectList;
            }
            Map<String, Aggregation> asMap = aggregations.getAsMap();
            MultiBucketsAggregation term = (MultiBucketsAggregation) asMap.get("skuCode_aggs");
            if (null != term) {
                for (MultiBucketsAggregation.Bucket bucket : term.getBuckets()) {
                    String key = (String) bucket.getKey();
                    if (key == null || StringUtils.isBlank(key)) continue;
                    long docCount = bucket.getDocCount();
                    if(docCount > 1){
                        TopHits top = bucket.getAggregations().get("top");
                        for (SearchHit hit : top.getHits()){
                            Map<String, Object> fieldMap = hit.getSourceAsMap();
                            String aliexpressAccountNumber = fieldMap.get("aliexpressAccountNumber").toString();
                            String productId = fieldMap.get("productId").toString();
                            String skuCode = fieldMap.get("skuCode").toString();
                            String articleNumber = fieldMap.get("articleNumber").toString();
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("aliexpressAccountNumber", aliexpressAccountNumber);
                            jsonObject.put("productId", productId);
                            jsonObject.put("skuCode", skuCode);
                            jsonObject.put("articleNumber", articleNumber);
                            jsonObjectList.add(jsonObject);
                        }
                    }
                }
            }
        }
        return jsonObjectList;
    }

    @Override
    public List<JSONObject> repeatProductByAccountList(List<String> accountList){
        if(CollectionUtils.isEmpty(accountList)){
            return null;
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        //在售状态
        String productStatusType = "onSelling,auditing";
        if(StringUtils.isNotBlank(productStatusType)){
            List<String> strings = CommonUtils.splitList(productStatusType, ",");
            boolQueryBuilder.must(QueryBuilders.termsQuery("productStatusType", strings));
        }

        //新增默认查询产品在线状态
        boolQueryBuilder.must(QueryBuilders.matchQuery("onlineStatus", OnlineStatusEnum.ONLINE.getCode() ));
        boolQueryBuilder.must(QueryBuilders.termsQuery("aliexpressAccountNumber", accountList));

        //分组显示的字段
        String[] fields = {"aliexpressAccountNumber", "productId", "skuCode", "articleNumber"};
        TopHitsAggregationBuilder topBuilder = AggregationBuilders.topHits("top").fetchSource(fields, Strings.EMPTY_ARRAY).size(1);
        TermsAggregationBuilder articleNumberAggsBuilder = AggregationBuilders.terms("articleNumber_aggs").field("articleNumber").subAggregation(topBuilder).size((1<<31)-1);
        TermsAggregationBuilder aliexpressAccountNumberAggsBuilder = AggregationBuilders.terms("aliexpressAccountNumber_aggs").field("aliexpressAccountNumber").subAggregation(articleNumberAggsBuilder).size((1<<31)-1);

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withFields(fields)
                .addAggregation(aliexpressAccountNumberAggsBuilder)
                .withPageable(PageRequest.of(0, 1))
                .build();
        query.setTrackTotalHits(true);
        AggregatedPage<EsAliexpressProductListing> search = (AggregatedPage) esAliexpressProductListingRepository.search(query);
        List<JSONObject> jsonObjectList = new ArrayList<>();
        if (null != search) {
            Terms aliexpressAccountNumberTerms  = search.getAggregations().get("aliexpressAccountNumber_aggs");
            if(aliexpressAccountNumberTerms != null){
                for (Terms.Bucket accountBucket : aliexpressAccountNumberTerms.getBuckets()) {
                    String accountNumber = (String) accountBucket.getKey();
                    if(StringUtils.isBlank(accountNumber)) continue;
                    Terms articleNumberTerms = accountBucket.getAggregations().get("articleNumber_aggs");
                    if(null == articleNumberTerms) continue;

                    for (Terms.Bucket articleNumberBucket : articleNumberTerms.getBuckets()) {
                        String key = (String)articleNumberBucket.getKey();
                        if(StringUtils.isBlank(key)) continue;
                        long docCount = articleNumberBucket.getDocCount();
                        if(docCount > 1){
                            TopHits top = articleNumberBucket.getAggregations().get("top");
                            for (SearchHit hit : top.getHits()){
                                Map<String, Object> fieldMap = hit.getSourceAsMap();
                                String aliexpressAccountNumber = fieldMap.get("aliexpressAccountNumber").toString();
                                String productId = fieldMap.get("productId").toString();
                                String skuCode = fieldMap.get("skuCode").toString();
                                String articleNumber = fieldMap.get("articleNumber").toString();
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("aliexpressAccountNumber", aliexpressAccountNumber);
                                jsonObject.put("productId", productId);
                                jsonObject.put("skuCode", skuCode);
                                jsonObject.put("articleNumber", articleNumber);
                                jsonObjectList.add(jsonObject);
                            }
                        }
                    }
                }
            }
        }
        return jsonObjectList;
    }

    /**
     * 根据条件统计数量
     * @param boolQueryBuilder
     * @return
     */
    private int conditionCount(BoolQueryBuilder boolQueryBuilder){
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .addAggregation(AggregationBuilders.cardinality("total").field("productId"))
                .withPageable(PageRequest.of(0, 1))
                .build();
        SearchHits<EsAliexpressProductListing> search = elasticsearchRestTemplate2.search(query, EsAliexpressProductListing.class, aliexpressProductListingIndexCoordinates);
        Aggregations aggregations = search.getAggregations();
        Cardinality aggregation = aggregations.get("total");
        return (int) aggregation.getValue();
    }

    @Override
    public int getRangeTimeAddListingTotal(String accountNumber, String starTime, String endTime) {
        if (StringUtils.isBlank(accountNumber) || StringUtils.isBlank(starTime) || StringUtils.isBlank(endTime)) {
            return 0;
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("aliexpressAccountNumber", accountNumber));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("gmtCreate").gt(starTime).lt(endTime));
        boolQueryBuilder.must(QueryBuilders.termsQuery("productStatusType", Arrays.asList("onSelling", "auditing")));
        //新增默认查询产品在线状态
        boolQueryBuilder.must(QueryBuilders.matchQuery("onlineStatus", OnlineStatusEnum.ONLINE.getCode() ));
        return conditionCount(boolQueryBuilder);
    }

    @Override
    public int getForbiddenListingNum(List<String> accountNumberList){
        if(CollectionUtils.isEmpty(accountNumberList)){
            return 0;
        }
        List<List<String>> partition = Lists.partition(accountNumberList, 500);
        int total = 0;
        for (List<String> strings : partition) {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termsQuery("aliexpressAccountNumber", strings));
            boolQueryBuilder.must(QueryBuilders.termsQuery("productStatusType", Arrays.asList("onSelling", "auditing")));

            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("forbidChannel", "*," + SaleChannel.CHANNEL_SMT + ",*"));
            //新增默认查询产品在线状态
            boolQuery.must(QueryBuilders.matchQuery("onlineStatus", OnlineStatusEnum.ONLINE.getCode() ));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
            total += conditionCount(boolQueryBuilder);
        }
        return total;
    }

    @Override
    public int getStopStatusListingNum(List<String> accountNumberList){
        if(CollectionUtils.isEmpty(accountNumberList)){
            return 0;
        }
        List<List<String>> partition = Lists.partition(accountNumberList, 500);
        int total = 0;
        for (List<String> strings : partition) {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termsQuery("aliexpressAccountNumber", strings));
            boolQueryBuilder.must(QueryBuilders.termsQuery("productStatusType", Arrays.asList("onSelling", "auditing")));
            boolQueryBuilder.must(QueryBuilders.termsQuery("skuStatus", Arrays.asList(SkuStatusEnum.ARCHIVED.getCode(), SkuStatusEnum.STOP.getCode())));
            boolQueryBuilder.must(QueryBuilders.rangeQuery("ipmSkuStock").gt(0));
            //新增默认查询产品在线状态
            boolQueryBuilder.must(QueryBuilders.matchQuery("onlineStatus", OnlineStatusEnum.ONLINE.getCode() ));
            total += conditionCount(boolQueryBuilder);
        }
        return total;
    }

    @Override
    public int getNotEnoughStockListingNum(List<String> accountNumberList, Integer stockThreshold){
        if(CollectionUtils.isEmpty(accountNumberList)){
            return 0;
        }
        List<List<String>> partition = Lists.partition(accountNumberList, 500);
        int total = 0;
        for (List<String> strings : partition) {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termsQuery("aliexpressAccountNumber", strings));
            boolQueryBuilder.must(QueryBuilders.termsQuery("productStatusType", Arrays.asList("onSelling", "auditing")));
            boolQueryBuilder.must(QueryBuilders.termQuery("skuStatus", SkuStatusEnum.NORMAL.getCode()));
            boolQueryBuilder.must(QueryBuilders.rangeQuery("ipmSkuStock").lt(stockThreshold));
            //新增默认查询产品在线状态
            boolQueryBuilder.must(QueryBuilders.matchQuery("onlineStatus", OnlineStatusEnum.ONLINE.getCode() ));
            total += conditionCount(boolQueryBuilder);
        }
        return total;
    }

    @Override
    public int getOnlineListingNum(List<String> accountNumberList){
        if(CollectionUtils.isEmpty(accountNumberList)){
            return 0;
        }
        List<List<String>> partition = Lists.partition(accountNumberList, 500);
        int total = 0;
        for (List<String> strings : partition) {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termsQuery("aliexpressAccountNumber", strings));
            boolQueryBuilder.must(QueryBuilders.termsQuery("productStatusType", Arrays.asList("onSelling", "auditing")));
            //新增默认查询产品在线状态
            boolQueryBuilder.must(QueryBuilders.matchQuery("onlineStatus", OnlineStatusEnum.ONLINE.getCode() ));
            total += conditionCount(boolQueryBuilder);
        }
        return total;
    }

    @Override
    public long updateAccountPvLog(String accountNumber, Map<String, Object> params) {
        if (StringUtils.isBlank(accountNumber)) {
            return 0;
        }
        try {
            TimeUnit.SECONDS.sleep(2);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        params.put("viewUpdateDate", LocalDateTimeUtil.format(LocalDateTime.now()));
        UpdateByQueryRequest request = new UpdateByQueryRequest("aliexpress_product_listing");
        request.setConflicts("proceed");
        StringBuilder scriptBuilder = new StringBuilder();
        // 设置脚本
        params.forEach((k, v) -> {
            if (v == null) {
                scriptBuilder.append("ctx._source.remove('").append(k).append("');");
            } else {
                scriptBuilder.append("ctx._source.").append(k).append(" = params.").append(k).append(";");
            }
        });
        scriptBuilder.append("ctx._source.viewUpdateDate = params.viewUpdateDate;");
        log.info("重置pv为空，accountNumber:{}, params:{}", accountNumber, scriptBuilder);
        Script script = new Script(ScriptType.INLINE, "painless", scriptBuilder.toString(), params);
        request.setScript(script);
        // 设置查询
        request.setQuery(QueryBuilders.termQuery("aliexpressAccountNumber", accountNumber));
        try {
            // 执行更新请求
            BulkByScrollResponse bulkResponse = restHighLevelClient2.updateByQuery(request, RequestOptions.DEFAULT);
            // 处理响应
            return bulkResponse.getUpdated();
        } catch (Exception e) {
            log.error("更新店铺pv记录失败：", e);
        }
        return 0;
    }


    @Override
    public List<SkuPubilshListingFirstJoinTimeVo> getSmtFirstJoinTime(List<String> articleNumberList) {
        if (CollectionUtils.isEmpty(articleNumberList)) {
            return null;
        }
        long now = System.currentTimeMillis();
        List<SkuPubilshListingFirstJoinTimeVo> skuPubilshListingFirstJoinTimeVos = new ArrayList<>();
        for (String sku : articleNumberList) {
            NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("articleNumber", sku.toUpperCase()));

            queryBuilder
                    .withQuery(boolQueryBuilder)
                    .withPageable(PageRequest.of(0, 10000))
                    .withFields("articleNumber", "gmtCreate")
                    .withSort(SortBuilders.fieldSort("gmtCreate").order(SortOrder.ASC))
                    .build();
            NativeSearchQuery searchQuery = queryBuilder.build();
            searchQuery.setTrackTotalHits(true);
            Page<EsAliexpressProductListing> search = esAliexpressProductListingRepository.search(searchQuery);
            if (CollectionUtils.isNotEmpty(search.getContent())) {
                EsAliexpressProductListing esAliexpressProductListing = search.getContent().get(0);
                SkuPubilshListingFirstJoinTimeVo skuPubilshListingFirstJoinTimeVo = new SkuPubilshListingFirstJoinTimeVo();
                skuPubilshListingFirstJoinTimeVo.setSonSku(sku);
                Timestamp smtEarliestTime = null;
                if (null != esAliexpressProductListing.getGmtCreate()) {
                    smtEarliestTime = new Timestamp(esAliexpressProductListing.getGmtCreate().getTime());
                }
                skuPubilshListingFirstJoinTimeVo.setFirstTime(smtEarliestTime);
                skuPubilshListingFirstJoinTimeVo.setPlatform(SaleChannelEnum.ALIEXPRESS.getChannelName());
                skuPubilshListingFirstJoinTimeVos.add(skuPubilshListingFirstJoinTimeVo);
            }
        }
        long timeEs = System.currentTimeMillis() - now;
        log.info("/publishListing/getFirstJoinTime查询ES->aliexpress_product_listing,耗时->{}ms,sku数量->{}", timeEs, articleNumberList.size());
        return skuPubilshListingFirstJoinTimeVos;
    }


    @Override
    public int adminTempProductCount(String spu, Integer categoryId) {
        if (StringUtils.isBlank(spu) || categoryId == null) {
            return 0;
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("spu", spu));
        boolQueryBuilder.must(QueryBuilders.termQuery("categoryId", categoryId));
        boolQueryBuilder.must(QueryBuilders.termsQuery("productStatusType", Arrays.asList("onSelling", "auditing")));
        //新增默认查询产品在线状态
        boolQueryBuilder.must(QueryBuilders.matchQuery("onlineStatus", OnlineStatusEnum.ONLINE.getCode()));
        return conditionCount(boolQueryBuilder);
    }

    @Override
    public int scrollQueryExecutorTask(EsAliexpressProductListingRequest request, Consumer<List<EsAliexpressProductListing>> executorTask) {
        // 查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        setQuery(request, boolQueryBuilder);

        //创建查询条件构造器
        queryBuilder.withQuery(boolQueryBuilder);

        SortOrder sortOrder = SortOrder.DESC.toString().equals(request.getSequence()) ? SortOrder.DESC : SortOrder.ASC;
        NativeSearchQuery searchQuery = queryBuilder
                .withFields(request.getPageFields())
                .withQuery(boolQueryBuilder)
                .withPageable(PageRequest.of(0, request.getPageSize()))
                .withSort(SortBuilders.fieldSort(request.getOrderBy()).order(sortOrder))
                .build();
        String scrollId = null;
        List<String> scrollIdList = new ArrayList<>();
        long scrollTimeInMillis = 10 * 60 * 1000;
        int totalCount = 0;
        while (true) {
            SearchScrollHits<EsAliexpressProductListing> searchScrollHits = null;
            if (scrollId == null) {
                searchScrollHits = elasticsearchRestTemplate2.searchScrollStart(scrollTimeInMillis, searchQuery, EsAliexpressProductListing.class, aliexpressProductListingIndexCoordinates);
            } else {
                searchScrollHits = elasticsearchRestTemplate2.searchScrollContinue(scrollId, scrollTimeInMillis, EsAliexpressProductListing.class, aliexpressProductListingIndexCoordinates);
            }
            scrollId = searchScrollHits.getScrollId();
            scrollIdList.add(scrollId);
            if (!searchScrollHits.hasSearchHits()) {
                elasticsearchRestTemplate2.searchScrollClear(scrollIdList);
                break;
            }
            List<EsAliexpressProductListing> dataList = (List) SearchHitSupport.unwrapSearchHits(searchScrollHits);
            if (CollectionUtils.isEmpty(dataList)) {
                break;
            }
            totalCount += dataList.size();
            // 执行任务
            executorTask.accept(dataList);
        }
        return totalCount;
    }
}

