package com.estone.erp.publish.system.product;


import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.system.product.bean.ComposeSku;
import com.estone.erp.publish.system.product.bean.ProductLogisticsInfoDO;
import com.estone.erp.publish.system.product.bean.SuiteSku;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class ProductLogisticsUtils {

    @Resource
    private ProductClient productClient;


    /**
     * 获取单品基础物流信息
     *
     * @param skuList 货号
     * @return 产品物流包装重量信息
     */
    public ApiResult<List<ProductLogisticsInfoDO>> getSingleProductLogisticsInfo(List<String> skuList) {
        // 查询产品系统获取包裹重量
        List<ProductInfo> skuInfoList = ProductUtils.findProductInfos(skuList);
        Map<String, ProductInfo> skuMap = skuInfoList.stream().collect(Collectors.toMap(ProductInfo::getSonSku, Function.identity(), (o1, o2) -> o1));
        List<ProductLogisticsInfoDO> productLogisticsInfoDOList = new ArrayList<>();

        skuList.forEach(sku -> {
            ProductInfo product = skuMap.get(sku);
            if (product == null) {
                return;
            }
            ProductLogisticsInfoDO logisticsInfoDO = new ProductLogisticsInfoDO();
            logisticsInfoDO.setSpu(product.getMainSku());
            logisticsInfoDO.setSku(product.getSonSku());
            logisticsInfoDO.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
            logisticsInfoDO.setProductWeight(product.getProductWeight());
            logisticsInfoDO.setLength(product.getLength());
            logisticsInfoDO.setWide(product.getWide());
            logisticsInfoDO.setHeight(product.getHeight());
            logisticsInfoDO.setPackLength(product.getPackLength());
            logisticsInfoDO.setPackWidth(product.getPackWidth());
            logisticsInfoDO.setPackHeight(product.getPackHeight());
            logisticsInfoDO.setSizeRemark(product.getSizeRemark());
            logisticsInfoDO.setPackingPrice(product.getPackingPrice());
            logisticsInfoDO.setPackingWeight(product.getPackingWeight());
            logisticsInfoDO.setMatchWeight(product.getMatchWeight());
            logisticsInfoDO.setPackageWeight(product.getPackageWeight());
            logisticsInfoDO.setStandardWeight(product.getStandardWeight());
            productLogisticsInfoDOList.add(logisticsInfoDO);
        });
        return ApiResult.newSuccess(productLogisticsInfoDOList);
    }

    /**
     * 获取组合和套装基础物流信息
     *
     * @param skuList 货号
     * @return
     */
    public ApiResult<List<ProductLogisticsInfoDO>> getComposeAndSuiteLogisticsInfo(List<String> skuList) {
        List<String> suiteItems = new ArrayList<>();
        List<String> composeItems = new ArrayList<>();
        List<ProductLogisticsInfoDO> productLogisticsInfoDOList = new ArrayList<>();

        skuList.forEach(sku -> {
            Boolean existSaleSuit = ProductUtils.isExistSaleSuite(sku);
            if (existSaleSuit) {
                // 查询一遍组合套装映射表 存在映射取对应的组合数据
                Map<String, String> composeSkuSuitMap = ProductUtils.getComposeSkuBySuit(Collections.singletonList(sku));
                String mappingSpu = MapUtils.getString(composeSkuSuitMap, sku, null);
                if (mappingSpu != null) {
                    // 映射后的spu, 重新设值
                    composeItems.add(mappingSpu);
                    return;
                }
                suiteItems.add(sku);
            } else {
                composeItems.add(sku);
            }
        });

        if (CollectionUtils.isNotEmpty(suiteItems)) {
            suiteItems.forEach(sku -> {
                SuiteSku suiteSku = ProductUtils.getSaleSuiteInfoBySonSku(sku);
                if (suiteSku == null) {
                    return;
                }
                ProductLogisticsInfoDO logisticsInfoDO = new ProductLogisticsInfoDO();
                logisticsInfoDO.setSpu(suiteSku.getSuiteSku());
                logisticsInfoDO.setSku(sku);
                logisticsInfoDO.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
                logisticsInfoDO.setPackageWeight(suiteSku.getPackageWeight());
                logisticsInfoDO.setWide(suiteSku.getWide());
                logisticsInfoDO.setLength(suiteSku.getLength());
                logisticsInfoDO.setHeight(suiteSku.getHeight());
                productLogisticsInfoDOList.add(logisticsInfoDO);
            });
        }

        if (CollectionUtils.isNotEmpty(composeItems)) {
            composeItems.forEach(sku -> {
                ComposeSku composeProduct = ProductUtils.getComposeProduct(sku);
                if (composeProduct == null) {
                    return;
                }

                ProductLogisticsInfoDO logisticsInfoDO = new ProductLogisticsInfoDO();
                logisticsInfoDO.setSpu(composeProduct.getComposeSku());
                logisticsInfoDO.setSku(sku);
                logisticsInfoDO.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
                logisticsInfoDO.setProductWeight(composeProduct.getProductWeight());
                logisticsInfoDO.setPackageWeight(composeProduct.getPackageWeight());
                logisticsInfoDO.setWide(composeProduct.getWide());
                logisticsInfoDO.setLength(composeProduct.getLength());
                logisticsInfoDO.setHeight(composeProduct.getHeight());
                productLogisticsInfoDOList.add(logisticsInfoDO);
            });
        }
        return ApiResult.newSuccess(productLogisticsInfoDOList);
    }


}
