package com.estone.erp.publish.system.product;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.shaded.com.google.common.cache.Cache;
import com.alibaba.nacos.shaded.com.google.common.cache.CacheBuilder;
import com.estone.erp.common.constant.PublishCommonConstant;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.constant.RedisKeyConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.PagingUtils;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.base.pms.model.InfringementWord;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.enums.WenAnTypeEnum;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.common.util.NumberUtils;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.util.SimilarityRatioUtils;
import com.estone.erp.publish.system.erpCommon.ErpCommonUtils;
import com.estone.erp.publish.system.product.bean.*;
import com.estone.erp.publish.system.product.bean.forbidden.InfringementSaleProhibitionVO;
import com.estone.erp.publish.system.product.bean.forbidden.RiskLevel;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.bean.forbidden.Sites;
import com.estone.erp.publish.system.product.enums.ProductApiCacheEnum;
import com.estone.erp.publish.system.product.enums.SpecialTagEnum;
import com.estone.erp.publish.system.product.esProduct.bean.*;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.system.product.request.*;
import com.estone.erp.publish.system.product.response.*;
import com.estone.erp.publish.system.product.util.GtProductUtils;
import com.estone.erp.publish.system.product.util.SingleItemEsUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import feign.template.UriUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.remoting.RemoteAccessException;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.PostConstruct;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.estone.erp.common.constant.RedisConstant.PRODUCT_SKU_TAG;

@Slf4j
@Component
public class ProductUtils {
    /**
     * 产品系统接口本地缓存
     * key: {@link ProductApiCacheEnum}
     * value: api返回结果
     */
    public static Cache<String, String> PRODUCT_API_RESULT_CACHE = CacheBuilder
            .newBuilder()
            .maximumSize(50)
            .expireAfterAccess(10, TimeUnit.MINUTES)
            .build();

    public static Cache<String,List<RiskLevel>> PRODUCT_RISK_LEVEL_CONFIG_INFO_CACHE = CacheBuilder
            .newBuilder()
            .maximumSize(50)
            .expireAfterAccess(30, TimeUnit.MINUTES)
            .build();

    // 停产sku静态List 避免取redis 数据过慢
    public static Set<String> staticStopSkuSets = new HashSet<>();
    public static Long staticStopSkuExpirationTime = 0L;
    // 存档sku静态List 避免取redis 数据过慢
    public static Set<String> staticArchivedSkuSets = new HashSet<>();
    public static Long staticArchivedSkuExpirationTime = 0L;

    private static ProductClient productClient;

    public static final String resultKey = "resultKey";

    public static final int DEFAULT_RETRY_NUM = 3;

    //侵权词静态map key = 平台
    public static Map<String, List<String>> staticInfringementWordMap = new HashMap<>();
    //有效时间 key = 平台
    public static Map<String, Long> staticTimeMap = new HashMap<>();
    //同步锁
    public static String synchronizedKey = "synchronizedKey";

    /**
     * 获取停产sku集合，并缓存
     * @return
     */
    private static Set<String> getStopSkuListSets() {
        // 当前时间
        long currentTimeMillis = System.currentTimeMillis();
        if(CollectionUtils.isEmpty(staticStopSkuSets) || currentTimeMillis > staticStopSkuExpirationTime) {
            synchronized (ProductUtils.class) {
                // 再次判断避免 多线程并发重复更新
                if(CollectionUtils.isEmpty(staticStopSkuSets) || currentTimeMillis > staticStopSkuExpirationTime) {
                    String stopSku = PublishRedisClusterUtils.get(RedisConstant.PRODUCT_SKU_STOP_STATUS);
                    if (StringUtils.isNotBlank(stopSku)) {
                        staticStopSkuSets = new HashSet<>(JSON.parseArray(stopSku).toJavaList(String.class));
                    }
                    // 10分钟后过期
                    staticStopSkuExpirationTime = currentTimeMillis + 10 * 60 * 1000;
                }
            }
        }
        // 后期无异常可去除
        if(CollectionUtils.isEmpty(staticStopSkuSets)) {
            log.error("使用 同步锁实现懒汉式单例加载停产sku 存在为空的情况！");
        }
        return staticStopSkuSets;
    }


    /**
     * 获取存档sku集合，并缓存
     * @return
     */
    private static Set<String> getArchivedSkuSets() {
        // 当前时间
        long currentTimeMillis = System.currentTimeMillis();
        if(CollectionUtils.isEmpty(staticArchivedSkuSets) || currentTimeMillis > staticArchivedSkuExpirationTime) {
            synchronized (ProductUtils.class) {
                // 再次判断避免 多线程并发重复更新
                if(CollectionUtils.isEmpty(staticArchivedSkuSets) || currentTimeMillis > staticArchivedSkuExpirationTime) {
                    String stopSku = PublishRedisClusterUtils.get(RedisConstant.PRODUCT_SKU_ARCHIVED_STATUS);
                    if (StringUtils.isNotBlank(stopSku)) {
                        staticArchivedSkuSets = new HashSet<>(JSON.parseArray(stopSku).toJavaList(String.class));
                    }
                    // 10分钟后过期
                    staticArchivedSkuExpirationTime = currentTimeMillis + 10 * 60 * 1000;
                }
            }
        }

        // 后期无异常可去除
        if(CollectionUtils.isEmpty(staticArchivedSkuSets)) {
            log.error("使用 同步锁实现懒汉式单例加载存档sku 存在为空的情况！");
        }
        return staticArchivedSkuSets;
    }

    /**
     * 获取组合套装最早的录入时间
     * @param parentSku 组合套装的父SKU
     * @return
     */
    public static String getComposeProductFirstCreateTime(String parentSku) {
        ComposeSku composeProduct = getComposeProduct(parentSku);
        if (composeProduct == null) {
            return null;
        }
        LocalDateTime createAt = composeProduct.getCreateAt();
        return createAt.toString();
    }

    /**
     * 获取产品最早的录入时间
     * @param parentSku 产品的父SKU
     * @return
     */
    public static String getProductFirstCreateTime(String parentSku) {
        List<ProductInfo> productInfos = findProductInfos(List.of(parentSku));
        if(CollectionUtils.isEmpty(productInfos)) {
            return null;
        }
        ProductInfo productInfo = productInfos.stream()
                .filter(product -> Objects.nonNull(product.getCreateAt()))
                .min(Comparator.comparing(ProductInfo::getCreateAt))
                .orElseGet(() -> null);
        if(null == productInfo) {
            return null;
        }
        Timestamp timestamp = new Timestamp(productInfo.getCreateAt());
        LocalDateTime createDateTime = timestamp.toLocalDateTime();
        return createDateTime.toString();
    }


    @PostConstruct
    private void init() {
        try {
            productClient = SpringUtils.getBean(ProductClient.class);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
        }
    }

    /**
     * 查询套装货号
     *
     * @param articleNumberList
     * @return
     */
    public static ResponseJson getSuiteWeightAndCost(List<String> articleNumberList) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);

        try {

            if (CollectionUtils.isEmpty(articleNumberList)) {
                rsp.setMessage("货号参数为空！");
            }

            String aa = productClient.getSuiteWeightAndCost(articleNumberList);
            ApiResult<List<SuiteSku>> apiResult = JSON.parseObject(aa, new TypeReference<ApiResult<List<SuiteSku>>>() {
            });

            boolean success = apiResult.isSuccess();
            if (success) {
                rsp.setStatus(StatusCode.SUCCESS);

                rsp.getBody().put(resultKey, apiResult.getResult());

            } else {
                rsp.setMessage(apiResult.getErrorMsg());
            }

        } catch (Exception e) {
            rsp.setMessage("套装查询对应信息异常:" + e.getMessage());
        }

        return rsp;
    }

    /**
     * 根据sku查询对应信息
     * @return List<ProductInfo>
     */
    public static ResponseJson findSkuInfos(List<String> articleNumberList) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        ApiResult<List<ProductInfo>> apiResult = null;
        if (CollectionUtils.isEmpty(articleNumberList)) {
            rsp.setMessage("货号参数为空！");
            return rsp;
        }
        boolean gtFlag = false;
        for (String articleNumber : articleNumberList) {
            if (articleNumber.startsWith("GT")) {
                gtFlag = true;
                break;
            }
        }

        long now = System.currentTimeMillis();
        try {
            if (gtFlag) {
                String aa = productClient.findSkuInfos(articleNumberList);
                apiResult = JSON.parseObject(aa, new TypeReference<ApiResult<List<ProductInfo>>>() {
                });
                boolean success = apiResult.isSuccess();
                if (success) {
                    rsp.setStatus(StatusCode.SUCCESS);

                    // 冠通产品转换基本信息
                    GtProductUtils.tranGtProducts(apiResult.getResult());

                    rsp.getBody().put(resultKey, apiResult.getResult());

                } else {
                    rsp.setMessage(apiResult.getErrorMsg());
                }
                long time = System.currentTimeMillis() - now;
                if (time >1000) {
                    log.info("调用/api/publish/findSkuInfos,耗时->{}ms", time);
                }
            } else {
                //查询ES
                SingleItemEsRequest criteria = new SingleItemEsRequest();
                criteria.setSkuList(articleNumberList);
                SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
                List<SingleItemEs> singleItemEsList = singleItemEsService.getSingleItemEsList(criteria);
                if (CollectionUtils.isNotEmpty(singleItemEsList)) {
                    List<ProductInfo> productInfoList = SingleItemEsUtils.tranSingleItemEsToProductInfo(singleItemEsList);
                    rsp.getBody().put(resultKey, productInfoList);
                    rsp.setStatus(StatusCode.SUCCESS);
                } else {
                    List<ProductInfo> productInfoList = new ArrayList<>();
                    rsp.getBody().put(resultKey, productInfoList);
                    rsp.setStatus(StatusCode.SUCCESS);
//                    rsp.setMessage("根据sku查询ES数据为空");
                }
                long timeES = System.currentTimeMillis() - now;
                if (timeES >1000) {
                    log.info("/findSkuInfos查询ES->singleitemes,耗时->{}ms,sku数量->{}", timeES, articleNumberList.size());
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage("sku查询对应信息异常:" + e.getMessage());
        }
        return rsp;
    }

    /**
     * 单个sku 只返回当前子SKU 先查redis接口 redis没有查到或者是冠通查接口
     *
     * @param sku
     * @return
     */
    public static ResponseJson findSkuInfo(String sku) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        if (StringUtils.isBlank(sku)) {
            rsp.setMessage("sku参数为空！");
            return rsp;
        }

        ProductInfo productInfo = null;

        boolean gtFlag = false;
        if(sku.startsWith("GT")){
            gtFlag = true;
        }

        long now = System.currentTimeMillis();
        try{
            if (!gtFlag) {
                // 查询redis对应的接口
                SingleItemEs singleItem = ErpCommonUtils.getSingleItemForRedis(sku);
                if(null != singleItem) {
                    List<ProductInfo> productInfoList = SingleItemEsUtils.tranSingleItemEsToProductInfo(Arrays.asList(singleItem));
                    if(CollectionUtils.isNotEmpty(productInfoList)) {
                        productInfo = productInfoList.get(0);
                        rsp.getBody().put(resultKey, productInfo);
                        rsp.setStatus(StatusCode.SUCCESS);
                        long timeRedis = System.currentTimeMillis() - now;
                        if (timeRedis >1000) {
                            log.info("/findSkuInfos查询Redis->singleitemes,耗时->{}ms,sku->{}", timeRedis, sku);
                        }
                    }
                }
            }

            if (null == productInfo) {
                String aa = productClient.findSkuInfos(Arrays.asList(sku));
                ApiResult<List<ProductInfo>> apiResult = JSON.parseObject(aa, new TypeReference<ApiResult<List<ProductInfo>>>() {});
                boolean success = apiResult.isSuccess();
                if(success){

                    // 冠通产品转换基本信息
                    GtProductUtils.tranGtProducts(apiResult.getResult());

                    // 过滤出当前的子SKU 忽略大小写
                    productInfo = apiResult.getResult().stream().filter(item-> item.getSonSku().equalsIgnoreCase(sku)).findAny().orElse(null);
                    if(null != productInfo) {
                        rsp.getBody().put(resultKey, productInfo);
                        rsp.setStatus(StatusCode.SUCCESS);
                    } else {
                        rsp.setMessage("查询成功，未匹配到单前SKU：" + sku);
                    }
                }else{
                    rsp.setMessage(apiResult.getErrorMsg());
                }
                long time = System.currentTimeMillis() - now;
                if (time >1000) {
                    log.info("调用/api/publish/findSkuInfos,耗时->{}ms", time);
                }
            }
        }catch (Exception e){
            rsp.setMessage("sku查询对应信息异常:" + e.getMessage());
        }
        return rsp;
    }

    /**
     *  分页获取sku信息
     * @param begin
     * @param end
     * @param retryNum
     * @return
     */
    public static String getBatchInfoPublish(int begin, int end, int retryNum) {
        int i = 1;
        do {
            try {
                return productClient.getBatchInfoPublish(begin, end);
            }catch (Exception e){
                log.error(String.format("第%s次请求begin:%s, end:%s 数据失败", i, begin, end), e);
            }
        }while (++i <= retryNum);
        return null;
    }

    /**
     * 分页获取清仓、甩卖sku信息
     * @param pageIndex
     * @param pageSize
     * @param changeTime
     * @param retryNum
     * @return
     */
    public static String getClearanceSaleList(int pageIndex, int pageSize, Date changeTime, int retryNum) {
        int i = 1;
        String date = null;
        if(changeTime != null){
            date = DateFormatUtils.format(changeTime, "yyyy/MM/dd hh:mm:ss");
        }
        do {
            try {
                return productClient.getClearanceSaleList(pageIndex, pageSize, date);
            }catch (Exception e){
                log.error(String.format("第%s次请求pageIndex:%s, pageSize:%s 数据失败", retryNum, pageIndex, pageSize), e);
            }
        }while (++i <= retryNum);
        return null;
    }

    /**
     * 查询改前状态为清仓、甩卖的sku信息
     * @param pageIndex
     * @param pageSize
     * @param changeTime
     * @param retryNum
     * @return
     */
    public static String getBeforeClearanceSaleList(int pageIndex, int pageSize, Date changeTime, int retryNum) {
        int i = 1;
        String date = null;
        if(changeTime != null){
            date = DateFormatUtils.format(changeTime, "yyyy/MM/dd hh:mm:ss");
        }
        do {
            try {
                return productClient.getBeforeClearanceSaleList(pageIndex, pageSize, date);
            }catch (Exception e){
                log.error(String.format("第%s次请求pageIndex:%s, pageSize:%s 数据失败", retryNum, pageIndex, pageSize), e);
            }
        }while (++i <= retryNum);
        return null;
    }

    /**
     * 根据主sku查询 spu相关信息
     * @param articleNumber sku
     */
    public static ResponseJson findSpuInfo(String articleNumber) {
        List<String> articleNumberList = new ArrayList<>();
        articleNumberList.add(articleNumber);
        return findSpuInfos(articleNumberList);
    }

    /**
     * 根据子sku查询刊登对应字段信息
     * @return List<Map<String, Object>>
     */
    public static ResponseJson findSonSkuInfos(List<String> articleNumberList){
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);

        try{
            if(CollectionUtils.isEmpty(articleNumberList)){
                rsp.setMessage("货号参数为空！");
                return rsp;
            }

            String sonSkuInfos = productClient.findSonSkuInfos(articleNumberList);

            ApiResult<List<Map<String, Object>>> apiResult = JSON
                    .parseObject(sonSkuInfos, new TypeReference<ApiResult<List<Map<String, Object>>>>() {

                    });

            boolean success = apiResult.isSuccess();
            if(success){
                rsp.setStatus(StatusCode.SUCCESS);

                rsp.getBody().put(resultKey, apiResult.getResult());

            }else{
                rsp.setMessage(apiResult.getErrorMsg());
            }

        }catch (Exception e){
            rsp.setMessage("根据子sku查询刊登对应字段异常" + e.getMessage());
        }
        return rsp;
    }

    /**
     * 根据主sku查询 spu相关信息
     * @return List<SpuInfo>
     */
    public static ApiResult<List<SpuInfo>> findSpuInfo(List<String> spuList) {
        if(CollectionUtils.isEmpty(spuList)){
            return ApiResult.newError("货号参数为空");
        }
        try {
            //请求接口的集合
            List<String> requestSkuList = new ArrayList<>();
            //所有的数据
            List<SpuInfo> spuInfoList = new ArrayList<>();
            for (String articleNumber : spuList) {
                String spuInfoCache = PublishRedisClusterUtils.get("SPU_" + articleNumber);
                if (StringUtils.isEmpty(spuInfoCache)) {
                    requestSkuList.add(articleNumber);
                    continue;
                }
                try {
                    SpuInfo spuInfo = JSON.parseObject(spuInfoCache, new TypeReference<SpuInfo>() {
                    });
                    //先存放redis数据，如果有
                    spuInfoList.add(spuInfo);
                } catch (Exception e) {
                    //数据异常 重新请求
                    log.error(e.getMessage(), e);
                    requestSkuList.add(articleNumber);
                }
            }
            if (CollectionUtils.isEmpty(requestSkuList)) {
                return ApiResult.newSuccess(spuInfoList);
            }
            String spuInfos = productClient.findSpuInfos(requestSkuList);
            ApiResult<List<SpuInfo>> apiResult = JSON.parseObject(spuInfos, new TypeReference<>() {
            });
            if (!apiResult.isSuccess()) {
                return ApiResult.newError("product-service ErrorMsg," + apiResult.getErrorMsg());
            }
            List<SpuInfo> result = apiResult.getResult();
            //请求成功存入redis
            for (SpuInfo spuInfo : result) {
                try {
                    PublishRedisClusterUtils.set("SPU_" + spuInfo.getSpu(), JSON.toJSONString(spuInfo), 10, TimeUnit.MINUTES);
                } catch (Exception e) {
                    log.error("set redis 异常 SPU_" + spuInfo.getSpu() + e.getMessage(), e);
                }
            }
            spuInfoList.addAll(result);
            return ApiResult.newSuccess(spuInfoList);
        } catch (Exception e){
            return ApiResult.newError("主sku集合获取spu相关信息异常："+ e.getMessage());
        }

    }

    /**
     * 根据主sku查询 spu相关信息
     * @return List<SpuInfo>
     */
    public static ResponseJson findSpuInfos(List<String> articleNumberList) {

        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);

        long now = System.currentTimeMillis();

        try{
            if(CollectionUtils.isEmpty(articleNumberList)){
                rsp.setMessage("货号参数为空！");
                return rsp;
            }

            //请求接口的集合
            List<String> requestSkuList = new ArrayList<>();

            //所有的数据
            List<SpuInfo> spuInfoList = new ArrayList<>();

            for (String articleNumber : articleNumberList) {
                String s = PublishRedisClusterUtils.get("SPU_" + articleNumber);

                if(StringUtils.isEmpty(s)){
                    requestSkuList.add(articleNumber);
                }else{
                    try {
                        SpuInfo spuInfo = JSON.parseObject(s, new TypeReference<SpuInfo>() {
                        });

                        //先存放redis数据，如果有
                        spuInfoList.add(spuInfo);
                    }
                    catch (Exception e) {
                        //数据异常 重新请求
                        log.error(e.getMessage(), e);
                        requestSkuList.add(articleNumber);
                    }
                }
            }
            //优先获取缓存
            if(CollectionUtils.isNotEmpty(requestSkuList)){
                String aa= productClient.findSpuInfos(requestSkuList);
                ApiResult<List<SpuInfo>> apiResult = JSON.parseObject(aa, new TypeReference<ApiResult<List<SpuInfo>>>() {
                });

                boolean success = apiResult.isSuccess();
                if(success){
                    rsp.setStatus(StatusCode.SUCCESS);

                    List<SpuInfo> result = apiResult.getResult();
                    if(CollectionUtils.isEmpty(result)) {
                        log.error("主sku集合获取spu相关信息 返回空结果requestSkuList" + JSON.toJSONString(requestSkuList));
                        log.error("主sku集合获取spu相关信息 返回空结果apiResult" + JSON.toJSONString(apiResult));
                        log.error("主sku集合获取spu相关信息 返回空结果" + aa);
                    }

                    //请求成功存入redis
                    for (SpuInfo spuInfo : result) {
                        try {
                            PublishRedisClusterUtils.set("SPU_" + spuInfo.getSpu(),JSON.toJSONString(spuInfo),10, TimeUnit.MINUTES);
                        }catch (Exception e) {
                            log.error("set redis 异常 SPU_" + spuInfo.getSpu() + e.getMessage(), e);
                        }
                    }

                    spuInfoList.addAll(result);

                    rsp.getBody().put(resultKey, spuInfoList);
                }else{
                    rsp.setMessage("product-service ErrorMsg " + apiResult.getErrorMsg());
                }
            }else{
                rsp.setStatus(StatusCode.SUCCESS);
                rsp.getBody().put(resultKey, spuInfoList);
            }
        }catch (Exception e){
            rsp.setStatus(StatusCode.FAIL);
            log.error("主sku集合获取spu相关信息异常:" + e.getMessage(), e);
            rsp.setMessage("主sku集合获取spu相关信息异常:" + e.getMessage());
        }

        long time = System.currentTimeMillis() - now;
        if(time > 2000){
            log.info("/findSpuInfos查询,耗时->{}ms,sku数量->{}", time, articleNumberList.size());
        }
        return rsp;
    }

    public static List<SpuOfficial> getSpuOfficialTitles(List<String> spus) {
        ResponseJson resp = ProductUtils.getSpuTitles(spus);
        if (resp.isSuccess()){
            return (List<SpuOfficial>)resp.getBody().get(ProductUtils.resultKey);
        }
        log.error("[{}]获取SPU标题失败：{}", spus, resp.getMessage());
        return Collections.emptyList();
    }
    public static List<SpuOfficial> getSpuTitles(String language, List<String> spus) {
        try {
            String aa = productClient.getSpuTitlesByLanguage(language, spus);
            ApiResult<List<SpuOfficial>> apiResult = JSON.parseObject(aa, new TypeReference<ApiResult<List<SpuOfficial>>>() {
            });
            if (apiResult.isSuccess()) {
                return apiResult.getResult();
            }
            log.error("[{}]获取SPU标题失败：{}", spus, apiResult.getErrorMsg());
        } catch (Exception e) {
            log.error("[{}]获取SPU标题失败：{}", spus, e.getMessage());
        }
        return Collections.emptyList();
    }

    /**
     * 根据主SPU获取 SPU标题
     * @param spus
     * @return
     */
    public static ResponseJson getSpuTitles(List<String> spus) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        if(CollectionUtils.isEmpty(spus)){
            rsp.setMessage("货号参数为空！");
            return rsp;
        }
        spus = spus.stream().distinct().collect(Collectors.toList());
        // 先查redis
        List<SpuOfficial> spuOfficials  = ErpCommonUtils.getSpuOfficialListForRedis(spus);
        List<SpuOfficial> spuOfficialList = new ArrayList();
        spuOfficialList.addAll(spuOfficials);
        if (CollectionUtils.isNotEmpty(spuOfficialList) && spuOfficialList.size() == spus.size()){
            rsp.setStatus(StatusCode.SUCCESS);
            rsp.getBody().put(resultKey, spuOfficialList);
            return rsp;
        }
        List<SpuOfficial> spuOfficialAllList = new ArrayList<>(spus.size());
        spuOfficialAllList.addAll(spuOfficialList);
        long now = System.currentTimeMillis();
        List<String> searchRedisSonSkuList = spuOfficialList.stream().map(o ->o.getSpu()).collect(Collectors.toList());

        // redis没查到的数据查询产品系统
        spus.removeAll(searchRedisSonSkuList);
        int i = 1;
        do {
            try{

                String aa= productClient.getSpuTitles(spus);
                ApiResult<List<SpuOfficial>> apiResult = JSON.parseObject(aa, new TypeReference<ApiResult<List<SpuOfficial>>>() {
                });

                boolean success = apiResult.isSuccess();
                if(success){
                    rsp.setStatus(StatusCode.SUCCESS);

                    List<SpuOfficial> result = apiResult.getResult();
                    spuOfficialAllList.addAll(result);
                    rsp.getBody().put(resultKey, spuOfficialAllList);
                }else{
                    rsp.setMessage(apiResult.getErrorMsg());
                }
                return rsp;
            }catch (Exception e){
                log.error(e.getMessage(), e);
                rsp.setMessage("获取SPU标题相关信息异常:" + e.getMessage());
            }
        }while (++i <= DEFAULT_RETRY_NUM);

        long time = System.currentTimeMillis() - now;
        if (time >1000) {
            log.info("/getSpuTitles查询,耗时->{}ms", time);
        }
        return rsp;
    }

    //根据子sku获取父sku（单个版本）
    public static String getMainSku(String sku) {
        sku = sku.trim();
        List<String> skuList = new ArrayList<>();
        skuList.add(sku);
        Map<String, String> skuMap = getMainSkuBySubSku(skuList);
        //如果返回值里获取不到当前sku对应的key，说明本来就是父sku
        if (StringUtils.isEmpty(skuMap.get(sku))) {
            return sku;
        } else {
            return skuMap.get(sku).trim();
        }
    }


    //根据子sku获取父sku，批量接口
    public static Map<String, String> getMainSkuBySubSku(List<String> subList, int... size) {
        Map<String, String> resultMap = new HashMap<>();

        SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
        int ps = 1000;
        if (size.length > 0) {
            ps = size[0];
        }
        List<List<String>> pagingList = PagingUtils.pagingList(subList, ps);
        for (List<String> list : pagingList) {
            Map<String, String> map = singleItemEsService.getMainSkuBySubSku(list);
            if (null != map && !map.isEmpty()) {
                resultMap.putAll(map);
            }
        }
        return resultMap;
    }

    //根据子sku或主sku获取主sku集合
    public static Map<String, String> getMainSkuBySonSkuOrMainSku(List<String> subList) {
        Map<String, String> resultMap = new HashMap<>();
        ApiResult<List<String>> apiResult = productClient.getMainSkuBySonSkuOrMainSku(subList);
        if (apiResult.isSuccess()) {
            List<String> result = apiResult.getResult();
            for (String mainSku : result) {
                for (String sonSkuOrMainSku : subList) {
                    if (sonSkuOrMainSku.contains(mainSku)) {
                        resultMap.put(sonSkuOrMainSku, mainSku);
                    }
                }
            }
        }
        return resultMap;
    }

    /**
     * 获取产品系统所有品类
     *
     * @return
     */
    public static ApiResult<List<ProductCategoryInfo>> getCategoryTree() {
        try {
            String result = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SYSTEM_CATEGORY);
            if (StringUtils.isNotBlank(result)) {
                ApiResult<List<ProductCategoryInfo>> apiResult = JSON.parseObject(result, new TypeReference<ApiResult<List<ProductCategoryInfo>>>() {
                });
                return apiResult;
            }
        } catch (Exception e) {
            log.error("获取缓存失败：", e);
        }
        ApiResult<List<ProductCategoryInfo>> apiResult = productClient.getAllCategoryCode();

        try {
            PublishRedisClusterUtils.set(RedisKeyConstant.PRODUCT_SYSTEM_CATEGORY, apiResult, 2 * 60 * 60 * 1000, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("设置缓存失败：", e);
        }
        return apiResult;
    }

    /**
     * 根据产品分类查询 spu销量数据
     *
     * @param request
     * @return List<SaleSpuResponse>
     */
    public static ResponseJson getSaleSpu(SaleSpuRequest request) {

        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);

        try {
            ApiResult<List<SaleSpuResponse>> result = productClient.getSaleSpu(request);
            if (result.isSuccess()) {

                rsp.setStatus(StatusCode.SUCCESS);

                rsp.getBody().put(resultKey, result.getResult());
            } else {
                rsp.setMessage(result.getErrorMsg());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage("主sku集合获取spu相关信息异常:" + e.getMessage());
        }

        return rsp;
    }


    /**
     * 根据时间查看此时间段内已编辑的spu
     *
     * @param request
     * @return Map<String, SkuListAndCode> key=spu value=code
     */
    public static ResponseJson getNewProduct(ProductNewSpuRequest request) {

        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);

        if (request == null || StringUtils.isBlank(request.getBeginTime()) || StringUtils.isBlank(request.getEndTime())) {
            rsp.setMessage("请求时间不能为空！");
            return rsp;
        }

        try {
            ApiResult<Map<String, SkuListAndCode>> result = productClient.getNewProduct(request);
            if (result.isSuccess()) {

                rsp.setStatus(StatusCode.SUCCESS);

                rsp.getBody().put(resultKey, result.getResult());
            } else {
                rsp.setMessage(result.getErrorMsg());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage("获取新品异常:" + e.getMessage());
        }
        return rsp;
    }


    /**
     * 根据时间查看此时间段内已编辑的spu （不限制数量，暂时用于amazon，传参时间跨度不超过1个月）
     *
     * @param request
     * @return Map<String, SkuListAndCode> key=spu value=code
     */
    public static ResponseJson getNewProductBySingItem(ProductNewSpuRequest request) {

        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);

        if (request == null || StringUtils.isBlank(request.getBeginTime()) || StringUtils.isBlank(request.getEndTime())) {
            rsp.setMessage("请求时间不能为空！");
            return rsp;
        }
        int between = DateUtils.getDaysBetween(com.estone.erp.common.util.DateUtils.formatStringToDate(request.getBeginTime(),com.estone.erp.common.util.DateUtils.STANDARD_DATE_PATTERN),
                com.estone.erp.common.util.DateUtils.formatStringToDate(request.getEndTime(),com.estone.erp.common.util.DateUtils.STANDARD_DATE_PATTERN));
        if ( between >= 35) {
            rsp.setMessage("请求时间超出35天，请检查！");
            return rsp;
        }

        try {
            ApiResult<Map<String, SkuListAndCode>> result = productClient.getNewProductBySingItem(request);
            if (result.isSuccess()) {

                rsp.setStatus(StatusCode.SUCCESS);

                rsp.getBody().put(resultKey, result.getResult());
            } else {
                rsp.setMessage(result.getErrorMsg());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage("获取新品异常:" + e.getMessage());
        }
        return rsp;
    }


    /**
     * 查看试卖SKU 是否进入单品 返回进入单品数据
     *
     * @return
     */
    public static ApiResult<List<String>> getSpSkuInSingle(List<String> skus) {
        ApiResult<List<String>> result = null;

        try {
            result = productClient.getNormalBySku(skus);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result = ApiResult.newError("查看试卖SKU是否进入单品异常:" + e.getMessage());
        }

        return result;
    }

    /**
     * 判断该试卖sku 是否进入单品
     *
     * @param mainSku
     * @return
     */
    public static ApiResult<Boolean> checkSpSkuInSingle(String mainSku) {

        ApiResult<Boolean> resultExist = null;
        if (StringUtils.isBlank(mainSku)) {
            return ApiResult.newError("传入SKU为空！");
        }

        ApiResult<List<String>> result = getSpSkuInSingle(Arrays.asList(mainSku));
        if (result.isSuccess()) {
            List<String> skus = result.getResult();
            if (null != skus && skus.contains(mainSku)) {
                resultExist = ApiResult.newSuccess(true);
            } else {
                resultExist = ApiResult.newSuccess(false);
            }

            return resultExist;
        }

        return ApiResult.newError(result.getErrorMsg());
    }

    /**
     * 新品推荐接口
     *
     * @param request
     * @return List<NewProductRemind>
     */
    public static ResponseJson getNewSpuInfo(ProductNewSpuRequest request) {

        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        String beginTime = request.getBeginTime();
        String endTime = request.getEndTime();
        if (StringUtils.isBlank(beginTime) || StringUtils.isBlank(endTime)) {
            rsp.setMessage("请求时间不能为空！");
            return rsp;
        }

        try {
            String response = productClient.getNewSpuInfo(request);

            ApiResult<List<NewProductRemind>> result = JSON
                    .parseObject(response, new TypeReference<ApiResult<List<NewProductRemind>>>() {
                    });


            if (result.isSuccess()) {
                rsp.setStatus(StatusCode.SUCCESS);

                rsp.getBody().put(resultKey, result.getResult());
            } else {
                rsp.setMessage(result.getErrorMsg());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage("获取新品推荐异常:" + e.getMessage());
        }
        return rsp;
    }

    /**
     * 根据单品上次编辑状态，时间获取子sku信息
     *
     * @param request
     * @return
     */
    public static ResponseJson getSonSkuByLastStatusInfo(SonSkuStatusChangeRequest request) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        Date beginTime = request.getBeginTime();
        Date endTime = request.getEndTime();
        if (beginTime == null || endTime == null || CollectionUtils.isEmpty(request.getLastStatus())) {
            rsp.setMessage("请求时间或者改前状态不能为空！");
            return rsp;
        }

        try {
            ApiResult<List<String>> result = productClient.getSonSkuByLastStatusInfo(request);
            if (result.isSuccess()) {
                rsp.setStatus(StatusCode.SUCCESS);
                rsp.getBody().put(resultKey, result.getResult());
            } else {
                rsp.setMessage(result.getErrorMsg());
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage("单品上次编辑状态，时间获取子sku信息异常:" + e.getMessage());
        }
        return rsp;
    }

    /**
     * 根据主sku获取产品信息
     */
    public static ProductInfo selectMainSkuInfo(String mainSku) {
        ProductInfo productInfo = null;
        ResponseJson responseJson = ProductUtils.findSkuInfos(Collections.singletonList(mainSku));
        if (!responseJson.isSuccess()) {
            log.error("查询产品异常：" + responseJson.getMessage());
        } else {
            List<ProductInfo> infos = (List<ProductInfo>) responseJson.getBody().get(ProductUtils.resultKey);
            Map<String, ProductInfo> infoMap = infos.stream()
                    .collect(Collectors
                            .toMap(t -> {
                                return t.getSonSku().toUpperCase();
                            }, productInfos -> productInfos, (k1, k2) -> k2));
            //主sku 也需要 一份数据
            infoMap.put(infos.get(0).getMainSku(), infos.get(0));
            productInfo = infoMap.get(mainSku);
        }
        return productInfo;
    }

    /**
     * 根据分类节点获取该节点的所有叶子节点，包含禁用的类目
     *
     * @param code
     * @return
     */
    public static ResponseJson getLeafNodeByCode(String code) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        if (StringUtils.isBlank(code)) {
            rsp.setMessage("code不能为空！");
            return rsp;
        }
        try {
            ApiResult<List<String>> result = productClient.getLeafNodeByCode(code);
            if (result.isSuccess()) {
                rsp.getBody().put(resultKey, result.getResult());
                rsp.setStatus(StatusCode.SUCCESS);
            } else {
                rsp.setMessage(result.getErrorMsg());
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setStatus(StatusCode.FAIL);
            rsp.setMessage("获取该节点的所有叶子节点:" + e.getMessage());
        }
        return rsp;
    }

    /**
     * 从产品系统获取侵权词，并且把数据缓存到redis里面
     */
    private static List<String> getInfringementWordFromProductService(String saleChannel) {
        List<String> infringementWordList = new ArrayList<>();
        long now = System.currentTimeMillis();
        ApiResult<?> apiResult = productClient.getInfringementWord(saleChannel);
        long time = System.currentTimeMillis() - now;
        if (time > 2000) {
            log.info("调用产品系统/api/publish/getInfringementWord?name={},耗时->{}ms", saleChannel, time);
        }
        List<Map<String, String>> resultList = (List) apiResult.getResult();
        for (Map<String, String> infringementWordMap : resultList) {
            infringementWordList.add(infringementWordMap.get("word"));
        }
        return infringementWordList;
    }

    /**
     * 判断是否包含侵权词的方法（忽略大小写）
     */
    public static boolean judgeInfrimentWordIgnoreCase(String content, String infringementWord) {
        //把要校验的内容前后加上空格进行校验
        if (StringUtils.containsIgnoreCase(" " + content + " ", " " + infringementWord + " ")) {
            return true;
        }
        return false;
    }

    /**
     * 查询所有侵权词，禁用状态， 忽略大小写状态
     *
     * @return
     */
    public static ApiResult<List<InfringementWord>> getAllInfringementService() {
        long now = System.currentTimeMillis();
        try {
            ApiResult<List<InfringementWord>> apiResult = JSON
                    .parseObject(productClient.getAllInfringement(), new TypeReference<ApiResult<List<InfringementWord>>>() {
                    });
            long time = System.currentTimeMillis() - now;
            if (time > 2000) {
                log.info("调用产品系统/api/publish/getAllInfringement,耗时->{}ms", time);
            }
            return apiResult;
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 根据侵权类型查询侵权词
     *
     * @return
     */
    public static ApiResult<List<InfringementWord>> getInfringementByType(String type) {
        long now = System.currentTimeMillis();
        try {
            ApiResult<List<InfringementWord>> apiResult = JSON
                    .parseObject(productClient.getInfringementByType(type), new TypeReference<ApiResult<List<InfringementWord>>>() {
                    });
            long time = System.currentTimeMillis() - now;
            if (time > 2000) {
                log.info("调用产品系统/api/publish/getInfringementByType ,耗时->{}ms", time);
            }
            return apiResult;
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 检测sku是否存在
     *
     * @param articleNumber
     * @return
     */
    public static ApiResult<Boolean> isExistArticleNumber(String articleNumber) {
        if (StringUtils.isEmpty(articleNumber)) {
            return ApiResult.newSuccess(false);
        }
        try {
            long now = System.currentTimeMillis();
            ApiResult<Boolean> apiResult = JSON
                    .parseObject(productClient.checkIsExist(articleNumber)
                            , new TypeReference<ApiResult<Boolean>>() {
                            });
            long time = System.currentTimeMillis() - now;
            if (time > 2000) {
                log.info("调用产品系统/api/publish/checkIsExist?sku={},耗时->{}ms", articleNumber, time);
            }
            return apiResult;
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 根据sku查询对应信息
     * 如果有GT开头的货号就通过接口调用
     * 否则默认查询ES
     */
    public static List<ProductInfo> findProductInfos(List<String> articleNumberList) {
        if (CollectionUtils.isEmpty(articleNumberList)) {
            return new ArrayList<>();
        }
        List<ProductInfo> productInfoList = new ArrayList<>();
        long now = System.currentTimeMillis();
        try {
            boolean gtFlag = articleNumberList.stream().anyMatch(articleNumber -> articleNumber.startsWith("GT"));
            if (gtFlag) {
                String aa = productClient.findSkuInfos(articleNumberList);
                ApiResult<List<ProductInfo>> apiResult = JSON.parseObject(aa, new TypeReference<>() {});
                productInfoList = apiResult.getResult();

                // 去掉一级分类
                if (CollectionUtils.isNotEmpty(productInfoList)) {
                    for (ProductInfo productInfo : productInfoList) {
                        productInfo.setFullpath(StringUtils.substringAfter(productInfo.getFullpath(), ">"));
                    }
                }
                GtProductUtils.tranGtProducts(productInfoList);
                long time = System.currentTimeMillis() - now;
                if (time >1000) {
                    log.info("调用产品系统/api/publish/findSkuInfos,耗时->{}ms,sku数量->{}", time, articleNumberList.size());
                }
                return productInfoList;
            }

            //查询ES
            SingleItemEsRequest criteria = new SingleItemEsRequest();
            criteria.setSkuList(articleNumberList);
            SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
            List<SingleItemEs> singleItemEsList = singleItemEsService.getSingleItemEsList(criteria);
            if (CollectionUtils.isNotEmpty(singleItemEsList)) {
                productInfoList = SingleItemEsUtils.tranSingleItemEsToProductInfo(singleItemEsList);
            }
            long timeES = System.currentTimeMillis() - now;
            if (timeES > 2000) {
                log.info("/findSkuInfos查询ES->singleitemes,耗时->{}ms,sku数量->{}", timeES, articleNumberList.size());
            }

            return productInfoList;
        } catch (Exception e) {
            log.error("调用产品系统 findProductInfos异常，param:{},error:{}", StringUtils.join(articleNumberList,","), e.getMessage(), e);
        }
        return productInfoList;
    }

    /**
     * 根据sku查询对应信息 指定查询返回字段
     * @param articleNumberList
     * @param fields mainSku sonSku 必须要有否则转换方法会忽略掉此对象
     * @return
     */
    public static List<ProductInfo> findProductInfos(List<String> articleNumberList, String[] fields) {
        if (CollectionUtils.isEmpty(articleNumberList)) {
            return new ArrayList<>();
        }
        boolean gtFlag = false;
        List<ProductInfo> productInfoList = new ArrayList<>();
        for (String articleNumber : articleNumberList) {
            if (articleNumber.startsWith("GT")) {
                gtFlag = true;
                break;
            }
        }

        long now = System.currentTimeMillis();
        try {
            if (gtFlag) {
                String aa = productClient.findSkuInfos(articleNumberList);
                ApiResult<List<ProductInfo>> apiResult = JSON.parseObject(aa, new TypeReference<ApiResult<List<ProductInfo>>>() {
                });
                productInfoList = apiResult.getResult();

                // 去掉一级分类
                if (CollectionUtils.isNotEmpty(productInfoList)) {
                    for (ProductInfo productInfo : productInfoList) {
                        productInfo.setFullpath(StringUtils.substringAfter(productInfo.getFullpath(), ">"));
                    }
                }

                GtProductUtils.tranGtProducts(productInfoList);
                long time = System.currentTimeMillis() - now;
                if (time >1000) {
                    log.info("调用产品系统/api/publish/findSkuInfos,耗时->{}ms,sku数量->{}", time, articleNumberList.size());
                }
            } else {
                //查询ES
                SingleItemEsRequest criteria = new SingleItemEsRequest();
                criteria.setSkuList(articleNumberList);
                criteria.setFields(fields);
                SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
                List<SingleItemEs> singleItemEsList = singleItemEsService.getSingleItemEsList(criteria);
                if (CollectionUtils.isNotEmpty(singleItemEsList)) {
                    productInfoList = SingleItemEsUtils.tranSingleItemEsToProductInfo(singleItemEsList);
                }
                long timeES = System.currentTimeMillis() - now;
                if (timeES > 2000) {
                    log.info("/findSkuInfos查询ES->singleitemes,耗时->{}ms,sku数量->{}", timeES, articleNumberList.size());
                }
            }
            return productInfoList;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return productInfoList;
    }

    /**
     * 指定查询返回字段
     *
     * @param articleNumberList
     * @param fields            mainSku sonSku 必须要有否则转换方法会忽略掉此对象
     * @return
     */
    public static List<ProductInfo> findSkuInfosByEs(List<String> articleNumberList, String[] fields) {
        if (CollectionUtils.isEmpty(articleNumberList)) {
            return Collections.emptyList();
        }

        List<ProductInfo> productInfoList = new ArrayList<>();
        long now = System.currentTimeMillis();
        try {
            //查询ES
            SingleItemEsRequest criteria = new SingleItemEsRequest();
            criteria.setSkuList(articleNumberList);
            criteria.setFields(fields);
            SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
            List<SingleItemEs> singleItemEsList = singleItemEsService.getSingleItemEsList(criteria);
            if (CollectionUtils.isNotEmpty(singleItemEsList)) {
                productInfoList = SingleItemEsUtils.tranSingleItemEsToProductInfo(singleItemEsList);
            }
            long timeES = System.currentTimeMillis() - now;
            if (timeES > 2000) {
                log.info("/findSkuInfos查询ES->singleitemes,耗时->{}ms,sku数量->{}", timeES, articleNumberList.size());
            }

            return productInfoList;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Collections.emptyList();
    }


    /**
     * 根据sku查询对应主图信息
     *
     * @return
     */
    public static List<ProductInfo> findSkuImage(List<String> articleNumberList) {
        if (CollectionUtils.isEmpty(articleNumberList)) {
            return Collections.emptyList();
        }
        boolean gtFlag = false;
        for (String articleNumber : articleNumberList) {
            if (articleNumber.startsWith("GT")) {
                gtFlag = true;
                break;
            }
        }

        try {
            if (gtFlag) {
                List<ProductInfo> productInfoList = new ArrayList<>();
                List<List<String>> skuLists = PagingUtils.pagingList(articleNumberList, 500);
                for (List<String> skus : skuLists) {
                    long now = System.currentTimeMillis();
                    String info = productClient.findSkuImage(skus);
                    ApiResult<List<ProductInfo>> apiResult = JSON.parseObject(info, new TypeReference<ApiResult<List<ProductInfo>>>() {
                    });
                    productInfoList.addAll(apiResult.getResult());
                    long time = System.currentTimeMillis() - now;
                    if (time > 2000) {
                        log.info("调用产品系统/api/publish/findSkuImage,耗时->{}ms,sku数量->{}", time, skus.size());
                    }
                }
                return productInfoList;
            } else {
                return findESSkuImageByMainSku(articleNumberList);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据主sku查询
     * [
     * {
     * "mainSku": "a",
     * "sonSku": "a-1",
     * "firstImage": "http://10.100.1.200:8888/amazon/2020-09/19-12-40-09-599/a-1.jpg",
     * "firstImageAdd": null
     * }
     * ]
     *
     * @param articleNumberList
     * @return
     */
    public static List<ProductInfo> findESSkuImageByMainSku(List<String> articleNumberList) {
        if (CollectionUtils.isEmpty(articleNumberList)) {
            return new ArrayList<>(0);
        }
        long now = System.currentTimeMillis();
        SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);

        List<ProductInfo> result = new ArrayList<>(articleNumberList.size());
        List<List<String>> pagingList = PagingUtils.pagingList(articleNumberList, 1024);
        for (List<String> list : pagingList) {
            List<SingleItemEs> singleItemEsList = singleItemEsService.getSingleItemEsList(list);
            if (CollectionUtils.isNotEmpty(singleItemEsList)) {
                List<ProductInfo> productInfoList = SingleItemEsUtils.tranSingleItemEsToProductInfo(singleItemEsList);
                result.addAll(productInfoList);
            }
        }
        long time = System.currentTimeMillis() - now;
        if (time > 2000) {
            log.info(" 查询Es findESSkuImageByMainSku /api/publish/findSkuImage,耗时->{}ms,sku数量->{}", time, articleNumberList.size());
        }
        return result;
    }

    /**
     * 根据sku集合查询单品状态为'Stop', 'Archived'的sku
     *
     * @return
     */
    public static List<String> findStopAndArchivedSku(List<String> skuList) {
        List<String> articleNumberList = new ArrayList<>();
        try {
            //查询Redis
            Map<String, Object> articleNumberMap = findStopAndArchivedByRedis(skuList);
            //判断返参是否需要调用接口查询
            if (Boolean.parseBoolean(articleNumberMap.get("isCall").toString())) {
                List<List<String>> skuLists = PagingUtils.pagingList(skuList, 500);
                for (List<String> skus : skuLists) {
                    long now = System.currentTimeMillis();
                    ApiResult<List<String>> apiResult = JSON.parseObject(productClient.findStopAndArchived(skus), new TypeReference<ApiResult<List<String>>>() {
                    });
                    if (apiResult.isSuccess()) {
                        articleNumberList.addAll(apiResult.getResult());
                    }
                    long time = System.currentTimeMillis() - now;
                    if (time > 2000) {
                        log.info("调用调用产品系统/api/publish/findStopAndArchived,耗时->{}ms,sku数量->{}", time, skus.size());
                    }
                }
            } else {
                articleNumberList = (ArrayList) articleNumberMap.get("articleNumberList");
            }
            return articleNumberList;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return articleNumberList;
    }

    private static Map<String, Object> findStopAndArchivedByRedis(List<String> skuList) {
        long now = System.currentTimeMillis();
        Map<String, Object> map = new HashMap<>();
        //判断是否调用接口
        Boolean isCall = false;
        //返参list
        Set<String> articleNumberList = new HashSet<>();
        try {
            Set<String> stopSkuSet = getStopSkuListSets();
            if (CollectionUtils.isEmpty(stopSkuSet)) {
                map.put("isCall", true);
                return map;
            }

            //取交集
            for (String sku : skuList) {
                if (stopSkuSet.contains(sku)) {
                    articleNumberList.add(sku);
                }
            }

            //判断入出参元素个数是否相等，相等则代表全都是停产的，就不查询存档Redis，不相等则再判断一次存档的Redis
            if (articleNumberList.size() != skuList.size()) {
                Set<String> archivedSkuSet = getArchivedSkuSets();
                if (CollectionUtils.isEmpty(archivedSkuSet)) {
                    map.put("isCall", true);
                    return map;
                }

                //取交集
                for (String sku : skuList) {
                    if (archivedSkuSet.contains(sku)) {
                        articleNumberList.add(sku);
                    }
                }
            }
            map.put("articleNumberList", new ArrayList<>(articleNumberList));
            map.put("isCall", isCall);
        } catch (Exception e) {
            log.error("从redis 查询 findStopAndArchived, 异常：{}", e.getMessage(), e);
        }
        long redisTime = System.currentTimeMillis() - now;
        if (redisTime > 1000) {
            log.info("从redis 查询 findStopAndArchived,耗时->{}ms,sku数量->{}", redisTime, skuList.size());
        }
        return map;
    }

    /**
     * 从redis中获取特定状态的sku
     */
    public static List<String> listByStatus(String status) {
        String skuArrayStr;
        switch (status) {
            case "New":
                skuArrayStr = PublishRedisClusterUtils.get("product_new_sku_key");
                return JSONArray.parseArray(skuArrayStr, String.class);
            case "Waiting":
                skuArrayStr = PublishRedisClusterUtils.get("product_waiting_sku_key");
                return JSONArray.parseArray(skuArrayStr, String.class);
            case "Normal":
                skuArrayStr = PublishRedisClusterUtils.get("product_normal_sku_key");
                return JSONArray.parseArray(skuArrayStr, String.class);
            case "Clearance":
                skuArrayStr = PublishRedisClusterUtils.get("product_clearance_sku_key");
                return JSONArray.parseArray(skuArrayStr, String.class);
            case "Pending":
                skuArrayStr = PublishRedisClusterUtils.get("product_pending_sku_key");
                return JSONArray.parseArray(skuArrayStr, String.class);
            case "Holiday":
                skuArrayStr = PublishRedisClusterUtils.get("product_holiday_sku_key");
                return JSONArray.parseArray(skuArrayStr, String.class);
            case "Reduction":
                skuArrayStr = PublishRedisClusterUtils.get("product_reduction_sku_key");
                return JSONArray.parseArray(skuArrayStr, String.class);
            case "Stop":
                skuArrayStr = PublishRedisClusterUtils.get("product_stop_sku_key");
                return JSONArray.parseArray(skuArrayStr, String.class);
            case "Archived":
                skuArrayStr = PublishRedisClusterUtils.get("product_archived_sku_key");
                return JSONArray.parseArray(skuArrayStr, String.class);
            default:
                return Lists.newArrayList();
        }
    }

    /**
     * 获取暂停，休假变正常状态的sku
     */
    public static ApiResult<List<String>> getPendingAndReductionRecoverSkus() {
        try {
            return productClient.getSkusByStateChange();
        } catch (Exception e) {
            log.error("调用获取暂停，休假变正常状态的sku 异常,{}", e.getMessage(), e);
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 根据时间获取侵权信息版本2
     *
     * @param startTime
     * @param endTime
     */
    public static String getInfInfoByTime(String startTime, String endTime) {
        String skuMap = null;
        Map<String, String> body = new HashMap<>(2);
        body.put("startTime", startTime);
        body.put("endTime", endTime);
        ApiResult<?> apiResult = productClient.getInfInfoByTimeV2(body);
        if (apiResult.isSuccess()) {
            skuMap = JSON.toJSONString(apiResult.getResult());
        }
        return skuMap;
    }

    /**
     * 根据时间查询禁售信息
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static ApiResult<Map<String, String>> getForbiddenByDate(String startTime, String endTime) {
        Map<String, String> body = new HashMap<>(2);
        body.put("startTime", startTime);
        body.put("endTime", endTime);
        long now = System.currentTimeMillis();
        try {
            ApiResult<Map<String, String>> apiResult = productClient.getForbiddenByDate(body);
            long time = System.currentTimeMillis() - now;
            if (time > 2000) {
                log.info("调用产品系统/api/publish/getForbiddenByDate,耗时->{}ms", time);
            }
            if (!apiResult.isSuccess()) {
                log.error("请求禁售信息接口返回结果失败：" + apiResult.getErrorMsg());
            }
            return apiResult;
        } catch (Exception e) {
            return ApiResult.newError("调用产品系统异常," + e.getMessage());
        }
    }


    /**
     * 获取所有sku的数量
     */
    public static ApiResult<Integer> getSkuCount() {
        long now = System.currentTimeMillis();
        try {
            ApiResult<Integer> apiResult = productClient.getSkuCount();
            long time = System.currentTimeMillis() - now;
            if (time > 2000) {
                log.info("调用产品系统/api/publish/findSkuInfos,耗时->{}ms", time);
            }
            return apiResult;
        } catch (Exception e) {
            return ApiResult.newError("调用产品系统异常，" + e.getMessage());
        }
    }


    /**
     * 移除不能刊登的sku状态的单品
     * 默认情况下 停产、存档、废弃状态的sku不能刊登
     * @param productInfos      单品
     */
    public static void filterCantPublishSkuStatusProduct(List<ProductInfo> productInfos) {
        filterSkuStatusProduct(productInfos, PublishCommonConstant.INTERCEPT_EN_STATE_LIST);
    }

    /**
     * 过滤插头规格不符合刊登站点的数据
     * @param productInfoList   数据
     * @param site              站点
     */
    public static void filterCountryOfPlugSpecification(List<ProductInfo> productInfoList,String site) {
        productInfoList.removeIf(productInfo -> {
            String plugSpecification = productInfo.getPlugSpecification();
            if (StringUtils.isBlank(plugSpecification)) {
                return false;
            }
            // 插头规格是否适用
            List<String> countryOfPlugSpecification = ProductUtils.getCountryOfPlugSpecification(plugSpecification);
            if (CollectionUtils.isEmpty(countryOfPlugSpecification)) {
                return false;
            }
            return !countryOfPlugSpecification.contains(site);
        });

    }

    /**
     * 校验sku是否符合插头规格
     * @param skuList
     * @param country
     */
    public static void validationCountryOfPlugSpecification(List<String> skuList, String country) {
        SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
        List<SingleItemEs> singleItemEsList = singleItemEsService.getSkuPlugSpecification(skuList);
        if (CollectionUtils.isNotEmpty(singleItemEsList)) {
            List<ProductInfo> productInfoList = SingleItemEsUtils.tranSingleItemEsToProductInfo(singleItemEsList);
            if (CollectionUtils.isNotEmpty(productInfoList)) {
                filterCountryOfPlugSpecification(productInfoList, country);
            }

            if (CollectionUtils.isEmpty(productInfoList)) {
                throw new BusinessException("插头规格不符合当前站点，不允许刊登!");
            }
            skuList.removeIf(sku-> productInfoList.stream()
                    .noneMatch(productInfo -> sku.equals(productInfo.getSonSku()) || sku.equals(productInfo.getMainSku())));
        }
    }

    /**
     * 移除指定sku状态的单品数据
     * @param productInfos      单品
     * @param filterSkuStatus   过滤的sku状态
     */
    public static void filterSkuStatusProduct(List<ProductInfo> productInfos, List<String> filterSkuStatus) {
        if (CollectionUtils.isEmpty(productInfos) || CollectionUtils.isEmpty(filterSkuStatus)) {
            return;
        }
        productInfos.removeIf(productInfo -> filterSkuStatus.contains(productInfo.getItemStatus()));
    }

    /**
     * 分页获取子sku和主sku对应的map
     */
    public static ApiResult<Map<String, String>> getSMSkuMapByOffSite(Integer offSite, Integer size) {
        try {
            return productClient.getSkuByOffSite(offSite, size);
        } catch (Exception e) {
            log.error("调用产品系统异常：{}", e.getMessage(), e);
            return ApiResult.newError("调用产品系统异常，" + e.getMessage());
        }
    }

    /**
     * ES查询单个子SKU对应产品状态等
     *
     * @param sonSku
     * @return
     */
    public static ProductInfoVO getSkuInfo(String sonSku) {
        SingleItemEs singleItem = null;
        singleItem = ErpCommonUtils.getSingleItemForRedis(sonSku);
        if (ObjectUtils.isEmpty(singleItem)) {
            SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
            singleItem = singleItemEsService.getSkuInfo(sonSku);
        }

        return singleItemToProductInfoVO(singleItem);
    }

    /**
     * 根据SingleItemEs信息查询单个产品信息
     *
     * @param singleItem
     * @return
     */
    public static ProductInfoVO singleItemToProductInfoVO(SingleItemEs singleItem) {
        ProductInfoVO productInfoVO = new ProductInfoVO();
        if (null != singleItem) {
            productInfoVO.setMainSku(singleItem.getMainSku());
            productInfoVO.setSonSku(singleItem.getSonSku());
            Category category = singleItem.getCategory();
            if (!ObjectUtils.isEmpty(category)) {
                productInfoVO.setCatId(category.getId());
                productInfoVO.setCategoryFullPathCode(category.getFullpathcode());
                String categoryPath = category.getCategoryPath();
                StringBuilder sb1 = new StringBuilder();
                StringBuilder sb2 = new StringBuilder();
                if (!StringUtils.isEmpty(categoryPath)) {
                    String[] paths = categoryPath.split("-");
                    sb1.append(",");
                    for (int i = 1; i < paths.length; i++) {
                        sb1.append(paths[i]).append(",");
                    }
                }
                String cnNames = singleItem.getCategoryPath();
                if (!StringUtils.isEmpty(cnNames)) {
                    String[] cns = cnNames.split(">");
                    sb2.append(",");
                    for (int i = 0; i < cns.length; i++) {
                        sb2.append(cns[i]).append(",");
                    }
                }
                productInfoVO.setCategoryId(sb1.toString());
                productInfoVO.setCategoryCnName(sb2.toString());
            }
            List<String> salesProbition = singleItem.getSalesProbition();
            if (!ObjectUtils.isEmpty(salesProbition)) {
                StringBuilder sb = new StringBuilder();
                sb.append(",");
                for (int i = 0; i < salesProbition.size(); i++) {
                    sb.append(salesProbition.get(i));
                    sb.append(",");
                }
                productInfoVO.setForbidChannel(sb.toString());
            }
            if (!StringUtils.isEmpty(singleItem.getTag())) {
                productInfoVO.setTagCodes("," + singleItem.getTagCode() + ",");
                productInfoVO.setTagNames("," + singleItem.getTag() + ",");
            }

            List<SpecialGoods> goodsList = singleItem.getSpecialGoodsList();
            if (!ObjectUtils.isEmpty(goodsList)) {
                StringBuilder sb = new StringBuilder();
                StringBuilder sb1 = new StringBuilder();
                sb.append(",");
                sb1.append(",");
                for (int i = 0; i < goodsList.size(); i++) {
                    SpecialGoods goods = goodsList.get(i);
                    Integer specialType = goods.getSpecialType();
                    String name = SpecialTagEnum.getNameByCode(specialType);
                    sb.append(specialType);
                    sb1.append(name);

                    sb.append(",");
                    sb1.append(",");

                }
                productInfoVO.setSpecialGoodsCode(sb.toString());
                productInfoVO.setSpecialGoodsName(sb1.toString());
            }
            Integer itemStatus = singleItem.getItemStatus();
            if(itemStatus != null){
                productInfoVO.setSkuStatus(SingleItemEnum.getEnNameByCode(itemStatus));
            }
            productInfoVO.setCreateAt(singleItem.getCreateAt());
            SingleItemExternalInfo singleItemExternalInfo = singleItem.getSingleItemExternalInfo();
            if (singleItemExternalInfo != null) {
                //全部禁售信息
                String infringementSaleProhibition = singleItemExternalInfo.getInfringementSaleProhibition();
                List<InfringementSaleProhibitionVO> infringementSaleProhibitionVOList = singleItemExternalInfo.getAllInfringementSaleProhibitionVOList();
                //禁售类型
                productInfoVO.setInfringementTypeName(singleItemExternalInfo.getInfringementTypeName());
                //禁售原因
                productInfoVO.setInfringementObj(singleItemExternalInfo.getInfringementObj());
                // 风险等级
                productInfoVO.setRiskLevelIdList(singleItemExternalInfo.getRiskLevelIdList(infringementSaleProhibitionVOList));
                //侵权审核
                productInfoVO.setInfringementUser(singleItemExternalInfo.getInfringementUser());
                //滞销标签
                productInfoVO.setUnsalableTag(singleItemExternalInfo.getUnsalableTag());
            }

            //产品开发人员
            productInfoVO.setProductMan(singleItem.getDevEmpName());

            //禁售平台站点(上级方法自行过滤站点列表)
            String salesProhibition = singleItem.getSalesProhibition();
            if (StringUtils.isNotBlank(salesProhibition)) {
                List<SalesProhibitionsVo> salesProhibitionsVos = JSON.parseObject(salesProhibition, new TypeReference<List<SalesProhibitionsVo>>() {
                });

                if (CollectionUtils.isNotEmpty(salesProhibitionsVos)) {
                    //整合Map
                    productInfoVO.setSalesProhibition(salesProhibitionsVos.stream().collect(Collectors.toMap(o -> o.getPlat(), o -> o.getSites(),
                            (List<Sites> oldList, List<Sites> newList) -> {
                                //如果key重复，取并集，并且去重
                                oldList.removeAll(newList);
                                oldList.addAll(newList);
                                return oldList;
                            })));
                }
            }

            //是否促销
            productInfoVO.setPromotion(singleItem.getPromotion() == null ? 0 : singleItem.getPromotion());
            //是否新品
            productInfoVO.setNewState(singleItem.getNewState());
            //主图
            if (null !=singleItem.getSingleItemPicInfo()){

                productInfoVO.setMainImage(singleItem.getSingleItemPicInfo().getFirstImage());
            }
            //标题
            productInfoVO.setName(singleItem.getName());
            //销售成本价
            productInfoVO.setSaleCost(singleItem.getSaleCost());
            //包材code
            productInfoVO.setPackingMaterialsCode(singleItem.getPackingMaterialsCode());
            //搭配包材code
            productInfoVO.setMatchMaterialsCode(singleItem.getMatchMaterialsCode());
            //净重
            productInfoVO.setProductWeight(singleItem.getProductWeight());
            // 预估包裹重量
            productInfoVO.setPackageWeight(singleItem.getPackageWeight());
            // 单品来源
            productInfoVO.setSingleSource(singleItem.getSingleSource());
            // 插头规格
            productInfoVO.setPlugSpecification(singleItem.getPlugSpecification());

            if (Objects.nonNull(singleItem.getSingleItemAllSupplier())) {
                productInfoVO.setFirstProcurementPrice(singleItem.getSingleItemAllSupplier().getDefaultProcurementPrice());
            }
        }
        return productInfoVO;
    }

    /**
     * Amazon 获取最高级风险等级
     * 风险等级 A > B > C ,如果风险等级名字有ABC，则会取 A>B>C这里面最高的，如果没有ABC又存在多个就随机取一个
     * @return
     */
    public static Integer getHighRiskLevelId(List<Integer> riskLevelIdList) {
        if (CollectionUtils.isEmpty(riskLevelIdList)) {
            return null;
        }
        Integer highRiskLevelId = riskLevelIdList.get(0);
        if (riskLevelIdList.size() == 1) {
            return highRiskLevelId;
        }
        try {
            List<RiskLevel> riskLevelList = getAllRiskLevel();
            List<RiskLevel> riskLevels = riskLevelList.stream().filter(o -> riskLevelIdList.contains(o.getId().intValue())).collect(Collectors.toList());
            Map<String, Integer> riskLevelGradeMap = new HashMap<>(3);
            riskLevels.stream().forEach(o -> {
                if (o.getRiskLevel().contains("A")) {
                    riskLevelGradeMap.put("A", o.getId());
                }
                if (o.getRiskLevel().contains("B")) {
                    riskLevelGradeMap.put("B", o.getId());
                }
                if (o.getRiskLevel().contains("C")) {
                    riskLevelGradeMap.put("C", o.getId());
                }
            });
            highRiskLevelId = riskLevelGradeMap.containsKey("A") ? riskLevelGradeMap.get("A") : riskLevelGradeMap.containsKey("B") ? riskLevelGradeMap.get("B") : riskLevelGradeMap.containsKey("C") ? riskLevelGradeMap.get("C") : riskLevels.get(0).getId();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return highRiskLevelId;
    }

    /**
     * 获取 风险等级id 和名字
     * @return
     */
    public static Map<Integer,String> getRiskLevelNameMap() {
        Map<Integer,String> riskLevelIdAndNameMap = new HashMap<>();
        try {
            List<RiskLevel> riskLevelList = getAllRiskLevel();
            if (CollectionUtils.isNotEmpty(riskLevelList)) {
                riskLevelList.stream().forEach(o -> {
                    riskLevelIdAndNameMap.put(o.getId(), o.getRiskLevel());
                });
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return riskLevelIdAndNameMap;
    }


    /**
     * 获取所有的风险等级
     * @return
     */
    public static List<RiskLevel> getAllRiskLevel(){
        List<RiskLevel> riskLevelList = new ArrayList<>();
        riskLevelList = PRODUCT_RISK_LEVEL_CONFIG_INFO_CACHE.getIfPresent(ProductApiCacheEnum.RISK_LEVEL_CONFIG_INFO.name());
        if (CollectionUtils.isEmpty(riskLevelList)) {
            ApiResult<List<RiskLevel>> apiResult = productClient.getRiskLevelList();
            if (!apiResult.isSuccess()) {
                throw new BusinessException("调用产品系统风险等级异常"+ apiResult.getErrorMsg());
            }
            PRODUCT_RISK_LEVEL_CONFIG_INFO_CACHE.put(ProductApiCacheEnum.RISK_LEVEL_CONFIG_INFO.name(), apiResult.getResult());
            riskLevelList = apiResult.getResult();
        }
        return riskLevelList;
    }


    /**
     * 按照时间范围查询停产存档SKU列表
     *
     * @param beginTime：开始时间
     * @param endTime：结束时间
     * @return
     */
    public static List<String> getChangeStopArchivedSku(String beginTime, String endTime) {
        List<String> skuList = Lists.newArrayList();
        try {
            // 获取单品管理停产存档sku
            Map<String, Object> body = new HashMap<>(3);
            body.put("beginTime", beginTime);
            body.put("endTime", endTime);
            ApiResult<List<String>> apiResult = productClient.getChangeStopArchivedSku(body);
            if (!apiResult.isSuccess()) {
                throw new RuntimeException(apiResult.getErrorMsg());
            }
            if (CollectionUtils.isNotEmpty(apiResult.getResult())) {
                skuList.addAll(apiResult.getResult());
            }

            // 获取组合套装停产存档sku
            List<Integer> skuStatusList = Lists.newArrayList(SingleItemEnum.ARCHIVED.getCode(), SingleItemEnum.STOP.getCode());
            for (Integer skuStatus : skuStatusList) {
                body.put("status", skuStatus);
                ApiResult<List<String>> composeSuiteResult = productClient.getComposeSuiteSkuByItemStatusAndUpdateTime(body);
                if (!composeSuiteResult.isSuccess()) {
                    throw new RuntimeException(composeSuiteResult.getErrorMsg());
                }
                if (CollectionUtils.isNotEmpty(composeSuiteResult.getResult())) {
                    skuList.addAll(composeSuiteResult.getResult());
                }
            }

            return skuList;
        } catch (Exception e) {
            throw new RuntimeException("按照时间范围查询停产存档SKU列表报错：" + e.getMessage());
        }
    }

    /**
     * 获取所有的分类
     *
     * @return
     */
    public static List<ProductCategoryInfo> getProductSystemAllCategory() {
        try {
            ApiResult<List<ProductCategoryInfo>> apiResult = productClient.getProductSystemAllCategory();
            return apiResult.getResult();
        } catch (Exception e) {
            log.error("调用产品系统失败：{}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取sku变更信息推送刊登看板
     *
     * @param request
     * @return
     */
    public static List<SkuChangeInfoDO> getSkuChangeInfoEs(SkuChangeRequest request) {
        try {
            ApiResult<PageResult<SkuChangeInfoDO>> apiResult = productClient.getSkuChangeInfoEs(request);
            if (apiResult.isSuccess()) {
                PageResult<SkuChangeInfoDO> result = apiResult.getResult();
                if (result.getRecords() == null) {
                    return Collections.emptyList();
                }
                return result.getRecords();
            }
            log.error("获取sku变更信息推送刊登看板失败,{}", apiResult.getErrorMsg());
        } catch (Exception e) {
            log.error("调用产品系统失败：{}", e.getMessage(), e);
        }
        return Collections.emptyList();

    }

    /**
     * 获取所有定制产品
     *
     * @return
     */
    public static ApiResult<List<String>> getAllCustomMade() {
        List<String> customMadeList = Lists.newArrayList();

        // 先查redis redis没有查接口
        String value = PublishRedisClusterUtils.get(RedisConstant.PRODUCT_SINGLE_SPECIAL_SKU_2001);
        if (StringUtils.isNotBlank(value)) {
            customMadeList = JSON.parseArray(value, String.class);
        }

        if (CollectionUtils.isEmpty(customMadeList)) {
            ApiResult<List<String>> result = productClient.getAllCustomMade();
            if (!result.isSuccess()) {
                return ApiResult.newError(result.getErrorMsg());
            }
            customMadeList = result.getResult();
        }

        return ApiResult.newSuccess(customMadeList);
    }

    /**
     * 根据sku状态和货源地获取该货源地的sku集合
     * @param skuStatusList
     * @param supply
     * @return
     */
    public static List<String> getAllSkuBySupplyAndStatus(List<Integer> skuStatusList, String supply) {
        if (StringUtils.isBlank(supply) || CollectionUtils.isEmpty(skuStatusList)) {
            return Collections.emptyList();
        }

        List<String> skuList = new ArrayList<>();

        Integer limit = 1000;
        Integer offSet = 1;
        Map<String, Object> data = new HashMap<>(4);
        data.put("itemStatus", skuStatusList);
        data.put("supply", supply);
        data.put("limit", limit);
        while (true) {
            data.put("offSet", offSet);
            ApiResult<List<String>> result = productClient.getSonSkuBySupplyAndItemStatus(data);
            if (!result.isSuccess()) {
                log.error(result.getErrorMsg());
                return Collections.emptyList();
            }
            if (CollectionUtils.isEmpty(result.getResult())) {
                break;
            }

            skuList.addAll(result.getResult());

            // 该产品接口的参数offset对应的是页码
            offSet ++;
        }

        return skuList;
    }
    /**
     * 提供根据spu批量查询侵权词
     * @param platform  平台
     * @param spu       spu
     */
    public static ApiResult<SpuInfringementWordDO> getSpuInfringementWord(String platform, String spu) {
        if (StringUtils.isBlank(platform) || StringUtils.isBlank(spu)) {
            return ApiResult.newError(" platform spu 不能为空");
        }
        try {
            Map<String, String> param = new HashMap<>();
            param.put("platform", platform);
            param.put("spu", spu);
            return productClient.getInfringementWordBySpu(param);
        } catch (Exception e) {
            log.error("call product exception:{}",e.getMessage());
            return ApiResult.newError("调用产品系统获取侵权词异常："+e.getMessage());
        }
    }

    /**
     * 获取分类
     * @param sku
     */
    public static Map<String, Category> getCategoryBySku(List<String> sku) {
        if (CollectionUtils.isEmpty(sku)) {
            return Collections.emptyMap();
        }
        try {
            SingleItemEsRequest criteria = new SingleItemEsRequest();
            criteria.setSkuList(sku);
            criteria.setFields(new String[]{"mainSku", "sonSku", "category"});
            SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
            List<SingleItemEs> singleItemEsList = singleItemEsService.getSingleItemEsList(criteria);

            return singleItemEsList.stream().collect(Collectors.toMap(item -> {
                if (sku.contains(item.getSonSku())) {
                    return item.getSonSku();
                }else {
                    return item.getMainSku();
                }
            }, SingleItemEs::getCategory, (o1, o2)->o1));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return Collections.emptyMap();
    }

    /**
     * 查询套装
     * @param begin
     * @param end
     * @return
     */
    public static ResponseJson getSaleSuiteSku(Integer begin, Integer end) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        if (begin == null || end == null) {
            rsp.setMessage("begin，end不能为空！");
            return rsp;
        }

        SaleSuiteRequest request = new SaleSuiteRequest();
        request.setBegin(begin);
        request.setEnd(end);

        try {
            ApiResult<List<String>> result = productClient.getSaleSuiteSku(request);
            if (result.isSuccess()) {
                rsp.getBody().put(resultKey, result.getResult());
                rsp.setStatus(StatusCode.SUCCESS);
            } else {
                rsp.setMessage(result.getErrorMsg());
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setStatus(StatusCode.FAIL);
            rsp.setMessage("查询套装:" + e.getMessage());
        }
        return rsp;
    }

    /**
     * 获取产品系统所有产品标签
     */
    public static List<TagCode> getAllTagCode() {
        try {
            String cacheJson = PublishRedisClusterUtils.get(PRODUCT_SKU_TAG);
            if (StringUtils.isNotBlank(cacheJson)) {
                return JSON.parseArray(cacheJson, TagCode.class);
            }

            ApiResult<List<TagCode>> result = productClient.getProductSkuTag();
            if (result.isSuccess()) {
                List<TagCode> tagCodes = result.getResult();
                PublishRedisClusterUtils.set(PRODUCT_SKU_TAG, JSON.toJSONString(tagCodes), 10L, TimeUnit.MINUTES);
                return tagCodes;
            }
        } catch (Exception e) {
            log.error("获取产品系统标签失败，{}",e.getMessage());
        }
        return Collections.emptyList();
    }

    /**
     * 获取产品系统的特殊标签
     * @return
     */
    public static List<SpecialTag> getAllSpecialTag(){
        try {
            String cacheJson = PublishRedisClusterUtils.get(RedisConstant.SPECIAL_SKU_TAG);
            if (StringUtils.isNotBlank(cacheJson)) {
                return JSON.parseArray(cacheJson, SpecialTag.class);
            }

            ApiResult<List<SpecialTag>> result = productClient.getSpecialTag();
            if (result.isSuccess()) {
                List<SpecialTag> specialTags = result.getResult();
                PublishRedisClusterUtils.set(RedisConstant.SPECIAL_SKU_TAG, JSON.toJSONString(specialTags), 10L, TimeUnit.MINUTES);
                return specialTags;
            }
        } catch (Exception e) {
            log.error("获取产品系统特殊标签失败，{}",e.getMessage());
        }
        return Collections.emptyList();
    }

    /**
     * 根据废弃sku获取产品系统合并SKU
     */
    public static String getMergeSku(String articleNumber) {
        String mergeSku = null;
        try {
            mergeSku = PublishRedisClusterUtils.hGet(RedisConstant.PRODUCT_MERGE_SKU, String.class, articleNumber);
            if (StringUtils.isNotBlank(mergeSku)) {
                return mergeSku;
            }

            ApiResult<String> result = productClient.getMergeSku(articleNumber);
            if (result.isSuccess() && StringUtils.isNotBlank(result.getResult())) {
                mergeSku = result.getResult().trim();
                PublishRedisClusterUtils.hSet(RedisConstant.PRODUCT_MERGE_SKU, articleNumber, mergeSku);
            }else {
                log.warn("{} 获取产品获取产品系统合并SKU失败返回错误信息，{}",articleNumber,result.getErrorMsg());
            }
        } catch (Exception e) {
            log.error("{} 获取产品获取产品系统合并SKU报错，{}",articleNumber,e.getMessage());
        }
        if (StringUtils.isBlank(mergeSku)){
            log.warn("{} 获取产品获取产品系统合并SKU为空",articleNumber);
            mergeSku = articleNumber;
        }
        return mergeSku;
    }

    /**
     * 根据组合SPU查询组合SKU信息
     */
    public static ComposeSku getComposeProduct(String spu) {
        try {
            ApiResult<ComposeSku> composeSkuApiResult = productClient.getComposeSkuInfoBySpu(spu);
            if (composeSkuApiResult.isSuccess()) {
                return composeSkuApiResult.getResult();
            }
            //log.error("调用产品根据组合SPU查询组合SKU信息失败：param:{}, res:{}", spu, JSON.toJSONString(composeSkuApiResult));
        } catch (Exception e) {
            log.error("{} 调用产品根据组合SPU查询组合SKU信息报错，{}", spu, e.getMessage());
        }
        return null;
    }

    /**
     * 根据套装货号获取对应组合SPU
     */
    public static Map<String,String> getComposeSkuBySuit(List<String> suitList) {
        try {
            ApiResult<Map<String, String>> composeSkuBySuit = productClient.getComposeSkuBySuit(suitList);
            if (composeSkuBySuit.isSuccess()) {
                return composeSkuBySuit.getResult();
            }
            //log.error("调用产品根据组合SPU查询组合SKU信息失败：param:{}, res:{}", suitList, JSON.toJSONString(composeSkuBySuit));
        } catch (Exception e) {
            log.error("{} 调用产品根据组合SPU查询组合SKU信息报错，{}", suitList, e.getMessage());
            throw new RuntimeException(e);
        }
        return Maps.newHashMap();
    }

    /**
     * 获取组合套装主子sku入参可能是主也可能是子
     * @param composeSku
     * @return
     */
    public static ComposeAndSuiteSkuResponse getComposeAndSuiteSku(List<String> composeSku) {
        try {
            ApiResult<ComposeAndSuiteSkuResponse> composeAndSuiteSku = productClient.getComposeAndSuiteSku(composeSku);
            if (composeAndSuiteSku.isSuccess()) {
                return composeAndSuiteSku.getResult();
            }
        } catch (Exception e) {
            log.error("{} 调用产品根获取组合套装主子sku入参可能是主也可能是子获取sku列表报错，{}", composeSku, e.getMessage());
            throw new RuntimeException(e);
        }
        return new ComposeAndSuiteSkuResponse();
    }

    /**
     * 批量根据单品状态和时间查询该状态被标记了n天的sku
     */
    public static List<String> batchGetSkuByItemStatusAndTime(List<SingleItemEnum> singleItemEnums, String date) {
        List<String> skuList = new ArrayList<>();
        for (SingleItemEnum singleItemEnum : singleItemEnums) {
            List<String> skus = getSkuByItemStatusAndTime(singleItemEnum, date);
            if (CollectionUtils.isEmpty(skus)) {
                continue;
            }
            skuList.addAll(skus);
        }
        return skuList;
    }

    /**
     * 根据单品状态和时间查询该状态被标记了n天的sku
     * @param singleItemEnum
     * @param date
     * @return
     */
    public static List<String> getSkuByItemStatusAndTime(SingleItemEnum singleItemEnum, String date) {
        List<String> skuList = new ArrayList<>();

        try {
            // 查询单品管理数据
            SkuStatusRequest request = new SkuStatusRequest();
            request.setItemStatus(singleItemEnum.getCode());
            request.setBeginDate(date);
            ApiResult<List<String>> result = productClient.getSkuByItemStatusAndTime(request);
            if (!result.isSuccess()) {
                throw new RuntimeException(result.getErrorMsg());
            }
            if (CollectionUtils.isNotEmpty(result.getResult())) {
                skuList.addAll(result.getResult());
            }

            // 查询组合套装数据
            Map<String, Object> body = new HashMap<>(2);
            body.put("beginTime", date);
            body.put("status", singleItemEnum.getCode());
            ApiResult<List<String>> composeSuiteResult = productClient.getComposeSuiteSkuByItemStatusAndUpdateTime(body);
            if (!composeSuiteResult.isSuccess()) {
                throw new RuntimeException(composeSuiteResult.getErrorMsg());
            }
            if (CollectionUtils.isNotEmpty(composeSuiteResult.getResult())) {
                skuList.addAll(composeSuiteResult.getResult());
            }

            return skuList;
        } catch (Exception e) {
            throw new RuntimeException(String.format("调用产品系统根据单品状态和时间查询该状态被标记了n天的sku报错：%s", e.getMessage()));
        }
    }

    /**
     * 根据分类完整路径code返回所有只节点类目id
     * @param fullCodeList
     * @return
     */
    public static List<Integer> getAllCodeByFullPath(List<String> fullCodeList) {
        if(CollectionUtils.isEmpty(fullCodeList)){
            return new ArrayList<>();
        }
        try {
            ApiResult<List<Integer>> result = productClient.getAllCodeByFullPath(fullCodeList);
            if (!result.isSuccess()) {
                throw new RuntimeException(result.getErrorMsg());
            }
            return result.getResult();
        } catch (Exception e) {
            throw new RuntimeException(String.format("调用产品系统根据分类完整路径code返回所有只节点类目id报错：%s", e.getMessage()));
        }
    }

    /**
     * 根据货号获取套装明细
     */
    public static SuiteSku getSaleSuiteInfoBySonSku(String suiteSku) {
        if (StringUtils.isBlank(suiteSku)) {
            return null;
        }
        try {
            ApiResult<SuiteSku> result = productClient.getSaleSuiteVOBySuiteSku(UriUtils.encode(suiteSku, StandardCharsets.UTF_8));
            if (!result.isSuccess()) {
                log.error(suiteSku + "调用产品系统,根据货号获取套装明细失败：{}",result.getErrorMsg());
                return null;
            }
            return result.getResult();
        } catch (Exception e) {
            log.error(suiteSku +  "调用产品系统,根据货号获取套装明细失败：{}",e.getMessage());
            return null;
        }
    }

    /**
     * 根据货号判断是否在套装管理中
     */
    public static Boolean isExistSaleSuite(String suiteSku) {
        if (StringUtils.isBlank(suiteSku)) {
            throw new IllegalArgumentException("货号不能为空");
        }
        try {
            return productClient.isExistSaleSuite(UriUtils.encode(suiteSku, StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("调用产品系统,根据货号判断是否在套装管理中失败：{}",e.getMessage());
            throw new RemoteAccessException("调用产品系统,根据货号判断是否在套装管理中失败："+e.getMessage());
        }
    }

    /**
     * 根据时间查看此时间段内标题更新的sku
     * @param beginTime
     * @param endTime
     * @return
     */
    public static List<String> getTitleChangeSku(String beginTime, String endTime) {
        if (StringUtils.isBlank(beginTime) || StringUtils.isBlank(endTime)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        Map<String, String> param = new HashMap<>(2);
        param.put("beginTime", beginTime);
        param.put("endTime", endTime);
        try {
            ApiResult<List<String>> apiResult = productClient.getTitleChangeSku(param);
            if (!apiResult.isSuccess()) {
                throw new RuntimeException("调用产品系统报错：" + apiResult.getErrorMsg());
            }
            return apiResult.getResult();
        } catch (Exception e) {
            throw new RuntimeException("调用产品系统报错：" + e.getMessage());
        }
    }

    /**
     * 根据时间查看此时间段内描述更新的sku
     * @param beginTime
     * @param endTime
     * @return
     */
    public static List<String> getDescriptionChangeSku(String beginTime, String endTime) {
        if (StringUtils.isBlank(beginTime) || StringUtils.isBlank(endTime)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        Map<String, String> param = new HashMap<>(2);
        param.put("beginTime", beginTime);
        param.put("endTime", endTime);
        try {
            List<String> skuList = new ArrayList<>();
            ApiResult<?> apiResult = productClient.getSkuChangeSku(param);
            if (!apiResult.isSuccess()) {
                throw new RuntimeException("调用产品系统报错：" + apiResult.getErrorMsg());
            }
            if (null == apiResult.getResult()) {
                return skuList;
            }
            Map<Integer, List<String>> map = (Map<Integer, List<String>>) apiResult.getResult();
            if (MapUtils.isEmpty(map)) {
                return skuList;
            }
            skuList = map.get(1000);
            return skuList;
        } catch (Exception e) {
            throw new RuntimeException("调用产品系统报错：" + e.getMessage());
        }
    }

    /**
     * 根据时间查看此时间段内标题更新的mainSku
     * @param beginTime
     * @param endTime
     * @return
     */
    public static List<String> getImageChangeMainSku(String beginTime, String endTime) {
        if (StringUtils.isBlank(beginTime) || StringUtils.isBlank(endTime)) {
            throw new IllegalArgumentException("参数不能为空");
        }
        List<String> imagesMainSkus = new ArrayList<>();
        SkuChangeRequest request = new SkuChangeRequest();
        request.setPageSize(500);
        request.setType(Collections.singletonList(1001));
        request.setBeginTime(beginTime);
        request.setEndTime(endTime);
        int page = 1;
        try {
            while (true) {
                request.setPageIndex(page);
                ApiResult<PageResult<SkuChangeInfoDO>> apiResult = productClient.getSkuChangeInfoEs(request);
                if (!apiResult.isSuccess()) {
                    log.error("调用产品系统报错：" + apiResult.getErrorMsg());
                    break;
                }
                PageResult<SkuChangeInfoDO> result = apiResult.getResult();
                List<String> mainSku = result.getRecords().stream()
                        .map(SkuChangeInfoDO::getMainSku)
                        .collect(Collectors.toList());
                imagesMainSkus.addAll(mainSku);
                if (result.getPages() > page) {
                    page ++;
                }else {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("调用产品系统报错：{}", e.getMessage(), e);
        }
        return imagesMainSkus.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 提供所有子节点类目(只需要子节点的相关类目)
     * http://172.16.10.40/web/#/27/7761
     */
    public static ApiResult<List<ProductCategoryResponse>> getAllSonFullPath(){
        try {
            return productClient.getAllSonFullPath();
        } catch (Exception e) {
            log.error("获取产品子节点类目异常: " + e.getMessage(), e);
            return ApiResult.newError("获取产品子节点类目异常: " + e.getMessage());
        }
    }

    /**
     * 根据spu查询产品系统存在的spu
     *
     * @param spuList
     * @return
     */
    public static List<String> getExistSpuBySpu(List<String> spuList) {
        if (CollectionUtils.isEmpty(spuList)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        List<String> existSpuList = new ArrayList<>();
        List<List<String>> lists = PagingUtils.newPagingList(spuList, 1000);
        for (List<String> list : lists) {
            try {
                ApiResult<List<String>> apiResult = productClient.getExistSpuBySpu(list);
                if (!apiResult.isSuccess()) {
                    throw new RuntimeException(apiResult.getErrorMsg());
                }
                if (CollectionUtils.isNotEmpty(apiResult.getResult())) {
                    existSpuList.addAll(apiResult.getResult());
                }
                return apiResult.getResult();
            } catch (Exception e) {
                throw new RuntimeException("调用产品系统报错：" + e.getMessage());
            }
        }

        return existSpuList;
    }

    /**
     * 根据主sku集合获取对应的类目code全路径
     * @param spus
     * @return
     */
    public static ApiResult<Map<String, String>> getFullpathcodeBySpu(List<String> spus){
        try {
            return productClient.getFullpathcodeBySpu(spus);
        } catch (Exception e) {
            log.error("获取产品子节点类目异常: " + e.getMessage(), e);
            return ApiResult.newError("获取产品子节点类目异常: " + e.getMessage());
        }
    }

    /**
     * 根据主sku获取对应的类目code全路径 优先查询接口 报错则查询es
     * @param spu
     * @return
     */
    public static String getFullpathcodeBySpu(String spu){
        ApiResult<Map<String, String>> apiResult = ProductUtils.getFullpathcodeBySpu(Arrays.asList(spu));
        if(apiResult.isSuccess() && MapUtils.isNotEmpty(apiResult.getResult())) {
            return apiResult.getResult().get(spu);
        }

        //查询es
        SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);
        return singleItemEsService.getFullpathcode(spu);
    }


    /**
     * 获取sku产品标签
     * @param skus 子skus, 属于同一个spu
     * @return tag
     */
    public static String getSkuProductTagWithSameSpu(List<String> skus) {
        String[] fields = {"mainSku", "sonSku", "tagCode", "tag"};
        List<ProductInfo> productInfoList = ProductUtils.findSkuInfosByEs(skus, fields);
        if (CollectionUtils.isEmpty(productInfoList)) {
            return null;
        }
        // 暂认为多个子sku标签一致,用于算价
        return productInfoList.get(0).getEnTag();
    }

    /**
     * 获取 spu CE认证
     *
     * @return key: spu_categoryId value: ce文件
     */
    public static ApiResult<Map<String, List<String>>> queryProductProprietary(List<QuerySpuCeFileRequest> querySpuCeFileRequests) {
        Map<String, List<String>> resultMap = new HashMap<>();
        try {
            List<String> spuList = querySpuCeFileRequests.stream().map(QuerySpuCeFileRequest::getSpu).collect(Collectors.toList());
            ApiResult<Map<String, Map<String, List<String>>>> mapApiResult = queryProductProprietaryV2(spuList);
            if (!mapApiResult.isSuccess()) {
                throw new RuntimeException(mapApiResult.getErrorMsg());
            }
            Map<String, Map<String, List<String>>> productProprietaryMap = mapApiResult.getResult();
            for (Map.Entry<String, Map<String, List<String>>> entry : productProprietaryMap.entrySet()) {
                // spu或spu分类对应文件
                Map<String, List<String>> fileToCeMap = entry.getValue();

                // 优先根据分类id获取文件，如果分类id文件夹下没有文件，则取spu对应的文件
                List<QuerySpuCeFileRequest> spuCeFileRequests = querySpuCeFileRequests.stream().filter(o -> entry.getKey().equals(o.getSpu())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(spuCeFileRequests)) {
                    continue;
                }
                for (QuerySpuCeFileRequest spuCeFileRequest : spuCeFileRequests) {
                    String spu = spuCeFileRequest.getSpu();
                    Integer categoryId = spuCeFileRequest.getCategoryId();
                    List<String> ceFileList = new ArrayList<>();
                    if (null != categoryId) {
                        ceFileList = fileToCeMap.get(categoryId.toString());
                    }
                    if (CollectionUtils.isEmpty(ceFileList)) {
                        ceFileList = fileToCeMap.get(spu);
                    }
                    if (CollectionUtils.isEmpty(ceFileList)) {
                        continue;
                    }
                    String key = spu + "_" + categoryId;
                    resultMap.put(key, ceFileList);
                }
            }
            return ApiResult.newSuccess(resultMap);
        } catch (Exception e) {
            log.error("获取产品CE认证异常: " + e.getMessage(), e);
            return ApiResult.newError("获取产品CE认证异常: " + e.getMessage());
        }
    }

    /**
     * 获取 spu CE认证
     */
    public static ApiResult<Map<String, Map<String, List<String>>>> queryProductProprietaryV2(List<String> spuList) {
        try {
            Map<String, Map<String, List<String>>> result = new HashMap<>();
            spuList = spuList.stream().distinct().collect(Collectors.toList());
            List<List<String>> lists = PagingUtils.newPagingList(spuList, 100);
            for (List<String> list : lists) {
                Map<String, String> param = new HashMap<>();
                param.put("mainSku", StringUtils.join(list, ","));
                ApiResult<Map<String, Map<String, List<String>>>> mapApiResult = productClient.queryProductProprietaryV2(param);
                if (!mapApiResult.isSuccess()) {
                    throw new RuntimeException(mapApiResult.getErrorMsg());
                }
                if (MapUtils.isNotEmpty(mapApiResult.getResult())) {
                    for (Map.Entry<String, Map<String, List<String>>> stringMapEntry : mapApiResult.getResult().entrySet()) {
                        String spu = stringMapEntry.getKey();
                        //需要检查 是否存在 spu 下面的 -label 图片，如果存在，就需要分配到分类文件夹
                        Map<String, List<String>> map = stringMapEntry.getValue();
                        List<String> urlList = map.get(spu);
                        if(CollectionUtils.isNotEmpty(urlList)){
                            List<String> labelUrlList = urlList.stream().filter(t -> StringUtils.contains(t, "-label")).collect(Collectors.toList());
                            if(CollectionUtils.isNotEmpty(labelUrlList)){
                                for (Map.Entry<String, List<String>> stringListEntry : map.entrySet()) {
                                    String key = stringListEntry.getKey();
                                    if(!StringUtils.equalsIgnoreCase(spu, key)){
                                        List<String> value = stringListEntry.getValue();
                                        value.addAll(labelUrlList);
                                    }
                                }
                            }
                        }
                    }
                    result.putAll(mapApiResult.getResult());
                }
            }
            return ApiResult.newSuccess(result);
        } catch (Exception e) {
            log.error("调用产品系统：获取产品CE认证异常: " + e.getMessage(), e);
            return ApiResult.newError("调用产品系统：获取产品CE认证异常: " + e.getMessage());
        }
    }

    public static Map<String, Integer> getComposeSuiteItemStatusBySku(List<String> articleNumberList) {
        if (CollectionUtils.isEmpty(articleNumberList)) {
            return null;
        }

        try {
            ApiResult<Map<String, Integer>> result = productClient.getComposeSuiteItemStatusBySku(articleNumberList);
            if (!result.isSuccess()) {
                throw new RuntimeException(result.getErrorMsg());
            }
            return result.getResult();
        } catch (Exception e) {
            throw new RuntimeException("根据货号查询组合套装产品的单品状态报错:" + e.getMessage());
        }
    }

    /**
     * 获取插头规格适用国家
     * @param plugSpecification 产品对应的插头规则
     * @return 适用国家
     */
    public static List<String> getCountryOfPlugSpecification(String plugSpecification) {
        if (StringUtils.isBlank(plugSpecification)) {
            throw new BusinessException("获取插头规格适用国家,插头规格不能为空");
        }
        // 取缓存
        Map<String, String> result = loadCountryOfPlugSpecificationCache();
        String countryStr = result.get(plugSpecification);
        if (StringUtils.isBlank(countryStr)) {
            return Collections.emptyList();
        }

        String[] countryArray = countryStr.split(",");
        return Arrays.stream(countryArray).collect(Collectors.toList());
    }

    /**
     * 获取缓存插头规格适用国家
     */
    private static Map<String, String> loadCountryOfPlugSpecificationCache() {
        String resultCache = PRODUCT_API_RESULT_CACHE.getIfPresent(ProductApiCacheEnum.GET_ALL_PLUG_CONFIG.name());
        if (StringUtils.isBlank(resultCache)) {
            ApiResult<Map<String, String>> allPlugConfig = productClient.getAllPlugConfig();
            if (!allPlugConfig.isSuccess()) {
                throw new BusinessException("调用产品系统获取插头规格适用国家异常"+allPlugConfig.getErrorMsg());
            }
            PRODUCT_API_RESULT_CACHE.put(ProductApiCacheEnum.GET_ALL_PLUG_CONFIG.name(), JSON.toJSONString(allPlugConfig));
            return allPlugConfig.getResult();
        }
        ApiResult<Map<String, String>> mapApiResult = JSON.parseObject(resultCache, new TypeReference<>(){});
        return mapApiResult.getResult();
    }


    /**
     * 获取包装费
     * @param sonSkuList
     * @return
     */
    public static List<SkuPackingmaterialPriceResponse> getPackingmaterialPriceBySkus(List<String> sonSkuList){
        if(CollectionUtils.isEmpty(sonSkuList)){
            return null;
        }

        ApiResult<List<SkuPackingmaterialPriceResponse>> result = productClient.getPackingmaterialPriceBySkus(sonSkuList);
        if(!result.isSuccess()){
            throw new RuntimeException(result.getErrorMsg());
        }
        return result.getResult();
    }


    /**
     * 获取子sku销售成本价
     * @param sku 子sku
     * @param skuDataSource 数据来源：单品、组合
     */
    public static Double getSkuSaleCostPrice(String sku, Integer skuDataSource) {
        if (StringUtils.isBlank(sku) || skuDataSource == null) {
            return null;
        }
        if (SkuDataSourceEnum.PRODUCT_SYSTEM.isTrue(skuDataSource)) {
            // 单品
            ProductInfoVO skuInfo = getSkuInfo(sku);
            if (skuInfo.getMainSku() == null) {
                return null;
            }
            return skuInfo.getSaleCost()!=null ? skuInfo.getSaleCost().doubleValue() : null;
        }

        if (SkuDataSourceEnum.COMPOSE_SYSTEM.isTrue(skuDataSource) && isExistSaleSuite(sku)) {
            // 套装
            SuiteSku suiteSku = getSaleSuiteInfoBySonSku(sku);
            if (suiteSku == null) {
                return null;
            }
            return suiteSku.getSaleCost() !=null ? suiteSku.getSaleCost().doubleValue() : null;
        }

        if (SkuDataSourceEnum.COMPOSE_SYSTEM.isTrue(skuDataSource) && !isExistSaleSuite(sku)) {
            // 组合
            ComposeSku composeProduct = getComposeProduct(sku);
            if (composeProduct == null) {
                return null;
            }
            return composeProduct.getSaleCost() !=null ? composeProduct.getSaleCost().doubleValue() : null;
        }
        return null;
    }

    /**
     * 获取组合套装信息
     * @param skuList
     * @return
     */
    public static List<ComposeAndSuiteInfoVO> getComposeAndSuitePublishInfoVo(List<String> skuList){
        if(CollectionUtils.isEmpty(skuList)){

            log.error("");
            return null;
        }

        ApiResult<List<ComposeAndSuiteInfoVO>> result = productClient.getComposeAndSuitePublishInfoVo(skuList);
        if(!result.isSuccess()){
            throw new RuntimeException(result.getErrorMsg());
        }
        return result.getResult();
    }

    /**
     * 获取Spu产品类型
     * 对应产品系统的数据来源
     */
    public static void getSpuSkuDataSource(ProductSourceRequest request) {
        if (SkuDataSourceEnum.ERP_DATA_SYSTEM.isTrue(request.getType())) {
            // 数据分析
            request.setSkuDataSource(SkuDataSourceEnum.ERP_DATA_SYSTEM.getCode());
            request.setIsSuite(false);
            return;
        }

        // 冠通大健云仓
        if (SkuDataSourceEnum.GUAN_TONG_DA_JIAN_CLOUD_WARE_HOUSE.isTrue(request.getType())) {
            // 冠通大健云仓
            request.setSkuDataSource(SkuDataSourceEnum.GUAN_TONG_DA_JIAN_CLOUD_WARE_HOUSE.getCode());
            request.setIsSuite(false);
            return;
        }

        if (!SkuDataSourceEnum.COMPOSE_SYSTEM.isTrue(request.getType())) {
            // 默认产品系统单品
            request.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
            request.setIsSuite(false);
            return;
        }

        Boolean existSaleSuit = ProductUtils.isExistSaleSuite(request.getSpu());
        if (existSaleSuit) {
            // 查询一遍组合套装映射表 存在映射取对应的组合数据
            Map<String, String> composeSkuSuitMap = ProductUtils.getComposeSkuBySuit(Collections.singletonList(request.getSpu()));
            String mappingSpu = MapUtils.getString(composeSkuSuitMap, request.getSpu(), null);
            if (mappingSpu != null) {
                // 组合套装
                request.setSpu(mappingSpu);
                request.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
                request.setIsSuite(false);
                return;
            }
            // 套装
            request.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
            request.setIsSuite(true);
            return;
        }
        // 组合套装
        request.setSkuDataSource(SkuDataSourceEnum.COMPOSE_SYSTEM.getCode());
        request.setIsSuite(false);
    }

    /**
     * 获取套装数据
     * @param beginId   起始ID
     * @param size      大小
     * @param isEnable  状态：是否启用【1启用 0 禁用】
     * @return
     */
    public static List<ProductSuiteResponse> getSuite(String beginId, String size, Integer isEnable) {
        if (StringUtils.isBlank(beginId) || StringUtils.isBlank(size)) {
            return null;
        }

        // 上层调用需要异常处理
        ApiResult<List<ProductSuiteResponse>> result = productClient.getSuite(beginId, size, isEnable);
        if (result == null || !result.isSuccess()) {
            throw new RuntimeException(result.getErrorMsg());
        }
        return result.getResult();
    }

    /**
     * 获取组合数据
     * @param beginId    起始ID
     * @param size       大小
     * @param checkStep  状态 8000:待提交 8001:待翻译，修图 8002:待审核 8003:正常 8004:废弃
     * @return
     */
    public static List<ProductComposeSkuResponse> getComposeSku(String beginId, String size, Integer checkStep) {
        if (StringUtils.isBlank(beginId) || StringUtils.isBlank(size)) {
            return null;
        }

        // 上层调用需要异常处理
        ApiResult<List<ProductComposeSkuResponse>> result = productClient.getComposeSku(beginId, size, checkStep);
        if (result == null || !result.isSuccess()) {
            throw new RuntimeException(result.getErrorMsg());
        }
        return result.getResult();
    }

    /**
     * 获取查询条件的spu集合
     * @param request
     * @return
     */
    public static List<QuerySpuByConditionVo> querySpuByCondition(QuerySpuByCondition request){
        if(request == null){
            return null;
        }
        ApiResult<List<QuerySpuByConditionVo>> result = productClient.querySpuByCondition(request);
        if(!result.isSuccess()){
            throw new RuntimeException("获取查询条件的spu集合:" + result.getErrorMsg());
        }
        return result.getResult();
    }

    /**
     * 获取spu结合的出单数
     * @param request
     * @return
     */
    public static List<SpuOrderCount> querySpuOrderCount(QuerySpuOrderCount request){
        if (request == null) {
            return null;
        }
        ApiResult<List<SpuOrderCount>> listApiResult = productClient.querySpuOrderCount(request);
        if (!listApiResult.isSuccess()) {
            throw new RuntimeException("获取spu集合的出单数异常:"+listApiResult.getErrorMsg());
        }
        return listApiResult.getResult();
    }

    public static SpuOfficial getAmazonOfficial(String spu) {
        try {
            ApiResult<?> amazonOfficial = productClient.getAmazonOfficial(spu, null);
            if (amazonOfficial.isSuccess()) {
                Object result = amazonOfficial.getResult();
                if (null != result) {
                    SpuOfficial spuOfficial = JSON.parseObject(JSON.toJSONString(result), SpuOfficial.class);
                    spuOfficial.setNeedWenAnType(WenAnTypeEnum.amazonWenAn.getCode());
                    return spuOfficial;
                }
            }
            return null;
        } catch (Exception e) {
            throw  new RuntimeException(e);
        }
    }

    public static List<SpuOfficial> getAmazonOfficialList(List<String> spu) {
        if (CollectionUtils.isEmpty(spu)) {
            return null;
        }
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("spu", spu);
            params.put("language", null);
            ApiResult<List<?>> amazonOfficial = productClient.getAmazonOfficialList(params);
            if (amazonOfficial.isSuccess()) {
                List<?> result = amazonOfficial.getResult();
                if (null != result) {
                    return JSON.parseArray(JSON.toJSONString(result), SpuOfficial.class);
                }
            }
            return null;
        } catch (Exception e) {
            throw  new RuntimeException(e);
        }
    }

    public static List<String> getNoTemplateSpu(QueryNoTemplateSpuRequest request) {
        try {
            ApiResult<List<String>> result = productClient.queryNoTemplateSpu(request);
            if (!result.isSuccess()) {
                throw new RuntimeException(result.getErrorMsg());
            }
            return result.getResult();
        } catch (Exception e) {
            throw new RuntimeException("查询无范本spu自定义传入平台站点:" + e.getMessage());
        }
    }

    /**
     * 查询亚马逊首位侵权词
     * @return
     */
    public static List<String> queryAmzFirstInfringementWords() {
        try {
            String resultCache = PRODUCT_API_RESULT_CACHE.getIfPresent(ProductApiCacheEnum.GET_AMAZON_FIRST_INFRACTION_WORD.name());
            if (StringUtils.isBlank(resultCache)) {
                ApiResult<List<String>> apiResult = productClient.queryAmzFirstInfringementWords();
                if (!apiResult.isSuccess()) {
                    throw new BusinessException("调用产品系统查询亚马逊首位侵权词失败:"+apiResult.getErrorMsg());
                }
                List<String> words = apiResult.getResult();
                PRODUCT_API_RESULT_CACHE.put(ProductApiCacheEnum.GET_AMAZON_FIRST_INFRACTION_WORD.name(), JSON.toJSONString(apiResult));
                return words;
            }
            ApiResult<List<String>> apiResult = JSON.parseObject(resultCache, new TypeReference<>(){});
            return apiResult.getResult();
        } catch (Exception e) {
            throw new RuntimeException("调用产品系统查询亚马逊首位侵权词失败:" + e.getMessage());
        }
    }

    /**
     * 根据sku获取对应的GPSR供应商
     * @param skuList
     * @return
     */
    public static Map<String, String> getGpsrManufacturerBySku(List<String> skuList){
        if(CollectionUtils.isEmpty(skuList)){
            return new HashMap<>();
        }
        ApiResult<?> gpsrManufacturer = productClient.getGpsrManufacturerBySku(skuList);

        if(!gpsrManufacturer.isSuccess()){
            throw new RuntimeException("根据sku获取对应的GPSR供应商异常:" + gpsrManufacturer.getErrorMsg());
        }
        Object result = gpsrManufacturer.getResult();
        if (null != result) {
            Map<String, String> map = JSON.parseObject(JSON.toJSONString(result), Map.class);
            return map;
        }
        return new HashMap<>();
    }

    /**
     * http://172.16.10.40/web/#/27/10716
     * 根据条件获取spu中最重sku
     * @param request
     * @return
     */
    public static List<QuerySpuByConditionVo> queryHeaviestSkuWithCondition(QuerySpuByCondition request){
        if(request == null){
            return null;
        }
        long now = System.currentTimeMillis();
        ApiResult<List<QuerySpuByConditionVo>> result = productClient.queryHeaviestSkuWithCondition(request);
        long time = System.currentTimeMillis() - now;
        if (time >1000) {
            log.info("调用/api/publish/query_heaviest_sku_with_condition,耗时->{}ms", time);
        }
        if(!result.isSuccess()){
            throw new RuntimeException("queryHeaviestSkuWithCondition 请求参数:"+ JSONObject.toJSONString(result) +" 根据条件获取spu中最重sku:" + result.getErrorMsg());
        }
        return result.getResult();
    }


    /**
     * 获取产品系统获取警示语
     *
     * @return k: 类目ID路径， v：警示语
     */
    public static Map<String, String> getGPSRWarnings() {
        try {
            String resultCache = PRODUCT_API_RESULT_CACHE.getIfPresent(ProductApiCacheEnum.GET_GPSR_WARNINGS.name());
            if (StringUtils.isBlank(resultCache)) {
                ApiResult<Map<String, String>> apiResult = productClient.getGPSRWarnings();
                if (!apiResult.isSuccess()) {
                    throw new BusinessException("调用产品系统获取警示语异常:" + apiResult.getErrorMsg());
                }
                PRODUCT_API_RESULT_CACHE.put(ProductApiCacheEnum.GET_GPSR_WARNINGS.name(), JSON.toJSONString(apiResult));
                return apiResult.getResult();
            }
            ApiResult<Map<String, String>> mapApiResult = JSON.parseObject(resultCache, new TypeReference<>() {
            });
            return mapApiResult.getResult();

        } catch (Exception e) {
            log.error("调用产品系统获取警示语异常：{}", e.getMessage());
            throw new BusinessException("调用产品系统获取警示语异常:" + e.getMessage());
        }
    }


    /**
     * 检查标题是否相似
     * 若存在一个标题相似度大于等于titleSimilarity，则认为相似，若相似度比对所有标题均小于titleSimilarity,认为不相似
     *
     * @param targetTitle
     * @param sku
     * @param titleSimilarity
     * @return
     */
    public static Pair<Boolean, String> checkIsSimilarity(String targetTitle, String sku, float titleSimilarity) {
        if (StringUtils.isBlank(targetTitle)) {
            //list标题为空,依照产品要求当作不相似处理
            return Pair.of(false, "listing标题为空");
        }
        ResponseJson resp = ProductUtils.findSkuInfo(sku);
        ProductInfo productInfo = null;
        if (resp.isSuccess()) {
            productInfo = (ProductInfo) resp.getBody().get(ProductUtils.resultKey);
        }
        if (null == productInfo) {
            //查询不到产品信息,依照产品要求当作相似处理,不进行推送
            return Pair.of(true, "查询不到产品信息");
        }

        List<String> productTitleList = processProductTitles(productInfo);

        if (CollectionUtils.isEmpty(productTitleList)) {
            //获取到的产品系统标题为空,依照产品要求,不进行推送
            return Pair.of(true, "获取到的产品系统标题为空");
        }


        float maxSimilarity = 0;
        for (String productTitle : productTitleList) {
            float similarity = SimilarityRatioUtils.getSimilarityRatio(productTitle, targetTitle);
            if (similarity > maxSimilarity) {
                maxSimilarity = similarity;
            }
            if (similarity >= titleSimilarity) {
                return Pair.of(true, null);
            }
        }
        //保留两位小数,向下取整
        String maxSimilarityStr = CommonUtils.formatToPercentage(maxSimilarity, 2, RoundingMode.DOWN);
        return Pair.of(false, maxSimilarityStr);
    }


    public static List<String> processProductTitles(ProductInfo productInfo) {
        List<String> productTitleList = new ArrayList<>();

        // 处理长标题
        List<String> longTitleList = handleTitle(productInfo.getLongTitle());
        if (CollectionUtils.isNotEmpty(longTitleList)) {
            productTitleList.addAll(longTitleList);
        }

        // 处理短标题
        List<String> shortTitleList = handleTitle(productInfo.getShortTitle());
        if (CollectionUtils.isNotEmpty(shortTitleList)) {
            productTitleList.addAll(shortTitleList);
        }

        // 长短标题集合只取前15个
        productTitleList = trimList(productTitleList, 15);

        // 只有当长·短标题都不存在时才处理产品标题，且只取前三个
        if (StringUtils.isBlank(productInfo.getLongTitle()) && StringUtils.isBlank(productInfo.getShortTitle()) && StringUtils.isNotBlank(productInfo.getTitleAtt())) {
            List<String> titleList = handleTitle(productInfo.getTitleAtt());
            if (CollectionUtils.isNotEmpty(titleList)) {
                productTitleList.addAll(titleList);
            }
            productTitleList = trimList(productTitleList, 3);
        }

        return productTitleList;
    }

    private static List<String> handleTitle(String titleJson) {
        if (StringUtils.isNotBlank(titleJson)) {
            try {
                List<String> titleList = JSON.parseObject(titleJson, new TypeReference<List<String>>() {
                });
                return titleList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            } catch (Exception e) {
                return Lists.newArrayList();
            }
        }
        return Lists.newArrayList();
    }

    private static List<String> trimList(List<String> list, int maxSize) {
        return list.size() > maxSize ? list.subList(0, maxSize) : list;
    }

    /**
     * 根据sku查询冠通大健云仓库存
     *
     * @return k: sku， v：库存
     */
    public static Map<String, Integer> getGigaStocks(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Collections.emptyMap();
        }
        Map<String, Integer> map = new HashMap<>();
        // 限制每次查询数量不超过500个
        List<List<String>> partition = Lists.partition(skuList, 500);

        for (List<String> skuPart : partition) {
            ApiResult<Map<String, Integer>> result = productClient.getGigaStocks(skuPart);
            if (!result.isSuccess()) {
                throw new RuntimeException(String.format("调用产品系统根据sku查询冠通大健云仓库存异常,param:%s,error:%s", skuList, result.getErrorMsg()));
            }
            Map<String, Integer> resultMap = result.getResult();
            if (null != resultMap) {
                map.putAll(resultMap);
            }
        }

        return map;
    }


    /**
     * 推送新品美工修图确认
     *
     * @param spuImageRequirement
     * @return
     */
    public static ApiResult<String> pushRollbackFixPicture(SpuImageRequirement spuImageRequirement) {
        if (null == spuImageRequirement) {
            return ApiResult.newError("参数不能为空");
        }
        try {
            return productClient.addNewProductPsConfirm(spuImageRequirement);
        } catch (Exception e) {
            log.error("调用产品系统推送新品美工修图确认异常：{}", e.getMessage());
            return ApiResult.newError("调用产品推送新品美工修图确认异常：" + e.getMessage());
        }
    }

    public static ApiResult<Map<String, List<InfringementSaleProhibitionVO>>> getAllSkuInfringementInfoBySpu(String spu) {
        if (StringUtils.isBlank(spu)) {
            return ApiResult.newError("参数不能为空");
        }
        try {
           return productClient.getAllSkuInfringementInfoBySpu(List.of(spu));
        } catch (Exception e) {
            log.error("调用产品系统通过spu获取到spu下所有的子sku以及子ku的禁售信息异常：" + e.getMessage(), e);
            return ApiResult.newError("调用产品系统通过spu获取到spu下所有的子sku以及子ku的禁售信息异常：" + e.getMessage());
        }
    }

    public static ApiResult<Map<String, List<String>>> getComposeSkuImageBySpu(@RequestBody List<String> spuList) {
        if (CollectionUtils.isEmpty(spuList)) {
            return ApiResult.newError("参数不能为空");
        }
        try {
            return productClient.getComposeSkuImageBySpu(spuList);
        } catch (Exception e) {
            return ApiResult.newError("根据spu获取销售开发列表图片异常：" + e.getMessage());
        }
    }


    public static ApiResult<Map<String, List<String>>> getComposeSkuQuerySonSku(@RequestBody List<String> spuList) {
        if (CollectionUtils.isEmpty(spuList)) {
            return ApiResult.newError("参数不能为空");
        }
        try {
            return productClient.getComposeSkuQuerySonSku(spuList);
        } catch (Exception e) {
            return ApiResult.newError("根据spu获取试卖子Sku：" + e.getMessage());
        }
    }


    /**
     * 产品系统-字符相似度
     *
     * @param str
     * @param target
     * @return
     */
    public static float checkSimilar(String str, String target) {
        if (StringUtils.isBlank(str) || StringUtils.isBlank(target)) {
            return 0;
        }

        int d[][];

        int n = str.length();

        int m = target.length();

        int i;

        int j;

        char ch1;

        char ch2;

        int temp;

        if (n == 0 || m == 0) {

            return 0;

        }

        d = new int[n + 1][m + 1];

        for (i = 0; i <= n; i++) {

            d[i][0] = i;

        }

        for (j = 0; j <= m; j++) {

            d[0][j] = j;

        }

        for (i = 1; i <= n; i++) {

            ch1 = str.charAt(i - 1);

            for (j = 1; j <= m; j++) {

                ch2 = target.charAt(j - 1);

                if (ch1 == ch2 || ch1 == ch2 + 32 || ch1 + 32 == ch2) {

                    temp = 0;

                } else {

                    temp = 1;

                }

                d[i][j] = Math.min(Math.min(d[i - 1][j] + 1, d[i][j - 1] + 1), d[i - 1][j - 1] + temp);

            }

        }

        return (1 - (float) d[n][m] / Math.max(str.length(), target.length())) * 100F;

    }


    /**
     * 文案重复校验规则
     * 规则1：一个标题中同一个词最多出现2次，超过2次不允许保存，并提示"XXX词在同一标题中出现超过2次，不允许保存"
     * 规则2：一个标题中重复出现的词去重后不允许超过2个，超过2个系统不允许保存，并提示"重复词超过2个，不允许保存"
     *
     * @param titleList 标题列表
     * @throws RuntimeException 校验失败时抛出异常
     */
    public static void validateTitleDuplication(List<String> titleList) {
        if (CollectionUtils.isEmpty(titleList)) {
            return;
        }

        for (int i = 0; i < titleList.size(); i++) {
            String title = titleList.get(i);
            if (org.apache.commons.lang.StringUtils.isBlank(title)) {
                continue;
            }

            // 对每个标题进行校验，传递标题索引（从6开始）
            validateSingleTitle(title.trim(), i + 6);
        }
    }

    /**
     * 校验单个标题
     *
     * @param title 标题
     * @param titleIndex 标题索引（从6开始，对应标题6、标题7、标题8等）
     * @throws RuntimeException 校验失败时抛出异常
     */
    private static void validateSingleTitle(String title, int titleIndex) {
        // 分词处理
        List<String> words = extractWords(title);

        if (CollectionUtils.isEmpty(words)) {
            return;
        }

        // 统计每个词的出现次数
        Map<String, Integer> wordCountMap = new HashMap<>();
        for (String word : words) {
            wordCountMap.put(word, wordCountMap.getOrDefault(word, 0) + 1);
        }

        // 规则1：检查是否有词出现超过2次
        for (Map.Entry<String, Integer> entry : wordCountMap.entrySet()) {
            if (entry.getValue() > 2) {
                throw new RuntimeException("标题" + titleIndex + "，重复词" + entry.getKey() + "，超过2次，不允许保存");
            }
        }

        // 规则2：检查重复词数量是否超过2个
        List<String> duplicateWords = wordCountMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (duplicateWords.size() > 2) {
            String duplicateWordsStr = String.join(",", duplicateWords);
            throw new RuntimeException("标题" + titleIndex + "，重复词" + duplicateWordsStr + "超过2个，不允许保存");
        }
    }

    /**
     * 从标题中提取词汇
     * 以空格作为分割标准，提取所有非空的词汇
     *
     * @param title 标题
     * @return 词汇列表
     */
    private static List<String> extractWords(String title) {
        List<String> words = new ArrayList<>();

        if (org.apache.commons.lang.StringUtils.isBlank(title)) {
            return words;
        }

        // 以空格分割标题，获取所有词汇
        String[] segments = title.trim().split("\\s+");

        for (String segment : segments) {
            if (org.apache.commons.lang.StringUtils.isNotBlank(segment)) {
                // 直接将非空的词汇加入列表
                words.add(segment.trim());
            }
        }

        return words;
    }

    /**
     * 根据词汇列表查询禁售信息
     *
     * @param wordList 词汇列表
     * @return 词汇禁售信息映射，key为大写词汇，value为禁售信息
     */
    public static Map<String, WordProhibitionVO> queryProhibitionByWords(List<String> wordList) {
        if (CollectionUtils.isEmpty(wordList)) {
            return Collections.emptyMap();
        }

        Map<String, WordProhibitionVO> resultMap = new HashMap<>();
        // 限制每次查询数量不超过1000个
        List<List<String>> partitions = Lists.partition(wordList, 1000);

        for (List<String> wordPart : partitions) {
            Map<String, WordProhibitionVO> partResult = queryProhibitionByWordsPart(wordPart, wordList);
            resultMap.putAll(partResult);
        }

        return resultMap;
    }

    /**
     * 查询单个分片的禁售信息
     *
     * @param wordPart         词汇分片
     * @param originalWordList 原始词汇列表（用于异常信息）
     * @return 禁售信息映射
     */
    private static Map<String, WordProhibitionVO> queryProhibitionByWordsPart(List<String> wordPart, List<String> originalWordList) {
        ApiResult<List<QueryProhibitionByWordsVO>> result = productClient.queryProhibitionByWords(wordPart);
        if (!result.isSuccess()) {
            throw new RuntimeException(String.format("查询对应词的禁售信息启用的侵权词和商标词异常,param:%s,error:%s",
                    originalWordList, result.getErrorMsg()));
        }

        return Optional.ofNullable(result.getResult())
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(
                        vo -> vo.getWord().toUpperCase(),
                        ProductUtils::convertToWordProhibitionVO,
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 转换查询结果为WordProhibitionVO对象
     *
     * @param queryResult 查询结果
     * @return WordProhibitionVO对象
     */
    private static WordProhibitionVO convertToWordProhibitionVO(QueryProhibitionByWordsVO queryResult) {
        WordProhibitionVO prohibitionVO = new WordProhibitionVO();

        if (CollectionUtils.isNotEmpty(queryResult.getTrademarkIdentification())) {
            prohibitionVO.setTrademarkIdentification(StringUtils.join(queryResult.getTrademarkIdentification(), ","));
        }

        // 处理平台站点信息
        if (MapUtils.isNotEmpty(queryResult.getPlatSite())) {
            List<String> prohibitionSites = new ArrayList<>();
            List<String> forbidChannels = new ArrayList<>();

            queryResult.getPlatSite().forEach((platform, sites) -> {
                forbidChannels.add(platform);
                if (CollectionUtils.isNotEmpty(sites)) {
                    sites.forEach(site -> prohibitionSites.add(platform + "_" + site));
                }
            });

            if (CollectionUtils.isNotEmpty(prohibitionSites)) {
                prohibitionVO.setProhibitionSite(StringUtils.join(prohibitionSites, ","));
            }
            if (CollectionUtils.isNotEmpty(forbidChannels)) {
                prohibitionVO.setForbidChannel(StringUtils.join(forbidChannels, ","));
            }
        }

        return prohibitionVO;
    }

    /**
     * 计算重量（自身净重+包材重量+搭配包材重量+面单重量3g），单位是g
     *
     * @param skuInfo
     * @return
     */
    public static Double calcSkuWeight(ProductInfo skuInfo) {
        //自身净重
        double skuWeight = skuInfo.getProductWeight() == null ? 0 : skuInfo.getProductWeight();
        //包材重量
        double packingMaterialWeight = skuInfo.getPackingWeight() == null ? 0 : skuInfo.getPackingWeight().doubleValue();
        //搭配包材重量
        double collocationPackingMaterialWeight = skuInfo.getMatchWeight() == null ? 0 : skuInfo.getMatchWeight().doubleValue();
        return NumberUtils.format(skuWeight + packingMaterialWeight + collocationPackingMaterialWeight + 3);
    }

    /**
     * 计算重量（自身净重+包材重量+搭配包材重量+面单重量3g），单位是g
     *
     * @param skuInfo
     * @return
     */
    public static Double calcSkuWeight(SonSkuFewInfo skuInfo) {
        //自身净重
        double skuWeight = skuInfo.getProductWeight() == null ? 0 : skuInfo.getProductWeight();
        //包材重量
        double packingMaterialWeight = skuInfo.getPackingWeight() == null ? 0 : skuInfo.getPackingWeight().doubleValue();
        //搭配包材重量
        double collocationPackingMaterialWeight = skuInfo.getMatchWeight() == null ? 0 : skuInfo.getMatchWeight().doubleValue();
        return NumberUtils.format(skuWeight + packingMaterialWeight + collocationPackingMaterialWeight + 3);
    }


    /**
     * 推送新品文案审核内容到产品更新
     *
     * @param message
     * @return
     */
    public static ApiResult<String> updateCopywritingReviewToProduct(CopywritingMessage message) {

        try {
            ApiResult<String> apiResult = productClient.checkAmazonOfficial(message);
            if (apiResult.isSuccess()) {
                return apiResult;
            }
            return ApiResult.newError("调用产品系统更新失败," + apiResult.getErrorMsg());
        } catch (Exception e) {
            return ApiResult.newError("调用产品系统更新失败," + e.getMessage());
        }
    }
}
