package com.estone.erp.publish.system.product;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.system.product.bean.*;
import com.estone.erp.publish.system.product.bean.forbidden.InfringementSaleProhibitionVO;
import com.estone.erp.publish.system.product.bean.forbidden.ProhibitionTypeConfig;
import com.estone.erp.publish.system.product.bean.forbidden.RiskLevel;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.request.*;
import com.estone.erp.publish.system.product.response.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 功能描述: 产品系统接口
 *
 * @Author: YCF
 * @Date: 2019/11/28
 * @Version: 0.0.1
 **/
@FeignClient(name = "product-service")
//@FeignClient(url = "192.168.8.159:8080", name = "product-service")
//@FeignClient(url = "192.168.8.167:8080", name = "product-service")
//@FeignClient(url = "10.100.1.1:31091", name = "product-service")
public interface ProductClient {

    /**
     * 功能描述: 获取一个平台的侵权词
     **/
    @RequestMapping(value = "/api/publish/getInfringementWord?name={saleChannel}", method = RequestMethod.GET, headers = "content-type=application/json")
    ApiResult<?> getInfringementWord(@PathVariable("saleChannel") String saleChannel);

    /**
     * 功能描述: 获取sku的侵权产品平台
     *  TODO 产品系统已经改版，刊登仍在调用的平台需要自行切换至单品es拦截
     **/
    @RequestMapping(value = "/api/publish/getInfringementProBySku", method = RequestMethod.POST, headers = "content-type=application/json")
    ApiResult<?> getInfringementProBySku(@RequestBody List<String> skuList);

    /**
     * 功能描述: 根据子sku查出所有的主sku
     * 此接口只针对一次性任务，每次子sku集合不得超过500
     **/
    @RequestMapping(value = "/api/publish/getMainSkuBySonSku", method = RequestMethod.POST, headers = "content-type=application/json")
    ApiResult<?> getMainSkuBySonSku(@RequestBody List<String> skuList);

    /**
     * 根据子sku或主sku获取主sku集合
     */
    @PostMapping(value = "/api/publish/getMainSkuBySonSkuOrMainSku", headers = "content-type=application/json")
    ApiResult<List<String>> getMainSkuBySonSkuOrMainSku(@RequestBody List<String> skuList);

    /**
     * 功能描述: 根据sku查出sku的长宽高
     **/
    @RequestMapping(value = "/api/publish/getSize?sku={sku}", method = RequestMethod.GET, headers = "content-type=application/json")
    ApiResult<?> getSkuSizeBysku(@PathVariable("sku") String sku);

    /**
     * 功能描述: 查询所有侵权词，禁用状态， 忽略大小写状态
     **/
    @RequestMapping(value = "/api/publish/getAllInfringement", method = RequestMethod.GET, headers = "content-type=application/json")
    String getAllInfringement();

    /**
     * http://172.16.10.40/web/#/27?page_id=6392
     * 功能描述: 根据侵权类型查询侵权词
     **/
    @RequestMapping(value = "/api/publish/getInfringementByType", method = RequestMethod.GET, headers = "content-type=application/json")
    String getInfringementByType(@RequestParam("type") String type);

    /**
     * 功能描述: 根据sku集合查询单品状态为'Stop', 'Archived'的sku
     **/
    @RequestMapping(value = "/api/publish/findStopAndArchived", method = RequestMethod.POST, headers = "content-type=application/json")
    String findStopAndArchived(@RequestBody List<String> skuList);

    /**
     * 功能描述: 根据sku查询对应信息
     * skuList 产品系统限制最大1000条
     * http://172.16.10.40/web/#/27?page_id=2243
     **/
    @RequestMapping(value = "/api/publish/findSkuInfos", method = RequestMethod.POST, headers = "content-type=application/json")
    String findSkuInfos(@RequestBody List<String> skuList);

    /**
     * 功能描述: 根据sku查询首图，主，子sku
     * http://172.16.10.40/web/#/27?page_id=3393
     * @param skuList
     * @return
     */
    @RequestMapping(value = "/api/publish/findSkuImage", method = RequestMethod.POST, headers = "content-type=application/json")
    String findSkuImage(@RequestBody List<String> skuList);

    /**
     * 功能描述: 根据sku查询对应信息（套装）
     * http://172.16.10.40/web/#/27?page_id=2742
     **/
    @RequestMapping(value = "/api/publish/getSuiteWeightAndCost", method = RequestMethod.POST, headers = "content-type=application/json")
    String getSuiteWeightAndCost(@RequestBody List<String> skuList);

    /**
     * 功能描述: 检测sku是否存在
     **/
    @RequestMapping(value = "/api/publish/checkIsExist?sku={sku}", method = RequestMethod.GET, headers = "content-type=application/json")
    String checkIsExist(@PathVariable("sku") String sku);

    /**
     *  分页获取sku信息
     *  http://172.16.10.40/web/#/27?page_id=2322
     * @return
     */
    @GetMapping(value = "/api/publish/getBatchInfoPublish")
    String getBatchInfoPublish(@RequestParam("begin") Integer begin, @RequestParam("end") Integer end);

    /**
     * 获取标签
     */
    @RequestMapping(value = "/api/order/getAllTagForOrder", method = RequestMethod.GET, headers = "content-type=application/json")
    String getSkuTag();

    /**
     * 获取某段时间内转正常的sku
     * 参数格式是2020/03/20 00:00:00
     */
    @RequestMapping(value = "/api/publish/getNormalSku", method = RequestMethod.GET, headers = "content-type=application/json")
    String getNormalSku(@RequestParam("begin") String begin, @RequestParam("end") String end);


    /**
     * 分页获取清仓甩卖 sku信息
     * http://172.16.10.40/web/#/27?page_id=2343
     * @param pageIndex
     * @param pageSize
     * @param changeTime
     * @return
     */
    @PostMapping("/api/publish/getClearanceSaleList")
    String getClearanceSaleList(@RequestParam("pageIndex") Integer pageIndex,
                                @RequestParam("pageSize") Integer pageSize,
                                @RequestParam(value = "changeTime", required = false) String changeTime
    );

    /**
     * 查询改前状态为清仓、甩卖的sku信息
     * http://172.16.10.40/web/#/27?page_id=3394
     * @param pageIndex
     * @param pageSize
     * @param changeTime
     * @return
     */
    @PostMapping("/api/publish/getBeforeClearanceSaleList")
    String getBeforeClearanceSaleList(@RequestParam("pageIndex") Integer pageIndex,
                                @RequestParam("pageSize") Integer pageSize,
                                @RequestParam(value = "changeTime", required = false) String changeTime
    );

    /**
     * 获取暂停，休假变正常状态的sku
     */
    @GetMapping("/api/publish/getSkusByStateChange")
    ApiResult<List<String>> getSkusByStateChange();

    /**
     * 根据主SPU获取 SPU标题 (优先取redis PRODUCT_COMMON_SPU: + SPU大写，查不到再调用接口)
     * http://172.16.10.40/web/#/27?page_id=4462
     * @param spus
     * @return
     */
    @RequestMapping(value = "/api/publish/getSpuTitles", method = RequestMethod.POST, headers = "content-type=application/json")
    String getSpuTitles(@RequestBody List<String> spus);
    @RequestMapping(value = "/api/publish/getSpuTitles", method = RequestMethod.POST, headers = "content-type=application/json")
    String getSpuTitlesByLanguage(@RequestParam("language") String language, @RequestBody List<String> spus);

    /**
     * 功能描述: 根据主sku集合获取spu相关信息
     * skuList 产品系统限制最大1000条
     * http://172.16.10.40/web/#/27?page_id=2816
     **/
    @RequestMapping(value = "/api/publish/getSpuInfoByMainSkus", method = RequestMethod.POST, headers = "content-type=application/json")
    String findSpuInfos(@RequestBody List<String> skuList);

    /**
     * 根据子sku查询刊登对应字段信息
     * http://172.16.10.40/web/#/27?page_id=3094
     */
    @RequestMapping(value = "/api/publish/queryForPublishBySku", method = RequestMethod.POST, headers = "content-type=application/json")
    String findSonSkuInfos(@RequestBody List<String> skuList);

    /**
     * 根据时间获取侵权信息
     * http://172.16.10.40/web/#/27?page_id=2678
     */
    @PostMapping("/api/publish/getInfInfoByTime")
    ApiResult<?> getInfInfoByTime(@RequestBody Map<String,String> body);

    /**
     * 根据时间获取侵权信息版本2
     * http://172.16.10.40/web/#/27?page_id=3344
     */
    @PostMapping("/api/publish/getInfInfoByTimeV2")
    ApiResult<?> getInfInfoByTimeV2(@RequestBody Map<String,String> body);

    /**
     * 根据时间查询禁售信息
     * http://172.16.10.40/web/#/27?page_id=3560
     * @param body
     * @return
     */
    @PostMapping("/api/publish/getForbiddenByDate")
    ApiResult<Map<String, String>> getForbiddenByDate(@RequestBody Map<String,String> body);

    /**
     * 获取分类code和path
     * http://172.16.10.40/web/#/27?page_id=2939
     * @return
     */
    @GetMapping("/api/publish/getAllCategoryCode")
    ApiResult<List<ProductCategoryInfo>> getAllCategoryCode();

    /**
     * 获取所有sku的数量
     * http://172.16.10.40/web/#/27?page_id=3173
     * @return
     */
    @RequestMapping(value = "/api/publish/getSkuCount", method = RequestMethod.GET, headers = "content-type=application/json")
    ApiResult<Integer> getSkuCount();

    /**
     * 获取子sku对应主sku
     * http://172.16.10.40/web/#/27?page_id=3174
     * @return
     */
    @RequestMapping(value = "/api/publish/getSkuByOffSite?offSite={offSite}&size={size}", method = RequestMethod.GET, headers = "content-type=application/json")
    ApiResult<Map<String,String>> getSkuByOffSite(@PathVariable("offSite") Integer offSite, @PathVariable("size") Integer size);

    /**
     * 获取sku组成标题元素
     * http://172.16.10.40/web/#/27?page_id=3266
     * @return
     */
    @GetMapping("/api/publish/getSkuComposeEle")
    ApiResult<?> getSkuComposeEle();

    /**
     * 刊登模板成功后调用
     * http://172.16.10.40/web/#/27?page_id=3480
     * @return
     */
    @PostMapping("/api/publish/publishSuccess")
    ApiResult<?> publishSuccess(@RequestBody Object body);


    /**
     * 根据产品分类查询 spu销量数据
     * http://172.16.10.40/web/#/27?page_id=3830
     * @return
     */
    @PostMapping("/api/publish/getSaleSpu")
    ApiResult<List<SaleSpuResponse>> getSaleSpu(@RequestBody SaleSpuRequest request);

    /**
     * 刊登根据 站点查询侵权词汇
     * http://172.16.10.40/web/#/27?page_id=3841
     * @return
     */
    @GetMapping("/api/publish/getInfringementWordsBySite")
    ApiResult<Map<String, List<String>>> getInfringementWordsBySite(@RequestParam String site);

    /**
     * 新增字段需要产品系统也一起更改
     * listing获取时间段内变更的sku，只返回如下 指定字段变更的sku
     *  单品状态 、禁售平台、平台禁售站点、禁售原因、禁售类型、产品标签、特殊标签、产品类目、促销状态、新品状态
     * @return
     */
    @PostMapping("/api/publish/getChangeSku")
    ApiResult<?> getChangeSku(@RequestBody Map<String,String> map);

    /**
     * 根据时间查看此时间段内停产与存档的sku
     * http://172.16.10.40/web/#/27?page_id=4755
     * @return
     */
    @PostMapping("/api/publish/getChangeStopArchivedSku")
    ApiResult<List<String>> getChangeStopArchivedSku(@RequestBody Map<String, Object> body);

    /**
     * 查看试卖SKU 是否进入单品
     * @param skus
     * @return
     */
    @PostMapping("/api/publish/getNormalBySku")
    ApiResult<List<String>> getNormalBySku(List<String> skus);


    /**
     * 根据时间查看此时间段内已编辑的spu
     * http://172.16.10.40/web/#/27/4858
     * @param request
     * @return
     */
    @PostMapping("/api/publish/getNewProduct")
    ApiResult<Map<String, SkuListAndCode>> getNewProduct(@RequestBody ProductNewSpuRequest request);

    /**
     * 根据时间查看此时间段内已编辑的spu（不限制数量）
     * http://172.16.10.40/web/#/27/9482
     * @param request
     * @return
     */
    @PostMapping("/api/publish/getNewProductBySingItem")
    ApiResult<Map<String, SkuListAndCode>> getNewProductBySingItem(@RequestBody ProductNewSpuRequest request);

    @GetMapping("/api/publish/getGtSkuByStatus")
    ApiResult<List<String>> getGtSkuByStatus(@RequestParam Integer publish);

    /**
     * 新品推荐接口
     * http://172.16.10.40/web/#/27?page_id=5095
     * @return
     */
    @PostMapping("/api/publish/getNewSpuInfo")
    String getNewSpuInfo(@RequestBody ProductNewSpuRequest request);

    @GetMapping("api/publish/getAllCategorys")
    ApiResult<List<ProductCategoryInfo>> getProductSystemAllCategory();

    /**
     * 根据单品上次编辑状态，时间获取子sku信息
     * http://172.16.10.40/web/#/27?page_id=6082
     * @return
     */
    @PostMapping("/api/publish/getSonSkuByLastStatusInfo")
    ApiResult<List<String>> getSonSkuByLastStatusInfo(@RequestBody SonSkuStatusChangeRequest request);




    /**
     * http://172.16.10.40/web/#/27?page_id=6473
     * 根据分类节点获取该节点的所有叶子节点，包含禁用的类目
     */
    @RequestMapping(value = "/api/publish/getLeafNodeByCode", method = RequestMethod.GET, headers = "content-type=application/json")
    ApiResult<List<String>> getLeafNodeByCode(@RequestParam("code") String code);

    /**
     * sku变更信息推送刊登看板
     * http://172.16.10.40/web/#/27?page_id=5107
     * @param request
     * @return
     */
    @PostMapping("/api/publish/getSkuChangeInfoEs")
    ApiResult<PageResult<SkuChangeInfoDO>> getSkuChangeInfoEs(@RequestBody SkuChangeRequest request);

    /**
     * 获取所有定制产品
     * @return
     */
    @GetMapping("/api/publish/getSpecialSku_2001")
    ApiResult<List<String>> getAllCustomMade();

    /**
     * 根据sku状态和货源地分页获取该货源地的货号
     * http://172.16.10.40/web/#/27?page_id=6697
     * @param map
     * @return
     */
    @PostMapping("/api/publish/getSonSkuBySupplyAndItemStatus")
    ApiResult<List<String>> getSonSkuBySupplyAndItemStatus(@RequestBody Map<String, Object> map);

    /**
     * 提供根据spu批量查询侵权词
     * @param param   {
     *   "platform": "Amazon",
     *   "spu": "10A00114"
     * }
     */
    @PostMapping("/api/publish/getInfringementWordBySpu")
    ApiResult<SpuInfringementWordDO> getInfringementWordBySpu(@RequestBody Map<String, String> param);



    /**
     * 提供所有套装支持分页(只返回套装货号集合)
     * http://172.16.10.40/web/#/27/6796
     * @param request
     * @return
     */
    @PostMapping("/api/publish/getSaleSuiteSku")
    ApiResult<List<String>> getSaleSuiteSku(@RequestBody SaleSuiteRequest request);

    /**
     * 取产品系统的产品标签
     */
    @GetMapping("skutag/all")
    ApiResult<List<TagCode>> getProductSkuTag();


    /**
     * http://172.16.10.40/web/#/27/6136
     * 取产品系统的特殊标签
     */
    @GetMapping("specialTag/getSpecialTagName")
    ApiResult<List<SpecialTag>> getSpecialTag();

    /**
     * 查询废弃sku对应的合并sku
     * http://172.16.10.40/web/#/27/7089
     * @param abandonArticleNumber
     * @return
     */
    @GetMapping("api/publish/getMergeSku")
    ApiResult<String> getMergeSku(@RequestParam("abandonArticleNumber") String abandonArticleNumber);

    /**
     * 根据组合SPU查询组合SKU信息
     * <a href="http://172.16.10.40/web/#/27/7207">接口地址</a>
     */
    @PostMapping("/api/publish/getComposeSkuInfoBySpu")
    ApiResult<ComposeSku> getComposeSkuInfoBySpu(@RequestParam("spu") String spu);

    /**
     * 根据sku查组合sku
     * <a href="http://172.16.10.40/web/#/27/7208">接口地址</a>
     */
    @PostMapping("/api/publish/getComposeSkuInfoBySku")
    ApiResult<ComposeSku> getComposeSkuInfoBySku(@RequestParam("sku") String sku);

    /**
     * 根据套装货号获取对应组合SPU
     * K,V   K: 套装货号 V: 组合SPU
     * <a href="http://172.16.10.40/web/#/27/7208">接口地址</a>
     */
    @PostMapping("/api/publish/getComposeSkuBySuit")
    ApiResult<Map<String,String>> getComposeSkuBySuit(@RequestBody List<String> suitList);

    /**
     * 获取组合套装主子sku
     * @param skuList
     * @return
     */
    @PostMapping("/api/publish/getComposeAndSuiteSku")
    ApiResult<ComposeAndSuiteSkuResponse> getComposeAndSuiteSku(@RequestBody List<String> skuList);

    /**
     * 根据单品状态和时间查询该状态被标记了n天的sku
     * http://172.16.10.40/web/#/27/7346
     * @return
     */
    @PostMapping("/api/publish/getSkuByItemStatusAndTime")
    ApiResult<List<String>> getSkuByItemStatusAndTime(@RequestBody SkuStatusRequest request);

    /**
     * 根据单品状态和状态修改时间查询该状态组合套装货号
     * <a href="http://172.16.10.40/web/#/27/9189">接口文档地址</a>
     *
     * @param paramBody
     * @return
     */
    @PostMapping("/api/publish/getComposeSuiteSkuByItemStatusAndUpdateTime")
    ApiResult<List<String>> getComposeSuiteSkuByItemStatusAndUpdateTime(@RequestBody Map<String, Object> paramBody);

    /**
     * 根据分类完整路径code返回所有只节点类目id
     * http://172.16.10.40/web/#/27/7377
     * @return
     */
    @PostMapping("/api/publish/getAllCodeByFullPath")
    ApiResult<List<Integer>> getAllCodeByFullPath(@RequestBody List<String> fullCodeList);


    /**
     * 根据货号获取套装明细
     * http://172.16.10.40/web/#/27/7507
     */
    @GetMapping("/api/publish/getSaleSuiteVOBySuiteSku")
    ApiResult<SuiteSku> getSaleSuiteVOBySuiteSku(@RequestParam("suiteSku") String suiteSku);


    /**
     * 根据货号判断是否在套装管理中
     * http://172.16.10.40/web/#/27/7505
     */
    @GetMapping("/api/publish/isExistSaleSuite")
    Boolean isExistSaleSuite(@RequestParam("suiteSku") String suiteSku);

    /**
     * 根据时间查看此时间段内sku变更信息
     * http://172.16.10.40/web/#/27/5457
     */
    @PostMapping("/api/publish/getSkuChangeSku")
    ApiResult<?> getSkuChangeSku(@RequestBody Map<String, String> param);

    /**
     * 根据时间查看此时间段内标题更新的sku
     * http://172.16.10.40/web/#/27/7765
     */
    @PostMapping("/api/publish/getTitleChangeSku")
    ApiResult<List<String>> getTitleChangeSku(@RequestBody Map<String, String> param);

    /**
     * 提供所有子节点类目(只需要子节点的相关类目)
     * http://172.16.10.40/web/#/27/7761
     */
    @GetMapping("/api/publish/getAllSonFullPath")
    ApiResult<List<ProductCategoryResponse>> getAllSonFullPath();

    /**
     * 根据spu查询产品系统存在的spu
     * <a href="http://172.16.10.40/web/#/27/8364">接口文档</a>
     */
    @PostMapping("/api/publish/getExistSpuBySpu")
    ApiResult<List<String>> getExistSpuBySpu(@RequestBody List<String> spu);

    /**
     * 查询传入的sku的主sku下的所有子sku禁售信息
     * spu sku集合,主 子sku都可以
     * http://172.16.10.40/web/#/27/8747
     */
    @PostMapping("/api/publish/getForbiddenSalesBySpu")
    ApiResult<List<SingleItemEs>> getForbiddenSalesBySpu(@RequestBody List<String> spu);


    /**
     * 查询sku禁售信息以及特殊标签(只查询传入的子sku,不查子主sku和其他同级子sku信息)
     * sku 子sku集合,只查询子sku信息
     * http://172.16.10.40/web/#/27/9571
     */
    @PostMapping("/api/publish/getInfringementAndSpecicalBySonSku")
    ApiResult<List<ForbiddenAndSpecical>> getForbiddenAndSpecicalBySonSku(@RequestBody List<String> sku);

    /**
     * 获取主sku的类目全路径code
     * 主sku集合
     * http://172.16.10.40/web/#/27/8753
     */
    @PostMapping("/api/publish/getFullpathcodeBySpu")
    ApiResult<Map<String, String>> getFullpathcodeBySpu(@RequestBody List<String> spu);


    /**
     * (废弃)
     * 获取主sku的CE认证
     * 主sku集合
     * http://172.16.10.40/web/#/27/9044
     */
    @Deprecated
    @GetMapping("/api/publish/getProductProprietary")
    ApiResult<List<String>> getProductProprietary(@RequestParam("spu") String spu);

    /**
     * 查询单品ce附件变动数据
     * @param request
     * @return
     */
    @PostMapping("/api/publish/queryCertifiedCeFile")
    ApiResult<Map<String, List<String>>> queryCertifiedCeFile(@RequestBody QueryCertifiedCeFileRequest request);

    /**
     * 获取主sku的CE认证
     * <p>
     * <a href="http://172.16.10.40/web/#/27/9560">接口文档地址</a>
     */
    @PostMapping("/api/publish/queryProductProprietaryV2")
    ApiResult<Map<String, Map<String, List<String>>>> queryProductProprietaryV2(@RequestBody Map<String, String> param);

    /**
     * 根据货号查询组合套装产品的单品状态
     * <a href="http://172.16.10.40/web/#/27/9179">接口文档地址</a>
     *
     * @param articleNumberList
     * @return key:货号 value:状态
     */
    @PostMapping("/api/publish/getComposeSuiteItemStatusBySkus")
    ApiResult<Map<String, Integer>> getComposeSuiteItemStatusBySku(@RequestBody List<String> articleNumberList);


    /**
     * 获取插头规格配置
     * <a href="http://172.16.10.40/web/#/27/9299">接口文档地址</a>
     *
     * @return key: 插头规格 value:适用地区
     */
    @GetMapping("/api/publish/getAllPlugConfig")
    ApiResult<Map<String, String>> getAllPlugConfig();


    /**
     * http://172.16.10.40/web/#/27/9602
     * 获取子sku的包装费(包材的采购费 + 搭配包材的采购费)
     * @param sonSkuList
     * @return
     */
    @PostMapping("/api/publish/getPackingmaterialPriceBySkus")
    ApiResult<List<SkuPackingmaterialPriceResponse>> getPackingmaterialPriceBySkus(@RequestBody List<String> sonSkuList);

    /**
     * 根据skus获取组合套装的刊登显示信息
     * http://172.16.10.40/web/#/27/9631
     * @param sonSkuList
     * @return
     */
    @PostMapping("/api/publish/getComposeAndSuitePublishInfoVo")
    ApiResult<List<ComposeAndSuiteInfoVO>> getComposeAndSuitePublishInfoVo(@RequestBody List<String> sonSkuList);

    /**
     * 获取套装数据
     * http://172.16.10.40/web/#/27/9831
     * @param beginId   起始ID
     * @param size      大小
     * @param isEnable  状态：是否启用【1启用 0 禁用】
     * @return
     */
    @GetMapping("/api/publish/getSuite")
    ApiResult<List<ProductSuiteResponse>> getSuite(@RequestParam("beginId") String beginId,
                                                   @RequestParam("size") String size,
                                                   @RequestParam(value = "isEnable", required = false) Integer isEnable);

    /**
     * 获取组合数据
     * http://172.16.10.40/web/#/27/9832
     * @param beginId    起始ID
     * @param size       大小
     * @param checkStep  状态 8000:待提交 8001:待翻译，修图 8002:待审核 8003:正常 8004:废弃
     * @return
     */
    @GetMapping("/api/publish/getComposeSku")
    ApiResult<List<ProductComposeSkuResponse>> getComposeSku(@RequestParam("beginId") String beginId,
                                                             @RequestParam("size") String size,
                                                             @RequestParam(value = "checkStep", required = false) Integer checkStep);


    /**
     * http://172.16.10.40/web/#/27/9847
     * 根据条件查询符合的spu集合
     * @param request
     * @return
     */
    @PostMapping("/api/publish/querySpuByCondition")
    ApiResult<List<QuerySpuByConditionVo>> querySpuByCondition(@RequestBody QuerySpuByCondition request);

    /**
     * http://172.16.10.40/web/#/27/11566
     * 根据spu获取spu维度出单量信息
     * @param request
     * @return
     */
    @PostMapping("/api/publish/query_spu_order_count")
    ApiResult<List<SpuOrderCount>> querySpuOrderCount(@RequestBody QuerySpuOrderCount request);

    /**
     * 根据spu获取亚马逊文案
     */
    @GetMapping("/api/publish/get-official/spu")
    ApiResult<?> getAmazonOfficial(@RequestParam("spu") String spu,@RequestParam("language") String language);

    /**
     * 根据spu获取亚马逊文案
     * spu	是	array<string>	spu
     * language	是	string	默认 en
     */
    @PostMapping("/api/publish/batch-get-official")
    ApiResult<List<?>> getAmazonOfficialList(Map<String, Object> params);

    /**
     *查询无范本spu自定义传入平台站点
     * @param request
     * @return
     */
    @PostMapping("/api/publish/spu/query_no_template")
    ApiResult<List<String>> queryNoTemplateSpu(@RequestBody QueryNoTemplateSpuRequest request);

    /**
     * 查询亚马逊首位侵权词
     * http://172.16.10.40/web/#/27/10261
     * @return
     */
    @GetMapping("/api/publish/amz_first_infringementWord/query")
    ApiResult<List<String>> queryAmzFirstInfringementWords();


    /**
     * 获取侵权风险等级配置
     * http://172.16.10.40/web/#/27/10452
     * @return
     */
    @GetMapping("/api/publish/infringement/risk_level_config_info")
    ApiResult<List<RiskLevel>> getRiskLevelList();

    /**
     * 获取侵权类型配置信息
     * http://172.16.10.40/web/#/27/10451
     * status    1：启用，0：禁用
     * @return
     */
    @GetMapping("/api/publish/infringement/prohibition_type_config")
    ApiResult<List<ProhibitionTypeConfig>> getProhibitionTypeConfigList(@RequestParam("status") int status);

    /**
     * 根据sku获取对应的GPSR供应商
     * @param skuList
     * @return
     */
    @PostMapping("/api/publish/get_gpsr_manufacturer_by_sku")
    ApiResult<?> getGpsrManufacturerBySku(@RequestBody List<String> skuList);

    /**
     * http://172.16.10.40/web/#/27/10716
     * 根据条件获取spu中最重sku
     * @param request
     * @return
     */
    @PostMapping("/api/publish/query_heaviest_sku_with_condition")
    ApiResult<List<QuerySpuByConditionVo>> queryHeaviestSkuWithCondition(@RequestBody QuerySpuByCondition request);

    /**
     * 查询所有分类对应的警示语
     * http://172.16.10.40/web/#/27/10796
     *
     * @return
     */
    @PostMapping("/api/publish/get_gpsr_warning")
    ApiResult<Map<String, String>> getGPSRWarnings();

    /**
     * 根据sku查询冠通大健云仓库存
     * http://172.16.10.40/web/#/27/10950
     *
     * @return
     */
    @PostMapping("/api/publish/goten/giga_stock")
    ApiResult<Map<String, Integer>> getGigaStocks(@RequestBody List<String> skuList);

    /**
     * 推送新品美工修图确认
     * http://172.16.10.40/web/#/27/11640
     *
     * @param spuImageRequirement
     * @return
     */
    @PostMapping("/api/publish/add/new_product_ps_confirm")
    ApiResult<String> addNewProductPsConfirm(@RequestBody SpuImageRequirement spuImageRequirement);

    /**
     * 通过spu获取到spu下所有的子sku以及子ku的禁售信息（要求实时查询的）
     * http://172.16.10.40/web/#/27/11647
     * @param spuList
     * @return
     */
    @PostMapping("/api/publish/getAllSkuInfringementInfoBySpu")
    ApiResult<Map<String, List<InfringementSaleProhibitionVO>>> getAllSkuInfringementInfoBySpu(@RequestBody List<String> spuList);

    /**
     * 根据spu获取销售开发列表图片
     * http://172.16.10.40/web/#/27/12153
     *
     * @param spuList
     */
    @PostMapping("/api/publish/compose_sku/image_by_spu")
    ApiResult<Map<String, List<String>>> getComposeSkuImageBySpu(@RequestBody List<String> spuList);

    /**
     * 根据spu获取销售开发列表子sku
     * http://172.16.10.40/web/#/27/12157
     *
     * @param spuList
     */
    @PostMapping("/api/publish/compose_sku/query_son_sku")
    ApiResult<Map<String, List<String>>> getComposeSkuQuerySonSku(@RequestBody List<String> spuList);


    /**
     * 功能描述: 获取审核亚马逊文案根据spu
     * http://172.16.10.40/web/#/27/12391
     **/
    @RequestMapping(value = "/api/publish/get_check_amazon_official/by_spu?spu={spu}", method = RequestMethod.GET, headers = "content-type=application/json")
    ApiResult<OfficialAmazonResponse> getCheckAmazonOfficial(@PathVariable("spu") String spu);


    /**
     * 目前是给必刊登新品文案审核时候使用
     * 校验了网址 和平台
     * http://172.16.10.40/web/#/27/12400
     *
     * @return
     */
    @PostMapping("/api/publish/check_official")
    ApiResult<Void> checkOfficial(@RequestBody CheckOfficialRequest request);


    /**
     * 查询对应词的禁售信息启用的侵权词和商标词
     * http://172.16.10.40/web/#/27/12423
     *
     * @return
     */
    @PostMapping("/api/publish/query_prohibition/by_words")
    ApiResult<List<QueryProhibitionByWordsVO>> queryProhibitionByWords(@RequestBody List<String> words);


    /**
     * 更新单品sku的禁售信息，SMT平台全站点禁售
     * http://172.16.10.40/web/#/27/12451
     * @param request 推送数据
     * @return 推送结果
     */
    @PostMapping("/api/publish/single_item/smt/infringement")
    ApiResult<Void> pushSkuForbiddenInfo(@RequestBody SkuForbiddenInfoPushRequest request);

    /**
     * 审核更新亚马逊文案
     * http://172.16.10.40/web/#/27/12465
     *
     * @param message
     * @return
     */
    @PostMapping("/api/publish/checkAmazonOfficial")
    ApiResult<String> checkAmazonOfficial(@RequestBody CopywritingMessage message);

}
