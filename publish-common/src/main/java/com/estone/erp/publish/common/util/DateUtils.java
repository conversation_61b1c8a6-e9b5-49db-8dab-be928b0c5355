package com.estone.erp.publish.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class DateUtils {

    public static Long DAYINMILISECOND = 86400000l;
    public final static String MAX_OF_TIME = "23:59:59";
    public final static String MIN_OF_TIME = "00:00:00";


    public static int[] getYmd(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        int day = cal.get(Calendar.DAY_OF_MONTH);
        int[] ymd = new int[] { year, month, day };
        return ymd;
    }

    public static String getYmdStr(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        int day = cal.get(Calendar.DAY_OF_MONTH);
        int[] ymd = new int[] { year, month, day };
        String ymdStr = "";
        for (int i : ymd) {
            ymdStr = ymdStr + i;
        }
        return ymdStr;
    }

    public static int[] getHms(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        int minute = cal.get(Calendar.MINUTE);
        int second = cal.get(Calendar.SECOND);
        int[] hms = new int[] { hour, minute, second };
        return hms;
    }

    public static int getM(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int M = cal.get(Calendar.MONTH) + 1;
        return M;
    }

    public static Calendar getCalendar(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, cal.getMinimum(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, cal.getMinimum(Calendar.MINUTE));
        cal.set(Calendar.SECOND, cal.getMinimum(Calendar.SECOND));
        return cal;
    }

    public static Date getFirstDayOfMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_MONTH, cal.getMinimum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, cal.getMinimum(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, cal.getMinimum(Calendar.MINUTE));
        cal.set(Calendar.SECOND, cal.getMinimum(Calendar.SECOND));
        return cal.getTime();
    }

    public static Date getMaxDateOfMonth(Integer year, Integer month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month - 1, Calendar.YEAR); // 设置年份和月份
        calendar.add(Calendar.MONTH, Calendar.YEAR); // 加一个月
        calendar.add(Calendar.DATE, -1); // 减去一天，得到当月最后一天
        calendar.set(Calendar.HOUR_OF_DAY, 23); // 设置小时为23
        calendar.set(Calendar.MINUTE, 59); // 设置分钟为59
        calendar.set(Calendar.SECOND, 59); // 设置秒为59
        return calendar.getTime();
    }

    public static Date getMinDateOfMonth(Integer year, Integer month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month - 1, Calendar.YEAR);
        calendar.set(Calendar.HOUR_OF_DAY, Calendar.ERA);
        calendar.set(Calendar.MINUTE, Calendar.ERA);
        calendar.set(Calendar.SECOND, Calendar.ERA);
        calendar.set(Calendar.MILLISECOND, Calendar.ERA);
        return calendar.getTime();
    }

    public static Calendar getFirstDayOfMonthCal(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_MONTH, cal.getMinimum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, cal.getMinimum(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, cal.getMinimum(Calendar.MINUTE));
        cal.set(Calendar.SECOND, cal.getMinimum(Calendar.SECOND));
        return cal;
    }

    /**
     * 对应时间月最后一天23.59.59
     * @param date
     * @return
     */
    public static Date getLastDayOfMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
//        cal.set(Calendar.DAY_OF_MONTH, cal.getMaximum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, cal.getMaximum(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, cal.getMaximum(Calendar.MINUTE));
        cal.set(Calendar.SECOND, cal.getMaximum(Calendar.SECOND));
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        return cal.getTime();
    }

    public static Calendar getLastDayOfMonthCal(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
//        cal.set(Calendar.DAY_OF_MONTH, cal.getMaximum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, cal.getMaximum(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, cal.getMaximum(Calendar.MINUTE));
        cal.set(Calendar.SECOND, cal.getMaximum(Calendar.SECOND));
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        return cal;
    }

    public static Date getDate(Integer year, Integer month, Integer day) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.MONTH, month - 1);
        cal.set(Calendar.DAY_OF_MONTH, day);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    public static Date getToday() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, cal.get(Calendar.YEAR));
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH));
        cal.set(Calendar.DAY_OF_MONTH, cal.get(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    public static Date getDate(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.YEAR, cal.get(Calendar.YEAR));
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH));
        cal.set(Calendar.DAY_OF_MONTH, cal.get(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    public static Date getDate(Date date, int hour) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.YEAR, cal.get(Calendar.YEAR));
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH));
        cal.set(Calendar.DAY_OF_MONTH, cal.get(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, hour);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    public static Calendar getTodayCal() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, cal.get(Calendar.YEAR));
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH));
        cal.set(Calendar.DAY_OF_MONTH, cal.get(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal;
    }

    public static XMLGregorianCalendar getXMLGregorianCalendar(Date date) {
        DatatypeFactory dataTypeFactory;
        try {
            dataTypeFactory = DatatypeFactory.newInstance();
        }
        catch (DatatypeConfigurationException e) {
            throw new RuntimeException(e);
        }
        GregorianCalendar gc = new GregorianCalendar();
        gc.setTimeInMillis(date.getTime());
        return dataTypeFactory.newXMLGregorianCalendar(gc);
    }

    public XMLGregorianCalendar dateToXml(Date date) {
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(date);
        XMLGregorianCalendar gc = null;
        try {
            gc = DatatypeFactory.newInstance().newXMLGregorianCalendar(cal);
        }
        catch (Exception e) {
            log.warn(e.getMessage(), e);
        }
        return gc;
    }

    public Date xmlToDate(XMLGregorianCalendar gc) {
        GregorianCalendar ca = gc.toGregorianCalendar();
        return ca.getTime();
    }

    public static Date getFirstDayOfMonth(Integer year, Integer month) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.MONTH, month - 1);
        cal.set(Calendar.DAY_OF_MONTH, cal.getMinimum(Calendar.DAY_OF_MONTH));
        return cal.getTime();
    }

    public static String dateToString(Date date, String pattern) {
        if (null == date) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    public static String dateToString(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }

    public static Date stringToDate(String dateStr, String pattern) {
        Date date = null;
        try {
            if (dateStr.contains("CST")) {
                date = parse(dateStr, "EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
            }
            else {
                SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                date = sdf.parse(dateStr);
            }
        }
        catch (ParseException e) {
            log.warn(e.getMessage(), e);
            date = parse(dateStr, "EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
        }
        return date;
    }

    public static Date parse(String str, String pattern, Locale locale) {
        if (str == null || pattern == null) {
            return null;
        }
        try {
            return new SimpleDateFormat(pattern, locale).parse(str);
        }
        catch (ParseException e) {
            log.warn(e.getMessage(), e);
        }
        return null;
    }

    @SuppressWarnings("deprecation")
    public static List<Date> getCalendarMonthDates(Integer year, Integer month) {
        List<Date> dates = new ArrayList<Date>();
        Date firstDateOfMonth = getFirstDayOfMonth(year, month);
        for (int i = 0; i < 6; i++) {
            for (int j = 0; j < 7; j++) {
                Date currentDay = new Date();
                currentDay.setTime(
                        firstDateOfMonth.getTime() + ((j - firstDateOfMonth.getDay()) + 7 * i) * DAYINMILISECOND);
                dates.add(currentDay);
            }
        }
        return dates;
    }

    public static List<Date> getBetweenDates(Date startDate, Date endDate) {
        if (startDate.after(endDate)) {
            return null;
        }
        List<Date> dates = new ArrayList<Date>();
        while (startDate.before(endDate)) {
            dates.add(startDate);
            startDate = DateUtils.getTomorrowDate(startDate);
        }
        dates.add(endDate);
        return dates;
    }

    public static int getWorkingDay(java.util.Calendar startCal, java.util.Calendar endCal) {
        int result = -1;
        if (startCal.after(endCal)) {
            java.util.Calendar swap = startCal;
            startCal = endCal;
            endCal = swap;
        }
        int charge_start_date = 0;
        int charge_end_date = 0;
        int stmp;
        int etmp;
        stmp = 7 - startCal.get(Calendar.DAY_OF_WEEK);
        etmp = 7 - endCal.get(Calendar.DAY_OF_WEEK);
        if (stmp != 0 && stmp != 6) {
            charge_start_date = stmp - 1;
        }
        if (etmp != 0 && etmp != 6) {
            charge_end_date = etmp - 1;
        }
        result = (getDaysBetween(getNextMonday(startCal).getTime(), getNextMonday(endCal).getTime()) / 7) * 5
                + charge_start_date - charge_end_date;
        return result;
    }

    public static Calendar getNextMonday(Calendar date) {
        Calendar result = null;
        result = date;
        do {
            result = (Calendar) result.clone();
            result.add(Calendar.DATE, 1);
        }
        while (result.get(Calendar.DAY_OF_WEEK) != 2);
        return result;
    }

    public static int getHolidays(Calendar d1, Calendar d2) {
        return getDaysBetween(d1.getTime(), d2.getTime()) - getWorkingDay(d1, d2);
    }

    public static Date getBeforeDate(Date date, int days) {
        Date beforeDate = new Date();
        beforeDate.setTime(date.getTime() - DAYINMILISECOND * days);
        return beforeDate;
    }

    public static Date getAfterDate(Date date, int days) {
        Date beforeDate = new Date();
        beforeDate.setTime(date.getTime() + DAYINMILISECOND * days);
        return beforeDate;
    }

    public static Date getBeforeHourDate(Date date, double hours) {
        Date beforeHourDate = new Date();
        beforeHourDate.setTime(date.getTime() - (long) (60 * 60 * 1000 * hours) - 15 * 60 * 1000);
        return beforeHourDate;
    }

    public static Date getAfterHourDate(Date date, double hours) {
        Date beforeHourDate = new Date();
        beforeHourDate.setTime(date.getTime() + (long) (60 * 60 * 1000 * hours));
        return beforeHourDate;
    }

    public static Date getBeforeMinuteDate(Date date, double minutes) {
        Date beforeHourDate = new Date();
        beforeHourDate.setTime(date.getTime() - (long) (60 * 1000 * minutes) - 5 * 60 * 1000);
        return beforeHourDate;
    }

    public static Date getBeforeMinuteDate(Date date, double minutes, int leg) {
        Date beforeHourDate = new Date();
        beforeHourDate.setTime(date.getTime() - (long) (60 * 1000 * minutes) - leg * 60 * 1000);
        return beforeHourDate;
    }

    public static Date getAfterMinuteDate(Date date, double minutes) {
        Date beforeHourDate = new Date();
        beforeHourDate.setTime(date.getTime() + (long) (60 * 1000 * minutes));
        return beforeHourDate;
    }

    public static Date getYesterdayDate(Date today) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(today.getTime() - DAYINMILISECOND);
        return cal.getTime();
    }

    public static Date getWeekBefore(Date today) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(today.getTime() - DAYINMILISECOND * 7);
        return cal.getTime();
    }

    public static Date getTomorrowDate(Date today) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(today.getTime() + DAYINMILISECOND);
        return cal.getTime();
    }

    public static Date getLastDayInLastMonth(Date date) {
        Date firstDay = getFirstDayOfMonth(date);
        return getYesterdayDate(firstDay);
    }

    public static Date getFirstDayInNextMonth(Date date) {
        Date lastDay = getLastDayOfMonth(date);
        return getTomorrowDate(lastDay);
    }

    public static Boolean isInSameMonth(Date date1, Date date2) {
        int[] ymd1 = getYmd(date1);
        int[] ymd2 = getYmd(date2);
        return (ymd1[1] == ymd2[1]) && (ymd1[0] == ymd2[0]);
    }

    public static boolean isEmptyDate(Calendar testCal) {
        boolean emptyDate = false;
        Calendar c = new GregorianCalendar(TimeZone.getTimeZone("GMT"));
        c.clear();
        if (testCal == null) {
            emptyDate = true;
        }
        else {
            int[] ymd = DateUtils.getYmd(testCal.getTime());
            if (ymd[0] == 1970 && ymd[1] == 1 && ymd[2] == 1) {
                emptyDate = true;
            }
            else if (c.getTimeInMillis() == testCal.getTimeInMillis()) {
                emptyDate = true;
            }
        }
        return emptyDate;
    }

    public static boolean isEmptyDate(Date testDate) {
        boolean emptyDate = false;
        Calendar c = new GregorianCalendar(TimeZone.getTimeZone("GMT"));
        c.clear();
        if (testDate == null) {
            emptyDate = true;
        }
        else {
            int[] ymd = DateUtils.getYmd(testDate);
            if (ymd[0] == 1970 && ymd[1] == 1 && ymd[2] == 1) {
                emptyDate = true;
            }
            else if (c.getTimeInMillis() == testDate.getTime()) {
                emptyDate = true;
            }
        }
        return emptyDate;
    }

    public static Integer getHoursBetween(Date startTime, Date endTime) {

        if (startTime == null || endTime == null) {
            return null;
        }

        Double hoursBetween = (endTime.getTime() - startTime.getTime()) / (60 * 60 * 1000d);
        return (int) hoursBetween.doubleValue();
    }

    public static Integer getDaysBetween(Date startTime, Date endTime) {

        if (startTime == null || endTime == null) {
            return null;
        }

        Double daysBetween = (endTime.getTime() - startTime.getTime()) / (24 * 60 * 60 * 1000d);
        return (int) daysBetween.doubleValue();
    }

    public static java.sql.Date toSqlDate(Date utilDate) {
        return new java.sql.Date(utilDate.getTime());
    }

    public static Date getAmazonDate(String date) {
        if (StringUtils.isNotBlank(date)) {
            String parsePatterns = "yyyy-MM-dd HH:mm:ss";
            if (StringUtils.contains(date, "BST") || StringUtils.contains(date, "GMT")
                    || StringUtils.contains(date, "MEST") || StringUtils.contains(date, "MET")) {
                parsePatterns = "dd/MM/yyyy HH:mm:ss";
            }
            try {
                return new SimpleDateFormat(parsePatterns).parse(date);
            }
            catch (ParseException e) {
                e.printStackTrace();
                log.error(e.getMessage(), e);
            }
        }

        return null;
    }

    /**
     * 获取当前时间前一天的日期时间
     * 
     * @return
     */
    public static Date getDateBegin(int day) {
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        if(day != 0){
            calendar.add(Calendar.DAY_OF_MONTH, day);
        }
        date = calendar.getTime();
        return date;
    }

    public static Date getDateEnd(int day) {
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        if(day != 0){
            calendar.add(Calendar.DAY_OF_MONTH, day);
        }
        date = calendar.getTime();
        return date;
    }

    /**
     * 获取当前时间前一天的日期时间
     *
     * @return
     */
    public static String getStringDateBegin(int day) {
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        if(day != 0){
            calendar.add(Calendar.DAY_OF_MONTH, day);
        }
        date = calendar.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    public static String getStringDateEnd(int day) {
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        if(day != 0){
            calendar.add(Calendar.DAY_OF_MONTH, day);
        }
        date = calendar.getTime();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    /**
     * 获取几天前的时间
     * @param i
     * @return
     */
    public static Date getNewDateBeforeDay(int i) {
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, -i);
        date = calendar.getTime();
        return date;
    }

    /**
     * 获取几个月前的日期 -1 则上个月
     * @param month
     * @return
     */
    public static Date getbeforeMonthDay(int month) {

        Date da = new Date();// 获取当前时间
        Calendar calendar = Calendar.getInstance(); // 得到日历
        calendar.setTime(da);// 把当前时间赋给日历
        calendar.add(calendar.MONTH, month); // 设置为前n月
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        return calendar.getTime();
    }

    /**
     * 获取时间区间集合
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static List<Map<String, Timestamp>> getTimeIntervalList(Timestamp startTime, Timestamp endTime) {
        if(startTime == null || endTime == null){
            return new ArrayList<>(0);
        }

        Calendar tempStart = Calendar.getInstance();
        tempStart.setTime(startTime);

        Calendar tempEnd = Calendar.getInstance();
        tempEnd.setTime(endTime);
        tempEnd.set(Calendar.HOUR_OF_DAY, 0);
        tempEnd.set(Calendar.MINUTE, 0);
        tempEnd.set(Calendar.SECOND, 0);
        tempEnd.set(Calendar.MILLISECOND, 0);

        List<Map<String, Timestamp>> result = new ArrayList<>();
        //左闭右开区间 [startTime, endTime)
        while (tempStart.before(tempEnd)) {
            Map<String, Timestamp> map = new HashMap<>(2);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(tempStart.getTime());

            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            map.put("start", new Timestamp(calendar.getTimeInMillis()));

            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            map.put("end", new Timestamp(calendar.getTimeInMillis()));

            result.add(map);
            tempStart.add(Calendar.DATE, 1);
        }
        return result;
    }

    /**
     * 获取中间日期
     * @param startTime
     * @param endTime
     * @return
     */
    public static Date getMiddleDate(Date startTime, Date endTime) {
        if (startTime == null || endTime == null) {
            return null;
        }

        long averageSeconds = (startTime.getTime() + endTime.getTime()) / 2;
        Date averageDate = new Date(averageSeconds);
        return averageDate;
    }


    /**
     * 获取月最后一天 既月的天数
     * @param date
     * @return
     */
    public static Integer getMonthLastDay(Date date)  {
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(date);
        return cal.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取距离 结束日期还有多少毫秒
     * @param day
     * @return
     */
    public static Long getDistanceEndDateMilliseconds(int day) {
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        if(day != 0){
            calendar.add(Calendar.DAY_OF_MONTH, day);
        }

        return calendar.getTimeInMillis() - System.currentTimeMillis();
    }

    /**
     * 当天最大时间
     *
     * @param date yyyy-MM-dd
     * @return yyyy-MM-dd 23:59:59
     */
    public static String formatMaxDateTime(String date) {
        return date + " " + MAX_OF_TIME;
    }

    /**
     * 当天最小时间
     *
     * @param date yyyy-MM-dd
     * @return yyyy-MM-dd 00:00:00
     */
    public static String formatMinDateTime(String date) {
        return date + " " + MIN_OF_TIME;
    }
}
