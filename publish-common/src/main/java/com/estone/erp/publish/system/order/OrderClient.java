package com.estone.erp.publish.system.order;

import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.system.account.modle.FrozenAccountVO;
import com.estone.erp.publish.system.account.modle.SalesmanAccountInfoRequest;
import com.estone.erp.publish.system.order.modle.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 销售订单系统
 *
 * @Auther yucm
 * @Date 2020/12/23
 */
@FeignClient(name = "sale-service")
public interface OrderClient {

    /**
     * 功能描述: 获取销售账号具体信息接口
     * saleChannel请使用 SaleChannelEnum 枚举类的name值
     **/
    @PostMapping(value = "/customerOrder/getCustomerOrderByCustomLabel")
    String getCustomerOrderBySku(@RequestBody ApiRequestParam<String> requestParam);

    /**
     * 获取sku销量
     * {
     *     "args":"[{'saleChannel':'SMT','accountNumber':'<EMAIL>','customLabel':'M89F7AB0341'}]"
     * }
     * @param
     * @return
     */
    @RequestMapping(value = "/customerOrder/getCustomerOrderBySellerSku",
            method = RequestMethod.POST, headers = "content-type=application/json")
    ApiResult<?> getCustomerOrderBySellerSku(String s);

    /**
     * 获取所有国家
     * @return
     */
    @GetMapping(value = "/country/list")
    String getCountryList();

    /**
     * 根据子asin查询订单FBA库存管理存在的asin
     * http://172.16.10.40/web/#/page/6187
     * @param body
     * @return
     */
    @PostMapping("/amazonFBA/getFbaInventoryAsin")
    ApiResult<String> getFbaInventoryAsin(@RequestBody Map<String,String> body);

    /**
     * 查询FBA库存管理中的asin码对应记录是否有可售数量或在途数量
     * <a href="http://172.16.10.40/web/#/18/8288">接口文档地址</a>
     * @param asinList 子asin
     * @return 存在库存的asin
     */
    @PostMapping("/amazonFBA/checkSellableAndOnWayQuantity")
    ApiResult<List<String>> checkSellableAndOnWayQuantity(@RequestBody List<String> asinList);

    /**
     * 修改账号状态为异常
     * @param accountInfoRequest 账号信息
     * @return 站点
     */
    @PostMapping("/saleaccount/updateAccountExceptionStatus")
    ApiResult<String> updateAccountExceptionStatus(@RequestBody SalesmanAccountInfoRequest accountInfoRequest);

    @GetMapping("/walmartOrder/selectWalmartTemuSaleVolume")
    String selectWalmartTemuSaleVolume(@RequestParam(value = "day") Integer day, @RequestParam(value = "accountNumber") String accountNumber);

    /**
     * 获取谷仓库龄
     * http://172.16.10.40/web/#/18/7884
     * @param request
     * @return
     */
    @PostMapping("/goodcang/getInventoryAge")
    ApiResult<List<GoodcangInventoryAgeResponse>> getGoodcangInventoryAge(@RequestBody GoodcangInventoryAgeRequest request);

    /**
     * 获取谷仓所有仓库code
     * <a href="http://172.16.10.40/web/#/18/8116">接口文档链接</a>
     * @return 仓库code
     */
    @GetMapping("/goodcang/getWarehouseCode")
    ApiResult<List<String>> getWarehouseCode();

    /**
     * 近3天冻结账号
     * http://172.16.10.40/web/#/18/7925
     *
     * @param saleChannel 平台
     * @return
     */
    @GetMapping("/publishService/account/listFrozenAccountIn3Days/{saleChannel}")
    ApiResult<List<FrozenAccountVO>> getFreezeAccountToPublish(@RequestParam String saleChannel);

    /**
     * 获取FBA库存管理所有asin的信息
     * http://172.16.10.40/web/#/18/9627
     * @return
     */
    @GetMapping("/amazonFBA/getAllAsinInfo")
    ApiResult<List<AsinInfoResponse>> getAllAsinInfo();

    /**
     * 统计亚马逊Asin当天销量
     * @param asinList
     * @return
     */
    @PostMapping("/publish/amazon/salesVolume")
    ApiResult<List<AsinSalesVolume>> publishAmazonSalesVolume(@RequestBody List<String> asinList);

    /**
     * 刷新亚马逊店铺账号ean前缀
     * http://172.16.10.40/web/#/18/10381
     * @param accountNumber
     * @return
     */
    @RequestMapping(value = "/publish/amazon/refreshAccountEanPrefix", method = RequestMethod.GET, headers = "content-type=application/json")
    ApiResult<String> refreshAccountEanPrefix(@RequestParam(value = "accountNumber") String accountNumber, @RequestParam(value = "abandonedEanPrefix") String abandonedEanPrefix);


    /**
     * 新增亚马逊店铺账号ean前缀
     * http://172.16.10.40/web/#/18/10756
     *
     * @param accountNumber   亚马逊店铺账号
     * @param eanPrefixAmount ean前缀总数量
     * @return
     */
    @RequestMapping(value = "/publish/amazon/addAccountEanPrefix", method = RequestMethod.GET, headers = "content-type=application/json")
    ApiResult<String> addAccountEanPrefix(@RequestParam(value = "accountNumber") String accountNumber, @RequestParam(value = "eanPrefixAmount") Integer eanPrefixAmount);

    /**
     * 获取FBM当天出单ASIN
     * http://172.16.10.40/web/#/18/11422
     */
    @RequestMapping(value = "/publish/amazon/todaySoldFbmAsin", method = RequestMethod.GET, headers = "content-type=application/json")
    ApiResult<List<String>> todaySoldFbmAsin();

//
//    /**
//     * 根据店铺返回shopee店铺绩效订单未完成率和逾期出货率数据
//     * http://172.16.10.40/web/#/52/11576
//     */
//    @PostMapping(value = "/publish/shopee/getShopPerformance")
//    ApiResult<List<ShopPerformance>> getShopPerformance(@RequestBody List<String> accounts);


    /**
     * 根据店铺返回shopee店铺绩效订单未完成率和逾期出货率数据
     * http://172.16.10.40/web/#/52/11576
     */
    @PostMapping(value = "/publish/shopee/getShopPerformance")
    ApiResult<List<ShopPerformance>> getShopPerformance(@RequestBody List<String> accounts);

    /**
     * 标记店铺状态为疑似冻结
     * http://172.16.10.40/web/#/18/11905
     */
    @RequestMapping(value = "/publish/account/amazon/freezeAccount", method = RequestMethod.GET, headers = "content-type=application/json")
    ApiResult<String> freezeAccount(@RequestParam(value = "merchantId") String merchantId);

    /**
     * 获取walmart当天出单ASIN
     *
     * @return
     */
    @RequestMapping(value = "/publish/walmart/todaySoldFbmAsin", method = RequestMethod.GET, headers = "content-type=application/json")
    ApiResult<List<WalmartSaleQuantity>> todaySoldWalmartAsin();
}
