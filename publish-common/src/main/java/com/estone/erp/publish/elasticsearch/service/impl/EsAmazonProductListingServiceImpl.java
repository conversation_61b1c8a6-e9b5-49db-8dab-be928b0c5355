package com.estone.erp.publish.elasticsearch.service.impl;


import com.estone.erp.common.elasticsearch.util.ElasticSearchHelper;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch.dao.EsAmazonProductListingRepository;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonSkuPublishNumberDO;
import com.estone.erp.publish.elasticsearch.model.beanresponse.EsCompositeAggsResponse;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.feginService.modle.PlatformListingInfo;
import com.estone.erp.publish.feginService.modle.SkuPubilshListingFirstJoinTimeVo;
import com.estone.erp.publish.feginService.modle.SkuPublishListingNum;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.Strings;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.NestedQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.composite.CompositeAggregation;
import org.elasticsearch.search.aggregations.bucket.composite.CompositeAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.composite.ParsedComposite;
import org.elasticsearch.search.aggregations.bucket.composite.TermsValuesSourceBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.CardinalityAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHitSupport;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.SearchScrollHits;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;


@Slf4j
@Service
public class EsAmazonProductListingServiceImpl implements EsAmazonProductListingService {


    public static final IndexCoordinates amazonProductListingIndexCoordinates = IndexCoordinates.of("amazon_product_listing");
    @Autowired
    private EsAmazonProductListingRepository esAmazonProductListingRepository;
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate1;
    @Resource
    private RestHighLevelClient restHighLevelClient1;

    @Override
    public long count() {
        return esAmazonProductListingRepository.count();
    }

    @Override
    public void save(EsAmazonProductListing esAmazonProductListing) {
        if (esAmazonProductListing != null) {
            elasticsearchRestTemplate1.save(esAmazonProductListing);
        }
    }

    @Override
    public void saveAll(List<EsAmazonProductListing> esAmazonProductListingList) {
        if (CollectionUtils.isNotEmpty(esAmazonProductListingList)) {
            elasticsearchRestTemplate1.save(esAmazonProductListingList);
        }
    }

    @Override
    public void deleteById(String id) {
        esAmazonProductListingRepository.deleteById(QueryParser.escape(id));
    }

    @Override
    public void batchDelete(List<EsAmazonProductListing> deleteEsAmazonProductListingList) {
        esAmazonProductListingRepository.deleteAll(deleteEsAmazonProductListingList);
    }

    @Override
    public EsAmazonProductListing findAllById(String id) {
        return elasticsearchRestTemplate1.get(id, EsAmazonProductListing.class);
    }

    @Override
    public Page<EsAmazonProductListing> page(EsAmazonProductListingRequest esAmazonProductListingRequest, int pageSize, int pageIndex) {
        if (esAmazonProductListingRequest == null) {
            return null;
        }
        // 查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        buildBoolQuery(boolQueryBuilder, esAmazonProductListingRequest);

        // 只查询导出的字段
        List<String> downFields = esAmazonProductListingRequest.getDownFields();
        List<String> downListingDataNeedFields = esAmazonProductListingRequest.getDownListingDataNeedFields();
        String[] selectfields = esAmazonProductListingRequest.getFields();
        if (CollectionUtils.isNotEmpty(downListingDataNeedFields)) {
            queryBuilder.withFields(downListingDataNeedFields.toArray(new String[]{}));
        } else if (CollectionUtils.isNotEmpty(downFields)) {
            List<String> fields = new ArrayList<>(downFields);
            String needField = "accountNumber";
            if (!fields.contains(needField)) {
                fields.add(needField);
            }
            String filterField = "saleManList";
            fields.remove(filterField);
            String[] saleArray = new String[fields.size()];
            fields.toArray(saleArray);
            queryBuilder.withFields(saleArray);
        } else if (selectfields.length > 1) {
            queryBuilder.withFields(selectfields);
        }

        //创建查询条件构造器
        queryBuilder.withQuery(boolQueryBuilder);
        //构建分页
        // 每页条数
        pageSize = pageSize == 0 ? 10 : pageSize;
        //es的分页的页码从0开始
        pageIndex = pageIndex < 1 ? 0 : pageIndex;

        //排序
        String orderby = esAmazonProductListingRequest.getOrderBy();
        if (StringUtils.isEmpty(orderby)) {
            orderby = "syncDate";
        }
        String sequence = esAmazonProductListingRequest.getSequence();
        if (StringUtils.isEmpty(sequence) || sequence.equalsIgnoreCase("DESC")) {
            queryBuilder.withSort(SortBuilders.fieldSort(orderby).order(SortOrder.DESC));
        } else {
            queryBuilder.withSort(SortBuilders.fieldSort(orderby).order(SortOrder.ASC));
        }

        queryBuilder.withPageable(PageRequest.of(pageIndex, pageSize));
        NativeSearchQuery searchQuery = queryBuilder.build();
        searchQuery.setTrackTotalHits(true);
        return esAmazonProductListingRepository.search(searchQuery);
    }

    @Override
    public Page<EsAmazonProductListing> search(NativeSearchQuery searchQuery) {
        searchQuery.setTrackTotalHits(true);
        return esAmazonProductListingRepository.search(searchQuery);
    }

    @Override
    public List<SkuPublishListingNum> getAmazonListingNum(List<String> articleNumberList) {
        if (CollectionUtils.isEmpty(articleNumberList)) {
            return null;
        }
        long now = System.currentTimeMillis();
        List<SkuPublishListingNum> skuPublishListingNumList = new ArrayList<>();
        for (String sku : articleNumberList) {
            NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("articleNumber", sku.toUpperCase()))
                    .must(QueryBuilders.matchQuery("isOnline", true));
            queryBuilder
                    .withQuery(boolQueryBuilder)
                    .withPageable(PageRequest.of(0, 10000))
                    .withFields("articleNumber", "firstOpenDate", "openDate")
                    .withSort(SortBuilders.fieldSort("firstOpenDate").order(SortOrder.ASC))
                    .withSort(SortBuilders.fieldSort("openDate").order(SortOrder.ASC))
                    .build();
            NativeSearchQuery searchQuery = queryBuilder.build();
            searchQuery.setTrackTotalHits(true);
            Page<EsAmazonProductListing> search = esAmazonProductListingRepository.search(searchQuery);
            if (null != search && CollectionUtils.isNotEmpty(search.getContent())) {
                EsAmazonProductListing esAmazonProductListing = search.getContent().get(0);
                SkuPublishListingNum skuPublishListingNum = new SkuPublishListingNum();
                skuPublishListingNum.setArticleNumber(sku);
                skuPublishListingNum.setAmazonNum(search.getContent().size());
                Timestamp amazonEarliestTime = null;
                if (null != esAmazonProductListing.getFirstOpenDate()) {
                    amazonEarliestTime = new Timestamp(esAmazonProductListing.getFirstOpenDate().getTime());
                } else if (null != esAmazonProductListing.getOpenDate()) {
                    amazonEarliestTime = new Timestamp(esAmazonProductListing.getOpenDate().getTime());
                }
                skuPublishListingNum.setAmazonEarliestTime(amazonEarliestTime);
                skuPublishListingNumList.add(skuPublishListingNum);
            }
        }
        long timeES = System.currentTimeMillis() - now;
        log.info("/publishListing/getSkuListingNum查询ES->amazon_product_listing,耗时->{}ms,sku数量->{}", timeES, articleNumberList.size());
        return skuPublishListingNumList;
    }


    @Override
    public List<PlatformListingInfo> getAmazonListing(List<String> articleNumberList) {
        if (CollectionUtils.isEmpty(articleNumberList) || articleNumberList.size() > 1000) {
            return null;
        }
        long now = System.currentTimeMillis();
        List<PlatformListingInfo> platformListingInfos = new ArrayList<>();

        // 查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.matchQuery("isOnline", true));

        //货号
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        for (String sku : articleNumberList) {
            boolQuery.must(QueryBuilders.matchQuery("articleNumber", sku.toUpperCase()));
        }
        boolQueryBuilder.must(boolQuery);

        queryBuilder
                .withQuery(boolQueryBuilder)
                .withPageable(PageRequest.of(0, 10000))
                .withFields("articleNumber", "sellerSku", "accountNumber", "firstOpenDate", "offlineDate", "openDate")
                .withSort(SortBuilders.fieldSort("firstOpenDate").order(SortOrder.ASC))
                .withSort(SortBuilders.fieldSort("openDate").order(SortOrder.ASC))
                .build();
        NativeSearchQuery searchQuery = queryBuilder.build();
        searchQuery.setTrackTotalHits(true);
        Page<EsAmazonProductListing> search = esAmazonProductListingRepository.search(searchQuery);
        if (null != search && CollectionUtils.isNotEmpty(search.getContent())) {
            for (EsAmazonProductListing esAmazonProductListing : search.getContent()) {
                PlatformListingInfo platformListingInfo = new PlatformListingInfo();
                platformListingInfo.setPlatform("Amazon");
                platformListingInfo.setArticleNumber(esAmazonProductListing.getArticleNumber());
                platformListingInfo.setSellerSku(esAmazonProductListing.getSellerSku());
                platformListingInfo.setAccountNumber(esAmazonProductListing.getAccountNumber());
                Timestamp amazonEarliestTime = null;
                if (null != esAmazonProductListing.getFirstOpenDate()) {
                    amazonEarliestTime = new Timestamp(esAmazonProductListing.getFirstOpenDate().getTime());
                } else if (null != esAmazonProductListing.getOpenDate()) {
                    amazonEarliestTime = new Timestamp(esAmazonProductListing.getOpenDate().getTime());
                }
                platformListingInfo.setPublishDate(amazonEarliestTime);
                if (null != esAmazonProductListing.getOfflineDate()) {
                    platformListingInfo.setOfflineDate(new Timestamp(esAmazonProductListing.getOfflineDate().getTime()));
                }
                platformListingInfos.add(platformListingInfo);
            }
        }
        long timeES = System.currentTimeMillis() - now;
        log.info("/publishListing/getListingInfoBySku查询ES->amazon_product_listing,耗时->{}ms,sku数量->{}", timeES, articleNumberList.size());
        return platformListingInfos;
    }

    /**
     * 查询标题存不存在
     *
     * @param title
     * @return
     */
    @Override
    public int selectTitleExist(String title) {
        if (StringUtils.isNotBlank(title)) {
            NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termQuery("name", title.trim()));

            NativeSearchQuery searchQuery = queryBuilder
                    .withQuery(boolQueryBuilder)
                    .withPageable(PageRequest.of(0, 10))
                    .withFields("accountNumber", "articleNumber", "sellerSku", "itemName", "name")
                    .build();
            searchQuery.setTrackTotalHits(true);
            Page<EsAmazonProductListing> search = esAmazonProductListingRepository.search(searchQuery);
            if (search != null && CollectionUtils.isNotEmpty(search.getContent())) {
                return search.getContent().size();
            }
        }
        return 0;
    }

    @Override
    public boolean checkExistById(String id) {
        if (StringUtils.isNotBlank(id)) {
            NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termQuery("id", id));

            NativeSearchQuery searchQuery = queryBuilder
                    .withQuery(boolQueryBuilder)
                    .withPageable(PageRequest.of(0, 1))
                    .withFields("id")
                    .build();
            searchQuery.setTrackTotalHits(true);
            Page<EsAmazonProductListing> search = esAmazonProductListingRepository.search(searchQuery);
            if (search != null && CollectionUtils.isNotEmpty(search.getContent())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<EsAmazonProductListing> getEsAmazonProductListing(EsAmazonProductListingRequest esAmazonProductListingRequest) {
        if (esAmazonProductListingRequest == null) {
            return null;
        }
        List<EsAmazonProductListing> esAmazonProductListingList = new ArrayList<>();
        // 查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        // id
        String id = esAmazonProductListingRequest.getId();
        if (StringUtils.isNotEmpty(id)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("id", id));
        }
        List<String> idList = esAmazonProductListingRequest.getIdList();
        if (CollectionUtils.isNotEmpty(idList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("id", idList));
        }

        String attribute3 = esAmazonProductListingRequest.getAttribute3();
        if (null != attribute3) {
            boolQueryBuilder.must(QueryBuilders.matchQuery("attribute3", attribute3));
        }

        //站点
        String site = esAmazonProductListingRequest.getSite();
        if (StringUtils.isNotBlank(site)) {
            boolQueryBuilder.must(QueryBuilders.matchQuery("site", site));
        }
        List<String> siteList = esAmazonProductListingRequest.getSiteList();
        if (CollectionUtils.isNotEmpty(siteList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("site", siteList));
        }

        //亚马逊账号（部门、销售主管、销售）
        String accountNumber = esAmazonProductListingRequest.getAccountNumber();
        if (StringUtils.isNotBlank(accountNumber)) {
            boolQueryBuilder.must(QueryBuilders.matchQuery("accountNumber", accountNumber));
        }
        List<String> accountNumberList = esAmazonProductListingRequest.getAccountNumberList();
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber", accountNumberList));
        }

        // 主asin
        String parentAsin = esAmazonProductListingRequest.getParentAsin();
        if (StringUtils.isNotBlank(parentAsin)) {
            boolQueryBuilder.must(QueryBuilders.matchQuery("parentAsin", parentAsin));
        }
        List<String> parentAsinList = esAmazonProductListingRequest.getParentAsinList();
        if (CollectionUtils.isNotEmpty(parentAsinList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("parentAsin", parentAsinList));
        }

        // 子asin
        String sonAsin = esAmazonProductListingRequest.getSonAsin();
        if (StringUtils.isNotBlank(sonAsin)) {
            boolQueryBuilder.must(QueryBuilders.matchQuery("sonAsin", sonAsin));
        }
        List<String> sonAsinList = esAmazonProductListingRequest.getSonAsinList();
        if (CollectionUtils.isNotEmpty(sonAsinList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("sonAsin", sonAsinList));
        }

        // 主sku
        String mainSku = esAmazonProductListingRequest.getMainSku();
        if (StringUtils.isNotBlank(mainSku)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("mainSku", mainSku.toUpperCase()));
        }

        // articleNumber
        String articleNumber = esAmazonProductListingRequest.getArticleNumber();
        if (StringUtils.isNotBlank(articleNumber)) {
            boolQueryBuilder.must(QueryBuilders.matchQuery("articleNumber", articleNumber.toUpperCase()));
        }
        List<String> articleNumberList = esAmazonProductListingRequest.getArticleNumberList();
        if (CollectionUtils.isNotEmpty(articleNumberList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("articleNumber", articleNumberList));
        }

        //sellerSku
        String sellerSku = esAmazonProductListingRequest.getSellerSku();
        if (StringUtils.isNotBlank(sellerSku)) {
            boolQueryBuilder.must(QueryBuilders.matchQuery("attribute2", sellerSku.toUpperCase()));
        }
        List<String> sellerSkuNumberList = esAmazonProductListingRequest.getSellerSkuList();
        if (CollectionUtils.isNotEmpty(sellerSkuNumberList)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String sSku : sellerSkuNumberList) {
                boolQuery.should(QueryBuilders.matchQuery("attribute2", sSku.toUpperCase()));
            }
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        // 需要排除的sellerSku
        List<String> notContainSellerSkuList = esAmazonProductListingRequest.getNotContainSellerSkuList();
        if (CollectionUtils.isNotEmpty(notContainSellerSkuList) && notContainSellerSkuList.size() > 0) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQueryBuilder.must(boolQuery.mustNot(QueryBuilders.termsQuery("sellerSku", notContainSellerSkuList)));
        }

        // 刊登来源（数据来源）
        Integer skuDataSource = esAmazonProductListingRequest.getSkuDataSource();
        if (!ObjectUtils.isEmpty(skuDataSource)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("skuDataSource", skuDataSource));
        }

        // 是否在线
        Boolean isOnline = esAmazonProductListingRequest.getIsOnline();
        if (!ObjectUtils.isEmpty(isOnline) && BooleanUtils.isTrue(isOnline)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("isOnline", true));
        } else if (!ObjectUtils.isEmpty(isOnline)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("isOnline", false));
        }

        String itemStatus = esAmazonProductListingRequest.getItemStatus();
        if (StringUtils.isNotEmpty(itemStatus)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("itemStatus", itemStatus));
        }

        List<Integer> excludeItemTypeList = esAmazonProductListingRequest.getExcludeItemTypeList();
        if (CollectionUtils.isNotEmpty(excludeItemTypeList)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("itemType", excludeItemTypeList));
        }

        //产品标签
        String tagCode = esAmazonProductListingRequest.getTagCode();
        if (StringUtils.isNotEmpty(tagCode)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("tagCodes",tagCode));
        }


        // 是否存在运费模板
        Boolean isExistShippingGroup = esAmazonProductListingRequest.getIsExistShippingGroup();
        if (!ObjectUtils.isEmpty(isExistShippingGroup) && BooleanUtils.isTrue(isExistShippingGroup)) {
            boolQueryBuilder.must(QueryBuilders.existsQuery("merchantShippingGroup"));
        } else if (!ObjectUtils.isEmpty(isExistShippingGroup)) {
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("merchantShippingGroup"));
        }

        //禁售平台
        String forbidChannel = esAmazonProductListingRequest.getForbidChannel();
        if (StringUtils.isNotEmpty(forbidChannel)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("forbidChannel", "*," + forbidChannel + ",*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }
        List<String> forbidChannelList = esAmazonProductListingRequest.getForbidChannelList();
        if (CollectionUtils.isNotEmpty(forbidChannelList)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String bidChannel : forbidChannelList) {
                boolQuery.should(QueryBuilders.wildcardQuery("forbidChannel", "*" + bidChannel + "*"));
            }
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }
        //禁售类型
        String infringementTypename = esAmazonProductListingRequest.getInfringementTypename();
        if (StringUtils.isNotBlank(infringementTypename)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("infringementTypename", "*|" + infringementTypename + "|*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }
        List<String> infringementTypenames = esAmazonProductListingRequest.getInfringementTypenames();
        if (CollectionUtils.isNotEmpty(infringementTypenames)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String s : infringementTypenames) {
                boolQuery.should(QueryBuilders.wildcardQuery("infringementTypename", "*|" + s + "|*"));
            }
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }
        //禁售原因
        String infringementObj = esAmazonProductListingRequest.getInfringementObj();
        if (StringUtils.isNotBlank(infringementObj)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("infringementObj", "*|" + infringementObj + "|*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }
        List<String> infringementObjs = esAmazonProductListingRequest.getInfringementObjs();
        if (CollectionUtils.isNotEmpty(infringementObjs)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String s : infringementObjs) {
                boolQuery.should(QueryBuilders.wildcardQuery("infringementObj", "*|" + s + "|*"));
            }
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }
        // 单品状态
        String skuStatus = esAmazonProductListingRequest.getSkuStatus();
        if (StringUtils.isNotBlank(skuStatus)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("skuStatus", skuStatus));
        }
        List<String> skuStatusList = esAmazonProductListingRequest.getSkuStatusList();
        if (CollectionUtils.isNotEmpty(skuStatusList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("skuStatus", skuStatusList));
        }

        List<String> notInSkuStatusList = esAmazonProductListingRequest.getNotInSkuStatusList();
        if (CollectionUtils.isNotEmpty(notInSkuStatusList)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("skuStatus", notInSkuStatusList));
        }

        //数量
        if (esAmazonProductListingRequest.getQuantityGt() != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("quantity").gt(esAmazonProductListingRequest.getQuantityGt()));
        }
        if (esAmazonProductListingRequest.getQuantityLt() != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("quantity").lt(esAmazonProductListingRequest.getQuantityLt()));
        }
        //最新上架时间
        String endOpenDate = esAmazonProductListingRequest.getEndOpenDate();
        if (!ObjectUtils.isEmpty(endOpenDate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("openDate").lte(endOpenDate));
        }

        //第一次上架时间
        String startFirstOpenDate = esAmazonProductListingRequest.getStartFirstOpenDate();
        if (!ObjectUtils.isEmpty(startFirstOpenDate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("firstOpenDate").gte(startFirstOpenDate));
        }
        String endFirstOpenDate = esAmazonProductListingRequest.getEndFirstOpenDate();
        if (!ObjectUtils.isEmpty(endFirstOpenDate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("firstOpenDate").to(endFirstOpenDate));
        }

//        // 销量范围,此处取了等于
//        Long fromSaleQuantity = esAmazonProductListingRequest.getFromSaleQuantity();
//        Long toSaleQuantity = esAmazonProductListingRequest.getToSaleQuantity();
//        String saleQuantityBean = esAmazonProductListingRequest.getSaleQuantityBean();
//        if ((!ObjectUtils.isEmpty(fromSaleQuantity) || !ObjectUtils.isEmpty(toSaleQuantity)) && StringUtils.isNotBlank(saleQuantityBean)) {
//            if (!ObjectUtils.isEmpty(fromSaleQuantity)) {
//                boolQueryBuilder.must(QueryBuilders.rangeQuery(saleQuantityBean).gte(fromSaleQuantity));
//            }
//            if (!ObjectUtils.isEmpty(toSaleQuantity)) {
//                boolQueryBuilder.must(QueryBuilders.rangeQuery(saleQuantityBean).lte(toSaleQuantity));
//            }
//        }

        // 销量范围
        Long fromSaleQuantity = esAmazonProductListingRequest.getFromSaleQuantity();
        Long toSaleQuantity = esAmazonProductListingRequest.getToSaleQuantity();
        String saleQuantityBean = esAmazonProductListingRequest.getSaleQuantityBean();
        Boolean isSaleQuantityNull = esAmazonProductListingRequest.getIsSaleQuantityNull();
        if ((!ObjectUtils.isEmpty(fromSaleQuantity) || !ObjectUtils.isEmpty(toSaleQuantity) || isSaleQuantityNull != null) && StringUtils.isNotBlank(saleQuantityBean)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            if (!ObjectUtils.isEmpty(fromSaleQuantity) || !ObjectUtils.isEmpty(toSaleQuantity)) {
                BoolQueryBuilder bool = QueryBuilders.boolQuery();
                if (!ObjectUtils.isEmpty(fromSaleQuantity)) {
                    bool.must(QueryBuilders.rangeQuery(saleQuantityBean).from(fromSaleQuantity));
                }
                if (!ObjectUtils.isEmpty(toSaleQuantity)) {
                    bool.must(QueryBuilders.rangeQuery(saleQuantityBean).to(toSaleQuantity));
                }
                boolQuery.should(bool);
            }

            // 总销量为空
            String order_num_total = "order_num_total";
            if (order_num_total.equals(saleQuantityBean) && isSaleQuantityNull != null && isSaleQuantityNull) {
                BoolQueryBuilder bool = QueryBuilders.boolQuery();
                bool.mustNot(QueryBuilders.existsQuery(saleQuantityBean));
                boolQuery.should(bool);
            }
            // 30天销量为空
            String order_last_30d_count = "order_last_30d_count";
            if (order_last_30d_count.equals(saleQuantityBean) && isSaleQuantityNull != null && isSaleQuantityNull) {
                BoolQueryBuilder bool = QueryBuilders.boolQuery();
                bool.mustNot(QueryBuilders.existsQuery(saleQuantityBean));
                boolQuery.should(bool);
            }
            // 14天销量为空
            String order_last_14d_count = "order_last_14d_count";
            if (order_last_14d_count.equals(saleQuantityBean) && isSaleQuantityNull != null && isSaleQuantityNull) {
                BoolQueryBuilder bool = QueryBuilders.boolQuery();
                boolQuery.should(bool);
            }
            // 7天销量为空
            String order_last_7d_count = "order_last_7d_count";
            if (order_last_7d_count.equals(saleQuantityBean) && isSaleQuantityNull != null && isSaleQuantityNull) {
                BoolQueryBuilder bool = QueryBuilders.boolQuery();
                boolQuery.should(bool);
            }
            // 24小时销量为空
            String order_last_24h_count = "order_last_24h_count";
            if (order_last_24h_count.equals(saleQuantityBean) && isSaleQuantityNull != null && isSaleQuantityNull) {
                BoolQueryBuilder bool = QueryBuilders.boolQuery();
                boolQuery.should(bool);
            }
            boolQueryBuilder.must(boolQuery);
        }

        //sku类目（产品系统类目）
        String categoryId = esAmazonProductListingRequest.getCategoryId();
        if (StringUtils.isNotEmpty(categoryId)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("categoryId", "*," + categoryId + ",*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

//        // 是否总销量为空
//        if (!ObjectUtils.isEmpty(esAmazonProductListingRequest.getIsSaleQuantityNull()) && BooleanUtils.isTrue(esAmazonProductListingRequest.getIsSaleQuantityNull())) {
//            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("order_num_total"));
//        }

        // 是否存在父asin
        Boolean isExistParentAsion = esAmazonProductListingRequest.getIsExistParentAsion();
        if (!ObjectUtils.isEmpty(isExistParentAsion) && BooleanUtils.isTrue(isExistParentAsion)) {
            boolQueryBuilder.must(QueryBuilders.existsQuery("parentAsin"));
        } else if (!ObjectUtils.isEmpty(isExistParentAsion)) {
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("parentAsin"));
        }

        // 是否存在品牌
        Boolean isExistBrandName = esAmazonProductListingRequest.getIsExistBrandName();
        if (!ObjectUtils.isEmpty(isExistBrandName) && BooleanUtils.isTrue(isExistBrandName)) {
            boolQueryBuilder.must(QueryBuilders.existsQuery("brandName"));
        } else if (!ObjectUtils.isEmpty(isExistBrandName)) {
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("brandName"));
        }
        // 品牌
        String notBrandName = esAmazonProductListingRequest.getNotBrandName();
        if (StringUtils.isNotBlank(notBrandName)) {
            boolQueryBuilder.mustNot(QueryBuilders.termQuery("brandName",notBrandName));
        }

        // 品牌,为 null 也会查出
        List<String> notBrandNameList = esAmazonProductListingRequest.getNotBrandNameList();
        if (CollectionUtils.isNotEmpty(notBrandNameList)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQueryBuilder.must(boolQuery.mustNot(QueryBuilders.termsQuery("brandName", notBrandNameList)));
        }

        // 是否存在利润 grossProfit
        Boolean isExistGrossProfit = esAmazonProductListingRequest.getIsExistGrossProfit();
        if (!ObjectUtils.isEmpty(isExistGrossProfit) && BooleanUtils.isTrue(isExistGrossProfit)) {
            boolQueryBuilder.must(QueryBuilders.existsQuery("grossProfit"));
        } else if (!ObjectUtils.isEmpty(isExistGrossProfit)) {
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("grossProfit"));
        }

        // 是否存在item的类型
        Boolean isExistItemType = esAmazonProductListingRequest.getIsExistItemType();
        if (!ObjectUtils.isEmpty(isExistItemType) && BooleanUtils.isTrue(isExistItemType)) {
            boolQueryBuilder.must(QueryBuilders.existsQuery("itemType"));
        } else if (!ObjectUtils.isEmpty(isExistItemType)) {
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("itemType"));
        }
        Integer itemType = esAmazonProductListingRequest.getItemType();
        if (!ObjectUtils.isEmpty(itemType)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("itemType", itemType));
        }
        List<Integer> itemTypeList = esAmazonProductListingRequest.getItemTypeList();
        if (CollectionUtils.isNotEmpty(itemTypeList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("itemType", itemTypeList));
        }

        String[] fields = esAmazonProductListingRequest.getFields();
        queryBuilder.withQuery(boolQueryBuilder)
                .withFields(fields)
                .withSort(SortBuilders.fieldSort("createDate").order(SortOrder.ASC));
        //创建查询条件构造器
        NativeSearchQuery searchQuery = queryBuilder.withPageable(PageRequest.of(0, 1000)).build();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        searchQuery.setTrackTotalHits(true);
        esAmazonProductListingList = ElasticSearchHelper.
                scrollQuery(elasticsearchRestTemplate1, 10 * 60 * 1000,
                        searchQuery, EsAmazonProductListing.class,
                        amazonProductListingIndexCoordinates);
        stopWatch.stop();
        long totalTimeMillis = stopWatch.getTotalTimeMillis();
        if (totalTimeMillis > 3000L) {
            log.warn("查询ES->esAmazonProductListing 条数{}耗时{}ms", esAmazonProductListingList.size(), totalTimeMillis);
        }
        return esAmazonProductListingList;
    }

    @Override
    public Map<String, Integer> getAccountSellerSkuSumMap(EsAmazonProductListingRequest esAmazonProductListingRequest) {
        if (esAmazonProductListingRequest == null) {
            return null;
        }
        long now = System.currentTimeMillis();
        Map<String, Integer> accountSellerSkuSumMap = new HashMap<>();
        try {
            // 查询条件
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            // 是否在线
            Boolean isOnline = esAmazonProductListingRequest.getIsOnline();
            if (null != isOnline) {
                if (isOnline == true) {
                    boolQueryBuilder.must(QueryBuilders.matchQuery("isOnline", true));
                } else {
                    boolQueryBuilder.must(QueryBuilders.matchQuery("isOnline", false));
                }
            }

            //最新上架时间
            String startOpenDate = esAmazonProductListingRequest.getStartOpenDate();
            if (!ObjectUtils.isEmpty(startOpenDate)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("openDate").gte(startOpenDate));
            }
            String endOpenDate = esAmazonProductListingRequest.getEndOpenDate();
            if (!ObjectUtils.isEmpty(endOpenDate)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("openDate").lte(endOpenDate));
            }

            // 账号
            List<String> accountNumberList = esAmazonProductListingRequest.getAccountNumberList();
            if (CollectionUtils.isNotEmpty(accountNumberList)) {
                boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber", accountNumberList));
            }

            //按账号分组
            CardinalityAggregationBuilder sellerSkuNumBuilder = AggregationBuilders.cardinality("sellerSkuNum").field("sellerSku");
            TermsAggregationBuilder accountBuilder = AggregationBuilders.terms("articleNumberGroup").field("accountNumber").subAggregation(sellerSkuNumBuilder).size((1 << 31) - 1);
            //sum

            NativeSearchQuery query = new NativeSearchQueryBuilder()
                    .withQuery(boolQueryBuilder)
                    .withFields("accountNumber", "sellerSku")
                    .addAggregation(accountBuilder)
                    .withPageable(PageRequest.of(0, 1))
                    .build();
            query.setTrackTotalHits(true);
            AggregatedPage<EsAmazonProductListing> search = (AggregatedPage) esAmazonProductListingRepository.search(query);
            if (null != search) {
                Map<String, Aggregation> asMap = search.getAggregations().getAsMap();
                MultiBucketsAggregation term = (MultiBucketsAggregation) asMap.get("articleNumberGroup");
                if (null != term) {

                    for (MultiBucketsAggregation.Bucket bucket : term.getBuckets()) {
                        String key = (String) bucket.getKey();
                        if (key == null || StringUtils.isBlank(key.toString())) continue;
                        long docCount = bucket.getDocCount();
                        int count = new Long(docCount).intValue();
                        accountSellerSkuSumMap.put(key, count);
                    }
                }
            }
            long timeES = System.currentTimeMillis() - now;
            log.info("获取条件范围内账号对应listing数量，查询ES->esAmazonProductListing,耗时->{}ms", timeES);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取条件范围内账号对应listing数量 es 查询失败！");
        }
        return accountSellerSkuSumMap;
    }

    @Override
    public List<String> getAccountNumberListByRequest(EsAmazonProductListingRequest esAmazonProductListingRequest) {
        if (esAmazonProductListingRequest == null) {
            return null;
        }
        long now = System.currentTimeMillis();
        List<String> accountNumberList = new ArrayList<>();
        try {
            // 查询条件
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            // 是否在线
            Boolean isOnline = esAmazonProductListingRequest.getIsOnline();
            if (null != isOnline) {
                if (isOnline == true) {
                    boolQueryBuilder.must(QueryBuilders.matchQuery("isOnline", true));
                } else {
                    boolQueryBuilder.must(QueryBuilders.matchQuery("isOnline", false));
                }
            }

            String attribute3 = esAmazonProductListingRequest.getAttribute3();
            if (null != attribute3) {
                    boolQueryBuilder.must(QueryBuilders.matchQuery("attribute3", attribute3));
            }

            Integer ItemType = esAmazonProductListingRequest.getItemType();
            if (null != ItemType) {
                boolQueryBuilder.must(QueryBuilders.termQuery("itemType", ItemType));
            }

            //站点
            String site = esAmazonProductListingRequest.getSite();
            if (StringUtils.isNotBlank(site)) {
                boolQueryBuilder.must(QueryBuilders.matchQuery("site", site));
            }
            List<String> siteList = esAmazonProductListingRequest.getSiteList();
            if (CollectionUtils.isNotEmpty(siteList)) {
                boolQueryBuilder.must(QueryBuilders.termsQuery("site", siteList));
            }


            //禁售平台
            String forbidChannel = esAmazonProductListingRequest.getForbidChannel();
            if (StringUtils.isNotEmpty(forbidChannel)) {
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                boolQuery.should(QueryBuilders.wildcardQuery("forbidChannel", "*," + forbidChannel + ",*"));
                boolQuery.minimumShouldMatch(1);
                boolQueryBuilder.must(boolQuery);
            }
            List<String> forbidChannelList = esAmazonProductListingRequest.getForbidChannelList();
            if (CollectionUtils.isNotEmpty(forbidChannelList)) {
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                for (String bidChannel : forbidChannelList) {
                    boolQuery.should(QueryBuilders.wildcardQuery("forbidChannel", "*," + bidChannel + ",*"));
                }
                boolQuery.minimumShouldMatch(1);
                boolQueryBuilder.must(boolQuery);
            }
            //禁售类型
            String infringementTypename = esAmazonProductListingRequest.getInfringementTypename();
            if (StringUtils.isNotBlank(infringementTypename)) {
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                boolQuery.should(QueryBuilders.wildcardQuery("infringementTypename", "*|" + infringementTypename + "|*"));
                boolQuery.minimumShouldMatch(1);
                boolQueryBuilder.must(boolQuery);
            }
            List<String> infringementTypenames = esAmazonProductListingRequest.getInfringementTypenames();
            if (CollectionUtils.isNotEmpty(infringementTypenames)) {
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                for (String s : infringementTypenames) {
                    boolQuery.should(QueryBuilders.wildcardQuery("infringementTypename", "*|" + s + "|*"));
                }
                boolQuery.minimumShouldMatch(1);
                boolQueryBuilder.must(boolQuery);
            }

            // 销量范围,此处取了等于
            Long fromSaleQuantity = esAmazonProductListingRequest.getFromSaleQuantity();
            Long toSaleQuantity = esAmazonProductListingRequest.getToSaleQuantity();
            String saleQuantityBean = esAmazonProductListingRequest.getSaleQuantityBean();
            if ((!ObjectUtils.isEmpty(fromSaleQuantity) || !ObjectUtils.isEmpty(toSaleQuantity)) && StringUtils.isNotBlank(saleQuantityBean)) {
                if (!ObjectUtils.isEmpty(fromSaleQuantity)) {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery(saleQuantityBean).gte(fromSaleQuantity));
                }
                if (!ObjectUtils.isEmpty(toSaleQuantity)) {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery(saleQuantityBean).lte(toSaleQuantity));
                }
            }

            //数量
            if (esAmazonProductListingRequest.getQuantityGt() != null) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("quantity").gt(esAmazonProductListingRequest.getQuantityGt()));
            }
            if (esAmazonProductListingRequest.getQuantityLt() != null) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("quantity").lt(esAmazonProductListingRequest.getQuantityLt()));
            }

            //禁售原因
            String infringementObj = esAmazonProductListingRequest.getInfringementObj();
            if (StringUtils.isNotBlank(infringementObj)) {
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                boolQuery.should(QueryBuilders.wildcardQuery("infringementObj", "*|" + infringementObj + "|*"));
                boolQuery.minimumShouldMatch(1);
                boolQueryBuilder.must(boolQuery);
            }
            List<String> infringementObjs = esAmazonProductListingRequest.getInfringementObjs();
            if (CollectionUtils.isNotEmpty(infringementObjs)) {
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                for (String s : infringementObjs) {
                    boolQuery.should(QueryBuilders.wildcardQuery("infringementObj", "*|" + s + "|*"));
                }
                boolQuery.minimumShouldMatch(1);
                boolQueryBuilder.must(boolQuery);
            }
            //最新上架时间
            String startOpenDate = esAmazonProductListingRequest.getStartOpenDate();
            if (!ObjectUtils.isEmpty(startOpenDate)) {
                boolQueryBuilder.filter(QueryBuilders.rangeQuery("openDate").gte(startOpenDate));
            }
            String endOpenDate = esAmazonProductListingRequest.getEndOpenDate();
            if (!ObjectUtils.isEmpty(endOpenDate)) {
                boolQueryBuilder.filter(QueryBuilders.rangeQuery("openDate").lte(endOpenDate));
            }

            // 同步时间
            String startSyncDate = esAmazonProductListingRequest.getStartSyncDate();
            if (!ObjectUtils.isEmpty(startSyncDate)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("syncDate").gte(startSyncDate));
            }
            String endSyncDate = esAmazonProductListingRequest.getEndSyncDate();
            if (!ObjectUtils.isEmpty(endSyncDate)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("syncDate").lte(endSyncDate));
            }

            //按账号分组
            String[] fields = {"accountNumber"};
            TermsAggregationBuilder accountBuilder = AggregationBuilders.terms("articleNumber_aggs").field("accountNumber").size((1 << 31) - 1)
                    .subAggregation(AggregationBuilders.topHits("top").fetchSource(fields, Strings.EMPTY_ARRAY).size(1));

            NativeSearchQuery query = new NativeSearchQueryBuilder()
                    .withQuery(boolQueryBuilder)
                    .withFields(fields)
                    .addAggregation(accountBuilder)
                    .withPageable(PageRequest.of(0, 1))
                    .build();
            query.setTrackTotalHits(true);
            AggregatedPage<EsAmazonProductListing> search = (AggregatedPage) esAmazonProductListingRepository.search(query);
            if (null != search) {
                Map<String, Aggregation> asMap = search.getAggregations().getAsMap();
                MultiBucketsAggregation term = (MultiBucketsAggregation) asMap.get("articleNumber_aggs");
                if (null != term) {
                    for (MultiBucketsAggregation.Bucket bucket : term.getBuckets()) {
                        accountNumberList.add(bucket.getKeyAsString());
                    }
                }
            }
            long timeES = System.currentTimeMillis() - now;
            log.info("获取条件范围内账号对应listing数量，查询ES->esAmazonProductListing,耗时->{}ms", timeES);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取条件范围内账号对应listing数量 es 查询失败！");
        }
        return accountNumberList;
    }

    @Override
    public Map<String, String> getMainSkuMapBySonSkuList(EsAmazonProductListingRequest request) {
        Map<String, String> dataMap = Maps.newHashMap();
        if (request == null) {
            return dataMap;
        }

        try {
            // 查询条件
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            if (CollectionUtils.isNotEmpty(request.getArticleNumberList())) {
                boolQueryBuilder.must(QueryBuilders.termsQuery("articleNumber", request.getArticleNumberList()));
            }
            NativeSearchQuery query = new NativeSearchQueryBuilder()
                    .withQuery(boolQueryBuilder)
                    .withFields("articleNumber", "mainSku")
                    .build();
            query.setTrackTotalHits(true);
            AggregatedPage<EsAmazonProductListing> search = (AggregatedPage) esAmazonProductListingRepository.search(query);
            if (null != search) {
                search.getContent().forEach(product -> {
                    if (StringUtils.isNotBlank(product.getArticleNumber()) && StringUtils.isNotBlank(product.getMainSku())) {
                        dataMap.put(product.getArticleNumber(), product.getMainSku());
                    }
                });
            }
        } catch (Exception e) {
            log.error("根据货号查询主sku失败", e);
        }
        return dataMap;
    }

    @Override
    public Long getRangeTimeAddListingTotal(String accountNumber, String starTime, String endTime) {
        if (StringUtils.isBlank(accountNumber) || StringUtils.isBlank(starTime) || StringUtils.isBlank(endTime)) {
            return 0L;
        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("accountNumber", accountNumber));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("openDate").gt(starTime).lt(endTime));

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .build();
        return elasticsearchRestTemplate1.count(query, EsAmazonProductListing.class);
    }

    /**
     * 获取店铺侵权禁售链接数
     *
     * @param accountNumberList 店铺
     */
    @Override
    public Long getForbiddenListingNum(List<String> accountNumberList) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber", accountNumberList));
        boolQueryBuilder.must(QueryBuilders.termQuery("isOnline", true));
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.should(QueryBuilders.wildcardQuery("forbidChannel", "*," + SaleChannelEnum.AMAZON.getChannelName() + ",*"));
        boolQuery.minimumShouldMatch(1);
        boolQueryBuilder.must(boolQuery);

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .build();
        return elasticsearchRestTemplate1.count(query, EsAmazonProductListing.class);
    }

    /**
     * 获取店铺不及格毛利链接数
     *
     * @param accountNumberList 店铺
     * @param grossThreshold    毛利率阈值
     */
    @Override
    public Long getSubGrossProfitListingNum(List<String> accountNumberList, Double grossThreshold) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber", accountNumberList));
        boolQueryBuilder.must(QueryBuilders.termQuery("isOnline", true));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("grossProfitRate").lt(grossThreshold));

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .build();
        return elasticsearchRestTemplate1.count(query, EsAmazonProductListing.class);
    }

    /**
     * 获取店铺包含侵权词链接
     *
     * @param accountNumberList 店铺
     */
    @Override
    public Long getInfringementWordListingNum(List<String> accountNumberList) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber", accountNumberList));
        boolQueryBuilder.must(QueryBuilders.existsQuery("infringingBrandWord"));
        boolQueryBuilder.must(QueryBuilders.termQuery("isOnline", true));

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .build();
        return elasticsearchRestTemplate1.count(query, EsAmazonProductListing.class);
    }

    /**
     * 获取停产存档库存不为0链接
     *
     * @param accountNumberList 店铺
     */
    @Override
    public Long getStopStatusListingNum(List<String> accountNumberList) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber", accountNumberList));
        boolQueryBuilder.must(QueryBuilders.termsQuery("skuStatus", Arrays.asList(SkuStatusEnum.ARCHIVED.getCode(), SkuStatusEnum.STOP.getCode())));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("quantity").gt(0));
        boolQueryBuilder.must(QueryBuilders.termQuery("isOnline", true));

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .build();
        return elasticsearchRestTemplate1.count(query, EsAmazonProductListing.class);
    }

    /**
     * 获取店铺正常状态库存不足在线链接数
     *
     * @param accountNumberList 店铺
     * @param stockThreshold    库存阈值
     */
    @Override
    public Long getNotEnoughStockListingNum(List<String> accountNumberList, Integer stockThreshold) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber", accountNumberList));
        boolQueryBuilder.must(QueryBuilders.termQuery("skuStatus", SkuStatusEnum.NORMAL.getCode()));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("quantity").lt(stockThreshold));
        boolQueryBuilder.must(QueryBuilders.termQuery("isOnline", true));

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .build();
        return elasticsearchRestTemplate1.count(query, EsAmazonProductListing.class);
    }

    /**
     * 获取店铺在线链接总数
     *
     * @param accountNumberList 店铺
     */
    @Override
    public Long getOnlineListingNum(List<String> accountNumberList) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("accountNumber", accountNumberList));
        boolQueryBuilder.must(QueryBuilders.termQuery("isOnline", true));

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .build();
        return elasticsearchRestTemplate1.count(query, EsAmazonProductListing.class);
    }

    @Override
    public Map<String, Integer> getListingSalesTotalCount(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("id", ids));
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withFields("id", "order_num_total")
                .build();

        List<EsAmazonProductListing> amazonProductListing = ElasticSearchHelper.scrollQuery(elasticsearchRestTemplate1,
                10 * 60 * 1000, query, EsAmazonProductListing.class, IndexCoordinates.of("amazon_product_listing"));
        return amazonProductListing.stream()
                .collect(Collectors.toMap(EsAmazonProductListing::getId, listing -> Optional.ofNullable(listing.getOrder_num_total()).orElse(0)));
    }

    @Override
    public List<String> getSonAsinList(List<String> parentAsinList) {
        if (CollectionUtils.isEmpty(parentAsinList)) {
            return new ArrayList<>();
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("parentAsin", parentAsinList));
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withFields("sonAsin")
                .build();

        List<EsAmazonProductListing> amazonProductListing = ElasticSearchHelper.scrollQuery(elasticsearchRestTemplate1,
                600000, query, EsAmazonProductListing.class, IndexCoordinates.of("amazon_product_listing"));
        return amazonProductListing.stream().map(EsAmazonProductListing::getSonAsin).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
    }

    @Override
    public EsCompositeAggsResponse<String> statisticsNoSaleOnlineSku(String searchAfter, List<String> sites) {
        EsCompositeAggsResponse<String> compositeAggsResponse = new EsCompositeAggsResponse<>();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termQuery("isOnline", true));
//        boolQuery.filter(QueryBuilders.termsQuery("site", sites));
        boolQuery.filter(QueryBuilders.termQuery("itemStatus","Active"));
        boolQuery.mustNot(QueryBuilders.termQuery("itemType",2));
        boolQuery.mustNot(QueryBuilders.termQuery("site", "US"));
        boolQuery.mustNot(QueryBuilders.termsQuery("articleNumber", "匹配不到货号", ""));
        CompositeAggregationBuilder compositeBuilder = new CompositeAggregationBuilder(
                "skuAgg",
                Collections.singletonList(new TermsValuesSourceBuilder("articleNumber").field("articleNumber").order("desc"))
        ).size(10000);
        if (StringUtils.isNotBlank(searchAfter)) {
            compositeBuilder.aggregateAfter(ImmutableMap.of("articleNumber", searchAfter));
        }
        searchSourceBuilder
                .query(boolQuery)
                .aggregation(compositeBuilder)
                .size(0);

        SearchRequest searchRequest = new SearchRequest(amazonProductListingIndexCoordinates.getIndexName());
        searchRequest.source(searchSourceBuilder);
        try {
            SearchResponse search = restHighLevelClient1.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations aggregations = search.getAggregations();
            if (aggregations == null) {
                return compositeAggsResponse;
            }
            CompositeAggregation skuAgg = aggregations.get("skuAgg");
            if (skuAgg == null) {
                return compositeAggsResponse;
            }

            List<String> articleNumbers = skuAgg.getBuckets().stream().map(bucket -> {
                Map<String, Object> dataMap = bucket.getKey();
                Object articleNumber = dataMap.get("articleNumber");
                if (articleNumber == null) {
                    return null;
                }
                return (String) articleNumber;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            if (skuAgg.afterKey() != null) {
                String afterKey = (String) skuAgg.afterKey().get("articleNumber");
                compositeAggsResponse.setSearchAfter(afterKey);
            }
            compositeAggsResponse.setDataList(articleNumbers);
        } catch (IOException | RuntimeException e) {
            log.error("search fail, query:{}, e:{}", searchSourceBuilder, e.getMessage(), e);
        }
        return compositeAggsResponse;
    }

    @Override
    public boolean countListingExistInfringementWord(String word, String type) {
        if (StringUtils.isNotBlank(word) && StringUtils.isNotBlank(type)) {
            NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.filter(QueryBuilders.termQuery("isOnline", true));
            BoolQueryBuilder infringementWordBool = QueryBuilders.boolQuery();
            infringementWordBool.must(QueryBuilders.termQuery("infringementWordInfos.originWord", StringUtils.lowerCase(word)));
            infringementWordBool.must(QueryBuilders.termQuery("infringementWordInfos.type", type));
            NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("infringementWordInfos", infringementWordBool, ScoreMode.Total);
            boolQuery.filter(nestedQuery);

            NativeSearchQuery searchQuery = queryBuilder
                    .withQuery(boolQuery)
                    .withPageable(PageRequest.of(0, 1))
                    .withFields("id")
                    .build();
            searchQuery.setTrackTotalHits(true);
            Page<EsAmazonProductListing> search = esAmazonProductListingRepository.search(searchQuery);
            return CollectionUtils.isNotEmpty(search.getContent());
        }
        return false;
    }

    @Override
    public Map<String, Integer> statisticsInfringingWordSiteCount(String word, String type) throws IOException {
        if (StringUtils.isBlank(word)) {
            return Collections.emptyMap();
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termQuery("isOnline", true));
        BoolQueryBuilder infringementWordBool = QueryBuilders.boolQuery();
        infringementWordBool.must(QueryBuilders.termQuery("infringementWordInfos.originWord", StringUtils.lowerCase(word)));
        infringementWordBool.must(QueryBuilders.termQuery("infringementWordInfos.type", type));
        NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("infringementWordInfos", infringementWordBool, ScoreMode.Total);
        boolQuery.filter(nestedQuery);

        CompositeAggregationBuilder compositeBuilder = new CompositeAggregationBuilder(
                "infringingWordAgg",
                Collections.singletonList(new TermsValuesSourceBuilder("site").field("site").order("desc"))
        ).size(20);
        searchSourceBuilder
                .query(boolQuery)
                .aggregation(compositeBuilder)
                .size(0);

        SearchRequest searchRequest = new SearchRequest(amazonProductListingIndexCoordinates.getIndexName());
        searchRequest.source(searchSourceBuilder);
        SearchResponse search = restHighLevelClient1.search(searchRequest, RequestOptions.DEFAULT);
        Aggregations aggregations = search.getAggregations();
        if (aggregations == null) {
            return Collections.emptyMap();
        }
        CompositeAggregation agg = aggregations.get("infringingWordAgg");
        if (agg == null) {
            return Collections.emptyMap();
        }

        Map<String, Integer> returnMap = new HashMap<>();
        for (CompositeAggregation.Bucket bucket : agg.getBuckets()) {
            Map<String, Object> dataMap = bucket.getKey();
            Object site = dataMap.get("site");
            if (site == null) {
                continue;
            }
            long docCount = bucket.getDocCount();
            returnMap.put(site + "count", (int) docCount);
        }

        return returnMap;
    }

    @Override
    public List<EsAmazonProductListing> getListingByOnSaleSkus(List<String> skus, List<String> excludeSite, List<String> includeSites) {
        if (CollectionUtils.isEmpty(skus)) {
            return new ArrayList<>();
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("articleNumber", skus));
        boolQueryBuilder.filter(QueryBuilders.termQuery("isOnline", true));
        if (CollectionUtils.isNotEmpty(includeSites)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("site", includeSites));
        }
        if (CollectionUtils.isNotEmpty(excludeSite)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("site", excludeSite));
        }
        boolQueryBuilder.filter(QueryBuilders.termsQuery("itemType", List.of(1, 3)));
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withFields("id", "accountNumber", "site", "parentAsin", "sonAsin", "sellerSku", "mainSku", "articleNumber",
                        "itemStatus", "isOnline", "itemType", "skuStatus", "skuDataSource", "categoryId", "order_num_total", "normalSale", "openDate", "firstOpenDate")
                .build();

        return ElasticSearchHelper.scrollQuery(elasticsearchRestTemplate1,
                10 * 60 * 1000, query, EsAmazonProductListing.class, amazonProductListingIndexCoordinates);
    }

    @Override
    public List<EsAmazonProductListing> getListingByOnSaleArticleNumber(String articleNumber) {
        if (StringUtils.isBlank(articleNumber)) {
            return new ArrayList<>();
        }
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("articleNumber", articleNumber));
        boolQueryBuilder.filter(QueryBuilders.termQuery("isOnline", true));
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withFields("accountNumber", "site", "sellerSku", "articleNumber", "isOnline", "openDate", "sonAsin", "order_num_total", "firstOpenDate", "site")
                .build();

        return ElasticSearchHelper.scrollQuery(elasticsearchRestTemplate1,
                10 * 60 * 1000, query, EsAmazonProductListing.class, amazonProductListingIndexCoordinates);
    }

    @Override
    public Long getOnlineParentAsinListingNum(List<String> accountNumber) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("accountNumber", accountNumber));
        boolQueryBuilder.filter(QueryBuilders.termQuery("isOnline", true));

        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withPageable(PageRequest.of(0, 1))
                .addAggregation(AggregationBuilders.cardinality("distinct_parent_asin").field("parentAsin"))
                .build();
        SearchHits<EsAmazonProductListing> search = elasticsearchRestTemplate1.search(query, EsAmazonProductListing.class, amazonProductListingIndexCoordinates);
        Aggregations aggregations = search.getAggregations();
        if (aggregations == null) {
            return 0L;
        }
        ParsedCardinality cardinality = aggregations.get("distinct_parent_asin");
        if (cardinality == null) {
            return 0L;
        }
        return cardinality.getValue();

    }

    @Override
    public List<String> searchExistingIds(List<String> ids) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(QueryBuilders.termsQuery("id", ids));
        sourceBuilder.size(ids.size()); // 设置查询结果数量与 IDs 数量相同

        SearchRequest searchRequest = new SearchRequest(amazonProductListingIndexCoordinates.getIndexName());
        searchRequest.source(sourceBuilder);

        // 存放相关查询数据
        List<String> esIds = new ArrayList<>();
        try {
            // 执行查询
            SearchResponse searchResponse = restHighLevelClient1.search(searchRequest, RequestOptions.DEFAULT);
            SearchHit[] searchHits = searchResponse.getHits().getHits();

            // 将已存在的 ID 添加到列表中
            for (SearchHit hit : searchHits) {
                esIds.add(hit.getId());
            }
        } catch (IOException e) {
            log.error("Error while searching existing IDs: {}", e.getMessage());
        }

        return esIds;
    }

    /**
     * 当天刊登成功的模板，在线且在售的链接数量总和
     *
     * @param sonSkus        子sku
     * @param accountNumbers 店铺
     * @return
     */
    @Override
    public Map<String, Long> getSkuAccountPublishNumber(List<String> sonSkus, List<String> accountNumbers) {
        Map<String, Long> skuPublishNumberMap = new HashMap<>();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (CollectionUtils.isNotEmpty(accountNumbers)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("accountNumber", accountNumbers));
        }
        boolQueryBuilder.filter(QueryBuilders.termsQuery("articleNumber", sonSkus));
        boolQueryBuilder.filter(QueryBuilders.termQuery("isOnline", true));
        boolQueryBuilder.filter(QueryBuilders.termQuery("itemStatus","Active"));
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withPageable(PageRequest.of(0, 1))
                .addAggregation(AggregationBuilders.terms("sku_group").field("articleNumber"))
                .build();

        SearchHits<EsAmazonProductListing> search = elasticsearchRestTemplate1.search(query, EsAmazonProductListing.class, amazonProductListingIndexCoordinates);
        Aggregations aggregations = search.getAggregations();
        if (aggregations == null) {
            return skuPublishNumberMap;
        }
        ParsedStringTerms skuGroup = aggregations.get("sku_group");
        if (CollectionUtils.isEmpty(skuGroup.getBuckets())) {
            return skuPublishNumberMap;
        }
        skuGroup.getBuckets().forEach(bucket -> {
            String key = (String)bucket.getKey();
            long docCount = bucket.getDocCount();
            skuPublishNumberMap.put(key,docCount);
        });
        return skuPublishNumberMap;
    }

    @Override
    public EsCompositeAggsResponse<AmazonSkuPublishNumberDO> compositeSkuPublishNumberSearch(String categoryId, List<String> sites, String searchAfter) {
        EsCompositeAggsResponse<AmazonSkuPublishNumberDO> compositeAggsResponse = new EsCompositeAggsResponse<>();

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.filter(QueryBuilders.termsQuery("site", sites));
        boolQuery.filter(QueryBuilders.termQuery("itemStatus","Active"));
        boolQuery.filter(QueryBuilders.termQuery("isOnline",true));
        boolQuery.must(QueryBuilders.wildcardQuery("categoryId", "*,"+categoryId+",*"));
        boolQuery.mustNot(QueryBuilders.termQuery("articleNumber", "匹配不到货号"));

        // 分组
        CompositeAggregationBuilder compositeBuilder = new CompositeAggregationBuilder(
                "sku_group_agg",
                Collections.singletonList(new TermsValuesSourceBuilder("sku").field("articleNumber").order("desc"))
        ).size(100);
        if (StringUtils.isNotBlank(searchAfter)) {
            compositeBuilder.aggregateAfter(ImmutableMap.of("sku", searchAfter));
        }

        compositeBuilder.subAggregation(AggregationBuilders.terms("site_agg").field("site"));
        // 查询
        searchSourceBuilder
                .query(boolQuery)
                .aggregation(compositeBuilder)
                .size(0);

        SearchRequest searchRequest = new SearchRequest(amazonProductListingIndexCoordinates.getIndexName());
        searchRequest.source(searchSourceBuilder);
        try {
            SearchResponse search = restHighLevelClient1.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations aggregations = search.getAggregations();
            if (aggregations == null) {
                return compositeAggsResponse;
            }

            ParsedComposite skuGroupAgg = aggregations.get("sku_group_agg");
            List<AmazonSkuPublishNumberDO> publishNumberDOS = skuGroupAgg.getBuckets().stream().map(bucket -> {
                Map<String, Object> key = bucket.getKey();
                String sku = (String) key.get("sku");

                AmazonSkuPublishNumberDO publishNumberDO = new AmazonSkuPublishNumberDO();
                publishNumberDO.setSku(sku);

                Aggregations bucketAggregations = bucket.getAggregations();

                ParsedStringTerms siteAgg = bucketAggregations.get("site_agg");

                siteAgg.getBuckets().forEach(siteBucket -> {
                    String site = (String) siteBucket.getKey();
                    if ("US".equals(site)) {
                        publishNumberDO.setUsSiteCount((int) siteBucket.getDocCount());
                    }
                    if ("DE".equals(site)) {
                        publishNumberDO.setDeSiteCount((int) siteBucket.getDocCount());
                    }
                });
                return publishNumberDO;
            }).collect(Collectors.toList());
            if (skuGroupAgg.afterKey() != null) {
                String afterKey = (String) skuGroupAgg.afterKey().get("sku");
                compositeAggsResponse.setSearchAfter(afterKey);
            }
            compositeAggsResponse.setDataList(publishNumberDOS);

        } catch (IOException | RuntimeException e) {
            log.error("search fail, query:{}, e:{}", searchSourceBuilder, e.getMessage(), e);
        }
        return compositeAggsResponse;
    }


    /**
     * 根据sonAsin查询在售数量，已下架的链接也需要计算
     * @param sonAsins  sonAsin列表
     * @return
     */
    @Override
    public Map<String, AmazonAsinSaleCountDO> getSonAsinSaleCount(List<String> sonAsins) {
        Map<String, AmazonAsinSaleCountDO> asinSaleCountMap = new HashMap<>();
        if (CollectionUtils.isEmpty(sonAsins)) {
            return asinSaleCountMap;
        }

        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
//                .filter(QueryBuilders.termQuery("isOnline", true))
                .filter(QueryBuilders.termsQuery("sonAsin", sonAsins));

        NativeSearchQuery searchQuery = queryBuilder
                .withQuery(boolQueryBuilder)
                .withFields("sonAsin","order_24H_count","order_last_7d_count","order_last_7d_count",
                        "order_last_14d_count","order_last_30d_count", "order_num_total")
                .build();

        List<EsAmazonProductListing> productListings = ElasticSearchHelper.scrollQuery(elasticsearchRestTemplate1,
                10 * 60 * 1000, searchQuery, EsAmazonProductListing.class, amazonProductListingIndexCoordinates);
        if (CollectionUtils.isEmpty(productListings)) {
            return asinSaleCountMap;
        }

        productListings.forEach(productListing -> {
            AmazonAsinSaleCountDO saleCountDO = asinSaleCountMap.getOrDefault(productListing.getSonAsin(), new AmazonAsinSaleCountDO(productListing.getSonAsin()));
            Integer sale_24_count = Optional.ofNullable(productListing.getOrder_24H_count()).orElse(0);
            Integer sale_7d_count = Optional.ofNullable(productListing.getOrder_last_7d_count()).orElse(0);
            Integer sale_14d_count = Optional.ofNullable(productListing.getOrder_last_14d_count()).orElse(0);
            Integer sale_30d_count = Optional.ofNullable(productListing.getOrder_last_30d_count()).orElse(0);
            Integer sale_total_count = Optional.ofNullable(productListing.getOrder_num_total()).orElse(0);

            saleCountDO.addSale_24_count(sale_24_count);
            saleCountDO.addSale_7d_count(sale_7d_count);
            saleCountDO.addSale_14d_count(sale_14d_count);
            saleCountDO.addSale_30d_count(sale_30d_count);
            saleCountDO.addSale_total_count(sale_total_count);
            asinSaleCountMap.put(productListing.getSonAsin(), saleCountDO);
        });
        return asinSaleCountMap;
    }

    /**
     * 滚动查询执行任务
     *
     * @param request      查询条件
     * @param executorTask 任务
     * @return
     */
    @Override
    public int scrollQueryExecutorTask(EsAmazonProductListingRequest request, Consumer<List<EsAmazonProductListing>> executorTask) {
        // 查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        buildBoolQuery(boolQueryBuilder, request);

        SortOrder sortOrder = SortOrder.DESC.toString().equals(request.getSequence()) ? SortOrder.DESC : SortOrder.ASC;
        NativeSearchQuery searchQuery = queryBuilder
                .withFields(request.getFields())
                .withQuery(boolQueryBuilder)
                .withPageable(PageRequest.of(0, 1000))
                .withSort(SortBuilders.fieldSort(request.getOrderBy()).order(sortOrder))
                .build();
        String scrollId = null;
        List<String> scrollIdList = new ArrayList<>();
        long scrollTimeInMillis = 10 * 60 * 1000;
        int totalCount = 0;
        while (true) {
            SearchScrollHits<EsAmazonProductListing> searchScrollHits = null;
            if (scrollId == null) {
                searchScrollHits = elasticsearchRestTemplate1.searchScrollStart(scrollTimeInMillis, searchQuery, EsAmazonProductListing.class, amazonProductListingIndexCoordinates);
            } else {
                searchScrollHits = elasticsearchRestTemplate1.searchScrollContinue(scrollId, scrollTimeInMillis, EsAmazonProductListing.class, amazonProductListingIndexCoordinates);
            }
            scrollId = searchScrollHits.getScrollId();
            scrollIdList.add(scrollId);
            if (!searchScrollHits.hasSearchHits()) {
                elasticsearchRestTemplate1.searchScrollClear(scrollIdList);
                break;
            }
            List<EsAmazonProductListing> dataList = (List) SearchHitSupport.unwrapSearchHits(searchScrollHits);
            if (CollectionUtils.isEmpty(dataList)) {
                break;
            }
            totalCount += dataList.size();
            // 执行任务
            executorTask.accept(dataList);
        }
        return totalCount;
    }

    @Override
    public int scrollQueryExecutorTask(EsAmazonProductListingRequest request, Consumer<BoolQueryBuilder> func, Consumer<List<EsAmazonProductListing>> executorTask) {
        // 查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        buildBoolQuery(boolQueryBuilder, request);
        func.accept(boolQueryBuilder);

        SortOrder sortOrder = SortOrder.DESC.toString().equals(request.getSequence()) ? SortOrder.DESC : SortOrder.ASC;
        NativeSearchQuery searchQuery = queryBuilder
                .withFields(request.getFields())
                .withQuery(boolQueryBuilder)
                .withPageable(PageRequest.of(0, 1000))
                .withSort(SortBuilders.fieldSort(request.getOrderBy()).order(sortOrder))
                .build();
        String scrollId = null;
        List<String> scrollIdList = new ArrayList<>();
        long scrollTimeInMillis = 10 * 60 * 1000;
        int totalCount = 0;
        while (true) {
            SearchScrollHits<EsAmazonProductListing> searchScrollHits = null;
            if (scrollId == null) {
                searchScrollHits = elasticsearchRestTemplate1.searchScrollStart(scrollTimeInMillis, searchQuery, EsAmazonProductListing.class, amazonProductListingIndexCoordinates);
            } else {
                searchScrollHits = elasticsearchRestTemplate1.searchScrollContinue(scrollId, scrollTimeInMillis, EsAmazonProductListing.class, amazonProductListingIndexCoordinates);
            }
            scrollId = searchScrollHits.getScrollId();
            scrollIdList.add(scrollId);
            if (!searchScrollHits.hasSearchHits()) {
                elasticsearchRestTemplate1.searchScrollClear(scrollIdList);
                break;
            }
            List<EsAmazonProductListing> dataList = (List) SearchHitSupport.unwrapSearchHits(searchScrollHits);
            if (CollectionUtils.isEmpty(dataList)) {
                break;
            }
            totalCount += dataList.size();
            // 执行任务
            executorTask.accept(dataList);
        }
        return totalCount;
    }

    private void buildBoolQuery(BoolQueryBuilder boolQueryBuilder, EsAmazonProductListingRequest esAmazonProductListingRequest) {
        // id
        List<String> idList = esAmazonProductListingRequest.getIdList();
        if (CollectionUtils.isNotEmpty(idList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("id", idList));
        }
        String gtId = esAmazonProductListingRequest.getGtId();
        if (StringUtils.isNotBlank(gtId)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("id").gt(gtId));
        }
        //站点
        String site = esAmazonProductListingRequest.getSite();
        if (StringUtils.isNotBlank(site)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("site", site));
        }
        List<String> siteList = esAmazonProductListingRequest.getSiteList();
        if (CollectionUtils.isNotEmpty(siteList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("site", siteList));
        }

        //亚马逊账号（部门、销售主管、销售）
        String accountNumber = esAmazonProductListingRequest.getAccountNumber();
        if (StringUtils.isNotBlank(accountNumber)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("accountNumber", accountNumber));
        }
        List<String> accountNumberList = esAmazonProductListingRequest.getAccountNumberList();
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            List<String> searchAccounts = accountNumberList.stream().filter(Objects::nonNull).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            boolQueryBuilder.filter(QueryBuilders.termsQuery("accountNumber", searchAccounts));
        }

        // 账号等级
        String accountLevel = esAmazonProductListingRequest.getAccountLevel();
        if (StringUtils.isNotBlank(accountLevel)) {
            boolQueryBuilder.must(QueryBuilders.matchQuery("attribute1", accountLevel));
        }
        List<String> accountLevelList = esAmazonProductListingRequest.getAccountLevelList();
        if (CollectionUtils.isNotEmpty(accountLevelList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("attribute1", accountLevelList));
        }

        // 主asin
        String parentAsin = esAmazonProductListingRequest.getParentAsin();
        if (StringUtils.isNotBlank(parentAsin)) {
            boolQueryBuilder.must(QueryBuilders.matchQuery("parentAsin", parentAsin));
        }
        List<String> parentAsinList = esAmazonProductListingRequest.getParentAsinList();
        if (CollectionUtils.isNotEmpty(parentAsinList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("parentAsin", parentAsinList));
        }

        // 子asin
        String sonAsin = esAmazonProductListingRequest.getSonAsin();
        if (StringUtils.isNotBlank(sonAsin)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("sonAsin", sonAsin));
        }
        List<String> sonAsinList = esAmazonProductListingRequest.getSonAsinList();
        if (CollectionUtils.isNotEmpty(sonAsinList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("sonAsin", sonAsinList));
        }

        List<String> parentOrSonAsinList = esAmazonProductListingRequest.getParentOrSonAsinList();
        if (CollectionUtils.isNotEmpty(parentOrSonAsinList)) {
            BoolQueryBuilder parentOrSonAsinQuery = QueryBuilders.boolQuery();
            parentOrSonAsinQuery.should(QueryBuilders.termsQuery("parentAsin", parentOrSonAsinList));
            parentOrSonAsinQuery.should(QueryBuilders.termsQuery("sonAsin", parentOrSonAsinList));
            boolQueryBuilder.must(parentOrSonAsinQuery);
        }

        // 过滤不包含FBA的sonAsin字段
        List<String> excludeSonAsinList = esAmazonProductListingRequest.getExcludeSonAsinList();
        if (CollectionUtils.isNotEmpty(excludeSonAsinList)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("sonAsin", excludeSonAsinList));
        }

        // 主sku
        String mainSku = esAmazonProductListingRequest.getMainSku();
        if (StringUtils.isNotBlank(mainSku)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("mainSku", mainSku.toUpperCase()));
        }

        // articleNumber
        String articleNumber = esAmazonProductListingRequest.getArticleNumber();
        if (StringUtils.isNotBlank(articleNumber)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("articleNumber", articleNumber.toUpperCase()));
        }
        List<String> articleNumberList = esAmazonProductListingRequest.getArticleNumberList();
        if (CollectionUtils.isNotEmpty(articleNumberList)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String sku : articleNumberList) {
                boolQuery.should(QueryBuilders.termQuery("articleNumber", sku.toUpperCase()));
            }
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        //sellerSku
        String sellerSku = esAmazonProductListingRequest.getSellerSku();
        if (StringUtils.isNotBlank(sellerSku)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("attribute2", sellerSku.toUpperCase()));
        }
        List<String> sellerSkuNumberList = esAmazonProductListingRequest.getSellerSkuList();
        if (CollectionUtils.isNotEmpty(sellerSkuNumberList)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String sSku : sellerSkuNumberList) {
                if (StringUtils.isNotBlank(sSku)) {
                    boolQuery.should(QueryBuilders.matchQuery("attribute2", sSku.toUpperCase()));
                }
            }
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        if (CollectionUtils.isNotEmpty(esAmazonProductListingRequest.getAttribute3List())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("attribute3", esAmazonProductListingRequest.getAttribute3List()));
        }

        // 刊登来源（数据来源）
        Integer skuDataSource = esAmazonProductListingRequest.getSkuDataSource();
        if (!ObjectUtils.isEmpty(skuDataSource)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("skuDataSource", skuDataSource));
        }

        //标题(子产品)
        String itemName = esAmazonProductListingRequest.getItemName();
        if (StringUtils.isNotBlank(itemName)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("itemName", "*" + itemName + "*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        // item状态
        String itemStatus = esAmazonProductListingRequest.getItemStatus();
        if (StringUtils.isNotBlank(itemStatus)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("itemStatus", itemStatus));
        }

        List<String> itemStatusList = esAmazonProductListingRequest.getItemStatusList();
        if (CollectionUtils.isNotEmpty(itemStatusList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("itemStatus", itemStatusList));
        }

        // 品牌
        String brandName = esAmazonProductListingRequest.getBrandName();
        if (StringUtils.isNotBlank(brandName)) {
            if (brandName.contains(",")) {
                List<String> brandNameList = CommonUtils.splitList(brandName, ",");
                if (CollectionUtils.isNotEmpty(brandNameList)) {
                    boolQueryBuilder.must(QueryBuilders.termsQuery("brandName", brandNameList));
                }
            } else {
                boolQueryBuilder.must(QueryBuilders.matchQuery("brandName", brandName));
            }
        }

        // itemType
        Boolean isExistItemType = esAmazonProductListingRequest.getIsExistItemType();
        if (!ObjectUtils.isEmpty(isExistItemType) && BooleanUtils.isTrue(isExistItemType)) {
            boolQueryBuilder.must(QueryBuilders.existsQuery("itemType"));
        } else if (!ObjectUtils.isEmpty(isExistItemType)) {
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("itemType"));
        }
        Integer itemType = esAmazonProductListingRequest.getItemType();
        if (!ObjectUtils.isEmpty(itemType)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("itemType", itemType));
        }
        List<Integer> itemTypeList = esAmazonProductListingRequest.getItemTypeList();
        if (CollectionUtils.isNotEmpty(itemTypeList)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("itemType", itemTypeList));
        }
        List<Integer> excludeItemTypeList = esAmazonProductListingRequest.getExcludeItemTypeList();
        if (CollectionUtils.isNotEmpty(excludeItemTypeList)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("itemType", excludeItemTypeList));
        }

        // conditionType
        Boolean isExistConditionType = esAmazonProductListingRequest.getIsExistConditionType();
        if (!ObjectUtils.isEmpty(isExistConditionType) && BooleanUtils.isTrue(isExistConditionType)) {
            boolQueryBuilder.must(QueryBuilders.existsQuery("conditionType"));
        } else if (!ObjectUtils.isEmpty(isExistConditionType)) {
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("conditionType"));
        }

        // issuesSeverity
        String issuesSeverity = esAmazonProductListingRequest.getIssuesSeverity();
        if (StringUtils.isNotEmpty(issuesSeverity)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("issuesSeverity", issuesSeverity));
        }

        // 是否在线
        Boolean isOnline = esAmazonProductListingRequest.getIsOnline();
        if (!ObjectUtils.isEmpty(isOnline) && BooleanUtils.isTrue(isOnline)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("isOnline", true));
        } else if (!ObjectUtils.isEmpty(isOnline)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("isOnline", false));
        }

        if (StringUtils.isNotBlank(esAmazonProductListingRequest.getStarUpdateInfringementTime())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("updateInfringementTime").gte(esAmazonProductListingRequest.getStarUpdateInfringementTime()));
        }
        // 侵权词详情对象
        BoolQueryBuilder infringementWordBool = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(esAmazonProductListingRequest.getInfringementWord())) {
            infringementWordBool.must(QueryBuilders.termQuery("infringementWordInfos.originWord", StringUtils.lowerCase(esAmazonProductListingRequest.getInfringementWord())));
        }
        if (CollectionUtils.isNotEmpty(esAmazonProductListingRequest.getInfringementWords())) {
            List<String> infringementWords = esAmazonProductListingRequest.getInfringementWords().stream().map(word -> StringUtils.lowerCase(word)).collect(Collectors.toList());
            infringementWordBool.must(QueryBuilders.termsQuery("infringementWordInfos.originWord", infringementWords));
        }
        if (StringUtils.isNotBlank(esAmazonProductListingRequest.getInfringementWordType())) {
            infringementWordBool.must(QueryBuilders.termQuery("infringementWordInfos.type", esAmazonProductListingRequest.getInfringementWordType()));
        }

        if (CollectionUtils.isNotEmpty(esAmazonProductListingRequest.getTrademarkIdentification())) {
            infringementWordBool.must(QueryBuilders.termsQuery("infringementWordInfos.trademarkIdentification", esAmazonProductListingRequest.getTrademarkIdentification()));
        }
        //是否包含侵权词
        Boolean isInfringementWord = esAmazonProductListingRequest.getIsInfringementWord();
        if (BooleanUtils.isTrue(isInfringementWord)) {
            //侵权词不为空
            infringementWordBool.must(QueryBuilders.existsQuery("infringementWordInfos.originWord"));
        } else if (null != isInfringementWord && !BooleanUtils.isTrue(isInfringementWord)) {
            //侵权词为空
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("infringingBrandWord"));
        }

        if (CollectionUtils.isNotEmpty(infringementWordBool.filter())
                || CollectionUtils.isNotEmpty(infringementWordBool.must())
                || CollectionUtils.isNotEmpty(infringementWordBool.should())
                || CollectionUtils.isNotEmpty(infringementWordBool.mustNot())) {
            NestedQueryBuilder nestedQuery = QueryBuilders.nestedQuery("infringementWordInfos", infringementWordBool, ScoreMode.Total);
            boolQueryBuilder.filter(nestedQuery);
        }

        // 是否跟卖
        String followSaleFlag = esAmazonProductListingRequest.getFollowSaleFlag();
        if (StringUtils.isNotBlank(followSaleFlag)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("followSaleFlag", followSaleFlag));
        }

        //平台库存是否为0
        Integer quantity = esAmazonProductListingRequest.getQuantity();
        if (!ObjectUtils.isEmpty(quantity)) {
            // 查询大于
            boolQueryBuilder.must(QueryBuilders.termQuery("quantity", quantity));
        }
        if (esAmazonProductListingRequest.getQuantityGt() != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("quantity").gt(esAmazonProductListingRequest.getQuantityGt()));
        }
        if (esAmazonProductListingRequest.getQuantityLt() != null) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("quantity").lt(esAmazonProductListingRequest.getQuantityLt()));
        }

        //sku类目（产品系统类目）
        String categoryId = esAmazonProductListingRequest.getCategoryId();
        if (StringUtils.isNotEmpty(categoryId)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("categoryId", "*," + categoryId + ",*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        //产品标签
        String tagCode = esAmazonProductListingRequest.getTagCode();
        if (StringUtils.isNotEmpty(tagCode)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("tagCodes", tagCode));
        }

        //产品标签
        String tagCodes = esAmazonProductListingRequest.getTagCodes();
        if (StringUtils.isNotEmpty(tagCodes)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("tagCodes", "*," + tagCodes + ",*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        //产品标签
        List<String> tagCodeList = esAmazonProductListingRequest.getTagCodeList();
        if (CollectionUtils.isNotEmpty(tagCodeList)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String tagCodeName : tagCodeList) {
                boolQuery.should(QueryBuilders.wildcardQuery("tagCodes", "*," + tagCodeName + ",*"));
            }
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        //特殊标签
        String specialGoodsCode = esAmazonProductListingRequest.getSpecialGoodsCode();
        if (StringUtils.isNotEmpty(specialGoodsCode)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("specialGoodsCode", "*," + specialGoodsCode + ",*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        //特殊标签
        List<String> specialGoodsCodes = esAmazonProductListingRequest.getSpecialGoodsCodes();
        if (CollectionUtils.isNotEmpty(specialGoodsCodes)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            specialGoodsCodes.forEach(code -> {
                boolQuery.should(QueryBuilders.wildcardQuery("specialGoodsCode", "*," + code + ",*"));
            });
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        //  风险等级查询
        Integer riskLevelId = esAmazonProductListingRequest.getRiskLevelId();
        if (null != riskLevelId) {
            boolQueryBuilder.must(QueryBuilders.termQuery("riskLevelId", riskLevelId));
        }
        List<Integer> riskLevelIdList = esAmazonProductListingRequest.getRiskLevelIdList();
        if (CollectionUtils.isNotEmpty(riskLevelIdList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("riskLevelId", riskLevelIdList));
        }

        //是否存在禁售平台
        String forbidChannel = esAmazonProductListingRequest.getForbidChannel();
        if (StringUtils.isNotEmpty(forbidChannel)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("forbidChannel", "*," + forbidChannel + ",*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }
        List<String> forbidChannelList = esAmazonProductListingRequest.getForbidChannelList();
        if (CollectionUtils.isNotEmpty(forbidChannelList)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String bidChannel : forbidChannelList) {
                boolQuery.should(QueryBuilders.wildcardQuery("forbidChannel", "*" + bidChannel + "*"));
            }
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        // 单品状态
        String skuStatus = esAmazonProductListingRequest.getSkuStatus();
        if (StringUtils.isNotBlank(skuStatus)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("skuStatus", skuStatus));
        }
        List<String> skuStatusList = esAmazonProductListingRequest.getSkuStatusList();
        if (CollectionUtils.isNotEmpty(skuStatusList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("skuStatus", skuStatusList));
        }

        // 7天销量
        if (!ObjectUtils.isEmpty(esAmazonProductListingRequest.getFrom7SaleQuantity())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("saleQuantity").from(esAmazonProductListingRequest.getFrom7SaleQuantity()));
        }
        if (!ObjectUtils.isEmpty(esAmazonProductListingRequest.getTo7SaleQuantity())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("saleQuantity").to(esAmazonProductListingRequest.getTo7SaleQuantity()));
        }

        // 30天销量
        if (!ObjectUtils.isEmpty(esAmazonProductListingRequest.getFrom30SaleQuantity())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("attribute1").from(esAmazonProductListingRequest.getFrom30SaleQuantity()));
        }
        if (!ObjectUtils.isEmpty(esAmazonProductListingRequest.getTo30SaleQuantity())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("attribute1").to(esAmazonProductListingRequest.getTo30SaleQuantity()));
        }

        // 更新人
        String updatedBy = esAmazonProductListingRequest.getUpdatedBy();
        if (StringUtils.isNotBlank(updatedBy)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("updatedBy", updatedBy));
        }

        //最新上架时间
        String startOpenDate = esAmazonProductListingRequest.getStartOpenDate();
        if (!ObjectUtils.isEmpty(startOpenDate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("openDate").from(startOpenDate));
        }
        String endOpenDate = esAmazonProductListingRequest.getEndOpenDate();
        if (!ObjectUtils.isEmpty(endOpenDate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("openDate").to(endOpenDate));
        }

        //第一次上架时间
        String startFirstOpenDate = esAmazonProductListingRequest.getStartFirstOpenDate();
        if (!ObjectUtils.isEmpty(startFirstOpenDate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("firstOpenDate").gte(startFirstOpenDate));
        }
        String endFirstOpenDate = esAmazonProductListingRequest.getEndFirstOpenDate();
        if (!ObjectUtils.isEmpty(endFirstOpenDate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("firstOpenDate").to(endFirstOpenDate));
        }

        String endFirstOrOpenDate = esAmazonProductListingRequest.getEndFirstOrOpenDate();
        if (StringUtils.isNotBlank(endFirstOrOpenDate)) {
            BoolQueryBuilder shouldBoolQuery = QueryBuilders.boolQuery();
            shouldBoolQuery.should(QueryBuilders.boolQuery().must(QueryBuilders.rangeQuery("openDate").lte(endFirstOrOpenDate)));
            shouldBoolQuery.should(QueryBuilders.boolQuery()
                    .must(QueryBuilders.rangeQuery("firstOpenDate").lte(endFirstOrOpenDate))
                    .mustNot(QueryBuilders.existsQuery("openDate")));
            shouldBoolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(shouldBoolQuery);
        }

        //最新下线时间
        String startOfflineDate = esAmazonProductListingRequest.getStartOfflineDate();
        if (!ObjectUtils.isEmpty(startOfflineDate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("offlineDate").from(startOfflineDate));
        }
        String endOfflineDate = esAmazonProductListingRequest.getEndOfflineDate();
        if (!ObjectUtils.isEmpty(endOfflineDate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("offlineDate").to(endOfflineDate));
        }
        Boolean isExistOfflineDate = esAmazonProductListingRequest.getIsExistOfflineDate();
        if (null != isExistOfflineDate && !BooleanUtils.isTrue(isExistOfflineDate)) {
            //下架时间为空
            boolQueryBuilder.mustNot(QueryBuilders.existsQuery("offlineDate"));
        }
        //第一次下架时间
        String startFirstOfflineDate = esAmazonProductListingRequest.getStartFirstOfflineDate();
        if (!ObjectUtils.isEmpty(startFirstOfflineDate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("firstOfflineDate").from(startFirstOfflineDate));
        }
        String endFirstOfflineDate = esAmazonProductListingRequest.getEndFirstOfflineDate();
        if (!ObjectUtils.isEmpty(endFirstOfflineDate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("firstOfflineDate").to(endFirstOfflineDate));
        }

        //同步时间
        String startSyncDate = esAmazonProductListingRequest.getStartSyncDate();
        if (!ObjectUtils.isEmpty(startSyncDate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("syncDate").from(startSyncDate));
        }
        String endSyncDate = esAmazonProductListingRequest.getEndSyncDate();
        if (!ObjectUtils.isEmpty(endSyncDate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("syncDate").to(endSyncDate));
        }

        // 更新时间
        String startUpdateDate = esAmazonProductListingRequest.getStartUpdateDate();
        if (!ObjectUtils.isEmpty(startUpdateDate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("updateDate").from(startUpdateDate));
        }
        String endUpdateDate = esAmazonProductListingRequest.getEndUpdateDate();
        if (!ObjectUtils.isEmpty(endUpdateDate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("updateDate").to(endUpdateDate));
        }

        // 销量范围
        Long fromSaleQuantity = esAmazonProductListingRequest.getFromSaleQuantity();
        Long toSaleQuantity = esAmazonProductListingRequest.getToSaleQuantity();
        String saleQuantityBean = esAmazonProductListingRequest.getSaleQuantityBean();
        Boolean isSaleQuantityNull = esAmazonProductListingRequest.getIsSaleQuantityNull();
        if ((!ObjectUtils.isEmpty(fromSaleQuantity) || !ObjectUtils.isEmpty(toSaleQuantity) || isSaleQuantityNull != null) && StringUtils.isNotBlank(saleQuantityBean)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

            if (!ObjectUtils.isEmpty(fromSaleQuantity) || !ObjectUtils.isEmpty(toSaleQuantity)) {
                BoolQueryBuilder bool = QueryBuilders.boolQuery();
                if (!ObjectUtils.isEmpty(fromSaleQuantity)) {
                    bool.must(QueryBuilders.rangeQuery(saleQuantityBean).from(fromSaleQuantity));
                }
                if (!ObjectUtils.isEmpty(toSaleQuantity)) {
                    bool.must(QueryBuilders.rangeQuery(saleQuantityBean).to(toSaleQuantity));
                }
                boolQuery.should(bool);
            }

            // 总销量为空
            String order_num_total = "order_num_total";
            if (order_num_total.equals(saleQuantityBean) && isSaleQuantityNull != null && isSaleQuantityNull) {
                BoolQueryBuilder bool = QueryBuilders.boolQuery();
                bool.mustNot(QueryBuilders.existsQuery(saleQuantityBean));
                boolQuery.should(bool);
            }
            // 30天销量为空
            String order_last_30d_count = "order_last_30d_count";
            if (order_last_30d_count.equals(saleQuantityBean) && isSaleQuantityNull != null && isSaleQuantityNull) {
                BoolQueryBuilder bool = QueryBuilders.boolQuery();
                bool.mustNot(QueryBuilders.existsQuery(saleQuantityBean));
                boolQuery.should(bool);
            }
            // 14天销量为空
            String order_last_14d_count = "order_last_14d_count";
            if (order_last_14d_count.equals(saleQuantityBean) && isSaleQuantityNull != null && isSaleQuantityNull) {
                BoolQueryBuilder bool = QueryBuilders.boolQuery();
                bool.mustNot(QueryBuilders.existsQuery(saleQuantityBean));
                boolQuery.should(bool);
            }
            // 7天销量为空
            String order_last_7d_count = "order_last_7d_count";
            if (order_last_7d_count.equals(saleQuantityBean) && isSaleQuantityNull != null && isSaleQuantityNull) {
                BoolQueryBuilder bool = QueryBuilders.boolQuery();
                bool.mustNot(QueryBuilders.existsQuery(saleQuantityBean));
                boolQuery.should(bool);
            }

            // 24小时销量为空
            String order_24H_count = "order_24H_count";
            if (order_24H_count.equals(saleQuantityBean) && isSaleQuantityNull != null && isSaleQuantityNull) {
                BoolQueryBuilder bool = QueryBuilders.boolQuery();
                bool.mustNot(QueryBuilders.existsQuery(saleQuantityBean));
                boolQuery.should(bool);
            }
            boolQueryBuilder.must(boolQuery);
        }

        // 总价范围
        Double fromTotalPrice = esAmazonProductListingRequest.getFromTotalPrice();
        Double toTotalPrice = esAmazonProductListingRequest.getToTotalPrice();
        if (!ObjectUtils.isEmpty(fromTotalPrice)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("totalPrice").from(fromTotalPrice));
        }
        if (!ObjectUtils.isEmpty(toTotalPrice)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("totalPrice").to(toTotalPrice));
        }

        // 利润率区间范围
        Double fromGrossProfitRate = esAmazonProductListingRequest.getFromGrossProfitRate();
        Double toGrossProfitRate = esAmazonProductListingRequest.getToGrossProfitRate();
        if (!ObjectUtils.isEmpty(fromGrossProfitRate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("grossProfitRate").from(fromGrossProfitRate));
        }
        if (!ObjectUtils.isEmpty(toGrossProfitRate)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("grossProfitRate").to(toGrossProfitRate));
        }

        // 利润率区间范围
        Double fromGrossProfit = esAmazonProductListingRequest.getFromGrossProfit();
        Double torossProfit = esAmazonProductListingRequest.getToGrossProfit();
        if (!ObjectUtils.isEmpty(fromGrossProfit)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("grossProfit").from(fromGrossProfit));
        }
        if (!ObjectUtils.isEmpty(torossProfit)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("grossProfit").to(torossProfit));
        }

        //禁售类型
        String infringementTypename = esAmazonProductListingRequest.getInfringementTypename();
        if (StringUtils.isNotBlank(infringementTypename)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("infringementTypename", "*|" + infringementTypename + "|*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }
        List<String> infringementTypenames = esAmazonProductListingRequest.getInfringementTypenames();
        if (CollectionUtils.isNotEmpty(infringementTypenames)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String s : infringementTypenames) {
                boolQuery.should(QueryBuilders.wildcardQuery("infringementTypename", "*|" + s + "|*"));
            }
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }


        //禁售原因
        String infringementObj = esAmazonProductListingRequest.getInfringementObj();
        if (StringUtils.isNotBlank(infringementObj)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("infringementObj", "*|" + infringementObj + "|*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }
        List<String> infringementObjs = esAmazonProductListingRequest.getInfringementObjs();
        if (CollectionUtils.isNotEmpty(infringementObjs)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String s : infringementObjs) {
                boolQuery.should(QueryBuilders.wildcardQuery("infringementObj", "*|" + s + "|*"));
            }
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }


        //可售站点
        String normalSale = esAmazonProductListingRequest.getNormalSale();
        if (StringUtils.isNotEmpty(normalSale)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            for (String s : normalSale.split(",")) {
                boolQuery.should(QueryBuilders.wildcardQuery("normalSale", "*," + s + ",*"));
            }
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        List<Integer> publishRoleList = esAmazonProductListingRequest.getPublishRoleList();
        if (CollectionUtils.isNotEmpty(publishRoleList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("publishRole", publishRoleList));
        }

        // 备货期
        if (!ObjectUtils.isEmpty(esAmazonProductListingRequest.getFromFulfillmentLatency())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("fulfillmentLatency").from(esAmazonProductListingRequest.getFromFulfillmentLatency()));
        }
        if (!ObjectUtils.isEmpty(esAmazonProductListingRequest.getToFulfillmentLatency())) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("fulfillmentLatency").to(esAmazonProductListingRequest.getToFulfillmentLatency()));
        }

        // 是否促销
        if (Objects.nonNull(esAmazonProductListingRequest.getIsPromotion())) {
            List<Integer> status = BooleanUtils.isTrue(esAmazonProductListingRequest.getIsPromotion()) ? Collections.singletonList(1) : Arrays.asList(0, 2);
            boolQueryBuilder.must(QueryBuilders.termsQuery("promotion", status));
        }
        // 是否新品
        if (Objects.nonNull(esAmazonProductListingRequest.getIsNewState())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("newState", esAmazonProductListingRequest.getIsNewState()));
        }
        // 下架备注
        if (Boolean.TRUE.equals(esAmazonProductListingRequest.getOfflineRemarkFullMatch()) && StringUtils.isNotBlank(esAmazonProductListingRequest.getOfflineRemark())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("attribute3", esAmazonProductListingRequest.getOfflineRemark()));
        }
        if (Boolean.FALSE.equals(esAmazonProductListingRequest.getOfflineRemarkFullMatch()) && StringUtils.isNotBlank(esAmazonProductListingRequest.getOfflineRemark())) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery("attribute3", "*" + esAmazonProductListingRequest.getOfflineRemark() + "*"));
            boolQuery.minimumShouldMatch(1);
            boolQueryBuilder.must(boolQuery);
        }

        // GPSR
        if (Objects.nonNull(esAmazonProductListingRequest.getExistGpsr())) {
            if (Boolean.TRUE.equals(esAmazonProductListingRequest.getExistGpsr())) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("existGpsr", Boolean.TRUE));
            } else {
                boolQueryBuilder.mustNot(QueryBuilders.existsQuery("existGpsr"));
//                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//                boolQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("existGpsr")));
//                boolQuery.should(QueryBuilders.termQuery("existGpsr", Boolean.FALSE));
//                boolQuery.minimumShouldMatch(1);
//                boolQueryBuilder.must(boolQuery);
            }
        }

        if (CollectionUtils.isNotEmpty(esAmazonProductListingRequest.getExcludeAccountNumber())) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("accountNumber", esAmazonProductListingRequest.getExcludeAccountNumber()));
        }

        if (esAmazonProductListingRequest.getCustomSaleNumberFileRangeQuery() != null) {
            Triple<String, Integer, Integer> customSaleNumberFileRangeQuery = esAmazonProductListingRequest.getCustomSaleNumberFileRangeQuery();
            String fileName = customSaleNumberFileRangeQuery.getLeft();
            Integer from = customSaleNumberFileRangeQuery.getMiddle();
            Integer to = customSaleNumberFileRangeQuery.getRight();
            if (StringUtils.isNotBlank(fileName) && from != null && from > 0 && to != null) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery(fileName).gte(from).lt(to));
            } else if (from != null && from == 0) {
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                BoolQueryBuilder rangeBoolQuery = QueryBuilders.boolQuery();
                rangeBoolQuery.must(QueryBuilders.rangeQuery(fileName).gte(from).lte(to));
                boolQuery.should(rangeBoolQuery);

                BoolQueryBuilder existBoolQuery = QueryBuilders.boolQuery();
                existBoolQuery.mustNot(QueryBuilders.existsQuery(fileName));
                boolQuery.should(existBoolQuery);

                boolQuery.minimumShouldMatch(1);
                boolQueryBuilder.must(boolQuery);
            }
        }
    }


    @Override
    public List<SkuPubilshListingFirstJoinTimeVo> getAmazonFirstJoinTime(List<String> articleNumberList) {
        if (CollectionUtils.isEmpty(articleNumberList)) {
            return List.of();
        }
        List<SkuPubilshListingFirstJoinTimeVo> skuPubilshListingFirstJoinTimeVoList = new ArrayList<>();
        for (String sku : articleNumberList) {
            NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("articleNumber", sku.toUpperCase()));
            queryBuilder
                    .withQuery(boolQueryBuilder)
                    .withPageable(PageRequest.of(0, 10000))
                    .withFields("articleNumber", "firstOpenDate","openDate")
                    .withSort(SortBuilders.fieldSort("firstOpenDate").order(SortOrder.ASC))
                    .withSort(SortBuilders.fieldSort("openDate").order(SortOrder.ASC))
                    .build();
            NativeSearchQuery searchQuery = queryBuilder.build();
            searchQuery.setTrackTotalHits(true);
            Page<EsAmazonProductListing> search = esAmazonProductListingRepository.search(searchQuery);
            if ( CollectionUtils.isNotEmpty(search.getContent())) {
                EsAmazonProductListing esAmazonProductListing = search.getContent().get(0);
                SkuPubilshListingFirstJoinTimeVo skuPubilshListingFirstJoinTimeVo = new SkuPubilshListingFirstJoinTimeVo();
                skuPubilshListingFirstJoinTimeVo.setPlatform(SaleChannelEnum.AMAZON.getChannelName());
                skuPubilshListingFirstJoinTimeVo.setSonSku(sku);
                Timestamp amazonEarliestTime = null;
                if (null != esAmazonProductListing.getFirstOpenDate()) {
                    amazonEarliestTime = new Timestamp(esAmazonProductListing.getFirstOpenDate().getTime());
                } else if (null != esAmazonProductListing.getOpenDate()) {
                    amazonEarliestTime = new Timestamp(esAmazonProductListing.getOpenDate().getTime());
                }
                skuPubilshListingFirstJoinTimeVo.setFirstTime(amazonEarliestTime);
                skuPubilshListingFirstJoinTimeVoList.add(skuPubilshListingFirstJoinTimeVo);
            }
        }

        return skuPubilshListingFirstJoinTimeVoList;
    }

    @Override
    public Long getAccountOnlineListingCount(List<String> accountNumbers) {
        if (CollectionUtils.isEmpty(accountNumbers)) {
            return 0L;
        }
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termsQuery("accountNumber", accountNumbers))
                .filter(QueryBuilders.termQuery("isOnline", Boolean.TRUE));
        queryBuilder.withQuery(boolQueryBuilder)
                .withPageable(PageRequest.of(0, 1))
                .withFields("id")
                .build();
        NativeSearchQuery searchQuery = queryBuilder.build();
        searchQuery.setTrackTotalHits(true);
        Page<EsAmazonProductListing> search = esAmazonProductListingRepository.search(searchQuery);
        if (CollectionUtils.isNotEmpty(search.getContent())) {
            return search.getTotalElements();
        }
        return 0L;
    }

    @Override
    public EsCompositeAggsResponse<String> statisticsOnlineAsin(EsAmazonProductListingRequest request, String searchAfter) {
        EsCompositeAggsResponse<String> compositeAggsResponse = new EsCompositeAggsResponse<>();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        buildBoolQuery(boolQuery, request);
        CompositeAggregationBuilder compositeBuilder = new CompositeAggregationBuilder(
                "asinAgg",
                Collections.singletonList(new TermsValuesSourceBuilder("sonAsin").field("sonAsin"))
        ).size(500);
        if (StringUtils.isNotBlank(searchAfter)) {
            compositeBuilder.aggregateAfter(ImmutableMap.of("sonAsin", searchAfter));
        }
        searchSourceBuilder
                .query(boolQuery)
                .aggregation(compositeBuilder)
                .size(0);

        SearchRequest searchRequest = new SearchRequest(amazonProductListingIndexCoordinates.getIndexName());
        searchRequest.source(searchSourceBuilder);
        try {
            SearchResponse search = restHighLevelClient1.search(searchRequest, RequestOptions.DEFAULT);
            Aggregations aggregations = search.getAggregations();
            if (aggregations == null) {
                return compositeAggsResponse;
            }
            CompositeAggregation skuAgg = aggregations.get("asinAgg");
            if (skuAgg == null) {
                return compositeAggsResponse;
            }

            List<String> articleNumbers = skuAgg.getBuckets().stream().map(bucket -> {
                Map<String, Object> dataMap = bucket.getKey();
                Object articleNumber = dataMap.get("sonAsin");
                if (articleNumber == null) {
                    return null;
                }
                return (String) articleNumber;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            if (skuAgg.afterKey() != null) {
                String afterKey = (String) skuAgg.afterKey().get("sonAsin");
                compositeAggsResponse.setSearchAfter(afterKey);
            }
            compositeAggsResponse.setDataList(articleNumbers);
        } catch (IOException | RuntimeException e) {
            log.error("search fail, query:{}, e:{}", searchSourceBuilder, e.getMessage(), e);
        }
        return compositeAggsResponse;
    }
}

