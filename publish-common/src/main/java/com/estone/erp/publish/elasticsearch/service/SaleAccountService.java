package com.estone.erp.publish.elasticsearch.service;

import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountPageRequest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

public interface SaleAccountService {
    List<String> getAccountList(EsSaleAccountRequest request);


    Page<SaleAccount> page(EsSaleAccountPageRequest request, int limit, int offset);

    List<SaleAccount> getSaleAccountsEs(EsSaleAccountRequest request, String... withFields);

    List<SaleAccount> getAccountInfoList(EsSaleAccountRequest request, String... withFields);

    /**
     * 获取销售和店铺对应关系
     * @param request
     * @return
     */
    List<String> getSaleIdAndAccount(EsSaleAccountRequest request);

    /**
     * 获取店铺对应销售idList
     * @param request
     * @return
     */
    Map<String,List<String>> getAccountAndSaleIdMap(EsSaleAccountRequest request);

    /**
     * 过滤冻结店铺和sip
     *
     * @param saleChannel
     * @param accountGroupNames
     * @return
     */
    Map<String, List<String>> filterFrozenStoreAndSip(String saleChannel, List<String> accountGroupNames);

    /**
     * 获取同一套商家ID下所属店铺
     *
     * @param merchantId  商家Id
     * @param saleChannel 平台
     * @return List<String> 店铺
     */
    List<String> getSameMerchantAccountNumber(String merchantId, String saleChannel);

    /**
     * 获取商家ID和店铺ID对应关系
     *
     * @param accountNumberList 店铺
     * @param saleChannel       平台
     * @return
     */
    Map<String, String> getAccountAndMerchantIdMap(List<String> accountNumberList, String saleChannel);

    List<String> getEmployeeManagedAccountNumbers(List<String> employeeNos, String saleChannel);
}
