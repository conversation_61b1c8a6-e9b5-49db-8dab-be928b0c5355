package com.estone.erp.common.mq.factory;

import lombok.Builder;
import lombok.Data;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;

/**
 * MQ 配置参数类
 * 
 * @Description: 用于封装 MQ 配置所需的所有参数，支持通用的对象工厂模式
 * @Author: AI Assistant
 * @Date: 2024/12/19
 */
@Data
@Builder
public class MqConfigParameters {
    
    /**
     * 虚拟主机名称
     */
    private String virtualHost;
    
    /**
     * 队列名称
     */
    private String queueName;
    
    /**
     * 路由键
     */
    private String routingKey;
    
    /**
     * 交换机名称
     */
    private String exchange;
    
    /**
     * 消费者数量
     */
    private int consumers;
    
    /**
     * 预取数量
     */
    private int prefetchCount;
    
    /**
     * 消息监听器
     */
    private ChannelAwareMessageListener messageListener;
    
    /**
     * 监听器开关
     */
    private boolean listenerEnabled;
    
    /**
     * Bean 名称前缀，用于生成唯一的 Bean 名称
     */
    private String beanNamePrefix;
    
    /**
     * 队列是否持久化，默认为 true
     */
    @Builder.Default
    private boolean durable = true;
    
    /**
     * 队列是否排他，默认为 false
     */
    @Builder.Default
    private boolean exclusive = false;
    
    /**
     * 队列是否自动删除，默认为 false
     */
    @Builder.Default
    private boolean autoDelete = false;
}
