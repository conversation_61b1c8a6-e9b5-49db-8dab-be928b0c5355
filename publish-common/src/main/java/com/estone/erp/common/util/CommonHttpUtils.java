package com.estone.erp.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.CategoryForecastDTO;
import com.estone.erp.common.model.SkuPackageImageDataDTO;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
public class CommonHttpUtils extends AbstractHttpClient {


    /**
     * 类目预测
     * 文档:http://172.16.10.40/web/#/87/11199
     * http://192.168.4.253:5012/query
     */
    public static ApiResult<CategoryForecastDTO> getCategoryForecast(String title, String site) {
        try {
            String httpUrl = getCategoryForecastHttpUrl();
            JSONObject param = new JSONObject();
            param.put("title", title);
            param.put("site", site);
            HttpPost httpPost = new HttpPost();

            httpPost.setURI(new URI(httpUrl));
            StringEntity entity = new StringEntity(param.toJSONString(), StandardCharsets.UTF_8);
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            // 阻塞了 6s等待，看数据，成功数据
            return getResultString(getHttpClient(6000, 3).execute(httpPost), new TypeReference<CategoryForecastDTO>() {
            });
        } catch (Exception e) {
            return ApiResult.newError(LogPrintUtil.getMinimumReverseStackCause(e));
        }
    }


    /**
     * 类目预测
     * 文档: http://172.16.10.40/web/#/87/11199
     * http://192.168.4.253:5012/query
     *
     * @param title      商品标题
     * @param site       站点名称
     * @param retryCount 最大重试次数
     * @return 类目预测结果
     */
    public static ApiResult<CategoryForecastDTO> getCategoryForecast(String title, String site, int retryCount) {
        String httpUrl = getCategoryForecastHttpUrl();
        JSONObject param = new JSONObject();
        param.put("title", title);
        param.put("site", site);
        ApiResult<CategoryForecastDTO> lastResult = null;


        if (StringUtils.isBlank(title)) { // 避免报错，直接返回错误结果
            return ApiResult.newError("标题或spu最次级分类为空");
        }

        for (int attempt = 1; attempt <= retryCount; attempt++) {
            try {
                HttpPost httpPost = new HttpPost();
                httpPost.setURI(new URI(httpUrl));
                StringEntity entity = new StringEntity(param.toJSONString(), StandardCharsets.UTF_8);
                entity.setContentEncoding("UTF-8");
                entity.setContentType("application/json");
                httpPost.setEntity(entity);

                lastResult = getResultString(getHttpClient(6000, 3).execute(httpPost), new TypeReference<CategoryForecastDTO>() {
                });

                CategoryForecastDTO data = lastResult.getResult();
                if (data != null && StringUtils.isNotBlank(data.getCat())) {
                    return lastResult;
                }

            } catch (Exception e) {
                log.error("第 {} 次调用 getCategoryForecast 异常，原因：{}", attempt, e.getMessage(), e);
                lastResult = ApiResult.newError(LogPrintUtil.getMinimumReverseStackCause(e));
            }

        }

        // 重试结束后，返回最后一次的结果（无论成功失败）
        return lastResult != null ? lastResult : ApiResult.newError("类目预测未能获取到任何结果");
    }


    /**
     * 类目预测接口地址
     */
    public static String getCategoryForecastHttpUrl() {
        String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_OZON, "OZON", "OZON_CATEGORY_FORECAST", null);
        if (StringUtils.isNotBlank(systemParamValue)) {
            return systemParamValue.trim();
        } else {
            // 避免缓存不存在
            return "http://192.168.4.253:5012/query";
        }
    }

    /**
     * 获取仓库系统中SKU管理列表-CE认证-SKU认证拍照需求列表数据
     *
     * @param photoDate 拍照日期（yyyy-MM-dd）
     * @return SKU拍照数据列表
     */
    public static ApiResult<List<SkuPackageImageDataDTO>> getSkuPackageImageData(String photoDate) {
        try {
            // 构建包含路径参数的URL
            String baseUrl = getSkuPackageImageDataHttpUrl();
            String httpUrl = baseUrl + "/" + photoDate;

            HttpPost httpPost = new HttpPost();
            httpPost.setURI(new URI(httpUrl));

            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("warehouseId", "1");

            // 根据接口规范，请求体为空
            StringEntity entity = new StringEntity("{}", StandardCharsets.UTF_8);
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            httpPost.setEntity(entity);

            // 设置6秒超时
            return getResultString(getHttpClient(6000, 3).execute(httpPost));
        } catch (Exception e) {
            log.error("调用仓库系统获取SKU拍照数据异常，拍照日期：{}，异常：{}", photoDate, e.getMessage(), e);
            return ApiResult.newError(LogPrintUtil.getMinimumReverseStackCause(e));
        }
    }

    /**
     * 获取仓库系统sku认证拍照（外包装图）接口基础地址
     */
    public static String getSkuPackageImageDataHttpUrl() {
        String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_SMT, "SMT", "SKU_PACKAGE_IMAGE_DATA_URL", null);
        if (StringUtils.isNotBlank(systemParamValue)) {
            return systemParamValue.trim();
        }

        throw new RuntimeException("获取不到仓库系统sku认证拍照接口地址");
    }


    private static <T> ApiResult<T> getResultString(CloseableHttpResponse httpResponse, TypeReference<T> reference) {
        int statusCode = httpResponse.getStatusLine().getStatusCode();
        try {
            try {
                String result = EntityUtils.toString(httpResponse.getEntity());
                if (statusCode != 200) {
                    return ApiResult.newError(result);
                }
                T t = JSON.parseObject(result, reference);
                return ApiResult.newSuccess(t);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } finally {
            IOUtils.closeQuietly(httpResponse);
        }
    }

    private static ApiResult<List<SkuPackageImageDataDTO>> getResultString(CloseableHttpResponse httpResponse) {
        int statusCode = httpResponse.getStatusLine().getStatusCode();
        try {
            try {
                String result = EntityUtils.toString(httpResponse.getEntity());
                if (statusCode != 200) {
                    return ApiResult.newError(result);
                }
                // 使用 TypeReference 正确解析泛型类型
                return JSON.parseObject(result, new TypeReference<ApiResult<List<SkuPackageImageDataDTO>>>() {});
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } finally {
            IOUtils.closeQuietly(httpResponse);
        }
    }
}
