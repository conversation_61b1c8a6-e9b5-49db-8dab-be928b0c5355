package com.estone.erp.common.mq.factory.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * MQ 配置注解
 *
 * @Description: 用于标注消息监听器类，自动创建和配置 MQ 相关对象
 * @Author: AI Assistant
 * @Date: 2024/12/19
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface MqConfig {

    /**
     * 虚拟主机名称
     */
    String virtualHost();

    /**
     * 队列名称
     */
    String queueName();

    /**
     * 路由键
     */
    String routingKey();

    /**
     * 交换机名称
     */
    String exchange();

    /**
     * 消费者数量配置属性名
     * 从 Spring 配置文件中获取，例如：${yml-config.exampleMqConsumers}
     */
    String consumersValueName();

    /**
     * 预取数量配置属性名
     * 从 Spring 配置文件中获取，例如：${yml-config.exampleMqPrefetchCount}
     */
    String prefetchCountValueName();

    /**
     * 监听器开关配置属性名
     * 从 Spring 配置文件中获取，例如：${yml-config.exampleMqListener}
     */
    String listenerEnabledValueName();

    /**
     * Bean 名称前缀，用于生成唯一的 Bean 名称
     * 生成的 Bean 名称格式：
     * - {beanNamePrefix}Queue
     * - {beanNamePrefix}QueueBinding
     * - {beanNamePrefix}MqListener
     * - {beanNamePrefix}MqListenerContainer
     */
    String beanNamePrefix();

    /**
     * 队列是否持久化，默认为 true
     */
    boolean durable() default true;

    /**
     * 队列是否排他，默认为 false
     */
    boolean exclusive() default false;

    /**
     * 队列是否自动删除，默认为 false
     */
    boolean autoDelete() default false;

}
