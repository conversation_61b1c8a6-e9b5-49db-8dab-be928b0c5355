//package com.estone.erp.common.mq.factory.annotation;
//
//import com.estone.erp.common.mq.PublishRabbitMqExchange;
//import com.estone.erp.common.mq.RabbitMqVirtualHosts;
//import com.rabbitmq.client.Channel;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.amqp.core.Message;
//import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
//import org.springframework.stereotype.Component;
//
//import java.nio.charset.StandardCharsets;
//
///**
// * 基于注解的 MQ 监听器示例
// *
// * @Description: 展示如何使用 @MqConfig 注解来自动配置 MQ 相关对象
// * @Author: AI Assistant
// * @Date: 2024/12/19
// */
//@Slf4j
//@Component
//@MqConfig(
//    virtualHost = RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST,
//    queueName = "ANNOTATION_EXAMPLE_QUEUE",
//    routingKey = "ANNOTATION_EXAMPLE_QUEUE_KEY",
//    exchange = PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE,
//    consumersValueName = "${yml-config.annotationExampleMqConsumers:1}",
//    prefetchCountValueName = "${yml-config.annotationExampleMqPrefetchCount:10}",
//    listenerEnabledValueName = "${yml-config.annotationExampleMqListener:true}",
//    beanNamePrefix = "annotationExample",
//    durable = true,
//    exclusive = false,
//    autoDelete = false
//)
//public class ExampleAnnotationMqListener implements ChannelAwareMessageListener {
//
//    /**
//     * 处理接收到的消息
//     *
//     * @param message 消息对象
//     * @param channel 通道对象
//     * @throws Exception 处理异常
//     */
//    @Override
//    public void onMessage(Message message, Channel channel) throws Exception {
//        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
//        long deliveryTag = message.getMessageProperties().getDeliveryTag();
//
//        try {
//            log.info("基于注解的 MQ 监听器接收到消息: {}", messageBody);
//
//            // 处理具体的业务逻辑
//            processBusinessLogic(messageBody);
//
//            // 手动确认消息
//            channel.basicAck(deliveryTag, false);
//            log.debug("消息处理完成并确认: deliveryTag={}", deliveryTag);
//
//        } catch (Exception e) {
//            log.error("处理消息时发生错误: messageBody={}, deliveryTag={}", messageBody, deliveryTag, e);
//
//            // 根据业务需要决定是否重新入队
//            // false 表示不重新入队，true 表示重新入队
//            boolean requeue = shouldRequeue(e, messageBody);
//            channel.basicNack(deliveryTag, false, requeue);
//
//            log.warn("消息处理失败，已拒绝: deliveryTag={}, requeue={}", deliveryTag, requeue);
//        }
//    }
//
//    /**
//     * 处理具体的业务逻辑
//     *
//     * @param messageBody 消息内容
//     */
//    private void processBusinessLogic(String messageBody) {
//        log.info("开始处理业务逻辑: {}", messageBody);
//
//        // 模拟业务处理逻辑
//        try {
//            // 这里可以添加具体的业务处理代码
//            // 例如：解析消息、调用服务、更新数据库等
//
//            // 模拟处理时间
//            Thread.sleep(50);
//
//            log.info("业务逻辑处理完成: {}", messageBody);
//
//        } catch (InterruptedException e) {
//            Thread.currentThread().interrupt();
//            throw new RuntimeException("业务处理被中断", e);
//        }
//    }
//
//    /**
//     * 判断是否应该重新入队
//     *
//     * @param exception 异常信息
//     * @param messageBody 消息内容
//     * @return true 表示重新入队，false 表示不重新入队
//     */
//    private boolean shouldRequeue(Exception exception, String messageBody) {
//        // 根据异常类型和业务逻辑决定是否重新入队
//        // 这里可以根据具体的业务需求来实现重试策略
//
//        if (exception instanceof IllegalArgumentException) {
//            // 参数错误，不重新入队
//            log.warn("参数错误，不重新入队: {}", messageBody);
//            return false;
//        }
//
//        if (exception instanceof RuntimeException) {
//            // 运行时异常，可以考虑重新入队
//            log.warn("运行时异常，重新入队: {}", messageBody);
//            return true;
//        }
//
//        // 默认不重新入队
//        return false;
//    }
//}
