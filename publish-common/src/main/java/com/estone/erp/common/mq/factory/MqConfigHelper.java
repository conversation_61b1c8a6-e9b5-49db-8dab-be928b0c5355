package com.estone.erp.common.mq.factory;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;

/**
 * MQ 配置助手类
 * 
 * @Description: 提供通用的 MQ 配置创建方法
 * @Author: AI Assistant
 * @Date: 2024/12/19
 */
@Slf4j
@Component
public class MqConfigHelper {
    
    private final MqConfigObjectFactory mqConfigObjectFactory;
    
    public MqConfigHelper(MqConfigObjectFactory mqConfigObjectFactory) {
        this.mqConfigObjectFactory = mqConfigObjectFactory;
    }
    
    /**
     * 创建并注册完整的 MQ 配置
     * 
     * @param virtualHost 虚拟主机
     * @param queueName 队列名称
     * @param routingKey 路由键
     * @param exchange 交换机
     * @param consumers 消费者数量
     * @param prefetchCount 预取数量
     * @param messageListener 消息监听器
     * @param listenerEnabled 监听器是否启用
     * @param beanNamePrefix Bean 名称前缀
     */
    public void createAndRegisterMqConfig(
            String virtualHost,
            String queueName,
            String routingKey,
            String exchange,
            int consumers,
            int prefetchCount,
            ChannelAwareMessageListener messageListener,
            boolean listenerEnabled,
            String beanNamePrefix) {
        
        log.info("开始创建 MQ 配置: {}", beanNamePrefix);
        
        // 构建配置参数
        MqConfigParameters parameters = MqConfigParameters.builder()
                .virtualHost(virtualHost)
                .queueName(queueName)
                .routingKey(routingKey)
                .exchange(exchange)
                .consumers(consumers)
                .prefetchCount(prefetchCount)
                .messageListener(messageListener)
                .listenerEnabled(listenerEnabled)
                .beanNamePrefix(beanNamePrefix)
                .durable(true)
                .exclusive(false)
                .autoDelete(false)
                .build();
        
        // 验证配置参数
        validateParameters(parameters);
        
        // 创建并注册 MQ 对象
        mqConfigObjectFactory.createAndRegisterMqObjects(parameters);
        
        log.info("MQ 配置创建完成: {}", beanNamePrefix);
        log.info("已自动创建并注册以下 Bean:");
        log.info("  - {}Queue (队列配置)", beanNamePrefix);
        log.info("  - {}QueueBinding (队列绑定配置)", beanNamePrefix);
        log.info("  - {}MqListener (消息监听器)", beanNamePrefix);
        log.info("  - {}MqListenerContainer (监听器容器)", beanNamePrefix);
    }
    

    
    /**
     * 验证配置参数的完整性
     * 
     * @param parameters 配置参数
     * @throws IllegalArgumentException 如果参数不完整
     */
    private void validateParameters(MqConfigParameters parameters) {
        if (parameters.getVirtualHost() == null || parameters.getVirtualHost().trim().isEmpty()) {
            throw new IllegalArgumentException("虚拟主机名称不能为空");
        }
        if (parameters.getQueueName() == null || parameters.getQueueName().trim().isEmpty()) {
            throw new IllegalArgumentException("队列名称不能为空");
        }
        if (parameters.getRoutingKey() == null || parameters.getRoutingKey().trim().isEmpty()) {
            throw new IllegalArgumentException("路由键不能为空");
        }
        if (parameters.getExchange() == null || parameters.getExchange().trim().isEmpty()) {
            throw new IllegalArgumentException("交换机名称不能为空");
        }
        if (parameters.getBeanNamePrefix() == null || parameters.getBeanNamePrefix().trim().isEmpty()) {
            throw new IllegalArgumentException("Bean 名称前缀不能为空");
        }
        if (parameters.getConsumers() <= 0) {
            throw new IllegalArgumentException("消费者数量必须大于 0");
        }
        if (parameters.getPrefetchCount() <= 0) {
            throw new IllegalArgumentException("预取数量必须大于 0");
        }
        
        log.debug("MQ 配置参数验证通过: {}", parameters.getBeanNamePrefix());
    }
    

}
