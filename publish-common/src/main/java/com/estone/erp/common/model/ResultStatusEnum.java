package com.estone.erp.common.model;

public enum ResultStatusEnum {
    FAILURE(0, "失败"),
    SUCCESS(1, "成功");

    private Integer code;
    private String name;

    ResultStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static String getNameByCode(Integer code) {
        for (ResultStatusEnum type : values()) {
            if (type.code.equals(code)) {
                return type.getName();
            }
        }
        return null;
    }
}
