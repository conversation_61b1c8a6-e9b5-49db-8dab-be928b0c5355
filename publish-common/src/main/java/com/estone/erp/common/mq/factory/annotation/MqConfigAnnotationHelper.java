package com.estone.erp.common.mq.factory.annotation;

import com.estone.erp.common.mq.factory.MqConfigHelper;
import com.estone.erp.common.mq.factory.MqConfigParameters;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * MQ 配置注解助手类
 * 
 * @Description: 处理 @MqConfig 注解的解析和配置创建
 * @Author: AI Assistant
 * @Date: 2024/12/19
 */
@Slf4j
@Component
public class MqConfigAnnotationHelper {
    
    @Autowired
    private Environment environment;
    
    @Autowired
    private MqConfigHelper mqConfigHelper;
    
    /**
     * 处理 @MqConfig 注解，创建并注册 MQ 配置
     * 
     * @param mqConfig MQ 配置注解
     * @param messageListener 消息监听器实例
     */
    public void processMqConfigAnnotation(MqConfig mqConfig, ChannelAwareMessageListener messageListener) {
        log.info("开始处理 @MqConfig 注解: {}", mqConfig.beanNamePrefix());
        
        try {
            // 解析配置参数
            MqConfigParameters parameters = parseAnnotationToParameters(mqConfig, messageListener);
            
            // 验证配置参数
            validateAnnotationParameters(mqConfig, parameters);
            
            // 创建并注册 MQ 对象
            mqConfigHelper.createAndRegisterMqConfig(
                    parameters.getVirtualHost(),
                    parameters.getQueueName(),
                    parameters.getRoutingKey(),
                    parameters.getExchange(),
                    parameters.getConsumers(),
                    parameters.getPrefetchCount(),
                    parameters.getMessageListener(),
                    parameters.isListenerEnabled(),
                    parameters.getBeanNamePrefix()
            );
            
            log.info("@MqConfig 注解处理完成: {}", mqConfig.beanNamePrefix());
            
        } catch (Exception e) {
            log.error("处理 @MqConfig 注解时发生错误: {}", mqConfig.beanNamePrefix(), e);
            throw new RuntimeException("MQ 配置注解处理失败: " + mqConfig.beanNamePrefix(), e);
        }
    }
    
    /**
     * 将注解参数解析为 MqConfigParameters 对象
     * 
     * @param mqConfig MQ 配置注解
     * @param messageListener 消息监听器实例
     * @return MqConfigParameters 配置参数对象
     */
    private MqConfigParameters parseAnnotationToParameters(MqConfig mqConfig, ChannelAwareMessageListener messageListener) {
        // 从环境变量中解析配置值
        int consumers = resolveIntProperty(mqConfig.consumersValueName(), "消费者数量");
        int prefetchCount = resolveIntProperty(mqConfig.prefetchCountValueName(), "预取数量");
        boolean listenerEnabled = resolveBooleanProperty(mqConfig.listenerEnabledValueName(), "监听器开关");
        
        log.debug("解析注解配置参数: beanNamePrefix={}, consumers={}, prefetchCount={}, listenerEnabled={}", 
                mqConfig.beanNamePrefix(), consumers, prefetchCount, listenerEnabled);
        
        return MqConfigParameters.builder()
                .virtualHost(mqConfig.virtualHost())
                .queueName(mqConfig.queueName())
                .routingKey(mqConfig.routingKey())
                .exchange(mqConfig.exchange())
                .consumers(consumers)
                .prefetchCount(prefetchCount)
                .messageListener(messageListener)
                .listenerEnabled(listenerEnabled)
                .beanNamePrefix(mqConfig.beanNamePrefix())
                .durable(mqConfig.durable())
                .exclusive(mqConfig.exclusive())
                .autoDelete(mqConfig.autoDelete())
                .build();
    }
    
    /**
     * 解析整型配置属性
     * 
     * @param propertyExpression 属性表达式，例如：${yml-config.exampleMqConsumers}
     * @param propertyDescription 属性描述，用于错误信息
     * @return 解析后的整型值
     */
    private int resolveIntProperty(String propertyExpression, String propertyDescription) {
        try {
            String resolvedValue = environment.resolvePlaceholders(propertyExpression);
            int value = Integer.parseInt(resolvedValue);
            
            if (value <= 0) {
                throw new IllegalArgumentException(propertyDescription + " 必须大于 0，当前值: " + value);
            }
            
            log.debug("解析{}配置: {} -> {}", propertyDescription, propertyExpression, value);
            return value;
            
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("无法解析" + propertyDescription + "配置: " + propertyExpression, e);
        }
    }
    
    /**
     * 解析布尔型配置属性
     * 
     * @param propertyExpression 属性表达式，例如：${yml-config.exampleMqListener}
     * @param propertyDescription 属性描述，用于错误信息
     * @return 解析后的布尔值
     */
    private boolean resolveBooleanProperty(String propertyExpression, String propertyDescription) {
        try {
            String resolvedValue = environment.resolvePlaceholders(propertyExpression);
            boolean value = Boolean.parseBoolean(resolvedValue);
            
            log.debug("解析{}配置: {} -> {}", propertyDescription, propertyExpression, value);
            return value;
            
        } catch (Exception e) {
            throw new IllegalArgumentException("无法解析" + propertyDescription + "配置: " + propertyExpression, e);
        }
    }
    
    /**
     * 验证注解配置参数的完整性
     * 
     * @param mqConfig MQ 配置注解
     * @param parameters 解析后的配置参数
     */
    private void validateAnnotationParameters(MqConfig mqConfig, MqConfigParameters parameters) {
        // 验证基本字符串参数
        validateStringParameter(mqConfig.virtualHost(), "虚拟主机名称");
        validateStringParameter(mqConfig.queueName(), "队列名称");
        validateStringParameter(mqConfig.routingKey(), "路由键");
        validateStringParameter(mqConfig.exchange(), "交换机名称");
        validateStringParameter(mqConfig.beanNamePrefix(), "Bean 名称前缀");
        
        // 验证配置属性表达式
        validateStringParameter(mqConfig.consumersValueName(), "消费者数量配置属性名");
        validateStringParameter(mqConfig.prefetchCountValueName(), "预取数量配置属性名");
        validateStringParameter(mqConfig.listenerEnabledValueName(), "监听器开关配置属性名");
        
        // 验证消息监听器
        if (parameters.getMessageListener() == null) {
            throw new IllegalArgumentException("消息监听器不能为空");
        }
        
        log.debug("注解配置参数验证通过: {}", mqConfig.beanNamePrefix());
    }
    
    /**
     * 验证字符串参数是否为空
     * 
     * @param value 参数值
     * @param parameterName 参数名称
     */
    private void validateStringParameter(String value, String parameterName) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException(parameterName + " 不能为空");
        }
    }
}
