package com.estone.erp.common.mq.factory.annotation;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

/**
 * MQ 配置注解处理器
 * 
 * @Description: 自动扫描和处理标注了 @MqConfig 注解的 Bean，创建相应的 MQ 配置
 * @Author: AI Assistant
 * @Date: 2024/12/19
 */
@Slf4j
@Component
public class MqConfigAnnotationProcessor implements BeanPostProcessor {
    
    @Autowired
    private MqConfigAnnotationHelper mqConfigAnnotationHelper;
    
    /**
     * Bean 初始化前的处理
     * 
     * @param bean Bean 实例
     * @param beanName Bean 名称
     * @return 处理后的 Bean 实例
     * @throws BeansException Bean 处理异常
     */
    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }
    
    /**
     * Bean 初始化后的处理
     * 扫描标注了 @MqConfig 注解的 Bean，并自动创建 MQ 配置
     * 
     * @param bean Bean 实例
     * @param beanName Bean 名称
     * @return 处理后的 Bean 实例
     * @throws BeansException Bean 处理异常
     */
    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        // 检查 Bean 是否标注了 @MqConfig 注解
        MqConfig mqConfig = AnnotationUtils.findAnnotation(bean.getClass(), MqConfig.class);
        
        if (mqConfig != null) {
            log.info("发现标注了 @MqConfig 注解的 Bean: {} ({})", beanName, bean.getClass().getSimpleName());
            
            // 验证 Bean 是否实现了 ChannelAwareMessageListener 接口
            if (!(bean instanceof ChannelAwareMessageListener)) {
                String errorMsg = String.format(
                    "标注了 @MqConfig 注解的类必须实现 ChannelAwareMessageListener 接口: %s", 
                    bean.getClass().getName()
                );
                log.error(errorMsg);
                throw new IllegalArgumentException(errorMsg);
            }
            
            try {
                // 处理注解配置
                ChannelAwareMessageListener messageListener = (ChannelAwareMessageListener) bean;
                mqConfigAnnotationHelper.processMqConfigAnnotation(mqConfig, messageListener);
                
                log.info("成功处理 @MqConfig 注解: {} -> {}", beanName, mqConfig.beanNamePrefix());
                
            } catch (Exception e) {
                String errorMsg = String.format("处理 @MqConfig 注解失败: %s", beanName);
                log.error(errorMsg, e);
                throw new RuntimeException(errorMsg, e);
            }
        }
        
        return bean;
    }
}
