package com.estone.erp.common.mq;


/***
 * 消息队列声明
 *
 * <AUTHOR>
 *
 */
public class PublishQueues {

    /**
     * 产品系统推送sku标题描述
     */
    public static final String PRODUCT_PUBLISH_TITLE_DES_QUEUE = "PRODUCT_PUBLISH_TITLE_DES_QUEUE";

    /**
     * 产品系统转正常推送刊登系统队列
     */
    public static final String PRODUCT_CHANGE_SALE_SKU_2_PUBLISH_QUEUE = "PRODUCT_CHANGE_SALE_SKU_2_PUBLISH_QUEUE";

    /**
     * 产品系统转正常推送刊登系统队列速卖通
     */
    public static final String PRODUCT_CHANGE_SALE_SKU_2_PUBLISH_SMT_QUEUE = "PRODUCT_CHANGE_SALE_SKU_2_PUBLISH_SMT_QUEUE";

    /**
     * 产品类目新增队列
     */
    public static final String PUSH_NEW_CATEGORY_TO_PUBLISH_QUEUE = "PUSH_NEW_CATEGORY_TO_PUBLISH_QUEUE";

    /**
     * 产品系统转正常推送刊登系统队列 EBAY
     */
    public static final String PRODUCT_CHANGE_SALE_SKU_2_PUBLISH_EBAY_QUEUE = "PRODUCT_CHANGE_SALE_SKU_2_PUBLISH_EBAY_QUEUE";

    /**
     * 产品系统转正常推送刊登系统队列 SHOPEE
     */
    public static final String PRODUCT_CHANGE_SALE_SKU_2_PUBLISH_SHOPEE_QUEUE = "PRODUCT_CHANGE_SALE_SKU_2_PUBLISH_SHOPEE_QUEUE";

    /**
     * 产品系统推送的更新标题，描述，图片，重量的SKU，队列
     */
    public static final String PRODUCT_DATA_CHANGE_2_PUBLISH_QUEUE = "PRODUCT_DATA_CHANGE_2_PUBLISH_QUEUE";
    /**
     * 产品系统推送的更新标题，描述，图片，重量的SKU，队列路由key
     */
    public static final String PRODUCT_CHANGE_SALE_SKU_2_PUBLISH_KEY = "PRODUCT_CHANGE_SALE_SKU_2_PUBLISH_KEY";

    /**
     * 产品系统推送冠通产品 上架变下架sku
     */
    public static final String PRODUCT_2_PUBLISH_GT_PRODUCT_QUEUE = "PRODUCT_2_PUBLISH_GT_PRODUCT_QUEUE";

    /**
     * 爬虫系统推送 刊登系统 ebay刊登额度 队列和路由key
     */
    public static final String CRAWL_2_PUBLISH_EBAY_PUBLISHING_QUOTA_QUEUE = "CRAWL_2_PUBLISH_EBAY_PUBLISHING_QUOTA_QUEUE";
    public static final String CRAWL_2_PUBLISH_EBAY_PUBLISHING_QUOTA_KEY = "CRAWL_2_PUBLISH_EBAY_PUBLISHING_QUOTA_KEY";

    /**
     * 推送Amazon sku 活跃量到订单系统 队列
     */
    public static final String PUSH_PUBLISH_ACTIVE_SKU_DATA_QUEUE = "PUSH_PUBLISH_ACTIVE_SKU_DATA_QUEUE";
    /**
     * 推送Amazon sku 活跃量到订单系统 路由key
     */
    public static final String PUSH_PUBLISH_ACTIVE_SKU_DATA_KEY = "PUSH_PUBLISH_ACTIVE_SKU_DATA_KEY";

    /** (刊登系统)亚马逊提交处理报告请求返回结果队列 */
    public static final  String PUBLISH_AMAZON_REQUEST_REPORT_RESULT_QUEUE = "PUBLISH_AMAZON_REQUEST_REPORT_RESULT_QUEUE";

    /** (刊登系统)亚马逊提交处理报告请求返回结果队列key */
    public static final  String PUBLISH_AMAZON_REQUEST_REPORT_RESULT_KEY = "PUBLISH_AMAZON_REQUEST_REPORT_RESULT_KEY";

    /** (刊登系统)亚马逊提交处理报告请求返回结果队列 */
    public static final  String PUBLISH_AMAZON_REQUEST_FEEDS_RESULT_QUEUE = "PUBLISH_AMAZON_REQUEST_FEEDS_RESULT_QUEUE";

    /** (刊登系统)亚马逊提交处理报告请求返回结果队列key */
    public static final  String PUBLISH_AMAZON_REQUEST_FEEDS_RESULT_KEY = "PUBLISH_AMAZON_REQUEST_FEEDS_RESULT_KEY";


    /** (刊登系统)处理报告请求返回结果回写到Listing 队列 */
    public static final  String AMAZON_PROCESS_REPORT_LISTING_QUEUE = "AMAZON_PROCESS_REPORT_LISTING_QUEUE";

    /** (刊登系统)处理报告请求返回结果回写到Listing队列key */
    public static final  String AMAZON_PROCESS_REPORT_LISTING_KEY = "AMAZON_PROCESS_REPORT_LISTING_KEY";

    /** (刊登系统)亚马逊在线listing 毛利、毛利率计算 队列 */
    public static final  String PUBLISH_AMAZON_LISTING_GROSS_PRAFIT_QUEUE = "PUBLISH_AMAZON_LISTING_GROSS_PRAFIT_QUEUE";
    public static final  String PUBLISH_AMAZON_LISTING_GROSS_PRAFIT_KEY = "PUBLISH_AMAZON_LISTING_GROSS_PRAFIT_KEY";

    /** (刊登系统)亚马逊在线listing品牌同步 队列 */
    public static final  String PUBLISH_AMAZON_SYNC_LISTING_BRAND_QUEUE = "PUBLISH_AMAZON_SYNC_LISTING_BRAND_QUEUE";
    public static final  String PUBLISH_AMAZON_SYNC_LISTING_BRAND_KEY = "PUBLISH_AMAZON_SYNC_LISTING_BRAND_KEY";

    /** (刊登系统)亚马逊在线listing父asin同步 队列 */
    public static final  String PUBLISH_AMAZON_SYNC_LISTING_PARENT_ASIN_QUEUE = "PUBLISH_AMAZON_SYNC_LISTING_PARENT_ASIN_QUEUE";
    public static final  String PUBLISH_AMAZON_SYNC_LISTING_PARENT_ASIN_KEY = "PUBLISH_AMAZON_SYNC_LISTING_PARENT_ASIN_KEY";

    /** (刊登系统)亚马逊在线listing ListingItem接口同步 队列 */
    public static final  String PUBLISH_AMAZON_SYNC_LISTINGS_ITEM_MSG_QUEUE = "PUBLISH_AMAZON_SYNC_LISTINGS_ITEM_MSG_QUEUE";
    public static final  String PUBLISH_AMAZON_SYNC_LISTINGS_ITEM_MSG_KEY = "PUBLISH_AMAZON_SYNC_LISTINGS_ITEM_MSG_KEY";


    /**
     * smt tidb数据区域调价开始 队列  smt - tidb  第一步 smt 定时任务 请求区域价格
     */
    public static final String SMT_UPDATE_TIDB_AREA_START_QUEUE = "SMT_UPDATE_TIDB_AREA_START_QUEUE";
    /**
     * smt tidb数据区域调价开始 路由key smt - tidb 第一步 smt 定时任务 请求区域价格
     */
    public static final String SMT_UPDATE_TIDB_AREA_START_ROUTE_KEY = "SMT_UPDATE_TIDB_AREA_START_ROUTE_KEY";

    /**
     * tidb数据区域调价回调-刊登smt队列  tidb- smt  第二步 接收 tidb 改价数据
     */
    public static final String RECEIVE_CALL_BACK_SMT_TIDB_AREA_QUEUE = "RECEIVE_CALL_BACK_SMT_TIDB_AREA_QUEUE";

    /**
     * smt tidb数据区域调价结果 队列  smt - tidb 第三步 改价结果 传给tidb
     */
    public static final String SMT_UPDATE_TIDB_AREA_RESULT_QUEUE = "SMT_UPDATE_TIDB_AREA_RESULT_QUEUE";
    /**
     * smt tidb数据区域调价结果 路由key  smt - tidb 第三步 改价结果 传给tidb
     */
    public static final String SMT_UPDATE_TIDB_AREA_RESULT_ROUTE_KEY = "SMT_UPDATE_TIDB_AREA_RESULT_ROUTE_KEY";

    /**
     * smt 推送过期品牌队列
     */
    public static final String PUSH_SMT_BRAND_QUEUE = "PUSH_SMT_BRAND_QUEUE";
    /**
     * ssmt 推送过期品牌路由key
     */
    public static final String PUSH_SMT_BRAND_ROUTE_KEY = "PUSH_SMT_BRAND_ROUTE_KEY";

    /**
     * smt 大数据规则修改POP库存队列
     */
    public static final String SMT_RULE_UPDATE_POP_STOCK_QUEUE = "SMT_RULE_UPDATE_POP_STOCK_QUEUE";
    /**
     * smt 大数据规则修改POP库存路由key
     */
    public static final String SMT_RULE_UPDATE_POP_STOCK_ROUTE_KEY = "SMT_RULE_UPDATE_POP_STOCK_ROUTE_KEY";

    /**
     * smt 大数据规则修改半托管库存队列
     */
    public static final String SMT_RULE_UPDATE_HALF_TG_STOCK_QUEUE = "SMT_RULE_UPDATE_HALF_TG_STOCK_QUEUE";
    /**
     * smt 大数据规则修改半托管库存路由key
     */
    public static final String SMT_RULE_UPDATE_HALF_TG_STOCK_ROUTE_KEY = "SMT_RULE_UPDATE_HALF_TG_STOCK_ROUTE_KEY";


    /**
     * smt 修改库存队列
     */
    public static final String SMT_UPDATE_STOCK_QUEUE = "SMT_UPDATE_STOCK_QUEUE";
    /**
     * smt 修改库存路由key
     */
    public static final String SMT_UPDATE_STOCK_ROUTE_KEY = "SMT_UPDATE_STOCK_ROUTE_KEY";

    /**
     * smt 修改半托管库存队列(每天定时任务专用)
     */
    public static final String SMT_TIME_UPDATE_HALF_STOCK_QUEUE = "SMT_TIME_UPDATE_HALF_STOCK_QUEUE";
    /**
     * pop指定sku刊登队列
     */
    public static final String POP_TO_SKU_PUBLISH_QUEUE = "POP_TO_SKU_PUBLISH_QUEUE";
    /**
     * pop指定sku 刊登队列 路由
     */
    public static final String SMT_POP_TO_SKU_ROUTE_KEY = "SMT_POP_TO_SKU_ROUTE_KEY";

    /**
     * smt 修改半托管库存路由key(每天定时任务专用)
     */
    public static final String SMT_TIME_UPDATE_HALF_STOCK_ROUTE_KEY = "SMT_TIME_UPDATE_HALF_STOCK_ROUTE_KEY";


    /**
     * smt 半托管立即加入队列
     */
    public static final String SMT_HALF_JOIN_ITEM_QUEUE = "SMT_HALF_JOIN_ITEM_QUEUE";
    /**
     * smt 半托管立即加入路由key
     */
    public static final String SMT_HALF_JOIN_ITEM_ROUTE_KEY = "SMT_HALF_JOIN_ITEM_ROUTE_KEY";


    /**
     * smt 半托管预约队列
     */
    public static final String SMT_HALF_PRE_ITEM_QUEUE = "SMT_HALF_PRE_ITEM_QUEUE";
    /**
     * smt 半托管路由key
     */
    public static final String SMT_HALF_PRE_ITEM_ROUTE_KEY = "SMT_HALF_PRE_ITEM_ROUTE_KEY";


    /**
     * smt 刊登队列
     */
    public static final String SMT_PUBLISH_QUEUE = "SMT_PUBLISH_QUEUE";
    /**
     * smt 刊登队列 路由
     */
    public static final String SMT_PUBLISH_ROUTE_KEY = "SMT_PUBLISH_ROUTE_KEY";

    /**
     * smt TG刊登队列
     */
    public static final String SMT_TG_PUBLISH_QUEUE = "SMT_TG_PUBLISH_QUEUE";
    /**
     * smt TG刊登队列 路由
     */
    public static final String SMT_TG_PUBLISH_ROUTE_KEY = "SMT_TG_PUBLISH_ROUTE_KEY";

    /**
     * smt 产品分类标签队列
     */
    public static final String SMT_PRODUCT_CATEGORY_LABEL_QUEUE = "SMT_PRODUCT_CATEGORY_LABEL_QUEUE";
    /**
     * smt产品分类标签 路由
     */
    public static final String SMT_PRODUCT_CATEGORY_LABEL_ROUTE_KEY = "SMT_PRODUCT_CATEGORY_LABEL_ROUTE_KEY";

    /**
     * smt excel队列
     */
    public static final String SMT_EXCEL_QUEUE = "SMT_EXCEL_QUEUE";
    /**
     * smt excle队列 路由
     */
    public static final String SMT_EXCEL_ROUTE_KEY = "SMT_EXCEL_ROUTE_KEY";

    /**
     * smt 修改属性队列
     */
    public static final String SMT_UPDATE_ATTR_QUEUE = "SMT_UPDATE_ATTR_QUEUE";
    /**
     * smt 修改属性队列 路由
     */
    public static final String SMT_UPDATE_ATTR_ROUTE_KEY = "SMT_UPDATE_ATTR_ROUTE_KEY";

    /**
     * smt 自动加入半托管队列 vh-publish-all 文件夹
     */
    public static final String SMT_ACCOUNT_AUTO_HALF_JOIN_QUEUE = "SMT_ACCOUNT_AUTO_HALF_JOIN_QUEUE";
    /**
     * smt 自动加入半托管 路由
     */
    public static final String SMT_ACCOUNT_AUTO_HALF_JOIN_ROUTE_KEY = "SMT_ACCOUNT_AUTO_HALF_JOIN_ROUTE_KEY";

    /**
     * smt 自动下架或者删除队列 vh-publish-all 文件夹 第一步店铺
     */
    public static final String SMT_ACCOUNT_AUTO_OFF_QUEUE = "SMT_ACCOUNT_AUTO_OFF_QUEUE";
    /**
     * smt 自动下架或者删除 路由
     */
    public static final String SMT_ACCOUNT_AUTO_OFF_ROUTE_KEY = "SMT_ACCOUNT_AUTO_OFF_ROUTE_KEY";


    /**
     * smt 自动下架或者删除队列 item vh-publish-all 文件夹 第二步 item
     */
    public static final String SMT_POP_ITEM_AUTO_OFF_QUEUE = "SMT_POP_ITEM_AUTO_OFF_QUEUE";
    /**
     * smt 自动下架或者删除 item路由
     */
    public static final String SMT_POP_ITEM_AUTO_OFF_ROUTE_KEY = "SMT_POP_ITEM_AUTO_OFF_ROUTE_KEY";


    /**
     * smt 自动店铺刊登队列
     */
    public static final String SMT_AUTO_ACCOUNT_PUBLISH_QUEUE = "SMT_AUTO_ACCOUNT_PUBLISH_QUEUE";
    /**
     * smt 自动店铺刊登队列 路由
     */
    public static final String SMT_AUTO_ACCOUNT_ROUTE_KEY = "SMT_AUTO_ACCOUNT_ROUTE_KEY";

    /**
     * smt 自动店铺刊登队列 路由
     */
    public static final String SMT_POP_PUBLISH_QUEUE = "SMT_POP_PUBLISH_QUEUE";
    public static final String SMT_POP_PUBLISH_ROUTE_KEY = "SMT_POP_PUBLISH_ROUTE_KEY";


    /**
     * smt admin范本属性同步到list
     */
    public static final String SMT_ADMIN_ATTR_SYNCH_TO_LIST_QUEUE = "SMT_ADMIN_ATTR_SYNCH_TO_LIST_QUEUE";
    /**
     * smt admin范本属性同步到list
     */
    public static final String SMT_ADMIN_ATTR_SYNCH_TO_LIST_ROUTE_KEY = "SMT_ADMIN_ATTR_SYNCH_TO_LIST_ROUTE_KEY";


    /**
     * smt pop配置调价
     */
    public static final String SMT_POP_ADJUST_PRICE_QUEUE = "SMT_POP_ADJUST_PRICE_QUEUE";
    /**
     * smt pop配置调价
     */
    public static final String SMT_POP_ADJUST_PRICE_ROUTE_KEY = "SMT_POP_ADJUST_PRICE_ROUTE_KEY";

    /**
     * smt 阶梯价格
     */
    public static final String SMT_LADDER_PRICE_QUEUE = "SMT_LADDER_PRICE_QUEUE";
    /**
     * smt 阶梯价格
     */
    public static final String SMT_LADDER_PRICE_ROUTE_KEY = "SMT_LADDER_PRICE_ROUTE_KEY";


    /**
     * smt 税费
     */
    public static final String SMT_TAX_TYPE_QUEUE = "SMT_TAX_TYPE_QUEUE";
    /**
     * smt 税费
     */
    public static final String SMT_TAX_TYPE_ROUTE_KEY = "SMT_TAX_TYPE_ROUTE_KEY";


    /**
     * smt 春节库存 休假改0
     */
    public static final String SMT_HOLIDAY_STOCK_QUEUE = "SMT_HOLIDAY_STOCK_QUEUE";
    /**
     * smt 春节库存 休假改0
     */
    public static final String SMT_HOLIDAY_STOCK_ROUTE_KEY = "SMT_HOLIDAY_STOCK_ROUTE_KEY";

    /**
     * smt 春节库存 可用-待发 < 1 改0
     */
    public static final String SMT_HOLIDAY_STOCK_TWO_QUEUE = "SMT_HOLIDAY_STOCK_TWO_QUEUE";
    /**
     * smt 春节库存 可用-待发 < 1 改0
     */
    public static final String SMT_HOLIDAY_STOCK_TWO_ROUTE_KEY = "SMT_HOLIDAY_STOCK_TWO_ROUTE_KEY";


    /**
     * smt 修改CE资质队列（tg）
     */
    public static final String SMT_UPDATE_TG_QUALIFICATION_QUEUE = "SMT_UPDATE_TG_QUALIFICATION_QUEUE";
    /**
     * smt 修改CE资质队列 路由（tg）
     */
    public static final String SMT_UPDATE_TG_QUALIFICATION_ROUTE_KEY = "SMT_UPDATE_TG_QUALIFICATION_ROUTE_KEY";


    /**
     * smt 修改CE资质队列
     */
    public static final String SMT_UPDATE_QUALIFICATION_QUEUE = "SMT_UPDATE_QUALIFICATION_QUEUE";
    /**
     * smt 修改CE资质队列 路由
     */
    public static final String SMT_UPDATE_QUALIFICATION_ROUTE_KEY = "SMT_UPDATE_QUALIFICATION_ROUTE_KEY";


    /**
     * smt 修改CE资质队列(定时)
     */
    public static final String SMT_UPDATE_QUALIFICATION_TIMING_QUEUE = "SMT_UPDATE_QUALIFICATION_TIMING_QUEUE";
    /**
     * smt 修改CE资质队列 路由(定时)
     */
    public static final String SMT_UPDATE_QUALIFICATION_TIMING_ROUTE_KEY = "SMT_UPDATE_QUALIFICATION_TIMING_ROUTE_KEY";


    /**
     * smt 删除es产品队列
     */
    public static final String SMT_DELETE_ES_PRODUCT_QUEUE = "SMT_DELETE_ES_PRODUCT_QUEUE";
    /**
     * smt 删除es产品队列 路由
     */
    public static final String SMT_DELETE_ES_PRODUCT_ROUTE_KEY = "SMT_DELETE_ES_PRODUCT_ROUTE_KEY";

    /**
     * smt 产品浏览量队列
     */
    public static final String SMT_PRODUCT_VIEW_QUEUE = "SMT_PRODUCT_VIEW_QUEUE";
    /**
     * smt产品浏览量队列 路由
     */
    public static final String SMT_PRODUCT_VIEW_ROUTE_KEY = "SMT_PRODUCT_VIEW_ROUTE_KEY";

    /**
     * smt 产品库存队列
     */
    public static final String SMT_PRODUCT_STOCK_QUEUE = "SMT_PRODUCT_STOCK_QUEUE";
    /**
     * smt产品库存队列 路由
     */
    public static final String SMT_PRODUCT_STOCK_ROUTE_KEY = "SMT_PRODUCT_STOCK_ROUTE_KEY";

    /**
     * smt亏损订单 路由
     */
    public static final String PUSH_SMT_DEFICIT_INFO_TO_PUBLISH = "PUSH_SMT_DEFICIT_INFO_TO_PUBLISH";

    /**
     * smt 上传视频 队列
     */
    public static final String SMT_UPLOAD_VIDEO_QUEUE = "SMT_UPLOAD_VIDEO_QUEUE";

    /**
     * smt 上传视频 队列KEY
     */
    public static final  String SMT_UPLOAD_VIDEO_KEY = "SMT_UPLOAD_VIDEO_KEY";

    /**
     * 平台回调消息-刊登smt队列
     */
    public static final String RECEIVE_CALL_BACK_MSG_PUBLISH_SMT_QUEUE = "RECEIVE_CALL_BACK_MSG_PUBLISH_SMT_QUEUE";

    /**
     * 爬虫推送速卖通EPR收费数据
     */
    public static final String SMT_REPTILE_EPR_PUSH_QUEUE = "SMT_REPTILE_EPR_PUSH_QUEUE";
    public static final String SMT_REPTILE_EPR_PUSH_KEY = "SMT_REPTILE_EPR_PUSH_KEY";

    /**
     * smt 校验侵权词 队列
     */
    public static final String SMT_ITEM_CHECK_INFRINGING_WORDS_QUEUE = "SMT_ITEM_CHECK_INFRINGING_WORDS_QUEUE";

    /**
     * smt 校验侵权词 队列KEY
     */
    public static final  String SMT_ITEM_CHECK_INFRINGING_WORDS_KEY = "SMT_ITEM_CHECK_INFRINGING_WORDS_KEY";

    /**
     * smt生成视频路由
     */
    public static final String SMT_GENERATE_VIDEO_KEY = "SMT_GENERATE_VIDEO_KEY";

    /**
     * smt生成视频队列
     */
    public static final String SMT_GENERATE_VIDEO_QUEUE = "SMT_GENERATE_VIDEO_QUEUE";

    /**
     * smt刊登成功后续处理队列key
     */
    public static final String SMT_POP_PUBLISH_SUCCESS_KEY = "SMT_POP_PUBLISH_SUCCESS_KEY";

    /**
     * smt刊登成功后续处理队列
     */
    public static final String SMT_POP_PUBLISH_SUCCESS_QUEUE = "SMT_POP_PUBLISH_SUCCESS_QUEUE";


    /**
     * smt更新欧盟负责人队列key
     */
    public static final String SMT_UPDATE_MSR_EU_ID_KEY = "SMT_UPDATE_MSR_EU_ID_KEY";

    /**
     * smt更新欧盟负责人队列
     */
    public static final String SMT_UPDATE_MSR_EU_ID_QUEUE = "SMT_UPDATE_MSR_EU_ID_QUEUE";


    /**
     * smt更新制造商队列key
     */
    public static final String SMT_UPDATE_MANUFACTURER_KEY = "SMT_UPDATE_MANUFACTURER_KEY";

    /**
     * smt更新制造商队列
     */
    public static final String SMT_UPDATE_MANUFACTURER_QUEUE = "SMT_UPDATE_MANUFACTURER_QUEUE";


    /**
     * smt更新制造商队列key(全托管)
     */
    public static final String SMT_UPDATE_TG_MANUFACTURER_KEY = "SMT_UPDATE_TG_MANUFACTURER_KEY";

    /**
     * smt更新制造商队列(全托管)
     */
    public static final String SMT_UPDATE_TG_MANUFACTURER_QUEUE = "SMT_UPDATE_TG_MANUFACTURER_QUEUE";


    /**
     * smt更新流量队列
     */
    public static final String SMT_UPDATE_PV_KEY = "SMT_UPDATE_PV_KEY";

    /**
     * smt更新流量队列
     */
    public static final String SMT_UPDATE_PV_QUEUE = "SMT_UPDATE_PV_QUEUE";

    /**
     * smt新增半托管国家队列key
     */
    public static final String SMT_ADD_HALF_COUNTRY_KEY = "SMT_ADD_HALF_COUNTRY_KEY";

    /**
     * smt新增半托管国家队列
     */
    public static final String SMT_ADD_HALF_COUNTRY_QUEUE = "SMT_ADD_HALF_COUNTRY_QUEUE";

    /**
     * smt 删除半托管国家队列key
     */
    public static final String SMT_DELETE_HALF_COUNTRY_KEY = "SMT_DELETE_HALF_COUNTRY_KEY";

    /**
     * smt 删除半托管国家队列
     */
    public static final String SMT_DELETE_HALF_COUNTRY_QUEUE = "SMT_DELETE_HALF_COUNTRY_QUEUE";


    /**
     * smt同步半托管队列key
     */
    public static final String SMT_SYNCH_HALF_ITEM_KEY = "SMT_SYNCH_HALF_ITEM_KEY";

    /**
     * smt同步半托管队列
     */
    public static final String SMT_SYNCH_HALF_ITEM_QUEUE = "SMT_SYNCH_HALF_ITEM_QUEUE";

    /**
     * smt 大数据待发数量变化sku队列
     */
    public static final String SMT_BIG_DATA_PUSH_PENDING_SKU_QUEUE = "SMT_BIG_DATA_PUSH_PENDING_SKU_QUEUE";

    /**
     * ebay 刊登队列
     */
    public static final String EBAY_PUBLISH_QUEUE = "EBAY_PUBLISH_QUEUE";
    /**
     * ebay 刊登队列 路由
     */
    public static final String EBAY_PUBLISH_ROUTE_KEY = "EBAY_PUBLISH_ROUTE_KEY";

    /** (刊登系统)ebay在线listing 毛利、毛利率计算 队列 */
    public static final  String PUBLISH_EBAY_LISTING_GROSS_PRAFIT_QUEUE = "PUBLISH_EBAY_LISTING_GROSS_PRAFIT_QUEUE";
    /** (刊登系统)ebay在线listing 毛利、毛利率计算 队列key */
    public static final  String PUBLISH_EBAY_LISTING_GROSS_PRAFIT_KEY = "PUBLISH_EBAY_LISTING_GROSS_PRAFIT_KEY";

    /**
     * ebay execl修改item 队列
     */
    public static final String EBAY_EXECL_UPATE_ITEM_QUEUE = "EBAY_EXECL_UPATE_ITEM_QUEUE";

    /**
     * ebay execl修改item key
     */
    public static final  String EBAY_EXECL_UPATE_ITEM_KEY = "EBAY_EXECL_UPATE_ITEM_KEY";

    /**
     * ebay execl修改item 价格库存 队列
     */
    public static final String EBAY_EXECL_UPATE_ITEM_PRICE_INVENTORY_QUEUE = "EBAY_EXECL_UPATE_ITEM_PRICE_INVENTORY_QUEUE";

    /**
     * ebay execl修改item 价格库存 key
     */
    public static final  String EBAY_EXECL_UPATE_ITEM_PRICE_INVENTORY_KEY = "EBAY_EXECL_UPATE_ITEM_PRICE_INVENTORY_KEY";

    /**
     * ebay 同步item 队列
     */
    public static final String EBAY_SYNC_ITEM_QUEUE = "EBAY_SYNC_ITEM_QUEUE";

    /**
     * ebay 同步item key
     */
    public static final  String EBAY_SYNC_ITEM_KEY = "EBAY_SYNC_ITEM_KEY";

    /**
     * ebay 同步item 最近60天刊登 队列
     */
    public static final String EBAY_SYNC_ITEM_SUMMARYS_QUEUE = "EBAY_SYNC_ITEM_SUMMARYS_QUEUE";

    /**
     * ebay 同步item 最近60天刊登 key
     */
    public static final  String EBAY_SYNC_ITEM_SUMMARYS_KEY = "EBAY_SYNC_ITEM_SUMMARYS_KEY";

    /**
     * ebay 在线列表导出excel 队列
     */
    public static final String EBAY_EXCEL_ITEM_DOWNLOAD_QUEUE = "EBAY_EXCEL_ITEM_DOWNLOAD_QUEUE";

    /**
     * ebay 在线列表导出excel key
     */
    public static final  String EBAY_EXCEL_ITEM_DOWNLOAD_KEY = "EBAY_EXCEL_ITEM_DOWNLOAD_KEY";

    /**
     * ebay 刷新链接费率 队列
     */
    public static final String EBAY_REFRESH_ITEM_RATE_TABLE_QUEUE = "EBAY_REFRESH_ITEM_RATE_TABLE_QUEUE";

    /**
     * ebay 刷新链接费率 key
     */
    public static final  String EBAY_REFRESH_ITEM_RATE_TABLE_KEY = "EBAY_REFRESH_ITEM_RATE_TABLE_KEY";

    /**
     * ebay 长尾刊登导出excel 队列
     */
    public static final String EBAY_EXCEL_LONG_TAIL_DOWNLOAD_QUEUE = "EBAY_EXCEL_LONG_TAIL_DOWNLOAD_QUEUE";

    /**
     * ebay 长尾刊登导出excel key
     */
    public static final  String EBAY_EXCEL_LONG_TAIL_DOWNLOAD_KEY = "EBAY_EXCEL_LONG_TAIL_DOWNLOAD_KEY";

    /**
     * ebay 修改续件运费 队列
     */
    public static final String EBAY_UPDATE_ADDITIONAL_SHIPPING_COST_QUEUE = "EBAY_UPDATE_ADDITIONAL_SHIPPING_COST_QUEUE";

    /**
     * ebay 修改续件运费 key
     */
    public static final  String EBAY_UPDATE_ADDITIONAL_SHIPPING_COST_KEY = "EBAY_UPDATE_ADDITIONAL_SHIPPING_COST_KEY";

    /**
     * ebay 临时 队列
     */
    public static final String EBAY_TEMPORARY_QUEUE = "EBAY_TEMPORARY_QUEUE";

    /**
     * ebay 临时 队列
     */
    public static final  String EBAY_TEMPORARY_KEY = "EBAY_TEMPORARY_KEY";

    /**
     * ebay 关闭国际运费 队列
     */
    public static final String EBAY_ClOSE_INTEL_SHIP_QUEUE = "EBAY_ClOSE_INTEL_SHIP_QUEUE";

    /**
     * ebay 关闭国际运费 队列
     */
    public static final  String EBAY_ClOSE_INTEL_SHIP_QUEUE_KEY = "EBAY_ClOSE_INTEL_SHIP_QUEUE_KEY";
    public static final String EBAY_ClOSE_INTEL_BY_CONFIG_SHIP_QUEUE = "EBAY_ClOSE_INTEL_BY_CONFIG_SHIP_QUEUE";
    public static final  String EBAY_ClOSE_INTEL_BY_CONFIG_SHIP_QUEUE_KEY = "EBAY_ClOSE_INTEL_BY_CONFIG_SHIP_QUEUE_KEY";

    /**
     * ebay 校验侵权词 队列
     */
    public static final String EBAY_ITEM_CHECK_INFRINGING_WORDS_QUEUE = "EBAY_ITEM_CHECK_INFRINGING_WORDS_QUEUE";

    /**
     * ebay 校验侵权词 队列
     */
    public static final  String EBAY_ITEM_CHECK_INFRINGING_WORDS_KEY = "EBAY_ITEM_CHECK_INFRINGING_WORDS_KEY";

    /**
     * ebay待发数量变化sku队列
     */
    public static final String EBAY_BIG_DATA_PUSH_PENDING_SKU_QUEUE = "EBAY_BIG_DATA_PUSH_PENDING_SKU_QUEUE";

    /**
     * ebay待发数量变化sku队列
     */
    public static final String EBAY_BIG_DATA_PUSH_PENDING_SKU_QUEUE_KEY = "EBAY_BIG_DATA_PUSH_PENDING_SKU_QUEUE_KEY";

    /**
     * ebay根据店铺配置规则自动下架队列
     */
    public static final String EBAY_AUTO_OFFLINE_BY_RULE_QUEUE = "EBAY_AUTO_OFFLINE_BY_RULE_QUEUE";

    /**
     * ebay根据店铺配置规则自动下架队列
     */
    public static final String EBAY_AUTO_OFFLINE_BY_RULE_QUEUE_KEY = "EBAY_AUTO_OFFLINE_BY_RULE_QUEUE_KEY";

    /**
     * ebay根据店铺配置规则自动上架队列
     */
    public static final String EBAY_AUTO_ONLINE_BY_RULE_QUEUE = "EBAY_AUTO_ONLINE_BY_RULE_QUEUE";

    /**
     * ebay根据店铺配置规则自动上架队列
     */
    public static final String EBAY_AUTO_ONLINE_BY_RULE_QUEUE_KEY = "EBAY_AUTO_ONLINE_BY_RULE_QUEUE_KEY";

    /**
     * eBay系统库存变更 将虚拟海外仓的库存调零
     */
    public static final String EBAY_STOCK_ZERO_BY_CHANGE_STOCK_SKU_QUEUE = "EBAY_STOCK_ZERO_BY_CHANGE_STOCK_SKU_QUEUE";
    public static final String EBAY_STOCK_ZERO_BY_CHANGE_STOCK_SKU_QUEUE_KEY = "EBAY_STOCK_ZERO_BY_CHANGE_STOCK_SKU_QUEUE_KEY";

    /**
     * eBay 休假sku调整
     */
    public static final String EBAY_HOLIDAY_UPDATE_STOCK_SKU_QUEUE = "EBAY_HOLIDAY_UPDATE_STOCK_SKU_QUEUE";
    public static final String EBAY_HOLIDAY_UPDATE_STOCK_SKU_QUEUE_KEY = "EBAY_HOLIDAY_UPDATE_STOCK_SKU_QUEUE_KEY";
    public static final String EBAY_HOLIDAY_RECOVER_STOCK_SKU_QUEUE = "EBAY_HOLIDAY_RECOVER_STOCK_SKU_QUEUE";
    public static final String EBAY_HOLIDAY_RECOVER_STOCK_SKU_QUEUE_KEY = "EBAY_HOLIDAY_RECOVER_STOCK_SKU_QUEUE_KEY";

    /**
     * eBay 删除tidb item数据
     */
    public static final String EBAY_DELETE_TIDB_ITEM_QUEUE = "EBAY_DELETE_TIDB_ITEM_QUEUE";
    public static final String EBAY_DELETE_TIDB_ITEM_QUEUE_QUEUE_KEY = "EBAY_HOLIDAY_UPDATE_STOCK_SKU_QUEUE_KEY";


    /**

    /**
     * 公共，监控es的aop发送mq-routingKey
     */
    public static final String COMMON_ES_AOP_ROUTING_KEY = "COMMON_ES_AOP_ROUTING_KEY";

    /**
     * 公共，监控es的aop发送mq
     */
    public static final String COMMON_ES_AOP_QUEUE = "COMMON_ES_AOP_QUEUE";

    /**
     * 公共，监控mysql的aop发送mq-routingKey
     */
    public static final String COMMON_MYSQL_AOP_ROUTING_KEY = "COMMON_MYSQL_AOP_ROUTING_KEY";

    /**
     * shopee 刊登队列
     */
    public static final String SHOPEE_PUBLISH_QUEUE = "SHOPEE_PUBLISH_QUEUE";
    /**
     * shopee 刊登队列 路由
     */
    public static final String SHOPEE_PUBLISH_ROUTE_KEY = "SHOPEE_PUBLISH_ROUTE_KEY";

    /**
     * shopee 同步Listing队列
     */
    public static final String SHOPEE_SYNC_LISTING_QUEUE = "SHOPEE_SYNC_LISTING_QUEUE";
    /**
     * shopee 同步Listing队列 路由
     */
    public static final String SHOPEE_SYNC_LISTING_ROUTE_KEY = "SHOPEE_SYNC_LISTING_ROUTE_KEY";

    /**
     * shopee 假日任务 路队列
     */
    public static final String SHOPEE_HOLIDAY_JOB_QUEUEY = "SHOPEE_HOLIDAY_JOB_QUEUEY";
    /**
     * shopee 假日任务 路由
     */
    public static final String SHOPEE_HOLIDAY_JOB_ROUTE_KEY = "SHOPEE_HOLIDAY_JOB_ROUTE_KEY";

    // Shopee 全球item视频处理
    public static final String SHOPEE_GLOBAL_ITEM_VIDEO_HANDLE_QUEUE = "SHOPEE_GLOBAL_ITEM_VIDEO_HANDLE_QUEUE";
    public static final String SHOPEE_GLOBAL_ITEM_VIDEO_HANDLE_ROUTE_KEY = "SHOPEE_GLOBAL_ITEM_VIDEO_HANDLE_ROUTE_KEY";

    // Shopee 全球item上传视频
    public static final String SHOPEE_GLOBAL_ITEM_UPLOAD_VIDEO_QUEUE = "SHOPEE_GLOBAL_ITEM_UPLOAD_VIDEO_QUEUE";
    public static final String SHOPEE_GLOBAL_ITEM_UPLOAD_VIDEO_ROUTE_KEY = "SHOPEE_GLOBAL_ITEM_UPLOAD_VIDEO_ROUTE_KEY";

    // Shopee 校验侵权词
    public static final String SHOPEE_ITEM_CHECK_INFRINGING_WORDS_QUEUE = "SHOPEE_ITEM_CHECK_INFRINGING_WORDS_QUEUE";
    public static final String SHOPEE_ITEM_CHECK_INFRINGING_WORDS_KEY = "SHOPEE_ITEM_CHECK_INFRINGING_WORDS_KEY";

    // Shopee 修改标题描述根据侵权词
    public static final String SHOPEE_UPDATE_TITLE_DESC_BY_INFRING_WORDS_QUEUE = "SHOPEE_UPDATE_TITLE_DESC_BY_INFRING_WORDS_QUEUE";
    public static final String SHOPEE_UPDATE_TITLE_DESC_BY_INFRING_WORDS_KEY = "SHOPEE_UPDATE_TITLE_DESC_BY_INFRING_WORDS_KEY";

    // shopee 修改发货天数
    public static final String SHOPEE_UPDATE_DAYS_TO_SHOP_KEY = "SHOPEE_UPDATE_DAYS_TO_SHOP_KEY";
    public static final String SHOPEE_UPDATE_DAYS_TO_SHOP_QUEUE = "SHOPEE_UPDATE_DAYS_TO_SHOP_QUEUE";

//    // shopee 一次性将正常的item数据同步到tidb
    public static final String SHOPEE_ITEM_NORMAL_TO_TIDB_QUEUE = "SHOPEE_ITEM_NORMAL_TO_TIDB_QUEUE";
    public static final String SHOPEE_ITEM_NORMAL_TO_TIDB_KEY = "SHOPEE_ITEM_NORMAL_TO_TIDB_KEY";


    // SHOPEE 链接调价
    public static final String SHOPEE_ADJUST_PRICE_QUEUE = "SHOPEE_ADJUST_PRICE_QUEUE";
    public static final String SHOPEE_ADJUST_PRICE_QUEUE_KEY = "SHOPEE_ADJUST_PRICE_QUEUE_KEY";

    // shopee 同步店铺活动
    public static final String SHOPEE_SYNC_DISCOUNT_QUEUE = "SHOPEE_SYNC_DISCOUNT_QUEUE";
    public static final String SHOPEE_SYNC_DISCOUNT_QUEUE_KEY = "SHOPEE_SYNC_DISCOUNT_QUEUE_KEY";
    public static final String SHOPEE_SYNC_ACCOUNT_DISCOUNT_QUEUE = "SHOPEE_SYNC_ACCOUNT_DISCOUNT_QUEUE";
    public static final String SHOPEE_SYNC_ACCOUNT_DISCOUNT_QUEUE_KEY = "SHOPEE_SYNC_ACCOUNT_DISCOUNT_QUEUE_KEY";
    /**
     * shopee 停止营销活动
     */
    public static final String SHOPEE_STOP_MARKETING_ACTIVITY_ROUTE_KEY = "SHOPEE_STOP_MARKETING_ACTIVITY_ROUTE_KEY";
    public static final String SHOPEE_STOP_MARKETING_ACTIVITY_QUEUE = "SHOPEE_STOP_MARKETING_ACTIVITY_QUEUE";

    /**
     * shopee 导出队列
     */
    public static final String SHOPEE_DOWNLOAD_QUEUE = "SHOPEE_DOWNLOAD_QUEUE";
    public static final String SHOPEE_DOWNLOAD_QUEUE_KEY = "SHOPEE_DOWNLOAD_QUEUE_KEY";

    /**
     * shopee 更新访问量队列
     */
    public static final String SHOPEE_UPDATE_SYNC_VIEW_KEY = "SHOPEE_UPDATE_SYNC_VIEW_KEY";

    /**
     * 更新访问量队列
     */
    public static final String SHOPEE_UPDATE_SYNC_VIEW_QUEUE = "SHOPEE_UPDATE_SYNC_VIEW_QUEUE";

    /**
     * shopee 报名秒杀商品队列
     */
    public static final String SHOPEE_MARKETING_FLASH_SALE_APPLY_GOOD_QUEUE = "SHOPEE_MARKETING_FLASH_SALE_APPLY_GOOD_QUEUE_NEW";
    public static final String SHOPEE_MARKETING_FLASH_SALE_APPLY_GOOD_KEY = "SHOPEE_MARKETING_FLASH_SALE_APPLY_GOOD_KEY_NEW";

    /**
     * shopee 生成可添加商品队列
     */
    public static final String SHOPEE_MARKETING_FLASH_SALE_CAN_GEN_APPLY_GOOD_QUEUE = "SHOPEE_MARKETING_FLASH_SALE_CAN_GEN_APPLY_GOOD_QUEUE";
    public static final String SHOPEE_MARKETING_FLASH_SALE_CAN_GEN_APPLY_GOOD_KEY = "SHOPEE_MARKETING_FLASH_SALE_CAN_GEN_APPLY_GOOD_KEY";

    /**
     * shopee 折扣活动报名
     */
    public static final String SHOPEE_MARKETING_DISCOUNT_QUEUE = "SHOPEE_MARKETING_DISCOUNT_QUEUE";
    public static final String SHOPEE_MARKETING_DISCOUNT_KEY = "SHOPEE_MARKETING_DISCOUNT_KEY";

    /**
     * shopee 自动创建关注礼
     */
    public static final String SHOPEE_MARKETING_FOLLOW_PRIZE_AUTO_CREATE_QUEUE = "SHOPEE_MARKETING_FOLLOW_PRIZE_AUTO_CREATE_QUEUE";
    public static final String SHOPEE_MARKETING_FOLLOW_PRIZE_AUTO_CREATE_KEY = "SHOPEE_MARKETING_FOLLOW_PRIZE_AUTO_CREATE_KEY";

    /**
     * shopee 自动创建优惠卷
     */
    public static final String SHOPEE_MARKETING_VOUCHER_AUTO_CREATE_QUEUE = "SHOPEE_MARKETING_VOUCHER_AUTO_CREATE_QUEUE";
    public static final String SHOPEE_MARKETING_VOUCHER_PRIZE_AUTO_CREATE_KEY = "SHOPEE_MARKETING_VOUCHER_PRIZE_AUTO_CREATE_KEY";

    /**
     * shopee 添加优惠套装
     */
    public static final String SHOPEE_ADD_COMPOSE_BUNDLE_DEAL_ITEM_QUEUE = "SHOPEE_ADD_COMPOSE_BUNDLE_DEAL_ITEM_QUEUE";
    public static final String SHOPEE_ADD_COMPOSE_BUNDLE_DEAL_ITEM_QUEUE_KEY = "SHOPEE_ADD_COMPOSE_BUNDLE_DEAL_ITEM_QUEUE_KEY";

    /**
     * shopee 添加组合优惠套装
     */
    public static final String SHOPEE_ADD_BUNDLE_DEAL_ITEM_QUEUE = "SHOPEE_ADD_BUNDLE_DEAL_ITEM_QUEUE";
    public static final String SHOPEE_ADD_BUNDLE_DEAL_ITEM_KEY = "SHOPEE_ADD_BUNDLE_DEAL_ITEM_KEY";

    /**
     * shopee 根据配置更新库存
     */
    public static final String SHOPEE_CONFIG_UPDATE_STOCK_QUEUE = "SHOPEE_CONFIG_UPDATE_STOCK_QUEUE";
    public static final String SHOPEE_CONFIG_UPDATE_STOCK_KEY = "SHOPEE_CONFIG_UPDATE_STOCK_KEY";

    /**
     * shopee 春节库存任务队列（更新库存）
     */
    public static final String SHOPEE_NEW_YEAR_UPDATE_STOCK_QUEUE = "SHOPEE_NEW_YEAR_UPDATE_STOCK_QUEUE";
    public static final String SHOPEE_NEW_YEAR_UPDATE_STOCK_KEY = "SHOPEE_NEW_YEAR_UPDATE_STOCK_KEY";

    /**
     * shopee 春节库存任务队列（恢复库存）
     */
    public static final String SHOPEE_NEW_YEAR_RECOVER_STOCK_QUEUE = "SHOPEE_NEW_YEAR_RECOVER_STOCK_QUEUE";
    public static final String SHOPEE_NEW_YEAR_RECOVER_STOCK_KEY = "SHOPEE_NEW_YEAR_RECOVER_STOCK_KEY";

    /**
     * shopee 春节库存记录任务队列（更新库存记录）
     */
    public static final String SHOPEE_NEW_YEAR_UPDATE_RECORD_STOCK_QUEUE = "SHOPEE_NEW_YEAR_UPDATE_RECORD_STOCK_QUEUE";
    public static final String SHOPEE_NEW_YEAR_UPDATE_RECORD_STOCK_KEY = "SHOPEE_NEW_YEAR_UPDATE_RECORD_STOCK_KEY";

    /**
     * shopee 下架队列
     */
    public static final String SHOPEE_CONFIG_OFFLINE_QUEUE = "SHOPEE_CONFIG_OFFLINE_QUEUE";
    public static final String SHOPEE_CONFIG_OFFLINE_KEY = "SHOPEE_CONFIG_OFFLINE_KEY";

    /**
     * shopee 上架队列
     */
    public static final String SHOPEE_CONFIG_PUBLISH_QUEUE = "SHOPEE_CONFIG_PUBLISH_QUEUE";
    public static final String SHOPEE_CONFIG_PUBLISH_KEY = "SHOPEE_CONFIG_PUBLISH_KEY";

    /**
     * shopee 短视频配置队列
     */
    public static final String SHOPEE_MARKETING_SHORT_VIDEO_QUEUE = "SHOPEE_MARKETING_SHORT_VIDEO_QUEUE";
    public static final String SHOPEE_MARKETING_SHORT_VIDEO_KEY = "SHOPEE_MARKETING_SHORT_VIDEO_KEY";

    /**
     * shopee 短视频配置队列
     */
    public static final String SHOPEE_RETRY_MARKETING_SHORT_VIDEO_QUEUE = "SHOPEE_RETRY_MARKETING_SHORT_VIDEO_QUEUE";
    public static final String SHOPEE_RETRY_MARKETING_SHORT_VIDEO_KEY = "SHOPEE_RETRY_MARKETING_SHORT_VIDEO_KEY";

    /**
     * shopee 短视频生成结果队列
     */
    public static final String SHOPEE_SHORT_VIDEO_GEN_RESULT_QUEUE = "SHOPEE_SHORT_VIDEO_GEN_RESULT_QUEUE";
    public static final String SHOPEE_SHORT_VIDEO_GEN_RESULT_KEY = "SHOPEE_SHORT_VIDEO_GEN_RESULT_KEY";
    public static final String SHOPEE_SHORT_VIDEO_GEN_QUEUE = "SHOPEE_SHORT_VIDEO_GEN_QUEUE";
    public static final String SHOPEE_SHORT_VIDEO_GEN_KEY = "SHOPEE_SHORT_VIDEO_GEN_KEY";

    /**
     * shopee 跨境活动配置队列
     */
    public static final String SHOPEE_MARKETING_CROSS_BORDER_ACTIVITY_QUEUE = "SHOPEE_MARKETING_CROSS_BORDER_ACTIVITY_QUEUE";
    public static final String SHOPEE_MARKETING_CROSS_BORDER_ACTIVITY_KEY = "SHOPEE_MARKETING_CROSS_BORDER_ACTIVITY_KEY";

    public static final String SHOPEE_MARKETING_CROSS_BORDER_ACTIVITY_LETTER_QUEUE = "SHOPEE_MARKETING_CROSS_BORDER_ACTIVITY_LETTER_QUEUE";
    public static final String SHOPEE_MARKETING_CROSS_BORDER_ACTIVITY_LETTER_KEY = "SHOPEE_MARKETING_CROSS_BORDER_ACTIVITY_LETTER_KEY";

    /**
     * shopee 竞价活动生成excel队列
     */
    public static final String SHOPEE_MARKETING_BIDDING_ACTIVITY_QUEUE = "SHOPEE_MARKETING_BIDDING_ACTIVITY_QUEUE";
    public static final String SHOPEE_MARKETING_BIDDING_ACTIVITY_KEY = "SHOPEE_MARKETING_BIDDING_ACTIVITY_KEY";


    /**
     * 推送shopee店铺分组变更信息至广告系统队列
     */
    public static final String SHOPEE_ACCOUNT_GROUP_CHANGE_TO_ADS_QUEUE = "SHOPEE_ACCOUNT_GROUP_CHANGE_TO_ADS_QUEUE";
    /**
     * 推送shopee店铺分组变更信息至广告系统路由key
     */
    public static final String SHOPEE_ACCOUNT_GROUP_CHANGE_TO_ADS_ROUTING_KEY = "SHOPEE_ACCOUNT_GROUP_CHANGE_TO_ADS_ROUTING_KEY";

    /**
     * shopee 过滤数据的下架队列
     */
    public static final String SHOPEE_OFFLNIE_LINK_ITEM_QUEUE = "SHOPEE_OFFLNIE_LINK_ITEM_QUEUE";
    public static final String SHOPEE_OFFLNIE_LINK_ITEM_KEY = "SHOPEE_OFFLNIE_LINK_ITEM_KEY";

    /**
     * shopee 执行下架的队列
     */
    public static final String SHOPEE_EXECUTE_OFFLNIE_LINK_ITEM_QUEUE = "SHOPEE_EXECUTE_OFFLNIE_LINK_ITEM_QUEUE";
    public static final String SHOPEE_EXECUTE_OFFLNIE_LINK_ITEM_KEY = "SHOPEE_EXECUTE_OFFLNIE_LINK_ITEM_KEY";

    /**
     * lazada 上架队列
     */
    public static final String LATEST_CONFIG_PUBLISH_QUEUE = "LATEST_CONFIG_PUBLISH_QUEUE";
    public static final String LATEST_CONFIG_PUBLISH_KEY = "LATEST_CONFIG_PUBLISH_KEY";

    /**
     * 引流SKU队列
     */
    public static final String PUBLISH_DRAINAGE_SKU_QUEUE = "PUBLISH_DRAINAGE_SKU_QUEUE";

    /**
     * 引流SKU路由
     */
    public static final String PUBLISH_DRAINAGE_SKU_KEY = "PUBLISH_DRAINAGE_SKU_KEY";


    /**
     * 推荐规则队列
     */
    public static final String PUBLISH_RECOMMEND_SKU_QUEUE = "PUBLISH_RECOMMEND_SKU_QUEUE";

    /**
     * 推荐规则路由
     */
    public static final String PUBLISH_RECOMMEND_SKU_KEY = "PUBLISH_RECOMMEND_SKU_KEY";

    /**
     * 推送禁用物流方式
     */
    public static final String TMS_PUBLISH_SHIPPINGMETHOD_QUEQUES = "TMS_PUBLISH_SHIPPINGMETHOD_QUEQUES";



    /**
     * Amazon 刊登定时队列
     */
    public static final String AMAZON_PUBLISH_SCHEDULE_QUEUE = "AMAZON_PUBLISH_SCHEDULE_QUEUE";
    public static final String AMAZON_PUBLISH_SCHEDULE_QUEUE_KEY = "AMAZON_PUBLISH_SCHEDULE_QUEUE_KEY";

    /**
     * Amazon同步产品信息路由
     */
    public static final String AMAZON_SYNC_PRODUCT_INFO_KEY = "AMAZON_SYNC_PRODUCT_INFO_KEY";

    /**
     * Amazon同步产品信息队列
     */
    public static final String AMAZON_SYNC_PRODUCT_INFO_QUEUE = "AMAZON_SYNC_PRODUCT_INFO_QUEUE";

    /**
     * Amazon 添加刊登成功的模板绑定sku关系到es
     */
    public static final String AMAZON_ADD_SUCCESS_TEMPLATE_BIND_SKU_TO_ES_QUEUE = "AMAZON_ADD_SUCCESS_TEMPLATE_BIND_SKU_TO_ES_QUEUE";
    public static final String AMAZON_ADD_SUCCESS_TEMPLATE_BIND_SKU_TO_ES_KEY = "AMAZON_ADD_SUCCESS_TEMPLATE_BIND_SKU_TO_ES_KEY";

    /**
     * Amazon 临时检测手工禁用商标词队列
     */
    public static final String AMAZON_LISTING_CHECK_ALL_BRAND_QUEUE = "AMAZON_LISTING_CHECK_ALL_BRAND_QUEUE";
    public static final String AMAZON_LISTING_CHECK_ALL_BRAND_QUEUE_KEY = "AMAZON_LISTING_CHECK_ALL_BRAND_QUEUE_KEY";

    /**
     * Amazon 临时队列：用于移除侵权词并更新标题描述到平台的临时任务
     */
    public static final String AMAZON_LISTING_UPDATE_ITEM_NAME_TEMP_QUEUE = "AMAZON_LISTING_UPDATE_ITEM_NAME_TEMP_QUEUE";
    public static final String AMAZON_LISTING_UPDATE_ITEM_NAME_TEMP_QUEUE_KEY = "AMAZON_LISTING_UPDATE_ITEM_NAME_TEMP_QUEUE_KEY";

    /**
     * Amazon 修改分类类型重刊登队列
     */
    public static final String PRODUCT_TYPE_REPEAT_PUBLISH_QUEUE = "PRODUCT_TYPE_REPEAT_PUBLISH_QUEUE";
    public static final String PRODUCT_TYPE_REPEAT_PUBLISH_QUEUE_KEY = "PRODUCT_TYPE_REPEAT_PUBLISH_QUEUE_KEY";

    /**
     * Amazon 词频统计队列
     */
    public static final String AMAZON_INFRINGEMENT_WORD_FREQUENCY_QUEUE = "AMAZON_INFRINGEMENT_WORD_FREQUENCY_QUEUE";
    public static final String AMAZON_INFRINGEMENT_WORD_FREQUENCY_QUEUE_KEY = "AMAZON_INFRINGEMENT_WORD_FREQUENCY_QUEUE_KEY";

    /**
     * smt同步产品信息路由
     */
    public static final String SMT_SYNC_PRODUCT_INFO_KEY = "SMT_SYNC_PRODUCT_INFO_KEY";

    /**
     * smt同步分类有资质的信息给产品系统 路由
     */
    public static final String SMT_SYNC_CATEGORY_QUALIFICATION_INFO_KEY = "SMT_SYNC_CATEGORY_QUALIFICATION_INFO_KEY";
    /**
     * smt 同步分类有资质的信息给产品系统 队列
     */
    public static final String SMT_SYNC_CATEGORY_QUALIFICATION_INFO_QUEUE = "SMT_SYNC_CATEGORY_QUALIFICATION_INFO_QUEUE";

    /**
     * SYNC质量产品资质失败 队列
     */
    public static final String SMT_SYNC_QUALIFICATION_PLAT_AUDIT_FAIL_QUEUE  = "SMT_SYNC_QUALIFICATION_PLAT_AUDIT_FAIL_QUEUE";
    public static final String SMT_SYNC_QUALIFICATION_PLAT_AUDIT_FAIL_QUEUE_KEY  = "SMT_SYNC_QUALIFICATION_PLAT_AUDIT_FAIL_QUEUE_KEY";


    /**
     * smt同步产品信息队列
     */
    public static final String SMT_SYNC_PRODUCT_INFO_QUEUE = "SMT_SYNC_PRODUCT_INFO_QUEUE";


    /**
     * smt专门全量同步产品信息路由
     */
    public static final String SMT_ALL_SYNC_PRODUCT_INFO_KEY = "SMT_ALL_SYNC_PRODUCT_INFO_KEY";

    /**
     * smt专门全量同步产品信息队列
     */
    public static final String SMT_ALL_SYNC_PRODUCT_INFO_QUEUE = "SMT_All_SYNC_PRODUCT_INFO_QUEUE";

    /**
     * Ebay 同步产品信息路由
     */
    public static final String EBAY_SYNC_PRODUCT_INFO_KEY = "EBAY_SYNC_PRODUCT_INFO_KEY";

    /**
     * Ebay 同步产品信息队列
     */
    public static final String EBAY_SYNC_PRODUCT_INFO_QUEUE = "EBAY_SYNC_PRODUCT_INFO_QUEUE";

    /**
     * walmart同步产品信息路由
     */
    public static final String WALMART_SYNC_PRODUCT_INFO_KEY = "WALMART_SYNC_PRODUCT_INFO_KEY";

    /**
     * walmart同步产品信息队列
     */
    public static final String WALMART_SYNC_PRODUCT_INFO_QUEUE = "WALMART_SYNC_PRODUCT_INFO_QUEUE";

    /**
     * walmart延迟路由
     */
    public static final String WALMART_PUBLISH_DELAY_QUEUE_KEY = "WALMART_PUBLISH_DELAY_QUEUE_KEY";

    /**
     * walmart延迟队列
     */
    public static final String WALMART_PUBLISH_DELAY_QUEUE = "WALMART_PUBLISH_DELAY_QUEUE";

    /**
     * walmart延迟重试路由
     */
    public static final String WALMART_PUBLISH_DELAY_RETRY_QUEUE_KEY = "WALMART_PUBLISH_DELAY_RETRY_QUEUE_KEY";

    /**
     * walmart延迟队列
     */
    public static final String WALMART_PUBLISH_DELAY_RETRY_QUEUE = "WALMART_PUBLISH_DELAY_RETRY_QUEUE";

    /**
     * walmart死信路由
     */
    public static final String WALMART_DEAD_LETTER_QUEUEA_ROUTING_KEY = "WALMART_DEAD_LETTER_QUEUEA_ROUTING_KEY";

    /**
     * walmart死信队列
     */
    public static final String WALMART_PUBLISH_DEAD_LETTER_QUEUE = "WALMART_PUBLISH_DEAD_LETTER_QUEUE";

    /**
     * walmart同步库存队列
     */
    public static final String WALMART_SYNC_INVENTORY_QUEUE = "WALMART_SYNC_INVENTORY_QUEUE";

    /**
     * walmart同步库存路由
     */
    public static final String WALMART_SYNC_INVENTORY_ROUTING_KEY = "WALMART_SYNC_INVENTORY_ROUTING_KEY";

    /**
     * walmart推送systemProblem状态sku到产品系统队列
     */
    public static final String WALMART_SYSTEM_PROBLEM_SKU_QUEUE = "WALMART_SYSTEM_PROBLEM_SKU_QUEUE";

    /**
     * walmart推送systemProblem状态sku到产品系统路由
     */
    public static final String WALMART_SYSTEM_PROBLEM_SKU_KEY = "WALMART_SYSTEM_PROBLEM_SKU_KEY";

    /**
     * walmart校验在线列表侵权词队列
     */
    public static final String WALMART_CHECK_ITEM_INFRINGEMENT_WORD_QUEUE = "WALMART_CHECK_ITEM_INFRINGEMENT_WORD_QUEUE";

    /**
     * walmart校验在线列表侵权词路由
     */
    public static final String WALMART_CHECK_ITEM_INFRINGEMENT_WORD_ROUTING_KEY = "WALMART_CHECK_ITEM_INFRINGEMENT_WORD_ROUTING_KEY";

    /**
     * walmart SKU刊登店铺数量限制队列
     */
    public static final String WALMART_PUBLISH_ACCOUNT_QUANTITY_LIMIT_QUEUE = "WALMART_PUBLISH_ACCOUNT_QUANTITY_LIMIT_QUEUE";

    /**
     * walmart SKU刊登店铺数量限制路由
     */
    public static final String WALMART_PUBLISH_ACCOUNT_QUANTITY_LIMIT_KEY = "WALMART_PUBLISH_ACCOUNT_QUANTITY_LIMIT_KEY";

    /**
     * walmart 在线列表导出excel 队列
     */
    public static final String WALMART_EXCEL_ITEM_DOWNLOAD_QUEUE = "WALMART_EXCEL_ITEM_DOWNLOAD_QUEUE";

    /**
     * walmart 在线列表导出excel key
     */
    public static final  String WALMART_EXCEL_ITEM_DOWNLOAD_KEY = "WALMART_EXCEL_ITEM_DOWNLOAD_KEY";

    /**
     * walmart 无销量下架数据导出excel 队列
     */
    public static final String WALMART_NO_SALES_DATA_DOWNLOAD_QUEUE = "WALMART_NO_SALES_DATA_DOWNLOAD_QUEUE";

    /**
     * walmart 无销量下架数据导出excel key
     */
    public static final  String WALMART_NO_SALES_DATA_DOWNLOAD_QUEUE_KEY = "WALMART_NO_SALES_DATA_DOWNLOAD_QUEUE_KEY";

    /**
     * walmart 店铺配置信息导出 队列
     */
    public static final String WALMART_ACCOUNT_CONFIG_DOWNLOAD_QUEUE = "WALMART_ACCOUNT_CONFIG_DOWNLOAD_QUEUE";

    /**
     * walmart 店铺配置信息导出 key
     */
    public static final String WALMART_ACCOUNT_CONFIG_DOWNLOAD_KEY = "WALMART_ACCOUNT_CONFIG_DOWNLOAD_KEY";

    /**
     * walmart 下载item报告 队列
     */
    public static final String WALMART_DOWNLOAD_ITEM_REPORT_QUEUE = "WALMART_DOWNLOAD_ITEM_REPORT_QUEUE";

    /**
     * walmart 下载item报告 key
     */
    public static final  String WALMART_DOWNLOAD_ITEM_REPORT_KEY = "WALMART_DOWNLOAD_ITEM_REPORT_KEY";

    /**
     * fruugo同步产品信息路由
     */
    public static final String FRUUGO_SYNC_PRODUCT_INFO_KEY = "FRUUGO_SYNC_PRODUCT_INFO_KEY";

    /**
     * fruugo同步产品信息队列
     */
    public static final String FRUUGO_SYNC_PRODUCT_INFO_QUEUE = "FRUUGO_SYNC_PRODUCT_INFO_QUEUE";

    /**
     * fruugo 自动刊登 key
     */
    public static final  String FRUUGO_AUTO_PUBLISH_QUEUE_KEY = "FRUUGO_AUTO_PUBLISH_QUEUE_KEY";
    /**
     * fruugo 自动刊登队列
     */
    public static final  String FRUUGO_AUTO_PUBLISH_QUEUE = "FRUUGO_AUTO_PUBLISH_QUEUE";

    /**
     * fruugo 产品推送队列
     */
    public static final String FRUUGO_PRODUCT_PUSH_QUEUE = "FRUUGO_PRODUCT_PUSH_QUEUE";

    /**
     * frugo 产品推送路由KEY
     */
    public static final String FRUUGO_PRODUCT_PUSH_ROUTING_KEY = "FRUUGO_PRODUCT_PUSH_ROUTING_KEY";

    /**
     * fruugo延迟路由
     */
    public static final String FRUUGO_PUBLISH_DELAY_QUEUE_KEY = "FRUUGO_PUBLISH_DELAY_QUEUE_KEY";

    /**
     * fruugo延迟队列
     */
    public static final String FRUUGO_PUBLISH_DELAY_QUEUE = "FRUUGO_PUBLISH_DELAY_QUEUE";

    /**
     * fruugo死信路由
     */
    public static final String FRUUGO_DEAD_LETTER_QUEUEA_ROUTING_KEY = "FRUUGO_DEAD_LETTER_QUEUEA_ROUTING_KEY";

    /**
     * fruugo死信队列
     */
    public static final String FRUUGO_PUBLISH_DEAD_LETTER_QUEUE = "FRUUGO_PUBLISH_DEAD_LETTER_QUEUE";

    /**
     * fruugo 批量提交平台 key
     */
    public static final  String FRUUGO_BATCH_SUBMIT_ROUTING_KEY = "FRUUGO_BATCH_SUBMIT_ROUTING_KEY";
    /**
     * fruugo  批量提交平台队列
     */
    public static final  String FRUUGO_BATCH_SUBMIT_QUEUE = "FRUUGO_BATCH_SUBMIT_QUEUE";

    /**
     * Shopee同步产品信息路由
     */
    public static final String SHOPEE_SYNC_PRODUCT_INFO_KEY = "SHOPEE_SYNC_PRODUCT_INFO_KEY";

    /**
     * Shopee同步产品信息队列
     */
    public static final String SHOPEE_SYNC_PRODUCT_INFO_QUEUE = "SHOPEE_SYNC_PRODUCT_INFO_QUEUE";

    /**
     * Amazon 在线类别更新毛利毛利率队列
     */
    public static final String AMAZON_LISTING_UPDATE_GROSS_PROFIT_QUEUE = "AMAZON_LISTING_UPDATE_GROSS_PROFIT_QUEUE";
    public static final String AMAZON_LISTING_UPDATE_GROSS_PROFIT_QUEUE_KEY = "AMAZON_LISTING_UPDATE_GROSS_PROFIT_QUEUE_KEY";

    /**
     * B2W同步产品信息路由
     */
    public static final String B2W_SYNC_PRODUCT_INFO_KEY = "B2W_SYNC_PRODUCT_INFO_KEY";

    /**
     * B2W同步产品信息队列
     */
    public static final String B2W_SYNC_PRODUCT_INFO_QUEUE = "B2W_SYNC_PRODUCT_INFO_QUEUE";

    /**
     * LAZADA同步产品信息路由
     */
    public static final String LAZADA_SYNC_PRODUCT_INFO_KEY = "LAZADA_SYNC_PRODUCT_INFO_KEY";

    /**
     * LAZADA同步产品信息队列
     */
    public static final String LAZADA_SYNC_PRODUCT_INFO_QUEUE = "LAZADA_SYNC_PRODUCT_INFO_QUEUE";

    /**
     * JOOM同步产品信息路由
     */
    public static final String JOOM_SYNC_PRODUCT_INFO_KEY = "JOOM_SYNC_PRODUCT_INFO_KEY";

    /**
     * JOOM同步产品信息队列
     */
    public static final String JOOM_SYNC_PRODUCT_INFO_QUEUE = "JOOM_SYNC_PRODUCT_INFO_QUEUE";

    /**
     * jdwalmart同步产品信息路由
     */
    public static final String JDWALMART_SYNC_PRODUCT_INFO_KEY = "JDWALMART_SYNC_PRODUCT_INFO_KEY";

    /**
     * jdwalmart同步产品信息队列
     */
    public static final String JDWALMART_SYNC_PRODUCT_INFO_QUEUE = "JDWALMART_SYNC_PRODUCT_INFO_QUEUE";

    /**
     * MICROSOFT同步产品信息路由
     */
    public static final String MICROSOFT_SYNC_PRODUCT_INFO_KEY = "MICROSOFT_SYNC_PRODUCT_INFO_KEY";

    /**
     * MICROSOFT同步产品信息队列
     */
    public static final String MICROSOFT_SYNC_PRODUCT_INFO_QUEUE = "MICROSOFT_SYNC_PRODUCT_INFO_QUEUE";

    /**
     * FYNDIQ同步产品信息路由
     */
    public static final String FYNDIQ_SYNC_PRODUCT_INFO_KEY = "FYNDIQ_SYNC_PRODUCT_INFO_KEY";

    /**
     * FYNDIQ同步产品信息队列
     */
    public static final String FYNDIQ_SYNC_PRODUCT_INFO_QUEUE = "FYNDIQ_SYNC_PRODUCT_INFO_QUEUE";

    /**
     * FYNDIQ 在线列表导出excel 队列
     */
    public static final String FYNDIQ_EXCEL_ITEM_DOWNLOAD_QUEUE = "FYNDIQ_EXCEL_ITEM_DOWNLOAD_QUEUE";

    /**
     * FYNDIQ 在线列表导出excel key
     */
    public static final  String FYNDIQ_EXCEL_ITEM_DOWNLOAD_KEY = "FYNDIQ_EXCEL_ITEM_DOWNLOAD_KEY";

    /**
     * VOGHION同步产品信息路由
     */
    public static final String VOGHION_SYNC_PRODUCT_INFO_KEY = "VOGHION_SYNC_PRODUCT_INFO_KEY";

    /**
     * VOGHION同步产品信息队列
     */
    public static final String VOGHION_SYNC_PRODUCT_INFO_QUEUE = "VOGHION_SYNC_PRODUCT_INFO_QUEUE";

    /**
     * VOGHION 在线列表导出excel 队列
     */
    public static final String VOGHION_EXCEL_ITEM_DOWNLOAD_QUEUE = "VOGHION_EXCEL_ITEM_DOWNLOAD_QUEUE";

    /**
     * VOGHION 在线列表导出excel key
     */
    public static final  String VOGHION_EXCEL_ITEM_DOWNLOAD_KEY = "VOGHION_EXCEL_ITEM_DOWNLOAD_KEY";

    /**
     *TIKTOK同步产品信息路由
     */
    public static final String TIKTOK_SYNC_PRODUCT_INFO_KEY = "TIKTOK_SYNC_PRODUCT_INFO_KEY";

    /**
     * TIKTOK同步产品信息队列
     */
    public static final String TIKTOK_SYNC_PRODUCT_INFO_QUEUE = "TIKTOK_SYNC_PRODUCT_INFO_QUEUE";

    /**
     * TIKTOK 在线列表导出excel 队列
     */
    public static final String TIKTOK_EXCEL_ITEM_DOWNLOAD_QUEUE = "TIKTOK_EXCEL_ITEM_DOWNLOAD_QUEUE";

    /**
     * TIKTOK 在线列表导出excel key
     */
    public static final  String TIKTOK_EXCEL_ITEM_DOWNLOAD_KEY = "TIKTOK_EXCEL_ITEM_DOWNLOAD_KEY";

    /**
     * TIKTOK 自动刊登 队列
     */
    public static final String TIKTOK_AUTO_PUBLISH_QUEUE = "TIKTOK_AUTO_PUBLISH_QUEUE";

    /**
     * TIKTOK 自动刊登 key
     */
    public static final  String TIKTOK_AUTO_PUBLISH_QUEUE_KEY = "TIKTOK_AUTO_PUBLISH_QUEUE_KEY";

    /**
     * tiktok 全量同步 队列
     */
    public static final String TIKTOK_ALL_SYNC_LISTING_QUEUE = "TIKTOK_ALL_SYNC_LISTING_QUEUE";

    /**
     * tiktok 全量同步 key
     */
    public static final String TIKTOK_ALL_SYNC_LISTING_QUEUE_KEY = "TIKTOK_ALL_SYNC_LISTING_QUEUE_KEY";

    /**
     * tiktok 全量同步 队列
     */
    public static final String TIKTOK_INCREMENT_SYNC_LISTING_QUEUE = "TIKTOK_INCREMENT_SYNC_LISTING_QUEUE";

    /**
     * tiktok 增量同步 key
     */
    public static final String TIKTOK_INCREMENT_SYNC_LISTING_QUEUE_KEY = "TIKTOK_INCREMENT_SYNC_LISTING_QUEUE_KEY";

    /**
     * NOCNOC同步产品信息路由
     */
    public static final String NOCNOC_SYNC_PRODUCT_INFO_KEY = "NOCNOC_SYNC_PRODUCT_INFO_KEY";

    /**
     * NOCNOC同步产品信息队列
     */
    public static final String NOCNOC_SYNC_PRODUCT_INFO_QUEUE = "NOCNOC_SYNC_PRODUCT_INFO_QUEUE";

    /**
     * NOCNOC延迟路由
     */
    public static final String NOCNOC_DELAY_QUEUE_KEY = "NOCNOC_DELAY_QUEUE_KEY";

    /**
     * NOCNOC延迟队列
     */
    public static final String NOCNOC_DELAY_QUEUE = "NOCNOC_DELAY_QUEUE";

    /**
     * NOCNOC延迟重试路由
     */
    public static final String NOCNOC_DELAY_RETRY_QUEUE_KEY = "NOCNOC_DELAY_RETRY_QUEUE_KEY";

    /**
     * NOCNOC延迟队列
     */
    public static final String NOCNOC_DELAY_RETRY_QUEUE = "NOCNOC_DELAY_RETRY_QUEUE";

    /**
     * NOCNOC死信路由
     */
    public static final String NOCNOC_DEAD_LETTER_QUEUEA_ROUTING_KEY = "NOCNOC_DEAD_LETTER_QUEUEA_ROUTING_KEY";

    /**
     * NOCNOC死信队列
     */
    public static final String NOCNOC_DEAD_LETTER_QUEUE = "NOCNOC_DEAD_LETTER_QUEUE";

    /**
     * NOCNOC库存改0路由
     */
    public static final String NOCNOC_STOCK_ZERO_QUEUE_KEY = "NOCNOC_STOCK_ZERO_QUEUE_KEY";

    /**
     * NOCNOC库存改0队列
     */
    public static final String NOCNOC_STOCK_ZERO_QUEUE = "NOCNOC_STOCK_ZERO_QUEUE";

    /**
     * NOCNOC库存改0死信路由
     */
    public static final String NOCNOC_STOCK_ZERO_DEAD_LETTER_KEY = "NOCNOC_STOCK_ZERO_DEAD_LETTER_KEY";

    /**
     * NOCNOC库存改0死信队列
     */
    public static final String NOCNOC_STOCK_ZERO_DEAD_LETTER_QUEUE = "NOCNOC_STOCK_ZERO_DEAD_LETTER_QUEUE";

    /**
     * 组合产品信息变更-SMT
     */
    public static final String COMPOSE_SKU_IS_ENABLE_CHANGE_QUEUE = "COMPOSE_SKU_IS_ENABLE_CHANGE_QUEUE";
    /**
     * 组合产品信息变更-SMT
     */
    public static final String COMPOSE_SKU_IS_ENABLE_CHANGE_QUEUE_KEY = "COMPOSE_SKU_IS_ENABLE_CHANGE_QUEUE_KEY";

    /**
     * 组合产品信息变更-Amazon
     */
    public static final String COMPOSE_SKU_IS_ENABLE_CHANGE_AMAZON_QUEUE = "COMPOSE_SKU_IS_ENABLE_CHANGE_AMAZON_QUEUE";

    /**
     * 组合产品信息变更-Amazon
     */
    public static final String COMPOSE_SKU_IS_ENABLE_CHANGE_AMAZON_QUEUE_KEY = "COMPOSE_SKU_IS_ENABLE_CHANGE_AMAZON_QUEUE_KEY";

    /**
     * 仓库保质期促销sku推送队列
     */
    public static final String PUSH_EXP_MANAGE_TO_PUBLISH_QUEUE = "PUSH_EXP_MANAGE_TO_PUBLISH_QUEUE";

    /**
     * 组合产品信息变更-Joom
     */
    public static final String COMPOSE_SKU_IS_ENABLE_CHANGE_JOOM_QUEUE = "COMPOSE_SKU_IS_ENABLE_CHANGE_JOOM_QUEUE";

    /**
     * 组合产品信息变更-Shopee
     */
    public static final String COMPOSE_SKU_IS_ENABLE_CHANGE_SHOPEE_QUEUE = "COMPOSE_SKU_IS_ENABLE_CHANGE_SHOPEE_QUEUE";

    /**
     * 组合产品信息变更-Walmart
     */
    public static final String COMPOSE_SKU_IS_ENABLE_CHANGE_WALMART_QUEUE = "COMPOSE_SKU_IS_ENABLE_CHANGE_WALMART_QUEUE";

    /**
     * 组合产品信息变更-Lazada
     */
    public static final String COMPOSE_SKU_IS_ENABLE_CHANGE_LAZADA_QUEUE = "COMPOSE_SKU_IS_ENABLE_CHANGE_LAZADA_QUEUE";

    /**
     * 组合产品信息变更-Ebay
     */
    public static final String COMPOSE_SKU_IS_ENABLE_CHANGE_EBAY_QUEUE = "COMPOSE_SKU_IS_ENABLE_CHANGE_EBAY_QUEUE";

    /**
     * 组合产品信息变更-b2w
     */
    public static final String COMPOSE_SKU_IS_ENABLE_CHANGE_B2W_QUEUE = "COMPOSE_SKU_IS_ENABLE_CHANGE_B2W_QUEUE";

    /**
     * 组合产品信息变更-NOCNOC
     */
    public static final String COMPOSE_SKU_IS_ENABLE_CHANGE_NOCNOC_QUEUE = "COMPOSE_SKU_IS_ENABLE_CHANGE_NOCNOC_QUEUE";

    /**
     * 组合产品信息变更-JDwalmart
     */
    public static final String COMPOSE_SKU_IS_ENABLE_CHANGE_JDWALMART_QUEUE = "COMPOSE_SKU_IS_ENABLE_CHANGE_JDWALMART_QUEUE";

    /**
     * 组合产品信息变更-Fyndiq
     */
    public static final String COMPOSE_SKU_IS_ENABLE_CHANGE_FYNDIQ_QUEUE = "COMPOSE_SKU_IS_ENABLE_CHANGE_FYNDIQ_QUEUE";

    /**
     * 组合产品信息变更-Voghion
     */
    public static final String COMPOSE_SKU_IS_ENABLE_CHANGE_VOGHION_QUEUE = "COMPOSE_SKU_IS_ENABLE_CHANGE_VOGHION_QUEUE";

    /**
     * 组合产品信息变更-tiktok
     */
    public static final String COMPOSE_SKU_IS_ENABLE_CHANGE_TIKTOK_QUEUE = "COMPOSE_SKU_IS_ENABLE_CHANGE_TIKTOK_QUEUE";

    /**
     * 组合产品信息变更-microsoft
     */
    public static final String COMPOSE_SKU_IS_ENABLE_CHANGE_MICROSOFT_QUEUE = "COMPOSE_SKU_IS_ENABLE_CHANGE_MICROSOFT_QUEUE";

    /**
     * 订单推送律所下单spu
     */
    public static final String PUSH_BLACK_LAW_FIRM_SKU = "PUSH_BLACK_LAW_FIRM_SKU";

    /**
     *  推荐产品同步产品信息路由
     */
    public static final String RECOMMEND_PRODUCT_SYNC_PRODUCT_INFO_KEY = "RECOMMEND_PRODUCT_SYNC_PRODUCT_INFO_KEY";

    /**
     * 推荐产品同步产品信息队列
     */
    public static final String RECOMMEND_PRODUCT_SYNC_PRODUCT_INFO_QUEUE = "RECOMMEND_PRODUCT_SYNC_PRODUCT_INFO_QUEUE";

    /**
     *  ebay刷新系统库存路由
     */
    public static final String EBAY_REFRESH_SYSTEM_STOCK_KEY = "EBAY_REFRESH_SYSTEM_STOCK_KEY";

    /**
     * ebay刷新系统库存队列
     */
    public static final String EBAY_REFRESH_SYSTEM_STOCK_QUEUE = "EBAY_REFRESH_SYSTEM_STOCK_QUEUE";

    /**
     *  smt刷新系统库存路由
     */
    public static final String SMT_REFRESH_SYSTEM_STOCK_KEY = "SMT_REFRESH_SYSTEM_STOCK_KEY";

    /**
     * smt刷新系统库存队列
     */
    public static final String SMT_REFRESH_SYSTEM_STOCK_QUEUE = "SMT_REFRESH_SYSTEM_STOCK_QUEUE";

    /**
     *  只有深圳仓 在途数量变化推送到mq 采购ToEbay
     */
    public static final String PUSH_ONLOAD_QUANTITY_CHANGE_QUEUE = "PUSH_ONLOAD_QUANTITY_CHANGE_QUEUE";
    /**
     * 试卖刊登成功模板推送到数据分析队列
     */
    public static final String SP_PUBLISH_SKU_TO_DAS_QUEUE = "SP_PUBLISH_SKU_TO_DAS_QUEUE";
    public static final String SP_PUBLISH_SKU_TO_DAS_QUEUE_KEY = "SP_PUBLISH_SKU_TO_DAS_QUEUE_KEY";

    /**
     * smt listing 检查资质信息 路由
     */
    public static final String SMT_CHECK_QUALIFICATION_INFO_KEY = "SMT_CHECK_QUALIFICATION_INFO_KEY";
    /**
     * smt listing 检查资质信息 队列
     */
    public static final String SMT_CHECK_QUALIFICATION_INFO_QUEUE = "SMT_CHECK_QUALIFICATION_INFO_QUEUE";

    /**
     * 订单 推送库存变化的队列到smt
     */
//    public static final String STOCK_IN_REDIS_CHANGED_BY_SALE_FOR_SMT = "STOCK_IN_REDIS_CHANGED_BY_SALE_FOR_SMT";

    /**
     * 仓库 推送smt中转仓库存变化的数据
     */
    public static final String WMS_PUBLISH_SKU_TRANSFER_SMT_STOCK_QUEUE = "WMS_PUBLISH_SKU_TRANSFER_SMT_STOCK_QUEUE";

    /**
     * wish 系统参数队列
     */
    public static final String WISH_SYSTEM_PARAM_QUEUE = "WISH_SYSTEM_PARAM_QUEUE";
    /**
     * wish 系统参数队列key
     */
    public static final String WISH_SYSTEM_PARAM_ROUTE_KEY = "WISH_SYSTEM_PARAM_ROUTE_KEY";


    /**
     * FMS EBAY 上传到腾讯云队列
     */
    public static final String FMS_OSS_TENCENT_UPLOAD_CUSTOM_IMG_KEY = "FMS_OSS_TENCENT_UPLOAD_CUSTOM_IMG_KEY";

    /**
     * AMAZON 处理报告成功修改本地数据
     */
    public static final String AMAZON_REPORT_SUCCESS_UPDATE_LOCAL_DATA = "AMAZON_REPORT_SUCCESS_UPDATE_LOCAL_DATA";
    public static final String AMAZON_REPORT_SUCCESS_UPDATE_LOCAL_DATA_KEY = "AMAZON_REPORT_SUCCESS_UPDATE_LOCAL_DATA_KEY";


    /**
     * 亚马逊 主图比较队列
     */
    public static final String AMAZON_MAIN_IMAGE_SYNC_COMPARISON_QUEUE = "AMAZON_MAIN_IMAGE_SYNC_COMPARISON_QUEUE";
    /**
     * 亚马逊 主图比较队列key
     */
    public static final String AMAZON_MAIN_IMAGE_SYNC_COMPARISON_KEY = "AMAZON_MAIN_IMAGE_SYNC_COMPARISON_KEY";

    /**
     * 订单 推送预减库存队列
     */
    public static final String SALE_PUBLISH_ORDER_NOTIFICATION_QUEUE = "SALE_PUBLISH_ORDER_NOTIFICATION_QUEUE";

    /**
     * smt全量同步在线列表 路由
     */
    public static final String SMT_SYNCH_ALL_ITEM_KEY = "SMT_SYNCH_ALL_ITEM_KEY";
    /**
     * smt全量同步在线列表 队列
     */
    public static final String SMT_SYNCH_ALL_ITEM_QUEUE = "SMT_SYNCH_ALL_ITEM_QUEUE";


    /**
     * smt单品折扣商品添加 队列
     */
    public static final String SMT_SINGLE_DISCOUNT_PRODUCT_ADD_QUEUE = "SMT_SINGLE_DISCOUNT_PRODUCT_ADD_QUEUE";

    /**
     * smt单品折扣商品添加 路由键
     */
    public static final String SMT_SINGLE_DISCOUNT_PRODUCT_ADD_KEY = "SMT_SINGLE_DISCOUNT_PRODUCT_ADD_KEY";


    /**
     * smt SPU-分类禁刊登 路由
     */
    public static final String SMT_SPU_CATEGORY_FORBID_PUBLISH_KEY = "SMT_SPU_CATEGORY_FORBID_PUBLISH_KEY";
    /**
     * smt SPU-分类禁刊登 队列
     */
    public static final String SMT_SPU_CATEGORY_FORBID_PUBLISH_QUEUE = "SMT_SPU_CATEGORY_FORBID_PUBLISH_QUEUE";


    /**
     * smt 自动加入早鸟活动 路由
     */
    public static final String SMT_AUTO_JOIN_EARLY_BIRD_KEY = "SMT_AUTO_JOIN_EARLY_BIRD_KEY";
    /**
     * smt 自动加入早鸟活动 队列
     */
    public static final String SMT_AUTO_JOIN_EARLY_BIRD_QUEUE = "SMT_AUTO_JOIN_EARLY_BIRD_QUEUE";

    /**
     * smt 生成设置联盟数据 路由
     */
    public static final String SMT_ALIANCE_PRODUCT_LISTING_KEY = "SMT_ALIANCE_PRODUCT_LISTING_KEY";
    /**
     * smt 生成设置联盟数据 队列
     */
    public static final String SMT_ALIANCE_PRODUCT_LISTING_QUEUE = "SMT_ALIANCE_PRODUCT_LISTING_QUEUE";
    /**
     *请求压缩文件路由
     */
    public static final String PUBLSIH_DOWNLOAD_ZIP_FILE_KEY = "PUBLSIH_DOWNLOAD_ZIP_FILE_KEY";
    /**
     *请求压缩文件队列
     */
    public static final String PUBLSIH_DOWNLOAD_ZIP_FILE = "PUBLSIH_DOWNLOAD_ZIP_FILE";

    /**
     * 压缩文件通知队列
     */
    public static final String FMS_DOWNLOAD_NOTICE_PUBLISH_QUEUE="FMS_DOWNLOAD_NOTICE_PUBLISH_QUEUE";

    /**
     * Ebay上传GPSR路由
     */
    public static final String EBAY_UPLOAD_GPSR_KEY = "EBAY_UPLOAD_GPSR_KEY";

    /**
     * Ebay上传GPSR队列
     */
    public static final String EBAY_UPLOAD_GPSR_QUEUE = "EBAY_UPLOAD_GPSR_QUEUE";

    /**
     * joom春节调库存路由key
     */
    public static final String JOOM_SPRING_FESTIVAL_UPDATE_STOCK_KEY = "JOOM_SPRING_FESTIVAL_UPDATE_STOCK_KEY";

    /**
     * joom春节调库存队列
     */
    public static final String JOOM_SPRING_FESTIVAL_UPDATE_STOCK_QUEUE = "JOOM_SPRING_FESTIVAL_UPDATE_STOCK_QUEUE";


    /**
     * joom春节恢复库存路由key
     */
    public static final String JOOM_SPRING_FESTIVAL_RECOVER_STOCK_KEY = "JOOM_SPRING_FESTIVAL_RECOVER_STOCK_KEY";
    /**
     * joom春节恢复库存队列
     */
    public static final String JOOM_SPRING_FESTIVAL_RECOVER_STOCK_QUEUE = "JOOM_SPRING_FESTIVAL_RECOVER_STOCK_QUEUE";

    /**
     * shopee 禁售信息
     */
    public final static String SHOPEE_PRODUCT_PROHIBITION_SPU_TO_PUBLISH_QUEUE = "SHOPEE_PRODUCT_PROHIBITION_SPU_TO_PUBLISH_QUEUE";
    public final static String SHOPEE_PRODUCT_PROHIBITION_SPU_TO_PUBLISH_QUEUE_KEY = "SHOPEE_PRODUCT_PROHIBITION_SPU_TO_PUBLISH_QUEUE_KEY";

    /**
     * smt 新品推荐监听 spu禁售信息
     */
    public final static String SMT_PRODUCT_PROHIBITION_SPU_TO_PUBLISH_QUEUE = "SMT_PRODUCT_PROHIBITION_SPU_TO_PUBLISH_QUEUE";


    /**
     * 上个工作日的spu
     */
    public static final String PRODUCT_PREVIOUS_WORKING_DAY_SPU_TO_SMT_QUEUE = "PRODUCT_PREVIOUS_WORKING_DAY_SPU_TO_SMT_QUEUE";
    public static final String PRODUCT_PREVIOUS_WORKING_DAY_SPU_TO_SHOPEE_QUEUE = "PRODUCT_PREVIOUS_WORKING_DAY_SPU_TO_SHOPEE_QUEUE";


    /**
     * yandex推送管理单品新状态消息队列
     */
    public static final String PRODUCT_SINGLE_NEW_STATUS_YANDEX_QUEUE = "PRODUCT_SINGLE_NEW_STATUS_YANDEX_QUEUE";
    // walmart 新品状态变更
    public static final String PRODUCT_SINGLE_NEW_STATUS_OZON_QUEUE = "PRODUCT_SINGLE_NEW_STATUS_OZON_QUEUE";
    public static final String PRODUCT_SINGLE_NEW_STATUS_WALMART_QUEUE = "PRODUCT_SINGLE_NEW_STATUS_WALMART_QUEUE";
    public static final String PRODUCT_SINGLE_NEW_STATUS_SMT_QUEUE = "PRODUCT_SINGLE_NEW_STATUS_SMT_QUEUE";
    public static final String PRODUCT_SINGLE_NEW_STATUS_TIKTOK_QUEUE = "PRODUCT_SINGLE_NEW_STATUS_TIKTOK_QUEUE";
    public static final String PRODUCT_SINGLE_NEW_STATUS_TEMU_QUEUE = "PRODUCT_SINGLE_NEW_STATUS_TEMU_QUEUE";
    public static final String PRODUCT_SINGLE_NEW_STATUS_FYNDIQ_QUEUE = "PRODUCT_SINGLE_NEW_STATUS_FYNDIQ_QUEUE";
    public static final String PRODUCT_SINGLE_NEW_STATUS_SHOPEE_QUEUE = "PRODUCT_SINGLE_NEW_STATUS_SHOPEE_QUEUE";
    public static final String PRODUCT_SINGLE_NEW_STATUS_MKD_QUEUE = "PRODUCT_SINGLE_NEW_STATUS_MKD_QUEUE";
    public static final String PRODUCT_SINGLE_NEW_STATUS_COUPANG_QUEUE = "PRODUCT_SINGLE_NEW_STATUS_COUPANG_QUEUE";
    public static final String PRODUCT_SINGLE_NEW_STATUS_LAZADA_QUEUE = "PRODUCT_SINGLE_NEW_STATUS_LAZADA_QUEUE";
    public static final String PRODUCT_SINGLE_NEW_STATUS_JOOM_QUEUE = "PRODUCT_SINGLE_NEW_STATUS_JOOM_QUEUE";
    public static final String PRODUCT_SINGLE_NEW_STATUS_FRUUGO_QUEUE = "PRODUCT_SINGLE_NEW_STATUS_FRUUGO_QUEUE";
    public static final String PRODUCT_SINGLE_NEW_STATUS_EBAY_QUEUE = "PRODUCT_SINGLE_NEW_STATUS_EBAY_QUEUE";
    public static final String PRODUCT_SINGLE_NEW_STATUS_AMAZON_QUEUE = "PRODUCT_SINGLE_NEW_STATUS_AMAZON_QUEUE";

    // 滞销的sku
    public static final String PMS_PUSH_RETURN_GOODS_TO_PS = "PMS_PUSH_RETURN_GOODS_TO_PS";



    /**
     * ebay 推荐刊登导出队列
     */
    public static final String EBAY_RECOMMEND_PUBLISH_DOWNLOAD_QUEUE = "EBAY_RECOMMEND_PUBLISH_DOWNLOAD_QUEUE";

    /**
     * ebay 推荐刊登导出队列key
     */
    public static final String EBAY_RECOMMEND_PUBLISH_DOWNLOAD_KEY = "EBAY_RECOMMEND_PUBLISH_DOWNLOAD_KEY";

    /**
     * 产品系统推送数据到eBay推荐刊登表队列
     */
    public static final String PRODUCT_PUSH_EBAY_RECOMMEND_PUBLISH_QUEUE = "PRODUCT_PUSH_EBAY_RECOMMEND_PUBLISH_QUEUE";

    /**
     * 产品系统推送数据到eBay推荐刊登表队列路由key
     */
    public static final String PRODUCT_PUSH_EBAY_RECOMMEND_PUBLISH_KEY = "PRODUCT_PUSH_EBAY_RECOMMEND_PUBLISH_KEY";


    /**
     * ebay 推荐刊登更新禁售信息队列
     */
    public static final String EBAY_RECOMMEND_FORBIDDEN_DATA_QUEUE = "EBAY_RECOMMEND_FORBIDDEN_DATA_QUEUE";

    /**
     * ebay 推荐刊登更新禁售信息路由key
     */
    public static final String EBAY_RECOMMEND_FORBIDDEN_DATA_KEY = "EBAY_RECOMMEND_FORBIDDEN_DATA_KEY";

    /**
     * smt同步托管产品队列
     */
    public static final String SMT_SYNCH_TG_ITEM_QUEUE = "SMT_SYNCH_TG_ITEM_QUEUE";
    /**
     * smt同步托管产品队列路由key
     */
    public static final String SMT_SYNCH_TG_ITEM_ROUTE_KEY = "SMT_SYNCH_TG_ITEM_ROUTE_KEY";

    /**
     * smt 速卖通归零队列
     */
    public static final String SMT_ALIEXPRESS_STOCK_ZERO_QUEUE = "SMT_ALIEXPRESS_STOCK_ZERO_QUEUE";
    /**
     * smt 速卖通归零队列路由key
     */
    public static final String SMT_ALIEXPRESS_STOCK_ZERO_ROUTE_KEY = "SMT_ALIEXPRESS_STOCK_ZERO_ROUTE_KEY";

    /**
     * smt 速卖通归零V2队列（每日定时归零专用，勿与现有归零业务混用）
     */
    public static final String SMT_ALIEXPRESS_STOCK_ZERO_V2_QUEUE = "SMT_ALIEXPRESS_STOCK_ZERO_V2_QUEUE";
    /**
     * smt 速卖通归零V2队列路由key（每日定时归零专用，勿与现有归零业务混用）
     */
    public static final String SMT_ALIEXPRESS_STOCK_ZERO_V2_ROUTE_KEY = "SMT_ALIEXPRESS_STOCK_ZERO_V2_ROUTE_KEY";

    // ... existing code ...
    public static final String SMT_UPDATE_STOCK_ZERO_BY_STOP_TG_QUEUE = "SMT_UPDATE_STOCK_ZERO_BY_STOP_TG_QUEUE";
    public static final String SMT_UPDATE_STOCK_ZERO_BY_STOP_TG_ROUTE_KEY = "SMT_UPDATE_STOCK_ZERO_BY_STOP_TG_ROUTE_KEY";
    // ... existing code ...

    // ... existing code ...
    public static final String SMT_SYNCH_HALF_TG_PRE_ITEM_QUEUE = "SMT_SYNCH_HALF_TG_PRE_ITEM_QUEUE";
    public static final String SMT_SYNCH_HALF_TG_PRE_ITEM_ROUTE_KEY = "SMT_SYNCH_HALF_TG_PRE_ITEM_ROUTE_KEY";
    // ... existing code ...

    /**
     * smt 修改品牌制造商ID队列
     */
    public static final String SMT_UPDATE_BRAND_MANUFACTURER_ID_QUEUE = "SMT_UPDATE_BRAND_MANUFACTURER_ID_QUEUE";
    /**
     * smt 修改品牌制造商ID路由key
     */
    public static final String SMT_UPDATE_BRAND_MANUFACTURER_ID_ROUTE_KEY = "SMT_UPDATE_BRAND_MANUFACTURER_ID_ROUTE_KEY";

    /**
     * smt去除侵权词汇异步处理队列
     */
    public static final String SMT_FILTER_INFRINGING_WORDS_EDIT_ITEM_QUEUE = "SMT_FILTER_INFRINGING_WORDS_EDIT_ITEM_QUEUE";
    /**
     * smt去除侵权词汇异步处理队列路由key
     */
    public static final String SMT_FILTER_INFRINGING_WORDS_EDIT_ITEM_ROUTE_KEY = "SMT_FILTER_INFRINGING_WORDS_EDIT_ITEM_ROUTE_KEY";

    /**
     * smt自动上传市场图异步处理队列
     */
    public static final String SMT_AUTO_UPLOAD_MARKET_IMAGE_QUEUE = "SMT_AUTO_UPLOAD_MARKET_IMAGE_QUEUE";
    /**
     * smt自动上传市场图异步处理队列路由key
     */
    public static final String SMT_AUTO_UPLOAD_MARKET_IMAGE_ROUTE_KEY = "SMT_AUTO_UPLOAD_MARKET_IMAGE_ROUTE_KEY";

    /**
     * smt单品折扣创建任务队列
     */
    public static final String SMT_SINGLE_DISCOUNT_CREATE_TASK_QUEUE = "SMT_SINGLE_DISCOUNT_CREATE_TASK_QUEUE";
    /**
     * smt单品折扣创建任务队列路由key
     */
    public static final String SMT_SINGLE_DISCOUNT_CREATE_TASK_ROUTE_KEY = "SMT_SINGLE_DISCOUNT_CREATE_TASK_ROUTE_KEY";

    /**
     * smt单品折扣状态同步队列
     */
    public static final String SMT_SINGLE_DISCOUNT_SYNC_STATUS_QUEUE = "SMT_SINGLE_DISCOUNT_SYNC_STATUS_QUEUE";
    /**
     * smt单品折扣状态同步队列路由key
     */
    public static final String SMT_SINGLE_DISCOUNT_SYNC_STATUS_ROUTE_KEY = "SMT_SINGLE_DISCOUNT_SYNC_STATUS_ROUTE_KEY";

    /**
     * smt同步爬虫PV按店铺队列
     */
    public static final String SMT_SYNC_PV_ACCOUNT_QUEUE = "SMT_SYNC_PV_ACCOUNT_QUEUE";
    /**
     * smt同步爬虫PV按店铺队列路由key
     */
    public static final String SMT_SYNC_PV_ACCOUNT_ROUTE_KEY = "SMT_SYNC_PV_ACCOUNT_ROUTE_KEY";

    /**
     * smt定时上下架队列
     */
    public static final String SMT_TIMING_ON_OFF_PRODUCT_QUEUE = "SMT_TIMING_ON_OFF_PRODUCT_QUEUE";
    /**
     * smt定时上下架队列路由key
     */
    public static final String SMT_TIMING_ON_OFF_PRODUCT_ROUTE_KEY = "SMT_TIMING_ON_OFF_PRODUCT_ROUTE_KEY";

    /**
     * smt联盟商品计划队列
     */
    public static final String SMT_UPDATE_ALIANCE_PRODUCT_LISTING_QUEUE = "SMT_UPDATE_ALIANCE_PRODUCT_LISTING_QUEUE";
    /**
     * smt联盟商品计划队列路由key
     */
    public static final String SMT_UPDATE_ALIANCE_PRODUCT_LISTING_ROUTE_KEY = "SMT_UPDATE_ALIANCE_PRODUCT_LISTING_ROUTE_KEY";

    /**
     * smt子SKU图片更新队列
     */
    public static final String SMT_UPDATE_SON_SKU_IMAGE_QUEUE = "SMT_UPDATE_SON_SKU_IMAGE_QUEUE";
    /**
     * smt子SKU图片更新队列路由key
     */
    public static final String SMT_UPDATE_SON_SKU_IMAGE_ROUTE_KEY = "SMT_UPDATE_SON_SKU_IMAGE_ROUTE_KEY";

    /**
     * smt产品描述更新队列
     */
    public static final String SMT_UPDATE_DETAIL_QUEUE = "SMT_UPDATE_DETAIL_QUEUE";
    /**
     * smt产品描述更新队列路由key
     */
    public static final String SMT_UPDATE_DETAIL_ROUTE_KEY = "SMT_UPDATE_DETAIL_ROUTE_KEY";

    /**
     * smt产品标题更新队列
     */
    public static final String SMT_UPDATE_TITLE_QUEUE = "SMT_UPDATE_TITLE_QUEUE";
    /**
     * smt产品标题更新队列路由key
     */
    public static final String SMT_UPDATE_TITLE_ROUTE_KEY = "SMT_UPDATE_TITLE_ROUTE_KEY";

    /**
     * smt 利润核算修改重量队列
     */
    public static final String SMT_UPDATE_WEIGHT_QUEUE = "SMT_UPDATE_WEIGHT_QUEUE";
    /**
     * smt 利润核算修改重量队列路由key
     */
    public static final String SMT_UPDATE_WEIGHT_ROUTE_KEY = "SMT_UPDATE_WEIGHT_ROUTE_KEY";

    /**
     * smt同步产品队列
     */
    public static final String SMT_SYNCH_ITEM_QUEUE = "SMT_SYNCH_ITEM_QUEUE";
    /**
     * smt同步产品队列路由key
     */
    public static final String SMT_SYNCH_ITEM_ROUTE_KEY = "SMT_SYNCH_ITEM_ROUTE_KEY";

    /**
     * smt同步审核不通过产品队列
     */
    public static final String SMT_SYNCH_NOPASS_ITEM_QUEUE = "SMT_SYNCH_NOPASS_ITEM_QUEUE";
    /**
     * smt同步审核不通过产品队列路由key
     */
    public static final String SMT_SYNCH_NOPASS_ITEM_ROUTE_KEY = "SMT_SYNCH_NOPASS_ITEM_ROUTE_KEY";

    /**
     * smt同步审核产品队列
     */
    public static final String SMT_SYNCH_AUDITING_ITEM_QUEUE = "SMT_SYNCH_AUDITING_ITEM_QUEUE";
    /**
     * smt同步审核产品队列路由key
     */
    public static final String SMT_SYNCH_AUDITING_ITEM_ROUTE_KEY = "SMT_SYNCH_AUDITING_ITEM_ROUTE_KEY";

    /**
     * smt更新主图和标题队列
     */
    public static final String SMT_UPDATE_IMAGE_TITLE_QUEUE = "SMT_UPDATE_IMAGE_TITLE_QUEUE";
    /**
     * smt更新主图和标题队列路由key
     */
    public static final String SMT_UPDATE_IMAGE_TITLE_ROUTE_KEY = "SMT_UPDATE_IMAGE_TITLE_ROUTE_KEY";

    /**
     * Shopee 调价任务队列
     */
    public static final String SHOPEE_UPDATE_TASK_QUEUE = "SHOPEE_UPDATE_TASK_QUEUE";
    public static final String SHOPEE_UPDATE_TASK_QUEUE_KEY = "SHOPEE_UPDATE_TASK_QUEUE_KEY";

    /**
     * Walmart 自动刊登 key
     */
    public static final  String WALMART_AUTO_PUBLISH_QUEUE_KEY = "WALMART_AUTO_PUBLISH_QUEUE_KEY";
    /**
     * Walmart 自动刊登队列
     */
    public static final  String WALMART_AUTO_PUBLISH_QUEUE = "WALMART_AUTO_PUBLISH_QUEUE";


    /**
     * Amazon新品文案审核推送到产品系统
     */
    public static final String AMAZON_NEW_PRODUCT_COPYWRITING_REVIEW_TO_PRODUCT_QUEUE = "AMAZON_NEW_PRODUCT_COPYWRITING_REVIEW_TO_PRODUCT_QUEUE";
    public static final String AMAZON_NEW_PRODUCT_COPYWRITING_REVIEW_TO_PRODUCT_KEY = "AMAZON_NEW_PRODUCT_COPYWRITING_REVIEW_TO_PRODUCT_KEY";

}
