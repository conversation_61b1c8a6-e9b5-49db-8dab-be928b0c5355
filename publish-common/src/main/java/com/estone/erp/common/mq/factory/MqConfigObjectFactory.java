package com.estone.erp.common.mq.factory;

import com.estone.erp.common.mq.RabbitMqManualFactory;
import com.estone.erp.common.mq.model.VhBinding;
import com.estone.erp.common.mq.model.VhQueue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;

import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;

/**
 * MQ 配置对象工厂
 *
 * @Description: 通用的 MQ 配置对象工厂，用于创建队列、绑定、监听器和监听器容器，并自动注册到 Spring 容器
 * @Author: AI Assistant
 * @Date: 2024/12/19
 */
@Slf4j
@Component
public class MqConfigObjectFactory implements ApplicationContextAware {

    private final RabbitMqManualFactory rabbitMqManualFactory;
    private ApplicationContext applicationContext;

    public MqConfigObjectFactory(RabbitMqManualFactory rabbitMqManualFactory) {
        this.rabbitMqManualFactory = rabbitMqManualFactory;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }
    
    /**
     * 创建队列配置
     * 
     * @param parameters MQ 配置参数
     * @return VhQueue 队列配置对象
     */
    public VhQueue createQueue(MqConfigParameters parameters) {
        log.debug("创建队列配置: virtualHost={}, queueName={}", 
                parameters.getVirtualHost(), parameters.getQueueName());
        
        return new VhQueue(
                parameters.getVirtualHost(),
                parameters.getQueueName(),
                parameters.isDurable(),
                parameters.isExclusive(),
                parameters.isAutoDelete(),
                null
        );
    }
    
    /**
     * 创建队列绑定配置
     * 
     * @param parameters MQ 配置参数
     * @return VhBinding 队列绑定配置对象
     */
    public VhBinding createQueueBinding(MqConfigParameters parameters) {
        log.debug("创建队列绑定配置: virtualHost={}, queueName={}, exchange={}, routingKey={}", 
                parameters.getVirtualHost(), parameters.getQueueName(), 
                parameters.getExchange(), parameters.getRoutingKey());
        
        return new VhBinding(
                parameters.getVirtualHost(),
                parameters.getQueueName(),
                VhBinding.DestinationType.QUEUE,
                parameters.getExchange(),
                parameters.getRoutingKey(),
                null
        );
    }
    
    /**
     * 创建消息监听器容器
     * 
     * @param parameters MQ 配置参数
     * @return SimpleMessageListenerContainer 监听器容器
     */
    public SimpleMessageListenerContainer createMessageListenerContainer(MqConfigParameters parameters) {
        log.debug("创建消息监听器容器: virtualHost={}, queueName={}, consumers={}, prefetchCount={}, listenerEnabled={}", 
                parameters.getVirtualHost(), parameters.getQueueName(), 
                parameters.getConsumers(), parameters.getPrefetchCount(), parameters.isListenerEnabled());
        
        // 获取连接工厂
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(parameters.getVirtualHost());
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        
        // 记录配置信息
        log.info("{}监听器配置: listenerEnabled={}, consumers={}, prefetchCount={}", 
                parameters.getBeanNamePrefix(), parameters.isListenerEnabled(), 
                parameters.getConsumers(), parameters.getPrefetchCount());
        
        // 如果监听器启用，则配置容器
        if (parameters.isListenerEnabled()) {
            configureMessageListenerContainer(container, parameters);
        }
        
        return container;
    }
    
    /**
     * 配置消息监听器容器的详细参数
     * 
     * @param container 监听器容器
     * @param parameters MQ 配置参数
     */
    private void configureMessageListenerContainer(SimpleMessageListenerContainer container, MqConfigParameters parameters) {
        // 设置队列名称
        container.setQueueNames(parameters.getQueueName());
        
        // 暴露监听器通道
        container.setExposeListenerChannel(true);
        
        // 设置每个消费者获取的最大消息数量
        container.setPrefetchCount(parameters.getPrefetchCount());
        
        // 设置消费者个数
        container.setConcurrentConsumers(parameters.getConsumers());
        
        // 设置确认模式为手工确认
        container.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        
        // 设置监听处理类
        container.setMessageListener(parameters.getMessageListener());
        
        log.debug("消息监听器容器配置完成: queueName={}, consumers={}, prefetchCount={}", 
                parameters.getQueueName(), parameters.getConsumers(), parameters.getPrefetchCount());
    }
    
    /**
     * 创建并注册完整的 MQ 配置对象到 Spring 容器
     *
     * @param parameters MQ 配置参数
     */
    public void createAndRegisterMqObjects(MqConfigParameters parameters) {
        if (applicationContext instanceof ConfigurableApplicationContext) {
            ConfigurableListableBeanFactory beanFactory =
                ((ConfigurableApplicationContext) applicationContext).getBeanFactory();

            String beanNamePrefix = parameters.getBeanNamePrefix();

            // 创建并注册队列配置
            VhQueue queue = createQueue(parameters);
            String queueBeanName = beanNamePrefix + "Queue";
            beanFactory.registerSingleton(queueBeanName, queue);
            log.info("已注册队列配置 Bean: {}", queueBeanName);

            // 创建并注册队列绑定配置
            VhBinding queueBinding = createQueueBinding(parameters);
            String bindingBeanName = beanNamePrefix + "QueueBinding";
            beanFactory.registerSingleton(bindingBeanName, queueBinding);
            log.info("已注册队列绑定配置 Bean: {}", bindingBeanName);

            // 创建并注册消息监听器
            if (parameters.getMessageListener() != null) {
                String listenerBeanName = beanNamePrefix + "MqListener";
                beanFactory.registerSingleton(listenerBeanName, parameters.getMessageListener());
                log.info("已注册消息监听器 Bean: {}", listenerBeanName);
            }

            // 创建并注册监听器容器
            SimpleMessageListenerContainer container = createMessageListenerContainer(parameters);
            String containerBeanName = beanNamePrefix + "MqListenerContainer";
            beanFactory.registerSingleton(containerBeanName, container);
            log.info("已注册监听器容器 Bean: {}", containerBeanName);

        } else {
            log.warn("无法获取 ConfigurableApplicationContext，跳过 Bean 注册");
        }
    }


}
