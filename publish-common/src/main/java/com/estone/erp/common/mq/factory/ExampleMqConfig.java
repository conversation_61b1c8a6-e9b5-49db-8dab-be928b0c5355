//package com.estone.erp.common.mq.factory;
//
//import com.estone.erp.common.mq.PublishRabbitMqExchange;
//import com.estone.erp.common.mq.RabbitMqVirtualHosts;
//import com.rabbitmq.client.Channel;
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.amqp.core.Message;
//import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
//import org.springframework.beans.factory.InitializingBean;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Configuration;
//
//import java.nio.charset.StandardCharsets;
//
///**
// * 示例 MQ 配置类
// *
// * @Description: 展示如何使用通用对象工厂模式创建 MQ 配置的完整示例
// * @Author: AI Assistant
// * @Date: 2024/12/19
// */
//@Slf4j
//@Configuration
//@ConfigurationProperties(prefix = "example-mq-config")
//@Data
//public class ExampleMqConfig implements InitializingBean {
//
//    /**
//     * MQ 配置助手，用于创建和管理 MQ 相关对象
//     */
//    @Autowired
//    private MqConfigHelper mqConfigHelper;
//
//    /**
//     * 消费者数量配置
//     */
//    private int exampleMqConsumers = 1;
//
//    /**
//     * 预取数量配置
//     */
//    private int exampleMqPrefetchCount = 10;
//
//    /**
//     * 监听器开关配置
//     */
//    private boolean exampleMqListener = false;
//
//    /**
//     * 创建示例消息监听器
//     *
//     * @return ExampleMqListener 消息监听器
//     */
//    private ExampleMqListener createMessageListener() {
//        log.debug("创建 ExampleMqListener 实例");
//        return new ExampleMqListener();
//    }
//
//    /**
//     * 在 Bean 初始化完成后自动创建并注册 MQ 相关对象
//     *
//     * @throws Exception 初始化异常
//     */
//    @Override
//    public void afterPropertiesSet() throws Exception {
//        log.info("开始初始化示例 MQ 配置");
//
//        // 创建消息监听器
//        ExampleMqListener messageListener = createMessageListener();
//
//        // 使用助手类创建并注册所有 MQ 相关对象
//        mqConfigHelper.createAndRegisterMqConfig(
//                RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST,
//                "EXAMPLE_QUEUE",
//                "EXAMPLE_QUEUE_KEY",
//                PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE,
//                exampleMqConsumers,
//                exampleMqPrefetchCount,
//                messageListener,
//                exampleMqListener,
//                "example"
//        );
//
//        log.info("示例 MQ 配置初始化完成");
//    }
//
//    /**
//     * 示例消息监听器实现
//     */
//    public static class ExampleMqListener implements ChannelAwareMessageListener {
//
//        @Override
//        public void onMessage(Message message, Channel channel) throws Exception {
//            String body = new String(message.getBody(), StandardCharsets.UTF_8);
//            try {
//                log.info("接收到示例消息：{}", body);
//
//                // 这里处理具体的业务逻辑
//                processMessage(body);
//
//                // 手动确认消息
//                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
//                log.debug("示例消息处理完成并确认");
//
//            } catch (Exception e) {
//                log.error("处理示例消息时发生错误：{}", body, e);
//
//                // 根据业务需要决定是否重新入队
//                // false 表示不重新入队，true 表示重新入队
//                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
//            }
//        }
//
//        /**
//         * 处理消息的具体业务逻辑
//         *
//         * @param messageBody 消息内容
//         */
//        private void processMessage(String messageBody) {
//            // 这里实现具体的业务逻辑
//            log.info("正在处理消息内容：{}", messageBody);
//
//            // 模拟业务处理
//            try {
//                Thread.sleep(100); // 模拟处理时间
//            } catch (InterruptedException e) {
//                Thread.currentThread().interrupt();
//                throw new RuntimeException("消息处理被中断", e);
//            }
//
//            log.info("消息处理完成");
//        }
//    }
//}
