package com.estone.erp.test.walmart;

/**
 * @Auther yucm
 * @Date 2020/11/21
 */

import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.walmart.call.authentication.WalmartGetTokenCall;
import org.junit.Test;

public class WalmartAccountTest {

    @Test
    public void testWalmartGetTokenCall() {
        SaleAccountAndBusinessResponse walmartPmsAccount = new SaleAccountAndBusinessResponse();
        walmartPmsAccount.setClientId(TestConstants.CLIENT_ID);
        walmartPmsAccount.setClientSecret(TestConstants.CLIENT_SECRET);

        String accessToken = new WalmartGetTokenCall(walmartPmsAccount).execute();
        System.out.println(accessToken);
    }
}