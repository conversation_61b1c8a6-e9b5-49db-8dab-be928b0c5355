<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.tidb.publishtidb.mapper.OzonReportProblemMaintainMapper">

    <resultMap id="BaseResultMap" type="com.estone.erp.publish.tidb.publishtidb.model.OzonReportProblemMaintain">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="operationType" column="operation_type" jdbcType="VARCHAR"/>
            <result property="problemType" column="problem_type" jdbcType="VARCHAR"/>
            <result property="report" column="report" jdbcType="VARCHAR"/>
            <result property="solutionType" column="solution_type" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,operation_type,problem_type,
        report,solution_type,created_by,
        created_time,updated_by,updated_time
    </sql>
    <select id="selectSolutionByErrorMsg"
            resultType="com.estone.erp.publish.tidb.publishtidb.model.OzonReportProblemMaintain">
        SELECT *,
               CASE
                   WHEN #{errorMsg} = report THEN 100
                   WHEN #{errorMsg} LIKE CONCAT(report, '%') THEN 80
                   WHEN #{errorMsg} LIKE CONCAT('%', report) THEN 60
                   WHEN #{errorMsg} LIKE CONCAT('%', report, '%') THEN 40
                   ELSE 0
                   END AS match_score,
               100 - LENGTH(report)/10 AS length_score
        FROM aliexpress_report_problem_maintain
        WHERE operation_type = #{operationType}
          AND #{errorMsg} LIKE CONCAT('%', report, '%')
        ORDER BY
            match_score DESC,
            LENGTH(report) ASC,
            updated_time DESC
        LIMIT 1;
    </select>
</mapper>
