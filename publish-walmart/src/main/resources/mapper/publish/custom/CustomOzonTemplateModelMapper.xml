<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.ozon.mapper.custom.CustomOzonTemplateModelMapper" >
    <resultMap id="BaseResultMap" type="com.estone.erp.publish.ozon.model.OzonTemplateModel" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
        <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
        <result column="sku_data_source" property="skuDataSource" jdbcType="TINYINT" />
        <result column="category_id" property="categoryId" jdbcType="INTEGER" />
        <result column="category_id_path" property="categoryIdPath" jdbcType="VARCHAR" />
        <result column="category_name_path" property="categoryNamePath" jdbcType="VARCHAR" />
        <result column="main_img" property="mainImg" jdbcType="VARCHAR" />
        <result column="title" property="title" jdbcType="VARCHAR" />
        <result column="description" property="description" jdbcType="VARCHAR" />
        <result column="vat" property="vat" jdbcType="VARCHAR" />
        <result column="video" property="video" jdbcType="VARCHAR" />
        <result column="sale_variant" property="saleVariant" jdbcType="BIT" />
        <result column="need_translate" property="needTranslate" jdbcType="BIT" />
        <result column="currency_code" property="currencyCode" jdbcType="VARCHAR" />
        <result column="category_attribute" property="categoryAttribute" jdbcType="VARCHAR" />
        <result column="merge_attribute" property="mergeAttribute" jdbcType="VARCHAR" />
        <result column="sku_size" property="skuSize" jdbcType="INTEGER" />
        <result column="variant_data" property="variantData" jdbcType="VARCHAR" />
        <result column="publish_type" property="publishType" jdbcType="INTEGER" />
        <result column="publish_role" property="publishRole" jdbcType="INTEGER" />
        <result column="publish_status" property="publishStatus" jdbcType="INTEGER" />
        <result column="inventory_upload" property="inventoryUpload" jdbcType="INTEGER" />
        <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR" />
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
        <result column="problem_type" property="problemType" jdbcType="VARCHAR" />
        <result column="problem_type_date" property="problemTypeDate" jdbcType="TIMESTAMP" />
    </resultMap>
    <select id="listIdByIdAndPublishStatus" resultType="java.lang.Integer">
        select id from ozon_template_model where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
         and publish_status in
        <foreach collection="publishStatus" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>
    </select>
    <select id="getPublishDataById" resultMap="BaseResultMap">
        select id,account_number,article_number, sku_data_source, title,publish_status, category_id from ozon_template_model where id = #{id}
    </select>
    <select id="listCanUploadStockId" resultMap="BaseResultMap">
        select id,account_number,article_number,sku_data_source,publish_status,inventory_upload from ozon_template_model where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and publish_status = 3 and inventory_upload in (4,5)
    </select>
    <select id="getVariantDataById" resultType="java.lang.String">
        select variant_data from ozon_template_model where id = #{id}
    </select>
    <select id="listExistPublishedTemplate" resultMap="BaseResultMap">
        select id,account_number,article_number,sku_data_source,publish_status from ozon_template_model where account_number = #{accountNumber}
        and publish_status in (2,3) and updated_time between #{fromTime} and #{toTime}
        and article_number in
        <foreach collection="articleNumbers" item="articleNumber" open="(" close=")" separator=",">
            #{articleNumber}
        </foreach>

    </select>

    <select id="countPublishedSkusByTimeRange" resultType="java.lang.Integer">
        select IFNULL(sum(sku_size), 0) from ozon_template_model 
        where account_number = #{accountNumber}
        and publish_status in (2,3) 
        and updated_time between #{fromTime} and #{toTime}
        <if test="notId != null">
            and id != #{notId,jdbcType=INTEGER}
        </if>
    </select>

</mapper>
