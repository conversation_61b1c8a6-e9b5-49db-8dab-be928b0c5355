<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.ozon.mapper.OzonTemplateModelMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.ozon.model.OzonTemplateModel" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
    <result column="sku_data_source" property="skuDataSource" jdbcType="TINYINT" />
    <result column="category_id" property="categoryId" jdbcType="INTEGER" />
    <result column="category_id_path" property="categoryIdPath" jdbcType="VARCHAR" />
    <result column="category_name_path" property="categoryNamePath" jdbcType="VARCHAR" />
    <result column="main_img" property="mainImg" jdbcType="VARCHAR" />
    <result column="title" property="title" jdbcType="VARCHAR" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="vat" property="vat" jdbcType="VARCHAR" />
    <result column="video" property="video" jdbcType="VARCHAR" />
    <result column="sale_variant" property="saleVariant" jdbcType="BIT" />
    <result column="need_translate" property="needTranslate" jdbcType="BIT" />
    <result column="currency_code" property="currencyCode" jdbcType="VARCHAR" />
    <result column="category_attribute" property="categoryAttribute" jdbcType="VARCHAR" />
    <result column="merge_attribute" property="mergeAttribute" jdbcType="VARCHAR" />
    <result column="sku_size" property="skuSize" jdbcType="INTEGER" />
    <result column="variant_data" property="variantData" jdbcType="VARCHAR" />
    <result column="publish_type" property="publishType" jdbcType="INTEGER" />
    <result column="publish_role" property="publishRole" jdbcType="INTEGER" />
    <result column="publish_status" property="publishStatus" jdbcType="INTEGER" />
    <result column="inventory_upload" property="inventoryUpload" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="updated_by" property="updatedBy" jdbcType="VARCHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
    <result column="problem_type" property="problemType" jdbcType="VARCHAR" />
    <result column="problem_type_date" property="problemTypeDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, account_number, article_number, sku_data_source, category_id, category_id_path,
    category_name_path, main_img, title, description, vat, video, sale_variant, need_translate,
    currency_code, category_attribute, merge_attribute, sku_size, variant_data, publish_type, publish_role,
    publish_status, inventory_upload, created_by, updated_by, created_time, updated_time,
    problem_type, problem_type_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.ozon.model.OzonTemplateModelExample" >
    select
    <choose>
      <when test="columns != null and columns != ''">
        ${columns}
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from ozon_template_model
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ozon_template_model
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from ozon_template_model
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.ozon.model.OzonTemplateModel" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ozon_template_model (account_number, article_number, sku_data_source,
      category_id, category_id_path, category_name_path,
      main_img, title, description,
      vat, video, sale_variant,
      need_translate, currency_code, category_attribute,
      merge_attribute, sku_size, variant_data, publish_type,
      publish_role, publish_status, inventory_upload,
      created_by, updated_by, created_time,
      updated_time, problem_type, problem_type_date)
    values (#{accountNumber,jdbcType=VARCHAR}, #{articleNumber,jdbcType=VARCHAR}, #{skuDataSource,jdbcType=TINYINT},
      #{categoryId,jdbcType=INTEGER}, #{categoryIdPath,jdbcType=VARCHAR}, #{categoryNamePath,jdbcType=VARCHAR},
      #{mainImg,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
      #{vat,jdbcType=VARCHAR}, #{video,jdbcType=VARCHAR}, #{saleVariant,jdbcType=BIT},
      #{needTranslate,jdbcType=BIT}, #{currencyCode,jdbcType=VARCHAR}, #{categoryAttribute,jdbcType=VARCHAR},
      #{mergeAttribute,jdbcType=VARCHAR}, #{skuSize,jdbcType=INTEGER}, #{variantData,jdbcType=VARCHAR}, #{publishType,jdbcType=INTEGER},
      #{publishRole,jdbcType=INTEGER}, #{publishStatus,jdbcType=INTEGER}, #{inventoryUpload,jdbcType=INTEGER},
      #{createdBy,jdbcType=VARCHAR}, #{updatedBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP},
      #{updatedTime,jdbcType=TIMESTAMP}, #{problemType,jdbcType=VARCHAR}, #{problemTypeDate,jdbcType=TIMESTAMP})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.ozon.model.OzonTemplateModelExample" resultType="java.lang.Integer" >
    select count(*) from ozon_template_model
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update ozon_template_model
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.articleNumber != null" >
        article_number = #{record.articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.skuDataSource != null" >
        sku_data_source = #{record.skuDataSource,jdbcType=TINYINT},
      </if>
      <if test="record.categoryId != null" >
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.categoryIdPath != null" >
        category_id_path = #{record.categoryIdPath,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryNamePath != null" >
        category_name_path = #{record.categoryNamePath,jdbcType=VARCHAR},
      </if>
      <if test="record.mainImg != null" >
        main_img = #{record.mainImg,jdbcType=VARCHAR},
      </if>
      <if test="record.title != null" >
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null" >
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.vat != null" >
        vat = #{record.vat,jdbcType=VARCHAR},
      </if>
      <if test="record.video != null" >
        video = #{record.video,jdbcType=VARCHAR},
      </if>
      <if test="record.saleVariant != null" >
        sale_variant = #{record.saleVariant,jdbcType=BIT},
      </if>
      <if test="record.needTranslate != null" >
        need_translate = #{record.needTranslate,jdbcType=BIT},
      </if>
      <if test="record.currencyCode != null" >
        currency_code = #{record.currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryAttribute != null" >
        category_attribute = #{record.categoryAttribute,jdbcType=VARCHAR},
      </if>
      <if test="record.mergeAttribute != null" >
        merge_attribute = #{record.mergeAttribute,jdbcType=VARCHAR},
      </if>
      <if test="record.skuSize != null" >
        sku_size = #{record.skuSize,jdbcType=INTEGER},
      </if>
      <if test="record.variantData != null" >
        variant_data = #{record.variantData,jdbcType=VARCHAR},
      </if>
      <if test="record.publishType != null" >
        publish_type = #{record.publishType,jdbcType=INTEGER},
      </if>
      <if test="record.publishRole != null" >
        publish_role = #{record.publishRole,jdbcType=INTEGER},
      </if>
      <if test="record.publishStatus != null" >
        publish_status = #{record.publishStatus,jdbcType=INTEGER},
      </if>
      <if test="record.inventoryUpload != null" >
        inventory_upload = #{record.inventoryUpload,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null" >
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdTime != null" >
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedTime != null" >
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.problemType != null" >
        problem_type = #{record.problemType,jdbcType=VARCHAR},
      </if>
      <if test="record.problemTypeDate != null" >
        problem_type_date = #{record.problemTypeDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.ozon.model.OzonTemplateModel" >
    update ozon_template_model
    <set >
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="articleNumber != null" >
        article_number = #{articleNumber,jdbcType=VARCHAR},
      </if>
      <if test="skuDataSource != null" >
        sku_data_source = #{skuDataSource,jdbcType=TINYINT},
      </if>
      <if test="categoryId != null" >
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="categoryIdPath != null" >
        category_id_path = #{categoryIdPath,jdbcType=VARCHAR},
      </if>
      <if test="categoryNamePath != null" >
        category_name_path = #{categoryNamePath,jdbcType=VARCHAR},
      </if>
      <if test="mainImg != null" >
        main_img = #{mainImg,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="vat != null" >
        vat = #{vat,jdbcType=VARCHAR},
      </if>
      <if test="video != null" >
        video = #{video,jdbcType=VARCHAR},
      </if>
      <if test="saleVariant != null" >
        sale_variant = #{saleVariant,jdbcType=BIT},
      </if>
      <if test="needTranslate != null" >
        need_translate = #{needTranslate,jdbcType=BIT},
      </if>
      <if test="currencyCode != null" >
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryAttribute != null" >
        category_attribute = #{categoryAttribute,jdbcType=VARCHAR},
      </if>
      <if test="mergeAttribute != null" >
        merge_attribute = #{mergeAttribute,jdbcType=VARCHAR},
      </if>
      <if test="skuSize != null" >
        sku_size = #{skuSize,jdbcType=INTEGER},
      </if>
      <if test="variantData != null" >
        variant_data = #{variantData,jdbcType=VARCHAR},
      </if>
      <if test="publishType != null" >
        publish_type = #{publishType,jdbcType=INTEGER},
      </if>
      <if test="publishRole != null" >
        publish_role = #{publishRole,jdbcType=INTEGER},
      </if>
      <if test="publishStatus != null" >
        publish_status = #{publishStatus,jdbcType=INTEGER},
      </if>
      <if test="inventoryUpload != null" >
        inventory_upload = #{inventoryUpload,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null" >
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null" >
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="problemType != null" >
        problem_type = #{problemType,jdbcType=VARCHAR},
      </if>
      <if test="problemTypeDate != null" >
        problem_type_date = #{problemTypeDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
