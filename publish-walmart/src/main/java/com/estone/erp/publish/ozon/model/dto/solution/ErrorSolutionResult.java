package com.estone.erp.publish.ozon.model.dto.solution;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public class ErrorSolutionResult {

    private final String errorReport;
    private final boolean success;
    private final String message;
    private final List<SolutionItem> solutions;
    private final long duration;

    private ErrorSolutionResult(String errorReport, boolean success, String message,
                                List<SolutionItem> solutions, long duration) {
        this.errorReport = errorReport;
        this.success = success;
        this.message = message;
        this.solutions = solutions != null ? solutions : new ArrayList<>();
        this.duration = duration;
    }

    public static ErrorSolutionResult success(String errorReport, List<SolutionItem> solutions, long duration) {
        return new ErrorSolutionResult(errorReport, true, "成功", solutions, duration);
    }

    public static ErrorSolutionResult noSolution(String errorReport, String message) {
        return new ErrorSolutionResult(errorReport, false, message, null, 0);
    }

    public static ErrorSolutionResult error(String errorReport, String message) {
        return new ErrorSolutionResult(errorReport, false, message, null, 0);
    }

}
