package com.estone.erp.publish.ozon.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.util.GoogleTranslateUtils;
import com.estone.erp.common.util.YandexTranslateUtils;
import com.estone.erp.publish.ozon.enums.OzonEnums;
import com.estone.erp.publish.ozon.model.OzonAccountConfig;
import com.estone.erp.publish.ozon.model.OzonTemplateModel;
import com.estone.erp.publish.ozon.model.dto.OzonCalcPriceRequest;
import com.estone.erp.publish.ozon.model.dto.OzonCalcPriceResponse;
import com.estone.erp.publish.ozon.model.dto.template.OzonSkuDO;
import com.estone.erp.publish.ozon.model.dto.template.OzonTemplateDO;
import com.estone.erp.publish.system.product.bean.SpuOfficial;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-07-10 10:58
 */
public class OzonTemplateDataUtil {

    public static boolean checkCompletenessOfTitleAndDesc(SpuOfficial spuOfficial) {
        List<String> titleList = null;
        if (StringUtils.isNotBlank(spuOfficial.getShortTitleJson())) {
            titleList = parsingStrList(spuOfficial.getShortTitleJson());
        }
        if (CollectionUtils.isEmpty(titleList)){
            if (StringUtils.isNotBlank(spuOfficial.getTitle())) {
                titleList = parsingStrList(spuOfficial.getTitle());
            }
        }
        String title = null;
        if(CollectionUtils.isNotEmpty(titleList)) {
            Collections.shuffle(titleList);
            title = titleList.get(0);
        }
        if (StringUtils.isBlank(title)) {
            return false;
        }

        String description = spuOfficial.getNewDescription();
        if(StringUtils.isBlank(description)) {
            description = spuOfficial.getDescription();
            if (StringUtils.isNotBlank(description) && description.startsWith("[")) {
                List<String> descriptions = parsingStrList(description);
                if (CollectionUtils.isNotEmpty(descriptions)) {
                    description = descriptions.get(0);
                } else {
                    description = "";
                }
            }
        }
        if (StringUtils.isBlank(description)) {
            return false;
        }
        return true;
    }

    /**
     * 取产品系统文案信息中的标题；优先级：短标题 > sku标题；
     * 若标题中存在相同的单词，则只保存第一次出现的单词位置，其他重复的进行删除
     * 优先级：描述新>描述；删除描述中存在描述颜色的词
     */
    public static void setTitleAndDesc(SpuOfficial spuOfficial, OzonTemplateDO templateData) {
        setTitleAndDesc(spuOfficial, templateData::setTitle, templateData::setDescription);
    }

    public static void setTitleAndDesc(SpuOfficial spuOfficial, Consumer<String> titleConsumer, Consumer<String> descConsumer) {
        // 短标题 > title > sku标题
        List<String> titleList = null;
        if (StringUtils.isNotBlank(spuOfficial.getShortTitleJson())) {
            titleList = parsingStrList(spuOfficial.getShortTitleJson());
        }
        if (CollectionUtils.isEmpty(titleList)){
            if (StringUtils.isNotBlank(spuOfficial.getTitle())) {
                titleList = parsingStrList(spuOfficial.getTitle());
            }
        }

        String title = null;
        if(CollectionUtils.isNotEmpty(titleList)) {
            Collections.shuffle(titleList);
            title = titleList.get(0);
        }

        //优先新描述
        String description = spuOfficial.getNewDescription();
        if(StringUtils.isBlank(description)){
            description = spuOfficial.getDescription();
            if(StringUtils.isNotBlank(description) && description.startsWith("[")){
                List<String> descriptions = parsingStrList(description);
                if (CollectionUtils.isNotEmpty(descriptions)) {
                    description = descriptions.get(0);
                } else {
                    description = "";
                }
            }
        }else {
            // Ozon 描述新 去除规则中带颜色的描述
            if (!"ru".equalsIgnoreCase(spuOfficial.getLanguage())) {
                description = OzonTemplateDataUtil.removeNewDescriptionColor(spuOfficial);
            }
        }
        // 标题中存在相同的单词，则只保存第一次出现的单词位置，其他重复的进行删除
        title = OzonTemplateDataUtil.removeTitleDuplicateWord(title);
        if (Objects.nonNull(titleConsumer)) {
            titleConsumer.accept(title);
        }
        if (Objects.nonNull(descConsumer)) {
            descConsumer.accept(description);
        }
    }

    /**
     * Ozon描述新删除规则中Color部分
     * @return 文案信息描述新
     */
    public static String removeNewDescriptionColor(SpuOfficial spuOfficial) {
        if (spuOfficial == null) {
            return null;
        }
        String newDescription = "";
        String featuresJson = spuOfficial.getFeaturesJson();
        String features = spuOfficial.getFeatures();
        String specifications = spuOfficial.getSpecifications();
        String packageIncludes = spuOfficial.getPackageIncludes();
        String note = spuOfficial.getNote();
        // Features
        if(StringUtils.isNotEmpty(featuresJson) || StringUtils.isNotEmpty(features)) {
            StringBuilder featureStr = new StringBuilder();
            String tempString = StringUtils.isNotEmpty(featuresJson) ? featuresJson : features;
            List<String> featureList = JSON.parseObject(tempString, new TypeReference<>() {});
            if(CollectionUtils.isNotEmpty(featureList)) {
                for (String feature :featureList) {
                    if(StringUtils.isNotBlank(feature)) {
                        featureStr.append(feature).append("\n");
                    }
                }
            }
            if(StringUtils.isNotBlank(featureStr.toString())) {
                newDescription = newDescription +  "Features: " + featureStr + "\n";
            }
        }

        // Specification
        if(StringUtils.isNotEmpty(specifications)) {
            String[] specificationLine = specifications.split("\n");
            StringBuilder sb = new StringBuilder();
            for (String specification : specificationLine) {
                if (!specification.startsWith("Color")) {
                    sb.append(specification).append("\n");
                }
            }
            newDescription = newDescription +  "Specifications: " + sb + "\n\n";
        }

        // Package Includes
        if(StringUtils.isNotEmpty(packageIncludes)) {
            newDescription = newDescription +  "Package Includes: " + packageIncludes + "\n\n";
        }

        // note
        if(StringUtils.isNotEmpty(note)) {
            newDescription = newDescription +  "note: " + note + "\n";
        }
        return newDescription;
    }

    /**
     * 移除标题重复的词汇
     * @return 标题
     */
    public static String removeTitleDuplicateWord(String title) {
        if (StringUtils.isBlank(title)) {
            return title;
        }
        String reTitle = title.replaceAll(",", "").replaceAll("\\.", "");
        String[] words = reTitle.split(" ");
        StringBuilder result = new StringBuilder();
        Set<String> uniqueWords = new LinkedHashSet<>(Arrays.asList(words));
        for (String word : uniqueWords) {
            result.append(word).append(" ");
        }
        return result.toString();
    }

    /**
     * 翻译
     * @param txt 待翻译文本
     * @return
     */
    public static String translate(String txt) {
//        throw new RuntimeException("文案翻译失败，请编辑模板翻译文案后重新刊登。");
        String translate = YandexTranslateUtils.translate(OzonEnums.Lang.EN.name().toLowerCase(), OzonEnums.Lang.RU.name().toLowerCase(), txt, 3);
        if (StringUtils.isBlank(translate)) {
            translate = GoogleTranslateUtils.translate(OzonEnums.Lang.EN.name().toLowerCase(), OzonEnums.Lang.RU.name().toLowerCase(), txt, 3);
        }
        if (StringUtils.isNotBlank(translate)) {
            return translate;
        }
        throw new RuntimeException("文案翻译失败，请编辑模板翻译文案后重新刊登。");
    }

    /**
     * 翻译
     * @param txt 待翻译文本
     * @return
     */
    public static String translate(List<String> txt) {
//        throw new RuntimeException("文案翻译失败，请编辑模板翻译文案后重新刊登。");
        String translate = YandexTranslateUtils.translate(OzonEnums.Lang.EN.name().toLowerCase(), OzonEnums.Lang.RU.name().toLowerCase(), txt, 3);
        if (StringUtils.isBlank(translate)) {
            translate = GoogleTranslateUtils.translate(OzonEnums.Lang.EN.name().toLowerCase(), OzonEnums.Lang.RU.name().toLowerCase(), txt, 3);
        }
        if (StringUtils.isNotBlank(translate)) {
            return translate;
        }
        throw new RuntimeException("文案翻译失败，请编辑模板翻译文案后重新刊登。");
    }

    public static List<OzonCalcPriceResponse> calcPrice(List<OzonSkuDO> ozonSkuDOS, Double grossProfitRate, OzonAccountConfig accountConfig) {
        List<OzonCalcPriceRequest> calcPriceRequests = new ArrayList<>();
        for (OzonSkuDO ozonSkuDO : ozonSkuDOS) {
            OzonCalcPriceRequest calcPriceRequest = new OzonCalcPriceRequest();
            calcPriceRequest.setBusinessId(ozonSkuDO.getSku());
            calcPriceRequest.setSku(ozonSkuDO.getSku());
            calcPriceRequest.setWeight(Double.valueOf(ozonSkuDO.getWeight()));
            calcPriceRequest.setLabels(ozonSkuDO.getProductTag());
            calcPriceRequest.setAccountNumber(accountConfig.getAccountNumber());
            calcPriceRequest.setGrossProfitRate(grossProfitRate);
            calcPriceRequest.setWarehouseId(accountConfig.getUpdateStockWarehouseId());
            calcPriceRequest.setCurrency(accountConfig.getCurrency());
            calcPriceRequest.setSpecialGoodsCode(ozonSkuDO.getSpecialTypeList());
            calcPriceRequests.add(calcPriceRequest);
        }
        return OzonCalcUtils.batchCalc(calcPriceRequests);
    }

    public static String getFirstSellerSku(OzonTemplateModel templateModel) {
        if (templateModel == null || templateModel.getVariantData() == null) {
            return "";
        }
        List<OzonSkuDO> ozonSkuDOS = JSON.parseArray(templateModel.getVariantData(), OzonSkuDO.class);
        if (CollectionUtils.isNotEmpty(ozonSkuDOS)) {
            return ozonSkuDOS.get(0).getSellerSku();
        }
        return "";
    }

    private static List<String> parsingStrList(String titleJson) {
        List<String> longTitles = JSON.parseObject(titleJson, new TypeReference<>() {});
        return longTitles.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

}
