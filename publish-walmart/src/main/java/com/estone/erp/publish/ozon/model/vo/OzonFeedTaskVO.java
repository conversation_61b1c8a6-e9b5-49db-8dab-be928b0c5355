package com.estone.erp.publish.ozon.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.component.converter.SaleNameConverter;
import com.estone.erp.publish.component.converter.TimestampFormatConverter;
import com.estone.erp.publish.ozon.converter.TaskTypeConverter;
import com.estone.erp.publish.ozon.enums.OzonFeedTaskEnums;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Ozon Feed Task VO for export
 *
 * <AUTHOR>
 */
@Data
public class OzonFeedTaskVO {
    @ExcelProperty("店铺")
    private String accountNumber;

    @ExcelProperty("商品/模板编码")
    private String associationId;

    @ExcelProperty("sellersku")
    private String attribute1;

    @ExcelProperty("SKU")
    private String articleNumber;

    @ExcelProperty("改前值")
    private String attribute3;

    @ExcelProperty("改后值")
    private String attribute4;

    @ExcelProperty(value = "任务类型", converter = TaskTypeConverter.class)
    private String taskType;

    @ExcelProperty("规则名称")
    private String attribute6;

    @ExcelProperty("请求状态")
    private String taskStatus;

    @ExcelProperty("结果状态")
    private String resultStatus;

    @ExcelProperty("备注")
    private String resultMsg;

    @ExcelProperty(value = "创建人", converter = SaleNameConverter.class)
    private String createdBy;

    @ExcelProperty(value = "创建时间", converter = TimestampFormatConverter.class)
    private Timestamp createTime;


    @ExcelProperty(value = "完成时间", converter = TimestampFormatConverter.class)
    private Timestamp finishTime;

    @ExcelProperty(value = "问题分类")
    public String problemTypeStr;

    /**
     * Convert FeedTask to OzonFeedTaskVO
     *
     * @param feedTask FeedTask
     * @return OzonFeedTaskVO
     */
    public static OzonFeedTaskVO convertFromFeedTask(FeedTask feedTask) {
        if (feedTask == null) {
            return null;
        }

        OzonFeedTaskVO vo = new OzonFeedTaskVO();
        vo.setAssociationId(feedTask.getAssociationId());
        vo.setAccountNumber(feedTask.getAccountNumber());
        vo.setArticleNumber(feedTask.getArticleNumber());
        OzonFeedTaskEnums.TaskType taskType = OzonFeedTaskEnums.TaskType.valueOf(feedTask.getTaskType());
        if (ObjectUtils.isNotEmpty(taskType)) {
            vo.setTaskType(taskType.getDesc());
        }

        // Convert task status to string
        Integer taskStatusCode = feedTask.getTaskStatus();
        if (taskStatusCode != null) {
            if (taskStatusCode == FeedTaskStatusEnum.WAITING.getTaskStatus()) {
                vo.setTaskStatus("等待中");
            } else if (taskStatusCode == FeedTaskStatusEnum.RUNNING.getTaskStatus()) {
                vo.setTaskStatus("执行中");
            } else if (taskStatusCode == FeedTaskStatusEnum.FINISH.getTaskStatus()) {
                vo.setTaskStatus("已完成");
            } else {
                vo.setTaskStatus(String.valueOf(taskStatusCode));
            }
        }

        // Convert result status to string
        Integer resultStatusCode = feedTask.getResultStatus();
        if (resultStatusCode != null) {
            if (resultStatusCode == FeedTaskResultStatusEnum.SUCCESS.getResultStatus()) {
                vo.setResultStatus("成功");
            } else if (resultStatusCode == FeedTaskResultStatusEnum.FAIL.getResultStatus()) {
                vo.setResultStatus("失败");
            } else if (resultStatusCode == FeedTaskResultStatusEnum.PARTIAL_SUCCESS.getResultStatus()) {
                vo.setResultStatus("部分成功");
            } else {
                vo.setResultStatus(String.valueOf(resultStatusCode));
            }
        }

        if (StringUtils.isNotBlank(feedTask.getResultMsg())) {
            if (feedTask.getResultMsg().length() > 32767) {
                vo.setResultMsg(feedTask.getResultMsg().substring(0, 32767));
            } else {
                vo.setResultMsg(feedTask.getResultMsg());
            }
        }
        vo.setCreatedBy(feedTask.getCreatedBy());
        vo.setCreateTime(feedTask.getCreateTime());
        vo.setFinishTime(feedTask.getFinishTime());
        vo.setAttribute1(feedTask.getAttribute1());
        vo.setAttribute3(feedTask.getAttribute3());
        vo.setAttribute4(feedTask.getAttribute4());
        vo.setAttribute6(feedTask.getAttribute6());

        String attribute12 = feedTask.getAttribute12();
        if (StringUtils.isNotBlank(attribute12)) {
            try {
                //{"allProblemTypes":["标题不能为空","销售原因"],"solutions":[{"id":30010,"solutionType":"标题不能为空","problemType":"标题不能为空"},{"id":30009,"solutionType":"描述不能为空","problemType":"销售原因"}],"primaryProblemType":"标题不能为空","processTime":"2025-07-28T15:17:07.029065100","solutionCount":2}
                JSONObject jsonObject = JSON.parseObject(attribute12);
                JSONArray allProblemTypes = jsonObject.getJSONArray("allProblemTypes");
                if (allProblemTypes != null && !allProblemTypes.isEmpty()) {
                    String collect = allProblemTypes.stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.joining("\n"));
                    vo.setProblemTypeStr(collect);
                }
            } catch (Exception e) {
                // ignore
            }
        }

        return vo;
    }
}
