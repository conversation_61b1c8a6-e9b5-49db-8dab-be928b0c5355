package com.estone.erp.publish.ozon.excel.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.ozon.excel.handler.validator.ValidationResult;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * 修改标题和描述
 * UpdateTitleAndDescriptionExcel
 */
@Data
public class UpdateTitleAndDescriptionExcel implements IExcelRow {
    @ExcelProperty(value = "账号*", index = 0)
    private String accountNumber;
    @ExcelProperty(value = "商品编码*", index = 1)
    private String productId;
    @ExcelProperty(value = "sellerSku*", index = 2)
    private String sellerSku;
    @ExcelProperty(value = "SKU*", index = 3)
    private String sku;
    @ExcelProperty(value = "标题", index = 4)
    private String title;

    @ExcelProperty(value = "描述", index = 5)
    private String description;

    @ExcelProperty(value = "结果", index = 6)
    private String result;
    @ExcelProperty(value = "备注", index = 7)
    private String remark;

    @ExcelIgnore
    private Integer rowIndex;
    @ExcelIgnore
    private Integer excelId;

    @ExcelIgnore
    private String newDescription;

    @ExcelIgnore
    private String newTitle;

    @ExcelIgnore
    private boolean updateTitle;

    @ExcelIgnore
    private boolean updateDesc;

    @ExcelIgnore
    private Long ozonSku;
    @ExcelIgnore
    private String spu;

    @Override
    public ValidationResult validate() {
        ValidationResult result = new ValidationResult();

        // 验证基本字段
        validateRequiredFields(result, this);
        return result;
    }

    /**
     * 验证必填字段
     *
     * @param result 验证结果
     * @param excel  Excel数据项
     */
    private void validateRequiredFields(ValidationResult result, UpdateTitleAndDescriptionExcel excel) {
        // 校验基本字段
        validateAndTrimField(result, excel.getAccountNumber(), "店铺账号不能为空", excel::setAccountNumber);

        validateAndTrimField(result, excel.getProductId(), "商品编码不能为空", excel::setProductId);

        validateAndTrimField(result, excel.getSellerSku(), "sellerSku不能为空", excel::setSellerSku);

        validateAndTrimField(result, excel.getSku(), "SKU不能为空", excel::setSku);
        // 判断了
        String title1 = excel.getTitle();
        String description1 = excel.getDescription();
        if (StringUtils.isBlank(title1) && StringUtils.isBlank(description1)) {
            result.addError("标题和描述不能同时为空");
        }
    }

}
