package com.estone.erp.publish.walmart.call.items;

import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.warpper.EnvironmentSupplierWrapper;
import com.estone.erp.common.warpper.PublishEnvEnums;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.util.CheckOrderSalesTimeUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.walmart.call.AbstractWalmartCall;
import com.estone.erp.publish.walmart.constant.WalmartCallConstant;
import com.estone.erp.publish.walmart.enums.WalmartFeedTaskMsgEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.HttpDelete;

import java.util.Arrays;
import java.util.List;

/**
 * 下架
 *
 * @Auther yucm从
 * @Date 2021/3/27
 */
@Slf4j
public class WallmartItemDeleteCall extends AbstractWalmartCall {
    private static final SystemParamService systemParamService = SpringUtils.getBean(SystemParamService.class);


    public WallmartItemDeleteCall(SaleAccountAndBusinessResponse walmartPmsAccount) {
        super(walmartPmsAccount);
    }

    public void execute(String sku) {
        // 获取不下架的sellerSku列表
        SystemParam systemParam = systemParamService.querySystemParamByCodeKey("WALMART.CAN_NOT_DELETE_SELLER_SKU");
        if (systemParam != null && StringUtils.isNotBlank(systemParam.getParamValue())) {
            List<String> canNotDeleteSellerSkus = Arrays.asList(systemParam.getParamValue().split(","));
            if (canNotDeleteSellerSkus.contains(sku)) {
                throw new BusinessException("参数中存在的Walmart不下架sellerSku，不下架此数据");
            }
        }

        EnvironmentSupplierWrapper.execute(()->{
            String path = String.format(WalmartCallConstant.ITEM_PATH + WalmartCallConstant.PATH_SPLIT + WalmartCallConstant.PARAM, sku);
            HttpDelete request = this.createDeleteRequest(path, null);
            // 执行请求
            this.execute(request);
            return ApiResult.newSuccess();
        },()->{
            log.info("非正式环境不执行下架，sku:{}", sku);
            throw new BusinessException("非正式环境不执行下架");
        });
    }

    public void execute(String sku, String feedType) {
        if (!WalmartFeedTaskMsgEnum.RETIRE_NO_SALE_SPECIAL_ACCOUNT_PUBLISH_SKU.getMsg().equals(feedType)) {
            // 获取不下架的sellerSku列表
            SystemParam systemParam = systemParamService.querySystemParamByCodeKey("WALMART.CAN_NOT_DELETE_SELLER_SKU");
            if (systemParam != null && StringUtils.isNotBlank(systemParam.getParamValue())) {
                List<String> canNotDeleteSellerSkus = Arrays.asList(systemParam.getParamValue().split(","));
                if (canNotDeleteSellerSkus.contains(sku)) {
                    throw new BusinessException("参数中存在的Walmart不下架sellerSku，不下架此数据");
                }
            }
        }

        String saleId = SaleChannel.CHANNEL_WALMART + "_" + walmartPmsAccount.getAccountNumber() + "_" + sku;
        boolean canDeleted = CheckOrderSalesTimeUtils.checkSaleTotalCountWriteTime(saleId);
        if (BooleanUtils.isFalse(canDeleted)) {
            throw new BusinessException("总销量写入时间不符合，不下架");
        }
        EnvironmentSupplierWrapper.execute(PublishEnvEnums.prod.name(),
                () -> {
                    String path = String.format(WalmartCallConstant.ITEM_PATH + WalmartCallConstant.PATH_SPLIT + WalmartCallConstant.PARAM, sku);
                    HttpDelete request = this.createDeleteRequest(path, null);
                    // 执行请求
                    this.execute(request);
                    return ApiResult.newSuccess();
                },
                () -> {
                    log.info("非正式环境不执行下架，sku:{}", sku);
                    throw new BusinessException("非正式环境不执行下架");
                }
        );
    }
}
