package com.estone.erp.publish.ozon.handler;

import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.PackageInfo;
import com.estone.erp.common.util.PagingUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItem;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItemAttributes;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsOzonItemRequest;
import com.estone.erp.publish.elasticsearch4.service.EsOzonItemService;
import com.estone.erp.publish.ozon.call.model.AttributesDTO;
import com.estone.erp.publish.ozon.call.model.request.CreateProductRequest;
import com.estone.erp.publish.ozon.call.model.request.UpdateAttributesRequest;
import com.estone.erp.publish.ozon.enums.OzonFeedTaskEnums;
import com.estone.erp.publish.ozon.excel.model.UpdateTitleAndDescriptionExcel;
import com.estone.erp.publish.ozon.excel.model.UpdateWeightExcel;
import com.estone.erp.publish.ozon.handler.common.JobInfo;
import com.estone.erp.publish.ozon.handler.common.TitleAndDescInfo;
import com.estone.erp.publish.ozon.handler.common.UpdateAttributeDto;
import com.estone.erp.publish.ozon.handler.common.UpdateListingContext;
import com.estone.erp.publish.ozon.handler.template.OzonTemplateValidation;
import com.estone.erp.publish.ozon.model.OzonAccountConfig;
import com.estone.erp.publish.ozon.model.OzonAccountConfigExample;
import com.estone.erp.publish.ozon.model.dto.OzonUpdateItemDO;
import com.estone.erp.publish.ozon.model.dto.OzonUpdatePackageDO;
import com.estone.erp.publish.ozon.model.dto.sync.OzonItemDO;
import com.estone.erp.publish.ozon.model.listing.*;
import com.estone.erp.publish.ozon.service.OzonAccountConfigService;
import com.estone.erp.publish.ozon.service.OzonFeedTaskService;
import com.estone.erp.publish.ozon.service.OzonProblemClassificationHandler;
import com.estone.erp.publish.ozon.utils.OzonImageUtils;
import com.estone.erp.publish.ozon.utils.OzonPublishUtils;
import com.estone.erp.publish.ozon.utils.OzonSkuUtils;
import com.estone.erp.publish.ozon.utils.OzonTemplateDataUtil;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.ComposeSku;
import com.estone.erp.publish.system.product.bean.SpuOfficial;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.system.product.util.SpuOfficialUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 批量修改Ozon商品工具
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OzonBatchUpdateListingInfoHandler {

    @Resource
    private EsOzonItemService esOzonItemService;
    @Resource
    private OzonSyncListingHandler ozonSyncListingHandler;
    @Resource
    private OzonFeedTaskService ozonFeedTaskService;
    @Resource
    private OzonUpdateHandler ozonUpdateHandler;
    @Resource
    private SingleItemEsService singleItemEsService;
    @Resource
    private OzonAccountConfigService ozonAccountConfigService;
    @Resource
    private OzonTemplateValidation ozonTemplateValidation;
    @Resource
    private OzonProblemClassificationHandler ozonProblemClassificationHandler;

    /**
     * 批量修改重量 excel表格
     *
     * @param errorList
     */
    public void batchUpdateWeight(List<UpdateWeightExcel> updateList, List<UpdateWeightExcel> errorList) {
        String userName = getUserName();
        if (CollectionUtils.isNotEmpty(errorList)) {
            Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
            List<FeedTask> list = new ArrayList<>();
            for (UpdateWeightExcel updateWeightExcel : errorList) {
                FeedTask excel = getErrorTask("Excel", userName, OzonFeedTaskEnums.TaskType.UPDATE_WEIGHT.name(),
                        updateWeightExcel.getAccountNumber(), updateWeightExcel.getProductId(), updateWeightExcel.getSellerSku(),
                        updateWeightExcel.getSku(), updateWeightExcel.getRemark(), timestamp);
                excel.setAttribute8(updateWeightExcel.getRowIndex().toString());
                excel.setAttribute7(updateWeightExcel.getExcelId().toString());
                list.add(excel);
            }
            ozonFeedTaskService.batchInsertSelective(list);
        }
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }
        List<UpdateListingWeightVo> collect = updateList.stream().map(a -> {
            UpdateListingWeightVo updateListingWeightVo = new UpdateListingWeightVo();
            updateListingWeightVo.setAfterWeight(Double.valueOf(a.getAfterWeight()));
            updateListingWeightVo.setProductId(Long.valueOf(a.getProductId()));
            updateListingWeightVo.setRowId(a.getRowIndex().toString());
            updateListingWeightVo.setExcelId(a.getExcelId().toString());
            return updateListingWeightVo;
        }).collect(Collectors.toList());
        batchUpdateWeight(collect, "Excel");
    }

    /**
     * 批量修改重量
     *
     * @param updateListingWeightVos 重量
     */
    public void batchUpdateWeight(List<UpdateListingWeightVo> updateListingWeightVos, String job) {
        if (CollectionUtils.isEmpty(updateListingWeightVos)) {
            return;
        }
        String userName = getUserName();
        Map<Long, UpdateListingWeightVo> productIdAndAfterWeightMap = updateListingWeightVos.stream().collect(Collectors.toMap(UpdateListingWeightVo::getProductId,
                a -> a, (a, b) -> a));
        List<EsOzonItem> esOzonItems = getEsListing(productIdAndAfterWeightMap.keySet());
        // 所有要先同步下商品
        Map<String, List<EsOzonItem>> accountNumberMap = esOzonItems.stream().collect(Collectors.groupingBy(EsOzonItem::getAccountNumber));
        Map<Long, FeedTask> productIdAndFeedTaskMap = getRunFeedTask(accountNumberMap, productIdAndAfterWeightMap, OzonFeedTaskEnums.TaskType.UPDATE_WEIGHT, job);
        for (Map.Entry<String, List<EsOzonItem>> entry : accountNumberMap.entrySet()) {
            List<EsOzonItem> itemList = entry.getValue();
            Map<Long, EsOzonItem> productIdAndItemMap = itemList.stream().collect(Collectors.toMap(EsOzonItem::getProductId, a -> a, (a, b) -> a));

            String accountNumber = entry.getKey();
            // syncErrorMap 在 getCreateProductRequest 方法里面是没有 创建处理报告的，根据需要去创建
            Map<Long, String> syncErrorMap = new HashMap<>();
            JobInfo jobInfo = JobInfo.builder().accountNumber(accountNumber).job(job).userName(userName).taskType(OzonFeedTaskEnums.TaskType.UPDATE_WEIGHT).build();
            List<FeedTask> sameErrorFeedTaskList = new ArrayList<>();
            List<OzonUpdateItemDO> createProductRequestList = getCreateProductRequest(jobInfo, productIdAndItemMap, productIdAndFeedTaskMap, syncErrorMap, (updateListingContext) -> {
                OzonItemDO ozonItemDO = updateListingContext.getOzonItemDO();
                OzonUpdatePackageDO ozonUpdatePackageDO = updateListingContext.getOzonUpdatePackageDO();
                FeedTask feedTask = updateListingContext.getFeedTask();
                UpdateListingWeightVo updateListingWeightVo = productIdAndAfterWeightMap.get(ozonItemDO.getProductId());
                feedTask.setAttribute8(updateListingWeightVo.getRowId());
                feedTask.setAttribute7(updateListingWeightVo.getExcelId());
                Double afterValue = updateListingWeightVo.getAfterWeight();
                Double beforeValue = ozonUpdatePackageDO.getWeight();
                feedTask.setAttribute3(beforeValue != null ? beforeValue.toString() : "");
                feedTask.setAttribute4(afterValue != null ? afterValue.toString() : "");
                if (ObjectUtils.equals(beforeValue, afterValue)) {
                    setErrorFeedTask(feedTask, "商品重量未修改, 重量：" + beforeValue);
                    sameErrorFeedTaskList.add(feedTask);
                    return false;
                }
                // 替换为改后值
                ozonUpdatePackageDO.setWeight(afterValue);
                return true;
            });
            updateListing(createProductRequestList, accountNumber, jobInfo);
            saveFeedTask(sameErrorFeedTaskList);
            saveSyncErrorFeedTask(syncErrorMap, productIdAndItemMap, productIdAndFeedTaskMap, jobInfo);
        }
    }


    /**
     * 批量修改图片信息
     *
     * @param updateListingImageVos 图片信息
     * @param job                   任务
     */
    public void batchUpdateImageInfo(List<UpdateListingImageVo> updateListingImageVos, String job) {
        if (CollectionUtils.isEmpty(updateListingImageVos)) {
            return;
        }
        Map<Long, UpdateListingImageVo> productIdAndImageMap = updateListingImageVos.stream().collect(Collectors.toMap(UpdateListingImageVo::getProductId, Function.identity(), (a, b) -> a));
        List<EsOzonItem> esOzonItems = getEsListing(productIdAndImageMap.keySet());
        // 所有要先同步下商品
        Map<String, List<EsOzonItem>> accountNumberMap = esOzonItems.stream().collect(Collectors.groupingBy(EsOzonItem::getAccountNumber));
        Map<Long, FeedTask> productIdAndFeedTaskMap = getRunFeedTask(accountNumberMap, OzonFeedTaskEnums.TaskType.UPDATE_IMAGE, job);
        for (Map.Entry<String, List<EsOzonItem>> entry : accountNumberMap.entrySet()) {
            List<EsOzonItem> itemList = entry.getValue();
            Map<Long, EsOzonItem> productIdAndItemMap = itemList.stream().collect(Collectors.toMap(EsOzonItem::getProductId, a -> a, (a, b) -> a));
            String accountNumber = entry.getKey();
            // 处理报告是没有保存的
            Map<Long, String> syncErrorMap = new HashMap<>();
            JobInfo jobInfo = JobInfo.builder().accountNumber(accountNumber).job(job).userName(getUserName()).taskType(OzonFeedTaskEnums.TaskType.UPDATE_IMAGE).build();

            List<FeedTask> uploadImageErrorFeedTaskList = new ArrayList<>();
            List<OzonUpdateItemDO> createProductRequestList = getCreateProductRequest(jobInfo, productIdAndItemMap, productIdAndFeedTaskMap, syncErrorMap, (updateListingContext) -> {
                OzonItemDO ozonItemDO = updateListingContext.getOzonItemDO();
                FeedTask feedTask = updateListingContext.getFeedTask();

                UpdateListingImageVo updateListingImageVo1 = productIdAndImageMap.get(ozonItemDO.getProductId());
                List<String> newImages = updateListingImageVo1.getNewImages();
                String newMainImage = updateListingImageVo1.getNewMainImage();
                List<String> allImages = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(newImages)) {
                    allImages.addAll(newImages);
                }
                if (StringUtils.isNotBlank(newMainImage)) {
                    allImages.add(newMainImage);
                }
                try {
                    Map<String, String> imageMapping = OzonImageUtils.getImageMapping(allImages);
                    if (CollectionUtils.isNotEmpty(newImages)) {
                        List<String> collect = newImages.stream().map(imageMapping::get).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                        ozonItemDO.setImages(StringUtils.join(collect, ","));
                    } else {
                        ozonItemDO.setImages("");
                    }
                    if (StringUtils.isNotBlank(newMainImage)) {
                        ozonItemDO.setMainImage(imageMapping.get(newMainImage));
                    } else {
                        ozonItemDO.setMainImage("");
                    }
                } catch (Exception e) {
                    setErrorFeedTask(feedTask, "上传图片失败, 原因：" + e.getMessage());
                    uploadImageErrorFeedTaskList.add(feedTask);
                    log.error("上传图片失败", e);
                    return false;
                }
                return true;
            });

            // 开始更新
            updateListing(createProductRequestList, accountNumber, jobInfo);
            saveFeedTask(uploadImageErrorFeedTaskList);
            saveSyncErrorFeedTask(syncErrorMap, productIdAndItemMap, productIdAndFeedTaskMap, jobInfo);
        }
    }

    /**
     * 批量修改此次
     *
     * @param updateListingPackageSizeVos 尺寸
     * @param job                         任务
     */
    public void batchUpdatePackageSize(UpdateListingPackageSizeVo updateListingPackageSizeVos, String job) {
        if (Objects.isNull(updateListingPackageSizeVos) || CollectionUtils.isEmpty(updateListingPackageSizeVos.getProductIdList())) {
            return;
        }
        Integer type = updateListingPackageSizeVos.getSizeType();

        List<Long> productIdList = updateListingPackageSizeVos.getProductIdList();
        List<EsOzonItem> esOzonItems = getEsListing(new HashSet<>(productIdList));
        // 所有要先同步下商品
        Map<String, List<EsOzonItem>> accountNumberMap = esOzonItems.stream().collect(Collectors.groupingBy(EsOzonItem::getAccountNumber));
        List<String> skuList = esOzonItems.stream().map(EsOzonItem::getSku).collect(Collectors.toList());

        // 初始化尺寸信息
        Map<String, PackageInfo> skuAndPackageSizeMap = new HashMap<>();
        Map<String, PackageInfo> accountAndPackageSizeMap = new HashMap<>();
        PackageInfo customSize = new PackageInfo();
        if (Objects.equals(type, UpdateListingPackageSizeVo.Type.PACKAGE_SIZE.getCode()) || Objects.equals(type, UpdateListingPackageSizeVo.Type.SIZE.getCode())) {
            Map<String, PackageInfo> tempPackageSize = getPackageSize(skuList);
            skuAndPackageSizeMap.putAll(tempPackageSize);
        } else if (Objects.equals(type, UpdateListingPackageSizeVo.Type.ACCOUNT_DEFAULT_SIZE.getCode())) {
            Map<String, PackageInfo> accountPackageSize = getAccountPackageSize(new ArrayList<>(accountNumberMap.keySet()));
            accountAndPackageSizeMap.putAll(accountPackageSize);
        } else if (Objects.equals(type, UpdateListingPackageSizeVo.Type.CUSTOM_SIZE.getCode())) {
            // 自定义尺寸 输入的是毫米，其他是里面来着, 先全部统一转为厘米先
            BigDecimal bigDecimal = BigDecimal.valueOf(10);
            customSize.setWide(updateListingPackageSizeVos.getWidth() != null ? BigDecimal.valueOf(updateListingPackageSizeVos.getWidth()).divide(bigDecimal, 5, RoundingMode.HALF_UP) : null);
            customSize.setHeight(updateListingPackageSizeVos.getHeight() != null ? BigDecimal.valueOf(updateListingPackageSizeVos.getHeight()).divide(bigDecimal, 5, RoundingMode.HALF_UP) : null);
            customSize.setLength(updateListingPackageSizeVos.getLength() != null ? BigDecimal.valueOf(updateListingPackageSizeVos.getLength()).divide(bigDecimal, 5, RoundingMode.HALF_UP) : null);
        }
        Map<Long, FeedTask> productIdAndFeedTaskMap = getRunFeedTask(accountNumberMap, OzonFeedTaskEnums.TaskType.UPDATE_PACKAGE_SIZE, job);
        for (Map.Entry<String, List<EsOzonItem>> entry : accountNumberMap.entrySet()) {
            List<EsOzonItem> itemList = entry.getValue();
            String accountNumber = entry.getKey();
            // 处理报告是没有保存的
            Map<Long, String> syncErrorMap = new HashMap<>();
            JobInfo jobInfo = JobInfo.builder().accountNumber(accountNumber).job(job).userName(getUserName()).taskType(OzonFeedTaskEnums.TaskType.UPDATE_PACKAGE_SIZE).build();
            Map<Long, EsOzonItem> allProductIdAndItemMap = itemList.stream().collect(Collectors.toMap(EsOzonItem::getProductId, a -> a, (a, b) -> a));

            itemList = itemList.stream().filter(a -> {
                if (Objects.equals(type, UpdateListingPackageSizeVo.Type.PACKAGE_SIZE.getCode())) {
                    boolean b = skuAndPackageSizeMap.containsKey(a.getSku()) && skuAndPackageSizeMap.get(a.getSku()).checkPack();
                    if (b) {
                        return true;
                    }
                    syncErrorMap.put(a.getProductId(), "sku" + a.getSku() + "包裹尺寸信息不全");
                    return false;
                } else if (Objects.equals(type, UpdateListingPackageSizeVo.Type.SIZE.getCode())) {
                    boolean b = skuAndPackageSizeMap.containsKey(a.getSku()) && skuAndPackageSizeMap.get(a.getSku()).checkProduct();
                    if (b) {
                        return true;
                    }
                    syncErrorMap.put(a.getProductId(), "sku" + a.getSku() + "包装尺寸信息不全");
                    return false;
                } else if (Objects.equals(type, UpdateListingPackageSizeVo.Type.ACCOUNT_DEFAULT_SIZE.getCode())) {
                    boolean b = accountAndPackageSizeMap.containsKey(a.getAccountNumber()) && accountAndPackageSizeMap.get(a.getAccountNumber()).checkProduct();
                    if (b) {
                        return true;
                    }
                    syncErrorMap.put(a.getProductId(), "账号" + a.getAccountNumber() + "默认尺寸信息不全");
                    return false;
                } else {
                    boolean b = customSize.checkProduct();
                    if (b) {
                        return true;
                    }
                    syncErrorMap.put(a.getProductId(), "自定义尺寸信息不全");
                    return false;
                }
            }).collect(Collectors.toList());

            Map<Long, EsOzonItem> productIdAndItemMap = itemList.stream().collect(Collectors.toMap(EsOzonItem::getProductId, a -> a, (a, b) -> a));
            List<FeedTask> updateSizeErrorFeedTaskList = new ArrayList<>();

            List<OzonUpdateItemDO> createProductRequestList = getCreateProductRequest(jobInfo, productIdAndItemMap, productIdAndFeedTaskMap, syncErrorMap, (updateListingContext) -> {
                OzonItemDO ozonItemDO = updateListingContext.getOzonItemDO();
                OzonUpdatePackageDO ozonUpdatePackageDO = updateListingContext.getOzonUpdatePackageDO();
                FeedTask feedTask = updateListingContext.getFeedTask();
                // 尺寸
                String packageSizeJson = ozonUpdatePackageDO.getPackageSizeJson();
                EsOzonItem esOzonItem = productIdAndItemMap.get(ozonItemDO.getProductId());
                Map<String, Object> attributesMap = JSON.parseObject(packageSizeJson, new TypeReference<>() {
                });
                attributesMap = Optional.ofNullable(attributesMap).orElseGet(HashMap::new);
                Integer beforeLength = (Integer) attributesMap.get("depth");
                Integer beforeWidth = (Integer) attributesMap.get("width");
                Integer beforeHeight = (Integer) attributesMap.get("height");
                Integer afterLength = null;
                Integer afterWidth = null;
                Integer afterHeight = null;
                if (Objects.equals(type, UpdateListingPackageSizeVo.Type.PACKAGE_SIZE.getCode())) {
                    PackageInfo packageInfo = skuAndPackageSizeMap.get(esOzonItem.getSku());
                    afterLength = packageInfo.getPackLength().multiply(new BigDecimal(10)).intValue();
                    afterWidth = packageInfo.getPackWidth().multiply(new BigDecimal(10)).intValue();
                    afterHeight = packageInfo.getPackHeight().multiply(new BigDecimal(10)).intValue();
                } else if (Objects.equals(type, UpdateListingPackageSizeVo.Type.SIZE.getCode())) {
                    PackageInfo packageInfo = skuAndPackageSizeMap.get(esOzonItem.getSku());
                    afterLength = packageInfo.getLength().multiply(new BigDecimal(10)).intValue();
                    afterWidth = packageInfo.getWide().multiply(new BigDecimal(10)).intValue();
                    afterHeight = packageInfo.getHeight().multiply(new BigDecimal(10)).intValue();
                } else if (Objects.equals(type, UpdateListingPackageSizeVo.Type.ACCOUNT_DEFAULT_SIZE.getCode())) {
                    PackageInfo packageInfo = accountAndPackageSizeMap.get(esOzonItem.getAccountNumber());
                    afterLength = packageInfo.getLength().multiply(new BigDecimal(10)).intValue();
                    afterWidth = packageInfo.getWide().multiply(new BigDecimal(10)).intValue();
                    afterHeight = packageInfo.getHeight().multiply(new BigDecimal(10)).intValue();
                } else {
                    afterLength = customSize.getLength().multiply(new BigDecimal(10)).intValue();
                    afterWidth = customSize.getWide().multiply(new BigDecimal(10)).intValue();
                    afterHeight = customSize.getHeight().multiply(new BigDecimal(10)).intValue();
                }

                feedTask.setAttribute3(beforeLength + "X" + beforeWidth + "X" + beforeHeight);
                feedTask.setAttribute4(afterLength + "X" + afterWidth + "X" + afterHeight);
                if (Objects.equals(beforeLength, afterLength) && Objects.equals(beforeWidth, afterWidth) && Objects.equals(beforeHeight, afterHeight)) {
                    setErrorFeedTask(feedTask, "尺寸改前改后相同，不修改, 长：" + beforeLength + " 宽：" + beforeWidth + " 高：" + beforeHeight);
                    updateSizeErrorFeedTaskList.add(feedTask);
                    return false;
                }

                // 覆盖
                attributesMap.put("depth", afterLength);
                attributesMap.put("width", afterWidth);
                attributesMap.put("height", afterHeight);
                ozonUpdatePackageDO.setPackageSizeJson(JSON.toJSONString(attributesMap));

                return true;
            });

            // 开始更新
            updateListing(createProductRequestList, accountNumber, jobInfo);
            saveFeedTask(updateSizeErrorFeedTaskList);
            saveSyncErrorFeedTask(syncErrorMap, allProductIdAndItemMap, productIdAndFeedTaskMap, jobInfo);
        }
    }


    /**
     * 编辑标题和描述
     *
     * @param updateListingTitleOrDescriptionVos 描述
     * @param job                                任务
     */
    public void batchUpdateListingTitleAndDescVo(UpdateListingTitleAndDescVo updateListingTitleOrDescriptionVos, String job) {
        if (Objects.isNull(updateListingTitleOrDescriptionVos) || CollectionUtils.isEmpty(updateListingTitleOrDescriptionVos.getProductIdList())) {
            return;
        }

        List<Long> productIdList = updateListingTitleOrDescriptionVos.getProductIdList();
        List<EsOzonItem> esOzonItems = getEsListing(new HashSet<>(productIdList));
        // 所有要先同步下商品
        Map<String, List<EsOzonItem>> accountNumberMap = esOzonItems.stream().collect(Collectors.groupingBy(EsOzonItem::getAccountNumber));
        List<String> spuList = esOzonItems.stream().map(EsOzonItem::getSpu).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        List<String> singleItems = new ArrayList<>();
        List<String> suiteItems = new ArrayList<>();
        List<String> composeItems = new ArrayList<>();
        OzonSkuUtils.skuGroup(spuList, singleItems, composeItems, suiteItems);

        Map<String, SpuOfficial> spuOfficialMap = new HashMap<>();
        // 获取spu文案描述
        if (CollectionUtils.isNotEmpty(singleItems)) {
            List<SpuOfficial> ruSpuOfficialList = ProductUtils.getSpuTitles("ru", singleItems);
            Map<String, SpuOfficial> ruAndSpuOfficialMap = Optional.ofNullable(ruSpuOfficialList).orElseGet(ArrayList::new).stream()
                    // 加入检查文案是否全，不全的情况下不取
                    .filter(OzonTemplateDataUtil::checkCompletenessOfTitleAndDesc)
                    .collect(Collectors.toMap(SpuOfficial::getSpu, a -> a, (a, b) -> a));
            spuOfficialMap.putAll(ruAndSpuOfficialMap);
            List<String> notRuSpuList = singleItems.stream().filter(a -> !ruAndSpuOfficialMap.containsKey(a)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notRuSpuList)) {
                List<SpuOfficial> spuOfficialTitles = ProductUtils.getSpuOfficialTitles(notRuSpuList);
                spuOfficialMap.putAll(Optional.ofNullable(spuOfficialTitles).orElseGet(ArrayList::new).stream().collect(Collectors.toMap(SpuOfficial::getSpu, a -> a, (a, b) -> a)));
            }
        }
        if (CollectionUtils.isNotEmpty(composeItems)) {
            for (String composeItem : composeItems) {
                ComposeSku composeProduct = ProductUtils.getComposeProduct(composeItem);
                if (CollectionUtils.isNotEmpty(composeItems)) {
                    SpuOfficial spuOfficial = SpuOfficialUtil.getSpuOfficial(composeProduct);
                    if (spuOfficial != null) {
                        spuOfficialMap.put(composeItem, spuOfficial);
                    }
                }
            }
        }
        //套装不处理，现在没有文案信息

        Boolean updateTitle = updateListingTitleOrDescriptionVos.getUpdateTitle();
        Boolean updateDesc = updateListingTitleOrDescriptionVos.getUpdateDesc();
        // 获取标题描述
        Map<Long, FeedTask> titleProductIdAndFeedTaskMap = new HashMap<>();
        if (BooleanUtils.isTrue(updateTitle)) {
            titleProductIdAndFeedTaskMap.putAll(getRunFeedTask(accountNumberMap, OzonFeedTaskEnums.TaskType.UPDATE_TITLE, job));
        }
        Map<Long, FeedTask> descProductIdAndFeedTaskMap = new HashMap<>();
        if (BooleanUtils.isTrue(updateDesc)) {
            descProductIdAndFeedTaskMap.putAll(getRunFeedTask(accountNumberMap, OzonFeedTaskEnums.TaskType.UPDATE_DESC, job));
        }
        for (Map.Entry<String, List<EsOzonItem>> entry : accountNumberMap.entrySet()) {
            List<EsOzonItem> itemList = entry.getValue();
            String accountNumber = entry.getKey();
            // 处理报告是没有保存的
            Map<Long, String> syncErrorMap = new HashMap<>();
            JobInfo jobInfo = JobInfo.builder().accountNumber(accountNumber).job(job).userName(getUserName()).taskType(OzonFeedTaskEnums.TaskType.UPDATE_TITLE).build();
            JobInfo jobInfo2 = JobInfo.builder().accountNumber(accountNumber).job(job).userName(getUserName()).taskType(OzonFeedTaskEnums.TaskType.UPDATE_DESC).build();
            Map<Long, EsOzonItem> allProductIdAndItemMap = itemList.stream().collect(Collectors.toMap(EsOzonItem::getProductId, a -> a, (a, b) -> a));

            itemList = itemList.stream().filter(a -> {
                if (StringUtils.isNotBlank(a.getSpu())) {
                    return true;
                }
                syncErrorMap.put(a.getProductId(), "商品spu信息为空");
                return false;
            }).filter(a -> {
                if (spuOfficialMap.containsKey(a.getSpu())) {
                    return true;
                }
                syncErrorMap.put(a.getProductId(), "商品spu文案信息不存在");
                return false;
            }).collect(Collectors.toList());
            Map<Long, EsOzonItem> productIdAndItemMap = itemList.stream().collect(Collectors.toMap(EsOzonItem::getProductId, a -> a, (a, b) -> a));

            if (BooleanUtils.isTrue(updateTitle)) {
                // 根据spu获取文案
                List<OzonUpdateItemDO> createProductRequestList = getCreateProductRequest(jobInfo, productIdAndItemMap, titleProductIdAndFeedTaskMap, syncErrorMap, (updateListingContext) -> {
                    OzonItemDO ozonItemDO = updateListingContext.getOzonItemDO();
                    OzonUpdatePackageDO ozonUpdatePackageDO = updateListingContext.getOzonUpdatePackageDO();
                    FeedTask feedTask = updateListingContext.getFeedTask();
                    EsOzonItem esOzonItem = productIdAndItemMap.get(ozonItemDO.getProductId());
                    // 文案
                    SpuOfficial spuOfficial = spuOfficialMap.get(esOzonItem.getSpu());

                    try {
                        TitleAndDescInfo titleAndDescInfo = new TitleAndDescInfo();
                        OzonTemplateDataUtil.setTitleAndDesc(spuOfficial, titleAndDescInfo::setTitle, titleAndDescInfo::setDescription);
                        // 去除侵权词
                        ozonTemplateValidation.filterInfringementWord(titleAndDescInfo.getTitle(), titleAndDescInfo.getDescription(), titleAndDescInfo::setTitle, titleAndDescInfo::setDescription);
                        if (!"ru".equalsIgnoreCase(spuOfficial.getLanguage())) {
                            // 英文的，需要翻译
                            String translateTitle = OzonTemplateDataUtil.translate(titleAndDescInfo.getTitle());
                            String removedTitleDuplicateWord = OzonTemplateDataUtil.removeTitleDuplicateWord(translateTitle);
                            String translateDescription = OzonTemplateDataUtil.translate(titleAndDescInfo.getDescription());
                            titleAndDescInfo.setTitle(removedTitleDuplicateWord);
                            titleAndDescInfo.setDescription(translateDescription);
                        }

                        ozonItemDO.setName(titleAndDescInfo.getTitle());
                        if (BooleanUtils.isTrue(updateDesc)) {
                            // 如果还要修改描述，就先一起修改吧
                            FeedTask runFeedTask = descProductIdAndFeedTaskMap.get(ozonItemDO.getProductId());
                            if (runFeedTask == null) {
                                runFeedTask = getRunFeedTask(jobInfo2, esOzonItem.getProductId(), esOzonItem.getSellerSku(), esOzonItem.getOzonSku(), esOzonItem.getSku(), feedTask.getCreateTime());
                            }
                            updateListingContext.setFeedTask2(runFeedTask);
                            List<EsOzonItemAttributes> attributes = ozonUpdatePackageDO.getAttributes();
                            OzonPublishUtils.addTemplateDescriptionAttribute(attributes, titleAndDescInfo.getDescription());
                        }
                    } catch (Exception e) {
                        syncErrorMap.put(ozonItemDO.getProductId(), "更新标题失败：" + e.getMessage());
                        return false;
                    }
                    return true;
                });

                // 开始更新
                updateListing(createProductRequestList, accountNumber, jobInfo);
                // 这里还有一个问题，就是修改标题，又是修改描述
                if (!BooleanUtils.isTrue(updateDesc)) {
                    //这里就是判断有没有更新描述，没有就删除了
                    saveSyncErrorFeedTask(syncErrorMap, productIdAndItemMap, titleProductIdAndFeedTaskMap, jobInfo);
                } else {
                    saveSyncErrorFeedTask(syncErrorMap, productIdAndItemMap, titleProductIdAndFeedTaskMap, jobInfo);
                    saveSyncErrorFeedTask(syncErrorMap, productIdAndItemMap, descProductIdAndFeedTaskMap, jobInfo2);
                }
            } else {
                if (!BooleanUtils.isTrue(updateDesc)) {
                    return;
                }
                // 只修改描述 用修改特征的方法
                List<UpdateAttributeDto> list = getUpdateAttributeInfo(jobInfo2, itemList, descProductIdAndFeedTaskMap, (updateAttributeDto) -> {
                    // 文案
                    try {
                        SpuOfficial spuOfficial = spuOfficialMap.get(updateAttributeDto.getSpu());
                        TitleAndDescInfo titleAndDescInfo = new TitleAndDescInfo();
                        OzonTemplateDataUtil.setTitleAndDesc(spuOfficial, titleAndDescInfo::setTitle, titleAndDescInfo::setDescription);
                        // 去除侵权词
                        ozonTemplateValidation.filterInfringementWord(titleAndDescInfo.getTitle(), titleAndDescInfo.getDescription(), titleAndDescInfo::setTitle, titleAndDescInfo::setDescription);
                        if (!"ru".equalsIgnoreCase(spuOfficial.getLanguage())) {
                            // 英文的，需要翻译
                            String translateTitle = OzonTemplateDataUtil.translate(titleAndDescInfo.getTitle());
                            String removedTitleDuplicateWord = OzonTemplateDataUtil.removeTitleDuplicateWord(translateTitle);
                            String translateDescription = OzonTemplateDataUtil.translate(titleAndDescInfo.getDescription());
                            titleAndDescInfo.setTitle(removedTitleDuplicateWord);
                            titleAndDescInfo.setDescription(translateDescription);
                        } else {
                            // 如果是俄语，需要将文案中的英文给替换了
                            String description = titleAndDescInfo.getDescription();
                            if (StringUtils.isNotBlank(description)) {
                                description = description.replaceAll("(?i)Features: ", "Особенности: ");
                                description = description.replaceAll("(?i)Specifications: ", "Технические характеристики: ");
                                description = description.replaceAll("(?i)Package Includes: ", "пакет включает в себя: ");
                                description = description.replaceAll("(?i)note: ", "Примечание: ");
                                titleAndDescInfo.setDescription(description);
                            }
                        }
                        OzonPublishUtils.addDescriptionAttribute(updateAttributeDto.getUpdateAttributesRequests().getAttributes(), titleAndDescInfo.getDescription());
                    } catch (Exception e) {
                        syncErrorMap.put(updateAttributeDto.getProductId(), "更新标题失败：" + e.getMessage());
                        return false;
                    }
                    return true;
                });
                updateAttribute(list, accountNumber, jobInfo2);
                saveSyncErrorFeedTask(syncErrorMap, allProductIdAndItemMap, descProductIdAndFeedTaskMap, jobInfo2);
            }
        }
    }

    public void batchUpdateListingTitleAndDescVo(List<UpdateTitleAndDescriptionExcel> collect, List<UpdateTitleAndDescriptionExcel> errorList, String job) {
        String userName = getUserName();
        if (CollectionUtils.isNotEmpty(errorList)) {
            Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
            List<FeedTask> list = new ArrayList<>();
            for (UpdateTitleAndDescriptionExcel updateWeightExcel : errorList) {
                String description = updateWeightExcel.getDescription();
                List<FeedTask> temp = new ArrayList<>();
                if (StringUtils.isNotBlank(description)) {
                    FeedTask excel = getErrorTask(job, userName, OzonFeedTaskEnums.TaskType.UPDATE_DESC.name(),
                            updateWeightExcel.getAccountNumber(), updateWeightExcel.getProductId(), updateWeightExcel.getSellerSku(),
                            updateWeightExcel.getSku(), updateWeightExcel.getRemark(), timestamp);
                    excel.setAttribute8(updateWeightExcel.getRowIndex().toString());
                    excel.setAttribute7(updateWeightExcel.getExcelId().toString());
                    temp.add(excel);
                }
                String title = updateWeightExcel.getTitle();
                if (StringUtils.isNotBlank(title)) {
                    FeedTask excel = getErrorTask(job, userName, OzonFeedTaskEnums.TaskType.UPDATE_TITLE.name(),
                            updateWeightExcel.getAccountNumber(), updateWeightExcel.getProductId(), updateWeightExcel.getSellerSku(),
                            updateWeightExcel.getSku(), updateWeightExcel.getRemark(), timestamp);
                    excel.setAttribute8(updateWeightExcel.getRowIndex().toString());
                    excel.setAttribute7(updateWeightExcel.getExcelId().toString());
                    temp.add(excel);
                }
                // 如果标题和描述都是空的，这里就要手动设置了
                if (temp.isEmpty()) {
                    FeedTask excel1 = getErrorTask(job, userName, OzonFeedTaskEnums.TaskType.UPDATE_TITLE.name(),
                            updateWeightExcel.getAccountNumber(), updateWeightExcel.getProductId(), updateWeightExcel.getSellerSku(),
                            updateWeightExcel.getSku(), updateWeightExcel.getRemark(), timestamp);
                    excel1.setAttribute8(updateWeightExcel.getRowIndex().toString());
                    excel1.setAttribute7(updateWeightExcel.getExcelId().toString());

                    temp.add(excel1);

                    FeedTask excel2 = getErrorTask(job, userName, OzonFeedTaskEnums.TaskType.UPDATE_DESC.name(),
                            updateWeightExcel.getAccountNumber(), updateWeightExcel.getProductId(), updateWeightExcel.getSellerSku(),
                            updateWeightExcel.getSku(), updateWeightExcel.getRemark(), timestamp);
                    excel2.setAttribute8(updateWeightExcel.getRowIndex().toString());
                    excel2.setAttribute7(updateWeightExcel.getExcelId().toString());
                    temp.add(excel2);
                }
                list.addAll(temp);
            }
            ozonFeedTaskService.batchInsertSelective(list);
        }
        // 获取标题描述
        Map<Long, FeedTask> titleProductIdAndFeedTaskMap = new HashMap<>();
        Map<Long, FeedTask> descProductIdAndFeedTaskMap = new HashMap<>();
        Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
        for (UpdateTitleAndDescriptionExcel updateTitleAndDescriptionExcel : collect) {
            boolean updateTitle = updateTitleAndDescriptionExcel.isUpdateTitle();
            boolean updateDesc = updateTitleAndDescriptionExcel.isUpdateDesc();
            if (BooleanUtils.isTrue(updateTitle)) {
                JobInfo build = JobInfo.builder().accountNumber(updateTitleAndDescriptionExcel.getAccountNumber()).taskType(OzonFeedTaskEnums.TaskType.UPDATE_TITLE).userName(getUserName()).job(job).build();
                FeedTask runFeedTask = getRunFeedTask(build, Long.valueOf(updateTitleAndDescriptionExcel.getProductId()), updateTitleAndDescriptionExcel.getSellerSku(), updateTitleAndDescriptionExcel.getOzonSku(), updateTitleAndDescriptionExcel.getSku(), timestamp);
                runFeedTask.setAttribute8(updateTitleAndDescriptionExcel.getRowIndex().toString());
                runFeedTask.setAttribute7(updateTitleAndDescriptionExcel.getExcelId().toString());
                ozonFeedTaskService.insert(runFeedTask);
                titleProductIdAndFeedTaskMap.put(Long.valueOf(updateTitleAndDescriptionExcel.getProductId()), runFeedTask);
            }
            if (BooleanUtils.isTrue(updateDesc)) {
                JobInfo build = JobInfo.builder().accountNumber(updateTitleAndDescriptionExcel.getAccountNumber()).taskType(OzonFeedTaskEnums.TaskType.UPDATE_DESC).userName(getUserName()).job(job).build();
                FeedTask runFeedTask = getRunFeedTask(build, Long.valueOf(updateTitleAndDescriptionExcel.getProductId()), updateTitleAndDescriptionExcel.getSellerSku(), updateTitleAndDescriptionExcel.getOzonSku(), updateTitleAndDescriptionExcel.getSku(), timestamp);
                runFeedTask.setAttribute8(updateTitleAndDescriptionExcel.getRowIndex().toString());
                runFeedTask.setAttribute7(updateTitleAndDescriptionExcel.getExcelId().toString());
                ozonFeedTaskService.insert(runFeedTask);
                descProductIdAndFeedTaskMap.put(Long.valueOf(updateTitleAndDescriptionExcel.getProductId()), runFeedTask);
            }
        }
        Map<String, List<UpdateTitleAndDescriptionExcel>> accountNumberMap = collect.stream().collect(Collectors.groupingBy(UpdateTitleAndDescriptionExcel::getAccountNumber));
        for (Map.Entry<String, List<UpdateTitleAndDescriptionExcel>> entry : accountNumberMap.entrySet()) {
            List<UpdateTitleAndDescriptionExcel> itemList = entry.getValue();
            String accountNumber = entry.getKey();
            // 处理报告是没有保存的
            Map<Long, String> syncErrorMap = new HashMap<>();
            JobInfo jobInfo = JobInfo.builder().accountNumber(accountNumber).job(job).userName(getUserName()).taskType(OzonFeedTaskEnums.TaskType.UPDATE_TITLE).build();
            JobInfo jobInfo2 = JobInfo.builder().accountNumber(accountNumber).job(job).userName(getUserName()).taskType(OzonFeedTaskEnums.TaskType.UPDATE_DESC).build();
            Map<Long, UpdateTitleAndDescriptionExcel> allProductIdAndItemMap = itemList.stream().collect(Collectors.toMap(a -> Long.valueOf(a.getProductId()), a -> a, (a, b) -> a));

            List<UpdateTitleAndDescriptionExcel> allUpdateList = itemList.stream().filter(a -> BooleanUtils.isTrue(a.isUpdateDesc()) && BooleanUtils.isTrue(a.isUpdateTitle())).collect(Collectors.toList());
            List<UpdateTitleAndDescriptionExcel> onlyUpdateDescList = itemList.stream().filter(a -> BooleanUtils.isTrue(a.isUpdateDesc()) && BooleanUtils.isFalse(a.isUpdateTitle())).collect(Collectors.toList());
            List<UpdateTitleAndDescriptionExcel> onlyUpdateTitleDescList = itemList.stream().filter(a -> BooleanUtils.isTrue(a.isUpdateTitle()) && BooleanUtils.isFalse(a.isUpdateDesc())).collect(Collectors.toList());

            if (!onlyUpdateDescList.isEmpty()) {
                // 只修改描述 用修改特征的方法
                List<UpdateAttributeDto> list = getUpdateAttributeInfoExcel(onlyUpdateDescList, descProductIdAndFeedTaskMap, (updateAttributeDto) -> {
                    // 文案
                    try {
                        Long productId = updateAttributeDto.getProductId();
                        UpdateTitleAndDescriptionExcel updateTitleAndDescriptionExcel = allProductIdAndItemMap.get(productId);
                        OzonPublishUtils.addDescriptionAttribute(updateAttributeDto.getUpdateAttributesRequests().getAttributes(), updateTitleAndDescriptionExcel.getNewDescription());
                    } catch (Exception e) {
                        syncErrorMap.put(updateAttributeDto.getProductId(), "更新标题失败：" + e.getMessage());
                        return false;
                    }
                    return true;
                });
                updateAttribute(list, accountNumber, jobInfo2);
                saveSyncErrorFeedTaskExcel(syncErrorMap, allProductIdAndItemMap, descProductIdAndFeedTaskMap, jobInfo2);
            }
            if (!allUpdateList.isEmpty()) {
                Set<Long> productIdSet = allUpdateList.stream().map(UpdateTitleAndDescriptionExcel::getProductId).map(Long::valueOf).collect(Collectors.toSet());
                // 根据spu获取文案
                List<OzonUpdateItemDO> createProductRequestList = getCreateProductRequest(jobInfo, productIdSet, titleProductIdAndFeedTaskMap, syncErrorMap, (updateListingContext) -> {
                    OzonItemDO ozonItemDO = updateListingContext.getOzonItemDO();
                    try {
                        OzonUpdatePackageDO ozonUpdatePackageDO = updateListingContext.getOzonUpdatePackageDO();
                        UpdateTitleAndDescriptionExcel esOzonItem = allProductIdAndItemMap.get(ozonItemDO.getProductId());
                        ozonItemDO.setName(esOzonItem.getNewTitle());
                        List<EsOzonItemAttributes> attributes = ozonUpdatePackageDO.getAttributes();
                        OzonPublishUtils.addTemplateDescriptionAttribute(attributes, esOzonItem.getNewDescription());
                        FeedTask runFeedTask = descProductIdAndFeedTaskMap.get(ozonItemDO.getProductId());
                        updateListingContext.setFeedTask2(runFeedTask);
                    } catch (Exception e) {
                        syncErrorMap.put(ozonItemDO.getProductId(), "更新标题失败：" + e.getMessage());
                        return false;
                    }
                    return true;
                });
                // 开始更新
                updateListing(createProductRequestList, accountNumber, jobInfo);
                // 这里还有一个问题，就是修改标题，又是修改描述
                saveSyncErrorFeedTaskExcel(syncErrorMap, allProductIdAndItemMap, titleProductIdAndFeedTaskMap, jobInfo);
                saveSyncErrorFeedTaskExcel(syncErrorMap, allProductIdAndItemMap, descProductIdAndFeedTaskMap, jobInfo2);
            }
            if (!onlyUpdateTitleDescList.isEmpty()) {
                Set<Long> productIdSet = onlyUpdateTitleDescList.stream().map(UpdateTitleAndDescriptionExcel::getProductId).map(Long::valueOf).collect(Collectors.toSet());
                // 根据spu获取文案
                List<OzonUpdateItemDO> createProductRequestList = getCreateProductRequest(jobInfo, productIdSet, titleProductIdAndFeedTaskMap, syncErrorMap, (updateListingContext) -> {
                    OzonItemDO ozonItemDO = updateListingContext.getOzonItemDO();
                    // 文案
                    try {
                        UpdateTitleAndDescriptionExcel esOzonItem = allProductIdAndItemMap.get(ozonItemDO.getProductId());
                        ozonItemDO.setName(esOzonItem.getNewTitle());
                    } catch (Exception e) {
                        syncErrorMap.put(ozonItemDO.getProductId(), "更新标题失败：" + e.getMessage());
                        return false;
                    }
                    return true;
                });
                // 开始更新
                updateListing(createProductRequestList, accountNumber, jobInfo);
                // 这里还有一个问题，就是修改标题，又是修改描述
                saveSyncErrorFeedTaskExcel(syncErrorMap, allProductIdAndItemMap, titleProductIdAndFeedTaskMap, jobInfo);
            }
        }
    }

    /**
     * 修改视频信息
     *
     * @param updateListingVideoVos 视频信息
     * @param job                   任务
     */
    public void batchUpdateVideoInfo(List<UpdateListingVideoVo> updateListingVideoVos, String job) {
        if (CollectionUtils.isEmpty(updateListingVideoVos)) {
            return;
        }
        Map<Long, String> productAndLocalVideoUrlMap = updateListingVideoVos.stream().filter(a -> StringUtils.isNotBlank(a.getNewVideo())).collect(Collectors.toMap(UpdateListingVideoVo::getProductId, UpdateListingVideoVo::getNewVideo));
        List<EsOzonItem> esOzonItems = getEsListing(productAndLocalVideoUrlMap.keySet());
        Map<String, List<EsOzonItem>> accountNumberMap = esOzonItems.stream().collect(Collectors.groupingBy(EsOzonItem::getAccountNumber));

        List<String> values = productAndLocalVideoUrlMap.values().stream().distinct().collect(Collectors.toList());
        Map<String, String> videoMapping = new HashMap<>();
        Map<Long, FeedTask> productIdAndFeedTaskMap = getRunFeedTask(accountNumberMap, OzonFeedTaskEnums.TaskType.UPDATE_VIDEO, job);

        try {
            Map<String, String> imageMapping = OzonImageUtils.getImageMapping(values);
            videoMapping.putAll(imageMapping);
        } catch (Exception e) {
            log.error("获取视频映射失败", e);
        }

        for (Map.Entry<String, List<EsOzonItem>> entry : accountNumberMap.entrySet()) {
            String accountNumber = entry.getKey();
            JobInfo jobInfo = JobInfo.builder().accountNumber(accountNumber).taskType(OzonFeedTaskEnums.TaskType.UPDATE_VIDEO).userName(getUserName()).job(job).build();

            List<EsOzonItem> itemList = entry.getValue();
            Map<Long, EsOzonItem> allProductIdAndItemMap = itemList.stream().collect(Collectors.toMap(EsOzonItem::getProductId, a -> a, (a, b) -> a));
            Map<Long, String> errorMap = new HashMap<>();
            List<UpdateAttributeDto> list = getUpdateAttributeInfo(jobInfo, itemList, productIdAndFeedTaskMap, updateAttributeDto -> {
                UpdateAttributesRequest updateAttributesRequests = updateAttributeDto.getUpdateAttributesRequests();
                Long productId = updateAttributeDto.getProductId();
                String sku = updateAttributeDto.getSku();
                List<AttributesDTO> attributes = updateAttributesRequests.getAttributes();
                String video = productAndLocalVideoUrlMap.get(productId);
                String newVideo = videoMapping.get(video);
                if (StringUtils.isBlank(newVideo)) {
                    errorMap.put(productId, "视频转换失败");
                    return false;
                }
                attributes.addAll(OzonPublishUtils.addVideoAttribute(newVideo, sku));
                return true;
            });

            updateAttribute(list, accountNumber, jobInfo);
            saveSyncErrorFeedTask(errorMap, allProductIdAndItemMap, productIdAndFeedTaskMap, jobInfo);
        }
    }

    /**
     * 批量更新属性
     *
     * @param updateListingAttributeVos 属性信息
     * @param job                       任务
     */
    public void batchUpdateAttribute(List<UpdateListingAttributeVo> updateListingAttributeVos, String job) {
        if (CollectionUtils.isEmpty(updateListingAttributeVos)) {
            return;
        }
        Map<Long, List<AttributesDTO>> productIdAndAttributesMap = updateListingAttributeVos.stream().collect(Collectors.toMap(UpdateListingAttributeVo::getProductId, UpdateListingAttributeVo::getAttributes, (a, b) -> a));

        List<EsOzonItem> esOzonItems = getEsListing(productIdAndAttributesMap.keySet());
        Map<String, List<EsOzonItem>> accountNumberMap = esOzonItems.stream().collect(Collectors.groupingBy(EsOzonItem::getAccountNumber));
        Map<Long, FeedTask> productIdAndFeedTaskMap = getRunFeedTask(accountNumberMap, OzonFeedTaskEnums.TaskType.UPDATE_ATTRIBUTE, job);
        for (Map.Entry<String, List<EsOzonItem>> entry : accountNumberMap.entrySet()) {
            String accountNumber = entry.getKey();
            JobInfo jobInfo = JobInfo.builder().accountNumber(accountNumber).taskType(OzonFeedTaskEnums.TaskType.UPDATE_ATTRIBUTE).userName(getUserName()).job(job).build();

            List<EsOzonItem> itemList = entry.getValue();
            Map<Long, EsOzonItem> allProductIdAndItemMap = itemList.stream().collect(Collectors.toMap(EsOzonItem::getProductId, a -> a, (a, b) -> a));
            Map<Long, String> errorMap = new HashMap<>();
            List<UpdateAttributeDto> list = getUpdateAttributeInfo(jobInfo, itemList, productIdAndFeedTaskMap, updateAttributeDto -> {
                UpdateAttributesRequest updateAttributesRequests = updateAttributeDto.getUpdateAttributesRequests();
                Long productId = updateAttributeDto.getProductId();
                // 整个替换
                updateAttributesRequests.setAttributes(productIdAndAttributesMap.get(productId));
                return true;
            });

            updateAttribute(list, accountNumber, jobInfo);
            saveSyncErrorFeedTask(errorMap, allProductIdAndItemMap, productIdAndFeedTaskMap, jobInfo);
        }
    }

    /**
     * 批量获取更新属性信息
     *
     * @param job      任务信息
     * @param itemList 商品信息
     * @param consumer 消费者
     * @return 更新属性信息
     */
    private List<UpdateAttributeDto> getUpdateAttributeInfo(JobInfo job, List<EsOzonItem> itemList, Map<Long, FeedTask> productIdAndFeedTaskMap, Function<UpdateAttributeDto, Boolean> consumer) {
        List<UpdateAttributeDto> attributeDtoList = new ArrayList<>();
        Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
        for (EsOzonItem esOzonItem : itemList) {
            FeedTask feedTask = productIdAndFeedTaskMap.get(esOzonItem.getProductId());
            if (feedTask == null) {
                feedTask = getRunFeedTask(job, esOzonItem.getProductId(), esOzonItem.getSellerSku(), esOzonItem.getOzonSku(), esOzonItem.getSku(), timestamp);
            }

            UpdateAttributeDto updateAttributeDto = new UpdateAttributeDto();
            updateAttributeDto.setFeedTask(feedTask);
            UpdateAttributesRequest updateAttributesRequest = new UpdateAttributesRequest();
            updateAttributesRequest.setOfferId(esOzonItem.getSellerSku());
            updateAttributesRequest.setAttributes(new ArrayList<>());
            updateAttributeDto.setUpdateAttributesRequests(updateAttributesRequest);
            updateAttributeDto.setSpu(esOzonItem.getSpu());
            updateAttributeDto.setSku(esOzonItem.getSku());
            updateAttributeDto.setSellerSku(esOzonItem.getSellerSku());
            updateAttributeDto.setProductId(esOzonItem.getProductId());
            if (consumer.apply(updateAttributeDto)) {
                attributeDtoList.add(updateAttributeDto);
            }
        }
        return attributeDtoList;
    }

    /**
     * 批量获取更新属性信息
     *
     * @param itemList 商品信息
     * @param consumer 消费者
     * @return 更新属性信息
     */
    private List<UpdateAttributeDto> getUpdateAttributeInfoExcel(List<UpdateTitleAndDescriptionExcel> itemList, Map<Long, FeedTask> productIdAndFeedTaskMap, Function<UpdateAttributeDto, Boolean> consumer) {
        List<UpdateAttributeDto> attributeDtoList = new ArrayList<>();
        for (UpdateTitleAndDescriptionExcel esOzonItem : itemList) {
            FeedTask feedTask = productIdAndFeedTaskMap.get(Long.valueOf(esOzonItem.getProductId()));
            UpdateAttributeDto updateAttributeDto = new UpdateAttributeDto();
            updateAttributeDto.setFeedTask(feedTask);
            UpdateAttributesRequest updateAttributesRequest = new UpdateAttributesRequest();
            updateAttributesRequest.setOfferId(esOzonItem.getSellerSku());
            updateAttributesRequest.setAttributes(new ArrayList<>());
            updateAttributeDto.setUpdateAttributesRequests(updateAttributesRequest);
            updateAttributeDto.setSpu(esOzonItem.getSpu());
            updateAttributeDto.setSku(esOzonItem.getSku());
            updateAttributeDto.setSellerSku(esOzonItem.getSellerSku());
            updateAttributeDto.setProductId(Long.valueOf(esOzonItem.getProductId()));
            if (consumer.apply(updateAttributeDto)) {
                attributeDtoList.add(updateAttributeDto);
            }
        }
        return attributeDtoList;
    }

    /**
     * 更新到平台
     *
     * @param createProductRequestList 请求信息
     * @param accountNumber            店铺
     */
    private void updateListing(List<OzonUpdateItemDO> createProductRequestList, String accountNumber, JobInfo jobInfo) {
        if (CollectionUtils.isNotEmpty(createProductRequestList)) {
            for (OzonUpdateItemDO createProductRequest : createProductRequestList) {
                FeedTask feedTask = createProductRequest.getFeedTask();
                if (feedTask.getId() == null) {
                    ozonFeedTaskService.insert(feedTask);
                }
                FeedTask feedTask2 = createProductRequest.getFeedTask2();
                if (feedTask2 != null && feedTask2.getId() == null) {
                    ozonFeedTaskService.insert(feedTask2);
                }
            }
            ozonUpdateHandler.updateItem(accountNumber, createProductRequestList, jobInfo.getTaskType());
        }
    }

    /**
     * 更新到平台
     *
     * @param updateAttributeDtoList 请求信息
     * @param accountNumber          店铺
     */
    private void updateAttribute(List<UpdateAttributeDto> updateAttributeDtoList, String accountNumber, JobInfo jobInfo) {
        if (CollectionUtils.isNotEmpty(updateAttributeDtoList)) {
            for (UpdateAttributeDto createProductRequest : updateAttributeDtoList) {
                FeedTask feedTask = createProductRequest.getFeedTask();
                // 不知道批量怎么返回id
                if (feedTask.getId() == null) {
                    ozonFeedTaskService.insert(feedTask);
                }
            }
            ozonUpdateHandler.updateAttribute(accountNumber, updateAttributeDtoList, jobInfo.getTaskType());
        }
    }

    /**
     * 创建请求信息
     *
     * @param job                 任务信息
     * @param productIdAndItemMap 产品id和商品信息
     * @param syncErrorMap        同步错误信息
     * @param consumer            消费者
     * @return 请求信息
     */
    public List<OzonUpdateItemDO> getCreateProductRequest(JobInfo job, Map<Long, EsOzonItem> productIdAndItemMap, Map<Long, FeedTask> productIdAndFeedTaskMap, Map<Long, String> syncErrorMap, Function<UpdateListingContext, Boolean> consumer) {
        List<UpdateListingContext> updateListingContexts = syncPlatForm(productIdAndItemMap.keySet(), job.getAccountNumber(), syncErrorMap);
        Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
        List<OzonUpdateItemDO> list = new ArrayList<>();
        for (UpdateListingContext mutablePair : updateListingContexts) {
            OzonItemDO ozonItemDO = mutablePair.getOzonItemDO();
            OzonUpdatePackageDO ozonUpdatePackageDO = mutablePair.getOzonUpdatePackageDO();
            EsOzonItem esOzonItem = productIdAndItemMap.get(ozonItemDO.getProductId());
            FeedTask feedTask = productIdAndFeedTaskMap.get(ozonItemDO.getProductId());
            if (feedTask == null) {
                feedTask = getRunFeedTask(job, esOzonItem.getProductId(), esOzonItem.getSellerSku(), esOzonItem.getOzonSku(), esOzonItem.getSku(), timestamp);
            }
            mutablePair.setFeedTask(feedTask);
            boolean success = consumer.apply(mutablePair);
            if (!success) {
                continue;
            }
            CreateProductRequest createProductRequest = OzonPublishUtils.convertUpdateRequest(ozonItemDO, ozonUpdatePackageDO);

            OzonUpdateItemDO requestAndFeedTaskContext = new OzonUpdateItemDO();
            requestAndFeedTaskContext.setCreateProductRequest(createProductRequest);
            requestAndFeedTaskContext.setFeedTask(feedTask);
            requestAndFeedTaskContext.setFeedTask2(mutablePair.getFeedTask2());
            list.add(requestAndFeedTaskContext);
        }
        return list;
    }

    /**
     * 创建请求信息
     *
     * @param job                 任务信息
     * @param syncErrorMap        同步错误信息
     * @param consumer            消费者
     * @return 请求信息
     */
    public List<OzonUpdateItemDO> getCreateProductRequest(JobInfo job, Set<Long> productIdSet, Map<Long, FeedTask> productIdAndFeedTaskMap, Map<Long, String> syncErrorMap, Function<UpdateListingContext, Boolean> consumer) {
        List<UpdateListingContext> updateListingContexts = syncPlatForm(productIdSet, job.getAccountNumber(), syncErrorMap);
        List<OzonUpdateItemDO> list = new ArrayList<>();
        for (UpdateListingContext mutablePair : updateListingContexts) {
            OzonItemDO ozonItemDO = mutablePair.getOzonItemDO();
            OzonUpdatePackageDO ozonUpdatePackageDO = mutablePair.getOzonUpdatePackageDO();
            FeedTask feedTask = productIdAndFeedTaskMap.get(ozonItemDO.getProductId());
            mutablePair.setFeedTask(feedTask);
            boolean success = consumer.apply(mutablePair);
            if (!success) {
                continue;
            }
            CreateProductRequest createProductRequest = OzonPublishUtils.convertUpdateRequest(ozonItemDO, ozonUpdatePackageDO);

            OzonUpdateItemDO requestAndFeedTaskContext = new OzonUpdateItemDO();
            requestAndFeedTaskContext.setCreateProductRequest(createProductRequest);
            requestAndFeedTaskContext.setFeedTask(feedTask);
            requestAndFeedTaskContext.setFeedTask2(mutablePair.getFeedTask2());
            list.add(requestAndFeedTaskContext);
        }
        return list;
    }

    /**
     * 同步的信息
     *
     * @param itemSet       itemSet
     * @param accountNumber 店铺
     * @param syncErrorMap  同步错误信息
     * @return 同步信息
     */
    private List<UpdateListingContext> syncPlatForm(Set<Long> itemSet, String accountNumber, Map<Long, String> syncErrorMap) {
        List<UpdateListingContext> syncItemInfoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(itemSet)) {
            return syncItemInfoList;
        }
        for (List<Long> productIdList : PagingUtils.newPagingList(new ArrayList<>(itemSet), 1000)) {
            Map<Long, OzonItemDO> productIdAndOzonItemDoMap = syncItemDo(accountNumber, syncErrorMap, productIdList);
            if (MapUtils.isEmpty(productIdAndOzonItemDoMap)) {
                continue;
            }
            Map<Long, OzonUpdatePackageDO> productIdAndAttrMap = syncAttr(accountNumber, syncErrorMap, productIdAndOzonItemDoMap);
            if (MapUtils.isEmpty(productIdAndAttrMap)) {
                continue;
            }

            for (Map.Entry<Long, OzonUpdatePackageDO> productIdAndAtrr : productIdAndAttrMap.entrySet()) {
                Long key = productIdAndAtrr.getKey();
                OzonUpdatePackageDO ozonUpdatePackageDO = productIdAndAtrr.getValue();
                OzonItemDO ozonItemDO = productIdAndOzonItemDoMap.get(key);
                UpdateListingContext build = UpdateListingContext.builder().ozonItemDO(ozonItemDO).ozonUpdatePackageDO(ozonUpdatePackageDO).build();
                syncItemInfoList.add(build);
            }
        }
        return syncItemInfoList;
    }

    /**
     * 同步属性
     *
     * @param accountNumber             账号
     * @param syncErrorMap              错误信息
     * @param productIdAndOzonItemDoMap 详情信息
     * @return 属性信息
     */
    private Map<Long, OzonUpdatePackageDO> syncAttr(String accountNumber, Map<Long, String> syncErrorMap, Map<Long, OzonItemDO> productIdAndOzonItemDoMap) {
        Set<Long> afterDoProductIdSet = productIdAndOzonItemDoMap.keySet();
        List<OzonUpdatePackageDO> ozonUpdatePackageDOList = null;
        try {
            ozonUpdatePackageDOList = ozonSyncListingHandler.syncAttributes(new ArrayList<>(afterDoProductIdSet), accountNumber);
        } catch (Exception e) {
            log.error("同步属性失败：{}", e.getMessage());
        }
        if (CollectionUtils.isEmpty(ozonUpdatePackageDOList)) {
            afterDoProductIdSet.forEach(l -> syncErrorMap.put(l, "同步属性失败"));
            return new HashMap<>();
        }

        Map<Long, OzonUpdatePackageDO> productIdAndAttrMap = ozonUpdatePackageDOList.stream().collect(Collectors.toMap(a -> Long.valueOf(a.getId()), a -> a, (a, b) -> a));
        Set<Long> needSyncAttrSet = new HashSet<>(afterDoProductIdSet);
        needSyncAttrSet.removeAll(productIdAndAttrMap.keySet());
        if (CollectionUtils.isNotEmpty(needSyncAttrSet)) {
            needSyncAttrSet.forEach(l -> syncErrorMap.put(l, "同步属性失败"));
        }
        return productIdAndAttrMap;
    }

    /**
     * 同步详情
     *
     * @param accountNumber 账号
     * @param syncErrorMap  错误信息
     * @param productIdList 商品id列表
     * @return 详情信息
     */
    private Map<Long, OzonItemDO> syncItemDo(String accountNumber, Map<Long, String> syncErrorMap, List<Long> productIdList) {
        List<OzonItemDO> ozonItemDOList = null;
        try {
            ozonItemDOList = ozonSyncListingHandler.batchSyncAndSaveItemInfo(accountNumber, productIdList, new HashMap<>());
        } catch (Exception e) {
            log.error("同步详情失败：{}", e.getMessage());
        }
        if (CollectionUtils.isEmpty(ozonItemDOList)) {
            productIdList.forEach(l -> syncErrorMap.put(l, "同步详情失败"));
            return new HashMap<>();
        }
        // 判断哪些没同步到
        Map<Long, OzonItemDO> productIdAndOzonItemDoMap = ozonItemDOList.stream().collect(Collectors.toMap(OzonItemDO::getProductId, a -> a, (a, b) -> a));
        Set<Long> needSyncItem = new HashSet<>(productIdList);
        needSyncItem.removeAll(productIdAndOzonItemDoMap.keySet());
        if (CollectionUtils.isNotEmpty(needSyncItem)) {
            needSyncItem.forEach(l -> syncErrorMap.put(l, "同步详情失败"));
        }
        return productIdAndOzonItemDoMap;
    }


    /**
     * 保存同步错误的日志
     *
     * @param syncErrorMap        错误信息
     * @param productIdAndItemMap 产品id和商品信息
     * @param jobInfo             任务信息
     */
    private void saveSyncErrorFeedTask(Map<Long, String> syncErrorMap, Map<Long, EsOzonItem> productIdAndItemMap, Map<Long, FeedTask> productIdAndFeedTaskMap, JobInfo jobInfo) {
        if (MapUtils.isNotEmpty(syncErrorMap)) {
            List<FeedTask> errorFeedTaskList = genSyncErrorMapFeedTask(syncErrorMap, productIdAndItemMap, productIdAndFeedTaskMap, jobInfo);
            saveFeedTask(errorFeedTaskList);
        }
    }

    /**
     * 保存同步错误的日志
     *
     * @param syncErrorMap        错误信息
     * @param productIdAndItemMap 产品id和商品信息
     * @param jobInfo             任务信息
     */
    private void saveSyncErrorFeedTaskExcel(Map<Long, String> syncErrorMap, Map<Long, UpdateTitleAndDescriptionExcel> productIdAndItemMap, Map<Long, FeedTask> productIdAndFeedTaskMap, JobInfo jobInfo) {
        if (MapUtils.isNotEmpty(syncErrorMap)) {
            List<FeedTask> errorFeedTaskList = new ArrayList<>();
            Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
            for (Map.Entry<Long, String> longStringEntry : syncErrorMap.entrySet()) {
                Long key = longStringEntry.getKey();
                String value = longStringEntry.getValue();
                FeedTask runFeedTask = productIdAndFeedTaskMap.get(key);
                if (runFeedTask == null) {
                    UpdateTitleAndDescriptionExcel esOzonItem = productIdAndItemMap.get(key);
                    runFeedTask = getRunFeedTask(jobInfo, Long.valueOf(esOzonItem.getProductId()), esOzonItem.getSellerSku(), esOzonItem.getOzonSku(), esOzonItem.getSku(), timestamp);
                    runFeedTask.setAttribute8(esOzonItem.getRowIndex().toString());
                    runFeedTask.setAttribute7(esOzonItem.getExcelId().toString());
                }
                runFeedTask.setRunTime(timestamp);
                runFeedTask.setFinishTime(timestamp);
                runFeedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                runFeedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                runFeedTask.setResultMsg(value);
                ozonProblemClassificationHandler.handleFeedTaskProblemClassification(runFeedTask, runFeedTask.getResultMsg(), runFeedTask.getTaskType());
                errorFeedTaskList.add(runFeedTask);
            }
            saveFeedTask(errorFeedTaskList);
        }
    }

    /**
     * 保存处理报告
     *
     * @param feedTasks 处理报告
     */
    public void saveFeedTask(List<FeedTask> feedTasks) {
        if (CollectionUtils.isEmpty(feedTasks)) {
            return;
        }

        List<FeedTask> saveList = feedTasks.stream().filter(a -> a.getId() == null).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(saveList)) {
            List<List<FeedTask>> lists = PagingUtils.newPagingList(saveList, 300);
            for (List<FeedTask> list : lists) {
                ozonFeedTaskService.batchInsertSelective(list);
            }
        }
        List<FeedTask> updateList = feedTasks.stream().filter(a -> a.getId() != null).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<List<FeedTask>> lists = PagingUtils.newPagingList(updateList, 300);
            for (List<FeedTask> list : lists) {
                ozonFeedTaskService.batchUpdateFeeds(list);
            }
        }
    }

    /**
     * 将同步错误的报告转换为处理报告
     *
     * @param syncErrorMap        错误
     * @param productIdAndItemMap 产品信息
     * @param jobInfo             任务信息
     * @return 处理报告
     */
    private List<FeedTask> genSyncErrorMapFeedTask(Map<Long, String> syncErrorMap, Map<Long, EsOzonItem> productIdAndItemMap, Map<Long, FeedTask> productIdAndFeedTaskMap, JobInfo jobInfo) {
        List<FeedTask> errorFeedTaskList = new ArrayList<>();
        Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
        for (Map.Entry<Long, String> longStringEntry : syncErrorMap.entrySet()) {
            Long key = longStringEntry.getKey();
            String value = longStringEntry.getValue();
            FeedTask runFeedTask = productIdAndFeedTaskMap.get(key);
            if (runFeedTask == null) {
                EsOzonItem esOzonItem = productIdAndItemMap.get(key);
                runFeedTask = getRunFeedTask(jobInfo, esOzonItem.getProductId(), esOzonItem.getSellerSku(), esOzonItem.getOzonSku(), esOzonItem.getSku(), timestamp);
            }
            runFeedTask.setRunTime(timestamp);
            runFeedTask.setFinishTime(timestamp);
            runFeedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
            runFeedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
            runFeedTask.setResultMsg(value);
            ozonProblemClassificationHandler.handleFeedTaskProblemClassification(runFeedTask, value, runFeedTask.getTaskType());
            errorFeedTaskList.add(runFeedTask);
        }
        return errorFeedTaskList;
    }


    /**
     * 优先生成处理报告
     *
     * @param accountNumberMap 店铺
     * @param taskType         任务
     * @return 处理报告
     */
    private Map<Long, FeedTask> getRunFeedTask(Map<String, List<EsOzonItem>> accountNumberMap, OzonFeedTaskEnums.TaskType taskType, String job) {
        Map<Long, FeedTask> feedTaskMap = new HashMap<>();
        Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
        for (Map.Entry<String, List<EsOzonItem>> entry : accountNumberMap.entrySet()) {
            String key = entry.getKey();
            JobInfo build = JobInfo.builder().accountNumber(key).taskType(taskType).userName(getUserName()).job(job).build();
            List<EsOzonItem> itemList = entry.getValue();
            for (EsOzonItem esOzonItem : itemList) {
                FeedTask runFeedTask = getRunFeedTask(build, esOzonItem.getProductId(), esOzonItem.getSellerSku(), esOzonItem.getOzonSku(), esOzonItem.getSku(), timestamp);
                ozonFeedTaskService.insert(runFeedTask);
                feedTaskMap.put(esOzonItem.getProductId(), runFeedTask);
            }
        }
        return feedTaskMap;
    }

    /**
     * 优先生成处理报告
     *
     * @param accountNumberMap 店铺
     * @param taskType         任务
     * @return 处理报告
     */
    private Map<Long, FeedTask> getRunFeedTask(Map<String, List<EsOzonItem>> accountNumberMap, Map<Long, UpdateListingWeightVo> productIdAndWeightMap, OzonFeedTaskEnums.TaskType taskType, String job) {
        Map<Long, FeedTask> feedTaskMap = new HashMap<>();
        Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
        for (Map.Entry<String, List<EsOzonItem>> entry : accountNumberMap.entrySet()) {
            String key = entry.getKey();
            JobInfo build = JobInfo.builder().accountNumber(key).taskType(taskType).userName(getUserName()).job(job).build();
            List<EsOzonItem> itemList = entry.getValue();
            for (EsOzonItem esOzonItem : itemList) {
                Long productId = esOzonItem.getProductId();
                UpdateListingWeightVo updateListingWeightVo = productIdAndWeightMap.get(productId);
                FeedTask runFeedTask = getRunFeedTask(build, esOzonItem.getProductId(), esOzonItem.getSellerSku(), esOzonItem.getOzonSku(), esOzonItem.getSku(), timestamp);
                runFeedTask.setAttribute8(updateListingWeightVo.getRowId());
                runFeedTask.setAttribute7(updateListingWeightVo.getExcelId());
                ozonFeedTaskService.insert(runFeedTask);
                feedTaskMap.put(esOzonItem.getProductId(), runFeedTask);
            }
        }
        return feedTaskMap;
    }


    /**
     * 构建处理报告
     *
     * @param job       任务信息
     * @param productId 产品id
     * @param sellerSku sellerSku
     * @param ozonSku   ozonSku
     * @param sku       sku
     * @param time      时间
     * @return 处理报告
     */
    private static FeedTask getRunFeedTask(JobInfo job, Long productId, String sellerSku, Long ozonSku, String sku, Timestamp time) {
        FeedTask feedTask = new FeedTask();
        feedTask.setAssociationId(productId.toString());
        feedTask.setAccountNumber(job.getAccountNumber());
        feedTask.setArticleNumber(sku);
        feedTask.setTaskType(job.getTaskType().name());
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
        feedTask.setAttribute1(sellerSku);
        feedTask.setAttribute5(ozonSku != null ? ozonSku.toString() : "");
        feedTask.setAttribute10(job.getJob());
        feedTask.setCreatedBy(job.getUserName());
        feedTask.setCreateTime(time);
        feedTask.setTableIndex();
        return feedTask;
    }

    /**
     * 创建错误 信息
     */
    private FeedTask getErrorTask(String job, String username, String taskType,
                                         String accountNumber, String productId, String sellerSku,
                                         String sku, String msg, Timestamp time) {
        FeedTask feedTask = new FeedTask();
        feedTask.setAssociationId(productId);
        feedTask.setAccountNumber(accountNumber);
        feedTask.setArticleNumber(sku);
        feedTask.setTaskType(taskType);
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
        feedTask.setResultMsg(msg);
        feedTask.setAttribute1(sellerSku);
        feedTask.setAttribute10(job);
        feedTask.setCreatedBy(username);
        feedTask.setCreateTime(time);
        feedTask.setTableIndex();

        ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, msg, taskType);

        return feedTask;
    }

    /**
     * 处理报告设置为失败
     *
     * @param feedTask 处理报告
     * @param msg      错误消息
     */
    private void setErrorFeedTask(FeedTask feedTask, String msg) {
        Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());
        feedTask.setResultMsg(msg);
        feedTask.setRunTime(timestamp);
        feedTask.setFinishTime(timestamp);
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
        ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, feedTask.getResultMsg(), feedTask.getTaskType());
    }

    /**
     * 获取在线列表信息
     *
     * @param productIdSet 产品id
     * @return 在线列表信息
     */
    private List<EsOzonItem> getEsListing(Set<Long> productIdSet) {
        if (CollectionUtils.isEmpty(productIdSet)) {
            return new ArrayList<>();
        }
        List<List<Long>> lists = PagingUtils.newPagingList(new ArrayList<>(productIdSet), 1000);
        List<EsOzonItem> esOzonItems = new ArrayList<>();
        for (List<Long> list : lists) {
            try {
                EsOzonItemRequest request = new EsOzonItemRequest();
                request.setProductIds(list);
                request.setFields(new String[]{"id", "productId", "accountNumber", "spu", "actualWeight", "sku", "sellerSku", "weight", "skuCount", "ozonSku"});
                List<EsOzonItem> esOzonItems1 = esOzonItemService.listItemByRequest(request);
                esOzonItems.addAll(esOzonItems1);
            } catch (Exception e) {
                log.error("获取在线列表信息失败：{}", e.getMessage());
            }
        }
        return esOzonItems;
    }

    /**
     * 获取sku的包装信息
     *
     * @param skuList sku列表
     * @return sku的包装信息
     */
    public Map<String, PackageInfo> getPackageSize(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return new HashMap<>();
        }
        List<String> singleItems = new ArrayList<>();
        List<String> suiteItems = new ArrayList<>();
        List<String> composeItems = new ArrayList<>();
        OzonSkuUtils.skuGroup(skuList, singleItems, composeItems, suiteItems);

        Map<String, PackageInfo> skuAndPackageInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(singleItems)) {
            // 查询产品系统获取包裹重量
            List<PackageInfo> packageInfoBySku = singleItemEsService.getPackageInfoBySku(singleItems);
            skuAndPackageInfoMap.putAll(packageInfoBySku.stream().collect(Collectors.toMap(PackageInfo::getSku, a -> a, (a, b) -> a)));
        }
        if (CollectionUtils.isNotEmpty(suiteItems)) {
            skuAndPackageInfoMap.putAll(OzonSkuUtils.getSuitePackageInfo(suiteItems));
        }
        if (CollectionUtils.isNotEmpty(composeItems)) {
            skuAndPackageInfoMap.putAll(OzonSkuUtils.getComposePackageInfo(composeItems));
        }
        return skuAndPackageInfoMap;
    }

    /**
     * 获取账号的包装信息
     *
     * @param accountNumberList 店铺
     * @return 账号的包装信息
     */
    public Map<String, PackageInfo> getAccountPackageSize(List<String> accountNumberList) {
        if (CollectionUtils.isEmpty(accountNumberList)) {
            return new HashMap<>();
        }
        List<List<String>> lists = PagingUtils.newPagingList(accountNumberList, 1000);
        Map<String, PackageInfo> packageInfoMap = new HashMap<>();
        for (List<String> list : lists) {
            try {
                OzonAccountConfigExample example = new OzonAccountConfigExample();
                example.createCriteria().andAccountNumberIn(list);
                example.setColumns("id, account_number, default_length, default_Width, default_height");
                List<OzonAccountConfig> ozonAccountConfigs = ozonAccountConfigService.selectByExample(example);
                Map<String, PackageInfo> collect = ozonAccountConfigs.stream().collect(Collectors.toMap(OzonAccountConfig::getAccountNumber, a -> {
                    PackageInfo packageInfo = new PackageInfo();
                    packageInfo.setLength(a.getDefaultLength() != null ? BigDecimal.valueOf(a.getDefaultLength()) : null);
                    packageInfo.setWide(a.getDefaultWidth() != null ? BigDecimal.valueOf(a.getDefaultWidth()) : null);
                    packageInfo.setHeight(a.getDefaultHeight() != null ? BigDecimal.valueOf(a.getDefaultHeight()) : null);
                    return packageInfo;
                }));
                packageInfoMap.putAll(collect);
            } catch (Exception e) {
                log.error("获取包装信息失败：{}", e.getMessage());
            }
        }
        return packageInfoMap;
    }

    /**
     * 获取用户信息
     *
     * @return 用户名
     */
    public String getUserName() {
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        return StringUtils.isBlank(currentUser) ? "admin" : currentUser;
    }
}
