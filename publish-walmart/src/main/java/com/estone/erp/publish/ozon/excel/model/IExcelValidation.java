package com.estone.erp.publish.ozon.excel.model;

import com.estone.erp.publish.ozon.excel.handler.validator.ValidationResult;
import org.apache.commons.lang.StringUtils;

import java.util.function.Consumer;

/**
 * excel 表格校验数据
 */
public interface IExcelValidation {

    /**
     * 第几行
     * @return excel行
     */
    Integer getRowIndex();

    /**
     * 校验的备注
     * @param remark 备注
     */
    void setRemark(String remark);

    /**
     * 校验的结果
     * @param result 结果
     */
    void setResult(String result);
    String getResult();
    String getRemark();

    /**
     * 这个必须需要，因为要做excel表格权限验证的时候用到
     */
    String getAccountNumber();

    /**
     * 校验
     * @return 结果
     */
    default ValidationResult validate() {
        return new ValidationResult();
    }

    /**
     * 验证并清理字段
     *
     * @param result        验证结果
     * @param value         字段值
     * @param emptyErrorMsg 为空时的错误消息
     * @param setter        设置器
     */
    default void validateAndTrimField(ValidationResult result, String value, String emptyErrorMsg, Consumer<String> setter) {
        if (StringUtils.isBlank(value)) {
            result.addError(emptyErrorMsg);
        } else {
            setter.accept(value.trim());
        }
    }

    /**
     * 验证并清理整数字段
     *
     * @param result 校验
     * @param value 只
     * @param emptyErrorMsg 错误信息
     * @param setter 设置方法
     */
    default void validateAndTrimIntegerField(ValidationResult result, String value, String emptyErrorMsg, Consumer<Integer> setter) {
        if (StringUtils.isBlank(value)) {
            result.addError(emptyErrorMsg);
        } else {
            try {
                setter.accept(Integer.parseInt(value.trim()));
            } catch (Exception e) {
                result.addError("格式错误");
            }
        }
    }

}
