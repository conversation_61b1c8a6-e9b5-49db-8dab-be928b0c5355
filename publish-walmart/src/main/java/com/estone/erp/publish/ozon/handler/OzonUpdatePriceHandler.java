package com.estone.erp.publish.ozon.handler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.ozon.call.OzonApiClient;
import com.estone.erp.publish.ozon.call.OzonResponseResult;
import com.estone.erp.publish.ozon.call.model.request.UpdatePriceRequest;
import com.estone.erp.publish.ozon.call.model.response.UpdateResponse;
import com.estone.erp.publish.ozon.common.OzonEsItemBulkProcessor;
import com.estone.erp.publish.ozon.common.OzonExecutors;
import com.estone.erp.publish.ozon.enums.OzonFeedTaskEnums;
import com.estone.erp.publish.ozon.excel.handler.definition.PriceFunctionTaskDefinition;
import com.estone.erp.publish.ozon.model.OzonAccountConfig;
import com.estone.erp.publish.ozon.model.OzonCalcPriceRule;
import com.estone.erp.publish.ozon.model.dto.OzonUpdatePriceInfoDo;
import com.estone.erp.publish.ozon.service.OzonAccountConfigService;
import com.estone.erp.publish.ozon.service.OzonFeedTaskService;
import com.estone.erp.publish.ozon.service.OzonProblemClassificationHandler;
import com.estone.erp.publish.ozon.utils.OzonPriceUtils;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ozon 更新价格工具类，后续采用这个作为新的
 */
@Slf4j
@Component
public class OzonUpdatePriceHandler {
    @Autowired
    private OzonAccountConfigService accountConfigService;

    @Autowired
    private OzonFeedTaskService ozonFeedTaskService;

    @Autowired
    private OzonApiClient apiClient;

    @Autowired
    private OzonEsItemBulkProcessor esItemBulkProcessor;

    @Autowired
    private OzonProblemClassificationHandler ozonProblemClassificationHandler;

    public String getUserName() {
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        return StringUtils.isBlank(currentUser) ? "admin" : currentUser;
    }

    /**
     * 修改价格
     *
     * @param accountNumber  店铺账号
     * @param updateDateList 更新主题
     */
    public void updateAllPrice(String accountNumber, List<OzonUpdatePriceInfoDo> updateDateList) {
        if (!validateInputParameters(accountNumber, updateDateList)) {
            return;
        }

        OzonAccountConfig accountConfig = getAccountConfig(accountNumber);
        if (accountConfig == null) {
            return;
        }

        List<OzonUpdatePriceInfoDo> validItems = filterValidItems(updateDateList);
        if (CollectionUtils.isEmpty(validItems)) {
            log.warn("No valid items to update after filtering for account {}", accountNumber);
            return;
        }

        processOriginPriceCalculation(accountConfig, validItems);
        initUpdateAllPriceFeedTask(validItems);
        executePriceUpdates(accountNumber, validItems);
    }

    /**
     * 验证输入参数
     *
     * @param accountNumber 账号
     * @param items         更新项列表
     * @return 是否验证通过
     */
    private boolean validateInputParameters(String accountNumber, List<OzonUpdatePriceInfoDo> items) {
        if (StringUtils.isBlank(accountNumber)) {
            log.warn("Account number is null or empty");
            return false;
        }

        if (CollectionUtils.isEmpty(items)) {
            log.warn("Update list is empty for account {}", accountNumber);
            return false;
        }

        return true;
    }

    /**
     * 获取账号配置
     *
     * @param accountNumber 账号
     * @return 账号配置
     */
    private OzonAccountConfig getAccountConfig(String accountNumber) {
        OzonAccountConfig config = accountConfigService.selectConfigWithCalePriceRule(accountNumber);
        if (config == null) {
            log.warn("Account config not found for account {}", accountNumber);
        }
        return config;
    }

    /**
     * 过滤有效项
     *
     * @param items 更新项列表
     * @return 有效项列表
     */
    private List<OzonUpdatePriceInfoDo> filterValidItems(List<OzonUpdatePriceInfoDo> items) {
        return items.stream()
                .filter(item -> item.getProductId() != null)
                .collect(Collectors.toList());
    }


    /**
     * 处理需要计算原价的项目
     *
     * @param accountConfig   账号配置
     * @param updatePriceList 价格更新列表
     */
    private void processOriginPriceCalculation(OzonAccountConfig accountConfig, List<OzonUpdatePriceInfoDo> updatePriceList) {
        List<OzonUpdatePriceInfoDo> itemsNeedingCalculation = filterItemsNeedingPriceCalculation(updatePriceList);
        if (CollectionUtils.isEmpty(itemsNeedingCalculation)) {
            return;
        }

        enrichItemsWithProductTags(itemsNeedingCalculation);
        applyPriceCalculationRules(accountConfig, itemsNeedingCalculation);
    }

    /**
     * 过滤需要计算原价的项目
     *
     * @param items 价格更新列表
     * @return 需要计算原价的项目列表
     */
    private List<OzonUpdatePriceInfoDo> filterItemsNeedingPriceCalculation(List<OzonUpdatePriceInfoDo> items) {
        return items.stream()
                .filter(OzonUpdatePriceInfoDo::needCalcAfterOriginPrice)
                .collect(Collectors.toList());
    }

    /**
     * 为项目添加产品标签
     *
     * @param items 需要计算原价的项目列表
     */
    private void enrichItemsWithProductTags(List<OzonUpdatePriceInfoDo> items) {
        OzonPriceUtils.initProductTag(items);
    }

    /**
     * 应用价格计算规则
     *
     * @param accountConfig 账号配置
     * @param items         需要计算原价的项目列表
     */
    private void applyPriceCalculationRules(OzonAccountConfig accountConfig, List<OzonUpdatePriceInfoDo> items) {
        List<OzonCalcPriceRule> rules = accountConfig.getCalcPriceRules();
        if (CollectionUtils.isEmpty(rules)) {
            log.warn("No price calculation rules found for account");
            return;
        }

        OzonPriceUtils.matchAfterOriginPriceCalcRule(rules, items);
    }


    /**
     * 执行价格更新
     *
     * @param accountNumber   账号
     * @param updatePriceList 价格更新列表
     */
    private void executePriceUpdates(String accountNumber, List<OzonUpdatePriceInfoDo> updatePriceList) {
        int batchSize = 500;
        List<List<OzonUpdatePriceInfoDo>> batches = Lists.partition(updatePriceList, batchSize);

        log.info("Processing {} items in {} batches for account {}", 
                updatePriceList.size(), batches.size(), accountNumber);

        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (int i = 0; i < batches.size(); i++) {
            List<OzonUpdatePriceInfoDo> batch = batches.get(i);
            int batchNumber = i + 1;

            CompletableFuture<Void> future = CompletableFuture
                .runAsync(() -> processBatch(accountNumber, batch, batchNumber, batches.size()), 
                        OzonExecutors.SYNC_UPDATE_PRICE_POOL)
                .exceptionally(ex -> {
                    log.error("Error processing batch {}/{} for account {}: {}", 
                            batchNumber, batches.size(), accountNumber, ex.getMessage(), ex);
                    markBatchAsFailed(batch, ex.getMessage());
                    return null;
                });

            futures.add(future);
        }

        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            log.info("Completed processing all batches for account {}", accountNumber);
        } catch (Exception e) {
            log.error("Error waiting for batch processing completion for account {}: {}", 
                    accountNumber, e.getMessage(), e);
        }
    }

    /**
     * 处理批次
     *
     * @param accountNumber 账号
     * @param batch         批次数据
     * @param batchNumber   批次编号
     * @param totalBatches  总批次数
     */
    private void processBatch(String accountNumber, List<OzonUpdatePriceInfoDo> batch, 
                             int batchNumber, int totalBatches) {
        log.info("Starting batch {}/{} with {} items for account {}", 
                batchNumber, totalBatches, batch.size(), accountNumber);

        doUpdateAllPrice(accountNumber, batch);

        log.info("Completed batch {}/{} for account {}", 
                batchNumber, totalBatches, accountNumber);
    }

    /**
     * 将批次标记为失败
     *
     * @param batch        批次数据
     * @param errorMessage 错误消息
     */
    private void markBatchAsFailed(List<OzonUpdatePriceInfoDo> batch, String errorMessage) {
        batch.forEach(item -> {
            item.setSuccess(false);
            item.setErrorMsg("Batch processing failed: " + errorMessage);
        });

        // 更新失败项的任务
        List<FeedTask> tasks = batch.stream()
                .filter(item -> CollectionUtils.isNotEmpty(item.getFeedTaskList()))
                .flatMap(item -> item.getFeedTaskList().stream())
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(tasks)) {
            tasks.forEach(task -> {
                task.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                task.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                ozonProblemClassificationHandler.handleFeedTaskProblemClassification(task, task.getResultMsg(), task.getTaskType());
                task.setResultMsg("Batch processing failed: " + errorMessage);
            });

            ozonFeedTaskService.batchUpdateFeeds(tasks);
        }
    }


    /**
     * 修改价格 处理报告
     *
     * @param updatePriceList 列表
     */
    private void initUpdateAllPriceFeedTask(List<OzonUpdatePriceInfoDo> updatePriceList) {
        String userName = getUserName();
        Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());

        // 定义价格类型和处理逻辑
        List<PriceFunctionTaskDefinition> taskDefinitions = Arrays.asList(
            new PriceFunctionTaskDefinition(
                OzonFeedTaskEnums.TaskType.UPDATE_PRICE,
                    OzonUpdatePriceInfoDo::getAfterPrice,
                    OzonUpdatePriceInfoDo::getBeforePrice
            ),
            new PriceFunctionTaskDefinition(
                OzonFeedTaskEnums.TaskType.UPDATE_OLD_PRICE,
                    OzonUpdatePriceInfoDo::getAfterOldPrice,
                    OzonUpdatePriceInfoDo::getBeforeOldPrice
            ),
            new PriceFunctionTaskDefinition(
                OzonFeedTaskEnums.TaskType.UPDATE_MIN_PRICE,
                    OzonUpdatePriceInfoDo::getAfterMinPrice,
                    OzonUpdatePriceInfoDo::getBeforeMinPrice
            )
        );

        List<FeedTask> equalPriceTasks = new ArrayList<>();

        // 处理每个更新项
        for (OzonUpdatePriceInfoDo item : updatePriceList) {
            List<FeedTask> itemTasks = new ArrayList<>();

            // 为每种价格类型创建任务
            for (PriceFunctionTaskDefinition def : taskDefinitions) {
                Double afterValue = def.getAfterValueExtractor().apply(item);
                if (afterValue == null) {
                    continue;
                }

                Double beforeValue = def.getBeforeValueExtractor().apply(item);

                FeedTask task = createFeedTask(item, timestamp, userName, def.getTaskType(),
                        afterValue.toString(), 
                        beforeValue != null ? beforeValue.toString() : null);

                // 检查价格是否相等
                if (Objects.equals(afterValue, beforeValue)) {
                    task.setResultMsg("改前值和改后值相等，不修改");
                    task.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                    task.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                    ozonProblemClassificationHandler.handleFeedTaskProblemClassification(task, task.getResultMsg(), task.getTaskType());
                    equalPriceTasks.add(task);
                } else {
                    task.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());

                    // 特殊处理原价
                    if (def.getTaskType() == OzonFeedTaskEnums.TaskType.UPDATE_PRICE && 
                            item.getAfterOldPrice() == null) {
                        String originPrice = item.getAfterOriginPrice() == null ? "0" : 
                                item.getAfterOriginPrice().toString();
                        task.setAttribute4(originPrice + "," + task.getAttribute4());
                    }

                    itemTasks.add(task);
                }
            }

            item.setFeedTaskList(itemTasks);
        }

        // 批量保存相等价格的任务
        if (CollectionUtils.isNotEmpty(equalPriceTasks)) {
            ozonFeedTaskService.batchInsertSelective(equalPriceTasks);
        }

        // 保存其他任务
        saveFeedTasks(updatePriceList);
    }

    /**
     * 保存Feed任务
     *
     * @param updatePriceList 价格更新列表
     */
    private void saveFeedTasks(List<OzonUpdatePriceInfoDo> updatePriceList) {
        for (OzonUpdatePriceInfoDo item : updatePriceList) {
            List<FeedTask> tasks = item.getFeedTaskList();
            if (CollectionUtils.isNotEmpty(tasks)) {
                tasks.forEach(ozonFeedTaskService::insert);
            }
        }
    }

    /**
     * 创建Feed任务
     *
     * @param item      价格更新项
     * @param timestamp 时间戳
     * @param username  用户名
     * @param taskType  任务类型
     * @param afterValue 修改后值
     * @param beforeValue 修改前值
     * @return Feed任务
     */
    private FeedTask createFeedTask(OzonUpdatePriceInfoDo item, Timestamp timestamp, 
                                   String username, OzonFeedTaskEnums.TaskType taskType,
                                   String afterValue, String beforeValue) {
        FeedTask feedTask = new FeedTask();
        feedTask.setAssociationId(item.getProductId().toString());
        feedTask.setAccountNumber(item.getAccountNumber());
        feedTask.setArticleNumber(item.getSku());
        feedTask.setTaskType(taskType.name());
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setResultMsg(item.getErrorMsg());
        feedTask.setCreatedBy(username);
        feedTask.setCreateTime(timestamp);
        feedTask.setFinishTime(timestamp);
        feedTask.setAttribute3(beforeValue);
        feedTask.setAttribute4(afterValue);
        feedTask.setAttribute9(JSON.toJSONString(item));
        feedTask.setAttribute1(item.getSellerSku());
        feedTask.setAttribute10(item.getJob());
        feedTask.setTableIndex();
        return feedTask;
    }



    /**
     * 执行价格更新API调用
     *
     * @param accountNumber 店铺账号
     * @param items         价格更新数据列表
     */
    private void doUpdateAllPrice(String accountNumber, List<OzonUpdatePriceInfoDo> items) {
        if (CollectionUtils.isEmpty(items)) {
            log.warn("Empty update list for account {}", accountNumber);
            return;
        }

        log.info("Updating prices for {} items in account {}", items.size(), accountNumber);

        try {
            OzonResponseResult<List<UpdateResponse>> response = callUpdatePriceApi(accountNumber, items);
            handleApiResponse(items, response);
        } catch (Exception e) {
            handleUpdateException(items, e);
        }
    }

    /**
     * 调用更新价格API
     *
     * @param accountNumber 账号
     * @param items         价格更新数据列表
     * @return API响应结果
     */
    private OzonResponseResult<List<UpdateResponse>> callUpdatePriceApi(String accountNumber, 
                                                                      List<OzonUpdatePriceInfoDo> items) {
        UpdatePriceRequest request = new UpdatePriceRequest();
        request.initPricesInfo(items);

        log.debug("Calling update price API for account {} with {} items", 
                accountNumber, items.size());

        return apiClient.updatePrice(accountNumber, request);
    }

    /**
     * 处理API响应
     *
     * @param items    价格更新数据列表
     * @param response API响应结果
     */
    private void handleApiResponse(List<OzonUpdatePriceInfoDo> items, 
                                 OzonResponseResult<List<UpdateResponse>> response) {
        if (!response.isSuccess()) {
            log.warn("API call failed: {}", response.getMessage());
            handleUpdateFailure(items, response);
            return;
        }

        log.info("API call successful, processing responses");
        processUpdateResponses(items, response.getResult());
    }

    /**
     * 处理更新异常
     *
     * @param items 价格更新数据列表
     * @param e     异常
     */
    private void handleUpdateException(List<OzonUpdatePriceInfoDo> items, Exception e) {
        String errorMessage = "Exception during price update: " + e.getMessage();
        log.error(errorMessage, e);
        markAllTasksAsFailed(items, errorMessage);
    }


    /**
     * 处理价格更新API调用失败的情况
     *
     * @param list           价格更新数据列表
     * @param responseResult API响应结果
     */
    private void handleUpdateFailure(List<OzonUpdatePriceInfoDo> list, OzonResponseResult<List<UpdateResponse>> responseResult) {
        String errorMessage = responseResult.getMessage();
        log.warn("Price update API call failed: {}", errorMessage);

        List<FeedTask> errorList = new ArrayList<>();

        for (OzonUpdatePriceInfoDo updateItem : list) {
            updateItem.setSuccess(false);
            updateItem.setErrorMsg(errorMessage);

            List<FeedTask> feedTaskList = updateItem.getFeedTaskList();
            if (CollectionUtils.isNotEmpty(feedTaskList)) {
                for (FeedTask feedTask : feedTaskList) {
                    feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                    feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                    feedTask.setResultMsg(StringUtils.defaultString(feedTask.getResultMsg(), "") + errorMessage);
                    ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, errorMessage, feedTask.getTaskType());
                    errorList.add(feedTask);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(errorList)) {
            ozonFeedTaskService.batchUpdateFeeds(errorList);
        }
    }

    /**
     * 处理价格更新API的响应结果
     *
     * @param items        价格更新数据列表
     * @param responses    API响应结果列表
     */
    private void processUpdateResponses(List<OzonUpdatePriceInfoDo> items, List<UpdateResponse> responses) {
        if (CollectionUtils.isEmpty(responses)) {
            log.warn("Empty response list from price update API");
            markAllTasksAsFailed(items, "Empty response from API");
            return;
        }

        log.info("Processing {} responses for {} items", responses.size(), items.size());

        // 创建productId到响应的映射
        Map<Long, UpdateResponse> responseMap = createResponseMap(responses);

        // 处理每个更新项
        items.stream()
            .filter(item -> CollectionUtils.isNotEmpty(item.getFeedTaskList()))
            .forEach(item -> processItemResponse(item, responseMap));

        // 更新所有任务状态
        updateAllFeedTasks(items);
    }

    /**
     * 创建productId到响应的映射
     *
     * @param responses API响应结果列表
     * @return productId到响应的映射
     */
    private Map<Long, UpdateResponse> createResponseMap(List<UpdateResponse> responses) {
        return responses.stream()
                .collect(Collectors.toMap(
                        UpdateResponse::getProductId,
                        Function.identity(),
                        (o1, o2) -> o1
                ));
    }

    /**
     * 处理单个项目的响应
     *
     * @param item        价格更新数据项
     * @param responseMap productId到响应的映射
     */
    private void processItemResponse(OzonUpdatePriceInfoDo item, Map<Long, UpdateResponse> responseMap) {
        Long productId = item.getProductId();
        List<FeedTask> tasks = item.getFeedTaskList();

        // 使用Optional处理可能为空的情况
        Optional.ofNullable(responseMap.get(productId))
            .ifPresentOrElse(
                // 响应存在
                response -> handleExistingResponse(item, tasks, response),
                // 响应不存在
                () -> handleMissingResponse(item, tasks, productId)
            );
    }

    /**
     * 处理存在的响应
     *
     * @param item     价格更新数据项
     * @param tasks    任务列表
     * @param response API响应结果
     */
    private void handleExistingResponse(OzonUpdatePriceInfoDo item, List<FeedTask> tasks, 
                                      UpdateResponse response) {
        if (response.getUpdated()) {
            markItemAsSuccess(item, tasks);
        } else {
            markItemAsFailed(item, tasks, JSON.toJSONString(response));
        }
    }

    /**
     * 处理缺失的响应
     *
     * @param item      价格更新数据项
     * @param tasks     任务列表
     * @param productId 产品ID
     */
    private void handleMissingResponse(OzonUpdatePriceInfoDo item, List<FeedTask> tasks, Long productId) {
        String errorMsg = "No response found for product ID: " + productId;
        log.warn(errorMsg);
        markItemAsFailed(item, tasks, errorMsg);
    }

    /**
     * 将项目标记为成功
     *
     * @param item  价格更新数据项
     * @param tasks 任务列表
     */
    private void markItemAsSuccess(OzonUpdatePriceInfoDo item, List<FeedTask> tasks) {
        item.setSuccess(true);
        tasks.forEach(task -> {
            task.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
            task.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
            task.setResultMsg("success");
        });
        updateLocalData(item);
    }

    /**
     * 更新本地数据
     * @param item item
     */
    private void updateLocalData(OzonUpdatePriceInfoDo item) {
        Long productId = item.getProductId();
        Double afterOldPrice = item.getAfterOldPrice();
        Double afterOriginPrice = item.getAfterOriginPrice();
        Double afterMinPrice = item.getAfterMinPrice();
        Double afterPrice = item.getAfterPrice();

        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime now = LocalDateTime.now();

        Map<String, Object> param = new HashMap<>();
        if (afterOriginPrice != null) {
            param.put("oldPrice", afterOriginPrice.toString());
            param.put("oldPriceNumber", afterOriginPrice);
        }
        if (afterOldPrice != null) {
            param.put("oldPrice", afterOldPrice.toString());
            param.put("oldPriceNumber", afterOldPrice);
        }
        if (afterMinPrice != null) {
            param.put("minPrice", afterMinPrice.toString());
            param.put("minPriceNumber", afterMinPrice);
            param.put("minPriceNumberUpdateDate", dateTimeFormatter.format(now));
        }
        if (afterPrice != null) {
            param.put("price", afterPrice.toString());
            param.put("priceNumber", afterPrice);
            param.put("updateDate", dateTimeFormatter.format(now));
        }
        esItemBulkProcessor.updatePrice(productId.toString(), param);
    }

    /**
     * 将项目标记为失败
     *
     * @param item       价格更新数据项
     * @param tasks      任务列表
     * @param errorMsg   错误消息
     */
    private void markItemAsFailed(OzonUpdatePriceInfoDo item, List<FeedTask> tasks, String errorMsg) {
        item.setSuccess(false);
        tasks.forEach(task -> {
            task.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
            task.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
            task.setResultMsg(errorMsg);
            ozonProblemClassificationHandler.handleFeedTaskProblemClassification(task, task.getResultMsg(), task.getTaskType());
        });
    }

    /**
     * 将所有任务标记为失败
     *
     * @param list         价格更新数据列表
     * @param errorMessage 错误信息
     */
    private void markAllTasksAsFailed(List<OzonUpdatePriceInfoDo> list, String errorMessage) {
        List<FeedTask> allTasks = new ArrayList<>();

        for (OzonUpdatePriceInfoDo updateItem : list) {
            updateItem.setSuccess(false);
            updateItem.setErrorMsg(errorMessage);

            List<FeedTask> feedTaskList = updateItem.getFeedTaskList();
            if (CollectionUtils.isNotEmpty(feedTaskList)) {
                for (FeedTask feedTask : feedTaskList) {
                    feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                    feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                    feedTask.setResultMsg(errorMessage);
                    ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, feedTask.getResultMsg(), feedTask.getTaskType());
                    allTasks.add(feedTask);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(allTasks)) {
            ozonFeedTaskService.batchUpdateFeeds(allTasks);
        }
    }


    /**
     * 更新所有任务状态
     *
     * @param list 价格更新数据列表
     */
    private void updateAllFeedTasks(List<OzonUpdatePriceInfoDo> list) {
        List<FeedTask> allTasks = list.stream()
                .filter(item -> CollectionUtils.isNotEmpty(item.getFeedTaskList()))
                .flatMap(item -> item.getFeedTaskList().stream())
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(allTasks)) {
            ozonFeedTaskService.batchUpdateFeeds(allTasks);
        }
    }

}
