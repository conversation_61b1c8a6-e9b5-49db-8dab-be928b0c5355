package com.estone.erp.publish.ozon.common;

/**
 * 处理报告常量信息
 */
public class OzonErrorConstant {

    /**
     * 标题描述中存在英语，请修改标题描述后再进行刊登。
     */
    public static final String TITLE_OR_DESCRIPTION_CONTENT_ENGLISH = "标题描述中存在英语，请修改标题描述后再进行刊登。";

    /**
     * 模版重复刊登
     */
    public static final String REPEAT_PUBLISH_TEMPLATE = "模板重复刊登";

    /**
     * 产品重复刊登
     * 在线列表数据存在了
     */
    public static final String REPEAT_PUBLISH_LISTING = "产品重复刊登";

    /**
     * 无店铺权限访问
     */
    public static final String NO_STORE_ACCESS_PERMISSION = "无店铺权限访问";

    /**
     * 未获取到授权用户
     */
    public static final String UNAUTHORIZED_USER_NOT_OBTAINED = "未获取到授权用户！";

    /**
     * 店铺配置问题
     */
    public static final String CLIENTID_OR_APIKEY_CANT_BE_EMPTY = "clientId or apiKey cant be empty";

    /**
     * 根据站点获取币种失败
     */
    public static final String FAILED_TO_OBTAIN_CURRENCY_BASED_ON_SITE = "根据站点获取币种失败";
    /**
     * 根据币种获取站点失败
     */
    public static final String FAILED_TO_OBTAIN_SITE_BASED_ON_CURRENCY = "根据币种获取站点失败";


}
