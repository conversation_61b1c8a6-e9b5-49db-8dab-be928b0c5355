package com.estone.erp.publish.ozon.model.dto.solution;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class BatchSolutionResult {
    private final List<ErrorSolutionResult> results;
    private final int totalSolutions;
    private final long duration;
    private final boolean success;
    private final String message;

    private BatchSolutionResult(List<ErrorSolutionResult> results,
                                int totalSolutions, long duration, boolean success, String message) {
        this.results = results != null ? results : new ArrayList<>();
        this.totalSolutions = totalSolutions;
        this.duration = duration;
        this.success = success;
        this.message = message;
    }

    public static BatchSolutionResult success(List<ErrorSolutionResult> results, long duration) {
        return new BatchSolutionResult(results, results.size(), duration, true, "成功");
    }

    public static BatchSolutionResult empty(int totalCount, String message) {
        return new BatchSolutionResult(null, totalCount, 0, false, message);
    }

    public static BatchSolutionResult error(int totalCount, String message) {
        return new BatchSolutionResult(null, totalCount, 0, false, message);
    }

}
