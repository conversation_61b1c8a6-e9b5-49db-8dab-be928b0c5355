package com.estone.erp.publish.ozon.excel.handler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.util.NumberUtils;
import com.estone.erp.publish.ozon.enums.OzonFeedTaskEnums;
import com.estone.erp.publish.ozon.model.OzonTemplateModel;
import com.estone.erp.publish.ozon.model.OzonTemplateModelExample;
import com.estone.erp.publish.ozon.model.vo.OzonFeedTaskPublishTemplateVO;
import com.estone.erp.publish.ozon.service.OzonTemplateModelService;
import com.estone.erp.publish.platform.enums.SmallPlatformDownloadEnums;
import com.estone.erp.publish.platform.model.SmallPlatformExcelDownloadLog;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskCriteria;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Feed Task handler for export
 * <AUTHOR>
 */
@Component
public class FeedTaskPublishTemplateHandler implements DownloadHandler {
    @Resource
    private FeedTaskService feedTaskService;
    @Resource
    private OzonTemplateModelService ozonTemplateModelService;
    @Override
    public SmallPlatformDownloadEnums.Type getType() {
        return SmallPlatformDownloadEnums.Type.FEED_TASK_PUBLISH_TEMPLATE;
    }

    @Override
    public void writeFile(SmallPlatformExcelDownloadLog downloadLog, File file) {
        DataContextHolder.setUsername(downloadLog.getCreateBy());

        // Parse query condition
        FeedTaskCriteria searchCriteria =  JSON.parseObject(downloadLog.getQueryCondition(),
                FeedTaskCriteria.class);
        searchCriteria.setTaskType(OzonFeedTaskEnums.TaskType.PUBLISH.name());
        searchCriteria.setAssociationIdIsNotNull(true);
        CQuery<FeedTaskCriteria> query = new CQuery<>();
        query.setSearch(searchCriteria);
        // Set up Excel writer
        ExcelWriter excelWriter = EasyExcel.write(file, OzonFeedTaskPublishTemplateVO.class).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
        
        // Track accounts for logging
        Set<String> accounts = Sets.newHashSet();
        
        // Pagination parameters
        int offset = 0;
        int limit = 1000;
        int total = 0;

        // Paginate through results
        while (true) {
            query.setOffset(offset);
            query.setLimit(limit);
            
            // Query feed tasks
            CQueryResult<FeedTask> search = feedTaskService.search(query);
            List<FeedTask> feedTasks = search.getRows();
            if (CollectionUtils.isEmpty(feedTasks)) {
                break;
            }

            List<Integer> templateIds = feedTasks.stream()
                    .map(FeedTask::getAssociationId)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .filter(NumberUtils::isNumber)
                    .map(Integer::valueOf).collect(Collectors.toList());

            Map<Integer, OzonTemplateModel> templateModelMap = new HashMap<>(templateIds.size());
            if (CollectionUtils.isNotEmpty(templateIds)) {
                OzonTemplateModelExample example = new OzonTemplateModelExample();
                example.createCriteria().andIdIn(templateIds);
                example.setColumns("id, title, description");
                List<OzonTemplateModel> ozonTemplateModels = ozonTemplateModelService.selectByExample(example);
                Map<Integer, OzonTemplateModel> collect = ozonTemplateModels.stream().collect(Collectors.toMap(OzonTemplateModel::getId, a -> a, (oldV, newV) -> newV));
                templateModelMap.putAll(collect);
            }

            List<OzonFeedTaskPublishTemplateVO> list = new ArrayList<>(feedTasks.size());
            for (FeedTask feedTask : feedTasks) {
                OzonFeedTaskPublishTemplateVO ozonFeedTaskPublishTemplateVO = new OzonFeedTaskPublishTemplateVO();
                if (StringUtils.isNotBlank(feedTask.getAccountNumber())) {
                    accounts.add(feedTask.getAccountNumber());
                }
                String associationId = feedTask.getAssociationId();
                ozonFeedTaskPublishTemplateVO.setAssociationId(associationId);
                if (StringUtils.isNotBlank(associationId)) {
                    try {
                        Integer templateId = Integer.valueOf(associationId);
                        OzonTemplateModel ozonTemplateModel = templateModelMap.get(templateId);
                        if (ozonTemplateModel != null) {
                            ozonFeedTaskPublishTemplateVO.setTitle(ozonTemplateModel.getTitle());
                            ozonFeedTaskPublishTemplateVO.setDesc(ozonTemplateModel.getDescription());
                        }
                    } catch (Exception e) {
                        // ignore
                    }
                }
                list.add(ozonFeedTaskPublishTemplateVO);
            }

            // Write to Excel
            excelWriter.write(list, writeSheet);
            
            // Update total and check limits
            total += feedTasks.size();
            if (feedTasks.size() < limit || total >= 500000) {
                break;
            }
            offset += limit;
        }
        
        // Update download log with account numbers and count
        if (CollectionUtils.isNotEmpty(accounts)) {
            if (accounts.size() > 50) {
                accounts = accounts.stream().limit(50).collect(Collectors.toSet());
            }
            downloadLog.setAccountNumber(StringUtils.join(accounts, ","));
        }
        downloadLog.setDownloadCount(total);
        
        // Finish writing
        excelWriter.finish();
    }
}