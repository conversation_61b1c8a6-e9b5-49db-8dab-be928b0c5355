package com.estone.erp.publish.ozon.handler.publish;

import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSON;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.ozon.enums.OzonFeedTaskEnums;
import com.estone.erp.publish.ozon.enums.OzonPublishProcessEnums;
import com.estone.erp.publish.ozon.enums.OzonTemplateEnums;
import com.estone.erp.publish.ozon.handler.publish.parm.TemplatePublishParam;
import com.estone.erp.publish.ozon.model.OzonPublishProcess;
import com.estone.erp.publish.ozon.model.OzonTemplateModel;
import com.estone.erp.publish.ozon.model.dto.template.OzonTemplateDO;
import com.estone.erp.publish.ozon.service.OzonTemplateModelService;
import com.estone.erp.publish.ozon.utils.OzonTemplateDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;

/**
 * 模板刊登
 *
 * <AUTHOR>
 * @date 2023-07-12 11:57
 */
@Slf4j
@Component
public class TemplatePublishExecutor extends PublishExecutor<TemplatePublishParam> {

    @Autowired
    private OzonTemplateModelService templateModelService;

    @Override
    protected OzonTemplateModel getTemplateData(TemplatePublishParam param) throws BusinessException {
        DataContextHolder.setUsername(param.getUser());
        Integer templateId = param.getTemplateId();
        OzonTemplateModel ozonTemplateModel = templateModelService.selectByPrimaryKey(templateId);
        try {
            if (OzonTemplateEnums.PublishStatus.PUBLISHING.isTrue(ozonTemplateModel.getPublishStatus())
                    || OzonTemplateEnums.PublishStatus.SUCCESS.isTrue(ozonTemplateModel.getPublishStatus())) {
                // 模板状态异常
                throw new IllegalStateException(String.format("%s,当前模板不能刊登，模板状态为：%s", templateId, OzonTemplateEnums.PublishStatus.getDesc(ozonTemplateModel.getPublishStatus())));
            }
            // 停产、存档、禁售过滤
            templateValidation.filterForbiddenItemStatus(ozonTemplateModel);

            // 重复刊登拦截
            templateValidation.validateRepeatPublish(ozonTemplateModel);
            // 每日刊登限制拦截
            templateValidation.validateDailyPublishLimit(ozonTemplateModel);

            // 侵权词拦截
            OzonTemplateDO ozonTemplateDO = new OzonTemplateDO();
            ozonTemplateDO.setTitle(ozonTemplateModel.getTitle());
            ozonTemplateDO.setDescription(ozonTemplateModel.getDescription());
            templateValidation.validateHasInfringementWord(ozonTemplateDO);
            // 翻译
            OzonTemplateModel updateTemplate = new OzonTemplateModel();
            if (Boolean.TRUE.equals(ozonTemplateModel.getNeedTranslate())) {
                String title = OzonTemplateDataUtil.translate(ozonTemplateModel.getTitle());
                String newTitle = OzonTemplateDataUtil.removeTitleDuplicateWord(title);
                String description = OzonTemplateDataUtil.translate(ozonTemplateModel.getDescription());
                ozonTemplateModel.setTitle(newTitle);
                ozonTemplateModel.setDescription(description);

                updateTemplate.setNeedTranslate(false);
                updateTemplate.setTitle(newTitle);
                updateTemplate.setDescription(description);
            }

            updateTemplate.setId(templateId);
            updateTemplate.setPublishType(OzonTemplateEnums.PublishType.NORMAL.getCode());
            updateTemplate.setPublishRole(OzonTemplateEnums.PublishRole.SALE_MAN.getCode());
            updateTemplate.setPublishStatus(OzonTemplateEnums.PublishStatus.PUBLISHING.getCode());
            updateTemplate.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
            updateTemplate.setVariantData(ozonTemplateModel.getVariantData());
            updateTemplate.setSkuSize(ozonTemplateModel.getSkuSize());

            templateModelService.updateByPrimaryKeySelective(updateTemplate);
            return ozonTemplateModel;
        } catch (Exception e) {
            // 判断是否要处理报告，如果要的话，就不需要把异常丢到外面
            Boolean needFeedTask = param.getNeedFeedTask();
            if (BooleanUtils.isTrue(needFeedTask)) {
                // 这里如果报错了。这里就可以拿到了
                String accountNumber = "";
                String articleNumber = "";
                String sellerSku = "";
                if (ozonTemplateModel != null) {
                    accountNumber = ozonTemplateModel.getAccountNumber();
                    articleNumber = ozonTemplateModel.getArticleNumber();
                    sellerSku = OzonTemplateDataUtil.getFirstSellerSku(ozonTemplateModel);
                }
                failFeedTask(param.getTemplateId(), accountNumber,  articleNumber, e.getMessage(),
                        sellerSku, param.getRuleName(), param.getExcelId(), param.getRowIndex());
                // 注意这里，必须返回null
                return null;
            } else {
                throw new RuntimeException(e);
            }
        } finally {
            try {
                templateValidation.deleteLock(ozonTemplateModel.getAccountNumber(), ozonTemplateModel.getArticleNumber());
            } catch (Exception e) {
                log.error("删除锁失败" + JSON.toJSONString(param));
            }
        }
    }

    @Override
    protected OzonPublishProcess initPublishProcess(TemplatePublishParam param) throws BusinessException {
        OzonPublishProcess publishProcess = new OzonPublishProcess();
        publishProcess.setTemplateId(param.getTemplateId());
        publishProcess.setStatus(OzonPublishProcessEnums.Status.INIT_TEMPLATE.getCode());
        publishProcess.setTaskId(null);
        publishProcess.setIsSuccess(true);
        publishProcess.setPublishType(OzonPublishProcessEnums.PublishType.TEMPLATE.getCode());
        publishProcess.setPublishRole(param.getRole());
        publishProcess.setUser(param.getUser());
        publishProcess.setRemarks(null);
        publishProcess.setTaskType(OzonFeedTaskEnums.TaskType.PUBLISH.name());
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        publishProcess.setCreatedTime(now);
        publishProcess.setUpdatedTime(now);
        publishProcessService.insert(publishProcess);
        return publishProcess;
    }
}
