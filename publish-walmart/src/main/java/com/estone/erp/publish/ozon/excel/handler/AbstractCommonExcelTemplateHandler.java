package com.estone.erp.publish.ozon.excel.handler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.ozon.excel.ImportExcelFileHandler;
import com.estone.erp.publish.ozon.excel.dto.ImportFileDto;
import com.estone.erp.publish.ozon.excel.handler.validator.ValidationResult;
import com.estone.erp.publish.ozon.excel.model.IExcelValidation;
import com.estone.erp.publish.platform.model.SmallPlatformExcelDownloadLog;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * excel公共校验类
 * @param <T>
 */
public abstract class AbstractCommonExcelTemplateHandler<T extends IExcelValidation> {

    @Autowired
    protected PermissionsHelper permissionsHelper;

    /**
     * 获取表头
     *
     * @param clazz 类
     * @return 表头
     */
    public static Set<String> head(Class<?> clazz) {
        HashMap<String, String> objectObjectHashMap = new HashMap<>();
        getExcelFields(clazz.getDeclaredFields(), objectObjectHashMap);
        return new HashSet<>(objectObjectHashMap.values());
    }

    /**
     * 获取属性
     *
     * @param declaredFields 字段
     * @param resultMap      结果映射
     */
    public static void getExcelFields(Field[] declaredFields, Map<String, String> resultMap) {
        for (Field field : declaredFields) {
            field.setAccessible(true);
            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
            if (null != excelProperty) {
                String[] value = excelProperty.value();
                resultMap.put(field.getName(), value[0]);
            }
        }
    }


    /**
     * 准备Excel输入流
     *
     * @param downloadLog 下载日志
     * @return Excel输入流
     */
    protected InputStream prepareExcelInputStream(SmallPlatformExcelDownloadLog downloadLog) {
        String queryCondition = downloadLog.getQueryCondition();
        String username = downloadLog.getCreateBy();
        DataContextHolder.setUsername(username);
        ImportFileDto importFileDto = JSON.parseObject(queryCondition, ImportFileDto.class);

        String url = importFileDto.getUrl();
        if (StringUtils.isBlank(url)) {
            throw new IllegalArgumentException("excel文件不能为空");
        }

        try {
            return ImportExcelFileHandler.downloadFileFromURL(url);
        } catch (IOException e) {
            throw new RuntimeException("excel文件下载失败：" + e.getMessage());
        }
    }

    /**
     * 解析Excel文件
     *
     * @param ins        输入流
     * @param head Excel类
     * @return 解析结果列表
     */
    protected List<T> parseExcelFile(InputStream ins, Set<String> head) {
        List<T> resultList = new ArrayList<>();

        // 解析excel文件
        EasyExcel.read(ins, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                if (context.readRowHolder().getRowIndex() == 0) {
                    validateExcelHeader(data, head);
                } else {
                    T excelRow = createExcelRowObject(data, context.readRowHolder().getRowIndex());
                    resultList.add(checkRequestParam(excelRow));
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                // 这个方法不需要实现，处理逻辑已移至processExcelData方法
            }
        }).headRowNumber(0).sheet().doRead();

        return resultList;
    }

    /**
     * 验证Excel表头
     *
     * @param data 表头数据
     * @param head 预期表头集合
     */
    protected void validateExcelHeader(Map<Integer, String> data, Set<String> head) {
        for (Map.Entry<Integer, String> entry : data.entrySet()) {
            String value = entry.getValue();
            if (!head.contains(value)) {
                throw new RuntimeException("excel文件格式错误, 缺少" + value + "列");
            }
        }

        // 子类需要实现特定的列验证
        validateSpecificColumns(data);
    }
    /**
     * 检查请求参数
     *
     * @param excelRow Excel数据项
     * @return 验证后的Excel数据项
     */
    protected T checkRequestParam(T excelRow) {
        ValidationResult result = excelRow.validate();
        if (!result.isValid()) {
            excelRow.setResult("失败");
            excelRow.setRemark(result.getErrorMessage());
        }
        return excelRow;
    }

    /**
     * 更新下载日志
     *
     * @param downloadLog 下载日志
     * @param resultList  结果列表
     */
    protected void updateDownloadLog(SmallPlatformExcelDownloadLog downloadLog, List<T> resultList) {
        downloadLog.setDownloadCount(resultList.size());
        List<String> accountNumbers = resultList.stream()
                .map(IExcelValidation::getAccountNumber)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .limit(50)
                .collect(Collectors.toList());

        downloadLog.setAccountNumber(String.join(",", accountNumbers));
    }

    /**
     * 检查exel 行是否有重复
     *
     * @param resultList     列
     * @param uniqueFunction 唯一函数
     * @return 过滤后的数据
     */
    public List<T> checkRepeat(List<T> resultList, Function<T, String> uniqueFunction) {
        if (CollectionUtils.isEmpty(resultList)) {
            return resultList;
        }
        Map<String, Integer> existMap = new HashMap<>();
        for (T updateStockExcel : resultList) {
            String uniqueKey = uniqueFunction.apply(updateStockExcel);
            if (existMap.containsKey(uniqueKey)) {
                updateStockExcel.setResult("失败");
                updateStockExcel.setRemark("数据重复：第" + existMap.get(uniqueKey) + "行:" + uniqueKey);
            } else {
                existMap.put(uniqueKey, updateStockExcel.getRowIndex());
            }
        }
        return resultList.stream().filter(a -> StringUtils.isBlank(a.getResult())).collect(Collectors.toList());
    }

    /**
     * 检查权限
     *
     * @param collect  数据
     * @param username 用户名
     * @return 权限数据
     */
    public List<T> checkAuth(List<T> collect, String username) {
        if (CollectionUtils.isEmpty(collect)) {
            return collect;
        }
        List<String> accountNumberList = collect.stream().map(T::getAccountNumber).collect(Collectors.toList());
        ApiResult<List<String>> listApiResult = authIntercept(permissionsHelper, accountNumberList, username);
        if (!listApiResult.isSuccess()) {
            String errorMsg = listApiResult.getErrorMsg();
            for (T updateStockExcel : collect) {
                updateStockExcel.setResult("失败");
                updateStockExcel.setRemark(errorMsg);
            }
            return List.of();
        }
        List<String> authAccountList = listApiResult.getResult();
        for (T updateStockExcel : collect) {
            String accountNumber = updateStockExcel.getAccountNumber();
            if (!authAccountList.contains(accountNumber)) {
                updateStockExcel.setResult("失败");
                updateStockExcel.setRemark("没有权限");
            }
        }
        return collect.stream().filter(a -> StringUtils.isBlank(a.getRemark())).collect(Collectors.toList());
    }

    public static ApiResult<List<String>> authIntercept(PermissionsHelper permissionsHelper, List<String> account, String userName) {
        try {
            if (com.alibaba.nacos.common.utils.CollectionUtils.isEmpty(account) || StringUtils.isBlank(userName)) {
                return ApiResult.newError("店铺或者操作人不能为空");
            }
            // 确保是有人进来的，不然下列方法是没法判断的
            DataContextHolder.setUsername(userName);
            // 判断是否超管
            ApiResult<Boolean> superAdminResult = NewUsermgtUtils.isSuperAdmin();
            if (!superAdminResult.isSuccess()) {
                return ApiResult.newError(superAdminResult.getErrorMsg());
            }
            boolean isSuperAdmin = superAdminResult.getResult();
            if (isSuperAdmin) {
                return ApiResult.newSuccess(account);
            }

            List<String> accountList = permissionsHelper.getCurrentPermissionAccount(SaleChannel.CHANNEL_OZON, false);

            List<String> collect = accountList.stream().filter(account::contains).collect(Collectors.toList());
            return ApiResult.newSuccess(collect);
        } catch (Exception e) {
            return ApiResult.newError("权限查询异常 " + e.getMessage());
        }
    }

    /**
     * 验证特定列
     * 子类需要实现此方法来验证特定的列
     *
     * @param data 表头数据
     */
    protected abstract void validateSpecificColumns(Map<Integer, String> data);

    /**
     * 从Excel行数据创建对象
     * 子类需要实现此方法来创建特定类型的对象
     *
     * @param data     行数据
     * @param rowIndex 行索引
     * @return Excel对象
     */
    protected abstract T createExcelRowObject(Map<Integer, String> data, int rowIndex);


}
