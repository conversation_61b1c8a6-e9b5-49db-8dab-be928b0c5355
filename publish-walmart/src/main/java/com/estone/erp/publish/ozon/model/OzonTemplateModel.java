package com.estone.erp.publish.ozon.model;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

@Data
public class OzonTemplateModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id database column ozon_template_model.id
     */
    private Integer id;

    /**
     * 店铺 database column ozon_template_model.account_number
     */
    private String accountNumber;

    /**
     * 货号 database column ozon_template_model.article_number
     */
    private String articleNumber;

    /**
     * 数据来源 database column ozon_template_model.sku_data_source
     */
    private Integer skuDataSource;

    /**
     * 平台类目Id database column ozon_template_model.category_id
     */
    private Integer categoryId;

    /**
     * 类目路径 database column ozon_template_model.category_id_path
     */
    private String categoryIdPath;

    /**
     * 类目名称路径 database column ozon_template_model.category_name_path
     */
    private String categoryNamePath;

    /**
     * 主图 database column ozon_template_model.main_img
     */
    private String mainImg;

    /**
     * 产品标题 database column ozon_template_model.title
     */
    private String title;

    /**
     * 描述 database column ozon_template_model.description
     */
    private String description;

    /**
     * vat database column ozon_template_model.vat
     */
    private String vat;

    /**
     * 视频地址 database column ozon_template_model.video
     */
    private String video;

    /**
     * 是否为变体 database column ozon_template_model.sale_variant
     */
    private Boolean saleVariant;

    /**
     * 是否需要翻译 database column ozon_template_model.need_translate
     */
    private Boolean needTranslate;

    /**
     * 货币 database column ozon_template_model.currency_code
     */
    private String currencyCode;

    /**
     * 分类属性 database column ozon_template_model.category_attribute
     */
    private String categoryAttribute;

    /**
     * 合并属性 database column ozon_template_model.merge_attribute
     */
    private String mergeAttribute;

    /**
     * SKU数量 database column ozon_template_model.sku_size
     */
    private Integer skuSize;

    /**
     * 变体数据 database column ozon_template_model.variant_data
     */
    private String variantData;

    /**
     * 刊登类型：1自动刊登 2普通刊登 database column ozon_template_model.publish_type
     */
    private Integer publishType;

    /**
     * 刊登角色: 0系统刊登 1销售刊登  database column ozon_template_model.publish_role
     */
    private Integer publishRole;

    /**
     * 刊登详细状态: 1待刊登  2刊登中 3刊登成功 4刊登失败  database column ozon_template_model.publish_status
     */
    private Integer publishStatus;

    /**
     * 库存上传：1未上传库存 2请求中 3上传成功 4上传失败 5待上传库存 database column ozon_template_model.inventory_upload
     */
    private Integer inventoryUpload;

    /**
     * 创建人 database column ozon_template_model.created_by
     */
    private String createdBy;

    /**
     * 修改人 database column ozon_template_model.updated_by
     */
    private String updatedBy;

    /**
     * 创建时间 database column ozon_template_model.created_time
     */
    private Timestamp createdTime;

    /**
     * 修改时间 database column ozon_template_model.updated_time
     */
    private Timestamp updatedTime;

    /**
     * 问题分类 database column ozon_template_model.problem_type
     */
    private String problemType;

    /**
     * 问题分类写入时间 database column ozon_template_model.problem_type_date
     */
    private Date problemTypeDate;
}
