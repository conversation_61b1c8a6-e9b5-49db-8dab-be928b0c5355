package com.estone.erp.publish.ozon.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.ozon.service.OzonReportProblemMaintainService;
import com.estone.erp.publish.tidb.publishtidb.model.OzonReportProblemMaintain;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Ozon问题处理方案本地缓存静态工具类
 */
@Slf4j
public final class OzonCacheReportProblemUtils {
    private static final OzonReportProblemMaintainService ozonReportProblemMaintainService = SpringUtils.getBean(OzonReportProblemMaintainService.class);

    /**
     * 本地缓存实例，缓存时间为30分钟
     * Key: operationType (操作类型)
     * Value: List<OzonReportProblemMaintain> (问题处理方案列表)
     */
    private static final Cache<String, List<OzonReportProblemMaintain>> LOCAL_CACHE =
            CacheBuilder.newBuilder()
                    .expireAfterWrite(30, TimeUnit.MINUTES)
                    .maximumSize(100)
                    .recordStats()
                    .build();

    /**
     * 根据操作类型获取问题处理方案列表（带缓存）
     * 本地缓存半小时，提高查询性能
     *
     * @param operationType 操作类型
     * @return 问题处理方案列表
     */
    public static List<OzonReportProblemMaintain> getReportProblemMaintain(String operationType) {
        // 参数校验
        if (StringUtils.isBlank(operationType)) {
            log.warn("操作类型参数为空，返回空列表");
            return Collections.emptyList();
        }

        try {
            // 尝试从缓存中获取数据
            List<OzonReportProblemMaintain> cachedResult = LOCAL_CACHE.getIfPresent(operationType);
            if (cachedResult != null) {
                log.debug("从缓存中获取到操作类型[{}]的问题处理方案，数量: {}", operationType, cachedResult.size());
                return cachedResult;
            }

            // 缓存中没有数据，从数据库查询
            log.debug("缓存中未找到操作类型[{}]的数据，开始从数据库查询", operationType);

            // 构建查询条件
            LambdaQueryWrapper<OzonReportProblemMaintain> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OzonReportProblemMaintain::getOperationType, operationType);
            queryWrapper.orderByDesc(OzonReportProblemMaintain::getUpdatedTime);

            // 执行数据库查询
            List<OzonReportProblemMaintain> result = ozonReportProblemMaintainService.list(queryWrapper);

            // 将结果放入缓存
            LOCAL_CACHE.put(operationType, result);

            log.info("成功查询并缓存操作类型[{}]的问题处理方案，数量: {}", operationType, result.size());
            return result;
        } catch (Exception e) {
            log.error("查询操作类型[{}]的问题处理方案时发生异常", operationType, e);
            return Collections.emptyList();
        }
    }

    /**
     * 清除指定操作类型的缓存
     *
     * @param operationType 操作类型
     */
    public static void evictCache(String operationType) {
        if (StringUtils.isNotBlank(operationType)) {
            LOCAL_CACHE.invalidate(operationType);
            log.info("已清除操作类型[{}]的缓存", operationType);
        }
    }

    /**
     * 清除所有缓存
     */
    public static void evictAllCache() {
        LOCAL_CACHE.invalidateAll();
        log.info("已清除所有问题处理方案缓存");
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息字符串
     */
    public static String getCacheStats() {
        return String.format("缓存统计 - 大小: %d, 命中率: %.2f%%, 请求次数: %d",
                LOCAL_CACHE.size(),
                LOCAL_CACHE.stats().hitRate() * 100,
                LOCAL_CACHE.stats().requestCount());
    }
}
