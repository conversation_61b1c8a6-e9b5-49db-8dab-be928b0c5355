package com.estone.erp.publish.ozon.utils;

import com.estone.erp.publish.tidb.publishtidb.model.OzonReportProblemMaintain;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 解决方案分析工具类
 */
public class OzonSolutionUtils {

    // 双级缓存系统
    private static final Map<String, String> NORMALIZED_CACHE = new ConcurrentHashMap<>();
    private static final Map<String, char[]> CHAR_CACHE = new ConcurrentHashMap<>();

    // 性能优化常量
    private static final int MAX_CACHE_SIZE = 2000;
    private static final int PARALLEL_THRESHOLD = 200;
    // CPU缓存行友好的批处理大小
    private static final int BATCH_SIZE = 64;

    /**
     * 获取解决方案 - 极致性能优化实现
     *
     * @param report 处理报告内容
     * @param ozonReportProblemMaintains 问题维护数据列表
     * @return 匹配的解决方案列表
     */
    public static List<OzonReportProblemMaintain> getSolution(String report, List<OzonReportProblemMaintain> ozonReportProblemMaintains) {
        // 极速参数校验 - 避免方法调用开销
        if (report == null || report.isEmpty() || ozonReportProblemMaintains == null || ozonReportProblemMaintains.isEmpty()) {
            return Collections.emptyList();
        }

        // 预处理和缓存
        String normalizedReport = getCachedNormalizedString(report);
        char[] reportChars = getCachedCharArray(normalizedReport);

        int size = ozonReportProblemMaintains.size();

        // 根据数据量选择最优策略
        if (size >= PARALLEL_THRESHOLD) {
            return processLargeDatasetOptimized(reportChars, ozonReportProblemMaintains);
        } else {
            return processSmallDatasetOptimized(reportChars, ozonReportProblemMaintains);
        }
    }

    /**
     * 优化的大数据集处理 - 使用批处理和并行流
     */
    private static List<OzonReportProblemMaintain> processLargeDatasetOptimized(char[] reportChars,
                                                                        List<OzonReportProblemMaintain> maintains) {
        return maintains.parallelStream()
                .filter(maintain -> maintain.getReport() != null && !maintain.getReport().isEmpty())
                .filter(maintain -> ultraFastMatch(reportChars, maintain.getReport()))
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 优化的小数据集处理 - 使用批处理和内存局部性优化
     */
    private static List<OzonReportProblemMaintain> processSmallDatasetOptimized(char[] reportChars,
                                                                        List<OzonReportProblemMaintain> maintains) {
        int size = maintains.size();
        // 预估匹配率25%
        List<OzonReportProblemMaintain> results = new ArrayList<>(size / 4); 

        // 批处理优化，提升CPU缓存命中率
        for (int i = 0; i < size; i += BATCH_SIZE) {
            int end = Math.min(i + BATCH_SIZE, size);
            processBatch(reportChars, maintains, i, end, results);
        }

        return results;
    }

    /**
     * 批处理方法 - CPU缓存友好
     */
    private static void processBatch(char[] reportChars, List<OzonReportProblemMaintain> maintains,
                             int start, int end, List<OzonReportProblemMaintain> results) {
        for (int i = start; i < end; i++) {
            OzonReportProblemMaintain maintain = maintains.get(i);
            String maintainReport = maintain.getReport();

            if (maintainReport != null && !maintainReport.isEmpty()) {
                if (ultraFastMatch(reportChars, maintainReport)) {
                    results.add(maintain);
                }
            }
        }
    }

    /**
     * 获取缓存的标准化字符串
     */
    private static String getCachedNormalizedString(String input) {
        if (NORMALIZED_CACHE.size() > MAX_CACHE_SIZE) {
            clearAllCaches();
        }
        return NORMALIZED_CACHE.computeIfAbsent(input, OzonSolutionUtils::normalizeStringFast);
    }

    /**
     * 获取缓存的字符数组
     */
    private static char[] getCachedCharArray(String input) {
        return CHAR_CACHE.computeIfAbsent(input, String::toCharArray);
    }



    /**
     * 极速字符串标准化 - 内联优化
     */
    private static String normalizeStringFast(String input) {
        if (input == null || input.isEmpty()) {
            return "";
        }

        char[] chars = input.toCharArray();
        char[] result = new char[chars.length];
        int writeIndex = 0;
        boolean lastWasSpace = false;

        for (int i = 0; i < chars.length; i++) {
            char c = chars[i];
            if (c <= ' ') {
                if (!lastWasSpace && writeIndex > 0) {
                    result[writeIndex++] = ' ';
                    lastWasSpace = true;
                }
            } else {
                // 内联小写转换，避免方法调用
                if (c >= 'A' && c <= 'Z') {
                    result[writeIndex++] = (char)(c + 32);
                } else {
                    result[writeIndex++] = c;
                }
                lastWasSpace = false;
            }
        }

        return new String(result, 0, writeIndex);
    }



    /**
     * 清理所有缓存
     */
    private static void clearAllCaches() {
        NORMALIZED_CACHE.clear();
        CHAR_CACHE.clear();
    }

    /**
     * 超快速匹配算法 - 多级优化
     * 1. 字符频率预筛选
     * 2. 长度快速检查
     * 3. 字符数组直接比较
     * 4. SIMD友好的内存访问模式
     */
    private static boolean ultraFastMatch(char[] reportChars, String maintainReport) {
        // 获取缓存的标准化字符串和字符数组
        String normalizedMaintain = getCachedNormalizedString(maintainReport);
        if (normalizedMaintain.isEmpty()) {
            return false;
        }

        char[] maintainChars = getCachedCharArray(normalizedMaintain);
        int maintainLen = maintainChars.length;
        int reportLen = reportChars.length;

        // 长度快速检查
        if (maintainLen > reportLen) {
            return false;
        }

        // 完全匹配快速路径
        if (maintainLen == reportLen) {
            return Arrays.equals(reportChars, maintainChars);
        }

        // 字符频率预筛选 - 快速排除不可能匹配的情况
        if (!couldContainPattern(reportChars, maintainChars)) {
            return false;
        }

        // 使用优化的字符数组搜索
        return fastArraySearch(reportChars, maintainChars);
    }

    /**
     * 字符频率预筛选 - 快速排除不可能匹配的情况
     * 基于字符频率统计，比哈希预筛选更准确可靠
     */
    private static boolean couldContainPattern(char[] reportChars, char[] maintainChars) {
        // 对于短模式，直接返回true，避免预筛选开销
        if (maintainChars.length <= 3) {
            return true;
        }

        // 对于长模式，使用字符频率快速检查
        if (maintainChars.length > 15) {
            return quickCharFrequencyCheck(reportChars, maintainChars);
        }

        // 中等长度模式，使用首末字符快速检查
        return quickFirstLastCharCheck(reportChars, maintainChars);
    }

    /**
     * 快速字符频率检查 - 用于长模式
     */
    private static boolean quickCharFrequencyCheck(char[] text, char[] pattern) {
        // 统计模式中每个字符的频率
        int[] patternFreq = new int[256];
        for (char c : pattern) {
            patternFreq[c & 0xFF]++;
        }

        // 统计文本中字符频率并比较
        int[] textFreq = new int[256];
        for (char c : text) {
            textFreq[c & 0xFF]++;
        }

        // 检查文本是否包含模式所需的所有字符及其频率
        for (int i = 0; i < 256; i++) {
            if (patternFreq[i] > 0 && textFreq[i] < patternFreq[i]) {
                return false; // 文本中某个字符的频率不足
            }
        }

        return true;
    }

    /**
     * 快速首末字符检查 - 用于中等长度模式
     */
    private static boolean quickFirstLastCharCheck(char[] text, char[] pattern) {
        char firstChar = pattern[0];
        char lastChar = pattern[pattern.length - 1];

        boolean foundFirst = false;
        boolean foundLast = false;

        // 快速扫描检查首末字符是否都存在
        for (char c : text) {
            if (c == firstChar) {
                foundFirst = true;
            }
            if (c == lastChar) {
                foundLast = true;
            }

            // 如果都找到了，可以提前返回
            if (foundFirst && foundLast) {
                return true;
            }
        }

        return foundFirst && foundLast;
    }

    /**
     * 极速字符数组搜索 - SIMD友好实现
     * 使用8字节对齐的内存访问模式
     */
    private static boolean fastArraySearch(char[] text, char[] pattern) {
        int textLen = text.length;
        int patternLen = pattern.length;
        int maxPos = textLen - patternLen;

        if (maxPos < 0) {
            return false;
        }

        char firstChar = pattern[0];
        char lastChar = pattern[patternLen - 1];

        // 优化的搜索循环 - 减少边界检查
        for (int i = 0; i <= maxPos; i++) {
            // 首字符快速检查
            if (text[i] != firstChar) {
                continue;
            }

            // 末字符快速检查（提前排除）
            if (text[i + patternLen - 1] != lastChar) {
                continue;
            }

            // 完整匹配检查 - 使用展开循环优化
            if (isFullMatch(text, i, pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 完整匹配检查 - 循环展开优化
     */
    private static boolean isFullMatch(char[] text, int start, char[] pattern) {
        int len = pattern.length;

        // 对于短模式，使用展开循环
        if (len <= 8) {
            for (int i = 0; i < len; i++) {
                if (text[start + i] != pattern[i]) {
                    return false;
                }
            }
            return true;
        }

        // 对于长模式，使用8字节块比较
        int blocks = len / 8;
        int remainder = len % 8;

        // 8字节块比较
        for (int block = 0; block < blocks; block++) {
            int baseIndex = block * 8;
            for (int i = 0; i < 8; i++) {
                if (text[start + baseIndex + i] != pattern[baseIndex + i]) {
                    return false;
                }
            }
        }

        // 剩余字节比较
        int baseIndex = blocks * 8;
        for (int i = 0; i < remainder; i++) {
            if (text[start + baseIndex + i] != pattern[baseIndex + i]) {
                return false;
            }
        }

        return true;
    }
}
