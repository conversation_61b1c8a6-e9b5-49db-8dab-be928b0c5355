package com.estone.erp.publish.walmart.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.model.api.CQueryWithOtherResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.PagingUtils;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.executors.WalmartExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.model.dto.ResultListDTO;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.elasticsearch.model.EsOrderSalesStatisticsTime;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.service.EsOrderSalesStatisticsTimeService;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.elasticsearch.util.CheckOrderSalesTimeUtils;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.WalmartTaskTypeEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.constant.ErpUsermgtNRedisConStant;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SpuOfficial;
import com.estone.erp.publish.system.product.bean.StockObj;
import com.estone.erp.publish.walmart.call.inventory.WalmartBulkUpdateInventoryCall;
import com.estone.erp.publish.walmart.call.inventory.WalmartGetInventoryCall;
import com.estone.erp.publish.walmart.call.items.WallmartItemDeleteCall;
import com.estone.erp.publish.walmart.call.items.WalmartBulkItemCall;
import com.estone.erp.publish.walmart.call.items.WalmartGetItemsCall;
import com.estone.erp.publish.walmart.call.items.WalmartSearchItemCall;
import com.estone.erp.publish.walmart.call.prices.WalmartBulkUpdatePricesCall;
import com.estone.erp.publish.walmart.call.reports.WalmartReportCall;
import com.estone.erp.publish.walmart.constant.WalmartApiErrorConstant;
import com.estone.erp.publish.walmart.constant.WalmartPublishConstant;
import com.estone.erp.publish.walmart.enums.*;
import com.estone.erp.publish.walmart.mapper.WalmartItemMapper;
import com.estone.erp.publish.walmart.mapper.custom.CustomWalmartItemMapper;
import com.estone.erp.publish.walmart.model.*;
import com.estone.erp.publish.walmart.model.dto.*;
import com.estone.erp.publish.walmart.model.vo.WalmartReplaceItemVO;
import com.estone.erp.publish.walmart.model.vo.WalmartSyncItemVO;
import com.estone.erp.publish.walmart.mq.bean.FeedMessage;
import com.estone.erp.publish.walmart.mq.sender.WalmartExcelDownloadMqSender;
import com.estone.erp.publish.walmart.service.*;
import com.estone.erp.publish.walmart.util.*;
import com.google.common.collect.Lists;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> walmart_item
 * 2021-03-04 10:32:52
 */
@Service("walmartItemService")
@Slf4j
public class WalmartItemServiceImpl implements WalmartItemService {
    @Resource
    private WalmartItemMapper walmartItemMapper;

    @Resource
    private CustomWalmartItemMapper customWalmartItemMapper;

    @Resource
    private FeedTaskService feedTaskService;

    @Resource
    private WalmartReplaceItemService walmartReplaceItemService;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private WalmartModifyLogService walmartModifyLogService;

    @Resource
    private WalmartTemporaryLogService walmartTemporaryLogService;

    @Resource
    private WalmartReportRecordService walmartReportRecordService;

    @Resource
    private SaleAccountService saleAccountService;

    @Resource
    private EsOrderSalesStatisticsTimeService esOrderSalesStatisticsTimeService;

    @Resource
    private WalmartExcelDownloadLogService walmartExcelDownloadLogService;

    @Resource
    private WalmartExcelDownloadMqSender walmartExcelDownloadMqSender;

    @Resource
    private PermissionsHelper permissionsHelper;

    @Resource
    private SystemParamService systemParamService;

    @Resource
    private WalmartAccountConfigService walmartAccountConfigService;
    @Override
    public int countByExample(WalmartItemExample example) {
        Assert.notNull(example, "example is null!");
        return walmartItemMapper.countByExample(example);
    }

    @Override
    public CQueryResult<WalmartItem> search(CQuery<WalmartItemCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        WalmartItemCriteria query = cquery.getSearch();
        Assert.notNull(query, "query is null!");

        try {
            // 权限控制
            handleAuth(query);

            // 查询账号状态
            handleAccountStatus(query);
        } catch (Exception e) {
            CQueryResult<WalmartItem> cQueryResult = new CQueryResult<>();
            return cQueryResult.failResult(e.getMessage());
        }

        WalmartItemExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = walmartItemMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        // 排序
        if (StringUtils.isNotBlank(cquery.getSort())) {
            example.setOrderByClause(cquery.getSort() + " " + cquery.getOrder());
        }
        List<WalmartItem> walmartItems = walmartItemMapper.selectByExample(example);

        // 扩展信息
        Map<String, Object> otherMap = handelPageExtend(walmartItems, query);

        // 组装结果
        CQueryWithOtherResult<WalmartItem> result = new CQueryWithOtherResult<>();
        result.setOther(otherMap);
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(walmartItems);
        return result;
    }

    private Map<String, Object> handelPageExtend(List<WalmartItem> walmartItems, WalmartItemCriteria query) {
        if (CollectionUtils.isEmpty(walmartItems)) {
            return Collections.emptyMap();
        }

        Map<String, Object> otherMap = new HashMap<>();
        if (StringUtils.isNotBlank(query.getAccountStatus())) {
            for (WalmartItem walmartItem : walmartItems) {
                otherMap.put(walmartItem.getId().toString(), query.getAccountStatus());
            }
        } else {
            List<String> accountNumberList = walmartItems.stream().map(WalmartItem::getAccountNumber).distinct().collect(Collectors.toList());
            String[] withFields = {"accountNumber","accountStatus"};
            ApiResult<List<SaleAccount>> saleAccountResult = EsAccountUtils.getSaleAccountsByAccounts(accountNumberList, SaleChannel.CHANNEL_WALMART, withFields);
            if (!saleAccountResult.isSuccess()) {
                throw new RuntimeException(saleAccountResult.getErrorMsg());
            }
            List<SaleAccount> saleAccounts = saleAccountResult.getResult();
            Map<String, SaleAccount> saleAccountMap = saleAccounts.stream().collect(Collectors.toMap(SaleAccount::getAccountNumber, o -> o, (o1, o2) -> o1));
            for (WalmartItem walmartItem : walmartItems) {
                SaleAccount saleAccount = saleAccountMap.get(walmartItem.getAccountNumber());
                otherMap.put(walmartItem.getId().toString(), saleAccount.getAccountStatus());
            }
        }

        List<String> accountNumberList = walmartItems.stream().map(WalmartItem::getAccountNumber).distinct().collect(Collectors.toList());
        Map<String, Triple<String, String, String>> saleSuperiorMap = NewUsermgtUtils.getSaleSuperiorMap(accountNumberList, SaleChannel.CHANNEL_WALMART);
        for (WalmartItem walmartItem : walmartItems) {
            if (MapUtils.isNotEmpty(saleSuperiorMap)) {
                Triple<String, String, String> stringStringStringTriple = saleSuperiorMap.get(walmartItem.getAccountNumber());
                if (null != stringStringStringTriple) {
                    String salemanager = stringStringStringTriple.getLeft();
                    walmartItem.setSaleId(salemanager);
                    walmartItem.setLeaderId(stringStringStringTriple.getMiddle());
                    walmartItem.setManagerId(stringStringStringTriple.getRight());
                }
            }
        }
        return otherMap;
    }

    @Override
    public void handleAccountStatus(WalmartItemCriteria query) {
        String accountStatus = query.getAccountStatus();
        if (StringUtils.isBlank(accountStatus)) {
            return;
        }

        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(SaleChannel.CHANNEL_WALMART);
        request.setAccountStatusList(Lists.newArrayList(accountStatus));
        List<String> accountNumberList = saleAccountService.getAccountList(request);
        if (CollectionUtils.isEmpty(accountNumberList)) {
            throw new BusinessException("没有该状态的店铺");
        }

        List<String> searchAccountList = query.getAccountNumberList();
        if (CollectionUtils.isEmpty(searchAccountList)) {
            query.setAccountNumberList(accountNumberList);
            return;
        }

        // 两个集合取交集
        accountNumberList.retainAll(searchAccountList);
        if (CollectionUtils.isEmpty(accountNumberList)) {
            throw new BusinessException("没有该状态的店铺");
        }

        query.setAccountNumberList(accountNumberList);
    }

    @Override
    public void handleAuth(WalmartItemCriteria query) {
        List<String> currentUserPermission = permissionsHelper.getCurrentUserPermission(query.getAccountNumberList(), query.getManagerIds(), query.getLeaderIds(), query.getSaleIds(), SaleChannel.CHANNEL_WALMART);
        query.setAccountNumberList(currentUserPermission);
        }


    @Override
    public WalmartItem selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return walmartItemMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<WalmartItem> selectByExample(WalmartItemExample example) {
        Assert.notNull(example, "example is null!");
        return walmartItemMapper.selectByExample(example);
    }

    @Override
    public List<WalmartItem> selectFiledColumnsByExample(WalmartItemExample example) {
        Assert.notNull(example, "example is null!");
        return walmartItemMapper.selectFiledColumnsByExample(example);
    }

    @Override
    public int insert(WalmartItem record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return walmartItemMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(WalmartItem record) {
        Assert.notNull(record, "record is null!");
        record.setUpdateDate(new Timestamp(System.currentTimeMillis()));
        // 默认加修改时间和修改人
        return walmartItemMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateProductInfoByPrimaryKey(WalmartItem record) {
        Assert.notNull(record, "record is null!");
        record.setUpdateDate(new Timestamp(System.currentTimeMillis()));
        // 默认加修改时间和修改人
        return walmartItemMapper.updateProductInfoByPrimaryKey(record);
    }

    @Override
    public int updateInfringementWordByPrimaryKey(WalmartItem record) {
        return walmartItemMapper.updateInfringementWordByPrimaryKey(record);
    }

    @Override
    public int updateByExampleSelective(WalmartItem record, WalmartItemExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return walmartItemMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return walmartItemMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public void batchInsert(List<WalmartItem> walmartItems) {
        if(CollectionUtils.isEmpty(walmartItems)) {
            return;
        }

        try {
            for (WalmartItem walmartItem :walmartItems) {
                walmartItem.setCreateDate(new Timestamp(System.currentTimeMillis()));
                walmartItem.setUpdateDate(new Timestamp(System.currentTimeMillis()));
            }

            customWalmartItemMapper.batchInsert(walmartItems);
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        }
    }

    @Override
    public void batchUpdateByUnique(List<WalmartItem> walmartItems) {
        if(CollectionUtils.isEmpty(walmartItems)) {
            return;
        }

        try {
            customWalmartItemMapper.batchUpdateByUnique(walmartItems);
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        }
    }

    @Override
    public void batchUpdate(List<WalmartItem> walmartItems) {
        if(CollectionUtils.isEmpty(walmartItems)) {
            return;
        }

        for (WalmartItem walmartItem :walmartItems) {
            walmartItem.setUpdateDate(new Timestamp(System.currentTimeMillis()));
        }

        try {
            // 有些字段没有判空
            customWalmartItemMapper.batchUpdate(walmartItems);
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 保存同账号的item
     * @param walmartItems
     */
    @Override
    public void batchSave(List<WalmartItem> walmartItems) {
        if(CollectionUtils.isEmpty(walmartItems)) {
            return;
        }

        // 计算利率
        WalmartCalcPriceUtil.batchCalcWalmartItemProfitRate(walmartItems);

        // 查询已经存在数据库的
        String accountNumber = walmartItems.get(0).getAccountNumber();
        List<String> itemIds = walmartItems.stream().map(o->o.getItemId()).collect(Collectors.toList());

        List<WalmartItem> updateWalmartItems = new ArrayList<>();
        List<WalmartItem> createWalmartItems = new ArrayList<>();
        Map<String, WalmartItem> dbItemMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(itemIds)) {
            WalmartItemExample example = new WalmartItemExample();
            example.createCriteria()
                    .andAccountNumberEqualTo(accountNumber)
                    .andItemIdIn(itemIds);
            List<WalmartItem> dbWalmartItems = selectByExample(example);
            dbItemMap = dbWalmartItems.stream()
                    .collect(Collectors.toMap(o->(o.getItemId()  + "&" + o.getGtin()), o->o, (k1,k2)->k1));
        }

        for (WalmartItem walmartItem : walmartItems) {
            String key = walmartItem.getItemId() +  "&" + walmartItem.getGtin();
            walmartItem.setSyncDate(new Timestamp(System.currentTimeMillis()));

            WalmartItem dbWalmartItem = dbItemMap.get(key);
            if(null != dbWalmartItem) {
                // 处理废弃状态sku合并
                WalmartItemLocalUtils.handleMergeSku(walmartItem, dbWalmartItem);
                updateWalmartItems.add(walmartItem);
            } else {
                createWalmartItems.add(walmartItem);
            }
        }

        // 已存在则根据itemId修改
        try{
            batchUpdateByUnique(updateWalmartItems);
        } catch (Exception e) {
            log.error("WalmartItem同步后批量修改报错");
        }

        try{
            // 不存在则创建 且需要设置SKU状态等产品信息
            WalmartItemLocalUtils.setItemsSkuStatus(createWalmartItems);
            batchInsert(createWalmartItems);
        } catch (Exception e) {
            log.error("WalmartItem同步后批量插入报错");
        }
    }

    @Override
    public void syncByAccountNumber(SaleAccountAndBusinessResponse walmartAccount, String sellerSku, String status) {
        String accountNumber = walmartAccount.getAccountNumber();
        Date startDate = new Date();
        log.warn("********************开始同步Walmart在线产品  accountNumber:[" + accountNumber + "] startDate:[" + startDate + "]********************");

        // 初始化处理报告
        FeedTask feedTask = WalmartFeedTaskUtil.initFeedTask(accountNumber, WalmartTaskTypeEnum.SYNC_ITEM.getStatusMsgEn(), sellerSku, null, null);
        List<String> msgList = new ArrayList<>();

        String limitStr = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_WALMART, "WALMART", "LISTING_PAGING_TIMES", 5);
        Assert.notNull(limitStr, "limitStr is null!");
        Integer limit = 1000;
        String nextCursor = "*";
        try {
            while (true) {
                // 刷新令牌
                WalmartAccountUtils.refreshAccountToken(walmartAccount);

                ResultListDTO<WalmartItem> resultListDTO = new WalmartGetItemsCall(walmartAccount).execute(limit, sellerSku, nextCursor, status);

                if (null == resultListDTO) {
                    break;
                }

                try {
                    batchSave(resultListDTO.getList());
                } catch (Exception e) {
                    if (msgList.size() <= 5) {
                        msgList.add("本地保存时报错：" + e.getMessage());
                    }
                    log.error("WalmartItem同步后保存报错：" + e.getMessage());
                }

                if ( StringUtils.isBlank(resultListDTO.getNextCursor())) {
                    log.error("********************nextCursor为空********************");
                    break;
                }
                nextCursor = resultListDTO.getNextCursor();
            }
        } catch (Exception e) {
            WalmartFeedTaskUtil.failFeedTask(feedTask, e.getMessage());
            log.error(String.format("账号%s同步产品报错：%s", accountNumber, e.getMessage()));
            return;
        }

        // 更新处理报告
        if (CollectionUtils.isNotEmpty(msgList)) {
            WalmartFeedTaskUtil.failFeedTask(feedTask, StringUtils.join(msgList, ","));
        } else {
            WalmartFeedTaskUtil.succeedFeedTask(feedTask, null, null);
        }

        Date endDate = new Date();
        log.warn("********************Walmart在线产品同步完成  accountNumber:[" + accountNumber + "] startDate:[" + startDate + "] endDate:[ " + endDate + " ]********************");
    }

    @Override
    public void syncOffsetItemByAccountNumber(SaleAccountAndBusinessResponse walmartAccount, String sellerSku) {
        Date startDate = new Date();
        log.warn("********************开始同步Walmart在线产品  accountNumber:[" + walmartAccount.getAccountNumber() + "] startDate:[" + startDate + "]********************");

        Integer limit = 200;
        Integer offset = 0;
        while (true) {
            // 刷新令牌
            WalmartAccountUtils.refreshAccountToken(walmartAccount);
            List<WalmartItem> walmartItems = new WalmartGetItemsCall(walmartAccount).execute(limit, offset, sellerSku);
            if(CollectionUtils.isEmpty(walmartItems)) {
                break;
            }

            try{
                batchSave(walmartItems);
            } catch (Exception e) {
                log.error("WalmartItem同步后保存报错");
            }

            offset += limit;
        }

        Date endDate = new Date();
        log.warn("********************Walmart在线产品同步完成  accountNumber:[" + walmartAccount.getAccountNumber() + "] startDate:[" + startDate + "] endDate:[ " + endDate + " ]********************");
    }

    @Override
    public void syncItemByReport(SaleAccountAndBusinessResponse account) {
        if (null == account) {
            return;
        }

        // 初始化处理报告
        FeedTask feedTask = WalmartFeedTaskUtil.initFeedTask(account.getAccountNumber(), WalmartTaskTypeEnum.SYNC_ITEM.getStatusMsgEn(), null, null, null);

        // 初始化报告记录
        WalmartReportRecord walmartReportRecord = new WalmartReportRecord();
        walmartReportRecord.setAccountNumber(account.getAccountNumber());
        walmartReportRecord.setReportType(WalmartReportTypeEnum.ITEM.getCode());
        walmartReportRecord.setCreateDate(new Timestamp(System.currentTimeMillis()));

        try {
            // 创建报告
            WalmartAccountUtils.refreshAccountToken(account);
            String rowFilters = WalmartItemLocalUtils.toDefaultRowFilters();
            String requestId = new WalmartReportCall(account).createReport(WalmartReportTypeEnum.ITEM.getCode(), rowFilters, null);
            walmartReportRecord.setRequestId(requestId);
            walmartReportRecord.setReportStatus(WalmartReportStatusEnum.RECEIVED.getCode());
            walmartReportRecordService.insert(walmartReportRecord);

            // 处理报告添加请求id
            feedTask.setAssociationId(requestId);
            feedTaskService.updateByPrimaryKeySelective(feedTask);
        } catch (Exception e) {
            walmartReportRecord.setReportStatus(WalmartReportStatusEnum.ERROR.getCode());
            walmartReportRecord.setEndDate(new Timestamp(System.currentTimeMillis()));
            walmartReportRecord.setMessage(e.getMessage());
            walmartReportRecordService.insert(walmartReportRecord);

            WalmartFeedTaskUtil.failFeedTask(feedTask, e.getMessage());
        }
    }

    @Override
    public void syncItemByItemId(WalmartSyncItemVO walmartSyncItemVO) {
        String accountNumber = walmartSyncItemVO.getAccountNumber();
        List<String> itemIdList = walmartSyncItemVO.getItemIdList();
        if (StringUtils.isBlank(accountNumber) || CollectionUtils.isEmpty(itemIdList)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        // 获取账号
        SaleAccountAndBusinessResponse walmartAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, accountNumber);
        if (null == walmartAccount) {
            throw new BusinessException("查询不到账号信息");
        }

        WalmartItemExample example = new WalmartItemExample();
        String filed = "id, seller_sku";
        example.setFiledColumns(filed);
        example.createCriteria().andAccountNumberEqualTo(accountNumber).andItemIdIn(itemIdList);
        List<WalmartItem> walmartItemList = this.selectFiledColumnsByExample(example);
        if (CollectionUtils.isEmpty(walmartItemList)) {
            throw new BusinessException("查询不到在线列表信息");
        }

        for (WalmartItem walmartItem : walmartItemList) {
            WalmartExecutors.syncItem(() -> syncByAccountNumber(walmartAccount, walmartItem.getSellerSku(), null));
        }
    }

    @Override
    public String syncInventory(List<WalmartItem> walmartItems, SaleAccountAndBusinessResponse walmartAccount) {
        if (CollectionUtils.isEmpty(walmartItems)) {
            return "参数为空";
        }

        String error = null;
        for (WalmartItem walmartItem :walmartItems) {
            try {
                // 刷新令牌
                WalmartAccountUtils.refreshAccountToken(walmartAccount);

                Integer inventory = new WalmartGetInventoryCall(walmartAccount).execute(walmartItem.getSellerSku());

                // 获取到库存后修改数据库
                WalmartItem newWalmartItem = new WalmartItem();
                newWalmartItem.setId(walmartItem.getId());
                newWalmartItem.setInventory(inventory);
                newWalmartItem.setSyncInventoryDate(new Timestamp(System.currentTimeMillis()));
                newWalmartItem.setUpdateDate(new Timestamp(System.currentTimeMillis()));
                walmartItemMapper.updateByPrimaryKeySelective(newWalmartItem);
            } catch (Exception e) {
                log.warn("==============>同步库存SellerSku: [" + walmartItem.getSellerSku() + "] 失败 ] " + e.getMessage());
                error = e.getMessage();
            }
        }

        return error;
    }

    @Override
    public String syncInverntoryExecutors(List<WalmartItem> walmartItems, SaleAccountAndBusinessResponse walmartAccount) {
        if (CollectionUtils.isEmpty(walmartItems)) {
            return "参数为空";
        }
        String accountNumber = walmartAccount.getAccountNumber();

        //返回结果
        Map<String, Future<ResponseJson>> itemFutureMap = new HashMap<>();

        for (WalmartItem walmartItem :walmartItems) {
            Future<ResponseJson> sysItemInventory = WalmartExecutors.syncItemInventory((rsp) -> {
                try {
                    // 刷新令牌
                    WalmartAccountUtils.refreshAccountToken(walmartAccount);

                    Integer inventory = new WalmartGetInventoryCall(walmartAccount).execute(walmartItem.getSellerSku());

                    // 获取到库存后修改数据库
                    WalmartItem newWalmartItem = new WalmartItem();
                    newWalmartItem.setId(walmartItem.getId());
                    newWalmartItem.setInventory(inventory);
                    newWalmartItem.setSyncInventoryDate(new Timestamp(System.currentTimeMillis()));
                    newWalmartItem.setUpdateDate(new Timestamp(System.currentTimeMillis()));
                    walmartItemMapper.updateByPrimaryKeySelective(newWalmartItem);
                } catch (Exception e) {
                    rsp.setMessage(e.getMessage());
                    rsp.setStatus(StatusCode.FAIL);
                }
            });

            itemFutureMap.put(walmartItem.getSellerSku(), sysItemInventory);
        }

        StringBuilder error = new StringBuilder();
        for (Map.Entry<String, Future<ResponseJson>> entry : itemFutureMap.entrySet()) {
            String futureK = entry.getKey();
            Future<ResponseJson> futureV = entry.getValue();
            try {
                ResponseJson rsp = futureV.get(1, TimeUnit.MINUTES);
                if (StringUtils.equals(rsp.getStatus(), StatusCode.FAIL)) {
                    log.warn(accountNumber + " 账号==============>同步库存SellerSku: [" + futureK + "] 失败 ] " + rsp.getMessage());
                    error.append(rsp.getMessage());
                }
            }
            catch (Exception e) {
                log.warn(accountNumber + " 账号*****************同步库存 异常SellerSku:" + futureK + e.getMessage());
                error.append(e.getMessage());
            }
        }

        return error.toString();
    }

    @Override
    public String batchUpdateInventory(List<WalmartItem> updateInventoryWalmartItems) {
        if(CollectionUtils.isEmpty(updateInventoryWalmartItems)) {
            return "数据不可以为空！";
        }

        List<String> errors = new ArrayList<>();
        Map<String, List<WalmartItem>> accountItemsMap = updateInventoryWalmartItems.stream().collect(Collectors.groupingBy(o->o.getAccountNumber()));
        accountItemsMap.forEach((account, walmartItems) ->{
            if(StringUtils.isBlank(account)) {
                errors.add("存在账号为空数据！");
                return;
            }
            SaleAccountAndBusinessResponse walmartAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, account);
            if(null == walmartAccount) {
                errors.add(account + " 获取账号失败！");
                return;
            }

            String error = batchUpdateInventory(walmartItems, walmartAccount, null);
            if(StringUtils.isNotBlank(error)) {
                errors.add(error);
            }
        });

        if(CollectionUtils.isNotEmpty(errors)) {
            return errors.toString();
        } else {
            return null;
        }
    }

    @Override
    public String batchUpdateInventory(List<WalmartItem> walmartItems, SaleAccountAndBusinessResponse walmartAccount, Map<Long, StockObj> stockObjMap) {
        if(CollectionUtils.isEmpty(walmartItems)) {
            return "数据不可以为空！";
        }
        if(null == walmartAccount) {
            return " 获取账号失败！";
        }

        if (stockObjMap == null) {
            stockObjMap = new HashMap<>();
        }

        // 查询旧的库存值
        List<Long> idList = walmartItems.stream().map(WalmartItem::getId).collect(Collectors.toList());
        WalmartItemExample example = new WalmartItemExample();
        example.createCriteria().andIdIn(idList);
        example.setFiledColumns("id,inventory");
        List<WalmartItem> items = selectFiledColumnsByExample(example);
        Map<Long, Integer> oldValueMap = items.stream()
                .collect(HashMap::new, (map, item) -> map.put(item.getId(), item.getInventory()), HashMap::putAll);

        // 改前改后库存相同 不做修改
        Iterator<WalmartItem> it = walmartItems.iterator();
        while (it.hasNext()) {
            WalmartItem walmartItem = it.next();
            Integer newValue = walmartItem.getInventory();
            Integer oldValue = oldValueMap.get(walmartItem.getId());
            if (ObjectUtils.nullSafeEquals(oldValue, newValue)) {
                it.remove();
            }
        }
        if (CollectionUtils.isEmpty(walmartItems)) {
            return "改前改后库存相同！";
        }

        // 初始化处理报告
        Map<Long, FeedTask> feedTaskMap = new HashMap<>();
        for (WalmartItem walmartItem : walmartItems) {
            FeedTask feedTask = WalmartFeedTaskUtil.initFeedTaskStock(walmartAccount.getAccountNumber(),
                    WalmartTaskTypeEnum.UPDATE_INVENTORY.getStatusMsgEn(), walmartItem.getSellerSku(),
                    walmartItem.getItemId(), walmartItem.getSku(), stockObjMap.get(walmartItem.getId()));
            feedTaskMap.put(walmartItem.getId(), feedTask);
        }

        String error = null;

        List<List<WalmartItem>> lists = PagingUtils.newPagingList(walmartItems, 3000);
        for (List<WalmartItem> walmartItemList : lists) {
            try {
                // 获取令牌 修改库存
                WalmartAccountUtils.refreshAccountToken(walmartAccount);
                String feedId = new WalmartBulkUpdateInventoryCall(walmartAccount).execute(walmartItemList);
                log.info("批量修改库存请求平台成功feedId:" + feedId);
            } catch (Exception e) {
                log.error("批量修改库存请求平台失败！" + e.getMessage());
                error = e.getMessage();
            }

            // 修改本地数据
            if (StringUtils.isBlank(error)) {
                List<WalmartItem> batchUpdateInventoryItem = new ArrayList<>();
                for (WalmartItem walmartItem : walmartItemList) {
                    // 获取到库存后修改数据库
                    WalmartItem newWalmartItem = new WalmartItem();
                    batchUpdateInventoryItem.add(newWalmartItem);
                    newWalmartItem.setId(walmartItem.getId());
                    newWalmartItem.setInventory(walmartItem.getInventory());
                    newWalmartItem.setUpdateDate(new Timestamp(System.currentTimeMillis()));
                }

                try {
                    customWalmartItemMapper.batchUpdateInventory(batchUpdateInventoryItem);
                } catch (Exception e) {
                    log.error("请求平台成功修改本地库失败！" + e.getMessage());
                    error = e.getMessage();
                }
            }

            // 更新处理报告
            if (StringUtils.isNotBlank(error)) {
                // 失败
                for (WalmartItem walmartItem : walmartItemList) {
                    FeedTask feedTask = feedTaskMap.get(walmartItem.getId());
                    Integer oldValue = oldValueMap.get(walmartItem.getId());
                    Integer newValue = walmartItem.getInventory();
                    feedTask.setAttribute1(oldValue != null ? oldValue.toString() : null);
                    feedTask.setAttribute2(newValue != null ? newValue.toString() : null);
                    WalmartFeedTaskUtil.failFeedTask(feedTask, error);
                }
            } else {
                // 成功处理报告 记录改前改后值
                for (WalmartItem walmartItem : walmartItemList) {
                    FeedTask feedTask = feedTaskMap.get(walmartItem.getId());
                    Integer oldValue = oldValueMap.get(walmartItem.getId());
                    Integer newValue = walmartItem.getInventory();
                    WalmartFeedTaskUtil.succeedFeedTask(feedTask, String.valueOf(oldValue), String.valueOf(newValue));
                }
            }
        }

        return null;
    }

    @Override
    public String batchUpdatePrice(List<WalmartItem> updatePriceWalmartItems) {
        if (CollectionUtils.isEmpty(updatePriceWalmartItems)) {
            return "数据不可以为空！";
        }

        // 查询旧的价格
        List<Long> idList = updatePriceWalmartItems.stream().map(WalmartItem::getId).collect(Collectors.toList());
        WalmartItemExample example = new WalmartItemExample();
        example.createCriteria().andIdIn(idList);
        example.setFiledColumns("id,price");
        List<WalmartItem> items = selectFiledColumnsByExample(example);
        Map<Long, Double> oldValueMap = items.stream().collect(Collectors.toMap(WalmartItem::getId, WalmartItem::getPrice));

        Map<String, List<WalmartItem>> accountItemsMap = updatePriceWalmartItems.stream()
                .filter(o -> null != o.getAccountNumber()).collect(Collectors.groupingBy(WalmartItem::getAccountNumber));

        accountItemsMap.forEach((account, walmartItems) -> submitUpdatePrice(walmartItems, oldValueMap, null, null));
        return null;
    }

    private void submitUpdatePrice(List<WalmartItem> walmartItems, Map<Long, Double> oldValueMap, List<WalmartModifyLog> updateModifyLogs, String type) {
        if (CollectionUtils.isEmpty(walmartItems)) {
            return;
        }

        String account = walmartItems.get(0).getAccountNumber();
        // 初始化处理报告
        Map<String, FeedTask> feedTaskMap = new HashMap<>();

        walmartItems = walmartItems.stream().filter(walmartItem -> Optional.ofNullable(walmartItem.getPrice()).orElse(0.0) > 0).collect(Collectors.toList());
        for (WalmartItem walmartItem : walmartItems) {
            FeedTask feedTask = WalmartFeedTaskUtil.initFeedTask(account, WalmartTaskTypeEnum.UPDATE_PRICE.getStatusMsgEn(),
                    walmartItem.getSellerSku(), walmartItem.getItemId(), walmartItem.getSku());
            feedTaskMap.put(walmartItem.getItemId(), feedTask);
        }

        // 执行
        String error = executeBatchUpdatePrice(account, walmartItems, false);

        // 更新处理报告
        if(StringUtils.isNotBlank(error)) {
            // 失败
            if (StringUtils.isNotBlank(type)) {
                error = type + "：" + error;
            }
            for (WalmartItem walmartItem : walmartItems) {
                FeedTask feedTask = feedTaskMap.get(walmartItem.getItemId());
                WalmartFeedTaskUtil.failFeedTask(feedTask, error);
            }
        } else {
            // 成功 记录改前改后值
            for (WalmartItem walmartItem : walmartItems) {
                FeedTask feedTask = feedTaskMap.get(walmartItem.getItemId());
                Double oldValue = oldValueMap.get(walmartItem.getId());
                Double newValue = walmartItem.getPrice();
                WalmartFeedTaskUtil.succeedFeedTask(feedTask, oldValue.toString(), newValue.toString(), type);
            }

            // 如果是恢复价格需要更新记录表状态
            if (CollectionUtils.isNotEmpty(updateModifyLogs)) {
                walmartModifyLogService.batchUpdateModifyLogById(updateModifyLogs);
            }
        }
    }

    @Override
    public void batchRestorePrice(List<WalmartModifyLog> walmartModifyLogList) {
        if (CollectionUtils.isEmpty(walmartModifyLogList)) {
            return;
        }

        // 根据itemId + sellerSku去重 并取最新一条记录（集合需要提前排好序）
        List<WalmartModifyLog> walmartModifyLogs = walmartModifyLogList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(()
                -> new TreeSet<>(Comparator.comparing(o -> o.getItemId() + "#" + o.getSellerSku()))), ArrayList::new));

        List<WalmartItem> updatePriceItems = new ArrayList<>();
        List<WalmartModifyLog> updateModifyLogs = new ArrayList<>();
        Map<Long, Double> oldValueMap = new HashMap<>();
        for (WalmartModifyLog walmartModifyLog : walmartModifyLogs) {
            // 如果在线列表价格和改前价格相等或者和改后价格不相等 不恢复价格
            WalmartItemExample itemExample = new WalmartItemExample();
            itemExample.setFiledColumns("id, account_number, item_id, seller_sku, price, sku");
            itemExample.createCriteria()
                    .andAccountNumberEqualTo(walmartModifyLog.getAccountNumber())
                    .andItemIdEqualTo(walmartModifyLog.getItemId())
                    .andSellerSkuEqualTo(walmartModifyLog.getSellerSku());
            List<WalmartItem> walmartItems = selectFiledColumnsByExample(itemExample);
            if (CollectionUtils.isEmpty(walmartItems)) {
                continue;
            }
            WalmartItem walmartItem = walmartItems.get(0);
            BigDecimal bigDecimal1 = BigDecimal.valueOf(walmartItem.getPrice());
            BigDecimal bigDecimal2 = BigDecimal.valueOf(walmartModifyLog.getBeforePrice());
            BigDecimal bigDecimal3 = BigDecimal.valueOf(walmartModifyLog.getAfterPrice());
            if (bigDecimal1.compareTo(bigDecimal2) == 0 || bigDecimal1.compareTo(bigDecimal3) != 0) {
                continue;
            }
            oldValueMap.put(walmartItem.getId(), walmartItem.getPrice());
            walmartItem.setPrice(walmartModifyLog.getBeforePrice());
            updatePriceItems.add(walmartItem);

            List<WalmartModifyLog> modifyLogs = walmartModifyLogList.stream()
                    .filter(o -> o.getSellerSku().equals(walmartModifyLog.getSellerSku())
                            && o.getItemId().equals(walmartModifyLog.getItemId())).collect(Collectors.toList());
            for (WalmartModifyLog modifyLog : modifyLogs) {
                WalmartModifyLog log = new WalmartModifyLog();
                log.setId(modifyLog.getId());
                log.setRestore(true);
                updateModifyLogs.add(log);
            }
        }
        if (CollectionUtils.isEmpty(updatePriceItems)) {
            return;
        }

        String type = "谷仓滞销SKU恢复价格";
        submitUpdatePrice(updatePriceItems, oldValueMap, updateModifyLogs, type);
    }

    @Override
    public String batchUpdatePriceByGrossMargin(List<WalmartItem> items, Double grossMargin, String remarks, Boolean isRecordOriginal) {
        if(CollectionUtils.isEmpty(items) || null == grossMargin) {
            return "修改数据和毛利率不可以为空！";
        }

        WalmartItemCalcPriceRequest calcPriceRequest = new WalmartItemCalcPriceRequest();
        calcPriceRequest.setGrossMargin(grossMargin);
        calcPriceRequest.setWalmartItems(items);
        ApiResult<?> listApiResult = WalmartCalcPriceUtil.batchCalcWalmartItemPrice(calcPriceRequest);
        if(!listApiResult.isSuccess()) {
            return listApiResult.getErrorMsg();
        }

        try {
            // 初始化处理报告
            Map<String, FeedTask> feedTaskMap = new HashMap<>();
            for (WalmartItem walmartItem : items) {
                FeedTask feedTask = WalmartFeedTaskUtil.initFeedTask(walmartItem.getAccountNumber(),
                        WalmartTaskTypeEnum.UPDATE_PRICE.getStatusMsgEn(), walmartItem.getSellerSku(), walmartItem.getItemId(), walmartItem.getSku());
                feedTaskMap.put(walmartItem.getItemId(), feedTask);
            }

            // 根据毛利率算价
            List<BatchPriceCalculatorResponse> batchPriceCalculatorResponses = (List<BatchPriceCalculatorResponse>)listApiResult.getResult();
            Map<String, BatchPriceCalculatorResponse> calcPriceResponseMap = batchPriceCalculatorResponses.stream().collect(Collectors.toMap(o->o.getId(), o->o, (k1,k2)->k1));

            Map<Long, Double> oldValueMap = new HashMap<>();
            List<WalmartItem> updatePriceWalmartItems = new ArrayList<>();
            for (WalmartItem walmartItem : items) {
                Long id = walmartItem.getId();
                BatchPriceCalculatorResponse calcPriceResponse = calcPriceResponseMap.get(id + "");
                if(null != calcPriceResponse && BooleanUtils.isTrue(calcPriceResponse.getIsSuccess())) { // 成功
                    oldValueMap.put(id, walmartItem.getPrice());
                    Double price = calcPriceResponse.getForeignPrice();
                    if (null != price) {
                        price = new BigDecimal(price).setScale(2, BigDecimal.ROUND_DOWN).doubleValue();
                    }

                    // 需要满足改后价大于0
                    if (Optional.ofNullable(price).orElse(0.0) <= 0) {
                        FeedTask feedTask = feedTaskMap.get(walmartItem.getItemId());
                        String msg = "改后价不满足大于0";
                        WalmartFeedTaskUtil.failFeedTask(feedTask, remarks + msg);
                        continue;
                    }

                    Double grossProfitMargin = calcPriceResponse.getGrossProfitRate();
                    if(null != grossProfitMargin) {
                        grossProfitMargin = new BigDecimal(grossProfitMargin).setScale(4, BigDecimal.ROUND_DOWN).doubleValue();
                    }
                    Double grossProfit = calcPriceResponse.getForeignGrossProfit();
                    if (null != grossProfit) {
                        grossProfit = new BigDecimal(grossProfit).setScale(2, BigDecimal.ROUND_DOWN).doubleValue();
                    }
                    walmartItem.setPrice(price);
                    walmartItem.setGrossMargin(grossProfitMargin);
                    walmartItem.setGrossProfit(grossProfit);
                    updatePriceWalmartItems.add(walmartItem);
                } else {// 失败直接记录处理报告
                    FeedTask feedTask = feedTaskMap.get(walmartItem.getItemId());
                    String msg = null == calcPriceResponse ? "毛利率算价结果未获取到" : calcPriceResponse.getErrorMsg();
                    WalmartFeedTaskUtil.failFeedTask(feedTask, remarks + msg);
                }
            }

            Map<String, List<WalmartItem>> accountItemsMap = updatePriceWalmartItems.stream()
                    .filter(o -> null != o.getAccountNumber()).collect(Collectors.groupingBy(WalmartItem::getAccountNumber));
            accountItemsMap.forEach((account, walmartItems) ->{
                String error = executeBatchUpdatePrice(account, walmartItems, true);
                // 更新处理报告
                if(StringUtils.isNotBlank(error)) {
                    // 失败
                    for (WalmartItem walmartItem : walmartItems) {
                        FeedTask feedTask = feedTaskMap.get(walmartItem.getItemId());
                        WalmartFeedTaskUtil.failFeedTask(feedTask, remarks + error);
                    }
                } else {
                    // 成功 记录改前改后值
                    for (WalmartItem walmartItem : walmartItems) {
                        FeedTask feedTask = feedTaskMap.get(walmartItem.getItemId());
                        feedTask.setResultMsg(remarks);
                        Double oldValue = oldValueMap.get(walmartItem.getId());
                        Double newValue = walmartItem.getPrice();
                        WalmartFeedTaskUtil.succeedFeedTask(feedTask, oldValue.toString(), newValue.toString());

                        // 记录原始价格到记录表 便于以后恢复价格
                        if (isRecordOriginal) {
                            WalmartModifyLog walmartModifyLog = new WalmartModifyLog();
                            walmartModifyLog.setAccountNumber(walmartItem.getAccountNumber());
                            walmartModifyLog.setItemId(walmartItem.getItemId());
                            walmartModifyLog.setSellerSku(walmartItem.getSellerSku());
                            walmartModifyLog.setSku(walmartItem.getSku());
                            walmartModifyLog.setBeforePrice(oldValue);
                            walmartModifyLog.setAfterPrice(newValue);
                            walmartModifyLog.setType(remarks);
                            walmartModifyLog.setModifyTime(new Timestamp(System.currentTimeMillis()));
                            walmartModifyLog.setRestore(false);
                            walmartModifyLogService.insert(walmartModifyLog);
                        }
                    }
                }
            });
        }catch (Exception e) {
            return "异常" + e.getMessage();
        }
        return null;
    }

    private String executeBatchUpdatePrice(String account, List<WalmartItem> walmartItems, Boolean updateLocalGross) {
        SaleAccountAndBusinessResponse walmartAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, account);
        if (null == walmartAccount) {
            return "获取账号信息失败";
        }

        try {
            // 获取令牌 修改价格
            WalmartAccountUtils.refreshAccountToken(walmartAccount);
            String feedId = new WalmartBulkUpdatePricesCall(walmartAccount).execute(walmartItems);
            log.info("批量修改库存请求平台成功feedId:" + feedId);
        } catch (Exception e) {
            log.error("批量修改价格请求平台失败！" + e.getMessage());
            return "批量修改价格请求平台失败！" + e.getMessage();
        }

        // 修改本地数据
        List<WalmartItem> batchUpdatePriceItem = new ArrayList<>();
        for (WalmartItem walmartItem :walmartItems) {
            // 获取到库存后修改数据库
            WalmartItem newWalmartItem = new WalmartItem();
            batchUpdatePriceItem.add(newWalmartItem);
            newWalmartItem.setId(walmartItem.getId());
            newWalmartItem.setPrice(walmartItem.getPrice());
            newWalmartItem.setUpdateDate(new Timestamp(System.currentTimeMillis()));
            if(BooleanUtils.isTrue(updateLocalGross)) {
                newWalmartItem.setGrossProfit(walmartItem.getGrossProfit());
                newWalmartItem.setGrossMargin(walmartItem.getGrossMargin());
            }
        }
        try {
            customWalmartItemMapper.batchUpdatePrice(batchUpdatePriceItem);
        } catch (Exception e) {
            return "请求平台成功修改本地库失败！" + e.getMessage();
        }

        return null;
    }

    @Override
    public String batchRetire(List<Long> ids, String retireRemark) {
        if (CollectionUtils.isEmpty(ids) || StringUtils.isBlank(retireRemark)) {
            return"请求参数错误：id或备注不能为空";
        }

        String userName = WebUtils.getUserName();
        if(StringUtils.isBlank(userName)) {
            return "获取当前操作人失败";
        }
        NewUser newUser = PublishRedisClusterUtils.hGet(ErpUsermgtNRedisConStant.GET_EMPLOYEE_INFO_BY_ID, new TypeReference<NewUser>() {
        }, userName);
        if (null != newUser) {
            String employeeNo = newUser.getEmployeeNo();
            String name = newUser.getName();
            userName = employeeNo + "-" + name;
        }

        WalmartItemExample example = new WalmartItemExample();
        example.createCriteria().andIdIn(ids);
        List<WalmartItem> batchUnPublishWalmartItems = walmartItemMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(batchUnPublishWalmartItems)) {
            return "本地没有查到数据！";
        }

        // 过滤单品状态为清仓，甩卖，且库存+在途-待发>0，且SKU在walmart不禁售的产品
//        Iterator<WalmartItem> it = batchUnPublishWalmartItems.iterator();
//        while (it.hasNext()) {
//            WalmartItem item = it.next();
//            try {
//                Integer skuStock = SkuStockUtils.getSkuSystemStock(item.getSku());
//                Boolean isTrue = WalmartItemLocalUtils.checkClearanceReductionListing(item, skuStock);
//                if (isTrue) {
//                    // 创建处理报告
//                    FeedTask feedTask = newWalmartItemFeedTask(item, WalmartTaskTypeEnum.RETIRE_ITEM, userName);
//                    feedTask.setResultMsg("该SKU单品状态为清仓，甩卖，且SKU在Walmart不禁售，不允许下架");
//                    feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
//                    feedTaskService.insert(feedTask);
//                    it.remove();
//                }
//            } catch (Exception e) {
//                log.error("清仓甩卖SKU下架限制报错：" + e.getMessage());
//            }
//        }

        String finalUserName = userName;
        String retireType = String.format("%s用户下架产品备注：%s", userName, retireRemark);
        List<String> errors = new ArrayList<>();
        Map<String, List<WalmartItem>> accountItemsMap = batchUnPublishWalmartItems.stream().collect(Collectors.groupingBy(o->o.getAccountNumber()));
        accountItemsMap.forEach((account, walmartItems) ->{
            if(StringUtils.isBlank(account)) {
                errors.add("存在账号为空数据！");
                return;
            }
            SaleAccountAndBusinessResponse walmartAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, account);
            if(null == walmartAccount) {
                errors.add(account + " 获取账号失败！");
                return;
            }

            String error = batchRetire(walmartItems, walmartAccount, finalUserName, retireType);
            if(StringUtils.isNotBlank(error)) {
                errors.add(error);
            }
        });

        if(CollectionUtils.isNotEmpty(errors)) {
            return errors.toString();
        } else {
            return null;
        }
    }

    @Override
    public String batchRetire(List<WalmartItem> walmartItems, SaleAccountAndBusinessResponse walmartAccount,
                              String taskCreatedBy, String feedType) {
        // 排除公司自注册sku 页面操作下架不用排除
        if (!feedType.contains("用户下架产品备注")) {
            WalmartItemLocalUtils.excludeSelfRegisteredSku(walmartItems);
        }
        if (CollectionUtils.isEmpty(walmartItems)) {
            return null;
        }

        for (WalmartItem walmartItem : walmartItems) {

            FeedTask feedTask = newWalmartItemFeedTask(walmartItem, WalmartTaskTypeEnum.RETIRE_ITEM, taskCreatedBy);
            WalmartExecutors.retireItem((rsp) -> {
                try{
                    // 刷新令牌
                    WalmartAccountUtils.refreshAccountToken(walmartAccount);
                    new WallmartItemDeleteCall(walmartAccount).execute(walmartItem.getSellerSku(), feedType);

                    // 下架后修改数据库
                    WalmartItem newWalmartItem = new WalmartItem();
                    newWalmartItem.setId(walmartItem.getId());
                    newWalmartItem.setLifecycleStatus(ItemLifecycleStatusEnum.RETIRED.getCode());
                    newWalmartItem.setUpdateDate(new Timestamp(System.currentTimeMillis()));
                    newWalmartItem.setRemarks(feedType);
                    walmartItemMapper.updateByPrimaryKeySelective(newWalmartItem);
                    feedTask.setResultStatus(WalmartTaskResultStatusEnum.SUCCESS.getCode());
                    feedTask.setResultMsg(feedType);
                } catch (Exception e) {
                    rsp.setMessage(e.getMessage());
                    rsp.setStatus(StatusCode.FAIL);
                    feedTask.setResultMsg(feedType + " " + e.getMessage());
                }

                feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                feedTaskService.insert(feedTask);
            });
        }

        return null;
    }

    @Override
    public String retireItems(List<WalmartItem> walmartItems, SaleAccountAndBusinessResponse walmartAccount,
                         String taskCreatedBy, String feedType) {
        // 排除公司自注册sku 页面操作下架不用排除
        if (!feedType.contains("用户下架产品备注")) {
            WalmartItemLocalUtils.excludeSelfRegisteredSku(walmartItems);
        }
        if (CollectionUtils.isEmpty(walmartItems)) {
            return null;
        }

        for (WalmartItem walmartItem : walmartItems) {
            FeedTask feedTask = newWalmartItemFeedTask(walmartItem, WalmartTaskTypeEnum.RETIRE_ITEM, taskCreatedBy);
            try {
                WalmartAccountUtils.refreshAccountToken(walmartAccount);
                new WallmartItemDeleteCall(walmartAccount).execute(walmartItem.getSellerSku());

                // 下架后修改数据库
                WalmartItem newWalmartItem = new WalmartItem();
                newWalmartItem.setId(walmartItem.getId());
                newWalmartItem.setLifecycleStatus(ItemLifecycleStatusEnum.RETIRED.getCode());
                newWalmartItem.setUpdateDate(new Timestamp(System.currentTimeMillis()));
                newWalmartItem.setRemarks(feedType);
                walmartItemMapper.updateByPrimaryKeySelective(newWalmartItem);

                feedTask.setResultStatus(WalmartTaskResultStatusEnum.SUCCESS.getCode());
                feedTask.setResultMsg(feedType);
            } catch (Exception e) {
                feedTask.setResultMsg(feedType + " " + e.getMessage());
            }

            feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
            feedTaskService.insert(feedTask);
        }

        return null;
    }

    @Override
    public List<WalmartItemWithSkuStock> selectItemSkuStock(String accountNumber, List<String> notSkuStatusList, String type, Timestamp ltDay) {
        return customWalmartItemMapper.selectItemSkuStock(accountNumber, notSkuStatusList, type,  ltDay);
    }

    @Override
    public List<String> filterNotActiveDrainageSku(String accountNumber,  List<String> skus) {
        if(StringUtils.isEmpty(accountNumber) || CollectionUtils.isEmpty(skus)) {
            return null;
        }

        WalmartItemExample example = new WalmartItemExample();
        example.createCriteria().andAccountNumberEqualTo(accountNumber)
                .andLifecycleStatusEqualTo(ItemLifecycleStatusEnum.ACTIVE.getCode())
                .andSkuIn(skus);
        List<WalmartItem> walmartItems = walmartItemMapper.selectByExample(example);
        List<String> activeSkus = walmartItems.stream().map(WalmartItem::getSku).collect(Collectors.toList());

        List<String> notActiveSkus = skus.stream().filter(o-> (CollectionUtils.isEmpty(activeSkus) || !activeSkus.contains(o))).collect(Collectors.toList());
        return notActiveSkus;
    }

    /**
     * 初始化ebay在线列表更新数据的日志对象
     * @param walmartItem
     * @param taskTypeEnum
     * @return
     */
    private FeedTask newWalmartItemFeedTask(WalmartItem walmartItem, WalmartTaskTypeEnum taskTypeEnum, String createdBy) {
        // 生成处理报告 默认失败状态 根据结果判断成功修改为成功
        FeedTask feedTask = new FeedTask();
        feedTask.setResultStatus(WalmartTaskResultStatusEnum.FAIL.getCode());
        feedTask.setAssociationId(walmartItem.getId().toString());
        feedTask.setAccountNumber(walmartItem.getAccountNumber());
        feedTask.setArticleNumber(walmartItem.getSku());
        feedTask.setAttribute3(walmartItem.getSellerSku());
        feedTask.setTaskType(taskTypeEnum.getStatusMsgEn());
        feedTask.setPlatform(Platform.Walmart.name());
        feedTask.setTableIndex();
        feedTask.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
        feedTask.setCreatedBy(createdBy);
        feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));

        return feedTask;
    }

    @Override
    public void syncProductInfo(List<String> skuList, List<String> accountNumberList) {
        if(CollectionUtils.isEmpty(skuList)) {
            return;
        }

        Map<String, ProductInfoVO> map = new HashMap<>(20000);

        WalmartItemExample example = new WalmartItemExample();
        String filed = "id, sku, account_number, mark_off_time, infringement_type_name";
        example.setFiledColumns(filed);
        WalmartItemExample.Criteria criteria = example.createCriteria();
        criteria.andSkuIn(skuList);
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            criteria.andAccountNumberIn(accountNumberList);
        }
        List<WalmartItem> walmartItems = selectFiledColumnsByExample(example);
        if (ObjectUtils.isEmpty(walmartItems)) {
            return;
        }
        log.info("当前更新数量：{}", walmartItems.size());
        walmartItems.forEach(walmartItem -> {
            // _TE账号,不执行!
            if(WalmartAccountUtils.isPublishTemuAccount(walmartItem.getAccountNumber())) {
                return;
            }
            ProductInfoVO productInfoVO = map.get(walmartItem.getSku());
            if (ObjectUtils.isEmpty(productInfoVO)) {
                productInfoVO = ProductUtils.getSkuInfo(walmartItem.getSku());
                map.put(walmartItem.getSku(), productInfoVO);
            }
            if (ObjectUtils.isEmpty(productInfoVO) || null == productInfoVO.getSonSku()) {
                return;
            }

            WalmartItem newWalmartItem = new WalmartItem();
            newWalmartItem.setId(walmartItem.getId());
            newWalmartItem.setMarkOffTime(walmartItem.getMarkOffTime());
            newWalmartItem.setInfringementTypeName(walmartItem.getInfringementTypeName());

            // 设置产品信息
            WalmartItemLocalUtils.setItemSkuStatus(newWalmartItem, productInfoVO);

            updateProductInfoByPrimaryKey(newWalmartItem);
        });
    }

    @Override
    public void updateItemTitleDescription(WalmartReplaceItemVO request) throws Exception {
        if (null == request || CollectionUtils.isEmpty(request.getIdList())) {
            throw new Exception("参数不可为空");
        }

        // 根据id查item
        WalmartItemExample itemExample = new WalmartItemExample();
        String filed = "id, account_number, seller_sku, main_sku, sku, gtin";
        itemExample.setFiledColumns(filed);
        itemExample.createCriteria().andIdIn(request.getIdList());
        List<WalmartItem> walmartItemList = selectFiledColumnsByExample(itemExample);
        if (CollectionUtils.isEmpty(walmartItemList)) {
            throw new Exception("查询不到勾选数据");
        }

        List<WalmartReplaceItem> replaceItems = new ArrayList<>();
        for (WalmartItem item : walmartItemList) {
            WalmartReplaceItem replaceItem = new WalmartReplaceItem();
            replaceItem.setRelationId(item.getId().intValue());
            replaceItem.setAccountNumber(item.getAccountNumber());
            replaceItem.setMainSku(item.getMainSku());
            replaceItem.setSku(item.getSku());
            replaceItem.setSellerSku(item.getSellerSku());
            replaceItem.setProductIdType(WalmartPublishConstant.DEFAULT_PRODUCT_ID_TYPE);
            replaceItem.setProductId(item.getGtin());
            replaceItem.setSubCategoryName(request.getSubCategoryName());
            replaceItem.setStatus(WalmartReplaceItemStatusEnum.WAIT_SUBMIT.getCode());
            replaceItem.setIsRetry(false);
            String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
            replaceItem.setCreateBy(currentUser);
            replaceItem.setCreateDate(new Timestamp(System.currentTimeMillis()));
            replaceItems.add(replaceItem);
        }

        // 获取标题描述五点描述
        getTitleDescription(replaceItems, request);

        // 插入数据库
        walmartReplaceItemService.batchInsert(replaceItems);

        // 生成处理报告
        List<String> typeList = new ArrayList<>();
        if (null != request.getIsReplaceTitle() && request.getIsReplaceTitle()) {
            typeList.add(WalmartTaskTypeEnum.UPDATE_TITLE.getStatusMsgEn());
        }
        if (null != request.getIsReplaceDescription() && request.getIsReplaceDescription()) {
            typeList.add(WalmartTaskTypeEnum.UPDATE_DESCRIPTION.getStatusMsgEn());
        }
        if (null != request.getIsReplaceKeyFeatures() && request.getIsReplaceKeyFeatures()) {
            typeList.add(WalmartTaskTypeEnum.UPDATE_KEY_FEATURES.getStatusMsgEn());
        }
        WalmartFeedTaskUtil.generateFeedTask(replaceItems, typeList);
    }

    private void getTitleDescription(List<WalmartReplaceItem> replaceItems, WalmartReplaceItemVO request) {
        // 根据主sku获取标题描述
        List<String> mainSkuList = replaceItems.stream().map(WalmartReplaceItem::getMainSku).distinct().collect(Collectors.toList());
        Map<String, SpuOfficial> amazonSpuOfficialMap = getSpuAndAmazonSpuOfficialMap(mainSkuList);
        Map<String, SpuOfficial> spuOfficialMap = getSpuAndSpuOfficicalMap(mainSkuList);

        for (WalmartReplaceItem replaceItem : replaceItems) {
            SpuOfficial amazonSpuOfficial = amazonSpuOfficialMap.get(replaceItem.getMainSku());
            SpuOfficial spuOfficial = spuOfficialMap.get(replaceItem.getMainSku());
            WalmartCommonUtils.matchingTitleDescription(spuOfficial, amazonSpuOfficial);
            TitleDescription titleDescription = new TitleDescription();

            // 过滤标题描述存在的侵权词
            if (null != request.getIsReplaceTitle() && request.getIsReplaceTitle()) {
                String title = WalmartInfringementWordUtils.delInfringementWords(titleDescription.getTitle(), false);
                replaceItem.setTitle(title);
            }
            if (null != request.getIsReplaceDescription() && request.getIsReplaceDescription()) {
                String description = WalmartInfringementWordUtils.delInfringementWords(titleDescription.getDescription(), false);
                replaceItem.setDescription(description);
            }
            if (null != request.getIsReplaceKeyFeatures() && request.getIsReplaceKeyFeatures()) {
                String keyFeatures = WalmartInfringementWordUtils.delInfringementWords(titleDescription.getKeyFeatures(), true);
                replaceItem.setKeyFeatures(keyFeatures);
            }
        }
    }

    private Map<String, SpuOfficial> getSpuAndAmazonSpuOfficialMap(List<String> mainSkuList) {
        Map<String, SpuOfficial> map = new HashMap<>();
        if (CollectionUtils.isEmpty(mainSkuList)) {
            return map;
        }

        try {
            List<SpuOfficial> amazonOfficialList = ProductUtils.getAmazonOfficialList(mainSkuList);
            if (CollectionUtils.isNotEmpty(amazonOfficialList)) {
                map = amazonOfficialList.stream().collect(Collectors.toMap(a -> a.getSpu(), Function.identity(), (oldV, newV) -> newV));
            }
        } catch (Exception e) {
            log.error("获取亚马逊文档失败：", JSON.toJSONString(mainSkuList));
        }
        return map;
    }

    private static Map<String, SpuOfficial> getSpuAndSpuOfficicalMap(List<String> mainSkuList) {
        Map<String, SpuOfficial> spuOfficialMap = new HashMap<>();
        ResponseJson resp = ProductUtils.getSpuTitles(mainSkuList);
        if (resp.isSuccess()) {
            List<SpuOfficial> spuInfos = (List<SpuOfficial>)resp.getBody().get(ProductUtils.resultKey);
            if (CollectionUtils.isNotEmpty(spuInfos)) {
                spuOfficialMap = spuInfos.stream().collect(Collectors.toMap(SpuOfficial::getSpu, o -> o));
            }
        }
        return spuOfficialMap;
    }

    @Override
    public void replaceItem(List<WalmartReplaceItem> replaceItems) {
        if (CollectionUtils.isEmpty(replaceItems)) {
            return;
        }

        // 更新提交状态
        List<Integer> idList = replaceItems.stream().map(WalmartReplaceItem::getId).collect(Collectors.toList());
        walmartReplaceItemService.batchUpdateStatus(idList, WalmartReplaceItemStatusEnum.SUBMITTED.getCode());

        try {
            // 提交修改
            SaleAccountAndBusinessResponse account =
                    AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, replaceItems.get(0).getAccountNumber());
            WalmartAccountUtils.refreshAccountToken(account);
            String feedId = new WalmartBulkItemCall(account).replaceItem(replaceItems);

            // 中间表插入feedId
            WalmartReplaceItem replaceItem = new WalmartReplaceItem();
            replaceItem.setFeedId(feedId);
            replaceItem.setIsRetry(false);
            WalmartReplaceItemExample replaceItemExample = new WalmartReplaceItemExample();
            replaceItemExample.createCriteria().andIdIn(idList);
            walmartReplaceItemService.updateByExampleSelective(replaceItem, replaceItemExample);

            // 发送消息
            FeedMessage feedMessage = new FeedMessage();
            feedMessage.setAccountNumber(replaceItems.get(0).getAccountNumber());
            feedMessage.setFeedId(feedId);
            // 更新标题描述五点描述，修改起止时间等，消息类型统一设置为REPLACE_ITEM
            feedMessage.setType(WalmartTaskTypeEnum.REPLACE_ITEM.getStatusMsgEn());
            feedMessage.setRelationIdList(idList);
            Map<Integer, String> idToTitleMap = replaceItems.stream()
                    .filter(o -> StringUtils.isNotBlank(o.getTitle()))
                    .collect(Collectors.toMap(WalmartReplaceItem::getRelationId, WalmartReplaceItem::getTitle, (o1, o2) -> o1));
            feedMessage.setIdToTitleMap(idToTitleMap);
            rabbitTemplate.convertAndSend(PublishMqConfig.WALMART_API_DIRECT_EXCHANGE,
                    PublishQueues.WALMART_PUBLISH_DELAY_QUEUE_KEY, feedMessage, (message)-> {
                        message.getMessageProperties().setExpiration(WalmartPublishConstant.FIRST_TTL_TIME);
                        return message;
                    });
        } catch (Exception e) {
            if (StringUtils.containsIgnoreCase(e.getMessage(), WalmartApiErrorConstant.THRESHOLD_EXCEEDED)) {
                WalmartReplaceItem replaceItem = new WalmartReplaceItem();
                replaceItem.setIsRetry(true);
                WalmartReplaceItemExample replaceItemExample = new WalmartReplaceItemExample();
                replaceItemExample.createCriteria().andIdIn(idList);
                walmartReplaceItemService.updateByExampleSelective(replaceItem, replaceItemExample);
                return;
            }
            // 更新失败处理报告
            handleFailFeedTaskStatus(idList, e.getMessage());
        }
    }

    @Override
    public void handleFailFeedTaskStatus(List<Integer> idList, String msg) {
        List<String> idStrList = idList.stream().map(String::valueOf).collect(Collectors.toList());
        FeedTask update = new FeedTask();
        update.setPlatform(Platform.Walmart.name());
        update.setTableIndex();
        update.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
        update.setResultStatus(WalmartTaskResultStatusEnum.FAIL.getCode());
        update.setFinishTime(new Timestamp(System.currentTimeMillis()));
        update.setResultMsg(msg);
        FeedTaskExample example = new FeedTaskExample();
        example.createCriteria().andAttribute5In(idStrList);
        feedTaskService.updateByExampleSelective(update, example);
    }

    @Override
    public void updateItem(List<WalmartItem> walmartItems, String type) {
        if (CollectionUtils.isEmpty(walmartItems)) {
            return;
        }

        // 生成处理报告
        String platform;
        if (WalmartTaskTypeEnum.IL_STATE_RESTRICTION.getStatusMsgEn().equals(type)
                || WalmartTaskTypeEnum.RECOVER_STATE_RESTRICTION.getStatusMsgEn().equals(type)) {
            platform = "walmart_2";
        } else {
            platform = Platform.Walmart.name();
        }
        List<FeedTask> feedTasks = WalmartFeedTaskUtil.generateFeedTask(walmartItems, type, platform);

        try {
            // 提交修改
            SaleAccountAndBusinessResponse account =
                    AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, walmartItems.get(0).getAccountNumber());
            WalmartAccountUtils.refreshAccountToken(account);
            String feedId = new WalmartBulkItemCall(account).updateItem(walmartItems);

            // 发送消息
            FeedMessage feedMessage = new FeedMessage();
            feedMessage.setAccountNumber(walmartItems.get(0).getAccountNumber());
            feedMessage.setFeedId(feedId);
            feedMessage.setType(type);
            List<Long> idList = feedTasks.stream().map(FeedTask::getId).collect(Collectors.toList());
            feedMessage.setFeedIdList(idList);
            rabbitTemplate.convertAndSend(PublishMqConfig.WALMART_API_DIRECT_EXCHANGE,
                    PublishQueues.WALMART_PUBLISH_DELAY_QUEUE_KEY, feedMessage, (message)-> {
                        message.getMessageProperties().setExpiration(WalmartPublishConstant.FIRST_TTL_TIME);
                        return message;
                    });
        } catch (Exception e) {
            // 更新失败处理报告
            feedTaskService.batchUpdateFeedTaskToFinish(feedTasks, TaskStatusEnum.FINISH.getStatusCode(), ResultStatusEnum.RESULT_FAIL.getStatusCode(), e.getMessage());

            // 调用达到阈值后抛出
            if (StringUtils.containsIgnoreCase(e.getMessage(), WalmartApiErrorConstant.THRESHOLD_EXCEEDED)) {
                throw new RuntimeException(e.getMessage());
            }
        }
    }

    @Override
    public String syncBrand(List<WalmartItem> walmartItems, SaleAccountAndBusinessResponse walmartAccount) {
        if (CollectionUtils.isEmpty(walmartItems)) {
            return "参数为空";
        }
        List<String> errorList = new ArrayList<>();
        for (WalmartItem walmartItem : walmartItems) {
            try {
                WalmartAccountUtils.refreshAccountToken(walmartAccount);
                JSONObject syncBrand = new WalmartSearchItemCall(walmartAccount).syncBrand(walmartItem.getGtin());
                if (ObjectUtils.isEmpty(syncBrand) || StringUtils.isBlank(syncBrand.getString("brand")) || StringUtils.isBlank(syncBrand.getString("itemId"))) {
                    continue;
                }

                // 本地保存
                WalmartItem newWalmartItem = new WalmartItem();
                newWalmartItem.setId(walmartItem.getId());
                newWalmartItem.setPlatformItemId(syncBrand.getString("itemId"));
                newWalmartItem.setBrand(syncBrand.getString("brand"));
                newWalmartItem.setSyncBrandDate(new Timestamp(System.currentTimeMillis()));
                newWalmartItem.setUpdateDate(new Timestamp(System.currentTimeMillis()));
                walmartItemMapper.updateByPrimaryKeySelective(newWalmartItem);
            } catch (Exception e) {
                if (errorList.size() < 5) {
                    errorList.add(e.getMessage());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(errorList)) {
            return StringUtils.join(errorList, ",");
        }
        return null;
    }

    @Override
    public List<String> selectSpuLEPastYearTotalSales(Integer pastYearTotalSales, Integer maxAccountQuantity) {
        return customWalmartItemMapper.selectSpuLEPastYearTotalSales(pastYearTotalSales, maxAccountQuantity);
    }

    @Override
    public List<String> selectSpuNewState(Integer maxAccountQuantity) {
        return customWalmartItemMapper.selectSpuNewState(maxAccountQuantity);
    }

    @Override
    public List<String> selectSpuClearanceReduction(Integer maxAccountQuantity) {
        return customWalmartItemMapper.selectSpuClearanceReduction(maxAccountQuantity);
    }

    @Override
    public List<String> selectSpuByMaxAccountQuantity(Integer maxAccountQuantity) {
        return customWalmartItemMapper.selectSpuByMaxAccountQuantity(maxAccountQuantity);
    }

    @Override
    public List<WalmartAccountSpu> selectByAccountSpu(List<String> normalAccounts, List<String> list, Integer maxAccountQuantity) {
        return customWalmartItemMapper.selectByAccountSpu(normalAccounts, list, maxAccountQuantity);
    }

    @Override
    public void retireNoSalesSku(List<SaleAccountAndBusinessResponse> accountList, Integer daysBefore,
                                 String executionType, int threadNum) throws InterruptedException {
        if (CollectionUtils.isEmpty(accountList) || null == daysBefore || StringUtils.isBlank(executionType)) {
            return;
        }

        // 控制线程池数量 创建Semaphore信号量
        final Semaphore sp = new Semaphore(threadNum);
        for (SaleAccountAndBusinessResponse account : accountList) {
            sp.acquire();
            WalmartExecutors.retireNoSalesItem(() -> {
                try {
                    this.retireNoSalesSku(account, daysBefore, executionType);
                } catch (Exception e) {
                    log.error("账号" + account.getAccountNumber() + "报错：" + e);
                } finally {
                    sp.release();
                }
            });
        }
    }

    @Override
    public void retireNoSalesSku(SaleAccountAndBusinessResponse account, Integer daysBefore, String executionType) {
        String retireType = String.format("上架超过%s天且%s", daysBefore, WalmartFeedTaskMsgEnum.RETIRE_NO_SALES_SKU_BY_CREATE_TIME.getMsg());

        WalmartAccountConfig accountConfig = walmartAccountConfigService.selectByAccountNumber(account.getAccountNumber());

        // 查询最晚创建时间超过n天，且总销量为0的spu
        Date date = DateUtils.addDays(new Date(), -daysBefore);
        List<String> spuList = selectCreateDayNoSalesSpu(account.getAccountNumber(), date);
        if (CollectionUtils.isEmpty(spuList)) {
            return;
        }
        log.info("店铺{} - 下架spu:{}", account.getAccountNumber(), spuList);
        XxlJobLogger.log("店铺{} - 开始下架spu:{}", account.getAccountNumber(), spuList);
        List<List<String>> lists = PagingUtils.newPagingList(spuList, 100);
        for (List<String> list : lists) {
            WalmartItemExample example = new WalmartItemExample();
            example.setFiledColumns("id, account_number, seller_sku, main_sku, sku, gtin");
            example.createCriteria()
                    .andAccountNumberEqualTo(account.getAccountNumber())
                    .andLifecycleStatusEqualTo(ItemLifecycleStatusEnum.ACTIVE.getCode())
                    .andMainSkuIn(list);
            List<WalmartItem> walmartItems = selectFiledColumnsByExample(example);

            // 排除特供店铺和店铺配置选择的特供标签的产品不下架，特供店铺和不是店铺配置选择的特供标签的产品需下架
            WalmartItemUtils.filterSpecialSupplyShopProducts(walmartItems, accountConfig);

            // 校验总销量写入时间
            checkSaleTotalCount(walmartItems);
            if (CollectionUtils.isEmpty(walmartItems)) {
                continue;
            }

            // 下架
            if (WalmartExecutionTypeEnum.DELETE_DATA.getCode().equals(executionType)) {
                batchRetire(walmartItems, account, StrConstant.ADMIN, retireType);
            } else if (WalmartExecutionTypeEnum.RECORD_DATA.getCode().equals(executionType)) {
                // 记录数据不下架
                List<WalmartTemporaryLog> temporaryLogs = buildRetireRecord(walmartItems, retireType);
                walmartTemporaryLogService.batchInsert(temporaryLogs);
            }
        }
    }

    @Override
    public List<WalmartTemporaryLog> buildRetireRecord(List<WalmartItem> walmartItems, String retireType) {
        List<WalmartTemporaryLog> temporaryLogs = new ArrayList<>();
        for (WalmartItem retiredItem : walmartItems) {
            WalmartTemporaryLog temporaryLog = new WalmartTemporaryLog();
            temporaryLog.setAccountNumber(retiredItem.getAccountNumber());
            temporaryLog.setSellerSku(retiredItem.getSellerSku());
            temporaryLog.setSpu(retiredItem.getMainSku());
            temporaryLog.setSku(retiredItem.getSku());
            temporaryLog.setGtin(retiredItem.getGtin());
            temporaryLog.setType(retireType);
            temporaryLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
            temporaryLogs.add(temporaryLog);
        }

        return temporaryLogs;
    }

    @Override
    public void checkSaleTotalCount(List<WalmartItem> walmartItems) {
        if (CollectionUtils.isEmpty(walmartItems)) {
            return;
        }

        Map<String, List<WalmartItem>> spuToItemMap = walmartItems.stream().collect(Collectors.groupingBy(WalmartItem::getMainSku));
        for (String spu : spuToItemMap.keySet()) {
            List<WalmartItem> walmartItemList = spuToItemMap.get(spu);
            try {
                // 查询销量写入时间信息
                List<String> idList = walmartItemList.stream()
                        .map(o -> SaleChannel.CHANNEL_WALMART + "_" + o.getAccountNumber() + "_" + o.getSellerSku()).collect(Collectors.toList());
                Map<String, EsOrderSalesStatisticsTime> idToWriteTimeMap = findSalesWriteTime(idList);

                // 校验总销量写入时间是否符合要求
                Boolean canRetired = checkSaleTotalCountWriteTime(idList, idToWriteTimeMap);
                if (!canRetired) {
                    XxlJobLogger.log("店铺{} - SPU:{} 不下架", walmartItemList.get(0).getAccountNumber(), spu);
                    log.info("店铺{} - SPU:{} 不下架", walmartItemList.get(0).getAccountNumber(), spu);
                    walmartItems.removeAll(walmartItemList);
                }
            } catch (Exception e) {
                log.error("校验销量写入时间报错：" + e.getMessage());
                walmartItems.removeAll(walmartItemList);
            }
        }
    }

    @Override
    public Map<String, EsOrderSalesStatisticsTime> findSalesWriteTime(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyMap();
        }

        List<EsOrderSalesStatisticsTime> writeTimeList = esOrderSalesStatisticsTimeService.findAllByIds(idList);
        if (CollectionUtils.isEmpty(writeTimeList)) {
            return Collections.emptyMap();
        }
        return writeTimeList.stream().collect(Collectors.toMap(EsOrderSalesStatisticsTime::getId, o -> o, (o1, o2) -> o1));
    }

    /**
     * 校验总销量写入时间 判断是否可以下架
     *
     * @param idList
     * @param idToWriteTimeMap
     * @return 1可以下架 0不可以下架
     */
    private Boolean checkSaleTotalCountWriteTime(List<String> idList, Map<String, EsOrderSalesStatisticsTime> idToWriteTimeMap) {
        for (String id : idList) {
            EsOrderSalesStatisticsTime orderSalesStatisticsTime = idToWriteTimeMap.get(id);
            // 如果查不到销量写入时间 可以下架
            if (null == orderSalesStatisticsTime) {
                continue;
            }

            // 如果总销量写入时间为当天 可以下架
            Date dateBegin = DateUtils.getDateBegin(0);
            Date saleTotalCountDate = orderSalesStatisticsTime.getSaleTotalCountDate();
            if (dateBegin.getTime() <= saleTotalCountDate.getTime()) {
                continue;
            }

            // 如果总销量为0，且销量写入时间和总销量写入时间为同一天，且总销量写入时间大于最新总销量置空时间，可以下架
            Integer orderNumTotal = orderSalesStatisticsTime.getOrderNumTotal();
            Date saleCountDate = orderSalesStatisticsTime.getSaleCountDate();
            Date saleTotalCountZeroDate = orderSalesStatisticsTime.getSaleTotalCountZeroDate();
            Boolean theSameDay = DateUtils.isTheSameDay(saleTotalCountDate, saleCountDate);
            if (orderNumTotal != null
                    && orderNumTotal == 0
                    && theSameDay
                    && saleTotalCountDate.getTime() > saleTotalCountZeroDate.getTime()) {
                continue;
            }
            return false;
        }

        return true;
    }

    @Override
    public List<String> selectCreateDayNoSalesSpu(String accountNumber, Date daysBefore) {
        return customWalmartItemMapper.selectCreateDayNoSalesSpu(accountNumber, daysBefore);
    }

    @Override
    public List<SpuToAccount> selectRetireAccountSpu() {
        return customWalmartItemMapper.selectRetireAccountSpu();
    }

    @Override
    public String getReportStatus(SaleAccountAndBusinessResponse account, String requestId) {
        if (null == account || StringUtils.isBlank(requestId)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        WalmartAccountUtils.refreshAccountToken(account);
        return new WalmartReportCall(account).getReportStatus(requestId);
    }

    @Override
    public String downloadReport(SaleAccountAndBusinessResponse account, String requestId) {
        if (null == account || StringUtils.isBlank(requestId)) {
            throw new IllegalArgumentException("参数不能为空");
        }

        WalmartAccountUtils.refreshAccountToken(account);
        return new WalmartReportCall(account).downloadReport(requestId);
    }

    @Override
    public void replaceItemImage(List<WalmartReplaceItemVO> walmartReplaceItemVOList) {
        if (CollectionUtils.isEmpty(walmartReplaceItemVOList)) {
            throw new IllegalArgumentException("参数异常");
        }

        // 根据id查item
        List<Long> idList = walmartReplaceItemVOList.stream().map(o -> o.getRelationId().longValue()).collect(Collectors.toList());
        WalmartItemExample itemExample = new WalmartItemExample();
        String filed = "id, account_number, seller_sku, main_sku, sku, gtin";
        itemExample.setFiledColumns(filed);
        itemExample.createCriteria().andIdIn(idList);
        List<WalmartItem> walmartItemList = selectFiledColumnsByExample(itemExample);
        if (CollectionUtils.isEmpty(walmartItemList)) {
            throw new BusinessException("查询不到勾选数据");
        }
        Map<Integer, WalmartItem> idToItemMap = walmartItemList.stream().collect(Collectors.toMap(o -> o.getId().intValue(), o -> o));

        List<WalmartReplaceItem> replaceItems = new ArrayList<>();
        for (WalmartReplaceItemVO replaceItemVO : walmartReplaceItemVOList) {
            WalmartReplaceItem replaceItem = new WalmartReplaceItem();
            WalmartItem item = idToItemMap.get(replaceItemVO.getRelationId());
            replaceItem.setRelationId(item.getId().intValue());
            replaceItem.setAccountNumber(item.getAccountNumber());
            replaceItem.setMainSku(item.getMainSku());
            replaceItem.setSku(item.getSku());
            replaceItem.setSellerSku(item.getSellerSku());
            replaceItem.setProductIdType(WalmartPublishConstant.DEFAULT_PRODUCT_ID_TYPE);
            replaceItem.setProductId(item.getGtin());
            replaceItem.setSubCategoryName(WalmartPublishConstant.DEFAULT_CATEGORY);
            replaceItem.setMainImageUrl(replaceItemVO.getMainImageUrl());
            replaceItem.setExtraImages(replaceItemVO.getExtraImageList() == null ? null : JSON.toJSONString(replaceItemVO.getExtraImageList()));
            replaceItem.setStatus(WalmartReplaceItemStatusEnum.WAIT_SUBMIT.getCode());
            replaceItem.setIsRetry(false);
            String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
            replaceItem.setCreateBy(currentUser);
            replaceItem.setCreateDate(new Timestamp(System.currentTimeMillis()));
            replaceItems.add(replaceItem);
        }
        walmartReplaceItemService.batchInsert(replaceItems);

        // 初始化处理报告
        List<FeedTask> feedTasks = WalmartFeedTaskUtil.generateFeedTask(replaceItems, Lists.newArrayList(WalmartTaskTypeEnum.UPDATE_IMAGE.getStatusMsgEn()));

        // 设置图片映射路径
        for (WalmartReplaceItem replaceItem : replaceItems) {
            try {
                WalmartItemLocalUtils.setOSSUrl(replaceItem);
            } catch (Exception e) {
                List<FeedTask> tasks = feedTasks.stream().filter(o -> replaceItem.getRelationId().toString().equals(o.getAssociationId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(tasks)) {
                    WalmartFeedTaskUtil.failFeedTask(tasks.get(0), e.getMessage());
                }
                replaceItem.setStatus(WalmartReplaceItemStatusEnum.SUBMITTED.getCode());
            }
        }
        walmartReplaceItemService.batchUpdateByPrimaryKeySelective(replaceItems);
    }

    @Override
    public List<String> selectAccountNumberByExample(WalmartItemExample example){
        return walmartItemMapper.selectAccountNumberByExample(example);
    }

    @Override
    public List<WalmartItem>  repeatListingCount(String accountNumber){
        return walmartItemMapper.repeatListingCount(accountNumber);
    }

    @Override
    public int statisticsNoSalesRetireData(List<SaleAccountAndBusinessResponse> accountList, Date dateBefore) {
        int sumCount = 0;
        for (SaleAccountAndBusinessResponse account : accountList) {
            int count = 0;
            WalmartAccountConfig accountConfig = walmartAccountConfigService.selectByAccountNumber(account.getAccountNumber());

            // 查询最晚创建时间超过n天，且总销量为0的spu
            List<String> spuList = this.selectCreateDayNoSalesSpu(account.getAccountNumber(), dateBefore);

            // 根据spu查询在线listing
            List<List<String>> lists = PagingUtils.newPagingList(spuList, 1000);
            for (List<String> list : lists) {
                WalmartItemExample example = new WalmartItemExample();
                example.setFiledColumns("id, seller_sku, main_sku, sku");
                example.createCriteria()
                        .andAccountNumberEqualTo(account.getAccountNumber())
                        .andLifecycleStatusEqualTo(ItemLifecycleStatusEnum.ACTIVE.getCode())
                        .andMainSkuIn(list);
                List<WalmartItem> walmartItems = selectFiledColumnsByExample(example);

                // 排除特供店铺和店铺配置选择的特供标签的产品不下架，特供店铺和不是店铺配置选择的特供标签的产品需下架
                WalmartItemUtils.filterSpecialSupplyShopProducts(walmartItems, accountConfig);

                count += walmartItems.size();
            }
            sumCount += count;
        }

        return sumCount;
    }

    @Override
    public void downloadNoSalesRetireData(Integer daysBefore) {
        // 记录下载日志
        WalmartExcelDownloadLog titleDownloadLog = new WalmartExcelDownloadLog();
        titleDownloadLog.setQueryCondition(JSON.toJSONString(daysBefore));
        titleDownloadLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
        titleDownloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
        titleDownloadLog.setType(WalmartExcelDownloadTypeEnum.NO_SALES_DATA.getCode());
        titleDownloadLog.setStatus(WalmartExcelStatusEnum.WAIT.getCode());
        walmartExcelDownloadLogService.insert(titleDownloadLog);

        // 发送消息
        walmartExcelDownloadMqSender.excelDownloadSend(titleDownloadLog.getId(), WalmartExcelDownloadTypeEnum.NO_SALES_DATA.getCode());
    }
}