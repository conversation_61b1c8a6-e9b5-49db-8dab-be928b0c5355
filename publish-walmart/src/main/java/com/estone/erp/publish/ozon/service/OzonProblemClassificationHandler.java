package com.estone.erp.publish.ozon.service;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.ozon.model.OzonTemplateModel;
import com.estone.erp.publish.ozon.model.dto.solution.ErrorSolutionResult;
import com.estone.erp.publish.ozon.model.dto.solution.SolutionItem;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Ozon问题分类处理组件
 * 负责分析错误报告，提取问题分类，并更新相关字段
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-27
 */
@Slf4j
@Service
public class OzonProblemClassificationHandler {

    @Autowired
    private OzonErrorSolutionService ozonErrorSolutionService;

    /**
     * 处理模板刊登失败的问题分类
     * 
     * @param templateModel 模板对象
     * @param errorMessage 错误信息
     * @param operationType 操作类型（PUBLISH、UPDATE等）
     */
    public void handleTemplateProblemClassification(OzonTemplateModel templateModel, String errorMessage, String operationType) {
        if (templateModel == null || StringUtils.isBlank(errorMessage)) {
            log.warn("模板或错误信息为空，跳过问题分类处理");
            return;
        }

        try {
            // 调用错误解决方案服务分析问题
            ErrorSolutionResult solutionResult = ozonErrorSolutionService.findSolutionForError(errorMessage, operationType);
            
            if (solutionResult.isSuccess() && !solutionResult.getSolutions().isEmpty()) {
                List<SolutionItem> solutions = solutionResult.getSolutions();
                
                // 获取第一个问题分类作为主要问题分类
                String primaryProblemType = solutions.get(0).getProblemType();
                
                // 更新模板的问题分类字段
                templateModel.setProblemType(primaryProblemType);
                templateModel.setProblemTypeDate(Timestamp.valueOf(LocalDateTime.now()));
                
                log.info("模板[{}]问题分类处理完成，主要问题分类: {}, 共找到{}个解决方案", 
                    templateModel.getId(), primaryProblemType, solutions.size());
            } else {
                log.warn("模板[{}]未找到匹配的问题分类，错误信息: {}", templateModel.getId(), errorMessage);
            }
        } catch (Exception e) {
            log.error("模板[{}]问题分类处理异常，错误信息: {}", templateModel.getId(), errorMessage, e);
        }
    }

    /**
     * 处理FeedTask的问题分类
     * 将第一个问题分类写入attribute11，所有问题分类信息写入attribute9
     * 
     * @param feedTask 任务对象
     * @param errorMessage 错误信息
     * @param operationType 操作类型（PUBLISH、UPDATE等）
     */
    public void handleFeedTaskProblemClassification(FeedTask feedTask, String errorMessage, String operationType) {
        if (feedTask == null || StringUtils.isBlank(errorMessage)) {
            log.warn("FeedTask或错误信息为空，跳过问题分类处理");
            return;
        }

        try {
            // 调用错误解决方案服务分析问题
            ErrorSolutionResult solutionResult = ozonErrorSolutionService.findSolutionForError(errorMessage, operationType);
            
            if (solutionResult.isSuccess() && !solutionResult.getSolutions().isEmpty()) {
                List<SolutionItem> solutions = solutionResult.getSolutions();
                
                // 获取第一个问题分类写入attribute11
                String primaryProblemType = solutions.get(0).getProblemType();
                feedTask.setAttribute11(primaryProblemType);
                
                // 构建所有问题分类信息的JSON，写入attribute9
                Map<String, Object> problemClassificationInfo = buildProblemClassificationInfo(solutions);
                feedTask.setAttribute12(JSON.toJSONString(problemClassificationInfo));
            } else {
                log.warn("FeedTask[{}]未找到匹配的问题分类，错误信息: {}", feedTask.getId(), errorMessage);
            }
        } catch (Exception e) {
            log.error("FeedTask[{}]问题分类处理异常，错误信息: {}", feedTask.getId(), errorMessage, e);
        }
    }

    /**
     * 构建问题分类信息的数据结构
     * 
     * @param solutions 解决方案列表
     * @return 问题分类信息Map
     */
    private Map<String, Object> buildProblemClassificationInfo(List<SolutionItem> solutions) {
        Map<String, Object> info = new HashMap<>();
        
        // 记录处理时间
        info.put("processTime", LocalDateTime.now().toString());
        
        // 记录主要问题分类
        if (!solutions.isEmpty()) {
            info.put("primaryProblemType", solutions.get(0).getProblemType());
        }
        
        // 记录所有问题分类
        List<String> allProblemTypes = solutions.stream()
            .map(SolutionItem::getProblemType)
            .distinct()
            .collect(Collectors.toList());
        info.put("allProblemTypes", allProblemTypes);
        
        // 记录解决方案数量
        info.put("solutionCount", solutions.size());
        
        // 记录详细的问题分类信息
        List<Map<String, Object>> detailSolutions = solutions.stream()
            .map(this::buildSolutionDetail)
            .collect(Collectors.toList());
        info.put("solutions", detailSolutions);
        
        return info;
    }

    /**
     * 构建单个解决方案的详细信息
     * 
     * @param solution 解决方案项
     * @return 解决方案详细信息Map
     */
    private Map<String, Object> buildSolutionDetail(SolutionItem solution) {
        Map<String, Object> detail = new HashMap<>();
        detail.put("id", solution.getId());
        detail.put("problemType", solution.getProblemType());
        detail.put("solutionType", solution.getSolutionType());
        // 不包含problemReport以节省空间
        return detail;
    }
} 