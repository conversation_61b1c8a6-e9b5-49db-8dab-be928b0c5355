package com.estone.erp.publish.ozon.excel.handler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.elasticsearch.util.ElasticSearchHelper;
import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsOzonItemRequest;
import com.estone.erp.publish.ozon.excel.handler.model.ListingWeightInfo;
import com.estone.erp.publish.ozon.model.vo.OzonListingVO;
import com.estone.erp.publish.ozon.service.OzonEsItemService;
import com.estone.erp.publish.platform.enums.SmallPlatformDownloadEnums;
import com.estone.erp.publish.platform.model.SmallPlatformExcelDownloadLog;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.esProduct.bean.Packingmaterial;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.util.SingleItemEsUtils;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * listing handler
 * <AUTHOR>
 */
@Component
public class ListingHandler implements DownloadHandler{
    @Autowired
    private OzonEsItemService esItemService;
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate3;

    @Override
    public SmallPlatformDownloadEnums.Type getType() {
        return SmallPlatformDownloadEnums.Type.LISTING;
    }

    @Override
    public void writeFile(SmallPlatformExcelDownloadLog downloadLog, File file) {
        DataContextHolder.setUsername(downloadLog.getCreateBy());
        EsOzonItemRequest request = JSON.parseObject(downloadLog.getQueryCondition(), EsOzonItemRequest.class);
        List<String> downloadFiles = request.getDownloadFiles();
        if (CollectionUtils.isEmpty(downloadFiles)) {
            request.setDownloadFiles(new ArrayList<>());
        }
        downloadFiles.add("sku");
        downloadFiles.add("skuDataSource");
        ExcelWriter excelWriter = EasyExcel.write(file, OzonListingVO.class).includeColumnFiledNames(request.getDownloadFiles()).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
        Set<String> accounts = Sets.newHashSet();
        int page = 0;
        int limit = 1000;
        int total = 0;

        while (true) {
            request.setPageIndex(page);
            request.setPageSize(limit);
            PageInfo<OzonListingVO> pageInfo = esItemService.searchListing(request);
            List<OzonListingVO> contents = pageInfo.getContents();
            if (CollectionUtils.isEmpty(contents)) {
                break;
            }

            List<String> skuList = contents.stream().map(OzonListingVO::getSku).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            List<ListingWeightInfo> ozonExport = getOzonExport(skuList);
            Map<String, ListingWeightInfo> skuAndWeightInfoMap = ozonExport.stream().collect(Collectors.toMap(ListingWeightInfo::getSku, a -> a, (oldV, newV) -> newV));

            for (OzonListingVO content : contents) {
                Double skuSaleCostPrice = ProductUtils.getSkuSaleCostPrice(content.getSku(), content.getSkuDataSource());
                content.setSaleCostPrice(skuSaleCostPrice == null ? "" : skuSaleCostPrice.toString());
                if (CollectionUtils.isNotEmpty(content.getWareHouseInfoVOList())) {
                    content.setWareHouseInfoVOStr(JSON.toJSONString(content.getWareHouseInfoVOList()));
                }
                ListingWeightInfo listingWeightInfo = skuAndWeightInfoMap.get(content.getSku());
                if (listingWeightInfo != null) {
                    content.setMatchWeight(listingWeightInfo.getMatchWeight());
                    content.setPackageWeight(listingWeightInfo.getPackageWeight());
                    content.setPackingPrice(listingWeightInfo.getPackingPrice());
                    content.setPackingWeight(listingWeightInfo.getPackingWeight());
                    content.setProductWeight(listingWeightInfo.getProductWeight());
                    content.setStandardWeight(listingWeightInfo.getStandardWeight());
                }
                accounts.add(content.getAccountNumber());
            }
            excelWriter.write(contents, writeSheet);
            total += contents.size();
            if (total == pageInfo.getTotal() || total > 500000) {
                break;
            }
            page++;
        }
        if (CollectionUtils.isNotEmpty(accounts)) {
            if (accounts.size() > 50) {
                accounts = accounts.stream().limit(50).collect(Collectors.toSet());
            }
            downloadLog.setAccountNumber(StringUtils.join(accounts,","));
        }
        downloadLog.setDownloadCount(total);
        excelWriter.finish();
    }

    public List<ListingWeightInfo> getOzonExport(List<String> skus) {
        BoolQueryBuilder sonBoolQueryBuilder = new BoolQueryBuilder();
        NativeSearchQueryBuilder sonSkuBuilder = new NativeSearchQueryBuilder();
        sonSkuBuilder.withFields("mainSku", "sonSku", "standardWeight", "productWeight", "packageWeight", "packingMaterialsCode", "matchMaterialsCode");
        sonBoolQueryBuilder.must(QueryBuilders.termsQuery("sonSku.keyword", skus));
        NativeSearchQuery searchQuery = sonSkuBuilder.withQuery(sonBoolQueryBuilder).build();
        searchQuery.setTrackTotalHits(true);
        List<SingleItemEs> singleitemes = ElasticSearchHelper.
                scrollQuery(elasticsearchRestTemplate3, 60000L,
                        searchQuery, SingleItemEs.class,
                        IndexCoordinates.of("singleitemes"));

        List<ListingWeightInfo> list = new ArrayList<>();
        for (SingleItemEs singleItemEs : singleitemes) {
            ListingWeightInfo listingWeightInfo = new ListingWeightInfo();
            listingWeightInfo.setSku(singleItemEs.getSonSku());
            listingWeightInfo.setProductWeight(singleItemEs.getProductWeight());
            listingWeightInfo.setPackageWeight(singleItemEs.getPackageWeight());
            listingWeightInfo.setStandardWeight(singleItemEs.getStandardWeight());

            Integer packingMaterialsCode1 = singleItemEs.getPackingMaterialsCode();
            if (packingMaterialsCode1 != null) {
                String packingMaterialsCode = SingleItemEsUtils.PRODUCT_PACKING_MATERIAL_KEY + singleItemEs.getPackingMaterialsCode();
                String packingmaterials = PublishRedisClusterUtils.get(packingMaterialsCode);
                if (StringUtils.isNotEmpty(packingmaterials)) {
                    Packingmaterial packingmaterial = JSONObject.parseObject(packingmaterials,Packingmaterial.class);
                    listingWeightInfo.setPackingWeight(BigDecimal.valueOf(null == packingmaterial.getWeight()?0.0 :packingmaterial.getWeight()));
                    listingWeightInfo.setPackingPrice(BigDecimal.valueOf(null == packingmaterial.getPurchaseprice()?0.0 :packingmaterial.getPurchaseprice()));
                }
            }
            String matchMaterialsCode1 = singleItemEs.getMatchMaterialsCode();
            if (StringUtils.isNotBlank(matchMaterialsCode1)) {
                List<String> matchMaterialsCodeList = Arrays.asList(singleItemEs.getMatchMaterialsCode().split(","));
                Double matchWeight =0.0;
                for (String s : matchMaterialsCodeList){
                    String matchMaterialsCode = SingleItemEsUtils.PRODUCT_PACKING_MATERIAL_KEY + s;
                    String packingmaterials = PublishRedisClusterUtils.get(matchMaterialsCode);
                    if (StringUtils.isNotEmpty(packingmaterials)) {
                        Packingmaterial packingmaterial = JSONObject.parseObject(packingmaterials,Packingmaterial.class);
                        matchWeight  = matchWeight + (null == packingmaterial.getWeight()?0.0 :packingmaterial.getWeight());
                    }
                }
                listingWeightInfo.setMatchWeight(BigDecimal.valueOf(matchWeight));
            }
            list.add(listingWeightInfo);
        }
        return list;
    }

}

