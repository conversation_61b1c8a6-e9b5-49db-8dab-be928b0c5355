package com.estone.erp.publish.ozon.excel.handler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.publish.ozon.model.dto.solution.OzonReportProblemMaintainQueryDto;
import com.estone.erp.publish.ozon.service.OzonReportProblemMaintainService;
import com.estone.erp.publish.platform.enums.SmallPlatformDownloadEnums;
import com.estone.erp.publish.platform.model.SmallPlatformExcelDownloadLog;
import com.estone.erp.publish.tidb.publishtidb.model.OzonReportProblemMaintain;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.List;

/**
 * ozon 处理报告问题维护导出
 */
@Slf4j
@Component
public class ReportProblemMaintainHandler implements DownloadHandler {

    @Resource
    private OzonReportProblemMaintainService ozonReportProblemMaintainService;

    @Override
    public SmallPlatformDownloadEnums.Type getType() {
        return SmallPlatformDownloadEnums.Type.REPORT_PROBLEM;
    }

    @Override
    public void writeFile(SmallPlatformExcelDownloadLog downloadLog, File temFile) {
        String queryCondition = downloadLog.getQueryCondition();

        OzonReportProblemMaintainQueryDto ozonReportProblemMaintainQueryDto = JSON.parseObject(queryCondition, OzonReportProblemMaintainQueryDto.class);

        ExcelWriter excelWriter = EasyExcel.write(temFile, OzonReportProblemMaintain.class).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
        int pageSize = 1000;
        int pageNum = 1;
        long count = 0;
        while (true) {
            ozonReportProblemMaintainQueryDto.setPageNum(pageNum);
            ozonReportProblemMaintainQueryDto.setPageSize(pageSize);
            IPage<OzonReportProblemMaintain> page = ozonReportProblemMaintainService.pageQuery(ozonReportProblemMaintainQueryDto);
            if (ObjectUtils.isEmpty(page) || CollectionUtils.isEmpty(page.getRecords())) {
                break;
            }
            count = page.getTotal();
            List<OzonReportProblemMaintain> records = page.getRecords();
            excelWriter.write(records, writeSheet);
            pageNum += 1;
        }
        excelWriter.finish();
        downloadLog.setDownloadCount(Long.valueOf(count).intValue());
    }
}
