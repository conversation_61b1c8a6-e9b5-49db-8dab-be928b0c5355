package com.estone.erp.publish.ozon.handler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.executors.ExecutorUtils;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItem;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItemOffline;
import com.estone.erp.publish.ozon.call.OzonApiClient;
import com.estone.erp.publish.ozon.call.OzonResponseResult;
import com.estone.erp.publish.ozon.call.model.request.*;
import com.estone.erp.publish.ozon.common.OzonEsItemBulkProcessor;
import com.estone.erp.publish.ozon.common.OzonExecutors;
import com.estone.erp.publish.ozon.enums.*;
import com.estone.erp.publish.ozon.service.*;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-04-06 16:58
 */
@Slf4j
@Component
public class OzonOfflineUpdateHandler {

    @Autowired
    private OzonFeedTaskService feedTaskService;
    @Autowired
    private OzonApiClient apiClient;
    @Autowired
    private OzonEsItemBulkProcessor esItemBulkProcessor;
    @Resource
    private OzonProblemClassificationHandler ozonProblemClassificationHandler;

    /**
     * 删除下降商品
     */
    public void deleteItems(String accountNumber, List<EsOzonItemOffline> items) {
        List<List<EsOzonItemOffline>> partition = Lists.partition(items, 100);
        for (List<EsOzonItemOffline> itemList : partition) {
            feedTaskService.batchOfflineItemFeeds(itemList, OzonFeedTaskEnums.TaskType.DELETE_ITEM.name());
            List<Long> productIds = itemList.stream().map(EsOzonItem::getProductId).collect(Collectors.toList());
            ExecutorUtils.execute(OzonExecutors.DELETE_ITEM_POOL, () -> {
                ArchiveRequest archiveRequest = new ArchiveRequest();
                archiveRequest.setProductIds(productIds);
                OzonResponseResult<Boolean> ozonResponseResult = apiClient.productArchive(accountNumber, archiveRequest);
                List<CompletableFuture<Map<Long, OzonResponseResult<Boolean>>>> list = new ArrayList<>();
                Map<Long, OzonResponseResult<Boolean>> singleResultMap = new HashMap<>();
                if (!ozonResponseResult.isSuccess()) {
                    // 批量不成功，就单个单个执行
                    for (Long productId : productIds) {
                        CompletableFuture<Map<Long, OzonResponseResult<Boolean>>> mapCompletableFuture = CompletableFuture.supplyAsync(() -> {
                            ArchiveRequest singleArchiveRequest = new ArchiveRequest();
                            singleArchiveRequest.setProductIds(Collections.singletonList(productId));
                            OzonResponseResult<Boolean> booleanOzonResponseResult = apiClient.productArchive(accountNumber, singleArchiveRequest);
                            Map<Long, OzonResponseResult<Boolean>> singleResult = new HashMap<>();
                            singleResult.put(productId, booleanOzonResponseResult);
                            return singleResult;
                        }, OzonExecutors.DELETE_SINGLE_ITEM_POOL);
                        list.add(mapCompletableFuture);
                    }
                    List<Map<Long, OzonResponseResult<Boolean>>> collect = list.stream().map(CompletableFuture::join).collect(Collectors.toList());
                    singleResultMap = collect.stream().flatMap(m -> m.entrySet().stream()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (o1, o2) -> o1));
                }
                List<String> ids = productIds.stream().map(String::valueOf).collect(Collectors.toList());
                List<FeedTask> feedTasks = getFeedsByAccountAndId(accountNumber, ids, OzonFeedTaskEnums.TaskType.DELETE_ITEM.name());
                if (!ozonResponseResult.isSuccess()) {
                    // 处理报告失败
                    List<Long> successProductIdList = new ArrayList<>();
                    for (FeedTask feedTask : feedTasks) {
                        String productId = feedTask.getAssociationId();
                        if (StringUtils.isBlank(productId)) {
                            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                            feedTask.setResultMsg("can not archive product result");
                            ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, feedTask.getResultMsg(), feedTask.getTaskType());
                            continue;
                        }

                        OzonResponseResult<Boolean> booleanOzonResponseResult = singleResultMap.get(Long.valueOf(productId));
                        if (booleanOzonResponseResult == null) {
                            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                            feedTask.setResultMsg("can not archive product result");
                            ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, feedTask.getResultMsg(), feedTask.getTaskType());
                            continue;
                        }

                        if (!booleanOzonResponseResult.isSuccess()) {
                            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                            feedTask.setResultMsg(JSON.toJSONString(booleanOzonResponseResult));
                            ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, feedTask.getResultMsg(), feedTask.getTaskType());
                            continue;
                        }

                        if (booleanOzonResponseResult.isSuccess()) {
                            feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                        }
                    }
                    feedTaskService.batchUpdateFeeds(feedTasks);
                    if (CollectionUtils.isNotEmpty(successProductIdList)) {
                        for (Long productId : productIds) {
                            esItemBulkProcessor.setOfflineListingOnlineAndStatus(productId.toString(), false, List.of(OzonEnums.ListVisibility.ARCHIVED.getCode()));
                        }
                    }
                    return;
                }
                if (ozonResponseResult.getResult()) {
                    for (FeedTask feedTask : feedTasks) {
                        feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                    }
                    feedTaskService.batchUpdateFeeds(feedTasks);
                    // delete item list
                    for (Long productId : productIds) {
                        esItemBulkProcessor.setOfflineListingOnlineAndStatus(productId.toString(), false, List.of(OzonEnums.ListVisibility.ARCHIVED.getCode()));
                    }
                }
            }, "delete_offline_item_pool");
        }
    }

    private List<FeedTask> getFeedsByAccountAndId(String accountNumber, List<String> associationIds, String taskType) {
        FeedTaskExample feedTaskExample = new FeedTaskExample();
        FeedTaskExample.Criteria criteria = feedTaskExample.createCriteria();
        criteria.andAccountNumberEqualTo(accountNumber);
        criteria.andTaskStatusEqualTo(FeedTaskStatusEnum.RUNNING.getTaskStatus());
        criteria.andTaskTypeEqualTo(taskType);
        criteria.andAssociationIdIn(associationIds);
        return feedTaskService.selectByExample(feedTaskExample, Platform.Ozon.name());
    }

}
