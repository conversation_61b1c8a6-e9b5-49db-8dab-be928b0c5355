package com.estone.erp.publish.ozon.service;


import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;

/**
 * ozon 新的处理报告
 */
@Service
public class OzonResultFeedTaskService {

    @Resource
    private FeedTaskService feedTaskService;

    @Resource
    private OzonProblemClassificationHandler ozonProblemClassificationHandler;

    /**
     * 操作人
     * @return default admin
     */
    public String getUsername() {
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        if (StringUtils.isBlank(currentUser)) {
            return "admin";
        }
        return currentUser;
    }

    /**
     * 更新报告为错误报告
     * @param feedTaskId feedTaskId
     * @param taskType taskType
     * @param message 错误信息
     */
    public void updateFail(Long feedTaskId, String taskType, String message) {
        FeedTask feedTask = new FeedTask();
        feedTask.setId(feedTaskId);
        feedTask.setTaskType(taskType);
        feedTask.setResultMsg(message);
        updateFail(feedTask);
    }

    /**
     * 更新报告为错误报告
     * @param feedTask feedTask
     */
    public void updateFail(FeedTask feedTask) {
        feedTask.setPlatform(SaleChannel.CHANNEL_OZON);
        feedTask.setTableIndex();
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));

        String resultMsg = feedTask.getResultMsg();
        if (StringUtils.isNotBlank(resultMsg)) {
            ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, resultMsg, feedTask.getTaskType());
        }
        feedTaskService.updateByPrimaryKeySelective(feedTask);
    }

    /**
     * 保存错误消息
     * @param taskType
     * @param message
     */
    public void saveFail(String taskType, String message) {
        FeedTask feedTask = new FeedTask();
        feedTask.setTaskType(taskType);
        feedTask.setResultMsg(message);
        saveFail(feedTask);
    }

    /**
     * 保存错误信息
     * @param feedTask
     */
    public void saveFail(FeedTask feedTask) {
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        feedTask.setPlatform(SaleChannel.CHANNEL_OZON);
        feedTask.setTableIndex();
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
        feedTask.setFinishTime(timestamp);
        feedTask.setCreatedBy(getUsername());
        feedTask.setCreateTime(timestamp);

        String resultMsg = feedTask.getResultMsg();
        if (StringUtils.isNotBlank(resultMsg)) {
            ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, resultMsg, feedTask.getTaskType());
        }
        feedTaskService.insertSelective(feedTask);
    }



}
