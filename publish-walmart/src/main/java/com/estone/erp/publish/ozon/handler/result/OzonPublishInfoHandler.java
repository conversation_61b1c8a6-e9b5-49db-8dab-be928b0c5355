package com.estone.erp.publish.ozon.handler.result;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.redis.config.RedisClusterTemplate;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.ozon.call.model.response.ProductImportInfoResponse;
import com.estone.erp.publish.ozon.enums.OzonFeedTaskEnums;
import com.estone.erp.publish.ozon.enums.OzonPublishProcessEnums;
import com.estone.erp.publish.ozon.enums.OzonTemplateEnums;
import com.estone.erp.publish.ozon.handler.OzonUpdateHandler;
import com.estone.erp.publish.ozon.handler.result.template.AbstractOzonUpdateInfoHandler;
import com.estone.erp.publish.ozon.model.OzonPublishProcess;
import com.estone.erp.publish.ozon.model.OzonTemplateModel;
import com.estone.erp.publish.ozon.model.dto.OzonUpdateDO;
import com.estone.erp.publish.ozon.model.dto.template.OzonSkuDO;
import com.estone.erp.publish.ozon.service.OzonAdminTemplateService;
import com.estone.erp.publish.ozon.service.OzonEsItemService;
import com.estone.erp.publish.ozon.service.OzonProblemClassificationHandler;
import com.estone.erp.publish.ozon.service.OzonTemplateModelService;
import com.estone.erp.publish.ozon.utils.OzonPublishUtils;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * ozon 刊登
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OzonPublishInfoHandler extends AbstractOzonUpdateInfoHandler {

    @Resource
    private OzonTemplateModelService ozonTemplateModelService;
    @Resource
    private OzonAdminTemplateService ozonAdminTemplateService;
    @Resource
    private OzonUpdateHandler updateHandler;
    @Resource
    private OzonEsItemService ozonEsItemService;
    @Resource
    private RedisClusterTemplate redisClusterTemplate;
    @Resource
    private OzonProblemClassificationHandler problemClassificationHandler;

    @Override
    public void afterSuccessHandler(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        // 上传产品多了个状态，要上传库存
        ozonPublishProcess.setStatus(OzonPublishProcessEnums.Status.UPLOAD_STOCK.getCode());
        ozonPublishProcessService.updateByPrimaryKeySelective(ozonPublishProcess);
        // 1.更新模板状态
        OzonTemplateModel templateModel = ozonTemplateModelService.getPublishDataById(ozonPublishProcess.getTemplateId());
        if (templateModel == null) {
            return;
        }
        templateModel.setPublishStatus(OzonTemplateEnums.PublishStatus.SUCCESS.getCode());
        templateModel.setInventoryUpload(OzonTemplateEnums.InventoryStatus.WAIT_UPLOAD.getCode());
        templateModel.setUpdatedTime(now);
        ozonTemplateModelService.updateByPrimaryKeySelective(templateModel);
        // 更新处理报告
        Long feedTaskId = ozonPublishProcess.getFeedTaskId();
        FeedTask feedTask = new FeedTask();
        feedTask.setId(feedTaskId);
        ozonFeedTaskService.succeedTask(feedTask, "success");
        // 2.上传库存
        uploadStock(ozonPublishProcess, items);
        // 3.生成admin范本
        ozonAdminTemplateService.createAdminTemplate(templateModel.getId());
        // 4. 同步数据
        // 按照sku 把数据同步过去
        sync(ozonPublishProcess.getAccountnumber(), items);
    }

    @Override
    public void afterFailHandler(OzonPublishProcess publishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        Timestamp now = new Timestamp(System.currentTimeMillis());
        String message = parseError(items);
        // 上传失败,异常 更新模板状态
        OzonTemplateModel templateModel = ozonTemplateModelService.getPublishDataById(publishProcess.getTemplateId());
        if (templateModel != null) {
            templateModel.setPublishStatus(OzonTemplateEnums.PublishStatus.ERROR.getCode());
            templateModel.setUpdatedTime(now);
            problemClassificationHandler.handleTemplateProblemClassification(templateModel, message, OzonFeedTaskEnums.TaskType.PUBLISH.name());

            ozonTemplateModelService.updateByPrimaryKeySelective(templateModel);
        }
        // 更新处理报告
        ozonFeedTaskService.updatePublishFail(publishProcess.getFeedTaskId(), publishProcess.getTemplateId(), publishProcess.getAccountnumber(), message);

        // 判断当前时间是否为8点，是的话就跳过下列操作
        LocalDateTime localDateTime = LocalDateTime.now();
        int hour = localDateTime.getHour();
        if (hour == 8) {
            return;
        }

        // 如果当前时间小于8点，使用前一天的日期
        LocalDate localDate = hour < 8 ?
                localDateTime.minusDays(1).toLocalDate() :
                localDateTime.toLocalDate();
        String day = localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        // 检查是否达到每日创建限制
        boolean isDailyLimitExceeded = items.stream()
                .flatMap(item -> item.getErrors().stream())
                .filter(Objects::nonNull)
                .map(ProductImportInfoResponse.ErrorText::getCode)
                .filter(StringUtils::isNotBlank)
                .anyMatch("DAILY_CREATE_LIMIT_EXCEEDED"::equalsIgnoreCase);
        if (isDailyLimitExceeded) {
            String accountNumber = publishProcess.getAccountnumber();
            redisClusterTemplate.set(
                    RedisConstant.DAILY_CREATE_LIMIT_EXCEEDED + accountNumber + ":" + day,
                    1,
                    1,
                    TimeUnit.DAYS
            );
        }
    }

    /**
     * 部分刊登成功，按照成功处理 因为就只有一个处理报告，所以不需要区分成功失败
     *
     * @param ozonPublishProcess 处理结果
     * @param items              item 信息
     */
    @Override
    public void afterHalfSuccessHandler(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        // 按照成功处理
        this.afterSuccessHandler(ozonPublishProcess, items);
    }

    /**
     * 重写超时，对业务处理
     *
     * @param ozonPublishProcess
     */
    @Override
    public void afterTimeoutHandler(OzonPublishProcess ozonPublishProcess) {
        Integer templateId = ozonPublishProcess.getTemplateId();
        if (templateId != null) {
            try {
                // 1.更新模板状态
                OzonTemplateModel templateModel = ozonTemplateModelService.getPublishDataById(ozonPublishProcess.getTemplateId());
                if (templateModel == null) {
                    return;
                }
                if (templateModel.getPublishStatus().equals(OzonTemplateEnums.PublishStatus.PUBLISHING.getCode())) {
                    templateModel.setPublishStatus(OzonTemplateEnums.PublishStatus.ERROR.getCode());
                    templateModel.setUpdatedTime(new Timestamp(System.currentTimeMillis()));

                    problemClassificationHandler.handleTemplateProblemClassification(templateModel, "timeout", OzonFeedTaskEnums.TaskType.PUBLISH.name());
                    ozonTemplateModelService.updateByPrimaryKeySelective(templateModel);
                    // 修改item更新处理报告
                    if (ozonPublishProcess.getFeedTaskId() != null) {
                        FeedTask feedTask = new FeedTask();
                        feedTask.setPlatform(Platform.Ozon.name());
                        feedTask.setResultMsg("timeout");
                        feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                        feedTask.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
                        feedTask.setTableIndex();
                        feedTask.setFinishTime(Timestamp.valueOf(LocalDateTime.now()));

                        // 进行问题分类分析 - 针对超时失败的FeedTask
                        problemClassificationHandler.handleFeedTaskProblemClassification(feedTask, "timeout", feedTask.getTaskType());
                        ozonFeedTaskService.batchUpdateFeeds(Collections.singletonList(feedTask));
                    } else {
                        ozonFeedTaskService.updateUpdateItemFailTask(ozonPublishProcess.getAccountnumber(), ozonPublishProcess.getTaskId(), "timeout", ozonPublishProcess.getTaskType());
                    }
                }
            } catch (Exception e) {
                log.error("确认刊登状态异常:process:{}", JSON.toJSONString(ozonPublishProcess), e);
            }
        }
    }

    @Override
    public String taskType() {
        return OzonFeedTaskEnums.TaskType.PUBLISH.name();
    }

    private void uploadStock(OzonPublishProcess publishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        try {
            List<OzonSkuDO> variantSku = ozonTemplateModelService.getVariantSku(publishProcess.getTemplateId());
            if (CollectionUtils.isEmpty(variantSku)) {
                return;
            }
            List<OzonUpdateDO> updateDOList = OzonPublishUtils.getUploadStockInfo(publishProcess, items, variantSku, "editListing");
            if (CollectionUtils.isNotEmpty(updateDOList)) {
                OzonTemplateModel templateModel = new OzonTemplateModel();
                templateModel.setId(publishProcess.getTemplateId());
                templateModel.setInventoryUpload(OzonTemplateEnums.InventoryStatus.UPLOADING.getCode());
                templateModel.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
                ozonTemplateModelService.updateByPrimaryKeySelective(templateModel);
                // 处理库存请求
                updateHandler.updateTemplateStock(publishProcess.getAccountnumber(), publishProcess.getTemplateId(), updateDOList);
            }
        } catch (Exception e) {
            log.error("上传库存异常, process:{}", JSON.toJSONString(publishProcess), e);
        }
    }

    private void sync(String accountNumber, List<ProductImportInfoResponse.ProductInfo> items) {
        List<String> collect = items.stream().map(ProductImportInfoResponse.ProductInfo::getOfferId).filter(Objects::nonNull).collect(Collectors.toList());
        ozonEsItemService.syncProductInfoWithSellerSkuFeed(accountNumber, collect);
    }

}
