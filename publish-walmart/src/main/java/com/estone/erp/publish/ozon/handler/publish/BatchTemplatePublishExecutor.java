package com.estone.erp.publish.ozon.handler.publish;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.ozon.common.cardcode.OzonCardCodeHelper;
import com.estone.erp.publish.ozon.enums.OzonFeedTaskEnums;
import com.estone.erp.publish.ozon.enums.OzonPublishProcessEnums;
import com.estone.erp.publish.ozon.enums.OzonTemplateEnums;
import com.estone.erp.publish.ozon.handler.publish.parm.AutoPublishMessage;
import com.estone.erp.publish.ozon.handler.publish.parm.BatchPublishParam;
import com.estone.erp.publish.ozon.model.OzonAccountConfig;
import com.estone.erp.publish.ozon.model.OzonPublishProcess;
import com.estone.erp.publish.ozon.model.OzonTemplateModel;
import com.estone.erp.publish.ozon.model.dto.OzonBatchPublishRequest;
import com.estone.erp.publish.ozon.model.dto.OzonCalcPriceResponse;
import com.estone.erp.publish.ozon.model.dto.template.OzonSkuDO;
import com.estone.erp.publish.ozon.mq.OzonMqConfig;
import com.estone.erp.publish.ozon.service.OzonAccountConfigService;
import com.estone.erp.publish.ozon.utils.OzonTemplateDataUtil;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 模板批量刊登
 *
 * <AUTHOR>
 * @date 2023-07-12 11:57
 */
@Slf4j
@Component
public class BatchTemplatePublishExecutor extends PublishExecutor<BatchPublishParam> {
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private OzonAccountConfigService ozonAccountConfigService;
    @Autowired
    private OzonCardCodeHelper cardCodeHelper;

    @Override
    protected OzonTemplateModel getTemplateData(BatchPublishParam param) throws BusinessException {
        DataContextHolder.setUsername(param.getUser());
        OzonTemplateModel templateModel = ozonTemplateModelService.selectByPrimaryKey(param.getTemplateId());
        if (templateModel == null) {
            failFeedTask(param.getTemplateId(), param.getAccountNumber(), "", "批量刊登查询模板数据为空", "", param.getRuleName(),
                    null, null);
            return null;
        }
        try {
            validationTemplate(templateModel, param);
            // 保存新的刊登数据
            templateModel.setPublishType(OzonTemplateEnums.PublishType.NORMAL.getCode());
            templateModel.setPublishRole(OzonTemplateEnums.PublishRole.SALE_MAN.getCode());
            templateModel.setPublishStatus(OzonTemplateEnums.PublishStatus.PUBLISHING.getCode());
            templateModel.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
            ozonTemplateModelService.updateByPrimaryKeySelective(templateModel);
            return templateModel;
        } catch (Exception e) {
            failFeedTask(param.getTemplateId(), param.getAccountNumber(), templateModel.getArticleNumber(), e.getMessage(), OzonTemplateDataUtil.getFirstSellerSku(templateModel), param.getRuleName(), null, null);
            templateModel.setPublishStatus(OzonTemplateEnums.PublishStatus.ERROR.getCode());
            templateModel.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
            ozonTemplateModelService.updateByPrimaryKeySelective(templateModel);
            return null;
        } finally {
            try {
                templateValidation.deleteLock(templateModel.getAccountNumber(), templateModel.getArticleNumber());
            } catch (Exception e) {
                log.error("删除锁失败" + JSON.toJSONString(param));
            }
        }
    }

    @Override
    protected OzonPublishProcess initPublishProcess(BatchPublishParam param) throws BusinessException {
        OzonPublishProcess publishProcess = new OzonPublishProcess();
        publishProcess.setTemplateId(param.getTemplateId());
        publishProcess.setStatus(OzonPublishProcessEnums.Status.INIT_TEMPLATE.getCode());
        publishProcess.setTaskId(null);
        publishProcess.setIsSuccess(true);
        publishProcess.setPublishType(OzonPublishProcessEnums.PublishType.BATCH_PUBLISH.getCode());
        publishProcess.setPublishRole(OzonTemplateEnums.PublishRole.SALE_MAN.getCode());
        publishProcess.setUser(param.getUser());
        publishProcess.setRemarks(null);
        publishProcess.setTaskType(OzonFeedTaskEnums.TaskType.PUBLISH.name());
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        publishProcess.setCreatedTime(now);
        publishProcess.setUpdatedTime(now);
        publishProcessService.insert(publishProcess);
        return publishProcess;
    }

    /**
     * 批量刊登数据校验
     * 校验店铺、标题是否与原模板一致,
     * 重复刊登
     *
     * @param templateModel 刊登模板
     * @param param         刊登参数
     */
    private void validationTemplate(OzonTemplateModel templateModel, BatchPublishParam param) {
        String title = param.getTitle();
        String accountNumber = param.getAccountNumber();
        if (!title.equals(templateModel.getTitle())) {
            String newTitle = OzonTemplateDataUtil.removeTitleDuplicateWord(title);
            // 模板标题不一致,  使用参数标题.
            templateModel.setTitle(newTitle);
        }
        // 停产、存档、废弃、禁售过滤
        templateValidation.filterForbiddenItemStatus(templateModel);
        if (!StringUtils.equals(accountNumber, templateModel.getAccountNumber())) {
            // 店铺不一致替换店铺数据, 重新算价刊登
            resetTemplateAccountData(templateModel, param);
        }
        // 重复刊登拦截
        templateValidation.validateRepeatPublish(templateModel);
        // 侵权词过滤
        templateValidation.filterInfringementWord(templateModel);
        // 每日刊登限制拦截
        templateValidation.validateDailyPublishLimit(templateModel);
        // 翻译
        String translateTitle = OzonTemplateDataUtil.translate(templateModel.getTitle());
        String removedTitleDuplicateWord = OzonTemplateDataUtil.removeTitleDuplicateWord(translateTitle);
        String translateDescription = OzonTemplateDataUtil.translate(templateModel.getDescription());
        templateModel.setTitle(removedTitleDuplicateWord);
        templateModel.setDescription(translateDescription);
    }

    /**
     * 根据店铺配置重新设置sku信息
     *
     * @param templateModel 模板
     * @param param         刊登参数
     */
    private void resetTemplateAccountData(OzonTemplateModel templateModel, BatchPublishParam param) {
        String variantData = templateModel.getVariantData();
        List<OzonSkuDO> ozonSkuDOS = JSON.parseArray(variantData, OzonSkuDO.class);
        if (CollectionUtils.isEmpty(ozonSkuDOS)) {
            throw new BusinessException("无可刊登sku");
        }

        OzonAccountConfig accountConfig = ozonAccountConfigService.selectConfigWithCalePriceRule(param.getAccountNumber());
        if (accountConfig == null) {
            throw new BusinessException("店铺配置为空");
        }
        templateModel.setCurrencyCode(accountConfig.getCurrency());
        templateModel.setAccountNumber(accountConfig.getAccountNumber());

        List<String> skus = ozonSkuDOS.stream().map(OzonSkuDO::getSku).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<ProductInfo> productInfos = ProductUtils.findProductInfos(skus);
        Map<String, ProductInfo> productInfoMap = productInfos.stream().collect(Collectors.toMap(ProductInfo::getSonSku, Function.identity(), (o1, o2) -> o1));
        for (OzonSkuDO ozonSkuDO : ozonSkuDOS) {
            ProductInfo productInfo = productInfoMap.get(ozonSkuDO.getSku());
            if (productInfo == null) {
                throw new BusinessException(ozonSkuDO.getSku() + ",子货号产品信息为空");
            }
            ozonSkuDO.setProductTag(productInfo.getEnTag());
            ozonSkuDO.setSpecialTypeList(productInfo.getSpecialTypeList());
        }
        List<OzonCalcPriceResponse> ozonCalcPriceResponses = OzonTemplateDataUtil.calcPrice(ozonSkuDOS, param.getGrossProfitRate(), accountConfig);
        Map<String, OzonCalcPriceResponse> calcPriceResponseMap = ozonCalcPriceResponses.stream().collect(Collectors.toMap(OzonCalcPriceResponse::getBusinessId, Function.identity(), (o1, o2) -> o1));

        List<String> cardCodes = new ArrayList<>();
        if (StringUtils.isNotBlank(accountConfig.getEanPrefix())) {
            List<String> eanCardCodes = cardCodeHelper.generateEANCardCodes(accountConfig.getEanPrefix(), ozonSkuDOS.size());
            cardCodes.addAll(eanCardCodes);
        }
        for (int i = 0; i < ozonSkuDOS.size(); i++) {
            OzonSkuDO ozonSkuDO = ozonSkuDOS.get(i);
            // 重新设置 seller_sku, 仓库, 价格, EAN
            ozonSkuDO.setSellerSku(ozonSkuDO.getSku() + "_" + accountConfig.getSkuSuffix());
            ozonSkuDO.setWarehouseId(accountConfig.getUpdateStockWarehouseId());
            ozonSkuDO.setQuantity(accountConfig.getDefaultStock());
            OzonCalcPriceResponse ozonCalcPriceResponse = calcPriceResponseMap.get(ozonSkuDO.getSku());
            if (ozonCalcPriceResponse == null) {
                throw new BusinessException(ozonSkuDO.getSku() + ",算价失败." + JSON.toJSONString(ozonCalcPriceResponses));
            }
            ozonSkuDO.setSalePrice(ozonCalcPriceResponse.getSalePrice());
            ozonSkuDO.setPrice(ozonCalcPriceResponse.getPrice());
            if (cardCodes.size() - 1 >= i) {
                String cardCode = cardCodes.get(i);
                ozonSkuDO.setBarcode(cardCode);
            }
        }
        templateModel.setVariantData(JSON.toJSONString(ozonSkuDOS));
        templateModel.setSkuSize(ozonSkuDOS.size());
    }

    /**
     * 添加到自动刊登队列
     *
     * @param batchPublishRequester 批量刊登
     */
    public void addAutoPublishQueue(List<OzonBatchPublishRequest> batchPublishRequester) {
        batchPublishRequester.forEach(OzonBatchPublishRequest::validationData);
        List<Integer> ids = batchPublishRequester.stream().map(OzonBatchPublishRequest::getTemplateId).collect(Collectors.toList());
        List<Integer> status = Arrays.asList(OzonTemplateEnums.PublishStatus.PUBLISHING.getCode(), OzonTemplateEnums.PublishStatus.SUCCESS.getCode());
        List<Integer> publishedIds = ozonTemplateModelService.listIdByIdAndPublishStatus(ids, status);
        batchPublishRequester.removeIf(request -> publishedIds.contains(request.getTemplateId()));
        if (CollectionUtils.isEmpty(batchPublishRequester)) {
            throw new BusinessException("无可刊登数据");
        }
        for (OzonBatchPublishRequest request : batchPublishRequester) {
            try {
                // 发送消息到MQ
                createAndSendMessage(request);
            } catch (Exception e) {
                log.error("批量刊登异常：param:{}", JSON.toJSONString(request), e);
            }
        }
    }

    private void createAndSendMessage(OzonBatchPublishRequest request) {
        BatchPublishParam publishParam = new BatchPublishParam();
        publishParam.setUser(WebUtils.getUserName());
        publishParam.setType(OzonPublishProcessEnums.PublishType.BATCH_PUBLISH.getCode());
        publishParam.setTemplateId(request.getTemplateId());
        publishParam.setAccountNumber(request.getAccountNumber());
        publishParam.setTitle(request.getTitle());
        publishParam.setGrossProfitRate(request.getGrossProfitRate());

        AutoPublishMessage publishMessage = new AutoPublishMessage(OzonPublishProcessEnums.PublishType.BATCH_PUBLISH.getCode(), JSON.toJSONString(publishParam));
        rabbitTemplate.convertAndSend(OzonMqConfig.OZON_API_DIRECT_EXCHANGE, OzonMqConfig.OZON_AUTO_PUBLISH_QUEUE_KEY, publishMessage);
        // 刊登中
        OzonTemplateModel updateTemplate = new OzonTemplateModel();
        updateTemplate.setId(request.getTemplateId());
        updateTemplate.setPublishType(OzonTemplateEnums.PublishType.NORMAL.getCode());
        updateTemplate.setPublishRole(OzonTemplateEnums.PublishRole.SALE_MAN.getCode());
        updateTemplate.setPublishStatus(OzonTemplateEnums.PublishStatus.PUBLISHING.getCode());
        updateTemplate.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
        ozonTemplateModelService.updateByPrimaryKeySelective(updateTemplate);
    }
}
