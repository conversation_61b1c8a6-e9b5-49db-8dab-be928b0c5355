package com.estone.erp.publish.ozon.handler.result.template;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.ozon.call.model.response.ProductImportInfoResponse;
import com.estone.erp.publish.ozon.handler.OzonSyncListingHandler;
import com.estone.erp.publish.ozon.handler.common.ExtendJsonDto;
import com.estone.erp.publish.ozon.model.OzonPublishProcess;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量Ozon更新信息处理器
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class BatchAbstractOzonUpdateInfoHandler extends AbstractOzonUpdateInfoHandler {
    @Resource
    protected OzonSyncListingHandler ozonSyncListingHandler;

    @Override
    public void afterSuccessHandler(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        // 处理报告无论如何，只要有id就行。
        List<FeedTask> feedTasks = getFeedTasks(ozonPublishProcess, items);
        List<FeedTask> updateList = new ArrayList<>();
        for (FeedTask feedTask : feedTasks) {
            FeedTask updateInfo = new FeedTask();
            updateInfo.setPlatform(Platform.Ozon.name());
            updateInfo.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
            updateInfo.setTableIndex();
            updateInfo.setId(feedTask.getId());
            updateInfo.setTaskType(feedTask.getTaskType());
            updateInfo.setFinishTime(Timestamp.valueOf(LocalDateTime.now()));
            updateList.add(updateInfo);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            ozonFeedTaskService.batchUpdateFeeds(updateList);
        }

        try {
            syncInfo(ozonPublishProcess, items);
        } catch (Exception e) {
            XxlJobLogger.log("syncInfo error, error:{}", e.getMessage());
        }
    }

    @Override
    public void afterFailHandler(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        Map<String, String> sellerSkuAndErrorMsgMap = parseErrorBySellerSku(items);
        // 全部失败的消息，应该要区出对应的sellerSku分来着，但是这里没有处理，所以就用全部失败的消息吧
        List<FeedTask> feedTasks = getFeedTasks(ozonPublishProcess, items);
        if (CollectionUtils.isEmpty(feedTasks)) {
            return;
        }

        List<FeedTask> updateList = new ArrayList<>();
        for (FeedTask feedTask : feedTasks) {
            String attribute1 = feedTask.getAttribute1();
            FeedTask updateInfo = new FeedTask();
            updateInfo.setPlatform(Platform.Ozon.name());
            String msg = "";
            if (StringUtils.isNotBlank(attribute1)) {
                msg = sellerSkuAndErrorMsgMap.get(attribute1);
            }
            updateInfo.setResultMsg(StringUtils.isBlank(msg) ? "失败" : msg);
            updateInfo.setTaskType(feedTask.getTaskType());
            updateInfo.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
            updateInfo.setTableIndex();
            updateInfo.setId(feedTask.getId());
            updateInfo.setFinishTime(Timestamp.valueOf(LocalDateTime.now()));

            problemClassificationHandler.handleFeedTaskProblemClassification(updateInfo, updateInfo.getResultMsg(), updateInfo.getTaskType());

            updateList.add(updateInfo);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            ozonFeedTaskService.batchUpdateFeeds(updateList);
        }
    }

    @Override
    public void afterTimeoutHandler(OzonPublishProcess ozonPublishProcess) {
        // 后续这里还得改，因为有个批量修改重量的方法
        List<FeedTask> feedTasks = getFeedTasks(ozonPublishProcess, null);
        if (CollectionUtils.isEmpty(feedTasks)) {
            return;
        }
        List<FeedTask> updateList = new ArrayList<>();
        for (FeedTask feedTask : feedTasks) {
            FeedTask updateInfo = new FeedTask();
            updateInfo.setPlatform(Platform.Ozon.name());
            updateInfo.setResultMsg("timeout");
            updateInfo.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
            updateInfo.setTableIndex();
            updateInfo.setId(feedTask.getId());
            updateInfo.setFinishTime(Timestamp.valueOf(LocalDateTime.now()));
            updateInfo.setTaskType(feedTask.getTaskType());
            problemClassificationHandler.handleFeedTaskProblemClassification(updateInfo, updateInfo.getResultMsg(), updateInfo.getTaskType());

            updateList.add(updateInfo);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            ozonFeedTaskService.batchUpdateFeeds(updateList);
        }
    }

    /**
     * 检查所有报告，如果平台没有返回对应的sellerSku ，就是要把处理报告设置成为失败
     * 批量那里要处理一下
     * @param ozonPublishProcess ozonPublishProcess
     * @param items 所有商品
     */
    @Override
    public void checkAllFeedTask(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        String extendJson = ozonPublishProcess.getExtendJson();
        List<ExtendJsonDto> extendJsonDtoList = parseFeedTaskIdList(extendJson);
        if (CollectionUtils.isEmpty(extendJsonDtoList)) {
            return;
        }
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        Set<String> existSellerSkuList = items.stream().map(ProductImportInfoResponse.ProductInfo::getOfferId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        List<ExtendJsonDto> noUpdateSellerSkuList = new ArrayList<>();
        for (ExtendJsonDto extendJsonDto : extendJsonDtoList) {
            String sellerSku = extendJsonDto.getSellerSku();
            if (existSellerSkuList.contains(sellerSku)) {
                continue;
            }
            noUpdateSellerSkuList.add(extendJsonDto);
        }
        if (CollectionUtils.isEmpty(noUpdateSellerSkuList)) {
            return;
        }

        List<FeedTask> feedTasks = new ArrayList<>();
        for (ExtendJsonDto extendJsonDto : noUpdateSellerSkuList) {
            FeedTask feedTask = new FeedTask();
            feedTask.setId(extendJsonDto.getFeedTaskId());
            feedTask.setAttribute1(extendJsonDto.getSellerSku());
            feedTask.setPlatform(Platform.Ozon.name());
            feedTask.setResultMsg("平台未返回结果");
            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
            feedTask.setFinishTime(Timestamp.valueOf(LocalDateTime.now()));
            feedTask.setTaskType(extendJsonDto.getFeedTask());
            problemClassificationHandler.handleFeedTaskProblemClassification(feedTask, feedTask.getResultMsg(), feedTask.getTaskType());

            feedTask.setTableIndex();
            feedTasks.add(feedTask);
        }
        ozonFeedTaskService.batchUpdateFeeds(feedTasks);
    }

    /**
     * 根据id获取处理报告
     *
     * @param ozonPublishProcess 处理报告
     * @return 处理报告
     */
    public List<FeedTask> getNewBatchFeedTasksById(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        String extendJson = ozonPublishProcess.getExtendJson();
        List<ExtendJsonDto> extendJsonDtoList = parseFeedTaskIdList(extendJson);
        if (CollectionUtils.isEmpty(extendJsonDtoList)) {
            XxlJobLogger.log("feedTaskIdList is empty, extendJson:{}", extendJson);
            return new ArrayList<>();
        }
        Set<String> existSellerSkuSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(items)) {
            List<String> collect = items.stream().map(ProductImportInfoResponse.ProductInfo::getOfferId).collect(Collectors.toList());
            existSellerSkuSet.addAll(collect);
        }

        // 需要sellerSku, 而且创建的时候也要处理下
        List<FeedTask> feedTasks = new ArrayList<>();
        for (ExtendJsonDto extendJsonDto : extendJsonDtoList) {
            if (existSellerSkuSet.contains(extendJsonDto.getSellerSku())) {
                FeedTask feedTask = new FeedTask();
                feedTask.setId(extendJsonDto.getFeedTaskId());
                feedTask.setAttribute1(extendJsonDto.getSellerSku());
                feedTask.setPlatform(Platform.Ozon.name());
                feedTask.setTaskType(extendJsonDto.getFeedTask());
                feedTask.setTableIndex();
                feedTasks.add(feedTask);
            }
        }
        return feedTasks;
    }

    /**
     * 解析id
     *
     * @param extendJson json字符串
     * @return id列表
     */
    protected static List<ExtendJsonDto> parseFeedTaskIdList(String extendJson) {
        if (StringUtils.isNotBlank(extendJson)) {
            try {
                List<ExtendJsonDto> extendJsonDtoList = JSON.parseArray(extendJson, ExtendJsonDto.class);
                if (CollectionUtils.isNotEmpty(extendJsonDtoList)) {
                    return extendJsonDtoList;
                }
            } catch (Exception e) {
                XxlJobLogger.log("parse extendJson error, extendJson:{}, error:{}", extendJson, e.getMessage());
            }
        }
        return List.of();
    }

    /**
     * 获取处理报告
     *
     * @param ozonPublishProcess 处理报告
     * @param items              产品信息
     * @return 处理报告
     */
    public abstract List<FeedTask> getFeedTasks(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items);

    /**
     * 同步信息
     *
     * @param ozonPublishProcess 处理报告
     * @param items              产品信息
     */
    public abstract void syncInfo(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items);

    /**
     * 同步属性信息
     *
     * @param ozonPublishProcess 处理报告
     * @param items              产品信息
     */
    public void syncAttribute(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        List<Long> collect = items.stream().map(ProductImportInfoResponse.ProductInfo::getProductId).collect(Collectors.toList());
        ozonSyncListingHandler.syncAttributes(collect, ozonPublishProcess.getAccountnumber());
    }



    /**
     * 全量同步信息
     *
     * @param ozonPublishProcess 处理报告
     * @param items              产品信息
     */
    public void syncItemInfo(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        List<Long> collect = items.stream().map(ProductImportInfoResponse.ProductInfo::getProductId).collect(Collectors.toList());
        ozonSyncListingHandler.batchSyncAndSaveItemInfo(ozonPublishProcess.getAccountnumber(), collect, Map.of());
    }

}
