package com.estone.erp.publish.ozon.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.ozon.model.dto.solution.OzonReportProblemMaintainQueryDto;
import com.estone.erp.publish.tidb.publishtidb.model.OzonReportProblemMaintain;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【aliexpress_report_problem_maintain】的数据库操作Service
* @createDate 2025-03-04 14:38:43
*/
public interface OzonReportProblemMaintainService extends IService<OzonReportProblemMaintain> {

    IPage<OzonReportProblemMaintain> pageQuery(OzonReportProblemMaintainQueryDto dto);

    String saveOrUpdateByEntity(OzonReportProblemMaintain aliexpress);

    List<String> getAllSolutionType();

    ApiResult<String> export(OzonReportProblemMaintainQueryDto dto);

}
