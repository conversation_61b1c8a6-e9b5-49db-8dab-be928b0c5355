package com.estone.erp.publish.ozon.service.impl;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.ozon.model.dto.solution.BatchSolutionResult;
import com.estone.erp.publish.ozon.model.dto.solution.ErrorSolutionResult;
import com.estone.erp.publish.ozon.model.dto.solution.SolutionItem;
import com.estone.erp.publish.ozon.service.OzonErrorSolutionService;
import com.estone.erp.publish.ozon.utils.OzonCacheReportProblemUtils;
import com.estone.erp.publish.ozon.utils.OzonSolutionUtils;
import com.estone.erp.publish.tidb.publishtidb.model.OzonReportProblemMaintain;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 处理报告内容解决方案
 */
@Slf4j
@Service
public class OzonErrorSolutionServiceImpl implements OzonErrorSolutionService {

    /**
     * 单个错误报告的解决方案查找
     *
     * @param errorReport 错误报告内容
     * @param operationType 操作类型（PUBLISH、UPDATE、DELETE等）
     * @return 解决方案建议
     */
    @Override
    public ErrorSolutionResult findSolutionForError(String errorReport, String operationType) {
        long startTime = System.currentTimeMillis();
        try {
            // 步骤1: 从缓存获取该操作类型的所有问题维护数据
            List<OzonReportProblemMaintain> problemMaintains =
                    OzonCacheReportProblemUtils.getReportProblemMaintain(operationType);

            if (problemMaintains.isEmpty()) {
                log.warn("未找到操作类型 {} 的问题维护数据", operationType);
                return ErrorSolutionResult.noSolution(errorReport, "未找到相关问题维护数据");
            }

            // 步骤2: 使用解决方案工具类进行智能匹配
            List<OzonReportProblemMaintain> matchedSolutions =
                    OzonSolutionUtils.getSolution(errorReport, problemMaintains);

            long duration = System.currentTimeMillis() - startTime;

            if (matchedSolutions.isEmpty()) {
                log.info("未找到匹配的解决方案，错误报告: {}, 耗时: {}ms", errorReport, duration);
                return ErrorSolutionResult.noSolution(errorReport, "未找到匹配的解决方案");
            }

            // 步骤3: 构建解决方案结果
            List<SolutionItem> solutions = matchedSolutions.stream()
                    .map(this::convertToSolutionItem)
                    .collect(Collectors.toList());

            log.info("成功找到 {} 个解决方案，耗时: {}ms", solutions.size(), duration);

            return ErrorSolutionResult.success(errorReport, solutions, duration);

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("查找解决方案时发生异常，错误报告: {}, 耗时: {}ms", errorReport, duration, e);
            return ErrorSolutionResult.error(errorReport, "系统异常: " + e.getMessage());
        }
    }

    /**
     * 批量错误报告的解决方案查找
     *
     * @param errorReports 错误报告列表
     * @param operationType 操作类型
     * @return 批量解决方案结果
     */
    public BatchSolutionResult findSolutionsForBatch(List<String> errorReports, String operationType) {
        log.info("开始批量查找解决方案，错误数量: {}, 操作类型: {}", errorReports.size(), operationType);
        long startTime = System.currentTimeMillis();
        try {
            // 一次性获取问题维护数据，提高批量处理效率
            List<OzonReportProblemMaintain> problemMaintains =
                    OzonCacheReportProblemUtils.getReportProblemMaintain(operationType);
            if (problemMaintains.isEmpty()) {
                log.warn("未找到操作类型 {} 的问题维护数据", operationType);
                return BatchSolutionResult.empty(errorReports.size(), "未找到相关问题维护数据");
            }

            // 批量处理每个错误报告
            List<ErrorSolutionResult> results = new ArrayList<>();
            int successCount = 0;
            int totalSolutions = 0;
            for (String errorReport : errorReports) {
                List<OzonReportProblemMaintain> matchedSolutions =
                        OzonSolutionUtils.getSolution(errorReport, problemMaintains);
                if (!matchedSolutions.isEmpty()) {
                    successCount++;
                    totalSolutions += matchedSolutions.size();

                    List<SolutionItem> solutions = matchedSolutions.stream()
                            .map(this::convertToSolutionItem)
                            .collect(Collectors.toList());

                    results.add(ErrorSolutionResult.success(errorReport, solutions, 0));
                } else {
                    results.add(ErrorSolutionResult.noSolution(errorReport, "未找到匹配的解决方案"));
                }
            }
            long duration = System.currentTimeMillis() - startTime;
            log.info("批量处理完成，总数: {}, 成功: {}, 解决方案总数: {}, 耗时: {}ms",
                    errorReports.size(), successCount, totalSolutions, duration);
            return BatchSolutionResult.success(results, duration);
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("批量查找解决方案时发生异常，耗时: {}ms", duration, e);
            return BatchSolutionResult.error(errorReports.size(), "系统异常: " + e.getMessage());
        }
    }

    /**
     * 清理指定操作类型的缓存
     *
     * @param operationType 操作类型
     */
    public void clearCache(String operationType) {
        log.info("清理操作类型 {} 的缓存", operationType);
        OzonCacheReportProblemUtils.evictCache(operationType);
    }

    /**
     * 清理所有缓存
     */
    public void clearAllCache() {
        log.info("清理所有缓存");
        OzonCacheReportProblemUtils.evictAllCache();
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public String getCacheStatistics() {
        return OzonCacheReportProblemUtils.getCacheStats();
    }

    /**
     * 转换为解决方案项
     */
    private SolutionItem convertToSolutionItem(OzonReportProblemMaintain maintain) {
        return new SolutionItem(
                maintain.getId(),
                maintain.getProblemType(),
                maintain.getReport(),
                maintain.getSolutionType()
        );
    }

    @Override
    public ApiResult<?> deleteCache() {
        clearAllCache();
        return ApiResult.newSuccess();
    }
}
