package com.estone.erp.publish.walmart.enums;

public enum WalmartFeedTaskMsgEnum {

    SYSTEM_GT_RETIRE_ITEM("system_gt_retire_item", "冠通下架系统自动下架"),

    SALE_RETIRE_ITEM("sale_retire_item", "下架产品"),

    RETIRE_MARK_OFF_ITEM("retire_mark_off_item", "标记禁售48H后系统自动下架"),

    RETIRE_LAW_FIRM_BUY_ITEM("retire_law_firm_buy_item", "律所下单SPU，系统自动下架。"),

    RETIRE_TEMPORARY_SPU("retire_temporary_spu", "临时spu下架"),

    RETIRE_BY_SKU_STATUS("retire_by_sku_status", "配置指定状态：停产存档下架"),

    RETIRE_ARCHIVED_7D_SKU("retire_archived_7d_sku", "存档7天系统自动下架"),

    RETIRE_STOP_30D_SKU("retire_stop_30d_sku", "停产30天系统自动下架"),

    RETIRE_PLATFORM_RULES_FORBID_SKU("retire_platform_rules_forbid_sku", "平台规则禁售，系统自动下架"),

    RETIRE_EXCESS_PUBLISH_SKU("retire_excess_publish_sku", "超量自动下架"),

    RETIRE_NO_SALES_SKU_BY_CREATE_TIME("retire_no_sales_sku_by_create_time", "SPU总销量为0或空，系统自动下架"),

    RETIRE_NO_SALES_SKU_BY_CREATE_TIME_180("retire_no_sales_sku_by_create_time_180", "上架时间超过180天无销量listing全部下架"),

    SKU_5_RETIRE_EXCESS_PUBLISH_SKU("sku_5_retire_excess_publish_sku", "180天内上架listing，保留最新上架的5条"),
    SKU_10_RETIRE_EXCESS_PUBLISH_SKU("sku_10_retire_excess_publish_sku", "180天内上架listing，保留最新上架的10条"),

    RETIRE_SPECIAL_SKU("retire_special_sku", "特供店铺上架非店铺配置选择的特供产品，系统自动下架"),

    RETIRE_NOT_SPECIAL_ACCOUNT_PUBLISH_SKU("retire_not_special_account_publish_sku", "非特供店铺上架特供产品，系统自动下架"),
    RETIRE_NO_SALE_SPECIAL_ACCOUNT_PUBLISH_SKU("retire_no_sale_special_account_publish_sku", "特供产品最近30天无销量系统自动下架"),
    ;

    //状态英文
    private String code;
    //状态中文
    private String msg;

    private WalmartFeedTaskMsgEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
