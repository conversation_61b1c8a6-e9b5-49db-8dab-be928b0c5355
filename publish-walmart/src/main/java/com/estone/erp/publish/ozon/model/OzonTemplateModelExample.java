package com.estone.erp.publish.ozon.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OzonTemplateModelExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    // 自定义查询字段
    private String columns;

    public OzonTemplateModelExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public String getColumns() {
        return columns;
    }

    public void setColumns(String columns) {
        this.columns = columns;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNull() {
            addCriterion("account_number is null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIsNotNull() {
            addCriterion("account_number is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNumberEqualTo(String value) {
            addCriterion("account_number =", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotEqualTo(String value) {
            addCriterion("account_number <>", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThan(String value) {
            addCriterion("account_number >", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberGreaterThanOrEqualTo(String value) {
            addCriterion("account_number >=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThan(String value) {
            addCriterion("account_number <", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLessThanOrEqualTo(String value) {
            addCriterion("account_number <=", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberLike(String value) {
            addCriterion("account_number like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotLike(String value) {
            addCriterion("account_number not like", value, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberIn(List<String> values) {
            addCriterion("account_number in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotIn(List<String> values) {
            addCriterion("account_number not in", values, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberBetween(String value1, String value2) {
            addCriterion("account_number between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andAccountNumberNotBetween(String value1, String value2) {
            addCriterion("account_number not between", value1, value2, "accountNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNull() {
            addCriterion("article_number is null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIsNotNull() {
            addCriterion("article_number is not null");
            return (Criteria) this;
        }

        public Criteria andArticleNumberEqualTo(String value) {
            addCriterion("article_number =", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotEqualTo(String value) {
            addCriterion("article_number <>", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThan(String value) {
            addCriterion("article_number >", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberGreaterThanOrEqualTo(String value) {
            addCriterion("article_number >=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThan(String value) {
            addCriterion("article_number <", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLessThanOrEqualTo(String value) {
            addCriterion("article_number <=", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberLike(String value) {
            addCriterion("article_number like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotLike(String value) {
            addCriterion("article_number not like", value, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberIn(List<String> values) {
            addCriterion("article_number in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotIn(List<String> values) {
            addCriterion("article_number not in", values, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberBetween(String value1, String value2) {
            addCriterion("article_number between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andArticleNumberNotBetween(String value1, String value2) {
            addCriterion("article_number not between", value1, value2, "articleNumber");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIsNull() {
            addCriterion("sku_data_source is null");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIsNotNull() {
            addCriterion("sku_data_source is not null");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceEqualTo(Integer value) {
            addCriterion("sku_data_source =", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotEqualTo(Integer value) {
            addCriterion("sku_data_source <>", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceGreaterThan(Integer value) {
            addCriterion("sku_data_source >", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("sku_data_source >=", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceLessThan(Integer value) {
            addCriterion("sku_data_source <", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceLessThanOrEqualTo(Integer value) {
            addCriterion("sku_data_source <=", value, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceIn(List<Integer> values) {
            addCriterion("sku_data_source in", values, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotIn(List<Integer> values) {
            addCriterion("sku_data_source not in", values, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceBetween(Integer value1, Integer value2) {
            addCriterion("sku_data_source between", value1, value2, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andSkuDataSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("sku_data_source not between", value1, value2, "skuDataSource");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Integer value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Integer value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Integer value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Integer value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Integer> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Integer> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathIsNull() {
            addCriterion("category_id_path is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathIsNotNull() {
            addCriterion("category_id_path is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathEqualTo(String value) {
            addCriterion("category_id_path =", value, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathNotEqualTo(String value) {
            addCriterion("category_id_path <>", value, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathGreaterThan(String value) {
            addCriterion("category_id_path >", value, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathGreaterThanOrEqualTo(String value) {
            addCriterion("category_id_path >=", value, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathLessThan(String value) {
            addCriterion("category_id_path <", value, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathLessThanOrEqualTo(String value) {
            addCriterion("category_id_path <=", value, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathLike(String value) {
            addCriterion("category_id_path like", value, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathNotLike(String value) {
            addCriterion("category_id_path not like", value, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathIn(List<String> values) {
            addCriterion("category_id_path in", values, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathNotIn(List<String> values) {
            addCriterion("category_id_path not in", values, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathBetween(String value1, String value2) {
            addCriterion("category_id_path between", value1, value2, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryIdPathNotBetween(String value1, String value2) {
            addCriterion("category_id_path not between", value1, value2, "categoryIdPath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathIsNull() {
            addCriterion("category_name_path is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathIsNotNull() {
            addCriterion("category_name_path is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathEqualTo(String value) {
            addCriterion("category_name_path =", value, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathNotEqualTo(String value) {
            addCriterion("category_name_path <>", value, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathGreaterThan(String value) {
            addCriterion("category_name_path >", value, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathGreaterThanOrEqualTo(String value) {
            addCriterion("category_name_path >=", value, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathLessThan(String value) {
            addCriterion("category_name_path <", value, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathLessThanOrEqualTo(String value) {
            addCriterion("category_name_path <=", value, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathLike(String value) {
            addCriterion("category_name_path like", value, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathNotLike(String value) {
            addCriterion("category_name_path not like", value, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathIn(List<String> values) {
            addCriterion("category_name_path in", values, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathNotIn(List<String> values) {
            addCriterion("category_name_path not in", values, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathBetween(String value1, String value2) {
            addCriterion("category_name_path between", value1, value2, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andCategoryNamePathNotBetween(String value1, String value2) {
            addCriterion("category_name_path not between", value1, value2, "categoryNamePath");
            return (Criteria) this;
        }

        public Criteria andMainImgIsNull() {
            addCriterion("main_img is null");
            return (Criteria) this;
        }

        public Criteria andMainImgIsNotNull() {
            addCriterion("main_img is not null");
            return (Criteria) this;
        }

        public Criteria andMainImgEqualTo(String value) {
            addCriterion("main_img =", value, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgNotEqualTo(String value) {
            addCriterion("main_img <>", value, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgGreaterThan(String value) {
            addCriterion("main_img >", value, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgGreaterThanOrEqualTo(String value) {
            addCriterion("main_img >=", value, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgLessThan(String value) {
            addCriterion("main_img <", value, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgLessThanOrEqualTo(String value) {
            addCriterion("main_img <=", value, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgLike(String value) {
            addCriterion("main_img like", value, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgNotLike(String value) {
            addCriterion("main_img not like", value, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgIn(List<String> values) {
            addCriterion("main_img in", values, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgNotIn(List<String> values) {
            addCriterion("main_img not in", values, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgBetween(String value1, String value2) {
            addCriterion("main_img between", value1, value2, "mainImg");
            return (Criteria) this;
        }

        public Criteria andMainImgNotBetween(String value1, String value2) {
            addCriterion("main_img not between", value1, value2, "mainImg");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andVatIsNull() {
            addCriterion("vat is null");
            return (Criteria) this;
        }

        public Criteria andVatIsNotNull() {
            addCriterion("vat is not null");
            return (Criteria) this;
        }

        public Criteria andVatEqualTo(String value) {
            addCriterion("vat =", value, "vat");
            return (Criteria) this;
        }

        public Criteria andVatNotEqualTo(String value) {
            addCriterion("vat <>", value, "vat");
            return (Criteria) this;
        }

        public Criteria andVatGreaterThan(String value) {
            addCriterion("vat >", value, "vat");
            return (Criteria) this;
        }

        public Criteria andVatGreaterThanOrEqualTo(String value) {
            addCriterion("vat >=", value, "vat");
            return (Criteria) this;
        }

        public Criteria andVatLessThan(String value) {
            addCriterion("vat <", value, "vat");
            return (Criteria) this;
        }

        public Criteria andVatLessThanOrEqualTo(String value) {
            addCriterion("vat <=", value, "vat");
            return (Criteria) this;
        }

        public Criteria andVatLike(String value) {
            addCriterion("vat like", value, "vat");
            return (Criteria) this;
        }

        public Criteria andVatNotLike(String value) {
            addCriterion("vat not like", value, "vat");
            return (Criteria) this;
        }

        public Criteria andVatIn(List<String> values) {
            addCriterion("vat in", values, "vat");
            return (Criteria) this;
        }

        public Criteria andVatNotIn(List<String> values) {
            addCriterion("vat not in", values, "vat");
            return (Criteria) this;
        }

        public Criteria andVatBetween(String value1, String value2) {
            addCriterion("vat between", value1, value2, "vat");
            return (Criteria) this;
        }

        public Criteria andVatNotBetween(String value1, String value2) {
            addCriterion("vat not between", value1, value2, "vat");
            return (Criteria) this;
        }

        public Criteria andVideoIsNull() {
            addCriterion("video is null");
            return (Criteria) this;
        }

        public Criteria andVideoIsNotNull() {
            addCriterion("video is not null");
            return (Criteria) this;
        }

        public Criteria andVideoEqualTo(String value) {
            addCriterion("video =", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoNotEqualTo(String value) {
            addCriterion("video <>", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoGreaterThan(String value) {
            addCriterion("video >", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoGreaterThanOrEqualTo(String value) {
            addCriterion("video >=", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoLessThan(String value) {
            addCriterion("video <", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoLessThanOrEqualTo(String value) {
            addCriterion("video <=", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoLike(String value) {
            addCriterion("video like", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoNotLike(String value) {
            addCriterion("video not like", value, "video");
            return (Criteria) this;
        }

        public Criteria andVideoIn(List<String> values) {
            addCriterion("video in", values, "video");
            return (Criteria) this;
        }

        public Criteria andVideoNotIn(List<String> values) {
            addCriterion("video not in", values, "video");
            return (Criteria) this;
        }

        public Criteria andVideoBetween(String value1, String value2) {
            addCriterion("video between", value1, value2, "video");
            return (Criteria) this;
        }

        public Criteria andVideoNotBetween(String value1, String value2) {
            addCriterion("video not between", value1, value2, "video");
            return (Criteria) this;
        }

        public Criteria andSaleVariantIsNull() {
            addCriterion("sale_variant is null");
            return (Criteria) this;
        }

        public Criteria andSaleVariantIsNotNull() {
            addCriterion("sale_variant is not null");
            return (Criteria) this;
        }

        public Criteria andSaleVariantEqualTo(Boolean value) {
            addCriterion("sale_variant =", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantNotEqualTo(Boolean value) {
            addCriterion("sale_variant <>", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantGreaterThan(Boolean value) {
            addCriterion("sale_variant >", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantGreaterThanOrEqualTo(Boolean value) {
            addCriterion("sale_variant >=", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantLessThan(Boolean value) {
            addCriterion("sale_variant <", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantLessThanOrEqualTo(Boolean value) {
            addCriterion("sale_variant <=", value, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantIn(List<Boolean> values) {
            addCriterion("sale_variant in", values, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantNotIn(List<Boolean> values) {
            addCriterion("sale_variant not in", values, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantBetween(Boolean value1, Boolean value2) {
            addCriterion("sale_variant between", value1, value2, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andSaleVariantNotBetween(Boolean value1, Boolean value2) {
            addCriterion("sale_variant not between", value1, value2, "saleVariant");
            return (Criteria) this;
        }

        public Criteria andNeedTranslateIsNull() {
            addCriterion("need_translate is null");
            return (Criteria) this;
        }

        public Criteria andNeedTranslateIsNotNull() {
            addCriterion("need_translate is not null");
            return (Criteria) this;
        }

        public Criteria andNeedTranslateEqualTo(Boolean value) {
            addCriterion("need_translate =", value, "needTranslate");
            return (Criteria) this;
        }

        public Criteria andNeedTranslateNotEqualTo(Boolean value) {
            addCriterion("need_translate <>", value, "needTranslate");
            return (Criteria) this;
        }

        public Criteria andNeedTranslateGreaterThan(Boolean value) {
            addCriterion("need_translate >", value, "needTranslate");
            return (Criteria) this;
        }

        public Criteria andNeedTranslateGreaterThanOrEqualTo(Boolean value) {
            addCriterion("need_translate >=", value, "needTranslate");
            return (Criteria) this;
        }

        public Criteria andNeedTranslateLessThan(Boolean value) {
            addCriterion("need_translate <", value, "needTranslate");
            return (Criteria) this;
        }

        public Criteria andNeedTranslateLessThanOrEqualTo(Boolean value) {
            addCriterion("need_translate <=", value, "needTranslate");
            return (Criteria) this;
        }

        public Criteria andNeedTranslateIn(List<Boolean> values) {
            addCriterion("need_translate in", values, "needTranslate");
            return (Criteria) this;
        }

        public Criteria andNeedTranslateNotIn(List<Boolean> values) {
            addCriterion("need_translate not in", values, "needTranslate");
            return (Criteria) this;
        }

        public Criteria andNeedTranslateBetween(Boolean value1, Boolean value2) {
            addCriterion("need_translate between", value1, value2, "needTranslate");
            return (Criteria) this;
        }

        public Criteria andNeedTranslateNotBetween(Boolean value1, Boolean value2) {
            addCriterion("need_translate not between", value1, value2, "needTranslate");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNull() {
            addCriterion("currency_code is null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIsNotNull() {
            addCriterion("currency_code is not null");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeEqualTo(String value) {
            addCriterion("currency_code =", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotEqualTo(String value) {
            addCriterion("currency_code <>", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThan(String value) {
            addCriterion("currency_code >", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("currency_code >=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThan(String value) {
            addCriterion("currency_code <", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLessThanOrEqualTo(String value) {
            addCriterion("currency_code <=", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeLike(String value) {
            addCriterion("currency_code like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotLike(String value) {
            addCriterion("currency_code not like", value, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeIn(List<String> values) {
            addCriterion("currency_code in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotIn(List<String> values) {
            addCriterion("currency_code not in", values, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeBetween(String value1, String value2) {
            addCriterion("currency_code between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCurrencyCodeNotBetween(String value1, String value2) {
            addCriterion("currency_code not between", value1, value2, "currencyCode");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeIsNull() {
            addCriterion("category_attribute is null");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeIsNotNull() {
            addCriterion("category_attribute is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeEqualTo(String value) {
            addCriterion("category_attribute =", value, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeNotEqualTo(String value) {
            addCriterion("category_attribute <>", value, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeGreaterThan(String value) {
            addCriterion("category_attribute >", value, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeGreaterThanOrEqualTo(String value) {
            addCriterion("category_attribute >=", value, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeLessThan(String value) {
            addCriterion("category_attribute <", value, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeLessThanOrEqualTo(String value) {
            addCriterion("category_attribute <=", value, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeLike(String value) {
            addCriterion("category_attribute like", value, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeNotLike(String value) {
            addCriterion("category_attribute not like", value, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeIn(List<String> values) {
            addCriterion("category_attribute in", values, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeNotIn(List<String> values) {
            addCriterion("category_attribute not in", values, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeBetween(String value1, String value2) {
            addCriterion("category_attribute between", value1, value2, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andCategoryAttributeNotBetween(String value1, String value2) {
            addCriterion("category_attribute not between", value1, value2, "categoryAttribute");
            return (Criteria) this;
        }

        public Criteria andMergeAttributeIsNull() {
            addCriterion("merge_attribute is null");
            return (Criteria) this;
        }

        public Criteria andMergeAttributeIsNotNull() {
            addCriterion("merge_attribute is not null");
            return (Criteria) this;
        }

        public Criteria andMergeAttributeEqualTo(String value) {
            addCriterion("merge_attribute =", value, "mergeAttribute");
            return (Criteria) this;
        }

        public Criteria andMergeAttributeNotEqualTo(String value) {
            addCriterion("merge_attribute <>", value, "mergeAttribute");
            return (Criteria) this;
        }

        public Criteria andMergeAttributeGreaterThan(String value) {
            addCriterion("merge_attribute >", value, "mergeAttribute");
            return (Criteria) this;
        }

        public Criteria andMergeAttributeGreaterThanOrEqualTo(String value) {
            addCriterion("merge_attribute >=", value, "mergeAttribute");
            return (Criteria) this;
        }

        public Criteria andMergeAttributeLessThan(String value) {
            addCriterion("merge_attribute <", value, "mergeAttribute");
            return (Criteria) this;
        }

        public Criteria andMergeAttributeLessThanOrEqualTo(String value) {
            addCriterion("merge_attribute <=", value, "mergeAttribute");
            return (Criteria) this;
        }

        public Criteria andMergeAttributeLike(String value) {
            addCriterion("merge_attribute like", value, "mergeAttribute");
            return (Criteria) this;
        }

        public Criteria andMergeAttributeNotLike(String value) {
            addCriterion("merge_attribute not like", value, "mergeAttribute");
            return (Criteria) this;
        }

        public Criteria andMergeAttributeIn(List<String> values) {
            addCriterion("merge_attribute in", values, "mergeAttribute");
            return (Criteria) this;
        }

        public Criteria andMergeAttributeNotIn(List<String> values) {
            addCriterion("merge_attribute not in", values, "mergeAttribute");
            return (Criteria) this;
        }

        public Criteria andMergeAttributeBetween(String value1, String value2) {
            addCriterion("merge_attribute between", value1, value2, "mergeAttribute");
            return (Criteria) this;
        }

        public Criteria andMergeAttributeNotBetween(String value1, String value2) {
            addCriterion("merge_attribute not between", value1, value2, "mergeAttribute");
            return (Criteria) this;
        }

        public Criteria andVariantDataIsNull() {
            addCriterion("variant_data is null");
            return (Criteria) this;
        }

        public Criteria andVariantDataIsNotNull() {
            addCriterion("variant_data is not null");
            return (Criteria) this;
        }

        public Criteria andVariantDataEqualTo(String value) {
            addCriterion("variant_data =", value, "variantData");
            return (Criteria) this;
        }

        public Criteria andVariantDataNotEqualTo(String value) {
            addCriterion("variant_data <>", value, "variantData");
            return (Criteria) this;
        }

        public Criteria andVariantDataGreaterThan(String value) {
            addCriterion("variant_data >", value, "variantData");
            return (Criteria) this;
        }

        public Criteria andVariantDataGreaterThanOrEqualTo(String value) {
            addCriterion("variant_data >=", value, "variantData");
            return (Criteria) this;
        }

        public Criteria andVariantDataLessThan(String value) {
            addCriterion("variant_data <", value, "variantData");
            return (Criteria) this;
        }

        public Criteria andVariantDataLessThanOrEqualTo(String value) {
            addCriterion("variant_data <=", value, "variantData");
            return (Criteria) this;
        }

        public Criteria andVariantDataLike(String value) {
            addCriterion("variant_data like", value, "variantData");
            return (Criteria) this;
        }

        public Criteria andVariantDataNotLike(String value) {
            addCriterion("variant_data not like", value, "variantData");
            return (Criteria) this;
        }

        public Criteria andVariantDataIn(List<String> values) {
            addCriterion("variant_data in", values, "variantData");
            return (Criteria) this;
        }

        public Criteria andVariantDataNotIn(List<String> values) {
            addCriterion("variant_data not in", values, "variantData");
            return (Criteria) this;
        }

        public Criteria andVariantDataBetween(String value1, String value2) {
            addCriterion("variant_data between", value1, value2, "variantData");
            return (Criteria) this;
        }

        public Criteria andVariantDataNotBetween(String value1, String value2) {
            addCriterion("variant_data not between", value1, value2, "variantData");
            return (Criteria) this;
        }

        public Criteria andPublishTypeIsNull() {
            addCriterion("publish_type is null");
            return (Criteria) this;
        }

        public Criteria andPublishTypeIsNotNull() {
            addCriterion("publish_type is not null");
            return (Criteria) this;
        }

        public Criteria andPublishTypeEqualTo(Integer value) {
            addCriterion("publish_type =", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeNotEqualTo(Integer value) {
            addCriterion("publish_type <>", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeGreaterThan(Integer value) {
            addCriterion("publish_type >", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("publish_type >=", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeLessThan(Integer value) {
            addCriterion("publish_type <", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeLessThanOrEqualTo(Integer value) {
            addCriterion("publish_type <=", value, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeIn(List<Integer> values) {
            addCriterion("publish_type in", values, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeNotIn(List<Integer> values) {
            addCriterion("publish_type not in", values, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeBetween(Integer value1, Integer value2) {
            addCriterion("publish_type between", value1, value2, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("publish_type not between", value1, value2, "publishType");
            return (Criteria) this;
        }

        public Criteria andPublishRoleIsNull() {
            addCriterion("publish_role is null");
            return (Criteria) this;
        }

        public Criteria andPublishRoleIsNotNull() {
            addCriterion("publish_role is not null");
            return (Criteria) this;
        }

        public Criteria andPublishRoleEqualTo(Integer value) {
            addCriterion("publish_role =", value, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleNotEqualTo(Integer value) {
            addCriterion("publish_role <>", value, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleGreaterThan(Integer value) {
            addCriterion("publish_role >", value, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleGreaterThanOrEqualTo(Integer value) {
            addCriterion("publish_role >=", value, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleLessThan(Integer value) {
            addCriterion("publish_role <", value, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleLessThanOrEqualTo(Integer value) {
            addCriterion("publish_role <=", value, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleIn(List<Integer> values) {
            addCriterion("publish_role in", values, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleNotIn(List<Integer> values) {
            addCriterion("publish_role not in", values, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleBetween(Integer value1, Integer value2) {
            addCriterion("publish_role between", value1, value2, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishRoleNotBetween(Integer value1, Integer value2) {
            addCriterion("publish_role not between", value1, value2, "publishRole");
            return (Criteria) this;
        }

        public Criteria andPublishStatusIsNull() {
            addCriterion("publish_status is null");
            return (Criteria) this;
        }

        public Criteria andPublishStatusIsNotNull() {
            addCriterion("publish_status is not null");
            return (Criteria) this;
        }

        public Criteria andPublishStatusEqualTo(Integer value) {
            addCriterion("publish_status =", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusNotEqualTo(Integer value) {
            addCriterion("publish_status <>", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusGreaterThan(Integer value) {
            addCriterion("publish_status >", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("publish_status >=", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusLessThan(Integer value) {
            addCriterion("publish_status <", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusLessThanOrEqualTo(Integer value) {
            addCriterion("publish_status <=", value, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusIn(List<Integer> values) {
            addCriterion("publish_status in", values, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusNotIn(List<Integer> values) {
            addCriterion("publish_status not in", values, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusBetween(Integer value1, Integer value2) {
            addCriterion("publish_status between", value1, value2, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andPublishStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("publish_status not between", value1, value2, "publishStatus");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadIsNull() {
            addCriterion("inventory_upload is null");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadIsNotNull() {
            addCriterion("inventory_upload is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadEqualTo(Integer value) {
            addCriterion("inventory_upload =", value, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadNotEqualTo(Integer value) {
            addCriterion("inventory_upload <>", value, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadGreaterThan(Integer value) {
            addCriterion("inventory_upload >", value, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadGreaterThanOrEqualTo(Integer value) {
            addCriterion("inventory_upload >=", value, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadLessThan(Integer value) {
            addCriterion("inventory_upload <", value, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadLessThanOrEqualTo(Integer value) {
            addCriterion("inventory_upload <=", value, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadIn(List<Integer> values) {
            addCriterion("inventory_upload in", values, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadNotIn(List<Integer> values) {
            addCriterion("inventory_upload not in", values, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadBetween(Integer value1, Integer value2) {
            addCriterion("inventory_upload between", value1, value2, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andInventoryUploadNotBetween(Integer value1, Integer value2) {
            addCriterion("inventory_upload not between", value1, value2, "inventoryUpload");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNull() {
            addCriterion("created_time is null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIsNotNull() {
            addCriterion("created_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeEqualTo(Timestamp value) {
            addCriterion("created_time =", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotEqualTo(Timestamp value) {
            addCriterion("created_time <>", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThan(Timestamp value) {
            addCriterion("created_time >", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("created_time >=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThan(Timestamp value) {
            addCriterion("created_time <", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("created_time <=", value, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeIn(List<Timestamp> values) {
            addCriterion("created_time in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotIn(List<Timestamp> values) {
            addCriterion("created_time not in", values, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andCreatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("created_time not between", value1, value2, "createdTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNull() {
            addCriterion("updated_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIsNotNull() {
            addCriterion("updated_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeEqualTo(Timestamp value) {
            addCriterion("updated_time =", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotEqualTo(Timestamp value) {
            addCriterion("updated_time <>", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThan(Timestamp value) {
            addCriterion("updated_time >", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time >=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThan(Timestamp value) {
            addCriterion("updated_time <", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("updated_time <=", value, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeIn(List<Timestamp> values) {
            addCriterion("updated_time in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotIn(List<Timestamp> values) {
            addCriterion("updated_time not in", values, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andUpdatedTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("updated_time not between", value1, value2, "updatedTime");
            return (Criteria) this;
        }

        public Criteria andProblemTypeIsNull() {
            addCriterion("problem_type is null");
            return (Criteria) this;
        }

        public Criteria andProblemTypeIsNotNull() {
            addCriterion("problem_type is not null");
            return (Criteria) this;
        }

        public Criteria andProblemTypeEqualTo(String value) {
            addCriterion("problem_type =", value, "problemType");
            return (Criteria) this;
        }

        public Criteria andProblemTypeNotEqualTo(String value) {
            addCriterion("problem_type <>", value, "problemType");
            return (Criteria) this;
        }

        public Criteria andProblemTypeGreaterThan(String value) {
            addCriterion("problem_type >", value, "problemType");
            return (Criteria) this;
        }

        public Criteria andProblemTypeGreaterThanOrEqualTo(String value) {
            addCriterion("problem_type >=", value, "problemType");
            return (Criteria) this;
        }

        public Criteria andProblemTypeLessThan(String value) {
            addCriterion("problem_type <", value, "problemType");
            return (Criteria) this;
        }

        public Criteria andProblemTypeLessThanOrEqualTo(String value) {
            addCriterion("problem_type <=", value, "problemType");
            return (Criteria) this;
        }

        public Criteria andProblemTypeLike(String value) {
            addCriterion("problem_type like", value, "problemType");
            return (Criteria) this;
        }

        public Criteria andProblemTypeNotLike(String value) {
            addCriterion("problem_type not like", value, "problemType");
            return (Criteria) this;
        }

        public Criteria andProblemTypeIn(List<String> values) {
            addCriterion("problem_type in", values, "problemType");
            return (Criteria) this;
        }

        public Criteria andProblemTypeNotIn(List<String> values) {
            addCriterion("problem_type not in", values, "problemType");
            return (Criteria) this;
        }

        public Criteria andProblemTypeBetween(String value1, String value2) {
            addCriterion("problem_type between", value1, value2, "problemType");
            return (Criteria) this;
        }

        public Criteria andProblemTypeNotBetween(String value1, String value2) {
            addCriterion("problem_type not between", value1, value2, "problemType");
            return (Criteria) this;
        }

        public Criteria andProblemTypeDateIsNull() {
            addCriterion("problem_type_date is null");
            return (Criteria) this;
        }

        public Criteria andProblemTypeDateIsNotNull() {
            addCriterion("problem_type_date is not null");
            return (Criteria) this;
        }

        public Criteria andProblemTypeDateEqualTo(Date value) {
            addCriterion("problem_type_date =", value, "problemTypeDate");
            return (Criteria) this;
        }

        public Criteria andProblemTypeDateNotEqualTo(Date value) {
            addCriterion("problem_type_date <>", value, "problemTypeDate");
            return (Criteria) this;
        }

        public Criteria andProblemTypeDateGreaterThan(Date value) {
            addCriterion("problem_type_date >", value, "problemTypeDate");
            return (Criteria) this;
        }

        public Criteria andProblemTypeDateGreaterThanOrEqualTo(Date value) {
            addCriterion("problem_type_date >=", value, "problemTypeDate");
            return (Criteria) this;
        }

        public Criteria andProblemTypeDateLessThan(Date value) {
            addCriterion("problem_type_date <", value, "problemTypeDate");
            return (Criteria) this;
        }

        public Criteria andProblemTypeDateLessThanOrEqualTo(Date value) {
            addCriterion("problem_type_date <=", value, "problemTypeDate");
            return (Criteria) this;
        }

        public Criteria andProblemTypeDateIn(List<Date> values) {
            addCriterion("problem_type_date in", values, "problemTypeDate");
            return (Criteria) this;
        }

        public Criteria andProblemTypeDateNotIn(List<Date> values) {
            addCriterion("problem_type_date not in", values, "problemTypeDate");
            return (Criteria) this;
        }

        public Criteria andProblemTypeDateBetween(Date value1, Date value2) {
            addCriterion("problem_type_date between", value1, value2, "problemTypeDate");
            return (Criteria) this;
        }

        public Criteria andProblemTypeDateNotBetween(Date value1, Date value2) {
            addCriterion("problem_type_date not between", value1, value2, "problemTypeDate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}