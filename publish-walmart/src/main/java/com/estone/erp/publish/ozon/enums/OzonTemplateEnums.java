package com.estone.erp.publish.ozon.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023-07-12 10:02
 */
public interface OzonTemplateEnums {

    /**
     * 刊登状态
     */
    @Getter
    @AllArgsConstructor
    enum PublishStatus {
        Waiting(1, "待刊登"),
        PUBLISHING(2, "刊登中"),
        SUCCESS(3, "刊登成功"),
        ERROR(4, "刊登失败");

        private final int code;
        private final String desc;


        public boolean isTrue(int val) {
            return this.code == val;
        }

        public static String getDesc(int code) {
            for (PublishStatus value : values()) {
                if (value.code == code) {
                    return value.desc;
                }
            }
            return null;
        }
    }

    /**
     * 刊登状态
     */
    @Getter
    @AllArgsConstructor
    enum InventoryStatus {
        NO_UPLOAD(1, "未上传库存"),
        UPLOADING(2, "请求中"),
        SUCCESS(3, "上传成功"),
        ERROR(4, "上传失败"),
        WAIT_UPLOAD(5, "待上传库存");

        private final int code;
        private final String desc;


        public boolean isTrue(int val) {
            return this.code == val;
        }
    }

    /**
     * 刊登类型
     */
    @Getter
    @AllArgsConstructor
    enum PublishType {
        AUTO_PUBLISH(1, "自动刊登"),
        NORMAL(2, "普通刊登");

        private final int code;
        private final String desc;


        public boolean isTrue(int val) {
            return this.code == val;
        }
    }

    /**
     * 刊登角色
     */
    @Getter
    @AllArgsConstructor
    enum PublishRole {
        SYSTEM(0, "系统刊登"),
        SALE_MAN(1, "销售刊登");

        private final int code;
        private final String desc;


        public boolean isTrue(int val) {
            return this.code == val;
        }
    }

    /**
     * admin范本状态
     */
    @Getter
    @AllArgsConstructor
    enum AdminTemplateStatus {
        DISABLE(0, "禁用"),
        ENABLE(1, "启用");

        private final int code;
        private final String desc;

        public boolean isTrue(int val) {
            return this.code == val;
        }
    }
}
