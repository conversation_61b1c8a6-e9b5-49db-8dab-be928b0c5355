package com.estone.erp.publish.ozon.common;

/**
 * <AUTHOR>
 * @date 2023-04-04 9:54
 */
public class OzonConstant {

    public static final String OZON_ES_INDEX = "ozon_item";
    public static final String OZON_ES_OFFLINE_INDEX = "ozon_item_offline";

    /**
     * 通用API默认请求账号
     */
    public static final String DEFAULT_ACCOUNT = "<EMAIL>";

    /**
     * 同步
     */
    public static final String ACCOUNT_SYNC_LOCK = "Publish:Ozon:ACCOUNT_SYNC_LOCK:";

    /**
     * 改库存限流KEY
     */
    public static final String UPDATE_STOCK_LIMITER = "u_s_l-";
    /**
     * 同步产品明细限流KEY
     */
    public static final String OZON_API_PRODUCT_INFO_LIMIT_KEY = "sync_p_i-";

    /**
     * 在线列表查询字段
     */
    public static final String[] LISTING_FILES = {"id","mainImage","accountNumber","productId","fbsSku","sellerSku",
            "name","categoryId","categoryPath","statusCode", "state","stock","packageSizeJson","weight","price","currencyCode",
            "sku","spu", "skuStatus", "productCategoryId", "productCategoryIdPath", "forbidChannel", "infringementObjs",
            "infringementTypeNames","prohibitionSites","tagCodes", "warehouseStockInfos", "specialGoodsCode",  "promotion",
            "newState", "skuDataSource", "composeStatus","saleMan","saleManLeader", "saleManManager", "createDate","updateDate",
            "syncDate", "linkTag","productCategoryCnName","stockSyncDate", "actualWeight", "weightDifference","rating", "orderLast7dCount",
            "orderLast14dCount", "orderLast30dCount", "orderLast90dCount", "orderNumTotal", "minPrice", "esOzonGrossProfits", "registeredForEvent", "registeredForEventDate",
            "isOnline", "oldPrice", "minPriceNumberUpdateDate", "errorTexts"
    };

    /**
     * 编辑产品描述查询
     */
    public static final String[] EDIT_PRODUCT_DESC_FILES = {"id","mainImage","accountNumber","productId","fbsSku","sellerSku","sku","images","attributes"};

    /**
     * 产品重复刊登查询
     */
    public static final String[] DUPLICATE_FILES = {"id","productId","accountNumber","sellerSku","spu","sku","statusCode","state"};

    /**
     * 类目树本地缓存
     */
    public static final String CATEGORY_CACHE = "CATEGORY_CACHE";


    /**
     * 默认仓库
     */
    public final static String DEFAULT_WAREHOUSE_NAME = "ESTD";

    /**
     * 默认空运仓库
     */
    public final static String DEFAULT_AIR_FREIGHT_WAREHOUSE_NAME = "西安空运EUB";

    /**
     * 修改库存查询
     */
    public static final String[] UPDATE_STOCK_FILES = {"id","accountNumber","productId","ozonSku","fbsSku","sku","sellerSku","skuStatus","warehouseStockInfos","stock", "linkTag", "weight", "priceNumber", "currencyCode", "specialGoodsCode","stockSyncDate"};


}
