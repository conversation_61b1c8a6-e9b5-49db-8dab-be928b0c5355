package com.estone.erp.publish.ozon.excel.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.ozon.enums.OzonPublishProcessEnums;
import com.estone.erp.publish.ozon.enums.OzonTemplateEnums;
import com.estone.erp.publish.ozon.excel.model.UpdateTemplateTitleAndDescExcel;
import com.estone.erp.publish.ozon.handler.publish.parm.AutoPublishMessage;
import com.estone.erp.publish.ozon.handler.publish.parm.TemplatePublishParam;
import com.estone.erp.publish.ozon.handler.template.OzonTemplateValidation;
import com.estone.erp.publish.ozon.model.OzonTemplateModel;
import com.estone.erp.publish.ozon.model.OzonTemplateModelExample;
import com.estone.erp.publish.ozon.mq.OzonMqConfig;
import com.estone.erp.publish.ozon.service.OzonFeedTaskService;
import com.estone.erp.publish.ozon.service.OzonTemplateModelService;
import com.estone.erp.publish.platform.enums.SmallPlatformDownloadEnums;
import com.estone.erp.publish.platform.model.SmallPlatformExcelDownloadLog;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 修改重量处理器
 */
@Component
public class UpdateTemplateTitleAndDescExcelHandler extends AbstractCommonExcelTemplateHandler<UpdateTemplateTitleAndDescExcel> implements DelayDownloadHandler {

    @Autowired
    private OzonTemplateModelService ozonTemplateModelService;
    @Autowired
    private OzonFeedTaskService ozonFeedTaskService;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private OzonTemplateValidation ozonTemplateValidation;
    @Override
    public SmallPlatformDownloadEnums.Type getType() {
        return SmallPlatformDownloadEnums.Type.UPDATE_TEMPLATE_TITLE_AND_DESC;
    }

    @Override
    public void writeFile(SmallPlatformExcelDownloadLog downloadLog) {
        InputStream ins = prepareExcelInputStream(downloadLog);
        Set<String> head = head(UpdateTemplateTitleAndDescExcel.class);
        List<UpdateTemplateTitleAndDescExcel> resultList = parseExcelFile(ins, head);

        // 设置excelId
        Integer id = downloadLog.getId();
        resultList.forEach(item -> item.setExcelId(id));
        
        processExcelData(resultList, downloadLog.getCreateBy());
        updateDownloadLog(downloadLog, resultList);
    }

    @Override
    protected void validateSpecificColumns(Map<Integer, String> data) {
        // 验证重量列
        String s1 = data.get(1);
        if (!"标题*".equals(s1)) {
            throw new RuntimeException("excel文件格式错误, 标题列顺序不对");
        }
        String s2 = data.get(2);
        if (!"描述*".equals(s2)) {
            throw new RuntimeException("excel文件格式错误, 描述列顺序不对");
        }
    }

    @Override
    protected UpdateTemplateTitleAndDescExcel createExcelRowObject(Map<Integer, String> data, int rowIndex) {
        UpdateTemplateTitleAndDescExcel updateTemplateTitleAndDescExcel = new UpdateTemplateTitleAndDescExcel();
        updateTemplateTitleAndDescExcel.setTemplateIdStr(data.get(0));
        updateTemplateTitleAndDescExcel.setTitle(data.get(1));
        updateTemplateTitleAndDescExcel.setDesc(data.get(2));
        updateTemplateTitleAndDescExcel.setRowIndex(rowIndex);
        return updateTemplateTitleAndDescExcel;
    }

    protected void processExcelData(List<UpdateTemplateTitleAndDescExcel> resultList, String username) {
        DataContextHolder.setUsername(username);
        
        // 必填校验
        List<UpdateTemplateTitleAndDescExcel> collect = resultList.stream()
                .filter(a -> StringUtils.isBlank(a.getRemark()))
                .collect(Collectors.toList());

        // 查询模版
        collect = checkTemplate(collect);

        // 权限校验
        collect = checkAuth(collect, username);

        // 数据重复性校验
        collect = checkRepeat(collect, UpdateTemplateTitleAndDescExcel::getTemplateIdStr);

        // 开始修改模版信息
        if (CollectionUtils.isNotEmpty(collect)) {
            for (UpdateTemplateTitleAndDescExcel updateTemplateTitleAndDescExcel : collect) {
                OzonTemplateModel ozonTemplateModel = new OzonTemplateModel();
                ozonTemplateModel.setId(updateTemplateTitleAndDescExcel.getTemplateId());
                try {
                    ozonTemplateValidation.filterInfringementWord(Optional.ofNullable(updateTemplateTitleAndDescExcel.getTitle()).orElse(""), Optional.ofNullable(updateTemplateTitleAndDescExcel.getDesc()).orElse(""),
                            updateTemplateTitleAndDescExcel::setTitle, updateTemplateTitleAndDescExcel::setDesc);
                } catch (Exception e) {
                    // ignore
                }

                ozonTemplateModel.setDescription(updateTemplateTitleAndDescExcel.getDesc());
                ozonTemplateModel.setTitle(updateTemplateTitleAndDescExcel.getTitle());
                ozonTemplateModel.setUpdatedBy(username);
                ozonTemplateModelService.updateByPrimaryKeySelective(ozonTemplateModel);
            }
            // 刊登
            for (UpdateTemplateTitleAndDescExcel updateTemplateTitleAndDescExcel : collect) {
                TemplatePublishParam templatePublishParam = new TemplatePublishParam();
                templatePublishParam.setUser(username);
                templatePublishParam.setType(OzonTemplateEnums.PublishType.NORMAL.getCode());
                templatePublishParam.setRole(OzonTemplateEnums.PublishRole.SALE_MAN.getCode());
                templatePublishParam.setTemplateId(updateTemplateTitleAndDescExcel.getTemplateId());
                templatePublishParam.setExcelId(updateTemplateTitleAndDescExcel.getExcelId());
                templatePublishParam.setRowIndex(updateTemplateTitleAndDescExcel.getRowIndex());
                templatePublishParam.setNeedFeedTask(true);
                AutoPublishMessage publishMessage = new AutoPublishMessage(OzonPublishProcessEnums.PublishType.TEMPLATE.getCode(), JSON.toJSONString(templatePublishParam));
                rabbitTemplate.convertAndSend(OzonMqConfig.OZON_API_DIRECT_EXCHANGE, OzonMqConfig.OZON_AUTO_PUBLISH_QUEUE_KEY, publishMessage);
            }
        }

        // 错误信息
        List<UpdateTemplateTitleAndDescExcel> errorList = resultList.stream().filter(a -> StringUtils.isNotBlank(a.getResult())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(errorList)) {
            // 创建处理报告 OzonTemplateDataUtil.getFirstSellerSku()
            for (UpdateTemplateTitleAndDescExcel updateTemplateTitleAndDescExcel : errorList) {
                ozonFeedTaskService.newPublishFailFeedTask(updateTemplateTitleAndDescExcel.getTemplateIdStr(), updateTemplateTitleAndDescExcel.getAccountNumber(),
                        updateTemplateTitleAndDescExcel.getArticleNumber(), updateTemplateTitleAndDescExcel.getRemark(),
                        updateTemplateTitleAndDescExcel.getSellerSku(), null,
                        updateTemplateTitleAndDescExcel.getExcelId(), updateTemplateTitleAndDescExcel.getRowIndex());
            }
        }
    }

    private List<UpdateTemplateTitleAndDescExcel> checkTemplate(List<UpdateTemplateTitleAndDescExcel> collect) {
        List<Integer> templateIds = collect.stream().map(UpdateTemplateTitleAndDescExcel::getTemplateId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<List<Integer>> partition = Lists.partition(templateIds, 1000);
        Map<Integer, OzonTemplateModel> ozonTemplateModelMap = new HashMap<>();
        for (List<Integer> integers : partition) {
            OzonTemplateModelExample example = new OzonTemplateModelExample();
            example.setColumns("id, account_number, article_number");
            example.createCriteria().andIdIn(integers);
            List<OzonTemplateModel> ozonTemplateModels = ozonTemplateModelService.selectByExample(example);
            ozonTemplateModels.forEach(a -> ozonTemplateModelMap.put(a.getId(), a));
        }
        for (UpdateTemplateTitleAndDescExcel updateTemplateTitleAndDescExcel : collect) {
            Integer templateId = updateTemplateTitleAndDescExcel.getTemplateId();
            if (ozonTemplateModelMap.containsKey(templateId)) {
                OzonTemplateModel ozonTemplateModel = ozonTemplateModelMap.get(templateId);
                String accountNumber = ozonTemplateModel.getAccountNumber();
                if (StringUtils.isBlank(accountNumber)) {
                    updateTemplateTitleAndDescExcel.setResult("失败");
                    updateTemplateTitleAndDescExcel.setRemark("模版的店铺账号为空");
                } else {
                    updateTemplateTitleAndDescExcel.setAccountNumber(accountNumber);
                }
                updateTemplateTitleAndDescExcel.setArticleNumber(ozonTemplateModel.getArticleNumber());
            } else {
                updateTemplateTitleAndDescExcel.setResult("失败");
                updateTemplateTitleAndDescExcel.setRemark("无此模版数据");
            }
        }
        return collect.stream().filter(a -> StringUtils.isBlank(a.getResult())).collect(Collectors.toList());
    }


}
