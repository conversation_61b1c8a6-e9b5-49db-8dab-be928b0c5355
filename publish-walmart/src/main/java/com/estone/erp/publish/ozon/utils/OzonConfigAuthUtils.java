package com.estone.erp.publish.ozon.utils;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.ozon.common.OzonErrorConstant;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class OzonConfigAuthUtils {

    public List<String> isAuth(List<String> employeeNos) {
        employeeNos = Optional.ofNullable(employeeNos).orElseGet(ArrayList::new);
        //权限控制：创建人只能看到自己创建的分组，上级可查看自己和下级，超管可查看全部
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils
                .isSuperAdminOrEquivalent(SaleChannel.CHANNEL_OZON);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
        }
        if (!superAdminOrEquivalent.getResult()) {
            List<String> authorEmployeeNos = PermissionsHelper.getAuthorEmployeeNoList(SaleChannel.CHANNEL_OZON);
            if (CollectionUtils.isNotEmpty(authorEmployeeNos)) {
                if (CollectionUtils.isNotEmpty(employeeNos)) {
                    authorEmployeeNos = authorEmployeeNos.stream().filter(employeeNos::contains).collect(Collectors.toList());
                    if (employeeNos.isEmpty()) {
                        throw new IllegalArgumentException(OzonErrorConstant.NO_STORE_ACCESS_PERMISSION);
                    }
                }
                return authorEmployeeNos;
            } else {
                throw new RuntimeException(OzonErrorConstant.UNAUTHORIZED_USER_NOT_OBTAINED);
            }
        }
        return employeeNos;
    }

}
