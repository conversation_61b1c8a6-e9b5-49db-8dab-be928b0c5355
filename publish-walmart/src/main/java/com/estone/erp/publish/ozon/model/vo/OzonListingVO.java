package com.estone.erp.publish.ozon.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.component.converter.*;
import com.estone.erp.publish.ozon.common.converter.OzonLinkTagConverter;
import com.estone.erp.publish.ozon.common.converter.OzonStatusCodeConverter;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-04-04 17:42
 */
@Data
public class OzonListingVO implements Serializable {

    private static final long serialVersionUID = -104309632175432325L;
    /**
     * id
     */
    @ExcelIgnore
    private String id;

    /**
     * 主图
     */
    @ExcelIgnore
    private String mainImage;

    /**
     * 店铺名称
     */
    @ExcelProperty("店铺名称")
    private String accountNumber;

    /**
     * 商品编码
     */
    @ExcelProperty("商品编码")
    private Long productId;

    /**
     * fbs sku
     */
    private Long fbsSku;

    /**
     * sellerSku
     */
    @ExcelProperty("sellerSku")
    private String sellerSku;

    /**
     * 标题
     */
    @ExcelProperty("标题")
    private String name;

    /**
     * 平台类目Id
     */
    @ExcelProperty("平台类目Id")
    private Long categoryId;

    /**
     * 平台类目
     */
    @ExcelProperty("平台类目")
    private String categoryPath;

    /**
     * 是否在线
     */
    @ExcelProperty(value = "在线状态", converter = OnlineCodeConverter.class)
    private Boolean isOnline;
    /**
     * 平台状态
     */
    @ExcelProperty(value = "平台状态", converter = OzonStatusCodeConverter.class)
    private List<String> statusCode;

    /**
     * 库存
     */
    @ExcelProperty("库存")
    private Integer stock;

    @ExcelProperty("库存、毛利、毛利率")
    private String wareHouseInfoVOStr;
    /**
     * 包裹尺寸
     */
    @ExcelProperty("包裹尺寸")
    private String packageSizeJson;

    /**
     * 重量
     */
    @ExcelProperty("重量")
    private Double weight;

    /**
     * 实际产品系统重量
     */
    @ExcelIgnore
    private Double actualWeight;

    /**
     * 重量差异
     */
    @ExcelIgnore
    private Double weightDifference;

    /**
     * 价格
     */
    @ExcelProperty("价格")
    private String price;

    /**
     * 原价
     */
    @ExcelProperty("原价")
    private String oldPrice;

    /**
     * 最低价格
     */
    @ExcelProperty("最低价格")
    private String minPrice;


    /**
     * 销售成本价
     */
    @ExcelProperty("销售成本价")
    private String saleCostPrice;
    /**
     * 货币
     */
    @ExcelProperty("货币")
    private String currencyCode;

    /**
     * SKU
     */
    @ExcelProperty("SKU")
    private String sku;

    /**
     * SPU
     */
    @ExcelProperty("SPU")
    private String spu;

    /**
     * 产品系统-单品状态
     */
    @ExcelProperty(value = "单品状态", converter = SkuStatusCodeConverter.class)
    private String skuStatus;

    /**
     * 产品系统 - 类目id
     */
    private Integer productCategoryId;

    /**
     * 类目路径id
     */
    private String productCategoryIdPath;

    @ExcelProperty("分类")
    private String productCategoryCnName;

    /**
     * 禁售平台
     */
    @ExcelProperty(value = "禁售平台", converter = ListStringFormatConverter.class)
    private List<String> forbidChannel;

    /**
     * 禁售原因
     */
    @ExcelProperty(value = "禁售原因", converter = ListStringFormatConverter.class)
    private List<String> infringementObjs;

    /**
     * 禁售类型
     */
    @ExcelProperty(value = "禁售类型", converter = ListStringFormatConverter.class)
    private List<String> infringementTypeNames;

    /**
     * 平台禁售站点
     */
    @ExcelProperty(value = "平台禁售站点", converter = ListStringFormatConverter.class)
    private List<String> prohibitionSites;


    /**
     * 产品标签
     */
    @ExcelProperty(value = "产品标签", converter = TagCodeConverter.class)
    private List<String> tagCodes;

    /**
     * 特殊标签
     */
    @ExcelProperty(value = "特殊标签", converter = SpecialTagConverter.class)
    private List<Integer> specialGoodsCode;
    /**
     * 是否报名参加活动
     */
    @Field(type = FieldType.Boolean)
    @ExcelProperty(value = "是否已报名活动", converter = BooleanCodeConverter.class)
    private Boolean registeredForEvent;
    /**
     * 是否报名参加活动 同步时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date registeredForEventDate;

    /**
     * 促销状态 0 没有  1，是  2，否  特别注意 0 和 2 都是(否)，1是（是）
     */
    @ExcelProperty(value = "促销状态", converter = PromotionConverter.class)
    private Integer promotion;

    /**
     * 新品状态
     */
    @ExcelProperty(value = "新品状态", converter = BooleanCodeConverter.class)
    private Boolean newState;


    /**
     * 数据来源
     */
    @ExcelProperty(value = "数据来源", converter = SkuDataSourceConverter.class)
    private Integer skuDataSource;

    /**
     * 组合状态
     */
    @ExcelProperty(value = "组合状态", converter = ComposeStatusConverter.class)
    private Integer composeStatus;

    /**
     * 链接标签
     */
    @ExcelProperty(value = "链接标签", converter = OzonLinkTagConverter.class)
    private Integer linkTag;

    /**
     * 内容评级
     */
    @ExcelProperty(value = "内容评级")
    private Double rating;

    /**
     * 销售账号
     */
    @ExcelProperty("销售")
    private String saleMan;

    /**
     * 销售组长
     */
    @ExcelProperty("销售组长")
    private String saleManLeader;

    /**
     * 销售主管
     */
    @ExcelProperty("销售主管")
    private String saleManManager;

    /**
     * 仓库库存毛利详情
     */
    @ExcelIgnore
    private List<OzonWareHouseGrossProfitVO> wareHouseInfoVOList;

    /**
     * 7天销量
     */
    @ExcelProperty("7天销量")
    private Integer orderLast7dCount;

    /**
     * 14天销量
     */
    @ExcelProperty("14天销量")
    private Integer orderLast14dCount;

    /**
     * 30天销量
     */
    @ExcelProperty("30天销量")
    private Integer orderLast30dCount;

    /**
     * 90天销量
     */
    @ExcelProperty("90天销量")
    private Integer orderLast90dCount;

    /**
     * 总销量
     */
    @ExcelProperty("总销量")
    private Integer orderNumTotal;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty("创建时间")
    private Date createDate;

    /**
     * 修改时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty("修改时间")
    private Date updateDate;

    /**
     * 同步日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty("同步日期")
    private Date syncDate;

    /**
     * 库存同步时间stockSyncDate
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty("库存同步时间")
    private Date stockSyncDate;


    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty("最低价更新时间")
    private Date minPriceNumberUpdateDate;

    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误", converter = ListStringFormatConverter.class)
    private List<String> errorTexts;

    /**
     * 包材价格
     */
    @ExcelProperty(value = "包材价格")
    private BigDecimal packingPrice;

    /**
     * 包材重量
     */
    @ExcelProperty(value = "包材重量")
    private BigDecimal packingWeight;

    /**
     * 搭配包材重量
     */
    @ExcelProperty(value = "搭配包材重量")
    private BigDecimal matchWeight;

    /**
     * 产品净重
     */
    @ExcelProperty(value = "产品净重")
    private Double productWeight;

    /**
     * 预估包裹重量
     */
    @ExcelProperty(value = "预估包裹重量")
    private Double packageWeight;

    /**
     * 标准重量
     */
    @ExcelProperty(value = "标准重量")
    private Double standardWeight;

}
