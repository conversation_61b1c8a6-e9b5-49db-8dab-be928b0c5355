package com.estone.erp.publish.ozon.handler.publish;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.publish.ozon.call.OzonApiClient;
import com.estone.erp.publish.ozon.call.OzonResponseResult;
import com.estone.erp.publish.ozon.call.model.AttributesDTO;
import com.estone.erp.publish.ozon.call.model.ComplexAttributesDO;
import com.estone.erp.publish.ozon.call.model.request.CreateProductRequest;
import com.estone.erp.publish.ozon.call.model.response.CreateProductResponse;
import com.estone.erp.publish.ozon.common.OzonErrorConstant;
import com.estone.erp.publish.ozon.enums.OzonFeedTaskEnums;
import com.estone.erp.publish.ozon.enums.OzonPublishProcessEnums;
import com.estone.erp.publish.ozon.enums.OzonTemplateEnums;
import com.estone.erp.publish.ozon.enums.OzonTimePublishEnums;
import com.estone.erp.publish.ozon.handler.publish.parm.BaseParam;
import com.estone.erp.publish.ozon.handler.publish.parm.PublishCommonParam;
import com.estone.erp.publish.ozon.handler.template.OzonTemplateValidation;
import com.estone.erp.publish.ozon.model.OzonCategory;
import com.estone.erp.publish.ozon.model.OzonPublishProcess;
import com.estone.erp.publish.ozon.model.OzonTemplateModel;
import com.estone.erp.publish.ozon.model.OzonTimePublishQueue;
import com.estone.erp.publish.ozon.model.dto.template.OzonSkuDO;
import com.estone.erp.publish.ozon.service.*;
import com.estone.erp.publish.ozon.service.OzonProblemClassificationHandler;
import com.estone.erp.publish.ozon.utils.OzonImageUtils;
import com.estone.erp.publish.ozon.utils.OzonPublishUtils;
import com.estone.erp.publish.ozon.utils.OzonRetryUtil;
import com.estone.erp.publish.ozon.utils.OzonTemplateDataUtil;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2023-07-12 11:50
 */
@Slf4j
public abstract class PublishExecutor<T extends BaseParam> implements Executor<T> {
    /**
     * 正则判断
     */
    private static final Pattern pattern = Pattern.compile("[a-zA-Z]");
    /**
     * 翻译重试次数常量
     */
    private static final int MAX_TRANSLATE_RETRY_COUNT = 3;

    @Autowired
    protected OzonTemplateModelService ozonTemplateModelService;
    @Autowired
    private OzonApiClient ozonApiClient;
    @Autowired
    private OzonFeedTaskService feedTaskService;
    @Autowired
    protected OzonPublishProcessService publishProcessService;
    @Autowired
    protected OzonTemplateValidation templateValidation;
    @Autowired
    private OzonCategoryService ozonCategoryService;
    @Autowired
    private OzonAdminTemplateService ozonAdminTemplateService;
    @Autowired
    private OzonTimePublishQueueService timePublishQueueService;
    @Autowired
    private OzonProblemClassificationHandler problemClassificationHandler;

    public PublishExecutor() {
    }

    protected abstract OzonTemplateModel getTemplateData(T param) throws BusinessException;

    protected abstract OzonPublishProcess initPublishProcess(T param) throws BusinessException;

    @Override
    public void execute(T param) throws BusinessException {
        StopWatch started = StopWatch.createStarted();
        OzonTemplateModel templateData = null;
        FeedTask feedTask = null;
        String ruleName = param.getRuleName();
        try {
            templateData = getTemplateData(param);
            if (templateData == null) {
                return;
            }

            OzonPublishProcess ozonPublishProcess = initPublishProcess(param);
            feedTask = initFeedTask(templateData, param);
            // 最后的检查。要求数据的模版不能存在英文,
            lastCheck(templateData);

            List<CreateProductRequest> requests = builderRequest(templateData);
            CreateProductRequest request = requests.get(0);
            feedTask.setAttribute1(request.getOfferId());
            OzonTemplateModel finalTemplateData = templateData;
            OzonResponseResult<CreateProductResponse> responseResult = OzonRetryUtil.doRetryOzonApi(() -> {
                OzonResponseResult<CreateProductResponse> apiClientProduct = ozonApiClient.createProduct(finalTemplateData.getAccountNumber(), requests);
                if (apiClientProduct.isSuccess()) {
                    return apiClientProduct;
                }
                throw new RuntimeException(JSON.toJSONString(apiClientProduct));
            }, 3);
            String message = JSON.toJSONString(responseResult);
            log.info("模板:{},刊登结果:{}", templateData.getId(), message);
            // 修改状态、记录处理报告
            if (responseResult.isSuccess()) {
                CreateProductResponse result = responseResult.getResult();
                ozonPublishProcess.setTaskId(result.getTaskId());
                feedTaskService.runningTask(feedTask, message);
            } else {
                feedTaskService.failTask(feedTask, message);
                updateTemplatePublishStatus(templateData.getId(), param.getTimePublishQueueId(), message);
            }
            ozonPublishProcess.setTemplateId(templateData.getId());
            ozonPublishProcess.setAccountnumber(templateData.getAccountNumber());
            ozonPublishProcess.setStatus(OzonPublishProcessEnums.Status.UPLOAD_ITEM.getCode());
            ozonPublishProcess.setIsSuccess(responseResult.isSuccess());
            ozonPublishProcess.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
            ozonPublishProcess.setFeedTaskId(feedTask.getId());
            publishProcessService.updateByPrimaryKeySelective(ozonPublishProcess);
        } catch (Exception e) {
            log.error("Ozon 刊登异常,class :{}, execute error,param:{}", this.getClass().getSimpleName(), JSON.toJSONString(param), e);
            feedTaskService.failTask(feedTask, e.getMessage());
            if (templateData != null) {
                updateTemplatePublishStatus(templateData.getId(), param.getTimePublishQueueId(), e.getMessage());
            }
            throw new RuntimeException(e.getMessage());
        } finally {
            log.info("Ozon刊登,class:{},param:{},time:{}", this.getClass().getSimpleName(), JSON.toJSONString(param), started);
        }
    }

    /**
     * 最后判断标题和描述是否有英文，要求模版是已经保存了
     * @param templateData 模版
     */
    private void lastCheck(OzonTemplateModel templateData) {
        String title = templateData.getTitle();
        String description = cleanHtmlTags(templateData.getDescription());

        boolean update = false;

        // 处理标题翻译
        String translatedTitle = translateTextIfContainsEnglish(title, "标题");
        if (!title.equals(translatedTitle)) {
            templateData.setTitle(translatedTitle);
            update = true;
        }

        // 处理描述翻译
        String translatedDescription = translateTextIfContainsEnglish(description, "描述");
        if (!description.equals(translatedDescription)) {
            templateData.setDescription(translatedDescription);
            update = true;
        }

        // 如果有更新，保存到数据库
        if (update) {
            updateTemplateData(templateData);
        }
    }

    /**
     * 清理HTML标签
     * @param text 原始文本
     * @return 清理后的文本
     */
    private String cleanHtmlTags(String text) {
        if (text == null) {
            return "";
        }
        return text
                .replaceAll("<p>", "")
                .replaceAll("</p>", "")
                .replaceAll("<br>", "\n")
                .replaceAll("<br/>", "\n");
    }

    /**
     * 如果文本包含英文则进行翻译，支持重试机制
     * @param text 待检查和翻译的文本
     * @param textType 文本类型（用于错误提示）
     * @return 翻译后的文本，如果不包含英文则返回原文本
     * @throws RuntimeException 当翻译失败时抛出异常
     */
    private String translateTextIfContainsEnglish(String text, String textType) {
        if (!containsEnglish(text)) {
            return text;
        }

        String translatedText = text;
        for (int i = 0; i < MAX_TRANSLATE_RETRY_COUNT; i++) {
            try {
                translatedText = OzonTemplateDataUtil.translate(translatedText);
                if (!containsEnglish(translatedText)) {
                    log.info("{}翻译成功，重试次数: {}", textType, i + 1);
                    return translatedText;
                }
            } catch (Exception e) {
                log.warn("{}翻译失败，重试次数: {}, 错误信息: {}", textType, i + 1, e.getMessage());
                if (i == MAX_TRANSLATE_RETRY_COUNT - 1) {
                    throw new RuntimeException(OzonErrorConstant.TITLE_OR_DESCRIPTION_CONTENT_ENGLISH);
                }
            }
        }

        // 如果经过最大重试次数后仍包含英文，抛出异常
        log.error("{}经过{}次翻译后仍包含英文: {}", textType, MAX_TRANSLATE_RETRY_COUNT, translatedText);
        throw new RuntimeException(OzonErrorConstant.TITLE_OR_DESCRIPTION_CONTENT_ENGLISH);
    }

    /**
     * 更新模版数据到数据库
     * @param templateData 模版数据
     */
    private void updateTemplateData(OzonTemplateModel templateData) {
        Integer id = templateData.getId();
        OzonTemplateModel updateTemplate = new OzonTemplateModel();
        updateTemplate.setId(id);
        updateTemplate.setTitle(templateData.getTitle());
        updateTemplate.setDescription(templateData.getDescription());
        ozonTemplateModelService.updateByPrimaryKeySelective(updateTemplate);
        log.info("模版数据更新成功，模版ID: {}", id);
    }
    /**
     * 判断字符串是否包含需要翻译的字符
     * 检测范围包括：英文字母、数字、西文标点符号等非俄语字符
     * 适用于Ozon平台，用于识别需要从英文翻译为俄语的内容
     *
     * @param str 待检查的字符串
     * @return 如果包含需要翻译的字符返回true，否则返回false
     */
    public static boolean containsEnglish(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        return pattern.matcher(str).find();
    }
    private FeedTask initFeedTask(OzonTemplateModel templateData, T param) {
        return feedTaskService.initPublishFeedTask(param.getExcelId(), param.getRowIndex(), param.getRuleName(), Optional.ofNullable(templateData.getId()).orElse(0).longValue(), templateData.getAccountNumber(),
                OzonFeedTaskEnums.TaskType.PUBLISH.name(), templateData.getArticleNumber(), OzonTemplateDataUtil.getFirstSellerSku(templateData));
    }

    protected void failFeedTask(Integer templateId, String accountNumber, String articleNumber, String message, String sellerSku, String ruleName,
                                Integer excelId, Integer rowIndex) {
        feedTaskService.newPublishFailFeedTask(Optional.ofNullable(templateId).map(String::valueOf).orElse(""), accountNumber, articleNumber, message, sellerSku, ruleName, excelId, rowIndex);
    }

    private void updateTemplatePublishStatus(Integer templateId, Integer queueId, String message) {
        OzonTemplateModel ozonTemplateModel = new OzonTemplateModel();
        ozonTemplateModel.setId(templateId);
        ozonTemplateModel.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
        ozonTemplateModel.setPublishStatus(OzonTemplateEnums.PublishStatus.ERROR.getCode());
        problemClassificationHandler.handleTemplateProblemClassification(ozonTemplateModel, message, OzonFeedTaskEnums.TaskType.PUBLISH.name());
        ozonTemplateModelService.updateByPrimaryKeySelective(ozonTemplateModel);

        if (queueId != null) {
            // 更新定时刊登信息
            OzonTimePublishQueue queue = new OzonTimePublishQueue();
            queue.setId(queueId);
            queue.setStatus(OzonTimePublishEnums.Status.END.getCode());
            queue.setPublishStatus(OzonTemplateEnums.PublishStatus.ERROR.getCode());
            queue.setExtra(message);
            queue.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
            timePublishQueueService.updateByPrimaryKeySelective(queue);
        }
    }

    private List<CreateProductRequest> builderRequest(OzonTemplateModel template) {
        String variantData = template.getVariantData();
        List<AttributesDTO> allProductAttributes = getProductAttribute(template);
        List<AttributesDTO> productAttributes = OzonPublishUtils.getNotComplexAttribute(allProductAttributes);
        List<ComplexAttributesDO> complexAttributes = new ArrayList<>();
        if (StringUtils.isNotBlank(template.getVideo())) {
            OzonPublishUtils.addVideoAttribute(complexAttributes, template.getVideo(), template.getArticleNumber());
        }
        OzonPublishUtils.addOtherComplexAttribute(complexAttributes, productAttributes);
        List<OzonSkuDO> variantList = JSON.parseArray(variantData, OzonSkuDO.class);
        // 图片转存
        List<String> allImages = OzonPublishUtils.getAllImage(variantList);
        Map<String, String> imageMapping = OzonImageUtils.getImageMapping(allImages);
        // 获取当前类目的上级
        Integer parentCategoryId = null;
        if (null != template.getCategoryId()) {
            OzonCategory category = ozonCategoryService.selectCategoryByCid(Long.valueOf(template.getCategoryId()));
            if (category == null) {
                // 如果当前分类不存在，需禁用该分类对应admin范本
                ozonAdminTemplateService.disableByCategory(template.getCategoryId());
                throw new RuntimeException("当前分类不可用");
            }
            parentCategoryId = category.getParentId();
        }
        PublishCommonParam build = PublishCommonParam.builder()
                .categoryId(template.getCategoryId())
                .parentCategoryId(parentCategoryId)
                .title(template.getTitle())
                .currencyCode(template.getCurrencyCode())
                .vat(template.getVat())
                .imageMapping(imageMapping)
                .complexAttributes(complexAttributes)
                .productAttributes(productAttributes)
                .build();
        return OzonPublishUtils.generateRequest(variantList, build);
    }

    private List<AttributesDTO> getProductAttribute(OzonTemplateModel template) {
        List<AttributesDTO> productAttributes = new ArrayList<>();
        OzonPublishUtils.addAttributes(template.getCategoryAttribute(), productAttributes);
        OzonPublishUtils.addAttributes(template.getMergeAttribute(), productAttributes);
        // 添加描述
        OzonPublishUtils.addDescriptionAttribute(productAttributes, template.getDescription());
        return productAttributes;
    }

}
