package com.estone.erp.publish.ozon.handler.result.template;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.GoogleTranslateUtils;
import com.estone.erp.publish.ozon.call.OzonApiClient;
import com.estone.erp.publish.ozon.call.model.response.ProductImportInfoResponse;
import com.estone.erp.publish.ozon.enums.OzonEnums;
import com.estone.erp.publish.ozon.model.OzonPublishProcess;
import com.estone.erp.publish.ozon.service.OzonFeedTaskService;
import com.estone.erp.publish.ozon.service.OzonProblemClassificationHandler;
import com.estone.erp.publish.ozon.service.OzonPublishProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 任务结果解析
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractOzonUpdateInfoHandler implements OzonUpdateInfoHandler, OzonUpdateInfoAfterHandler {

    /**
     * 错误
     */
    private final static String ERROR = "error";

    @Resource
    protected OzonFeedTaskService ozonFeedTaskService;

    @Resource
    protected OzonPublishProcessService ozonPublishProcessService;

    @Resource
    protected OzonApiClient ozonApiClient;

    @Resource
    protected OzonProblemClassificationHandler problemClassificationHandler;

    @Override
    public void handler(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        try {
            // 任务是批量的，如果有一个还在加载中，就先不设置结果
            boolean hasPending = items.stream().map(ProductImportInfoResponse.ProductInfo::getStatus).anyMatch(status -> OzonEnums.ItemImportStatus.PENDING.name().equalsIgnoreCase(status));

            // 还在加载中，直接结束，等待下次
            if (hasPending) {
                // 如果还没有加载，那么就判断是否超时了
                checkTimeoutHandler(ozonPublishProcess);
                return;
            }
            // 检查所有的处理报告，如果没有更新到的就要设置失败
            checkAllFeedTask(ozonPublishProcess, items);
            // 全部加载完成了
            // 全部成功
            boolean allSuccess = allSuccess(items);
            if (allSuccess) {
                successHandler(ozonPublishProcess, items);
                return;
            }
            // 全部失败
            boolean allFailed = allFailed(items);
            if (allFailed) {
                failHandler(ozonPublishProcess, items);
                return;
            }
            // 部分成功
            halfSuccessHandler(ozonPublishProcess, items);

        } catch (Exception e) {
            log.error("解析任务结果异常: 任务id:{}", ozonPublishProcess.getId(), e);
            // 如果发生了异常，那么可能回导致一致异常，这时候这里就判断一下，是不是异常到最后也超时了，如果是，就直接超时处理
            checkTimeoutHandler(ozonPublishProcess);
        }
    }

    @Override
    public void timeout(OzonPublishProcess ozonPublishProcess) {
        checkTimeoutHandler(ozonPublishProcess);
    }

    /**
     * 部分成功处理
     *
     * @param ozonPublishProcess 处理
     * @param items              结果
     */
    private void halfSuccessHandler(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        completeProcess(ozonPublishProcess, Timestamp.valueOf(LocalDateTime.now()), JSON.toJSONString(items));
        afterHalfSuccessHandler(ozonPublishProcess, items);
    }

    /**
     * 全失败处理
     *
     * @param ozonPublishProcess 处理
     * @param items              结果
     */
    private void failHandler(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        // 检查有没有错误信息，如果一个都没有，就先跳过
        // 判断是否为1小时内的请求，是的话就等待下次
        Timestamp createdTime = ozonPublishProcess.getCreatedTime();
        long errorSize = items.stream().map(ProductImportInfoResponse.ProductInfo::getErrors).filter(CollectionUtils::isNotEmpty)
                .flatMap(a -> a.stream()).filter(Objects::nonNull)
                .count();
        if (errorSize == 0 && LocalDateTime.now().minusHours(1).isBefore(createdTime.toLocalDateTime())) {
            // 如果是半小时前的请求，就直接返回
            return;
        }

        completeProcess(ozonPublishProcess, Timestamp.valueOf(LocalDateTime.now()), JSON.toJSONString(items));
        afterFailHandler(ozonPublishProcess, items);
    }

    /**
     * 全成功处理
     *
     * @param ozonPublishProcess 处理
     * @param items              结果
     */
    private void successHandler(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        completeProcess(ozonPublishProcess, Timestamp.valueOf(LocalDateTime.now()), JSON.toJSONString(items));
        afterSuccessHandler(ozonPublishProcess, items);
    }

    /**
     * 检查超时问题
     *
     * @param ozonPublishProcess 处理信息
     */
    private void checkTimeoutHandler(OzonPublishProcess ozonPublishProcess) {
        Timestamp createdTime = ozonPublishProcess.getCreatedTime();
        LocalDateTime createdTimeLocalDateTime = createdTime.toLocalDateTime();
        LocalDateTime timeoutTime = LocalDateTime.now().minusHours(48);
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        // 超时48小时，且还没有操作过的, 这个先不抽公共的。调用不到的
        if (createdTimeLocalDateTime.isBefore(timeoutTime) && ozonPublishProcess.getIsSuccess()) {
            completeProcess(ozonPublishProcess, now, "timout");
            afterTimeoutHandler(ozonPublishProcess);
        }
    }

    /**
     * 公共部分成功处理
     *
     * @param ozonPublishProcess 处理
     * @param items              结果
     */
    @Override
    public void afterHalfSuccessHandler(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        // 这个修改重量的方法有点麻烦，需要知道哪些成功，哪些失败的
        List<ProductImportInfoResponse.ProductInfo> successItems = new ArrayList<>();
        List<ProductImportInfoResponse.ProductInfo> failItems = new ArrayList<>();
        for (ProductImportInfoResponse.ProductInfo item : items) {
            boolean success = isSuccess(item);
            if (success) {
                successItems.add(item);
            } else {
                failItems.add(item);
            }
        }
        if (CollectionUtils.isNotEmpty(successItems)) {
            afterSuccessHandler(ozonPublishProcess, successItems);
        }
        if (CollectionUtils.isNotEmpty(failItems)) {
            afterFailHandler(ozonPublishProcess, failItems);
        }
    }

    /**
     * 检查所有报告，如果平台没有返回对应的sellerSku ，就是要把处理报告设置成为失败
     * 批量那里要处理一下
     *
     * @param ozonPublishProcess ozonPublishProcess
     * @param items              所有商品
     */
    public void checkAllFeedTask(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items) {

    }

    /**
     * 明确只有单个处理报告的
     *
     * @param ozonPublishProcess 进程
     * @param items              item
     */
    protected void afterSingleFailHandler(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        Long feedTaskId = ozonPublishProcess.getFeedTaskId();
        if (feedTaskId == null) {
            return;
        }
        ozonFeedTaskService.updateFail(ozonPublishProcess.getFeedTaskId(), parseError(items), ozonPublishProcess.getTaskType());
    }

    /**
     * 明确只有单个处理报告的
     *
     * @param ozonPublishProcess 进程
     */
    protected void afterSingleTimeoutHandler(OzonPublishProcess ozonPublishProcess) {
        Long feedTaskId = ozonPublishProcess.getFeedTaskId();
        if (feedTaskId == null) {
            return;
        }
        ozonFeedTaskService.updateFail(ozonPublishProcess.getFeedTaskId(), "timeout", ozonPublishProcess.getTaskType());
    }

    /**
     * 更新
     *
     * @param ozonPublishProcess 处理信息
     * @param updatedTime        更新时间
     * @param remark             备注
     */
    private void completeProcess(OzonPublishProcess ozonPublishProcess, Timestamp updatedTime, String remark) {
        ozonPublishProcess.setIsSuccess(false);
        ozonPublishProcess.setUpdatedTime(updatedTime);
        ozonPublishProcess.setRemarks(remark);
        ozonPublishProcessService.updateByPrimaryKeySelective(ozonPublishProcess);
    }

    /**
     * 消息全失败判断
     *
     * @param items item
     * @return true/false
     */
    private static boolean allFailed(List<ProductImportInfoResponse.ProductInfo> items) {
        for (ProductImportInfoResponse.ProductInfo item : items) {
            boolean success = isSuccess(item);
            if (success) {
                return false;
            }
        }
        return true;
    }

    /**
     * 消息全成功判断
     *
     * @param items item
     * @return true/false
     */
    private static boolean allSuccess(List<ProductImportInfoResponse.ProductInfo> items) {
        for (ProductImportInfoResponse.ProductInfo item : items) {
            boolean success = isSuccess(item);
            if (!success) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断是否成功
     *
     * @param item item
     * @return true/false
     */
    private static boolean isSuccess(ProductImportInfoResponse.ProductInfo item) {
        String status = item.getStatus();
        boolean existErrors = CollectionUtils.isNotEmpty(item.getErrors());
        // 状态是成功的，且没有错误
        return (StringUtils.equalsIgnoreCase(status, OzonEnums.ItemImportStatus.IMPORTED.name()) || StringUtils.equalsIgnoreCase(status, OzonEnums.ItemImportStatus.SKIPPED.name())) && !existErrors;
    }

    /**
     * 解析错误信息
     *
     * @param items item
     * @return 错误信息
     */
    protected static String parseError(List<ProductImportInfoResponse.ProductInfo> items) {
        StringJoiner sj = new StringJoiner(";");
        for (ProductImportInfoResponse.ProductInfo item : items) {
            String s = parseError(item);
            sj.add(s);
        }
        return "审核失败：" + sj;
    }



    /**
     * 解析错误信息
     *
     * @param items item
     * @return 错误信息
     */
    protected static Map<String, String> parseErrorBySellerSku(List<ProductImportInfoResponse.ProductInfo> items) {
        Map<String, String> errorMap = new HashMap<>();
        for (ProductImportInfoResponse.ProductInfo item : items) {
            String sb = parseError(item);
            errorMap.put(item.getOfferId(), "审核失败：" + sb);
        }
        return errorMap;
    }

    /**
     * 解析错误信息
     *
     * @param item item
     * @return 错误信息
     */
    private static String parseError(ProductImportInfoResponse.ProductInfo item) {
        StringBuilder sb = new StringBuilder();
        String offerId = item.getOfferId();
        sb.append("sellerSku:[").append(offerId).append("]\n");
        List<ProductImportInfoResponse.ErrorText> errors = item.getErrors();
        for (ProductImportInfoResponse.ErrorText msg : errors) {
            String field = msg.getField();
            String attributeId = msg.getAttributeId();
            String attributeName = msg.getAttributeName();
            String description = msg.getDescription();
            String code = msg.getCode();
            sb.append("code:[").append(code).append("],attributeId:[").append(attributeId).append("],attributeName:[").append(attributeName)
                    .append("],field:[").append(field).append("],description:[").append(description).append("]\n");
        }
        return sb.toString();
    }

    private static String translateMsgRuToCn(String msg) {
        if (StringUtils.isBlank(msg)){
            return "";
        }
        String srcLang = GoogleTranslateUtils.changeDestLang("ru");
        String destLang = GoogleTranslateUtils.changeDestLang("cn");
        return GoogleTranslateUtils.translate(srcLang, destLang, msg, 3);
    }

    
    private static String translateMsgEnToCn(String msg) {
        if (StringUtils.isBlank(msg)){
            return "";
        }
        String srcLang = GoogleTranslateUtils.changeDestLang("en");
        String destLang = GoogleTranslateUtils.changeDestLang("cn");
        return GoogleTranslateUtils.translate(srcLang, destLang, msg, 3);
    }

}
