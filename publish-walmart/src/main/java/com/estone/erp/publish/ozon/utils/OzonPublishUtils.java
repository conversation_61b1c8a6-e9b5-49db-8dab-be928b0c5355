package com.estone.erp.publish.ozon.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItemAttributes;
import com.estone.erp.publish.ozon.call.model.AttributesDTO;
import com.estone.erp.publish.ozon.call.model.ComplexAttributesDO;
import com.estone.erp.publish.ozon.call.model.request.CreateProductRequest;
import com.estone.erp.publish.ozon.call.model.response.ProductImportInfoResponse;
import com.estone.erp.publish.ozon.enums.OzonEnums;
import com.estone.erp.publish.ozon.handler.publish.parm.PublishCommonParam;
import com.estone.erp.publish.ozon.model.OzonPublishProcess;
import com.estone.erp.publish.ozon.model.dto.OzonUpdateDO;
import com.estone.erp.publish.ozon.model.dto.OzonUpdatePackageDO;
import com.estone.erp.publish.ozon.model.dto.sync.OzonItemDO;
import com.estone.erp.publish.ozon.model.dto.template.OzonSkuDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ozon 刊登和编辑工具
 */
public class OzonPublishUtils {

    public static void addVideoAttribute(List<ComplexAttributesDO> complexAttributes, String video, String name) {
        Map<String, String> urlMapping = new HashMap<>();
        if (video != null && !video.contains("https")) {
            Map<String, String> imageMapping = OzonImageUtils.getImageMapping(Collections.singletonList(video));
            urlMapping = imageMapping;
        }
        List<AttributesDTO> attributesList = addVideoAttribute(urlMapping.get(video), name);
        ComplexAttributesDO complexAttributesDO = new ComplexAttributesDO();
        complexAttributesDO.setAttributes(attributesList);
        complexAttributes.add(complexAttributesDO);
    }

    /**
     * 添加视频属性
     * @param video 远程的视频链接
     * @param name 名称
     * @return 视频属性
     */
    public static List<AttributesDTO> addVideoAttribute(String video, String name) {
        List<AttributesDTO> attributesList = new ArrayList<>();
        AttributesDTO linkAttr = new AttributesDTO();
        linkAttr.setId(OzonEnums.AttributeCode.VIDEO_LINK.getCode());
        linkAttr.setComplexId(OzonEnums.AttributeCode.VIDEO_CMX_ID.getCode());
        AttributesDTO.ValuesDTO linkValue = new AttributesDTO.ValuesDTO();
        linkValue.setValue(video);
        linkAttr.setValues(Collections.singletonList(linkValue));

        AttributesDTO nameAttr = new AttributesDTO();
        nameAttr.setId(OzonEnums.AttributeCode.VIDEO_NAME.getCode());
        nameAttr.setComplexId(OzonEnums.AttributeCode.VIDEO_CMX_ID.getCode());
        AttributesDTO.ValuesDTO nameValue = new AttributesDTO.ValuesDTO();
        nameValue.setValue(name);
        nameAttr.setValues(Collections.singletonList(nameValue));

        attributesList.add(linkAttr);
        attributesList.add(nameAttr);
        return attributesList;
    }

    public static void addAttributes(String attribute, List<AttributesDTO> data) {
        if (StringUtils.isNotBlank(attribute)) {
            List<AttributesDTO> categoryAttributes = JSON.parseArray(attribute, AttributesDTO.class);
            Set<Long> collect = data.stream().map(AttributesDTO::getId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(categoryAttributes)) {
                for (AttributesDTO categoryAttribute : categoryAttributes) {
                    if (!collect.contains(categoryAttribute.getId())) {
                        data.add(categoryAttribute);
                    }
                }
            }
        }
    }

    /**
     * 描述，如果存在了就要替换
     *
     * @param productAttributes 产品属性
     * @param description       描述
     */
    public static void addDescriptionAttribute(List<AttributesDTO> productAttributes, String description) {
        Optional<AttributesDTO> first = productAttributes.stream().filter(a -> Objects.equals(a.getId(), OzonEnums.AttributeCode.DESCRIPTION.getCode())).findFirst();
        if (first.isPresent()) {
            AttributesDTO attributesDTO = first.get();
            List<AttributesDTO.ValuesDTO> values = new ArrayList<>();
            AttributesDTO.ValuesDTO valuesDTO = new AttributesDTO.ValuesDTO();
            valuesDTO.setValue(description);
            values.add(valuesDTO);
            attributesDTO.setValues(values);
        } else {
            // 商品描述
            AttributesDTO attributesDTO = new AttributesDTO();
            attributesDTO.setId(OzonEnums.AttributeCode.DESCRIPTION.getCode());
            attributesDTO.setComplexId(0L);
            AttributesDTO.ValuesDTO valuesDTO = new AttributesDTO.ValuesDTO();
            valuesDTO.setValue(description);
            attributesDTO.setValues(Collections.singletonList(valuesDTO));
            productAttributes.add(attributesDTO);
        }
    }


    public static List<AttributesDTO> getComplexAttribute(List<AttributesDTO> allProductAttributes) {
        return allProductAttributes.stream().filter(a -> a.getComplexId() == null || a.getComplexId() != 0).collect(Collectors.toList());
    }

    public static List<AttributesDTO> getNotComplexAttribute(List<AttributesDTO> allProductAttributes) {
        return allProductAttributes.stream().filter(a -> a.getComplexId() != null && a.getComplexId() == 0).collect(Collectors.toList());
    }

    public static void addOtherComplexAttribute(List<ComplexAttributesDO> complexAttributes, List<AttributesDTO> productAttributes) {
        List<AttributesDTO> complexAttribute = getComplexAttribute(productAttributes);
        if (CollectionUtils.isNotEmpty(complexAttribute)) {
            if (CollectionUtils.isEmpty(complexAttributes)) {
                ComplexAttributesDO complexAttributesDO = new ComplexAttributesDO();
                complexAttributesDO.setAttributes(complexAttribute);
                complexAttributes.add(complexAttributesDO);
            } else {
                // 需要判断是否有同样的值，有就跳过
                ComplexAttributesDO complexAttributesDO = complexAttributes.get(0);
                List<AttributesDTO> attributes = Optional.ofNullable(complexAttributesDO.getAttributes()).orElseGet(ArrayList::new);
                Set<Long> collect = attributes.stream().map(a -> a.getId()).collect(Collectors.toSet());
                for (AttributesDTO attributesDTO : complexAttribute) {
                    Long id = attributesDTO.getId();
                    if (collect.contains(id)) {
                        continue;
                    }
                    collect.add(id);
                    attributes.add(attributesDTO);
                }
                complexAttributesDO.setAttributes(attributes);
            }
        }
    }

    public static List<String> getAllImage(List<OzonSkuDO> variantList) {
        return variantList.stream().map(skuDO -> {
            List<String> images = Optional.ofNullable(skuDO.getImages()).orElseGet(ArrayList::new);
            if (StringUtils.isNotBlank(skuDO.getMainImage())) {
                images.add(skuDO.getMainImage());
            }
            return images;
        }).flatMap(Collection::stream).distinct().collect(Collectors.toList());
    }

    public static List<CreateProductRequest> generateRequest(List<OzonSkuDO> variantList, PublishCommonParam param) {
        List<CreateProductRequest> requests = new ArrayList<>();
        for (OzonSkuDO skuDO : variantList) {
            CreateProductRequest productRequest = new CreateProductRequest();
            productRequest.setName(param.getTitle());
            productRequest.setVat(param.getVat());

            productRequest.setCurrencyCode(param.getCurrencyCode());
            productRequest.setCategoryId(param.getParentCategoryId());
            productRequest.setTypeId(param.getCategoryId());
            productRequest.setOfferId(skuDO.getSellerSku());
            // 属性组装
            List<AttributesDTO> skuAttributes = new ArrayList<>(param.getProductAttributes());
            OzonPublishUtils.addAttributes(skuDO.getVariantAttribute(), skuAttributes);
            productRequest.setAttributes(skuAttributes);
            productRequest.setBarcode(skuDO.getBarcode());
            if (CollectionUtils.isNotEmpty(param.getComplexAttributes())) {
                productRequest.setComplexAttributes(param.getComplexAttributes());
            }
            productRequest.setDepth(skuDO.getDepth());
            productRequest.setWidth(skuDO.getWidth());
            productRequest.setHeight(skuDO.getHeight());
            productRequest.setDimensionUnit(skuDO.getDimensionUnit());
            List<String> newImages = Optional.ofNullable(skuDO.getImages()).orElseGet(ArrayList::new).stream()
                    .map(imgUrl -> MapUtils.getString(param.getImageMapping(), imgUrl, null))
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            productRequest.setImages(newImages);
            productRequest.setPrice(String.valueOf(skuDO.getSalePrice()));
            productRequest.setOldPrice(String.valueOf(skuDO.getPrice()));
            String mainImage = MapUtils.getString(param.getImageMapping(), skuDO.getMainImage(), null);
            productRequest.setPrimaryImage(mainImage);
            productRequest.setWeight(skuDO.getWeight());
            productRequest.setWeightUnit(skuDO.getWeightUnit());
            requests.add(productRequest);
        }
        return requests;
    }

    public static List<OzonUpdateDO> getUploadStockInfo(OzonPublishProcess publishProcess,
                                                        List<ProductImportInfoResponse.ProductInfo> items,
                                                        List<OzonSkuDO> variantSku,
                                                        String job) {
        Map<String, OzonSkuDO> sellerSkuAndSkuDoMap = variantSku.stream().collect(Collectors.toMap(OzonSkuDO::getSellerSku, Function.identity(), (o1, o2) -> o1));
        return items.stream().map(item -> {
                    String offerId = item.getOfferId();
                    OzonSkuDO ozonSkuDO = sellerSkuAndSkuDoMap.get(offerId);
                    if (ozonSkuDO == null) {
                        return null;
                    }
                    OzonUpdateDO ozonUpdateDO = new OzonUpdateDO();
                    ozonUpdateDO.setProductId(item.getProductId());
                    ozonUpdateDO.setAccountNumber(publishProcess.getAccountnumber());
                    ozonUpdateDO.setSku(ozonSkuDO.getSku());
                    ozonUpdateDO.setSellerSku(ozonSkuDO.getSellerSku());
                    ozonUpdateDO.setWarehouseId(ozonSkuDO.getWarehouseId());
                    ozonUpdateDO.setUpdateBeforeStock(0);
                    ozonUpdateDO.setUpdateAfterStock(ozonSkuDO.getQuantity());
                    ozonUpdateDO.setJob(job);
                    return ozonUpdateDO;
                }).filter(Objects::nonNull)
                .filter(a -> a.getUpdateAfterStock() != null && a.getUpdateAfterStock() > 0)
                .collect(Collectors.toList());
    }

    public static CreateProductRequest convertUpdateRequest(OzonItemDO ozonItemDO, OzonUpdatePackageDO ozonUpdatePackageDO) {
        CreateProductRequest productRequest = new CreateProductRequest();
        productRequest.setName(ozonItemDO.getName());
        productRequest.setOfferId(ozonItemDO.getSellerSku());
        productRequest.setImages(CommonUtils.splitList(ozonItemDO.getImages(), ","));
        productRequest.setPrimaryImage(ozonItemDO.getMainImage());
        productRequest.setPrice(ozonItemDO.getPrice());
        productRequest.setCurrencyCode(ozonItemDO.getCurrencyCode());
        productRequest.setOldPrice(ozonItemDO.getOldPrice());
        productRequest.setVat(ozonItemDO.getVat());

        productRequest.setCategoryId(ozonItemDO.getParentCategoryId().intValue());
        productRequest.setTypeId(ozonItemDO.getCategoryId().intValue());
        // 属性组装
        List<EsOzonItemAttributes> attributes = ozonUpdatePackageDO.getAttributes();
        List<AttributesDTO> attributesDTOS = new ArrayList<>();
        for (EsOzonItemAttributes attribute : attributes) {
            AttributesDTO attributesDTO = new AttributesDTO();
            List<EsOzonItemAttributes.AttributeValue> values = attribute.getValues();
            List<AttributesDTO.ValuesDTO> valuesDTOS = new ArrayList<>();
            for (EsOzonItemAttributes.AttributeValue value : values) {
                AttributesDTO.ValuesDTO valuesDTO = new AttributesDTO.ValuesDTO();
                valuesDTO.setDictionaryValueId(value.getDictionaryValueId());
                valuesDTO.setValue(value.getValue());
                valuesDTOS.add(valuesDTO);
            }
            attributesDTO.setValues(valuesDTOS);
            if (attribute.getAttributeId() == null || attribute.getAttributeId() == 0) {
                if (attribute.getId() != null) {
                    attributesDTO.setId(attribute.getId());
                }
            }
            attributesDTO.setComplexId(attribute.getComplexId());
            attributesDTOS.add(attributesDTO);
        }
        productRequest.setAttributes(attributesDTOS);

        List<AttributesDTO> complexAttributes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ozonUpdatePackageDO.getComplexAttributes())) {
            for (EsOzonItemAttributes attribute : ozonUpdatePackageDO.getComplexAttributes()) {
                AttributesDTO attributesDTO = new AttributesDTO();
                List<EsOzonItemAttributes.AttributeValue> values = attribute.getValues();
                List<AttributesDTO.ValuesDTO> valuesDTOS = new ArrayList<>();
                for (EsOzonItemAttributes.AttributeValue value : values) {
                    AttributesDTO.ValuesDTO valuesDTO = new AttributesDTO.ValuesDTO();
                    valuesDTO.setDictionaryValueId(value.getDictionaryValueId());
                    valuesDTO.setValue(value.getValue());
                    valuesDTOS.add(valuesDTO);
                }
                attributesDTO.setValues(valuesDTOS);
                if (attribute.getAttributeId() == null || attribute.getAttributeId() == 0) {
                    if (attribute.getId() != null) {
                        attributesDTO.setId(attribute.getId());
                    }
                }
                attributesDTO.setComplexId(attribute.getComplexId());
                complexAttributes.add(attributesDTO);
            }
            if (CollectionUtils.isNotEmpty(complexAttributes)) {
                List<ComplexAttributesDO> complexAttributes1 = productRequest.getComplexAttributes();
                if (complexAttributes1 == null) {
                    complexAttributes1 = new ArrayList<>();
                }
                ComplexAttributesDO complexAttributesDO = new ComplexAttributesDO();
                complexAttributesDO.setAttributes(complexAttributes);
                complexAttributes1.add(complexAttributesDO);
                productRequest.setComplexAttributes(complexAttributes1);
            }
        }
        // 尺寸
        String packageSizeJson = ozonUpdatePackageDO.getPackageSizeJson();
        Map<String, Object> attributesMap = JSON.parseObject(packageSizeJson, new TypeReference<>() {
        });
        if (MapUtils.isNotEmpty(attributesMap)) {
            productRequest.setDepth((Integer) attributesMap.get("depth"));
            productRequest.setWidth((Integer) attributesMap.get("width"));
            productRequest.setHeight((Integer) attributesMap.get("height"));
            productRequest.setDimensionUnit((String) attributesMap.get("unit"));
        }

        // 重量
        productRequest.setWeight((int) Math.round(ozonUpdatePackageDO.getWeight() * 1000));
        productRequest.setWeightUnit("g");
        return productRequest;
    }

    /**
     * 描述，如果存在了就要替换
     *
     * @param productAttributes 产品属性
     * @param description       描述
     */
    public static void addTemplateDescriptionAttribute(List<EsOzonItemAttributes> productAttributes, String description) {
        Optional<EsOzonItemAttributes> first = productAttributes.stream().filter(a -> Objects.equals(a.getId(), OzonEnums.AttributeCode.DESCRIPTION.getCode())).findFirst();
        if (first.isPresent()) {
            EsOzonItemAttributes attributesDTO = first.get();
            List<EsOzonItemAttributes.AttributeValue> values = new ArrayList<>();
            EsOzonItemAttributes.AttributeValue valuesDTO = new EsOzonItemAttributes.AttributeValue();
            valuesDTO.setValue(description);
            values.add(valuesDTO);
            attributesDTO.setValues(values);
        } else {
            // 商品描述
            EsOzonItemAttributes attributesDTO = new EsOzonItemAttributes();
            attributesDTO.setId(OzonEnums.AttributeCode.DESCRIPTION.getCode());
            attributesDTO.setComplexId(0L);
            EsOzonItemAttributes.AttributeValue valuesDTO = new EsOzonItemAttributes.AttributeValue();
            valuesDTO.setValue(description);
            attributesDTO.setValues(Collections.singletonList(valuesDTO));
            productAttributes.add(attributesDTO);
        }
    }

    public static void addTypeAttribute(List<AttributesDTO> productAttributes, Long categoryId) {
        Optional<AttributesDTO> first = productAttributes.stream().filter(a -> Objects.equals(a.getId(),  OzonAttributeUtil.TYPE_ID.longValue())).findFirst();
        if (first.isPresent()) {
            AttributesDTO attributesDTO = first.get();
            List<AttributesDTO.ValuesDTO> values = new ArrayList<>();
            AttributesDTO.ValuesDTO valuesDTO = new AttributesDTO.ValuesDTO();
            valuesDTO.setDictionaryValueId(categoryId);
            values.add(valuesDTO);
            attributesDTO.setValues(values);
        } else {
            // 商品描述
            AttributesDTO attributesDTO = new AttributesDTO();
            attributesDTO.setId(OzonAttributeUtil.TYPE_ID.longValue());
            attributesDTO.setComplexId(0L);
            AttributesDTO.ValuesDTO valuesDTO = new AttributesDTO.ValuesDTO();
            valuesDTO.setDictionaryValueId(categoryId);
            attributesDTO.setValues(Collections.singletonList(valuesDTO));
            productAttributes.add(attributesDTO);
        }
    }
}
