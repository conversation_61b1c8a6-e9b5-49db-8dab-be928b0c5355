package com.estone.erp.publish.ozon.handler.publish.parm;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-07-13 16:08
 */
@Data
public class SpuPublishParam implements BaseParam{
    private String accountNumber;
    private String user;
    private String spu;
    private Integer skuDataSource;
    private Integer publishType;
    private Integer templateId;
    private Integer timePublishQueueId;
    private Integer categoryId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则json
     */
    private String ruleJson;

    /**
     * 配置类型
     */
    private String configSource;

    @Override
    public String getRuleName() {
        return ruleName;
    }

    @Override
    public Integer getTimePublishQueueId() {
        return this.timePublishQueueId;
    }

    @Override
    public Integer getRowIndex() {
        return null;
    }

    @Override
    public Integer getExcelId() {
        return null;
    }
}
