package com.estone.erp.publish.ozon.excel.handler;

import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.ozon.excel.model.UpdateWeightExcel;
import com.estone.erp.publish.ozon.handler.OzonBatchUpdateListingInfoHandler;
import com.estone.erp.publish.platform.enums.SmallPlatformDownloadEnums;
import com.estone.erp.publish.platform.model.SmallPlatformExcelDownloadLog;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 修改重量处理器
 */
@Component
public class UpdateWeightExcelHandler extends AbstractListingExcelHandler<UpdateWeightExcel> implements DelayDownloadHandler {

    @Autowired
    private OzonBatchUpdateListingInfoHandler ozonBatchUpdateListingInfoHandler;

    @Override
    public SmallPlatformDownloadEnums.Type getType() {
        return SmallPlatformDownloadEnums.Type.UPDATE_WEIGHT;
    }

    @Override
    public void writeFile(SmallPlatformExcelDownloadLog downloadLog) {
        InputStream ins = prepareExcelInputStream(downloadLog);
        List<UpdateWeightExcel> resultList = parseExcelFile(ins, UpdateWeightExcel.class);
        
        // 设置excelId
        Integer id = downloadLog.getId();
        resultList.forEach(item -> item.setExcelId(id));
        
        processExcelData(resultList, downloadLog.getCreateBy());
        updateDownloadLog(downloadLog, resultList);
    }

    @Override
    protected void validateSpecificColumns(Map<Integer, String> data) {
        // 验证重量列
        String s1 = data.get(4);
        if (!"重量（KG）*".equals(s1)) {
            throw new RuntimeException("excel文件格式错误, 重量列顺序不对");
        }
    }

    @Override
    protected UpdateWeightExcel createExcelRowObject(Map<Integer, String> data, int rowIndex) {
        UpdateWeightExcel updateWeightExcel = new UpdateWeightExcel();
        updateWeightExcel.setAccountNumber(data.get(0));
        updateWeightExcel.setProductId(data.get(1));
        updateWeightExcel.setSellerSku(data.get(2));
        updateWeightExcel.setSku(data.get(3));
        updateWeightExcel.setAfterWeight(data.get(4));
        updateWeightExcel.setRowIndex(rowIndex);
        return updateWeightExcel;
    }

    @Override
    protected void processExcelData(List<UpdateWeightExcel> resultList, String username) {
        DataContextHolder.setUsername(username);
        
        // 必填校验
        List<UpdateWeightExcel> collect = resultList.stream()
                .filter(a -> StringUtils.isBlank(a.getRemark()))
                .collect(Collectors.toList());
        
        // 权限校验
        collect = checkAuth(collect, username);
        
        // 数据重复性校验
        collect = checkRepeat(collect, (updateWeightExcel) -> {
            String accountNumber = updateWeightExcel.getAccountNumber();
            String productId = updateWeightExcel.getProductId();
            return accountNumber + "_" + productId;
        });
        
        // 是否存在在线列表
        collect = checkListing(collect, new String[] {"id", "productId", "sellerSku"}, (esOzonItem, updateWeightExcel) -> {
            // 不需要额外设置信息
        });

        List<UpdateWeightExcel> errorList = resultList.stream()
                .filter(a -> a.getResult() != null)
                .collect(Collectors.toList());
        
        ozonBatchUpdateListingInfoHandler.batchUpdateWeight(collect, errorList);
    }

}