package com.estone.erp.publish.ozon.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.ozon.common.OzonExecutors;
import com.estone.erp.publish.ozon.enums.OzonFeedTaskEnums;
import com.estone.erp.publish.ozon.model.OzonAction;
import com.estone.erp.publish.ozon.model.OzonActionCriteria;
import com.estone.erp.publish.ozon.model.dto.OzonSyncAction;
import com.estone.erp.publish.ozon.service.OzonActionService;
import com.estone.erp.publish.ozon.service.OzonFeedTaskService;
import com.estone.erp.publish.system.feedTask.model.FeedTaskCriteria;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> ozon_action
 * 2023-11-17 15:37:11
 */
@Slf4j
@RestController
@RequestMapping("ozonAction")
public class OzonActionController {
    @Resource
    private OzonActionService ozonActionService;

    @Resource
    private SystemParamService systemParamService;

    @Resource
    private OzonFeedTaskService ozonFeedTaskService;

    @PostMapping
    public ApiResult<?> postOzonAction(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchOzonAction": // 查询列表
                    CQuery<OzonActionCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<OzonActionCriteria>>() {
                    });
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<OzonAction> results = ozonActionService.search(cquery);
                    return results;
                case "addOzonAction": // 添加
                    OzonAction ozonAction = requestParam.getArgsValue(new TypeReference<OzonAction>() {
                    });
                    ozonActionService.insert(ozonAction);
                    return ApiResult.newSuccess(ozonAction);
            }
        }
        return ApiResult.newSuccess();
    }

    @PostMapping("/syncOzonAction")
    public ApiResult<?> syncOzonAction(@RequestBody OzonSyncAction ozonSyncAction) {
        if (ozonSyncAction.getAccountNumberList() == null) {
            return ApiResult.newError("请选择同步账号");
        }
        String userName = WebUtils.getUserName();
        for (String accountNumber : ozonSyncAction.getAccountNumberList()) {
            boolean b = ozonFeedTaskService.existExecutingTask(accountNumber, OzonFeedTaskEnums.TaskType.SYNC_ACTION.name(), -1);
            if (b) {
                ozonFeedTaskService.addFailFeedTask(null, accountNumber, OzonFeedTaskEnums.TaskType.SYNC_ACTION.name(), null,"该账号正在同步中，请稍后再试");
                continue;
            }
            OzonExecutors.ACTION_POOL.execute(() -> {
                ozonActionService.syncAction( accountNumber, 20000, 7, userName);
            });
        }
        return ApiResult.newSuccess("同步请求成功,请前往处理报告查询结果");
    }

    private List<String> getMoreActionAccounts() {
        SystemParam system = systemParamService.querySystemParamByCodeKey("OZON.MORE_ACTION_ACCOUNT");
        if (null == system || StringUtils.isBlank(system.getParamValue())) {
            return Collections.emptyList();
        }

        return CommonUtils.splitList(system.getParamValue(), ",");
    }

    @GetMapping("/getAccountTypeList")
    public ApiResult<List<String>> getAccountTypeList() {
        List<String> accountTypeList = ozonActionService.getAccountTypeList();
        return ApiResult.newSuccess(accountTypeList);
    }

    /**
     * 导出Feed Task数据
     * @param requestParam 请求参数
     * @return 下载日志ID
     */
    @PostMapping("/exportFeedTasks")
    public ApiResult<String> exportFeedTasks(@RequestBody ApiRequestParam<String> requestParam) {
        try {
            CQuery<FeedTaskCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<FeedTaskCriteria>>() {
            });
            ozonFeedTaskService.exportFeedTasks(cquery);
            return ApiResult.newSuccess("导出中...请稍后在导出日志中下载");
        } catch (Exception e) {
            log.error("Export feed tasks error", e);
            return ApiResult.newError("导出失败：" + e.getMessage());
        }
    }
    /**
     * 导出Feed Task数据
     * @param requestParam 请求参数
     * @return 下载日志ID
     */
    @PostMapping("/exportPublishFeedTasks")
    public ApiResult<String> exportPublishFeedTasks(@RequestBody ApiRequestParam<String> requestParam) {
        try {
            CQuery<FeedTaskCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<FeedTaskCriteria>>() {
            });
            ozonFeedTaskService.exportPublishFeedTasks(cquery);
            return ApiResult.newSuccess("导出中...请稍后在导出日志中下载");
        } catch (Exception e) {
            log.error("Export feed tasks error", e);
            return ApiResult.newError("导出失败：" + e.getMessage());
        }
    }
}
