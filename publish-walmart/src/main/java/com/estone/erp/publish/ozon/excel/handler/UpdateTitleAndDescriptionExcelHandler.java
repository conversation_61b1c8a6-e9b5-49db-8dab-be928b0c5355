package com.estone.erp.publish.ozon.excel.handler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.ozon.excel.model.UpdateTitleAndDescriptionExcel;
import com.estone.erp.publish.ozon.handler.OzonBatchUpdateListingInfoHandler;
import com.estone.erp.publish.ozon.handler.template.OzonTemplateValidation;
import com.estone.erp.publish.ozon.utils.OzonTemplateDataUtil;
import com.estone.erp.publish.platform.enums.SmallPlatformDownloadEnums;
import com.estone.erp.publish.platform.model.SmallPlatformExcelDownloadLog;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 修改标题和描述处理器
 */
@Component
public class UpdateTitleAndDescriptionExcelHandler extends AbstractListingExcelHandler<UpdateTitleAndDescriptionExcel> implements DelayDownloadHandler {

    @Autowired
    private OzonBatchUpdateListingInfoHandler ozonBatchUpdateListingInfoHandler;
    @Autowired
    private OzonTemplateValidation ozonTemplateValidation;

    @Override
    public SmallPlatformDownloadEnums.Type getType() {
        return SmallPlatformDownloadEnums.Type.TITLE_AND_DESCPRICTION;
    }

    @Override
    public void writeFile(SmallPlatformExcelDownloadLog downloadLog) {
        InputStream ins = prepareExcelInputStream(downloadLog);
        List<UpdateTitleAndDescriptionExcel> resultList = parseExcelFile(ins, UpdateTitleAndDescriptionExcel.class);
        
        // 设置excelId
        Integer id = downloadLog.getId();
        resultList.forEach(item -> item.setExcelId(id));
        
        processExcelData(resultList, downloadLog.getCreateBy());
        updateDownloadLog(downloadLog, resultList);
    }

    @Override
    protected void validateSpecificColumns(Map<Integer, String> data) {
        // 验证重量列
        String s1 = data.get(4);
        if (!"标题".equals(s1)) {
            throw new RuntimeException("excel文件格式错误, 标题列顺序不对");
        }
        String s2 = data.get(5);
        if (!"描述".equals(s2)) {
            throw new RuntimeException("excel文件格式错误, 描述列顺序不对");
        }
    }

    @Override
    protected UpdateTitleAndDescriptionExcel createExcelRowObject(Map<Integer, String> data, int rowIndex) {
        UpdateTitleAndDescriptionExcel updateTitleAndDescriptionExcel = new UpdateTitleAndDescriptionExcel();
        updateTitleAndDescriptionExcel.setAccountNumber(data.get(0));
        updateTitleAndDescriptionExcel.setProductId(data.get(1));
        updateTitleAndDescriptionExcel.setSellerSku(data.get(2));
        updateTitleAndDescriptionExcel.setSku(data.get(3));
        updateTitleAndDescriptionExcel.setTitle(data.get(4));
        updateTitleAndDescriptionExcel.setDescription(data.get(5));
        updateTitleAndDescriptionExcel.setRowIndex(rowIndex);
        return updateTitleAndDescriptionExcel;
    }

    @Override
    protected void processExcelData(List<UpdateTitleAndDescriptionExcel> resultList, String username) {
        DataContextHolder.setUsername(username);
        
        // 必填校验
        List<UpdateTitleAndDescriptionExcel> collect = resultList.stream()
                .filter(a -> StringUtils.isBlank(a.getRemark()))
                .collect(Collectors.toList());
        
        // 权限校验
        collect = checkAuth(collect, username);
        
        // 数据重复性校验
        collect = checkRepeat(collect, (updateTitleAndDescriptionExcel) -> {
            String accountNumber = updateTitleAndDescriptionExcel.getAccountNumber();
            String productId = updateTitleAndDescriptionExcel.getProductId();
            return accountNumber + "_" + productId;
        });
        
        // 是否存在在线列表
        collect = checkListing(collect, new String[] {"id", "productId", "sellerSku", "ozonSku"}, (esOzonItem, updateTitleAndDescriptionExcel) -> {
            updateTitleAndDescriptionExcel.setOzonSku(esOzonItem.getOzonSku());
        });

        // 删除侵权词
        for (UpdateTitleAndDescriptionExcel updateTitleAndDescriptionExcel : collect) {
            try {
                ozonTemplateValidation.filterInfringementWord(Optional.ofNullable(updateTitleAndDescriptionExcel.getTitle()).orElse(""), Optional.ofNullable(updateTitleAndDescriptionExcel.getDescription()).orElse(""),
                        updateTitleAndDescriptionExcel::setTitle, updateTitleAndDescriptionExcel::setDescription);
                List<String> list = new ArrayList<>();
                list.add(Optional.ofNullable(updateTitleAndDescriptionExcel.getTitle()).orElse(""));
                list.add(Optional.ofNullable(updateTitleAndDescriptionExcel.getDescription()).orElse(""));
                String translate = OzonTemplateDataUtil.translate(list);
                if (StringUtils.isNotBlank(translate)) {
                    List<String> transList = JSON.parseArray(translate, String.class);
                    if (transList.size() == 2) {
                        updateTitleAndDescriptionExcel.setNewTitle(transList.get(0));
                        updateTitleAndDescriptionExcel.setNewDescription(transList.get(1));
                    }
                }
                if (StringUtils.isNotBlank(updateTitleAndDescriptionExcel.getNewTitle())) {
                    updateTitleAndDescriptionExcel.setUpdateTitle(true);
                } else {
                    updateTitleAndDescriptionExcel.setUpdateDesc(false);
                }
                if (StringUtils.isNotBlank(updateTitleAndDescriptionExcel.getNewDescription())) {
                    updateTitleAndDescriptionExcel.setUpdateDesc(true);
                } else {
                    updateTitleAndDescriptionExcel.setUpdateDesc(false);
                }
            } catch (Exception e) {
                updateTitleAndDescriptionExcel.setResult("失败");
                updateTitleAndDescriptionExcel.setRemark("删除侵权词或翻译为俄语失败");
            }
        }
        List<UpdateTitleAndDescriptionExcel> errorList = resultList.stream()
                .filter(a -> a.getResult() != null)
                .collect(Collectors.toList());

        List<UpdateTitleAndDescriptionExcel> collect1 = collect.stream().filter(a -> StringUtils.isBlank(a.getResult())).collect(Collectors.toList());

        ozonBatchUpdateListingInfoHandler.batchUpdateListingTitleAndDescVo(collect1, errorList, "Excel");
    }

}