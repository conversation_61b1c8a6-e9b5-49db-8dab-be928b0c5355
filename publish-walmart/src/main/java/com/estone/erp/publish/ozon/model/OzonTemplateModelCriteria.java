package com.estone.erp.publish.ozon.model;

import com.estone.erp.publish.common.util.CommonUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR> ozon_template_model
 * 2023-07-10 17:28:26
 */
@Data
public class OzonTemplateModelCriteria extends OzonTemplateModel {
    private static final long serialVersionUID = 1L;

    /**
     * 模板编号逗号分隔
     */
    private String idStr;

    /**
     * 账号集合
     */
    private List<String> accountNumbers;

    /**
     * SPU逗号分隔
     */
    private String spuStr;

    /**
     * 标题模糊
     */
    private String titleLike;

    /**
     * 平台类目Id集合
     */
    private List<Integer> categoryIds;

    // 创建时间范围
    private Timestamp fromCreatedTime;
    private Timestamp toCreatedTime;

    // 修改时间范围
    private Timestamp fromUpdatedTime;
    private Timestamp toUpdatedTime;

    /**
     * 刊登状态集合
     */
    private List<Integer> publishStatusList;

    /**
     * 库存上传状态
     */
    private List<Integer> inventoryUploads;

    private List<String> createByList;

    public OzonTemplateModelExample getExample() {
        OzonTemplateModelExample example = new OzonTemplateModelExample();
        OzonTemplateModelExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getIdStr())) {
            criteria.andIdIn(CommonUtils.splitIntList(this.getIdStr(), ","));
        }
        if (StringUtils.isNotBlank(this.getAccountNumber())) {
            criteria.andAccountNumberEqualTo(this.getAccountNumber());
        }
        if(CollectionUtils.isNotEmpty(this.getAccountNumbers())) {
            criteria.andAccountNumberIn(this.getAccountNumbers());
        }
        if (StringUtils.isNotBlank(this.getArticleNumber())) {
            criteria.andArticleNumberEqualTo(this.getArticleNumber());
        }
        if (StringUtils.isNotBlank(this.getSpuStr())) {
            criteria.andArticleNumberIn(CommonUtils.splitList(this.getSpuStr(), ","));
        }
        if (this.getSkuDataSource() != null) {
            criteria.andSkuDataSourceEqualTo(this.getSkuDataSource());
        }
        if (this.getCategoryId() != null) {
            criteria.andCategoryIdEqualTo(this.getCategoryId());
        }
        if (CollectionUtils.isNotEmpty(this.getCategoryIds())) {
            criteria.andCategoryIdIn(this.getCategoryIds());
        }
        if (StringUtils.isNotBlank(this.getCategoryIdPath())) {
            criteria.andCategoryIdPathEqualTo(this.getCategoryIdPath());
        }
        if (StringUtils.isNotBlank(this.getCategoryNamePath())) {
            criteria.andCategoryNamePathEqualTo(this.getCategoryNamePath());
        }
        if (StringUtils.isNotBlank(this.getMainImg())) {
            criteria.andMainImgEqualTo(this.getMainImg());
        }
        if (StringUtils.isNotBlank(this.getTitle())) {
            criteria.andTitleEqualTo(this.getTitle());
        }
        if (StringUtils.isNotBlank(this.getTitleLike())) {
            criteria.andTitleLike("%" + this.getTitleLike().trim() + "%" );
        }
        if (StringUtils.isNotBlank(this.getDescription())) {
            criteria.andDescriptionEqualTo(this.getDescription());
        }
        if (StringUtils.isNotBlank(this.getVat())) {
            criteria.andVatEqualTo(this.getVat());
        }
        if (StringUtils.isNotBlank(this.getVideo())) {
            criteria.andVideoEqualTo(this.getVideo());
        }
        if (this.getSaleVariant() != null) {
            criteria.andSaleVariantEqualTo(this.getSaleVariant());
        }
        if (this.getNeedTranslate() != null) {
            criteria.andNeedTranslateEqualTo(this.getNeedTranslate());
        }
        if (StringUtils.isNotBlank(this.getCurrencyCode())) {
            criteria.andCurrencyCodeEqualTo(this.getCurrencyCode());
        }
        if (StringUtils.isNotBlank(this.getCategoryAttribute())) {
            criteria.andCategoryAttributeEqualTo(this.getCategoryAttribute());
        }
        if (StringUtils.isNotBlank(this.getMergeAttribute())) {
            criteria.andMergeAttributeEqualTo(this.getMergeAttribute());
        }
        if (StringUtils.isNotBlank(this.getVariantData())) {
            criteria.andVariantDataEqualTo(this.getVariantData());
        }
        if (this.getPublishType() != null) {
            criteria.andPublishTypeEqualTo(this.getPublishType());
        }
        if (this.getPublishRole() != null) {
            criteria.andPublishRoleEqualTo(this.getPublishRole());
        }
        if (this.getPublishStatus() != null) {
            criteria.andPublishStatusEqualTo(this.getPublishStatus());
        }
        if (CollectionUtils.isNotEmpty(this.getPublishStatusList())) {
            criteria.andPublishStatusIn(this.getPublishStatusList());
        }
        if (this.getInventoryUpload() != null) {
            criteria.andInventoryUploadEqualTo(this.getInventoryUpload());
        }
        if (CollectionUtils.isNotEmpty(this.getInventoryUploads())) {
            criteria.andInventoryUploadIn(this.getInventoryUploads());
        }
        if (StringUtils.isNotBlank(this.getCreatedBy())) {
            criteria.andCreatedByEqualTo(this.getCreatedBy());
        }
        if (CollectionUtils.isNotEmpty(this.getCreateByList())) {
            criteria.andCreatedByIn(this.getCreateByList());
        }
        if (StringUtils.isNotBlank(this.getUpdatedBy())) {
            criteria.andUpdatedByEqualTo(this.getUpdatedBy());
        }
        if (this.getCreatedTime() != null) {
            criteria.andCreatedTimeEqualTo(this.getCreatedTime());
        }
        if (this.getFromCreatedTime() != null) {
            criteria.andCreatedTimeGreaterThanOrEqualTo(this.getFromCreatedTime());
        }
        if (this.getToCreatedTime() != null) {
            criteria.andCreatedTimeLessThanOrEqualTo(this.getToCreatedTime());
        }
        if (this.getUpdatedTime() != null) {
            criteria.andUpdatedTimeEqualTo(this.getUpdatedTime());
        }
        if (this.getFromUpdatedTime() != null) {
            criteria.andUpdatedTimeGreaterThanOrEqualTo(this.getFromUpdatedTime());
        }
        if (this.getToUpdatedTime() != null) {
            criteria.andUpdatedTimeLessThan(this.getToUpdatedTime());
        }
        if (StringUtils.isNotBlank(this.getProblemType())) {
            criteria.andProblemTypeEqualTo(this.getProblemType());
        }
        return example;
    }
}