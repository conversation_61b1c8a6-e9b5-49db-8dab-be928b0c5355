package com.estone.erp.publish.ozon.handler.template;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.redis.config.RedisClusterTemplate;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.PublishRedissonUtils;
import com.estone.erp.publish.base.pms.service.InfringementWordService;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItem;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsOzonItemRequest;
import com.estone.erp.publish.elasticsearch4.service.EsOzonItemService;
import com.estone.erp.publish.ozon.common.OzonConstant;
import com.estone.erp.publish.ozon.common.OzonErrorConstant;
import com.estone.erp.publish.ozon.enums.OzonEnums;
import com.estone.erp.publish.ozon.model.OzonAccountConfig;
import com.estone.erp.publish.ozon.model.OzonTemplateModel;
import com.estone.erp.publish.ozon.model.dto.BuilderTemplateDO;
import com.estone.erp.publish.ozon.model.dto.template.OzonSkuDO;
import com.estone.erp.publish.ozon.model.dto.template.OzonTemplateDO;
import com.estone.erp.publish.ozon.service.OzonAccountConfigService;
import com.estone.erp.publish.ozon.service.OzonTemplateModelService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.infringement.InfringementUtils;
import com.estone.erp.publish.system.infringement.response.InfringmentResponse;
import com.estone.erp.publish.system.infringement.vo.SearchVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-07-11 14:34
 */
@Slf4j
@Component
public class OzonTemplateValidation {
    @Autowired
    private OzonTemplateModelService ozonTemplateModelService;
    @Autowired
    private EsOzonItemService esItemService;
    @Autowired
    private InfringementWordService infringementWordService;
    @Autowired
    private OzonTemplateBuilderHandler ozonTemplateBuilderHandler;
    @Autowired
    private OzonAccountConfigService ozonAccountConfigService;
    @Autowired
    private RedisClusterTemplate redisClusterTemplate;

    private static String LOCK_KEY = RedisConstant.OZON_PREFIX_SYSTEM + "checkPublish:lock";

    /**
     * 校验是否存侵权词
     * @param templateData 模板
     */
    public void validateHasInfringementWord(OzonTemplateDO templateData) {
        String description = templateData.getDescription();
        String title = templateData.getTitle();
        if (StringUtils.isBlank(title)) {
            return;
        }
        SearchVo searchVo = new SearchVo();
        searchVo.setPlatform(Platform.Ozon.name());
        searchVo.setText(title + StrConstant.CHECK_INFRING_WORD_SPLIT + Optional.ofNullable(description).orElse(""));
        ApiResult<InfringmentResponse> apiResult = InfringementUtils.checkInfringWordAndBrand(searchVo);
        if (!apiResult.isSuccess()) {
            throw new BusinessException(String.format(" 调用侵权词服务异常：%s", apiResult.getErrorMsg()));
        }

        Set<String> words = new HashSet<>();
        InfringmentResponse result = apiResult.getResult();
        Map<String, Integer> brandMap = result.getBrandMap();
        if (MapUtils.isNotEmpty(brandMap)) {
            words.addAll(brandMap.keySet());
        }
        Map<String, Integer> infringementMap = result.getInfringementMap();
        if (MapUtils.isNotEmpty(infringementMap)) {
            words.addAll(infringementMap.keySet());
        }
        if (CollectionUtils.isEmpty(words)) {
            return;
        }
        throw new BusinessException(String.format(" 标题/描述,存在侵权词: %s", StringUtils.join(words,",")));
    }


    /**
     * 重复刊登校验
     * 刊登中、刊登成功模板/在线listing
     */
    public void validateRepeatPublish(OzonTemplateModel templateModel) {
        String variantData = templateModel.getVariantData();
        List<OzonSkuDO> ozonSkuDOS = JSON.parseArray(variantData, OzonSkuDO.class);
        if (CollectionUtils.isEmpty(ozonSkuDOS)) {
            throw new BusinessException("子变体不能为空");
        }
        List<String> articleNumbers = ozonSkuDOS.stream().map(OzonSkuDO::getSku).collect(Collectors.toList());
        articleNumbers.add(templateModel.getArticleNumber());
        checkLock(templateModel.getAccountNumber(), templateModel.getArticleNumber());
        validateRepeatPublish(templateModel.getAccountNumber(), articleNumbers, templateModel.getId());
    }

    /**
     * 检查是否重复刊登了
     * @param accountNumber 店铺
     * @param spu spu
     */
    public void checkLock(String accountNumber, String spu) {
        // spu 分布式锁
        String key = LOCK_KEY + "::" + accountNumber + "::" + spu;
        boolean b = PublishRedissonUtils.tryLock(key, TimeUnit.MINUTES, 0, 3);
        if (!b) {
            throw new RuntimeException("重复刊登");
        }
    }
    public void deleteLock(String accountNumber, String spu) {
        // spu 分布式锁
        String key = LOCK_KEY + "::" + accountNumber + "::" + spu;
        PublishRedisClusterUtils.del(key);
    }
    /**
     * 重复刊登校验
     * 当天刊登中、刊登成功模板/在线listing
     * @param accountNumber    店铺
     * @param articleNumbers   货号
     * @param currTemplateId   当前模板Id
     */
    public void validateRepeatPublish(String accountNumber, List<String> articleNumbers,Integer currTemplateId) {

        // 模板
        List<OzonTemplateModel> ozonTemplateModels = ozonTemplateModelService.listExistPublishedTemplate(accountNumber, articleNumbers);
        if (CollectionUtils.isNotEmpty(ozonTemplateModels)) {
            if (currTemplateId != null) {
                ozonTemplateModels.removeIf(templateModel -> templateModel.getId().equals(currTemplateId));
            }
            if (CollectionUtils.isNotEmpty(ozonTemplateModels)) {
                List<String> publishedSpu = ozonTemplateModels.stream().map(OzonTemplateModel::getArticleNumber).distinct().collect(Collectors.toList());
                throw new BusinessException(String.format("[%s]," + OzonErrorConstant.REPEAT_PUBLISH_TEMPLATE, StringUtils.join(publishedSpu,",")));
            }
        }
        // Listing 在售产品
        EsOzonItemRequest request = new EsOzonItemRequest();
        request.setPageIndex(0);
        request.setPageSize(1000);
        request.setFields(OzonConstant.DUPLICATE_FILES);
        request.setAccountNumber(accountNumber);
        request.setStatusCodes(List.of(OzonEnums.ListVisibility.IN_SALE.getCode()));
        request.setSpus(articleNumbers);
        request.setSkus(articleNumbers);
        List<EsOzonItem> items = esItemService.listItemByRequest(request);
        if (CollectionUtils.isNotEmpty(items)) {
            List<String> publishedSku = items.stream().map(EsOzonItem::getSku).distinct().collect(Collectors.toList());
            throw new BusinessException(String.format("[%s]," + OzonErrorConstant.REPEAT_PUBLISH_LISTING, StringUtils.join(publishedSku,",")));
        }
    }

    /**
     * 过滤侵权词
     * @param templateModel 模板数据
     */
    public void filterInfringementWord(OzonTemplateModel templateModel) {
        filterInfringementWord(templateModel.getTitle(), templateModel.getDescription(), templateModel::setTitle, templateModel::setDescription);
    }

    public void filterInfringementWord(String title, String description, Consumer<String> titleConsumer, Consumer<String> descriptionConsumer) {
        if (StringUtils.isBlank(title) && StringUtils.isBlank(description)) {
            return;
        }
        title = Optional.ofNullable(title).orElse("");
        SearchVo searchVo = new SearchVo();
        searchVo.setPlatform(Platform.Ozon.name());
        searchVo.setText(title + StrConstant.CHECK_INFRING_WORD_SPLIT + Optional.ofNullable(description).orElse(""));
        ApiResult<InfringmentResponse> apiResult = InfringementUtils.checkInfringWordAndBrand(searchVo);
        if (!apiResult.isSuccess()) {
            throw new BusinessException(String.format(" 调用侵权词服务异常：%s", apiResult.getErrorMsg()));
        }

        String newTitle = title;
        String newDescription = description;
        InfringmentResponse result = apiResult.getResult();
        Map<String, Integer> brandMap = result.getBrandMap();
        if (MapUtils.isNotEmpty(brandMap)) {
            newTitle = infringementWordService.delInfringementWord(newTitle, brandMap);
            newDescription = infringementWordService.htmlDelInfringementWord(newDescription, brandMap);
        }
        Map<String, Integer> infringementMap = result.getInfringementMap();
        if (MapUtils.isNotEmpty(infringementMap)) {
            newTitle = infringementWordService.delInfringementWord(newTitle, infringementMap);
            newDescription = infringementWordService.htmlDelInfringementWord(newDescription, infringementMap);
        }

        String replaced = newTitle.replaceAll(" {2}", "");
        if (Objects.nonNull(titleConsumer)) {
            titleConsumer.accept(replaced);
        }
        if (Objects.nonNull(descriptionConsumer)) {
            descriptionConsumer.accept(newDescription);
        }
    }

    public void filterForbiddenItemStatus(OzonTemplateModel ozonTemplateModel) {
        String variantData = ozonTemplateModel.getVariantData();
        List<OzonSkuDO> ozonSkuDOS = JSON.parseArray(variantData, OzonSkuDO.class);
        if (CollectionUtils.isEmpty(ozonSkuDOS)) {
            throw new BusinessException("子sku变体信息为空");
        }
        BuilderTemplateDO build = BuilderTemplateDO.builder().articleNumber(ozonTemplateModel.getArticleNumber()).skuDataSource(ozonTemplateModel.getSkuDataSource()).build();
        // 可刊登sku
        List<String> canPublishSkus = ozonTemplateBuilderHandler.getCanPublishSku(build);
        ozonSkuDOS.removeIf(ozonSkuDO -> !canPublishSkus.contains(ozonSkuDO.getSku()));
        if (CollectionUtils.isEmpty(ozonSkuDOS)) {
            throw new BusinessException("过滤停产、存档、废弃、Ozon禁售后无可刊登sku");
        }
        ozonTemplateModel.setVariantData(JSON.toJSONString(ozonSkuDOS));
        ozonTemplateModel.setSkuSize(ozonSkuDOS.size());
    }

    /**
     * 验证每日刊登限制
     * 当天刊登成功+当天刊登中模版的SKU数量<=每日刊登额度，并且小于（店铺限额数-所有商品数）
     * 当天指的是 今天八点到明天八点
     * @param ozonTemplateModel 模板数据
     */
    public void validateDailyPublishLimit(OzonTemplateModel ozonTemplateModel) {
        String accountNumber = ozonTemplateModel.getAccountNumber();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime todayEight = LocalDateTime.of(now.toLocalDate(), LocalTime.of(8, 0));
        // 当前时间小于8点的设置为昨天
        if (now.isBefore(todayEight)) {
            now = now.minusDays(1);
        }
        String day = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        // 如果存在当天已用完刊登额度，则直接失败
        Boolean flag = redisClusterTemplate.existsKey(RedisConstant.DAILY_CREATE_LIMIT_EXCEEDED + accountNumber + ":" + day);
        if (flag) {
            throw new BusinessException("今日刊登已超过刊登额度，请明天再重试");
        }

        Integer skuSize = ozonTemplateModel.getSkuSize();

        // 获取店铺配置
        OzonAccountConfig accountConfig = ozonAccountConfigService.selectByAccountNumber(accountNumber);
        if (accountConfig == null) {
            throw new BusinessException("店铺配置不存在");
        }

        Integer accountLimitNumber = accountConfig.getAccountLimitNumber();
        Integer allProductNumber = accountConfig.getAllProductNumber();
        Integer dailyCreateLimit = accountConfig.getDailyCreateLimit();

        // 如果未设置限制，则不进行验证
        if (accountLimitNumber == null || allProductNumber == null || dailyCreateLimit == null) {
            return;
        }
        Integer id = ozonTemplateModel.getId();
        // 获取当天已刊登成功和刊登中的SKU数量
        Integer todayPublishedSkus = ozonTemplateModelService.countTodayPublishedSkus(accountNumber, id);

        // 计算刊登后的总数
        int totalSkus = todayPublishedSkus + skuSize;

        // 验证是否超过每日刊登额度
        if (totalSkus > dailyCreateLimit) {
            throw new BusinessException("今日刊登已超过刊登额度，请明天再重试");
        }

        // 验证是否超过店铺限额
        int remainingQuota = accountLimitNumber - allProductNumber;
        if (skuSize > remainingQuota) {
            throw new BusinessException("今日刊登数已大于等于店铺限额数-所有商品数，请明天再重试");
        }
    }
}
