package com.estone.erp.publish.ozon.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.ozon.model.dto.solution.ErrorSolutionResult;
import com.estone.erp.publish.ozon.model.dto.solution.OzonGetSolutionDto;
import com.estone.erp.publish.ozon.model.dto.solution.OzonReportProblemMaintainQueryDto;
import com.estone.erp.publish.ozon.service.OzonErrorSolutionService;
import com.estone.erp.publish.ozon.service.OzonReportProblemMaintainService;
import com.estone.erp.publish.tidb.publishtidb.model.OzonReportProblemMaintain;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.tidb.publishtidb.controller
 * @Author: sj
 * @CreateTime: 2025-03-04  14:39
 * @Description: Ozon /处理报告问题维护前端控制器
 */

@Slf4j
@RestController
@RequestMapping("/ozonReportProblemMaintain")
public class OzonReportProblemMaintainController {

    @Resource
    private OzonReportProblemMaintainService ozonReportProblemMaintainService;
    @Resource
    private OzonErrorSolutionService ozonErrorSolutionService;

    /**
     * 分页查询
     */
    @PostMapping("/pageQuery")
    public ApiResult<IPage<OzonReportProblemMaintain>> pageQuery(@RequestBody OzonReportProblemMaintainQueryDto dto) {
        IPage<OzonReportProblemMaintain> page = null;
        try {
            page = ozonReportProblemMaintainService.pageQuery(dto);
        } catch (Exception e) {
            log.error("处理报告问题维护分页查询失败", e);
            throw new BusinessException("分页查询失败" + e.getMessage());
        }
        return ApiResult.newSuccess(page);
    }

    /**
     * 添加或修改
     */
    @PostMapping("/saveOrUpdate")
    public ApiResult<String> saveOrUpdate(@RequestBody @Valid OzonReportProblemMaintain entity){
        String result = null;
        try {
            result = ozonReportProblemMaintainService.saveOrUpdateByEntity(entity);
        } catch (Exception e) {
            log.error("处理报告问题维护添加或修改失败", e);
            throw new BusinessException("添加或修改失败" + e.getMessage());
        }
        return ApiResult.newSuccess(result);
    }

    /**
     * 根据id删除
     */
    @PostMapping("/delete")
    public ApiResult<String> delete(@RequestBody  List<Integer> idList){
        if (CollectionUtils.isEmpty(idList)){
            return ApiResult.newError("删除失败，请选择数据！");
        }
        ozonReportProblemMaintainService.removeByIds(idList);
        return ApiResult.newSuccess("删除成功！");
    }

    /**
     * 获取分类下拉
     */
    @PostMapping("/getAllSolutionType")
    public ApiResult<List<String>> getAllSolutionType(){
        List<String> list = null;
        try {
            list = ozonReportProblemMaintainService.getAllSolutionType();
        } catch (Exception e) {
            log.error("处理报告问题维护获取分类下拉失败", e);
            throw new BusinessException("获取分类下拉失败" + e.getMessage());
        }
        return ApiResult.newSuccess(list);
    }

    @PostMapping("/export")
    public ApiResult<String> export(@RequestBody OzonReportProblemMaintainQueryDto dto) {
        return ozonReportProblemMaintainService.export(dto);
    }

    /**
     * 获取解决方案
     * @param dto 对象
     * @return 结果
     */
    @PostMapping("/getSoleSolution")
    public ErrorSolutionResult getSoleSolution(@RequestBody OzonGetSolutionDto dto) {
        return ozonErrorSolutionService.findSolutionForError(dto.getErrorMsg(), dto.getTaskType());
    }

    @PostMapping("/deleteCache")
    public ApiResult<?> deleteCache() {
        return ozonErrorSolutionService.deleteCache();
    }
}
