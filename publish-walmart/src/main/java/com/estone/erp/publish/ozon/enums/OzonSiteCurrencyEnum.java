package com.estone.erp.publish.ozon.enums;

import com.estone.erp.publish.ozon.common.OzonErrorConstant;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/4/1 11:04
 */
public enum OzonSiteCurrencyEnum {

    US("US", "USD"),
    RU("RU", "RUB"),
    CN("CN", "CNY"),
    ;

    OzonSiteCurrencyEnum(String site, String currency) {
        this.site = site;
        this.currency = currency;
    }

    private final String site;
    private final String currency;

    public String getSite() {
        return site;
    }

    public String getCurrency() {
        return currency;
    }

    public static String getCurrencyBySite(String site) {
        OzonSiteCurrencyEnum[] values = values();
        for (OzonSiteCurrencyEnum value : values) {
            if (StringUtils.equalsIgnoreCase(value.site, site)) {
                return value.currency;
            }
        }
        throw new RuntimeException(OzonErrorConstant.FAILED_TO_OBTAIN_CURRENCY_BASED_ON_SITE);
    }

    public static String getSiteByCurrency(String currency) {
        OzonSiteCurrencyEnum[] values = values();
        for (OzonSiteCurrencyEnum value : values) {
            if (StringUtils.equalsIgnoreCase(value.currency, currency)) {
                return value.site;
            }
        }
        throw new RuntimeException(OzonErrorConstant.FAILED_TO_OBTAIN_SITE_BASED_ON_CURRENCY);
    }
}
