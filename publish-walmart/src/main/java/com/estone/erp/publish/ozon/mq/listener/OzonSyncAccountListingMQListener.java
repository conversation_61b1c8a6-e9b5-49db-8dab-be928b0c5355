package com.estone.erp.publish.ozon.mq.listener;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItem;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsOzonItemRequest;
import com.estone.erp.publish.elasticsearch4.service.EsOzonItemService;
import com.estone.erp.publish.ozon.common.OzonEsItemBulkProcessor;
import com.estone.erp.publish.ozon.common.OzonExecutors;
import com.estone.erp.publish.ozon.enums.OzonFeedTaskEnums;
import com.estone.erp.publish.ozon.handler.OzonSyncListingHandler;
import com.estone.erp.publish.ozon.model.dto.sync.OzonItemDO;
import com.estone.erp.publish.ozon.model.dto.sync.OzonSyncListingMessageDO;
import com.estone.erp.publish.ozon.mq.OzonMqConfig;
import com.estone.erp.publish.ozon.service.OzonFeedTaskService;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.google.common.collect.Lists;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Ozon同步listing
 *
 * <AUTHOR>
 * @date 2024-04-24 上午11:58
 */
@ConditionalOnProperty(
        name = {"mq-config.ozon-sync-account-listing-queue-enable"},
        havingValue = "true"
)
@Slf4j
@Component
public class OzonSyncAccountListingMQListener implements ChannelAwareMessageListener {

    @Autowired
    private OzonFeedTaskService ozonFeedTaskService;
    @Autowired
    private OzonSyncListingHandler syncListingHandler;
    @Autowired
    private EsOzonItemService esOzonItemService;
    @Autowired
    private OzonEsItemBulkProcessor itemBulkProcessor;
    @Autowired
    private RabbitMqSender rabbitMqSender;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
//        log.info("queue[{}]: {}", "OZON_SYNC_ACCOUNT_LISTING_QUEUE", body);
        try {
            OzonSyncListingMessageDO messageDO = JSON.parseObject(body, OzonSyncListingMessageDO.class);
            syncAccountListing(messageDO);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            log.error("OZON_SYNC_ACCOUNT_LISTING_QUEUE 消费异常, message: {}", body, e);
        }
    }

    private void syncAccountListing(OzonSyncListingMessageDO messageDO) {
        StopWatch started = StopWatch.createStarted();
        String accountNumber = messageDO.getAccountNumber();
        Long feedTaskId = messageDO.getFeedTaskId();
        String syncType = messageDO.getSyncType();
        FeedTask feedTask = new FeedTask();
        feedTask.setId(feedTaskId);
        feedTask.setTaskType(OzonFeedTaskEnums.TaskType.SYNC_ITEM.name());
        ozonFeedTaskService.updateTaskStatus(feedTask, FeedTaskStatusEnum.RUNNING.getTaskStatus());

        ApiResult<Map<Long, List<String>>> apiResult = syncListingHandler.loadAllSyncProductIds(accountNumber, false);
        if (!apiResult.isSuccess() || MapUtils.isEmpty(apiResult.getResult())) {
            if (MapUtils.isEmpty(apiResult.getResult())) {
                apiResult.setErrorMsg(apiResult.getErrorMsg());
            }
            ozonFeedTaskService.failTask(feedTask, "获取所有同步商品ID失败, 错误原因: " + apiResult.getErrorMsg());
            // 将没有同步到的数据设置为不在线
            setOtherProductOffline(new HashSet<>(), accountNumber);
            return;
        }
        Map<Long, List<String>> productStateMap = apiResult.getResult();
        List<Long> productIdList = new ArrayList<>(productStateMap.keySet());

        List<String> notSpuEsIdList = new ArrayList<>();
        List<Long> notAttributeList = new ArrayList<>();
        // 按是否新品分开同步
        Map<String, List<Long>> newStatesMap = partitionProductIdListGroupNewStates(productIdList, notSpuEsIdList,  accountNumber, notAttributeList);
        List<Long> newProducts = newStatesMap.get(Boolean.TRUE.toString());
        List<Long> existingProducts = newStatesMap.get(Boolean.FALSE.toString());
        log.info("开始同步商品详情, 店铺: {}, 数量: {}, 新品：{}, 耗时：{} ms", accountNumber, apiResult.getErrorMsg(), newProducts.size(), started.getTime());
        // 同步新品
        syncNewProduct(newProducts, productStateMap, accountNumber);

        if (OzonFeedTaskEnums.SyncItemType.ALL.name().equals(syncType)) {
            // 全量时更新已存在商品
            updateExistProduct(existingProducts, productStateMap, accountNumber);
        } else {
            // 增量的时候 ，将商品设置为在线，避免不在线商品没扭转成为在线
            // 增量同步的时候，要判断之前的spu信息是否全
            try {
                for (Long id : existingProducts) {
                    itemBulkProcessor.setOnline(id.toString(), true);
                }
            } catch (Exception e) {
                log.error("新品设置在线异常：" + e.getMessage());
            }

            try {
                for (String esId : notSpuEsIdList) {
                    rabbitMqSender.allPublishVHostRabbitTemplateSend(OzonMqConfig.OZON_API_DIRECT_EXCHANGE, OzonMqConfig.OZON_SYNC_ITEM_INFO_QUEUE_KEY, esId);
                }
            } catch (Exception e) {
                log.error("同步非spu商品失败：" + e.getMessage());
            }
            try {
                List<List<Long>> lists = PagingUtils.newPagingList(notAttributeList, 100);
                for (List<Long> list : lists) {
                    // 同步属性
                    syncListingHandler.syncAttributes(list, accountNumber);
                }
            } catch (Exception e) {
                log.error("同步非spu商品属性失败：" + e.getMessage());
            }
        }
        // 将没有同步到的数据设置为不在线
        setOtherProductOffline(productStateMap.keySet(), accountNumber);

        log.info("同步商品详情完成, 店铺: {}, 数量: {}, 耗时：{} ms", accountNumber, productIdList.size(), started.getTime(TimeUnit.MILLISECONDS));
        ozonFeedTaskService.succeedTask(feedTask, apiResult.getErrorMsg() + ",已同步新品:" + newProducts.size());
    }

    /**
     * 设置非同步商品为不在线
     * @param onlineProductIdSet 在线的产品id
     * @param accountNumber 店铺
     */
    private void setOtherProductOffline(Set<Long> onlineProductIdSet, String accountNumber) {
        try {
            // do 在线数据改为不在线
            EsOzonItemRequest request = new EsOzonItemRequest();
            request.setAccountNumber(accountNumber);
            request.setOnlineOrIsNot(true);
            request.setFields(new String[] {"id", "productId"});
            request.setOrderBy("id");
            request.setSequence("asc");
            esOzonItemService.scrollQueryExecutorTask(request, (itemList) -> {
                if (CollectionUtils.isEmpty(itemList)) {
                    return;
                }
                List<EsOzonItem> setOfflineItems = itemList.stream().filter(a -> !onlineProductIdSet.contains(a.getProductId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(setOfflineItems)) {
                    return;
                }
                for (EsOzonItem setOfflineItem : setOfflineItems) {
                    itemBulkProcessor.setOnline(setOfflineItem.getId(), false);
                }
            });


        } catch (Exception e) {
            log.error("店铺：{}， 设置非同步商品为不在线失败：{}", accountNumber, e.getMessage(), e);
        }
    }

    /**
     * 按是否新品分开同步
     */
    private Map<String, List<Long>> partitionProductIdListGroupNewStates(List<Long> productIdList, List<String> notSpuEsIdList, String accountNumber, List<Long> notAttributeList) {
        Map<String, List<Long>> newStatesMap = new HashMap<>();
        newStatesMap.put(Boolean.FALSE.toString(), new ArrayList<>());
        newStatesMap.put(Boolean.TRUE.toString(), new ArrayList<>());
        List<List<Long>> partition = Lists.partition(productIdList, 1000);
        for (List<Long> productIds : partition) {
            EsOzonItemRequest request = new EsOzonItemRequest();
            request.setAccountNumber(accountNumber);
            request.setProductIds(productIds);
            request.setFields(new String[]{"id", "productId", "spu", "weight"});
            List<EsOzonItem> esOzonItems = esOzonItemService.listItemByRequest(request);
            if (CollectionUtils.isEmpty(esOzonItems)) {
                List<Long> newProductIds = newStatesMap.get(Boolean.TRUE.toString());
                newProductIds.addAll(productIds);
                continue;
            }
            List<String> collect = esOzonItems.stream().filter(a -> StringUtils.isBlank(a.getSpu())).map(EsOzonItem::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                notSpuEsIdList.addAll(collect);
            }
            List<Long> collect1 = esOzonItems.stream().filter(a -> a.getWeight() == null).map(EsOzonItem::getProductId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                notAttributeList.addAll(collect1);
            }
            List<Long> esProductList = esOzonItems.stream().map(EsOzonItem::getProductId).collect(Collectors.toList());
            productIds.forEach(productId -> {
                List<Long> newProductIds;
                if (esProductList.contains(productId)) {
                    newProductIds = newStatesMap.get(Boolean.FALSE.toString());
                } else {
                    newProductIds = newStatesMap.get(Boolean.TRUE.toString());
                }
                newProductIds.add(productId);
            });
        }
        return newStatesMap;
    }

    /**
     * 同步商品详情
     * @param accountNumber
     * @param productIdList
     * @param productStateMap
     * @return
     */
    private int executeSyncProductInfo(String accountNumber, List<Long> productIdList, Map<Long, List<String>> productStateMap, Executor taskExecutor, Executor stockExecutor) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return 0;
        }
        // 分批同步详情
        AtomicInteger count = new AtomicInteger();
        List<List<Long>> partition = Lists.partition(productIdList, 500);
        List<CompletableFuture<List<OzonItemDO>>> completableFutures = partition.stream()
                .map(partitionProductIdList -> CompletableFuture.supplyAsync(() -> {
                    try {
                        // 获取商品详情
                        return syncListingHandler.batchSyncAndSaveItemInfo(accountNumber, partitionProductIdList, productStateMap);
                    } catch (Exception e) {
                        log.error("提交同步商品详细任务 exception:", e);
                        return null;
                    }
                }, taskExecutor))
                .collect(Collectors.toList());
        List<List<OzonItemDO>> itemList = completableFutures.stream().map(CompletableFuture::join).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemList)) {
            return 0;
        }
        itemList.forEach(items -> {
            count.addAndGet(items.size());
            // 同步仓库库存
            List<Long> skuIds = items.stream()
                    .map(OzonItemDO::getOzonSku)
                    .filter(Objects::nonNull)
                    .filter(skuId -> skuId > 0)
                    .collect(Collectors.toList());

            List<Long> productIds = items.stream().map(OzonItemDO::getProductId).collect(Collectors.toList());
            stockExecutor.execute(() -> {
                // 同步库存
                syncListingHandler.batchSyncWarehouseStockInfo(skuIds, accountNumber);
                // 同步属性
                syncListingHandler.syncAttributes(productIds, accountNumber);
            });
        });
        return count.get();
    }

    /**
     * 更新已存在商品
     */
    private void updateExistProduct(List<Long> productIdList, Map<Long, List<String>> productStateMap, String accountNumber) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return;
        }

        int limitSize = 3000;
        String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_OZON, "account_sync_limit_size", "limit_value", 10);
        if (StringUtils.isNotBlank(systemParamValue)) {
            limitSize = Integer.parseInt(systemParamValue);
        }
        // 大于limitSize的店铺异步同步
        if (productIdList.size() > limitSize) {
            OzonExecutors.SYNC_ACCOUNT_EXIST_LISTING_INFO.execute(() -> {
                syncBigAccountProduct(productIdList, productStateMap, accountNumber);
            });
            return;
        }
        // 小于limitSize的店铺阻塞同步
        syncSmallAccountProduct(productIdList, productStateMap, accountNumber);
    }

    /**
     * 同步新品
     */
    private void syncNewProduct(List<Long> productIdList, Map<Long, List<String>> productStateMap, String accountNumber) {
        StopWatch started = StopWatch.createStarted();
        int executeTotal = executeSyncProductInfo(accountNumber, productIdList, productStateMap, OzonExecutors.SYNC_ACCOUNT_NEW_LISTING_INFO, OzonExecutors.SYNC_ACCOUNT_NEW_LISTING_INFO);
        log.info("同步新品详情, 店铺: {}, 数量: {}, 已完成: {}, 耗时：{} ms", accountNumber, productIdList.size(), executeTotal, started.getTime(TimeUnit.MILLISECONDS));
    }

    /**
     * 同步小店铺商品详情
     */
    private void syncSmallAccountProduct(List<Long> productIdList, Map<Long, List<String>> productStateMap, String accountNumber) {
        StopWatch started = StopWatch.createStarted();
        int executedTotal = executeSyncProductInfo(accountNumber, productIdList, productStateMap, OzonExecutors.SYNC_ACCOUNT_SMALL_LISTING_INFO, OzonExecutors.SYNC_ITEM_STOCK_INFO);
        log.info("更新商品详情, 小店铺: {}, 数量: {}, 已完成: {}, 耗时：{} ms", accountNumber, productIdList.size(), executedTotal, started.getTime(TimeUnit.MILLISECONDS));
    }

    /**
     * 同步大店铺商品详情
     */
    private void syncBigAccountProduct(List<Long> productIdList, Map<Long, List<String>> productStateMap, String accountNumber) {
        StopWatch started = StopWatch.createStarted();
        int executedTotal = executeSyncProductInfo(accountNumber, productIdList, productStateMap, OzonExecutors.SYNC_ITEM_LISTING_INFO, OzonExecutors.SYNC_ITEM_STOCK_INFO);
        log.info("更新商品详情, 大店铺: {}, 数量: {}, 已完成: {}, 耗时：{} ms", accountNumber, productIdList.size(), executedTotal, started.getTime(TimeUnit.MILLISECONDS));
    }
}
