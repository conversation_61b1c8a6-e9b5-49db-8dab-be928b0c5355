package com.estone.erp.publish.ozon.excel.handler;

import com.alibaba.excel.util.BooleanUtils;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.ozon.enums.OzonFeedTaskEnums;
import com.estone.erp.publish.ozon.excel.handler.definition.TaskDefinition;
import com.estone.erp.publish.ozon.excel.model.UpdatePriceExcel;
import com.estone.erp.publish.ozon.handler.OzonUpdatePriceHandler;
import com.estone.erp.publish.ozon.handler.update.UpdateMinPriceAfterSyncHandler;
import com.estone.erp.publish.ozon.model.dto.OzonUpdatePriceInfoDo;
import com.estone.erp.publish.ozon.service.OzonFeedTaskService;
import com.estone.erp.publish.ozon.service.OzonProblemClassificationHandler;
import com.estone.erp.publish.platform.enums.SmallPlatformDownloadEnums;
import com.estone.erp.publish.platform.model.SmallPlatformExcelDownloadLog;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 修改价格处理器
 */
@Component
public class UpdatePriceExcelHandler extends AbstractListingExcelHandler<UpdatePriceExcel> implements DownloadHandler {
    public static final String[] UPDATE_PRICE_FILES = {"id", "accountNumber", "productId", "ozonSku", "fbsSku", "sku", "sellerSku", "priceNumber", "currencyCode",
            "oldPriceNumber", "minPriceNumber"};

    @Autowired
    private OzonUpdatePriceHandler ozonUpdatePriceHandler;

    @Autowired
    private OzonFeedTaskService ozonFeedTaskService;

    @Autowired
    private UpdateMinPriceAfterSyncHandler updateMinPriceAfterSyncHandler;

    @Autowired
    private OzonProblemClassificationHandler ozonProblemClassificationHandler;

    @Override
    public SmallPlatformDownloadEnums.Type getType() {
        return SmallPlatformDownloadEnums.Type.UPDATE_PRICE;
    }

    @Override
    public void writeFile(SmallPlatformExcelDownloadLog downloadLog, File tempFile) {
        InputStream ins = prepareExcelInputStream(downloadLog);
        List<UpdatePriceExcel> resultList = parseExcelFile(ins, UpdatePriceExcel.class);
        processExcelData(resultList, downloadLog.getCreateBy());
        writeResultToFile(resultList, tempFile, UpdatePriceExcel.class);
        updateDownloadLog(downloadLog, resultList);
    }

    @Override
    protected void validateSpecificColumns(Map<Integer, String> data) {
        // 验证价格列
        String priceColumn = data.get(4);
        if (!"价格*".equals(priceColumn)) {
            throw new RuntimeException("excel文件格式错误, 价格列顺序不对");
        }
    }

    @Override
    protected UpdatePriceExcel createExcelRowObject(Map<Integer, String> data, int rowIndex) {
        UpdatePriceExcel updatePriceExcel = new UpdatePriceExcel();
        updatePriceExcel.setAccountNumber(data.get(0));
        updatePriceExcel.setProductId(data.get(1));
        updatePriceExcel.setSellerSku(data.get(2));
        updatePriceExcel.setSku(data.get(3));
        updatePriceExcel.setAfterPriceStr(data.get(4));
        updatePriceExcel.setAfterMinPriceStr(data.get(5));
        updatePriceExcel.setAfterOldPriceStr(data.get(6));
        updatePriceExcel.setRowIndex(rowIndex);
        return updatePriceExcel;
    }

    @Override
    protected void processExcelData(List<UpdatePriceExcel> resultList, String username) {
        DataContextHolder.setUsername(username);

        // 必填校验
        List<UpdatePriceExcel> validItems = resultList.stream()
                .filter(a -> StringUtils.isBlank(a.getRemark()))
                .collect(Collectors.toList());

        // 权限校验
        validItems = checkAuth(validItems, username);

        // 数据重复性校验
        validItems = checkRepeat(validItems, (updatePriceExcel -> {
            String accountNumber = updatePriceExcel.getAccountNumber();
            String productId = updatePriceExcel.getProductId();
            return accountNumber + "_" + productId;
        }));

        // 是否存在在线列表
        validItems = checkListing(validItems, UPDATE_PRICE_FILES, (esOzonItem, excelItem) -> {
            excelItem.setCurrencyCode(esOzonItem.getCurrencyCode());
            excelItem.setBeforePrice(esOzonItem.getPriceNumber());
            excelItem.setBeforeMinPrice(esOzonItem.getMinPriceNumber());
            excelItem.setBeforeOldPrice(esOzonItem.getOldPriceNumber());
            excelItem.setSku(esOzonItem.getSku());
        });

        // 检查所有的价格是否相等
        validItems = checkEqualPrice(validItems);

        // 处理错误项
        processErrorItems(resultList, username);

        if (CollectionUtils.isEmpty(validItems)) {
            return;
        }

        // 更新价格
        updatePrices(validItems, resultList);
    }

    /**
     * 处理错误项
     *
     * @param resultList Excel数据列表
     * @param username   用户名
     */
    private void processErrorItems(List<UpdatePriceExcel> resultList, String username) {
        List<UpdatePriceExcel> errorList = resultList.stream()
                .filter(a -> StringUtils.isNotBlank(a.getResult()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(errorList)) {
            return;
        }

        List<FeedTask> feedTasks = new ArrayList<>();
        Timestamp timestamp = Timestamp.valueOf(LocalDateTime.now());

        for (UpdatePriceExcel updatePriceExcel : errorList) {
            List<FeedTask> list = getErrorFeedTask(updatePriceExcel, timestamp, username);
            if (CollectionUtils.isNotEmpty(list)) {
                feedTasks.addAll(list);
            }
        }

        ozonFeedTaskService.batchInsertSelective(feedTasks);
    }

    /**
     * 更新价格
     *
     * @param validItems 有效项列表
     * @param resultList 结果列表
     */
    private void updatePrices(List<UpdatePriceExcel> validItems, List<UpdatePriceExcel> resultList) {
        // 创建更新数据对象
        List<OzonUpdatePriceInfoDo> updateDo = createUpdateDo(validItems);

        // 按账号分组
        Map<String, List<OzonUpdatePriceInfoDo>> accountGroups = updateDo.stream()
                .collect(Collectors.groupingBy(OzonUpdatePriceInfoDo::getAccountNumber));

        // 对每个账号执行更新
        for (Map.Entry<String, List<OzonUpdatePriceInfoDo>> entry : accountGroups.entrySet()) {
            String accountNumber = entry.getKey();
            List<OzonUpdatePriceInfoDo> items = entry.getValue();
            ozonUpdatePriceHandler.updateAllPrice(accountNumber, items);
        }

        // 更新Excel结果
        updateExcelResults(validItems, updateDo);
    }

    /**
     * 更新Excel结果
     *
     * @param validItems 有效项列表
     * @param updateDo   更新数据对象列表
     */
    private void updateExcelResults(List<UpdatePriceExcel> validItems, List<OzonUpdatePriceInfoDo> updateDo) {
        Map<Integer, OzonUpdatePriceInfoDo> rowToUpdateMap = updateDo.stream()
                .collect(Collectors.toMap(OzonUpdatePriceInfoDo::getRowIndex, java.util.function.Function.identity()));

        for (UpdatePriceExcel excelItem : validItems) {
            Integer rowIndex = excelItem.getRowIndex();
            OzonUpdatePriceInfoDo updateItem = rowToUpdateMap.get(rowIndex);

            if (updateItem == null) {
                excelItem.setResult("失败");
                excelItem.setRemark("执行修改接口后数据丢失");
                continue;
            }

            Boolean success = updateItem.getSuccess();
            if (BooleanUtils.isTrue(success)) {
                excelItem.setResult("成功");
            } else {
                excelItem.setResult("失败");
                excelItem.setRemark(updateItem.getErrorMsg());
            }
        }
        List<UpdatePriceExcel> collect = validItems.stream().filter(a -> "成功".equalsIgnoreCase(a.getResult()))
                .filter(a -> a.getAfterMinPrice() != null)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            try {
                Map<String, List<UpdatePriceExcel>> collect1 = collect.stream().collect(Collectors.groupingBy(UpdatePriceExcel::getAccountNumber));
                for (Map.Entry<String, List<UpdatePriceExcel>> stringListEntry : collect1.entrySet()) {
                    String accountNumber = stringListEntry.getKey();
                    List<UpdatePriceExcel> value = stringListEntry.getValue();
                    Map<Long, Double> collect2 = value.stream()
                            .collect(Collectors.toMap(
                                    a -> Long.valueOf(a.getProductId()),
                                    UpdatePriceExcel::getAfterMinPrice,
                                    (price1, price2) -> price1));
                    updateMinPriceAfterSyncHandler.syncMinPrice(accountNumber, collect2);
                }
            } catch (Exception e) {
                // ignore
            }
        }

        // 处理下数据，最低价修改成功的处理
    }

    /**
     * 检查价格是否有变化
     *
     * @param items 待检查的Excel数据列表
     * @return 价格有变化的项目列表
     */
    private List<UpdatePriceExcel> checkEqualPrice(List<UpdatePriceExcel> items) {
        items.forEach(item -> {
            if (!hasPriceChanged(item)) {
                item.setResult("失败");
                item.setRemark("改前改后值相等，不进行修改");
            }
        });
        return filterValidItems(items);
    }

    /**
     * 判断价格是否有变化
     *
     * @param item Excel数据项
     * @return 是否有价格变化
     */
    private boolean hasPriceChanged(UpdatePriceExcel item) {
        return isPriceChanged(item.getBeforeMinPrice(), item.getAfterMinPrice()) ||
                isPriceChanged(item.getBeforeOldPrice(), item.getAfterOldPrice()) ||
                isPriceChanged(item.getBeforePrice(), item.getAfterPrice());
    }

    /**
     * 比较两个价格是否不同
     *
     * @param before 修改前价格
     * @param after  修改后价格
     * @return 价格是否不同
     */
    private boolean isPriceChanged(Double before, Double after) {
        if (before == null && after != null) {
            return true;
        }
        if (before != null && after != null) {
            return !Objects.equals(before, after);
        }
        return false;
    }

    /**
     * 获取错误任务列表
     *
     * @param excel     Excel数据项
     * @param timestamp 时间戳
     * @param username  用户名
     * @return 错误任务列表
     */
    private List<FeedTask> getErrorFeedTask(UpdatePriceExcel excel, Timestamp timestamp, String username) {
        List<FeedTask> tasks = new ArrayList<>();

        // 定义价格类型和对应的数据
        List<TaskDefinition> priceDefinitions = Arrays.asList(
                new TaskDefinition(
                        OzonFeedTaskEnums.TaskType.UPDATE_MIN_PRICE,
                        excel.getAfterMinPriceStr(),
                        excel.getBeforeMinPrice() != null ? excel.getBeforeMinPrice().toString() : null
                ),
                new TaskDefinition(
                        OzonFeedTaskEnums.TaskType.UPDATE_OLD_PRICE,
                        excel.getAfterOldPriceStr(),
                        excel.getBeforeOldPrice() != null ? excel.getBeforeOldPrice().toString() : null
                ),
                new TaskDefinition(
                        OzonFeedTaskEnums.TaskType.UPDATE_PRICE,
                        excel.getAfterPriceStr(),
                        excel.getBeforePrice() != null ? excel.getBeforePrice().toString() : null
                )
        );

        // 为每种价格类型创建任务
        for (TaskDefinition def : priceDefinitions) {
            if (def.getTaskType().equals(OzonFeedTaskEnums.TaskType.UPDATE_PRICE)) {
                FeedTask task = getErrorFeedTask(excel, timestamp, username, def);
                tasks.add(task);
            } else if (StringUtils.isNotBlank(def.getAfterValueStr())) {
                FeedTask task = getErrorFeedTask(excel, timestamp, username, def);
                tasks.add(task);
            }
        }
        return tasks;
    }

    private FeedTask getErrorFeedTask(UpdatePriceExcel updatePriceExcel, Timestamp timestamp, String username, TaskDefinition taskDefinition) {
        FeedTask feedTask = new FeedTask();
        feedTask.setAssociationId(updatePriceExcel.getProductId());
        feedTask.setAccountNumber(updatePriceExcel.getAccountNumber());
        feedTask.setArticleNumber(updatePriceExcel.getSku());
        feedTask.setTaskType(taskDefinition.getTaskType().name());
        feedTask.setPlatform(Platform.Ozon.name());
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
        feedTask.setResultMsg(updatePriceExcel.getRemark());
        feedTask.setCreatedBy(username);
        feedTask.setCreateTime(timestamp);
        feedTask.setFinishTime(timestamp);
        feedTask.setAttribute3(taskDefinition.getBeforeValueStr());
        feedTask.setAttribute4(taskDefinition.getAfterValueStr());
        feedTask.setTableIndex();
        feedTask.setAttribute1(updatePriceExcel.getSellerSku());
        feedTask.setAttribute10("Excel");

        if (StringUtils.isNotBlank(feedTask.getResultMsg())) {
            ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, feedTask.getResultMsg(), feedTask.getTaskType());
        }

        return feedTask;
    }

    /**
     * 创建价格更新数据对象列表
     *
     * @param excelItems Excel数据项列表
     * @return 价格更新数据对象列表
     */
    private List<OzonUpdatePriceInfoDo> createUpdateDo(List<UpdatePriceExcel> excelItems) {
        return excelItems.stream()
                .map(this::convertToUpdatePriceInfo)
                .collect(Collectors.toList());
    }

    /**
     * 将Excel数据项转换为价格更新数据对象
     *
     * @param item Excel数据项
     * @return 价格更新数据对象
     */
    private OzonUpdatePriceInfoDo convertToUpdatePriceInfo(UpdatePriceExcel item) {
        OzonUpdatePriceInfoDo updateDO = new OzonUpdatePriceInfoDo();

        // 设置基本信息
        updateDO.setProductId(Long.parseLong(item.getProductId()));
        updateDO.setAccountNumber(item.getAccountNumber());
        updateDO.setSku(item.getSku());
        updateDO.setSellerSku(item.getSellerSku());
        updateDO.setCurrencyCode(item.getCurrencyCode());

        // 设置价格相关信息
        updateDO.setBeforePrice(item.getBeforePrice());
        updateDO.setBeforeOldPrice(item.getBeforeOldPrice());
        updateDO.setBeforeMinPrice(item.getBeforeMinPrice());
        updateDO.setAfterPrice(item.getAfterPrice());
        updateDO.setAfterOldPrice(item.getAfterOldPrice());
        updateDO.setAfterMinPrice(item.getAfterMinPrice());

        // 设置其他信息
        updateDO.setRowIndex(item.getRowIndex());
        updateDO.setJob("Excel");

        return updateDO;
    }
}