package com.estone.erp.publish.ozon.excel.handler;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.elasticsearch4.model.EsOzonStockInfo;
import com.estone.erp.publish.ozon.common.OzonConstant;
import com.estone.erp.publish.ozon.excel.model.UpdateStockExcel;
import com.estone.erp.publish.ozon.handler.OzonUpdateHandler;
import com.estone.erp.publish.ozon.model.dto.OzonUpdateDO;
import com.estone.erp.publish.ozon.model.vo.OzonWareHouseInfoVO;
import com.estone.erp.publish.ozon.service.OzonAccountConfigService;
import com.estone.erp.publish.platform.enums.SmallPlatformDownloadEnums;
import com.estone.erp.publish.platform.model.SmallPlatformExcelDownloadLog;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 修改库存处理器
 */
@Component
public class UpdateStockExcelHandler extends AbstractListingExcelHandler<UpdateStockExcel> implements DownloadHandler {

    @Autowired
    private OzonAccountConfigService ozonAccountConfigService;

    @Autowired
    private OzonUpdateHandler ozonUpdateHandler;

    @Override
    public SmallPlatformDownloadEnums.Type getType() {
        return SmallPlatformDownloadEnums.Type.UPDATE_STOCK;
    }

    @Override
    public void writeFile(SmallPlatformExcelDownloadLog downloadLog, File tempFile) {
        InputStream ins = prepareExcelInputStream(downloadLog);
        List<UpdateStockExcel> resultList = parseExcelFile(ins, UpdateStockExcel.class);
        processExcelData(resultList, downloadLog.getCreateBy());
        writeResultToFile(resultList, tempFile, UpdateStockExcel.class);
        updateDownloadLog(downloadLog, resultList);
    }

    @Override
    protected void validateSpecificColumns(Map<Integer, String> data) {
        // 再判断下，仓库和库存这两列对不对得上
        String s1 = data.get(4);
        String s2 = data.get(5);
        if (!"仓库*".equals(s1) || !"库存*".equals(s2)) {
            throw new RuntimeException("excel文件格式错误, 仓库名称和库存列顺序不对");
        }
    }

    @Override
    protected UpdateStockExcel createExcelRowObject(Map<Integer, String> data, int rowIndex) {
        UpdateStockExcel updateStockExcel = new UpdateStockExcel();
        updateStockExcel.setAccountNumber(data.get(0));
        updateStockExcel.setProductId(data.get(1));
        updateStockExcel.setSellerSku(data.get(2));
        updateStockExcel.setSku(data.get(3));
        updateStockExcel.setWarehouseName(data.get(4));
        updateStockExcel.setAfterStock(data.get(5));
        updateStockExcel.setRowIndex(rowIndex);
        return updateStockExcel;
    }

    @Override
    protected void processExcelData(List<UpdateStockExcel> resultList, String username) {
        // 必填校验
        List<UpdateStockExcel> collect = resultList.stream()
                .filter(a -> StringUtils.isBlank(a.getRemark()))
                .collect(Collectors.toList());
        
        // 权限校验
        collect = checkAuth(collect, username);
        
        // 仓库存在性校验
        collect = checkWarehouseName(collect);
        
        // 数据重复性校验
        collect = checkRepeat(collect, (updateStockExcel -> {
            String accountNumber = updateStockExcel.getAccountNumber();
            String productId = updateStockExcel.getProductId();
            String warehouseName = updateStockExcel.getWarehouseName();
            return accountNumber + "_" + productId + "_" + warehouseName;
        }));
        
        // 是否存在在线列表
        collect = checkListing(collect, OzonConstant.UPDATE_STOCK_FILES, (esOzonItem, excelItem) -> {
            // 设置下改之前的仓库
            List<EsOzonStockInfo> esOzonStockInfos = esOzonItem.getWarehouseStockInfos();
            EsOzonStockInfo esOzonStockInfo = esOzonStockInfos.stream()
                    .filter(wareHouseInfo -> excelItem.getWarehouseId().equals(wareHouseInfo.getWarehouseId()))
                    .findFirst()
                    .orElse(null);
            if (esOzonStockInfo != null) {
                Long present = esOzonStockInfo.getPresent();
                excelItem.setBeforeStock(present);
            }
        });
        
        // 检查前后仓库是否相等
        collect = checkStock(collect);

        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        
        // 开始更新库存
        List<OzonUpdateDO> updateDo = createUpdateDo(collect);
        Map<String, List<OzonUpdateDO>> accountAndUpdateStockMap = updateDo.stream()
                .collect(Collectors.groupingBy(OzonUpdateDO::getAccountNumber));
        
        for (Map.Entry<String, List<OzonUpdateDO>> stringListEntry : accountAndUpdateStockMap.entrySet()) {
            String accountNumber = stringListEntry.getKey();
            List<OzonUpdateDO> value = stringListEntry.getValue();
            ozonUpdateHandler.syncUpdateStock(accountNumber, value, username);
        }
        
        // 处理完后，开始处理excel表格，查看情况
        Map<Integer, OzonUpdateDO> collect1 = updateDo.stream()
                .collect(Collectors.toMap(OzonUpdateDO::getRowIndex, Function.identity()));
        
        for (UpdateStockExcel updateStockExcel : collect) {
            Integer rowIndex = updateStockExcel.getRowIndex();
            OzonUpdateDO ozonUpdateDO = collect1.get(rowIndex);
            if (ozonUpdateDO == null) {
                updateStockExcel.setResult("失败");
                updateStockExcel.setRemark("执行修改库存接口后数据丢失");
                continue;
            }
            boolean success = ozonUpdateDO.isSuccess();
            if (success) {
                updateStockExcel.setResult("成功");
            } else {
                updateStockExcel.setResult("失败");
                updateStockExcel.setRemark("修改失败");
            }
        }
    }

    private List<UpdateStockExcel> checkStock(List<UpdateStockExcel> collect) {
        for (UpdateStockExcel updateStockExcel : collect) {
            Long beforeStock = updateStockExcel.getBeforeStock();
            String afterStock = updateStockExcel.getAfterStock();
            if (beforeStock == null || afterStock == null) {
                continue;
            }
            if (Long.parseLong(afterStock) == beforeStock) {
                updateStockExcel.setResult("失败");
                updateStockExcel.setRemark("改前改后仓库库存一致");
            }
        }
        return collect.stream()
                .filter(a -> StringUtils.isBlank(a.getResult()))
                .collect(Collectors.toList());
    }

    private List<OzonUpdateDO> createUpdateDo(List<UpdateStockExcel> collect) {
        List<OzonUpdateDO> list = new ArrayList<>();
        for (UpdateStockExcel item : collect) {
            OzonUpdateDO ozonUpdateDO = new OzonUpdateDO();
            ozonUpdateDO.setProductId(Long.parseLong(item.getProductId()));
            ozonUpdateDO.setAccountNumber(item.getAccountNumber());
            ozonUpdateDO.setSku(item.getSku());
            ozonUpdateDO.setSellerSku(item.getSellerSku());
            ozonUpdateDO.setUpdateBeforeStock(item.getBeforeStock() != null ? item.getBeforeStock().intValue() : null);
            ozonUpdateDO.setUpdateAfterStock(item.getAfterStock() != null ? Integer.parseInt(item.getAfterStock()) : null);
            ozonUpdateDO.setWarehouseId(item.getWarehouseId());
            ozonUpdateDO.setWarehouseName(item.getWarehouseName());
            ozonUpdateDO.setRowIndex(item.getRowIndex());
            ozonUpdateDO.setJob("Excel");
            list.add(ozonUpdateDO);
        }
        return list;
    }

    private List<UpdateStockExcel> checkWarehouseName(List<UpdateStockExcel> collect) {
        if (CollectionUtils.isEmpty(collect)) {
            return collect;
        }
        
        List<String> accountNumberList = collect.stream()
                .map(UpdateStockExcel::getAccountNumber)
                .collect(Collectors.toList());
        
        ApiResult<Map<String, List<OzonWareHouseInfoVO>>> accountWareHouseInfo = 
                ozonAccountConfigService.getAccountWareHouseInfo(accountNumberList);
        
        if (!accountWareHouseInfo.isSuccess()) {
            String errorMsg = accountWareHouseInfo.getErrorMsg();
            for (UpdateStockExcel updateStockExcel : collect) {
                updateStockExcel.setResult("失败");
                updateStockExcel.setRemark(errorMsg);
            }
            return collect.stream()
                    .filter(a -> StringUtils.isBlank(a.getResult()))
                    .collect(Collectors.toList());
        }
        
        Map<String, List<OzonWareHouseInfoVO>> warehouseMapTemp = accountWareHouseInfo.getResult();
        Map<String, Map<String, String>> accountAndNameIdMap = warehouseMapTemp.entrySet().stream()
                .filter(a -> a.getValue() != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        a -> a.getValue().stream().collect(
                                Collectors.toMap(
                                        OzonWareHouseInfoVO::getName,
                                        OzonWareHouseInfoVO::getWarehouseId,
                                        (oldV, newV) -> newV
                                )
                        )
                ));
        
        Map<String, List<UpdateStockExcel>> accountAndStockMap = collect.stream()
                .collect(Collectors.groupingBy(UpdateStockExcel::getAccountNumber));
        
        for (Map.Entry<String, List<UpdateStockExcel>> entry : accountAndStockMap.entrySet()) {
            String accountNumber = entry.getKey();
            List<UpdateStockExcel> value = entry.getValue();
            
            if (!accountAndNameIdMap.containsKey(accountNumber)) {
                for (UpdateStockExcel updateStockExcel : value) {
                    updateStockExcel.setResult("失败");
                    updateStockExcel.setRemark("该店铺查询不到查库存");
                }
                continue;
            }

            Map<String, String> existWarehouseNameMap = accountAndNameIdMap.get(accountNumber);
            for (UpdateStockExcel updateStockExcel : value) {
                String warehouseName = updateStockExcel.getWarehouseName();
                if (!existWarehouseNameMap.containsKey(warehouseName)) {
                    updateStockExcel.setResult("失败");
                    updateStockExcel.setRemark(warehouseName + " 仓库不存在");
                    continue;
                }
                updateStockExcel.setWarehouseId(Long.valueOf(existWarehouseNameMap.get(warehouseName)));
            }
        }
        
        return collect.stream()
                .filter(a -> StringUtils.isBlank(a.getResult()))
                .collect(Collectors.toList());
    }
}