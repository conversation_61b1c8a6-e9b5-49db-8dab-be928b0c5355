package com.estone.erp.publish.ozon.job.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.ozon.enums.OzonFeedTaskEnums;
import com.estone.erp.publish.ozon.excel.ImportExcelFileHandler;
import com.estone.erp.publish.ozon.excel.dto.ImportFileDto;
import com.estone.erp.publish.ozon.excel.model.UpdateTemplateTitleAndDescExcel;
import com.estone.erp.publish.ozon.excel.util.OzonExcelUtils;
import com.estone.erp.publish.ozon.service.OzonFeedTaskService;
import com.estone.erp.publish.platform.enums.SmallPlatformDownloadEnums;
import com.estone.erp.publish.platform.model.SmallPlatformExcelDownloadLog;
import com.estone.erp.publish.platform.model.SmallPlatformExcelDownloadLogExample;
import com.estone.erp.publish.platform.service.SmallPlatformExcelDownloadLogService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * excel 表格结果获取
 *
 * <AUTHOR>
 */
@Component
public class OzonUpdateTemplateTitleAndDescExcelResultJobHandler extends AbstractJobHandler {

    @Resource
    private SmallPlatformExcelDownloadLogService smallPlatformExcelDownloadLogService;

    @Resource
    private OzonFeedTaskService ozonFeedTaskService;

    public OzonUpdateTemplateTitleAndDescExcelResultJobHandler() {
        super("OzonUpdateTemplateTitleAndDescExcelResultJobHandler");
    }

    @Data
    public static class InnerParams {
        private List<Integer> idList;
        private Integer timeoutDay = 2;
    }

    @XxlJob("OzonUpdateTemplateTitleAndDescExcelResultJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        InnerParams innerParams = passParam(param, InnerParams.class);
        if (innerParams == null) {
            innerParams = new InnerParams();
        }

        int limit = 100;
        int offset = 0;
        while (true) {
            SmallPlatformExcelDownloadLogExample excelDownloadLogExample = new SmallPlatformExcelDownloadLogExample();
            SmallPlatformExcelDownloadLogExample.Criteria criteria = excelDownloadLogExample.createCriteria();
            if (CollectionUtils.isNotEmpty(innerParams.getIdList())) {
                criteria.andIdIn(innerParams.getIdList());
            }
            criteria.andTypeEqualTo(SmallPlatformDownloadEnums.Type.UPDATE_TEMPLATE_TITLE_AND_DESC.name());
            criteria.andStatusEqualTo(SmallPlatformDownloadEnums.Status.EXECUTING.getCode());
            criteria.andMessageEqualTo("1");
            criteria.andPlatformEqualTo(Platform.Ozon.name());
            excelDownloadLogExample.setOrderByClause("id asc");
            excelDownloadLogExample.setLimit(limit);
            excelDownloadLogExample.setOffset(offset);
            List<SmallPlatformExcelDownloadLog> smallPlatformExcelDownloadLogs = smallPlatformExcelDownloadLogService.selectByExample(excelDownloadLogExample);
            if (CollectionUtils.isEmpty(smallPlatformExcelDownloadLogs)) {
                break;
            }
            offset += limit;
            for (SmallPlatformExcelDownloadLog downloadLog : smallPlatformExcelDownloadLogs) {
                try {
                    XxlJobLogger.log("id[{}] 开始解析结果", downloadLog.getId());
                    String queryCondition = downloadLog.getQueryCondition();
                    String username = downloadLog.getCreateBy();
                    DataContextHolder.setUsername(username);
                    ImportFileDto importFileDto = JSON.parseObject(queryCondition, ImportFileDto.class);
                    String url = importFileDto.getUrl();
                    if (StringUtils.isBlank(url)) {
                        throw new IllegalArgumentException("excel文件不能为空");
                    }

                    InputStream ins = null;
                    try {
                        ins = ImportExcelFileHandler.downloadFileFromURL(url);
                    } catch (IOException e) {
                        throw new RuntimeException("excel文件下载失败：" + e.getMessage());
                    }
                    doService(downloadLog, ins, innerParams.getTimeoutDay());
                } catch (Exception e) {
                    XxlJobLogger.log("id[{}] 解析结果异常", downloadLog.getId(), e);
                    error(downloadLog, e);
                }
            }
        }

        return ReturnT.SUCCESS;
    }

    private void doService(SmallPlatformExcelDownloadLog downloadLog, InputStream ins, Integer timeoutDay) {
        List<UpdateTemplateTitleAndDescExcel> resultList = new ArrayList<>();
        Integer id = downloadLog.getId();
        EasyExcel.read(ins, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                UpdateTemplateTitleAndDescExcel updateTemplateTitleAndDescExcel = new UpdateTemplateTitleAndDescExcel();
                updateTemplateTitleAndDescExcel.setTemplateIdStr(data.get(0));
                updateTemplateTitleAndDescExcel.setTitle(data.get(1));
                updateTemplateTitleAndDescExcel.setDesc(data.get(2));
                updateTemplateTitleAndDescExcel.setRowIndex(context.readRowHolder().getRowIndex());
                updateTemplateTitleAndDescExcel.setExcelId(id);
                resultList.add(updateTemplateTitleAndDescExcel);
            }
            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                XxlJobLogger.log("解析结果完成，共{}条数据", resultList.size());
            }
        }).headRowNumber(1).sheet().doRead();

        setResult(id, resultList);
        List<UpdateTemplateTitleAndDescExcel> collect = resultList.stream().filter(a -> StringUtils.isBlank(a.getResult())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            // 存在有空数据
            Timestamp createTime = downloadLog.getCreateTime();
            if (timeoutDay != null && timeoutDay > 0) {
                // 使用TimeUnit避免手动计算
                long timeoutMillis = TimeUnit.DAYS.toMillis(timeoutDay);
                long diff = System.currentTimeMillis() - createTime.getTime();
                if (diff > timeoutMillis) {
                    XxlJobLogger.log("id[{}] 解析结果超时，超过{}天", id, timeoutDay);
                    collect.forEach(a -> {
                        a.setResult("失败");
                        a.setRemark("解析结果超时");
                    });
                    doWriteResult(downloadLog,resultList);
                }
            }
        } else {
            doWriteResult(downloadLog,resultList);
        }
    }

    /**
     * 写入结果
     * @param downloadLog
     * @param resultList
     */
    private void doWriteResult(SmallPlatformExcelDownloadLog downloadLog, List<UpdateTemplateTitleAndDescExcel> resultList) {
        File tempFile = OzonExcelUtils.createTemFile(downloadLog);
        if (tempFile == null) {
            throw new BusinessException(String.format("%s create tem file fail, type:%s", downloadLog.getId(), downloadLog.getType()));
        }
        EasyExcel.write(tempFile, UpdateTemplateTitleAndDescExcel.class).sheet("sheet").doWrite(resultList);
        // 上传到文件服务器
        String url = OzonExcelUtils.upload(tempFile, tempFile.getName());
        // 完成
        SmallPlatformExcelDownloadLog newDownloadLog = new SmallPlatformExcelDownloadLog();
        newDownloadLog.setId(downloadLog.getId());
        newDownloadLog.setStatus(SmallPlatformDownloadEnums.Status.COMPLETE.getCode());
        newDownloadLog.setCompleteTime(new Timestamp(System.currentTimeMillis()));
        newDownloadLog.setDownloadUrl(url);
        smallPlatformExcelDownloadLogService.updateByPrimaryKeySelective(newDownloadLog);
    }

    private void setResult(Integer excelId, List<UpdateTemplateTitleAndDescExcel> resultList) {
        List<FeedTask> feedTaskList = getFeedTask(excelId);
        Map<String, FeedTask> collect = feedTaskList.stream().collect(Collectors.toMap(FeedTask::getAttribute8, a -> a, (a, b) -> a));
        for (UpdateTemplateTitleAndDescExcel updateTemplateTitleAndDescExcel : resultList) {
            Integer rowIndex = updateTemplateTitleAndDescExcel.getRowIndex();
            FeedTask feedTask = collect.get(rowIndex.toString());
            if (feedTask == null) {
                continue;
            }
            Integer resultStatus = feedTask.getResultStatus();
            // 没有结果，跳过
            if (resultStatus == null) {
                continue;
            }
            String result = resultStatus == FeedTaskResultStatusEnum.SUCCESS.getResultStatus() ? "成功" : "失败";
            String resultMsg = feedTask.getResultMsg();
            updateTemplateTitleAndDescExcel.setResult(result);
            updateTemplateTitleAndDescExcel.setRemark(resultMsg);
        }
    }

    public List<FeedTask> getFeedTask(Integer excelId) {
        int limit = 100;
        long gtId = 0;
        List<FeedTask> list = new ArrayList<>();
        while (true) {
            FeedTaskExample example = new FeedTaskExample();
            example.createCriteria()
//                    .andAttribute10EqualTo("Excel")
                    .andAttribute8IsNotNull()
                    .andAttribute7EqualTo(excelId.toString())
                    .andResultStatusIsNotNull()
                    .andTaskTypeEqualTo(OzonFeedTaskEnums.TaskType.PUBLISH.name())
                    .andIdGreaterThan(gtId);
            example.setOrderByClause("id asc");
            example.setLimit(limit);
            example.setCustomColumn("id, result_status, result_msg, attribute8");
            List<FeedTask> feedTasks = ozonFeedTaskService.selectByExample(example, Platform.Ozon.name());
            if (CollectionUtils.isEmpty(feedTasks)) {
                break;
            }
            list.addAll(feedTasks);
            gtId = feedTasks.get(feedTasks.size() - 1).getId();
            if (feedTasks.size() < limit) {
                break;
            }
        }
        return list.stream().filter(a -> a.getAttribute8() != null).collect(Collectors.toList());
    }


    private void error(SmallPlatformExcelDownloadLog excelDownloadLog, Exception e) {
        SmallPlatformExcelDownloadLog downloadLog = new SmallPlatformExcelDownloadLog();
        downloadLog.setId(excelDownloadLog.getId());
        downloadLog.setStatus(SmallPlatformDownloadEnums.Status.FAIL.getCode());
        String message = e.getMessage();
        if (StringUtils.isNotBlank(message) && message.length() > 200) {
            message = message.substring(0, 200);
        }
        downloadLog.setMessage("执行导出时报错" + message);
        downloadLog.setCompleteTime(new Timestamp(System.currentTimeMillis()));
        smallPlatformExcelDownloadLogService.updateByPrimaryKeySelective(downloadLog);
    }
}
