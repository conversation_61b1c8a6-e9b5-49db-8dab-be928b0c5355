package com.estone.erp.publish.ozon.excel.handler;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItem;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsOzonItemRequest;
import com.estone.erp.publish.elasticsearch4.service.EsOzonItemService;
import com.estone.erp.publish.ozon.excel.model.IExcelRow;
import com.estone.erp.publish.platform.model.SmallPlatformExcelDownloadLog;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.io.InputStream;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Excel处理器抽象基类
 * 提取公共方法，优化代码结构
 */
@org.springframework.stereotype.Component
public abstract class AbstractListingExcelHandler<T extends IExcelRow> extends AbstractCommonExcelTemplateHandler<T> {

    @Autowired
    protected EsOzonItemService esOzonItemService;

    /**
     * 解析Excel文件
     *
     * @param ins        输入流
     * @param excelClass Excel类
     * @return 解析结果列表
     */
    protected List<T> parseExcelFile(InputStream ins, Class<T> excelClass) {
        List<T> resultList = new ArrayList<>();
        Set<String> head = head(excelClass);

        // 解析excel文件
        EasyExcel.read(ins, new AnalysisEventListener<Map<Integer, String>>() {
            @Override
            public void invoke(Map<Integer, String> data, AnalysisContext context) {
                if (context.readRowHolder().getRowIndex() == 0) {
                    validateExcelHeader(data, head);
                } else {
                    T excelRow = createExcelRowObject(data, context.readRowHolder().getRowIndex());
                    resultList.add(checkRequestParam(excelRow));
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                // 这个方法不需要实现，处理逻辑已移至processExcelData方法
            }
        }).headRowNumber(0).sheet().doRead();

        return resultList;
    }
    /**
     * 处理Excel数据
     * 子类需要实现此方法来处理特定类型的数据
     *
     * @param resultList Excel数据列表
     * @param username   用户名
     */
    protected abstract void processExcelData(List<T> resultList, String username);

    /**
     * 写入结果到文件
     *
     * @param resultList 结果列表
     * @param tempFile   临时文件
     * @param excelClass Excel类
     */
    protected void writeResultToFile(List<T> resultList, File tempFile, Class<T> excelClass) {
        EasyExcel.write(tempFile, excelClass).sheet("sheet").doWrite(resultList);
    }

    /**
     * 更新下载日志
     *
     * @param downloadLog 下载日志
     * @param resultList  结果列表
     */
    protected void updateDownloadLog(SmallPlatformExcelDownloadLog downloadLog, List<T> resultList) {
        downloadLog.setDownloadCount(resultList.size());
        List<String> accountNumbers = resultList.stream()
                .map(IExcelRow::getAccountNumber)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .limit(50)
                .collect(Collectors.toList());

        downloadLog.setAccountNumber(String.join(",", accountNumbers));
    }

    /**
     * 检查商品是否存在于在线列表中
     *
     * @param resultList 待检查的Excel数据列表
     * @param fields     需要查询的字段
     * @param executorTask 赋值
     * @return 检查通过的Excel数据列表
     */
    protected List<T> checkListing(List<T> resultList, String[] fields, BiConsumer<EsOzonItem, T> executorTask) {
        // 按账号分组处理数据
        Map<String, List<T>> accountGroups = groupByAccount(resultList);

        // 处理每个账号组
        accountGroups.forEach((accountNumber, items) -> processAccountGroup(accountNumber, items, fields, executorTask));

        // 返回所有没有被标记为失败的项
        return filterValidItems(resultList);
    }

    /**
     * 按账号分组
     *
     * @param items Excel数据项列表
     * @return 按账号分组的映射
     */
    protected Map<String, List<T>> groupByAccount(List<T> items) {
        return items.stream()
                .collect(Collectors.groupingBy(IExcelRow::getAccountNumber));
    }

    /**
     * 处理账号组
     *
     * @param accountNumber 账号
     * @param excelItems    Excel数据项列表
     * @param fields        需要查询的字段
     */
    protected void processAccountGroup(String accountNumber, List<T> excelItems, String[] fields, BiConsumer<EsOzonItem, T> executorTask) {
        // 获取所有不重复的sellerSku
        List<String> sellerSkuList = extractSellerSkus(excelItems);

        // 查询商品信息
        List<EsOzonItem> esOzonItems = fetchEsItems(accountNumber, sellerSkuList, fields);

        // 如果没有找到任何商品，标记所有项为失败
        if (CollectionUtils.isEmpty(esOzonItems)) {
            markAllAsFailed(excelItems, "商品不存在");
            return;
        }

        // 创建sellerSku到商品的映射，便于快速查找
        Map<String, EsOzonItem> skuToItemMap = createSkuToItemMap(esOzonItems);

        // 处理每个Excel项
        excelItems.forEach(excelItem -> processExcelItem(excelItem, skuToItemMap, executorTask));
    }

    /**
     * 提取sellerSku列表
     *
     * @param excelItems Excel数据项列表
     * @return sellerSku列表
     */
    protected List<String> extractSellerSkus(List<T> excelItems) {
        return excelItems.stream()
                .map(IExcelRow::getSellerSku)
                .distinct()
                .collect(Collectors.toList());
    }


    /**
     * 获取ES商品项
     *
     * @param accountNumber 账号
     * @param sellerSkuList sellerSku列表
     * @param fields        需要查询的字段
     * @return ES商品项列表
     */
    protected List<EsOzonItem> fetchEsItems(String accountNumber, List<String> sellerSkuList, String[] fields) {
        EsOzonItemRequest request = new EsOzonItemRequest();
        request.setAccountNumber(accountNumber);
        request.setSellerSkus(sellerSkuList);
        request.setFields(fields);

        return esOzonItemService.listItemByRequest(request);
    }

    /**
     * 将所有项标记为失败
     *
     * @param excelItems Excel数据项列表
     * @param message    错误消息
     */
    protected void markAllAsFailed(List<T> excelItems, String message) {
        excelItems.forEach(item -> {
            item.setResult("失败");
            item.setRemark(message);
        });
    }

    /**
     * 创建sellerSku到商品的映射
     *
     * @param esOzonItems ES商品项列表
     * @return sellerSku到商品的映射
     */
    protected Map<String, EsOzonItem> createSkuToItemMap(List<EsOzonItem> esOzonItems) {
        return esOzonItems.stream()
                .collect(Collectors.toMap(
                        EsOzonItem::getSellerSku,
                        Function.identity(),
                        (oldv, newv) -> newv
                ));
    }

    /**
     * 处理Excel项
     *
     * @param excelItem    Excel数据项
     * @param skuToItemMap sellerSku到商品的映射
     */
    protected void processExcelItem(T excelItem, Map<String, EsOzonItem> skuToItemMap, BiConsumer<EsOzonItem, T> executorTask) {
        String sellerSku = excelItem.getSellerSku();
        String productId = excelItem.getProductId();

        // 检查sellerSku是否存在
        if (!skuToItemMap.containsKey(sellerSku)) {
            excelItem.setResult("失败");
            excelItem.setRemark("sellerSku不存在");
            return;
        }

        EsOzonItem esOzonItem = skuToItemMap.get(sellerSku);
        // 检查productId是否匹配
        if (!productId.equals(esOzonItem.getProductId().toString())) {
            excelItem.setResult("失败");
            excelItem.setRemark("商品编码和sellerSku不匹配，excel：[sellerSku:" + sellerSku + ",productid:" + productId
                    + "]，在线列表：[sellerSku:" + esOzonItem.getSellerSku()+ ",productid:"+ esOzonItem.getProductId() + "]");
            return;
        }
        if (Objects.isNull(executorTask)) {
            return;
        }
        executorTask.accept(esOzonItem, excelItem);
    }

    /**
     * 过滤有效项
     *
     * @param items Excel数据项列表
     * @return 有效项列表
     */
    protected List<T> filterValidItems(List<T> items) {
        return items.stream()
                .filter(item -> StringUtils.isBlank(item.getResult()))
                .collect(Collectors.toList());
    }

}
