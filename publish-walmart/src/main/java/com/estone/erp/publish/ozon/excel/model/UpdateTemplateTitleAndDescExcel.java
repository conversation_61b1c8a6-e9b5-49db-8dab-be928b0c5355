package com.estone.erp.publish.ozon.excel.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.ozon.excel.handler.validator.ValidationResult;
import lombok.Data;

/**
 * 修改重量
 */
@Data
public class UpdateTemplateTitleAndDescExcel implements IExcelValidation {
    @ExcelProperty(value = "模版编号*", index = 0)
    private String templateIdStr;
    @ExcelProperty(value = "标题*", index = 1)
    private String title;
    @ExcelProperty(value = "描述*", index = 2)
    private String desc;

    @ExcelProperty(value = "结果", index = 3)
    private String result;
    @ExcelProperty(value = "备注", index = 4)
    private String remark;

    @ExcelIgnore
    private Integer rowIndex;
    @ExcelIgnore
    private Double beforeWeight;

    @ExcelIgnore
    private Integer excelId;

    @ExcelIgnore
    private Integer templateId;

    /**
     * 通过获取模版来判断权限
     */
    @ExcelIgnore
    private String accountNumber;
    /**
     * sku
     */
    @ExcelIgnore
    private String articleNumber;
    @ExcelIgnore
    private String sellerSku;

    @Override
    public ValidationResult validate() {
        ValidationResult result = new ValidationResult();
        // 验证基本字段
        validateRequiredFields(result, this);
        return result;
    }

    /**
     * 验证必填字段
     *
     * @param result 验证结果
     * @param excel  Excel数据项
     */
    private void validateRequiredFields(ValidationResult result, UpdateTemplateTitleAndDescExcel excel) {
        // 校验基本字段
        validateAndTrimIntegerField(result, excel.getTemplateIdStr(), "模版编号不能为空", excel::setTemplateId);
        validateAndTrimField(result, excel.getTitle(), "标题不能为空", excel::setTitle);
        validateAndTrimField(result, excel.getDesc(), "描述不能为空", excel::setDesc);
    }

}
