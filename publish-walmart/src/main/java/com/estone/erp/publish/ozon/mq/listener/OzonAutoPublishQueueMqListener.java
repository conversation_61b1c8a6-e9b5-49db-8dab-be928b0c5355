package com.estone.erp.publish.ozon.mq.listener;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.ozon.enums.OzonPublishProcessEnums;
import com.estone.erp.publish.ozon.handler.publish.BatchTemplatePublishExecutor;
import com.estone.erp.publish.ozon.handler.publish.SpuAutoPublishExecutor;
import com.estone.erp.publish.ozon.handler.publish.TemplatePublishExecutor;
import com.estone.erp.publish.ozon.handler.publish.parm.AutoPublishMessage;
import com.estone.erp.publish.ozon.handler.publish.parm.BatchPublishParam;
import com.estone.erp.publish.ozon.handler.publish.parm.SpuPublishParam;
import com.estone.erp.publish.ozon.handler.publish.parm.TemplatePublishParam;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * ozon 自动刊登队列
 * <AUTHOR>
 * @date 2023-07-14 14:50
 */
@ConditionalOnProperty(
        name = {"mq-config.ozon-auto-publish-queue-enable"},
        havingValue = "true"
)
@Slf4j
@Component
public class OzonAutoPublishQueueMqListener implements ChannelAwareMessageListener {

    @Autowired
    private BatchTemplatePublishExecutor batchTemplatePublishExecutor;

    @Autowired
    private SpuAutoPublishExecutor spuAutoPublishExecutor;

    @Autowired
    private TemplatePublishExecutor templatePublishExecutor;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.debug("queue[{}]: {}", "OZON_AUTO_PUBLISH_QUEUE", body);
        try {
            AutoPublishMessage publishMessage = JSON.parseObject(body, AutoPublishMessage.class);
            // 根据消息类型分发
            dispatch(publishMessage);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("自动刊登消息消费异常：message:{}",body, e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            throw new RuntimeException(e);
        }
    }

    private void dispatch(AutoPublishMessage autoPublishMessage) {
        Integer type = autoPublishMessage.getType();
        if (OzonPublishProcessEnums.PublishType.BATCH_PUBLISH.isTrue(type)) {
            // 批量刊登
            BatchPublishParam batchPublishParam = JSON.parseObject(autoPublishMessage.getPublishParam(), BatchPublishParam.class);
            batchTemplatePublishExecutor.execute(batchPublishParam);
        } else if (OzonPublishProcessEnums.PublishType.SPU_PUBLISH.isTrue(type)) {
            // spu直接刊登
            SpuPublishParam spuPublishParam = JSON.parseObject(autoPublishMessage.getPublishParam(), SpuPublishParam.class);
            spuAutoPublishExecutor.execute(spuPublishParam);
        } else if (OzonPublishProcessEnums.PublishType.TIME_PUBLISH.isTrue(type)) {
            // spu定时刊登
            SpuPublishParam spuPublishParam = JSON.parseObject(autoPublishMessage.getPublishParam(), SpuPublishParam.class);
            spuAutoPublishExecutor.execute(spuPublishParam);
        } else if (OzonPublishProcessEnums.PublishType.TEMPLATE.isTrue(type)) {
            TemplatePublishParam spuPublishParam = JSON.parseObject(autoPublishMessage.getPublishParam(), TemplatePublishParam.class);
            templatePublishExecutor.execute(spuPublishParam);
        } else {
            log.error("自动刊登消息转发异常：message:{}", JSON.toJSONString(autoPublishMessage));
        }
    }


}
