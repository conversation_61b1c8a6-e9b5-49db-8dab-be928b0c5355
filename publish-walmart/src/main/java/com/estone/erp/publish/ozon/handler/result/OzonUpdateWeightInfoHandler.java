package com.estone.erp.publish.ozon.handler.result;

import com.estone.erp.publish.ozon.call.model.response.ProductImportInfoResponse;
import com.estone.erp.publish.ozon.enums.OzonFeedTaskEnums;
import com.estone.erp.publish.ozon.handler.result.template.BatchAbstractOzonUpdateInfoHandler;
import com.estone.erp.publish.ozon.model.OzonPublishProcess;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 修改重量
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OzonUpdateWeightInfoHandler extends BatchAbstractOzonUpdateInfoHandler {

    /**
     * 修改重量有多个地方的数据来源，要重写
     *
     * @param ozonPublishProcess 处理报告
     * @param items              产品信息
     * @return 处理报告
     */
    @Override
    public List<FeedTask> getFeedTasks(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        List<String> sellerSkus = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(items)) {
            List<String> temp = items.stream().map(ProductImportInfoResponse.ProductInfo::getOfferId).filter(Objects::nonNull).collect(Collectors.toList());
            sellerSkus.addAll(temp);
        }
        // 优先根据id获取，如果获取不到，再根据sellerSku 获取
        List<FeedTask> newBatchFeedTasksById = getNewBatchFeedTasksById(ozonPublishProcess, items);
        if (CollectionUtils.isNotEmpty(newBatchFeedTasksById)) {
            return newBatchFeedTasksById;
        }
        FeedTaskExample feedTaskExample = new FeedTaskExample();
        feedTaskExample.setCustomColumn("id, association_id, attribute1, attribute4, attribute5, task_type");
        feedTaskExample.setOrderByClause("id ASC");
        FeedTaskExample.Criteria criteria = feedTaskExample.createCriteria()
                .andAccountNumberEqualTo(ozonPublishProcess.getAccountnumber())
                .andTaskTypeEqualTo(ozonPublishProcess.getTaskType())
                .andAttribute2EqualTo(ozonPublishProcess.getTaskId().toString());
        if (CollectionUtils.isNotEmpty(sellerSkus)) {
            criteria.andAttribute1In(sellerSkus);
        }
        return ozonFeedTaskService.selectByExample(feedTaskExample, Platform.Ozon.name());
    }

    @Override
    public void syncInfo(OzonPublishProcess ozonPublishProcess, List<ProductImportInfoResponse.ProductInfo> items) {
        super.syncAttribute(ozonPublishProcess, items);
    }

    @Override
    public String taskType() {
        return OzonFeedTaskEnums.TaskType.UPDATE_WEIGHT.name();
    }
}
