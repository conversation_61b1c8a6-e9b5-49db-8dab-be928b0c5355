package com.estone.erp.publish.ozon.job;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.ozon.enums.OzonFeedTaskEnums;
import com.estone.erp.publish.ozon.enums.OzonTemplateEnums;
import com.estone.erp.publish.ozon.model.OzonTemplateModel;
import com.estone.erp.publish.ozon.model.OzonTemplateModelExample;
import com.estone.erp.publish.ozon.model.OzonTimePublishQueue;
import com.estone.erp.publish.ozon.model.OzonTimePublishQueueExample;
import com.estone.erp.publish.ozon.service.OzonFeedTaskService;
import com.estone.erp.publish.ozon.service.OzonProblemClassificationHandler;
import com.estone.erp.publish.ozon.service.OzonTemplateModelService;
import com.estone.erp.publish.ozon.service.OzonTimePublishQueueService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.walmart.jobHandler
 * @Author: sj
 * @CreateTime: 2025-04-24  18:30
 * @Description: 模板刊登中超过2天设置为刊登失败，处理报告记录为刊登超时失败
 */

@Component
@Slf4j
public class OzonTemplatePublishTimeOutJobHandler extends AbstractJobHandler {

    public OzonTemplatePublishTimeOutJobHandler() {
        super("OzonTemplatePublishTimeOutJobHandler");
    }

    @Resource
    private OzonTemplateModelService ozonTemplateModelService;

    @Resource
    private OzonTimePublishQueueService ozonTimePublishQueueService;
    @Resource
    private OzonFeedTaskService ozonFeedTaskService;
    @Resource
    private OzonProblemClassificationHandler ozonProblemClassificationHandler;

    @XxlJob("OzonTemplatePublishTimeOutJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        Date dateBegin = DateUtils.getDateBegin(-2);

        OzonTemplateModelExample example = new OzonTemplateModelExample();
        example.createCriteria()
                .andPublishStatusEqualTo(OzonTemplateEnums.PublishStatus.PUBLISHING.getCode())
                .andUpdatedTimeLessThanOrEqualTo(new Timestamp(dateBegin.getTime()));
        List<OzonTemplateModel> ozonTemplateModels = ozonTemplateModelService.selectByExample(example);
        if (CollectionUtils.isNotEmpty(ozonTemplateModels)){
            for (OzonTemplateModel ozonTemplateModel : ozonTemplateModels) {
                ozonTemplateModel.setPublishStatus(OzonTemplateEnums.PublishStatus.ERROR.getCode());
                ozonTemplateModel.setUpdatedBy("admin");
                ozonTemplateModel.setUpdatedTime(new Timestamp(System.currentTimeMillis()));
                ozonProblemClassificationHandler.handleTemplateProblemClassification(ozonTemplateModel, "刊登超时", OzonFeedTaskEnums.TaskType.PUBLISH.name());
                ozonTemplateModelService.updateByPrimaryKeySelective(ozonTemplateModel);

                FeedTaskExample executingFeedTaskExample = new FeedTaskExample();
                executingFeedTaskExample.createCriteria()
                        .andPlatformEqualTo(Platform.Ozon.name())
                        .andTaskStatusEqualTo(TaskStatusEnum.EXECUTING.getStatusCode())
                        .andTaskTypeEqualTo(OzonFeedTaskEnums.TaskType.PUBLISH.name())
                        .andAssociationIdEqualTo(String.valueOf(ozonTemplateModel.getId()))
                        .andAccountNumberEqualTo(ozonTemplateModel.getAccountNumber());
                List<FeedTask> executingFeedTasks = ozonFeedTaskService.selectByExample(executingFeedTaskExample, Platform.Ozon.name());
                if (CollectionUtils.isNotEmpty(executingFeedTasks)){
                    ozonFeedTaskService.batchUpdateFeedTaskToFinish(executingFeedTasks, TaskStatusEnum.FINISH.getStatusCode(), 2, "刊登超时");
                }else {
                    FeedTask feedTask = new FeedTask();
                    feedTask.setPlatform(Platform.Ozon.name());
                    feedTask.setTaskType(OzonFeedTaskEnums.TaskType.PUBLISH.name());
                    feedTask.setTaskStatus(TaskStatusEnum.FINISH.getStatusCode());
                    feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                    feedTask.setResultMsg("刊登超时");
                    feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                    feedTask.setAssociationId(String.valueOf(ozonTemplateModel.getId()));
                    feedTask.setAccountNumber(ozonTemplateModel.getAccountNumber());
                    feedTask.setArticleNumber(ozonTemplateModel.getArticleNumber());
                    feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
                    feedTask.setCreatedBy("admin");
                    feedTask.setTableIndex();
                    ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, feedTask.getResultMsg(), feedTask.getTaskType());
                    ozonFeedTaskService.insert(feedTask);
                }

                //队列
                OzonTimePublishQueueExample ozonTimePublishQueueExample = new OzonTimePublishQueueExample();
                ozonTimePublishQueueExample.createCriteria()
                        .andTemplateIdEqualTo(ozonTemplateModel.getId())
                        .andAccountNumberEqualTo(ozonTemplateModel.getAccountNumber())
                        .andPublishStatusEqualTo(2)
                        .andCreatedTimeLessThanOrEqualTo(new Timestamp(dateBegin.getTime()));

                List<OzonTimePublishQueue> ozonTimePublishQueues = ozonTimePublishQueueService.selectByExample(ozonTimePublishQueueExample);
                if (CollectionUtils.isNotEmpty(ozonTimePublishQueues)){
                    for (OzonTimePublishQueue ozonTimePublishQueue : ozonTimePublishQueues) {
                        ozonTimePublishQueue.setPublishStatus(4);
                        ozonTimePublishQueue.setUpdateTime(new Timestamp(System.currentTimeMillis()));
                        ozonTimePublishQueue.setPublishTime(new Timestamp(System.currentTimeMillis()));
                        ozonTimePublishQueueService.updateByPrimaryKeySelective(ozonTimePublishQueue);
                    }
                }
            }
        }
        return ReturnT.SUCCESS;
    }
}
