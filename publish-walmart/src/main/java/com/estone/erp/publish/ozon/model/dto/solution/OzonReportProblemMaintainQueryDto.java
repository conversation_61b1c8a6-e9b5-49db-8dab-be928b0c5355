package com.estone.erp.publish.ozon.model.dto.solution;

import lombok.Data;

import java.util.List;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.tidb.publishtidb.dto
 * @Author: sj
 * @CreateTime: 2025-03-04  14:41
 * @Description: TODO
 */
@Data
public class OzonReportProblemMaintainQueryDto {

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 问题分类
     */
    private String problemType;

    /**
     * 处理报告关键字
     */
    private String report;

    private Integer pageSize;
    private Integer pageNum;

    /**
     * 导出勾选ID
     */
    private List<Integer> idList;
}
