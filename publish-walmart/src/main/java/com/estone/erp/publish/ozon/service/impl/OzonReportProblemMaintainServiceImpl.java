package com.estone.erp.publish.ozon.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.ozon.model.dto.solution.OzonReportProblemMaintainQueryDto;
import com.estone.erp.publish.ozon.mq.OzonMqConfig;
import com.estone.erp.publish.ozon.service.OzonReportProblemMaintainService;
import com.estone.erp.publish.platform.enums.SmallPlatformDownloadEnums;
import com.estone.erp.publish.platform.model.SmallPlatformExcelDownloadLog;
import com.estone.erp.publish.platform.service.SmallPlatformExcelDownloadLogService;
import com.estone.erp.publish.tidb.publishtidb.mapper.OzonReportProblemMaintainMapper;
import com.estone.erp.publish.tidb.publishtidb.model.OzonReportProblemMaintain;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【aliexpress_report_problem_maintain】的数据库操作Service实现
 * @createDate 2025-03-04 14:38:43
 */
@Service
public class OzonReportProblemMaintainServiceImpl extends ServiceImpl<OzonReportProblemMaintainMapper, OzonReportProblemMaintain>
        implements OzonReportProblemMaintainService {

    @Resource
    private SmallPlatformExcelDownloadLogService smallPlatformExcelDownloadLogService;
    @Resource
    private RabbitMqSender rabbitMqSender;

    @Override
    public List<String> getAllSolutionType() {
        LambdaQueryWrapper<OzonReportProblemMaintain> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OzonReportProblemMaintain::getProblemType);
        queryWrapper.groupBy(OzonReportProblemMaintain::getProblemType);
        List<OzonReportProblemMaintain> list = this.list(queryWrapper);
        return list.stream().map(OzonReportProblemMaintain::getProblemType).distinct().collect(Collectors.toList());
    }

    @Override
    public String saveOrUpdateByEntity(OzonReportProblemMaintain aliexpress) {
        String result = "";
        if (ObjectUtils.isEmpty(aliexpress)) {
            throw new BusinessException("参数不能为空");
        }
        if (aliexpress.getReport() != null) {
            aliexpress.setReport(aliexpress.getReport().trim());
        }
        if (aliexpress.getOperationType() != null) {
            aliexpress.setSolutionType(aliexpress.getSolutionType().trim());
        }
        if (aliexpress.getProblemType() != null) {
            aliexpress.setProblemType(aliexpress.getProblemType().trim());
        }
        //判断是否有重复类型数据
        LambdaQueryWrapper<OzonReportProblemMaintain> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OzonReportProblemMaintain::getOperationType, aliexpress.getOperationType())
                .eq(OzonReportProblemMaintain::getReport, aliexpress.getReport());

        if (ObjectUtils.isNotEmpty(aliexpress.getId())) {
            queryWrapper.ne(OzonReportProblemMaintain::getId, aliexpress.getId());
            List<OzonReportProblemMaintain> aliexpressReportProblemMaintainList = this.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(aliexpressReportProblemMaintainList)) {
                throw new BusinessException("该类型数据已存在");
            }
            aliexpress.setUpdatedBy(WebUtils.getUserName());
            aliexpress.setUpdatedTime(new Timestamp(System.currentTimeMillis()));
            this.updateById(aliexpress);
            result = "修改成功";
        } else {
            List<OzonReportProblemMaintain> aliexpressReportProblemMaintainList = this.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(aliexpressReportProblemMaintainList)) {
                throw new BusinessException("该类型数据已存在");
            }
            aliexpress.setCreatedTime(new Timestamp(System.currentTimeMillis()));
            aliexpress.setCreatedBy(WebUtils.getUserName());
            aliexpress.setUpdatedBy(WebUtils.getUserName());
            aliexpress.setUpdatedTime(new Timestamp(System.currentTimeMillis()));
            this.save(aliexpress);
            result = "新增成功";
        }
        return result;
    }

    @Override
    public IPage<OzonReportProblemMaintain> pageQuery(OzonReportProblemMaintainQueryDto dto) {
        if (ObjectUtils.isEmpty(dto)) {
            throw new BusinessException("查询条件不能为空");
        }
        LambdaQueryWrapper<OzonReportProblemMaintain> queryWrapper = this.getQueryWrapper(dto);
        Page<OzonReportProblemMaintain> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        return this.page(page, queryWrapper);
    }

    private LambdaQueryWrapper<OzonReportProblemMaintain> getQueryWrapper(OzonReportProblemMaintainQueryDto dto) {
        LambdaQueryWrapper<OzonReportProblemMaintain> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(dto.getOperationType())) {
            queryWrapper.eq(OzonReportProblemMaintain::getOperationType, dto.getOperationType());
        }
        if (StringUtils.isNotBlank(dto.getProblemType())) {
            queryWrapper.eq(OzonReportProblemMaintain::getProblemType, dto.getProblemType());
        }
        if (StringUtils.isNotBlank(dto.getReport())) {
            queryWrapper.like(OzonReportProblemMaintain::getReport, dto.getReport());
        }
        if (CollectionUtils.isNotEmpty(dto.getIdList())) {
            queryWrapper.in(OzonReportProblemMaintain::getId, dto.getIdList());
        }
        queryWrapper.orderByDesc(OzonReportProblemMaintain::getCreatedTime);
        return queryWrapper;
    }

    @Override
    public ApiResult<String> export(OzonReportProblemMaintainQueryDto dto) {
        try {
            // 处理查询条件
            LambdaQueryWrapper<OzonReportProblemMaintain> queryWrapper = this.getQueryWrapper(dto);
            int count = count(queryWrapper);
            if (count > 500000) {
                return ApiResult.newError("导出数据量过大,请缩小查询条件");
            }

            SmallPlatformExcelDownloadLog downloadLog = new SmallPlatformExcelDownloadLog();
            downloadLog.setType(SmallPlatformDownloadEnums.Type.REPORT_PROBLEM.name());
            downloadLog.setQueryCondition(JSON.toJSONString(dto));
            downloadLog.setPlatform(SaleChannel.CHANNEL_OZON);
            downloadLog.setStatus(SmallPlatformDownloadEnums.Status.WAIT.getCode());
            downloadLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
            downloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
            int insert = smallPlatformExcelDownloadLogService.insert(downloadLog);
            if (insert > 0) {
                // 发送到下载队列
                rabbitMqSender.allPublishVHostRabbitTemplateSend(OzonMqConfig.OZON_API_DIRECT_EXCHANGE, OzonMqConfig.OZON_DOWNLOAD_QUEUE_KEY, downloadLog.getId());
            }
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
        return ApiResult.newSuccess("前往导出结果页查询");
    }
}




