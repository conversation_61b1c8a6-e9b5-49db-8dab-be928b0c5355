package com.estone.erp.publish.tidb.publishtidb.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.estone.erp.publish.component.converter.SaleNameConverter;
import com.estone.erp.publish.ozon.converter.TaskTypeConverter;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * @TableName ozon_report_problem_maintain
 */
@TableName(value ="ozon_report_problem_maintain")
@Data
public class OzonReportProblemMaintain {
    /**
     * 
     */
    @ExcelProperty(value = "ID",index = 0)
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 操作类型
     */
    @NotBlank(message = "操作类型不能为空")
    @ExcelProperty(value = "操作类型",index = 1,converter = TaskTypeConverter.class)
    @TableField(value = "operation_type")
    private String operationType;

    /**
     * 问题分类
     */
    @ExcelProperty(value = "问题分类",index = 2)
    @TableField(value = "problem_type")
    private String problemType;

    /**
     * 处理报告
     */
    @NotBlank(message = "处理报告不能为空")
    @ExcelProperty(value = "处理报告",index = 3)
    @TableField(value = "report")
    private String report;

    /**
     * 解决方案
     */
    @NotBlank(message = "解决方案不能为空")
    @ExcelProperty(value = "解决方案",index = 4)
    @TableField(value = "solution_type")
    private String solutionType;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人", index = 5, converter = SaleNameConverter.class)
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间", index = 6)
    @TableField(value = "created_time")
    private Date createdTime;

    /**
     * 修改人
     */
    @ExcelProperty(value = "修改人", index = 7, converter = SaleNameConverter.class)
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @ExcelProperty(value = "修改时间", index = 8)
    @TableField(value = "updated_time")
    private Date updatedTime;
}