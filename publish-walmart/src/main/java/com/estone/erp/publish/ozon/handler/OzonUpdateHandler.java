package com.estone.erp.publish.ozon.handler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.common.util.PagingUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.executors.ExecutorUtils;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItem;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItemAttributes;
import com.estone.erp.publish.elasticsearch4.model.EsOzonStockInfo;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsOzonItemRequest;
import com.estone.erp.publish.elasticsearch4.service.EsOzonItemService;
import com.estone.erp.publish.ozon.call.OzonApiClient;
import com.estone.erp.publish.ozon.call.OzonResponseResult;
import com.estone.erp.publish.ozon.call.model.AttributesDTO;
import com.estone.erp.publish.ozon.call.model.request.*;
import com.estone.erp.publish.ozon.call.model.response.CreateProductResponse;
import com.estone.erp.publish.ozon.call.model.response.UpdateResponse;
import com.estone.erp.publish.ozon.call.model.richcontent.BaseRichContent;
import com.estone.erp.publish.ozon.call.model.richcontent.ImgBlocks;
import com.estone.erp.publish.ozon.call.model.richcontent.RichContent;
import com.estone.erp.publish.ozon.call.model.richcontent.TxtImgBlocks;
import com.estone.erp.publish.ozon.common.OzonConstant;
import com.estone.erp.publish.ozon.common.OzonEsItemBulkProcessor;
import com.estone.erp.publish.ozon.common.OzonExecutors;
import com.estone.erp.publish.ozon.enums.*;
import com.estone.erp.publish.ozon.handler.common.ExtendJsonDto;
import com.estone.erp.publish.ozon.handler.common.UpdateAttributeDto;
import com.estone.erp.publish.ozon.handler.update.UpdateMinPriceAfterSyncHandler;
import com.estone.erp.publish.ozon.model.OzonAccountConfig;
import com.estone.erp.publish.ozon.model.OzonCalcPriceRule;
import com.estone.erp.publish.ozon.model.OzonPublishProcess;
import com.estone.erp.publish.ozon.model.OzonUpdateItemLog;
import com.estone.erp.publish.ozon.model.dto.*;
import com.estone.erp.publish.ozon.model.vo.OzonEditItemDescVO;
import com.estone.erp.publish.ozon.service.*;
import com.estone.erp.publish.ozon.utils.OzonCalcUtils;
import com.estone.erp.publish.ozon.utils.OzonImageUtils;
import com.estone.erp.publish.ozon.utils.OzonRetryUtil;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-04-06 16:58
 */
@Slf4j
@Component
public class OzonUpdateHandler {

    @Autowired
    private OzonFeedTaskService feedTaskService;
    @Autowired
    private OzonApiClient apiClient;
    @Autowired
    private OzonEsItemBulkProcessor esItemBulkProcessor;
    @Autowired
    private OzonAccountConfigService accountConfigService;
    @Autowired
    private EsOzonItemService esOzonItemService;
    @Autowired
    private OzonTemplateModelService templateModelService;
    @Autowired
    private OzonEsItemService ozonEsItemService;
    @Autowired
    protected OzonPublishProcessService publishProcessService;
    @Autowired
    private OzonUpdateItemLogService ozonUpdateItemLogService;
    @Resource
    private UpdateMinPriceAfterSyncHandler updateMinPriceAfterSyncHandler;
    @Resource
    private OzonProblemClassificationHandler ozonProblemClassificationHandler;

    /**
     * 修改库存
     * 单个店铺在一次请求中最多可以改变100个商品。每分钟最多可以发送80个请求。
     *
     * @param accountNumber  店铺
     * @param updateDateList 数据
     */
    public void updateStock(String accountNumber, List<OzonUpdateDO> updateDateList) {
        List<List<OzonUpdateDO>> partition = Lists.partition(updateDateList, 100);
        for (List<OzonUpdateDO> ozonUpdateDOS : partition) {
            // 处理报告
            feedTaskService.batchNewUpdateFeeds(ozonUpdateDOS, OzonFeedTaskEnums.TaskType.UPDATE_STOCK.name());
            // call Api
            ExecutorUtils.execute(OzonExecutors.UPDATE_STOCK_POOL, () -> {
                updateStockHandler(accountNumber, null, ozonUpdateDOS);
            }, "ozon-update-stock");
        }
    }

    /**
     * 修改库存
     * 单个店铺在一次请求中最多可以改变100个商品。每分钟最多可以发送80个请求。
     *
     * @param accountNumber  店铺
     * @param templateId     模板编号
     * @param updateDateList 数据
     */
    public void updateTemplateStock(String accountNumber, Integer templateId, List<OzonUpdateDO> updateDateList) {
        List<List<OzonUpdateDO>> partition = Lists.partition(updateDateList, 100);
        for (List<OzonUpdateDO> ozonUpdateDOS : partition) {
            // 处理报告
            feedTaskService.batchNewUpdateFeeds(ozonUpdateDOS, OzonFeedTaskEnums.TaskType.UPDATE_STOCK.name());
            // call Api
            ExecutorUtils.execute(OzonExecutors.UPDATE_STOCK_POOL, () -> {
                updateStockHandler(accountNumber, templateId, ozonUpdateDOS);
            }, "ozon-update-stock");
        }
    }

    /**
     * 同步修改库存
     */
    public void syncUpdateStock(String accountNumber, List<OzonUpdateDO> updateDateList, String username) {
        if (CollectionUtils.isEmpty(updateDateList)) {
            return;
        }

        List<List<OzonUpdateDO>> partition = Lists.partition(updateDateList, 100);
        CompletableFuture.allOf(partition.stream().map(partList -> CompletableFuture.runAsync(() -> {
            try {
                DataContextHolder.setUsername(username);
                // 处理报告
                feedTaskService.batchNewUpdateFeeds(partList, OzonFeedTaskEnums.TaskType.UPDATE_STOCK.name());
                // call Api
                updateTimerStockHandler(accountNumber, partList);
            } catch (Exception e) {
                log.error("修改库存异常：{}", accountNumber, e);
            }
        }, OzonExecutors.SYNC_UPDATE_STOCK_POOL)).toArray(CompletableFuture[]::new)).join();
    }

    /**
     * 同步修改库存
     */
    public void syncUpdateStock(String accountNumber, List<OzonUpdateDO> updateDateList) {
        if (CollectionUtils.isEmpty(updateDateList)) {
            return;
        }
        syncUpdateStock(accountNumber, updateDateList, "admin");
    }

    private void remarkUpdateLog(FeedTask feedTask) {
        try {
            OzonUpdateItemLog ozonUpdateItemLog = new OzonUpdateItemLog();
            ozonUpdateItemLog.setAccountNumber(feedTask.getAccountNumber());
            ozonUpdateItemLog.setType(feedTask.getTaskType());
            ozonUpdateItemLog.setProductId(feedTask.getAssociationId());
            ozonUpdateItemLog.setSellerSku(feedTask.getAttribute1());
            ozonUpdateItemLog.setWarehouseId(feedTask.getAttribute5());
            ozonUpdateItemLog.setBefore(feedTask.getAttribute3());
            ozonUpdateItemLog.setAfter(feedTask.getAttribute4());
            ozonUpdateItemLog.setCreateTime(new Timestamp(System.currentTimeMillis()));
            ozonUpdateItemLogService.insert(ozonUpdateItemLog);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }


    /**
     * 修改价格
     *
     * @param accountNumber  账号
     * @param updateDateList 修改数据
     */
    public void updatePrice(String accountNumber, List<OzonUpdateDO> updateDateList, OzonUpdatePriceTypeEnum typeEnum, boolean sync) {
        // 获取店铺配置算价规则
        OzonAccountConfig accountConfig = accountConfigService.selectConfigWithCalePriceRule(accountNumber);
        List<List<OzonUpdateDO>> partition = Lists.partition(updateDateList, 500);
        for (List<OzonUpdateDO> updateDOList : partition) {
            try {
                if (accountConfig != null
                        && CollectionUtils.isNotEmpty(accountConfig.getCalcPriceRules())
                        && OzonUpdatePriceTypeEnum.UPDATE_PRICE.getCode().equals(typeEnum.getCode())) {
                    // 匹配sku标签
                    OzonCalcUtils.listingUpdatePriceMatchSkuTag(updateDOList);
                    // 匹配算价规则
                    List<OzonCalcPriceRule> calcPriceRules = accountConfig.getCalcPriceRules();
                    OzonCalcUtils.matchListingCalcRule(calcPriceRules, updateDOList);
                }
            } catch (Exception e) {
                log.error("更新价格失败：{}", e.getMessage());
            }
            // 处理报告
            feedTaskService.batchNewUpdateFeeds(updateDOList, OzonUpdatePriceTypeEnum.getTaskType(typeEnum));
        }
        if (!sync) {
            for (List<OzonUpdateDO> list : partition) {
                ExecutorUtils.execute(OzonExecutors.UPDATE_PRICE_POOL, () -> {
                    updatePrice(accountNumber, typeEnum, list);
                }, "update_price_pool");
            }
        } else {
            CompletableFuture.allOf(partition.stream().map(list -> CompletableFuture.runAsync(() -> {
                updatePrice(accountNumber, typeEnum, list);
            }, OzonExecutors.SYNC_UPDATE_PRICE_POOL)).toArray(CompletableFuture[]::new)).join();
        }
    }

    /**
     * 修改价格
     * @param accountNumber
     * @param typeEnum
     * @param list
     */
    private void updatePrice(String accountNumber, OzonUpdatePriceTypeEnum typeEnum, List<OzonUpdateDO> list) {
        UpdatePriceRequest updatePriceRequest = new UpdatePriceRequest();
        updatePriceRequest.initPricesInfo(list, typeEnum);
        OzonResponseResult<List<UpdateResponse>> listOzonResponseResult = apiClient.updatePrice(accountNumber, updatePriceRequest);
        if (!listOzonResponseResult.isSuccess()) {
            // 处理报告失败
            for (OzonUpdateDO ozonUpdateDO : list) {
                ozonUpdateDO.setSuccess(false);
                ozonUpdateDO.setErrorMsg(listOzonResponseResult.getMessage());
            }
            // 处理报告失败
            feedTaskService.batchUpdateFail(accountNumber, list, OzonUpdatePriceTypeEnum.getTaskType(typeEnum), JSON.toJSONString(listOzonResponseResult));
            return;
        }
        log.info("update price {}", JSON.toJSONString(listOzonResponseResult));
        List<String> associationIds = list.stream().map(OzonUpdateDO::getProductId).map(String::valueOf).collect(Collectors.toList());
        List<FeedTask> feedTasks = getFeedsByAccountAndId(accountNumber, associationIds, OzonUpdatePriceTypeEnum.getTaskType(typeEnum), list.get(0).getJob());
        if (CollectionUtils.isEmpty(feedTasks)) {
            log.error("未找到对应处理报告：{},{},{}", accountNumber, OzonUpdatePriceTypeEnum.getTaskType(typeEnum), associationIds);
            return;
        }
        updateFeedTask(listOzonResponseResult.getResult(), list, feedTasks, false);
        // 处理本地数据
        afterSuccessHandler(accountNumber, feedTasks);
        feedTaskService.batchUpdateFeeds(feedTasks);
    }

    /**
     * 请求平台修改库存 Ozon改库存需要指定fbs仓库才能改库存
     * 获取店铺配置的ESTD仓库ID
     * 单个店铺在一次请求中最多可以改变100个商品。每分钟最多可以发送80个请求。
     * 执行修改库存
     */
    private void updateStockHandler(String accountNumber, Integer templateId, List<OzonUpdateDO> updateDataList) {
        if (CollectionUtils.isEmpty(updateDataList)) {
            return;
        }
        OzonAccountConfig accountConfig = accountConfigService.selectByAccountNumber(accountNumber);
        if (accountConfig == null || Objects.isNull(accountConfig.getUpdateStockWarehouseId())) {
            // 处理报告失败
            feedTaskService.batchUpdateFail(accountNumber, updateDataList, OzonFeedTaskEnums.TaskType.UPDATE_STOCK.name(), "店铺未配置库存仓库");
            return;
        }
        Long updateStockWarehouseId = accountConfig.getUpdateStockWarehouseId();
        UpdateStockRequest updateStockRequest = new UpdateStockRequest();
        updateStockRequest.initStockInfo(updateDataList, updateStockWarehouseId);
        executeUpdateStock(accountNumber, updateDataList, updateStockRequest, templateId, false);
    }

    /**
     * 请求平台修改库存 Ozon改库存需要指定fbs仓库才能改库存
     * 获取店铺配置的ESTD仓库ID
     * 单个店铺在一次请求中最多可以改变100个商品。每分钟最多可以发送80个请求。
     * 执行修改库存
     */
    private void updateTimerStockHandler(String accountNumber, List<OzonUpdateDO> updateDataList) {
        if (CollectionUtils.isEmpty(updateDataList)) {
            return;
        }
        UpdateStockRequest updateStockRequest = new UpdateStockRequest();
        updateStockRequest.initStockInfo(updateDataList, null);
        executeUpdateStock(accountNumber, updateDataList, updateStockRequest, null, true);
    }

    /**
     * 执行修改库存
     */
    private void executeUpdateStock(String accountNumber, List<OzonUpdateDO> updateDataList, UpdateStockRequest updateStockRequest, Integer templateId, Boolean needLog) {
        OzonResponseResult<List<UpdateResponse>> listOzonResponseResult = apiClient.updateStock(accountNumber, updateStockRequest);
        if (!listOzonResponseResult.isSuccess()) {
            // 处理报告失败
            for (OzonUpdateDO ozonUpdateDO : updateDataList) {
                ozonUpdateDO.setSuccess(false);
            }
            feedTaskService.batchUpdateFail(accountNumber, updateDataList, OzonFeedTaskEnums.TaskType.UPDATE_STOCK.name(), JSON.toJSONString(listOzonResponseResult));
            return;
        }
        if (templateId != null) {
            // 更新模板信息
            Boolean uploadSuccess = listOzonResponseResult.isSuccess();
            templateModelService.updateInventoryStatus(templateId, uploadSuccess);
        }
//        log.info("update stock {}", JSON.toJSONString(listOzonResponseResult));
        List<String> associationIds = updateDataList.stream().map(OzonUpdateDO::getProductId).map(String::valueOf).collect(Collectors.toList());
        List<FeedTask> feedTasks = getFeedsByAccountAndId(accountNumber, associationIds, OzonFeedTaskEnums.TaskType.UPDATE_STOCK.name(), updateDataList.get(0).getJob());
        if (CollectionUtils.isEmpty(feedTasks)) {
            log.error("未找到对应处理报告：{},{},{}", accountNumber, OzonFeedTaskEnums.TaskType.UPDATE_STOCK.name(), associationIds);
            return;
        }
        updateFeedTask(listOzonResponseResult.getResult(), updateDataList, feedTasks, needLog);
        // 处理本地数据
        afterSuccessHandler(accountNumber, feedTasks);
        // 记录每条处理报告
        feedTaskService.batchUpdateFeeds(feedTasks);
    }

    private void updateFeedTask(List<UpdateResponse> responseList, List<OzonUpdateDO> ozonUpdateDO, List<FeedTask> feedTasks, Boolean needLog) {
        Map<Long, UpdateResponse> stockResponseMap = responseList.stream().collect(Collectors.toMap(UpdateResponse::getProductId, Function.identity(), (o1, o2) -> o1));
        for (FeedTask feedTask : feedTasks) {
            feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
            feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
            feedTask.setTableIndex();

            Long associationId = Long.valueOf(feedTask.getAssociationId());
            UpdateResponse updateResponse = stockResponseMap.get(associationId);
            if (updateResponse == null) {
                feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                feedTask.setResultMsg(StringUtils.defaultString(feedTask.getResultMsg(),"") + " not find result,"+JSON.toJSONString(responseList));
                ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, feedTask.getResultMsg(), feedTask.getTaskType());
                continue;
            }

            if (Boolean.TRUE.equals(updateResponse.getUpdated())) {
                feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                feedTask.setResultMsg(StringUtils.defaultString(feedTask.getResultMsg(),"")+",success");
                if (needLog) {
                    // 备份表
                    remarkUpdateLog(feedTask);
                }
            }else {
                feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                feedTask.setResultMsg(StringUtils.defaultString(feedTask.getResultMsg(),"") + JSON.toJSONString(updateResponse));
                ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, feedTask.getResultMsg(), feedTask.getTaskType());
            }
        }

        for (OzonUpdateDO updateDO : ozonUpdateDO) {
            Long productId = updateDO.getProductId();
            UpdateResponse updateResponse = stockResponseMap.get(productId);
            if (updateResponse == null) {
                updateDO.setSuccess(false);
            } else {
                updateDO.setSuccess(Boolean.TRUE.equals(updateResponse.getUpdated()));
                updateDO.setErrorMsg(JSON.toJSONString(updateResponse));
            }
        }
    }

    /**
     * 修改本地数据
     *
     * @param feedTasks feedTask
     */
    private void afterSuccessHandler(String accountNumber, List<FeedTask> feedTasks) {
        if (OzonFeedTaskEnums.TaskType.UPDATE_STOCK.name().equals(feedTasks.get(0).getTaskType())) {
            updateStockLocalData(feedTasks);
        } else {
            updatePriceLocalData(accountNumber, feedTasks);
        }
        feedTaskService.batchUpdateFeeds(feedTasks);
    }

    private void updatePriceLocalData(String accountNumber, List<FeedTask> feedTasks) {
        List<OzonUpdateMinPriceDO> list = new ArrayList<>();
        for (FeedTask feedTask : feedTasks) {
            String updateAfterValue = feedTask.getAttribute4();
            if (StringUtils.isNotBlank(updateAfterValue)
                    && FeedTaskResultStatusEnum.SUCCESS.getResultStatus() == feedTask.getResultStatus()) {
                if (OzonFeedTaskEnums.TaskType.UPDATE_PRICE.name().equals(feedTask.getTaskType())) {
                    OzonUpdatePriceDO ozonUpdatePriceDO = new OzonUpdatePriceDO();
                    if (updateAfterValue.contains(",")) {
                        String[] priceArray = updateAfterValue.split(", ");
                        ozonUpdatePriceDO.setPrice(priceArray[1]);
                        ozonUpdatePriceDO.setPriceNumber(new BigDecimal(priceArray[1]).doubleValue());
                    }
                    ozonUpdatePriceDO.setId(feedTask.getAssociationId());
                    ozonUpdatePriceDO.setUpdateDate(new Date());
                    esItemBulkProcessor.updatePrice(ozonUpdatePriceDO);
                } else {
                    OzonUpdateMinPriceDO ozonUpdatePriceDO = new OzonUpdateMinPriceDO();
                    ozonUpdatePriceDO.setMinPrice(updateAfterValue);
                    ozonUpdatePriceDO.setMinPriceNumber(new BigDecimal(updateAfterValue).doubleValue());
                    ozonUpdatePriceDO.setId(feedTask.getAssociationId());
                    ozonUpdatePriceDO.setUpdateDate(new Date());
                    ozonUpdatePriceDO.setMinPriceNumberUpdateDate(new Date());
                    list.add(ozonUpdatePriceDO);
                    esItemBulkProcessor.updateMinPrice(ozonUpdatePriceDO);
                }
            }
        }
        // other operator
        list = list.stream().filter(a -> StringUtils.isNotBlank(a.getId()) && Objects.nonNull(a.getMinPriceNumber())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            updateMinPriceAfterSyncHandler.syncMinPrice(list, accountNumber);
        }
    }

    /**
     * 修改本地库存数据
     */
    private void updateStockLocalData(List<FeedTask> feedTasks) {
        Map<String, List<FeedTask>> productIdToTaskMap = feedTasks.stream().collect(Collectors.groupingBy(FeedTask::getAssociationId));
        for (Map.Entry<String, List<FeedTask>> entry : productIdToTaskMap.entrySet()) {
            String productId = entry.getKey();
            List<FeedTask> tasks = entry.getValue();
            try {
                // 获取链接本地仓库信息
                EsOzonItemRequest request = new EsOzonItemRequest();
                request.setFields(new String[]{"id", "warehouseStockInfos"});
                request.setAccountNumber(tasks.get(0).getAccountNumber());
                request.setId(productId);
                List<EsOzonItem> esOzonItems = esOzonItemService.listItemByRequest(request);
                if (CollectionUtils.isEmpty(esOzonItems)) {
                    return;
                }
                EsOzonItem esOzonItem = esOzonItems.get(0);
                List<EsOzonStockInfo> warehouseStockInfos = esOzonItem.getWarehouseStockInfos();
                if (CollectionUtils.isEmpty(warehouseStockInfos)) {
                    return;
                }

                for (FeedTask feedTask : tasks) {
                    String updateAfterValue = feedTask.getAttribute4();
                    if (StringUtils.isNotBlank(updateAfterValue)
                            && FeedTaskResultStatusEnum.SUCCESS.getResultStatus() == feedTask.getResultStatus()) {
                        EsOzonStockInfo esOzonStockInfo = warehouseStockInfos.stream()
                                .filter(wareHouseInfo -> Long.valueOf(feedTask.getAttribute5()).equals(wareHouseInfo.getWarehouseId()))
                                .findFirst()
                                .orElse(null);
                        if (null == esOzonStockInfo) {
                            continue;
                        }
                        esOzonStockInfo.setPresent(Long.valueOf(updateAfterValue));
                    }
                }
                TemporaryOzonUpdateStockDO ozonUpdateStockDO = new TemporaryOzonUpdateStockDO();
                ozonUpdateStockDO.setId(esOzonItem.getId());
                ozonUpdateStockDO.setWarehouseStockInfos(warehouseStockInfos);
                Integer reduce = warehouseStockInfos.stream().reduce(0, (sum, p) -> Math.toIntExact(sum + p.getPresent()), Integer::sum);
                ozonUpdateStockDO.setStock(reduce);
                ozonUpdateStockDO.setUpdateDate(new Date());
                esItemBulkProcessor.temUpdateWarehouseStock(ozonUpdateStockDO);
            } catch (Exception e) {
                log.error(String.format("产品%s更新本地仓库库存数据报错：%s", productId, e.getMessage()));
            }
        }
    }

    private List<FeedTask> getFeedsByAccountAndId(String accountNumber, List<String> associationIds, String taskType, String job) {
        FeedTaskExample feedTaskExample = new FeedTaskExample();
        FeedTaskExample.Criteria criteria = feedTaskExample.createCriteria();
        criteria.andAccountNumberEqualTo(accountNumber);
        criteria.andTaskStatusEqualTo(FeedTaskStatusEnum.RUNNING.getTaskStatus());
        criteria.andTaskTypeEqualTo(taskType);
        criteria.andAssociationIdIn(associationIds);
        if(StringUtils.isNotBlank(job)){
            criteria.andAttribute10EqualTo(job);
        }
        return feedTaskService.selectByExample(feedTaskExample, Platform.Ozon.name());
    }

    /**
     * 删除下降商品
     */
    public void deleteItems(String accountNumber, List<EsOzonItem> items) {
        List<List<EsOzonItem>> partition = Lists.partition(items, 100);
        for (List<EsOzonItem> itemList : partition) {
            feedTaskService.batchNewItemFeeds(itemList, OzonFeedTaskEnums.TaskType.DELETE_ITEM.name());
            List<Long> productIds = itemList.stream().map(EsOzonItem::getProductId).collect(Collectors.toList());
            ExecutorUtils.execute(OzonExecutors.DELETE_ITEM_POOL, () -> {
                ArchiveRequest archiveRequest = new ArchiveRequest();
                archiveRequest.setProductIds(productIds);
                OzonResponseResult<Boolean> ozonResponseResult = apiClient.productArchive(accountNumber, archiveRequest);
                List<CompletableFuture<Map<Long, OzonResponseResult<Boolean>>>> list = new ArrayList<>();
                Map<Long, OzonResponseResult<Boolean>> singleResultMap = new HashMap<>();
                if (!ozonResponseResult.isSuccess()) {
                    // 批量不成功，就单个单个执行
                    for (Long productId : productIds) {
                        CompletableFuture<Map<Long, OzonResponseResult<Boolean>>> mapCompletableFuture = CompletableFuture.supplyAsync(() -> {
                            ArchiveRequest singleArchiveRequest = new ArchiveRequest();
                            singleArchiveRequest.setProductIds(Collections.singletonList(productId));
                            OzonResponseResult<Boolean> booleanOzonResponseResult = apiClient.productArchive(accountNumber, singleArchiveRequest);
                            Map<Long, OzonResponseResult<Boolean>> singleResult = new HashMap<>();
                            singleResult.put(productId, booleanOzonResponseResult);
                            return singleResult;
                        }, OzonExecutors.DELETE_SINGLE_ITEM_POOL);
                        list.add(mapCompletableFuture);
                    }
                    List<Map<Long, OzonResponseResult<Boolean>>> collect = list.stream().map(CompletableFuture::join).collect(Collectors.toList());
                    singleResultMap = collect.stream().flatMap(m -> m.entrySet().stream()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (o1, o2) -> o1));
                }
                List<String> ids = productIds.stream().map(String::valueOf).collect(Collectors.toList());
                List<FeedTask> feedTasks = getFeedsByAccountAndId(accountNumber, ids, OzonFeedTaskEnums.TaskType.DELETE_ITEM.name(), null);
                if (!ozonResponseResult.isSuccess()) {
                    // 处理报告失败
                    List<Long> successProductIdList = new ArrayList<>();
                    for (FeedTask feedTask : feedTasks) {
                        String productId = feedTask.getAssociationId();
                        if (StringUtils.isBlank(productId)) {
                            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                            feedTask.setResultMsg("can not archive product result");
                            ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, feedTask.getResultMsg(), feedTask.getTaskType());
                            continue;
                        }

                        OzonResponseResult<Boolean> booleanOzonResponseResult = singleResultMap.get(Long.valueOf(productId));
                        if (booleanOzonResponseResult == null) {
                            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                            feedTask.setResultMsg("can not archive product result");
                            ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, feedTask.getResultMsg(), feedTask.getTaskType());
                            continue;
                        }

                        if (!booleanOzonResponseResult.isSuccess()) {
                            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                            feedTask.setResultMsg(JSON.toJSONString(booleanOzonResponseResult));
                            ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, feedTask.getResultMsg(), feedTask.getTaskType());
                            continue;
                        }

                        if (booleanOzonResponseResult.isSuccess()) {
                            feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                        }
                    }
                    feedTaskService.batchUpdateFeeds(feedTasks);
                    // 将状态设置为不在线和已归档
                    if (CollectionUtils.isNotEmpty(successProductIdList)) {
                        for (Long productId : successProductIdList) {
                            esItemBulkProcessor.setOnlineAndStatus(productId.toString(), false, List.of(OzonEnums.ListVisibility.ARCHIVED.getCode()));
                        }
                    }
                    return;
                }
                if (ozonResponseResult.getResult()) {
                    for (FeedTask feedTask : feedTasks) {
                        feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                    }
                    feedTaskService.batchUpdateFeeds(feedTasks);
                    for (Long productId : productIds) {
                        esItemBulkProcessor.setOnlineAndStatus(productId.toString(), false, List.of(OzonEnums.ListVisibility.ARCHIVED.getCode()));
                    }
                }
            }, "delete_item_pool");
        }
    }

    /**
     * 取消归档商品
     */
    public void unarchiveItems(String accountNumber, List<EsOzonItem> items) {
        List<List<EsOzonItem>> partition = Lists.partition(items, 100);
        for (List<EsOzonItem> itemList : partition) {
            feedTaskService.batchNewItemFeeds(itemList, OzonFeedTaskEnums.TaskType.UNARCHIVE_ITEM.name());
            List<Long> productIds = itemList.stream().map(EsOzonItem::getProductId).collect(Collectors.toList());
            ExecutorUtils.execute(OzonExecutors.UNARCHIVE_ITEM_POOL, () -> {
                ArchiveRequest unarchiveRequest = new ArchiveRequest();
                unarchiveRequest.setProductIds(productIds);
                OzonResponseResult<Boolean> ozonResponseResult = apiClient.productUnarchive(accountNumber, unarchiveRequest);
                List<CompletableFuture<Map<Long, OzonResponseResult<Boolean>>>> list = new ArrayList<>();
                Map<Long, OzonResponseResult<Boolean>> singleResultMap = new HashMap<>();
                if (!ozonResponseResult.isSuccess()) {
                    // 批量不成功，就单个单个执行
                    for (Long productId : productIds) {
                        CompletableFuture<Map<Long, OzonResponseResult<Boolean>>> mapCompletableFuture = CompletableFuture.supplyAsync(() -> {
                            ArchiveRequest singleUnarchiveRequest = new ArchiveRequest();
                            singleUnarchiveRequest.setProductIds(Collections.singletonList(productId));
                            OzonResponseResult<Boolean> booleanOzonResponseResult = apiClient.productUnarchive(accountNumber, singleUnarchiveRequest);
                            Map<Long, OzonResponseResult<Boolean>> singleResult = new HashMap<>();
                            singleResult.put(productId, booleanOzonResponseResult);
                            return singleResult;
                        }, OzonExecutors.UNARCHIVE_SINGLE_ITEM_POOL);
                        list.add(mapCompletableFuture);
                    }
                    List<Map<Long, OzonResponseResult<Boolean>>> collect = list.stream().map(CompletableFuture::join).collect(Collectors.toList());
                    singleResultMap = collect.stream().flatMap(m -> m.entrySet().stream()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (o1, o2) -> o1));
                }
                List<String> ids = productIds.stream().map(String::valueOf).collect(Collectors.toList());
                List<FeedTask> feedTasks = getFeedsByAccountAndId(accountNumber, ids, OzonFeedTaskEnums.TaskType.UNARCHIVE_ITEM.name(), null);
                if (!ozonResponseResult.isSuccess()) {
                    // 处理报告失败
                    List<Long> successProductIdList = new ArrayList<>();
                    for (FeedTask feedTask : feedTasks) {
                        String productId = feedTask.getAssociationId();
                        if (StringUtils.isBlank(productId)) {
                            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                            feedTask.setResultMsg("can not unarchive product result");
                            ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, feedTask.getResultMsg(), feedTask.getTaskType());
                            continue;
                        }

                        OzonResponseResult<Boolean> booleanOzonResponseResult = singleResultMap.get(Long.valueOf(productId));
                        if (booleanOzonResponseResult == null) {
                            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                            feedTask.setResultMsg("can not unarchive product result");
                            ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, feedTask.getResultMsg(), feedTask.getTaskType());
                            continue;
                        }

                        if (!booleanOzonResponseResult.isSuccess()) {
                            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                            feedTask.setResultMsg(JSON.toJSONString(booleanOzonResponseResult));
                            ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, feedTask.getResultMsg(), feedTask.getTaskType());
                            continue;
                        }

                        if (booleanOzonResponseResult.isSuccess()) {
                            feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                            successProductIdList.add(Long.valueOf(productId));
                        }
                    }
                    feedTaskService.batchUpdateFeeds(feedTasks);
                    // 将状态设置为在线和取消归档
                    if (CollectionUtils.isNotEmpty(successProductIdList)) {
                        OzonSyncItemDO syncItemDO = new OzonSyncItemDO();
                        syncItemDO.setProductIds(successProductIdList);
                        syncItemDO.setAccountNumber(accountNumber);
                        ozonEsItemService.syncProductInfo(syncItemDO);
                    }
                    return;
                }
                if (ozonResponseResult.getResult()) {
                    for (FeedTask feedTask : feedTasks) {
                        feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                    }
                    feedTaskService.batchUpdateFeeds(feedTasks);
                    OzonSyncItemDO syncItemDO = new OzonSyncItemDO();
                    syncItemDO.setProductIds(productIds);
                    syncItemDO.setAccountNumber(accountNumber);
                    ozonEsItemService.syncProductInfo(syncItemDO);
                }
            }, "unarchive_item_pool");
        }
    }

    /**
     * 修改商品描述
     * 缺少设置回调
     * @param username
     * @param account          账号
     * @param updateAttributes 属性
     */
    public void updateItemDesc(String username, String account, List<OzonEditItemDescVO> updateAttributes) {
        DataContextHolder.setUsername(username);
        List<List<OzonEditItemDescVO>> partition = Lists.partition(updateAttributes, 20);
        for (List<OzonEditItemDescVO> part : partition) {
            List<UpdateAttributesRequest> updateAttributesRequests = assemblyUpdateAttributesRequest(part);
            if (CollectionUtils.isEmpty(updateAttributesRequests)) {
                log.error("未查询到对应的数据");
            }
            List<Long> productIds = part.stream().map(OzonEditItemDescVO::getProductId).collect(Collectors.toList());

            List<String> ids = productIds.stream().map(String::valueOf).collect(Collectors.toList());
            List<FeedTask> feedTasks = getFeedsByAccountAndId(account, ids, OzonFeedTaskEnums.TaskType.UPDATE_DESC.name(), null);
            OzonResponseResult<String> responseResult = apiClient.updateAttributes(account, updateAttributesRequests);
            String msg = JSON.toJSONString(responseResult);
            FeedTaskResultStatusEnum statusEnum = responseResult.isSuccess() ? FeedTaskResultStatusEnum.SUCCESS : FeedTaskResultStatusEnum.FAIL;
            for (FeedTask feedTask : feedTasks) {
                feedTask.setResultStatus(statusEnum.getResultStatus());
                feedTask.setResultMsg(msg);
                feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                if (!responseResult.isSuccess()) {
                    ozonProblemClassificationHandler.handleFeedTaskProblemClassification(feedTask, feedTask.getResultMsg(), feedTask.getTaskType());
                }
            }
            feedTaskService.batchUpdateFeeds(feedTasks);
        }
    }

    private List<UpdateAttributesRequest> assemblyUpdateAttributesRequest(List<OzonEditItemDescVO> dataList) {
        List<Long> productIds = dataList.stream().map(OzonEditItemDescVO::getProductId).collect(Collectors.toList());
        EsOzonItemRequest request = new EsOzonItemRequest();
        request.setProductIds(productIds);
        request.setFields(OzonConstant.EDIT_PRODUCT_DESC_FILES);
        List<EsOzonItem> esOzonItems = esOzonItemService.listItemByRequest(request);
        if (CollectionUtils.isEmpty(esOzonItems)) {
            return null;
        }
        return esOzonItems.stream()
                .map(OzonEditItemDescVO::convent2VO)
                .map(editItemDescVO -> {
                    RichContent richContent = createRichContent(editItemDescVO);
                    if (richContent == null) {
                        return null;
                    }
                    // 处理报告
                    feedTaskService.newTask(editItemDescVO.getProductId(), editItemDescVO.getAccountNumber(), OzonFeedTaskEnums.TaskType.UPDATE_DESC.name(), editItemDescVO.getSku(), editItemDescVO.getSellerSku());
                    return createAttributesRequest(editItemDescVO, richContent);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

    }

    private UpdateAttributesRequest createAttributesRequest(OzonEditItemDescVO editItemDescVO, RichContent content) {
        if (CollectionUtils.isEmpty(editItemDescVO.getAttributes())) {
            return null;
        }
        List<EsOzonItemAttributes> esAttributes = editItemDescVO.getAttributes();

        AttributesDTO.ValuesDTO attributeValue = new AttributesDTO.ValuesDTO();
        attributeValue.setDictionaryValueId(0L);
        attributeValue.setValue(JSON.toJSONString(content));

        List<AttributesDTO> attributesDTOS = esAttributes.stream().map(attribute -> {
            AttributesDTO attributes = new AttributesDTO();
            attributes.setId(attribute.getAttributeId());
            attributes.setComplexId(attribute.getComplexId());
            List<AttributesDTO.ValuesDTO> valuesDTOS = BeanUtil.copyBeanList(attribute.getValues(), AttributesDTO.ValuesDTO.class);
            if (OzonEnums.AttributeCode.COLOR_SWATCH.isTrue(attribute.getAttributeId())) {
                AttributesDTO.ValuesDTO valuesDTO = valuesDTOS.get(0);
                if (editItemDescVO.getMainImage().equals(valuesDTO.getValue())) {
                    // 色卡图与主图相同则替换链接重新上传
                    Map<String, String> imageMapping = OzonImageUtils.getImageMapping(List.of(valuesDTO.getValue()));
                    String newUrl = imageMapping.get(valuesDTO.getValue());
                    valuesDTO.setValue(newUrl);
                }
            }
            attributes.setValues(valuesDTOS);
            return attributes;
        }).collect(Collectors.toList());

        AttributesDTO attributes = new AttributesDTO();
        attributes.setId(OzonEnums.AttributeCode.RICH_CONTENT.getCode());
        attributes.setComplexId(0L);
        attributes.setValues(Collections.singletonList(attributeValue));

        attributesDTOS.add(attributes);
        UpdateAttributesRequest request = new UpdateAttributesRequest();
        request.setAttributes(attributesDTOS);
        request.setOfferId(editItemDescVO.getSellerSku());
        return request;
    }

    private RichContent createRichContent(OzonEditItemDescVO editItemDescVO) {
        RichContent ozonRichContent = new RichContent();
        List<BaseRichContent<?>> contentList = new ArrayList<>();
        List<String> images = editItemDescVO.getImages();
        if (CollectionUtils.isNotEmpty(images)) {
            BaseRichContent<ImgBlocks> imageBlocks = RichContent.createImageBlocks(images);
            contentList.add(imageBlocks);
        }
        String mainImage = editItemDescVO.getMainImage();
        String description = editItemDescVO.getDescription();
        BaseRichContent<TxtImgBlocks> txtImageBlocks = RichContent.createTxtImageBlocks(mainImage, description);
        if (txtImageBlocks == null) {
            return null;
        }
        contentList.add(txtImageBlocks);
        ozonRichContent.setContent(contentList);
        ozonRichContent.setVersion(0.3);
        return ozonRichContent;
    }

    public void markAirFreight(List<String> idList) {
        for (String id : idList) {
            OzonUpdateLinkTagDO linkTagDO = new OzonUpdateLinkTagDO();
            linkTagDO.setId(id);
            linkTagDO.setLinkTag(OzonLinkTagEnum.EMBARGO_PRODUCTS.getCode());
            linkTagDO.setUpdateDate(new Date());

            esItemBulkProcessor.updateLinkTag(linkTagDO);
        }
    }

    public void deleteAirFreight(List<String> idList) {
        for (String id : idList) {
            OzonUpdateLinkTagDO linkTagDO = new OzonUpdateLinkTagDO();
            linkTagDO.setId(id);
            linkTagDO.setLinkTag(null);
            linkTagDO.setUpdateDate(new Date());
            esItemBulkProcessor.updateLinkTag(linkTagDO);
        }
    }

    public void updateItem(String accountNumber, List<OzonUpdateItemDO> ozonUpdateItemDOS, OzonFeedTaskEnums.TaskType taskType) {
        if (CollectionUtils.isEmpty(ozonUpdateItemDOS)) {
            return;
        }

        String userName = getUserName();

        List<List<OzonUpdateItemDO>> lists = PagingUtils.newPagingList(ozonUpdateItemDOS, 100);
        for (List<OzonUpdateItemDO> list : lists) {
            List<FeedTask> tasks = list.stream().map(OzonUpdateItemDO::getFeedTask).collect(Collectors.toList());
            // 额外处理报告
            List<FeedTask> tasks2 = list.stream().map(OzonUpdateItemDO::getFeedTask2).filter(Objects::nonNull).collect(Collectors.toList());
            tasks.addAll(tasks2);
            List<ExtendJsonDto> extendJsonDtoList = tasks.stream().filter(Objects::nonNull).map(a -> {
                ExtendJsonDto extendJsonDto = new ExtendJsonDto();
                extendJsonDto.setFeedTaskId(a.getId());
                extendJsonDto.setSellerSku(a.getAttribute1());
                extendJsonDto.setFeedTask(a.getTaskType());
                return extendJsonDto;
            }).collect(Collectors.toList());
            try {
                List<CreateProductRequest> requests = list.stream().map(OzonUpdateItemDO::getCreateProductRequest).collect(Collectors.toList());
                OzonResponseResult<CreateProductResponse> responseResult = OzonRetryUtil.doRetryOzonApi(() -> {
                    OzonResponseResult<CreateProductResponse> apiClientProduct = apiClient.createProduct(accountNumber, requests);
                    if (apiClientProduct.isSuccess()) {
                        return apiClientProduct;
                    }
                    throw new RuntimeException(JSON.toJSONString(apiClientProduct));
                }, 3);

                String message = JSON.toJSONString(responseResult);
                // 修改状态、记录处理报告 部分成功怎么处理
                if (responseResult.isSuccess()) {
                    CreateProductResponse result = responseResult.getResult();
                    insertPublishProcess(accountNumber, result.getTaskId(), extendJsonDtoList, taskType, userName);
                    for (FeedTask task : tasks) {
                        task.setFinishTime(new Timestamp(System.currentTimeMillis()));
                        // 结果状态不处理
//                        task.setResultStatus(ResultStatusEnum.RESULT_SUCCESS.getStatusCode());
                        task.setAttribute2(result.getTaskId().toString());
                    }
                    feedTaskService.batchUpdateTaskIdAndRunning(tasks);
                } else {
                    // 只有直接失败的才处理结果状态
                    feedTaskService.batchUpdateFeedTaskToFinish(tasks, FeedTaskStatusEnum.FINISH.getTaskStatus(),
                            ResultStatusEnum.RESULT_FAIL.getStatusCode(), message);
                }
            } catch (Exception e) {
                feedTaskService.batchUpdateFeedTaskToFinish(tasks, FeedTaskStatusEnum.FINISH.getTaskStatus(),
                        ResultStatusEnum.RESULT_FAIL.getStatusCode(), e.getMessage());
            }
        }
    }

    private void insertPublishProcess(String accountNumber, Long taskId, List<ExtendJsonDto> extendJsonDtos, OzonFeedTaskEnums.TaskType taskType, String username) {
        OzonPublishProcess publishProcess = new OzonPublishProcess();
        publishProcess.setStatus(OzonPublishProcessEnums.Status.UPLOAD_ITEM.getCode());
        publishProcess.setTaskId(taskId);
        publishProcess.setIsSuccess(true);
        publishProcess.setUser(username);
        publishProcess.setRemarks(null);
        publishProcess.setTaskType(taskType.name());
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        publishProcess.setCreatedTime(now);
        publishProcess.setUpdatedTime(now);
        publishProcess.setAccountnumber(accountNumber);
        publishProcess.setTemplateId(-1);
        publishProcess.setPublishRole(-1);
        publishProcess.setPublishType(-1);
        if (CollectionUtils.isNotEmpty(extendJsonDtos)) {
            publishProcess.setExtendJson(JSON.toJSONString(extendJsonDtos));
        }
        publishProcessService.insert(publishProcess);
    }

    public void updateAttribute(String accountNumber, List<UpdateAttributeDto> updateAttributeDtoList, OzonFeedTaskEnums.TaskType taskType) {
        if (CollectionUtils.isEmpty(updateAttributeDtoList)) {
            return;
        }
        String userName = getUserName();
        List<List<UpdateAttributeDto>> lists = PagingUtils.newPagingList(updateAttributeDtoList, 100);
        for (List<UpdateAttributeDto> list : lists) {
            List<FeedTask> tasks = list.stream().map(UpdateAttributeDto::getFeedTask).collect(Collectors.toList());
            // 额外处理报告
            List<ExtendJsonDto> extendJsonDtoList = tasks.stream().filter(Objects::nonNull).map(a -> {
                ExtendJsonDto extendJsonDto = new ExtendJsonDto();
                extendJsonDto.setFeedTaskId(a.getId());
                extendJsonDto.setFeedTask(a.getTaskType());
                extendJsonDto.setSellerSku(a.getAttribute1());
                return extendJsonDto;
            }).collect(Collectors.toList());
            try {
                List<UpdateAttributesRequest> collect = list.stream().map(UpdateAttributeDto::getUpdateAttributesRequests).collect(Collectors.toList());
                OzonResponseResult<String> responseResult = OzonRetryUtil.doRetryOzonApi(() -> {
                    OzonResponseResult<String> apiClientProduct = apiClient.updateAttributes(accountNumber, collect);
                    if (apiClientProduct.isSuccess()) {
                        return apiClientProduct;
                    }
                    throw new RuntimeException(JSON.toJSONString(apiClientProduct));
                }, 3);

                String message = JSON.toJSONString(responseResult);
                // 修改状态、记录处理报告 部分成功怎么处理
                if (responseResult.isSuccess()) {
                    Long taskId = responseResult.getTaskId();
                    insertPublishProcess(accountNumber, taskId, extendJsonDtoList, taskType, userName);
                    for (FeedTask task : tasks) {
                        task.setFinishTime(new Timestamp(System.currentTimeMillis()));
                        task.setAttribute2(taskId.toString());
                    }
                    feedTaskService.batchUpdateTaskIdAndRunning(tasks);
                } else {
                    // 只有直接失败的才处理结果状态
                    feedTaskService.batchUpdateFeedTaskToFinish(tasks, FeedTaskStatusEnum.FINISH.getTaskStatus(),
                            ResultStatusEnum.RESULT_FAIL.getStatusCode(), message);
                }
            } catch (Exception e) {
                feedTaskService.batchUpdateFeedTaskToFinish(tasks, FeedTaskStatusEnum.FINISH.getTaskStatus(),
                        ResultStatusEnum.RESULT_FAIL.getStatusCode(), e.getMessage());
            }
        }

    }

    public String getUserName() {
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        return org.apache.commons.lang.StringUtils.isBlank(currentUser) ? "admin" : currentUser;
    }

}
