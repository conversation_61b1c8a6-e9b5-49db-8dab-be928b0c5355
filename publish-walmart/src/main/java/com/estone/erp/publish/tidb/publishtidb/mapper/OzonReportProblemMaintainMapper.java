package com.estone.erp.publish.tidb.publishtidb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.estone.erp.publish.tidb.publishtidb.model.OzonReportProblemMaintain;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【ozon_report_problem_maintain】的数据库操作Mapper
* @createDate 2025-03-04 14:38:43
* @Entity com.estone.erp.publish.tidb.publishtidb.model.AliexpressReportProblemMaintain
*/
public interface OzonReportProblemMaintainMapper extends BaseMapper<OzonReportProblemMaintain> {

    OzonReportProblemMaintain selectSolutionByErrorMsg(@Param("errorMsg") String errorMsg, @Param("operationType") String operationType);
}




