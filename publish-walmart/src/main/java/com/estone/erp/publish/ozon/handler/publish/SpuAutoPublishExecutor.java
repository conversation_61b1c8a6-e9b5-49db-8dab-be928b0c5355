package com.estone.erp.publish.ozon.handler.publish;

import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.ozon.call.model.AttributesDTO;
import com.estone.erp.publish.ozon.common.cardcode.OzonCardCodeHelper;
import com.estone.erp.publish.ozon.enums.*;
import com.estone.erp.publish.ozon.handler.publish.parm.SpuPublishParam;
import com.estone.erp.publish.ozon.handler.template.OzonTemplateBuilderHandler;
import com.estone.erp.publish.ozon.model.*;
import com.estone.erp.publish.ozon.model.dto.BuilderTemplateDO;
import com.estone.erp.publish.ozon.model.dto.OzonCalcPriceResponse;
import com.estone.erp.publish.ozon.model.dto.base.ProhibitedInfo;
import com.estone.erp.publish.ozon.model.dto.online.OnlineConfigRuleJson;
import com.estone.erp.publish.ozon.model.dto.template.OzonSkuDO;
import com.estone.erp.publish.ozon.model.dto.template.OzonTemplateDO;
import com.estone.erp.publish.ozon.service.OzonAccountConfigService;
import com.estone.erp.publish.ozon.service.OzonAdminTemplateService;
import com.estone.erp.publish.ozon.service.OzonTimePublishQueueService;
import com.estone.erp.publish.ozon.utils.OzonAttributeUtil;
import com.estone.erp.publish.ozon.utils.OzonTemplateDataUtil;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.bean.forbidden.Sites;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEsRequest;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.system.product.util.SingleItemEsUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * spu自动刊登
 *
 * <AUTHOR>
 * @date 2023-07-12 11:57
 */
@Slf4j
@Component
public class SpuAutoPublishExecutor extends PublishExecutor<SpuPublishParam> {
    @Autowired
    private OzonAdminTemplateService adminTemplateService;
    @Autowired
    private OzonTemplateBuilderHandler ozonTemplateBuilderHandler;
    @Autowired
    private OzonAccountConfigService accountConfigService;
    @Autowired
    private OzonTimePublishQueueService timePublishQueueService;
    @Autowired
    private OzonCardCodeHelper cardCodeHelper;
    @Autowired
    private SingleItemEsService singleItemEsService;

    @Override
    protected OzonTemplateModel getTemplateData(SpuPublishParam param) throws BusinessException {
        try {
            DataContextHolder.setUsername(param.getUser());
            OzonTemplateModel templateModel = createTemplateWithSpu(param);
            param.setTemplateId(templateModel.getId());

            if (OzonPublishProcessEnums.PublishType.TIME_PUBLISH.isTrue(param.getPublishType())) {
                // 更新定时刊登信息
                Integer queueId = param.getTimePublishQueueId();
                OzonTimePublishQueue queue = new OzonTimePublishQueue();
                queue.setId(queueId);
                queue.setTemplateId(templateModel.getId());
                queue.setTitle(templateModel.getTitle());
                queue.setPublishStatus(templateModel.getPublishStatus());
                queue.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
                timePublishQueueService.updateByPrimaryKeySelective(queue);
            }
            return templateModel;
        } catch (Exception e) {
            failFeedTask(param.getTemplateId(), param.getAccountNumber(), param.getSpu(), e.getMessage(), "", param.getRuleName(), null, null);
            updateTimePublishQueueError(param, e.getMessage());
        } finally {
            try {
                templateValidation.deleteLock(param.getAccountNumber(), param.getSpu());
            } catch (Exception e) {
                log.error("删除锁失败" + JSON.toJSONString(param));
            }
        }
        return null;
    }

    private void updateTimePublishQueueError(SpuPublishParam param, String message) {
        if (OzonPublishProcessEnums.PublishType.TIME_PUBLISH.isTrue(param.getPublishType())) {
            // 更新定时刊登信息
            Integer queueId = param.getTimePublishQueueId();
            OzonTimePublishQueue queue = new OzonTimePublishQueue();
            queue.setId(queueId);
            queue.setStatus(OzonTimePublishEnums.Status.END.getCode());
            queue.setPublishStatus(OzonTemplateEnums.PublishStatus.ERROR.getCode());
            queue.setExtra(message);
            queue.setUpdateTime(Timestamp.valueOf(LocalDateTime.now()));
            timePublishQueueService.updateByPrimaryKeySelective(queue);
        }
    }

    private OzonTemplateModel createTemplateWithSpu(SpuPublishParam param) throws RuntimeException {
        // 生成模板数据
        BuilderTemplateDO build = BuilderTemplateDO.builder()
                .accountNumber(param.getAccountNumber())
                .skuDataSource(param.getSkuDataSource())
                .categoryId(param.getCategoryId())
                .articleNumber(param.getSpu()).build();
        OzonTemplateDO ozonTemplateDO = ozonTemplateBuilderHandler.builderTemplate(build);
        if (ozonTemplateDO.getCategoryId() == null) {
            throw new IllegalArgumentException("无分类信息");
        }
        List<OzonSkuDO> variantList = ozonTemplateDO.getVariantList();

        // 重复刊登校验
        List<String> articleNumbers = variantList.stream().map(OzonSkuDO::getSku).collect(Collectors.toList());
        articleNumbers.add(ozonTemplateDO.getArticleNumber());
        templateValidation.checkLock(param.getAccountNumber(), param.getSpu());
        templateValidation.validateRepeatPublish(param.getAccountNumber(), articleNumbers, null);
        // 店铺配置
        OzonAccountConfig accountConfig = accountConfigService.selectConfigWithCalePriceRule(param.getAccountNumber());
        if (accountConfig == null) {
            throw new IllegalArgumentException("无店铺配置");
        }
        // 算价
        List<OzonCalcPriceResponse> ozonCalcPriceResponses = OzonTemplateDataUtil.calcPrice(variantList, null, accountConfig);
        Map<String, OzonCalcPriceResponse> calcPriceResponseMap = ozonCalcPriceResponses.stream().collect(Collectors.toMap(OzonCalcPriceResponse::getBusinessId, Function.identity(), (o1, o2) -> o1));

        List<OzonSkuDO> publishSkuDataList = assemblySkuData(variantList, accountConfig, calcPriceResponseMap);
        if (CollectionUtils.isEmpty(publishSkuDataList)) {
            throw new IllegalArgumentException("无可刊登数据");
        }
        ozonTemplateDO.setVariantList(publishSkuDataList);
        if (StringUtils.isNotBlank(param.getRuleJson())
                && StringUtils.isNotBlank(param.getRuleName())
                && StringUtils.isNotBlank(param.getConfigSource())
                && param.getConfigSource().equalsIgnoreCase(QueueConfigSourceEnum.ONLINE_CONFIG.getCode())) {
            // 只有是上架配置，才需要再次过滤
            List<OzonSkuDO> ozonSkuDos = filterPublishConfigSku(ozonTemplateDO.getVariantList(), param);
            ozonTemplateDO.setVariantList(ozonSkuDos);
        }

        ozonTemplateDO.setCurrencyCode(accountConfig.getCurrency());
        ozonTemplateDO.setAccountNumber(accountConfig.getAccountNumber());
        ozonTemplateDO.setCurrencyCode(accountConfig.getCurrency());
        // 新建模板
        OzonTemplateModel ozonTemplateModel = BeanUtil.copyProperties(ozonTemplateDO, OzonTemplateModel.class);
        ozonTemplateModel.setVariantData(JSON.toJSONString(ozonTemplateDO.getVariantList()));
        // Set skuSize based on variantList size
        if (ozonTemplateDO.getVariantList() != null) {
            ozonTemplateModel.setSkuSize(ozonTemplateDO.getVariantList().size());
        }
        // 每日刊登限制拦截
        templateValidation.validateDailyPublishLimit(ozonTemplateModel);
        ozonTemplateModel.setMainImg(ozonTemplateDO.getMainImage());
        ozonTemplateModel.setInventoryUpload(OzonTemplateEnums.InventoryStatus.NO_UPLOAD.getCode());
        ozonTemplateModel.setCreatedBy(param.getUser());
        ozonTemplateModel.setUpdatedBy(param.getUser());
        ozonTemplateModel.setCreatedTime(Timestamp.valueOf(LocalDateTime.now()));
        ozonTemplateModel.setUpdatedTime(Timestamp.valueOf(LocalDateTime.now()));
        ozonTemplateModel.setPublishType(OzonTemplateEnums.PublishType.AUTO_PUBLISH.getCode());
        ozonTemplateModel.setPublishRole(OzonTemplateEnums.PublishRole.SALE_MAN.getCode());
        ozonTemplateModel.setPublishStatus(OzonTemplateEnums.PublishStatus.PUBLISHING.getCode());
        // 侵权词过滤
        templateValidation.filterInfringementWord(ozonTemplateModel);
        // 翻译
        String translateTitle = BooleanUtils.isTrue(ozonTemplateDO.getNeedTranslate()) ? OzonTemplateDataUtil.translate(ozonTemplateModel.getTitle()) : ozonTemplateModel.getTitle();
        String removedTitleDuplicateWord = OzonTemplateDataUtil.removeTitleDuplicateWord(translateTitle);
        String translateDescription = BooleanUtils.isTrue(ozonTemplateDO.getNeedTranslate()) ? OzonTemplateDataUtil.translate(ozonTemplateModel.getDescription()) : ozonTemplateModel.getDescription();

        // 处理属性的关键字，进行翻译
        String categoryAttribute = ozonTemplateModel.getCategoryAttribute();
        if (StringUtils.isNotBlank(categoryAttribute)) {
            if (BooleanUtils.isTrue(ozonTemplateDO.getNeedTranslate())) {
                List<AttributesDTO> categoryAttributes = JSON.parseArray(categoryAttribute, AttributesDTO.class);
                List<AttributesDTO> collect = categoryAttributes.stream().filter(a -> a.getId().equals(OzonAttributeUtil.KEYWORDS_ID)).collect(Collectors.toList());
                // 关键字，翻译
                for (AttributesDTO attributesDTO : collect) {
                    List<AttributesDTO.ValuesDTO> values = attributesDTO.getValues();
                    for (AttributesDTO.ValuesDTO value : values) {
                        String desc = value.getValue();
                        if (StringUtils.isNotBlank(desc)) {
                            desc = OzonTemplateDataUtil.translate(desc);
                        }
                        value.setValue(desc);
                    }
                }
                String jsonString = JSON.toJSONString(categoryAttributes);
                ozonTemplateModel.setCategoryAttribute(jsonString);
            }
        }

        ozonTemplateModel.setTitle(removedTitleDuplicateWord);
        ozonTemplateModel.setDescription(translateDescription);
        ozonTemplateModel.setNeedTranslate(false);
        ozonTemplateModelService.insert(ozonTemplateModel);

        param.setTemplateId(ozonTemplateModel.getId());

        return ozonTemplateModel;
    }

    /**
     * 校验规则
     *
     * @param publishSkuDataList sku
     * @param param 参数
     * @return 过滤后的sku
     */
    private List<OzonSkuDO> filterPublishConfigSku(List<OzonSkuDO> publishSkuDataList, SpuPublishParam param) {
        if (CollectionUtils.isEmpty(publishSkuDataList)) {
            return publishSkuDataList;
        }
        String ruleJson = param.getRuleJson();
        OnlineConfigRuleJson onlineConfigRuleJson = JSON.parseObject(ruleJson, OnlineConfigRuleJson.class);
        List<String> skuList = publishSkuDataList.stream().map(OzonSkuDO::getSku).collect(Collectors.toList());
        Map<String, ProductInfo> productInfoMap = getProductInfoMap(skuList);
        List<OzonSkuDO> ozonSkuDos = new ArrayList<>();
        Map<String, String> skuAndErrorMap = new HashMap<>();
        for (OzonSkuDO ozonSkuDO : publishSkuDataList) {
            ProductInfo productInfo = productInfoMap.get(ozonSkuDO.getSku());
            if (productInfo == null) {
                skuAndErrorMap.put(ozonSkuDO.getSku(), "未查询到产品系统基础信息");
                continue;
            }
            // 过滤规则
            // 过滤产品标签
            List<String> productTags = onlineConfigRuleJson.getProductTags();
            if (CollectionUtils.isNotEmpty(productTags)) {
                String tagCode = Optional.ofNullable(productInfo.getEnTag()).orElse("");
                List<String> tagCodeList = CommonUtils.splitList(tagCode, ",");
                List<String> codes = tagCodeList.stream().filter(productTags::contains).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(codes)) {
                    skuAndErrorMap.put(ozonSkuDO.getSku(), String.format("产品标签不匹配，规则产品标签：%s，产品标签：%s", productTags, tagCodeList));
                    continue;
                }
            }

            // 过滤单品状态
            List<String> itemStatus = onlineConfigRuleJson.getItemStatus();
            if (CollectionUtils.isNotEmpty(itemStatus)) {
                String enNameByCode = productInfo.getItemStatus();
                if (StringUtils.isBlank(enNameByCode)) {
                    skuAndErrorMap.put(ozonSkuDO.getSku(), "单品状态枚举查不到");
                    continue;
                }
                if (!itemStatus.contains(enNameByCode)) {
                    skuAndErrorMap.put(ozonSkuDO.getSku(), String.format("单品状态不匹配，规则单品状态：%s，单品状态：%s", itemStatus, enNameByCode));
                    continue;
                }
            }

            // 过滤重量
            if (onlineConfigRuleJson.getWeightStart() != null || onlineConfigRuleJson.getWeightEnd() != null) {
                // 重量=净重 + 包材 + 搭配包材+面单3g
                double productWeight = Optional.ofNullable(productInfo.getProductWeight()).orElse(0D);
                //包材重量
                double packingWeight = Optional.ofNullable(productInfo.getPackingWeight()).orElse(BigDecimal.ZERO).doubleValue();
                //搭配包材重量
                double matchWeight = Optional.ofNullable(productInfo.getMatchWeight()).orElse(BigDecimal.ZERO).doubleValue();
                //总重量
                double totalWeight = productWeight + packingWeight + matchWeight + 3.0;
                if (onlineConfigRuleJson.getWeightStart() != null && totalWeight < onlineConfigRuleJson.getWeightStart()) {
                    skuAndErrorMap.put(ozonSkuDO.getSku(), String.format("重量不匹配，规则起始重量：%s，产品重量：%s", onlineConfigRuleJson.getWeightStart(), totalWeight));
                    continue;
                }
                if (onlineConfigRuleJson.getWeightEnd() != null && totalWeight > onlineConfigRuleJson.getWeightEnd()) {
                    skuAndErrorMap.put(ozonSkuDO.getSku(), String.format("重量不匹配，规则结束重量：%s，产品重量：%s", onlineConfigRuleJson.getWeightEnd(), totalWeight));
                    continue;
                }
            }

            // 检查季节性商品
            List<String> seasonProduct = onlineConfigRuleJson.getSeasonProduct();
            if (CollectionUtils.isNotEmpty(seasonProduct)) {
                String isSeasonNew = productInfo.getIsSeasonNew();
                if (StringUtils.isBlank(isSeasonNew)) {
                    skuAndErrorMap.put(ozonSkuDO.getSku(), String.format("季节性商品不匹配，规则季节性商品：%s，季节性商品为空", seasonProduct));
                    continue;
                }

                List<String> seasonNewCodeList = CommonUtils.splitList(isSeasonNew, ",");
                List<String> codes = seasonNewCodeList.stream().filter(seasonProduct::contains).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(codes)) {
                    skuAndErrorMap.put(ozonSkuDO.getSku(), String.format("季节性商品不匹配，规则季节性商品：%s，季节性商品：%s", seasonProduct, seasonNewCodeList));
                    continue;
                }
            }

            // 过滤禁售信息
            List<ProhibitedInfo> configJsonProhibitedInfo = onlineConfigRuleJson.getProhibitedInfo();
            if (CollectionUtils.isNotEmpty(configJsonProhibitedInfo)) {
                List<ProhibitedInfo> prohibitedInfoList = configJsonProhibitedInfo.stream()
                        .filter(prohibitedInfo -> StringUtils.isNotBlank(prohibitedInfo.getPlatform()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(prohibitedInfoList)) {
                    String salesProhibition = productInfo.getSalesProhibition();
                    if (!StringUtils.isBlank(salesProhibition) && !"[]".equals(salesProhibition)) {
                        List<SalesProhibitionsVo> prohibitionsVoList = JSON.parseObject(salesProhibition, new TypeReference<List<SalesProhibitionsVo>>() {
                        });
                        Map<String, List<Sites>> prohibitionsVoMap = prohibitionsVoList.stream()
                                .collect(Collectors.toMap(SalesProhibitionsVo::getPlat, SalesProhibitionsVo::getSites));
                        boolean anyMatch = prohibitedInfoList.stream().anyMatch(prohibitedInfo -> {
                            if (!prohibitionsVoMap.containsKey(prohibitedInfo.getPlatform())) {
                                return false;
                            }
                            List<Sites> sitesList = prohibitionsVoMap.get(prohibitedInfo.getPlatform());
                            if (CollectionUtils.isEmpty(sitesList)) {
                                sitesList = Optional.ofNullable(sitesList).orElseGet(ArrayList::new);
                            }
                            List<String> validSites = sitesList.stream()
                                    .map(Sites::getSite)
                                    .filter(StringUtils::isNotBlank)
                                    .collect(Collectors.toList());
                            List<String> prohibitedSites = prohibitedInfo.getSiteList();
                            return CollectionUtils.isEmpty(prohibitedSites) ||
                                    prohibitedSites.stream().anyMatch(validSites::contains);
                        });
                        if (anyMatch) {
                            skuAndErrorMap.put(ozonSkuDO.getSku(),  String.format("禁售信息不匹配，规则禁售信息：%s，产品禁售信息：%s", JSON.toJSONString(prohibitedInfoList), JSON.toJSONString(prohibitionsVoMap)));
                            continue;
                        }
                    }
                }
            }
            ozonSkuDos.add(ozonSkuDO);
        }
        if (CollectionUtils.isEmpty(ozonSkuDos)) {
            throw new RuntimeException("无可刊登数据,原因:" + JSON.toJSONString(skuAndErrorMap));
        }
        return ozonSkuDos;
    }

    private Map<String, ProductInfo> getProductInfoMap(List<String> skuList) {
        SingleItemEsRequest singleItemEsRequest = new SingleItemEsRequest();
        singleItemEsRequest.setSkuList(skuList);
        List<SingleItemEs> singleItemEsList = singleItemEsService.getSingleItemEsList(singleItemEsRequest);
        List<SingleItemEs> singleItems = singleItemEsList.stream().filter(e -> skuList.contains(e.getSonSku()))
                .collect(Collectors.toList());
        //转成ProductInfo,包含相关重量信息
        List<ProductInfo> productInfos = SingleItemEsUtils.tranSingleItemEsToProductInfo(singleItems);
        if (productInfos == null) {
            return Map.of();
        }
        return productInfos.stream()
                .collect(Collectors.toMap(
                        ProductInfo::getSonSku,
                        Function.identity()
                ));
    }

    @Override
    protected OzonPublishProcess initPublishProcess(SpuPublishParam param) throws BusinessException {
        OzonPublishProcess publishProcess = new OzonPublishProcess();
        publishProcess.setTemplateId(param.getTemplateId() == null ? 0 : param.getTemplateId());
        publishProcess.setStatus(OzonPublishProcessEnums.Status.INIT_TEMPLATE.getCode());
        publishProcess.setTaskId(null);
        publishProcess.setIsSuccess(true);
        publishProcess.setPublishType(param.getPublishType());
        publishProcess.setPublishRole(OzonTemplateEnums.PublishRole.SALE_MAN.getCode());
        publishProcess.setUser(param.getUser());
        publishProcess.setRemarks(null);
        publishProcess.setTaskType(OzonFeedTaskEnums.TaskType.PUBLISH.name());
        Timestamp now = Timestamp.valueOf(LocalDateTime.now());
        publishProcess.setCreatedTime(now);
        publishProcess.setUpdatedTime(now);
        publishProcessService.insert(publishProcess);
        return publishProcess;
    }

    private List<OzonSkuDO> assemblySkuData(List<OzonSkuDO> variantList, OzonAdminTemplate adminTemplate, OzonAccountConfig accountConfig, Map<String, OzonCalcPriceResponse> calcPriceMap) {
        List<OzonSkuDO> adminSkus = JSON.parseArray(adminTemplate.getVariantData(), OzonSkuDO.class);
        Map<String, OzonSkuDO> adminSkuMap = adminSkus.stream().collect(Collectors.toMap(OzonSkuDO::getSku, Function.identity(), (o1, o2) -> o1));

        List<OzonSkuDO> publishSkuDataList = new ArrayList<>();
        List<String> cardCodes = new ArrayList<>();
        if (StringUtils.isNotBlank(accountConfig.getEanPrefix())) {
            List<String> eanCardCodes = cardCodeHelper.generateEANCardCodes(accountConfig.getEanPrefix(), variantList.size());
            cardCodes.addAll(eanCardCodes);
        }
        for (int i = 0; i < variantList.size(); i++) {
            OzonSkuDO ozonSkuDO = variantList.get(i);
            OzonSkuDO adminSku = adminSkuMap.get(ozonSkuDO.getSku());
            if (adminSku == null) {
                continue;
            }
            // 取Admin范本属性值数据
            ozonSkuDO.setVariantAttribute(adminSku.getVariantAttribute());
            ozonSkuDO.setBarcode(adminSku.getBarcode());
            ozonSkuDO.setWeight(adminSku.getWeight());
            ozonSkuDO.setDepth(adminSku.getDepth());
            ozonSkuDO.setWidth(adminSku.getWidth());
            ozonSkuDO.setHeight(adminSku.getHeight());
            if (CollectionUtils.isEmpty(ozonSkuDO.getImages())) {
                ozonSkuDO.setImages(adminSku.getImages());
            }
            if (StringUtils.isBlank(ozonSkuDO.getMainImage())) {
                ozonSkuDO.setMainImage(adminSku.getMainImage());
            }
            // 重新设置 seller_sku, 仓库, 价格, EAN
            ozonSkuDO.setSellerSku(ozonSkuDO.getSku() + "_" + accountConfig.getSkuSuffix());
            ozonSkuDO.setWarehouseId(accountConfig.getUpdateStockWarehouseId());
            ozonSkuDO.setQuantity(accountConfig.getDefaultStock());
            OzonCalcPriceResponse ozonCalcPriceResponse = calcPriceMap.get(ozonSkuDO.getSku());
            if (ozonCalcPriceResponse == null) {
                continue;
            }
            ozonSkuDO.setSalePrice(ozonCalcPriceResponse.getSalePrice());
            ozonSkuDO.setPrice(ozonCalcPriceResponse.getPrice());
            if (cardCodes.size() - 1 >= i) {
                String cardCode = cardCodes.get(i);
                ozonSkuDO.setBarcode(cardCode);
            }
            publishSkuDataList.add(ozonSkuDO);
        }
        return publishSkuDataList;
    }

    private List<OzonSkuDO> assemblySkuData(List<OzonSkuDO> variantList, OzonAccountConfig accountConfig, Map<String, OzonCalcPriceResponse> calcPriceMap) {
        List<OzonSkuDO> publishSkuDataList = new ArrayList<>();
        List<String> cardCodes = new ArrayList<>();
        if (StringUtils.isNotBlank(accountConfig.getEanPrefix())) {
            List<String> eanCardCodes = cardCodeHelper.generateEANCardCodes(accountConfig.getEanPrefix(), variantList.size());
            cardCodes.addAll(eanCardCodes);
        }
        for (int i = 0; i < variantList.size(); i++) {
            OzonSkuDO ozonSkuDO = variantList.get(i);
            // 重新设置 seller_sku, 仓库, 价格, EAN
            ozonSkuDO.setSellerSku(ozonSkuDO.getSku() + "_" + accountConfig.getSkuSuffix());
            ozonSkuDO.setWarehouseId(accountConfig.getUpdateStockWarehouseId());
            ozonSkuDO.setQuantity(accountConfig.getDefaultStock());
            OzonCalcPriceResponse ozonCalcPriceResponse = calcPriceMap.get(ozonSkuDO.getSku());
            if (ozonCalcPriceResponse == null) {
                continue;
            }
            Boolean isSuccess = ozonCalcPriceResponse.getIsSuccess();
            if (BooleanUtils.isNotTrue(isSuccess)) {
                continue;
            }
            ozonSkuDO.setSalePrice(ozonCalcPriceResponse.getSalePrice());
            ozonSkuDO.setPrice(ozonCalcPriceResponse.getPrice());
            if (cardCodes.size() - 1 >= i) {
                String cardCode = cardCodes.get(i);
                ozonSkuDO.setBarcode(cardCode);
            }
            publishSkuDataList.add(ozonSkuDO);
        }
        return publishSkuDataList;
    }
}
