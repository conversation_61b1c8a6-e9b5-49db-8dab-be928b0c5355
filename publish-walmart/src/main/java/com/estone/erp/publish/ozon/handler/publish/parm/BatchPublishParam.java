package com.estone.erp.publish.ozon.handler.publish.parm;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-07-12 11:58
 */
@Data
public class BatchPublishParam implements BaseParam{

    private String user;

    private Integer type;

    private Integer role;

    /**
     * 模板Id
     */
    private Integer templateId;

    /**
     * 店铺
     */
    private String accountNumber;

    /**
     * 标题
     */
    private String title;

    /**
     * 毛利率
     */
    private Double grossProfitRate;

    @Override
    public String getRuleName() {
        return null;
    }

    @Override
    public Integer getTimePublishQueueId() {
        return null;
    }

    @Override
    public Integer getRowIndex() {
        return null;
    }

    @Override
    public Integer getExcelId() {
        return null;
    }
}
