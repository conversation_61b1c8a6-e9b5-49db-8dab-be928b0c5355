package com.estone.erp.publish.ozon.mq.listener;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.ozon.common.OzonExecutors;
import com.estone.erp.publish.ozon.enums.OzonFeedTaskEnums;
import com.estone.erp.publish.ozon.handler.OzonSyncListingHandler;
import com.estone.erp.publish.ozon.model.dto.sync.OzonItemDO;
import com.estone.erp.publish.ozon.model.dto.sync.OzonSyncListingMessageDO;
import com.estone.erp.publish.ozon.service.OzonFeedTaskService;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.google.common.collect.Lists;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * Ozon同步listing
 *
 * <AUTHOR>
 * @date 2024-04-24 上午11:58
 */
@Slf4j
@Component
public class OzonSyncAccountArchivedListingMQListener implements ChannelAwareMessageListener {

    @Autowired
    private OzonFeedTaskService ozonFeedTaskService;
    @Autowired
    private OzonSyncListingHandler syncListingHandler;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            OzonSyncListingMessageDO messageDO = JSON.parseObject(body, OzonSyncListingMessageDO.class);
            syncAccountListing(messageDO);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            log.error("OZON_SYNC_ACCOUNT_ARCHIVED_LISTING_QUEUE 消费异常, message: {}", body, e);
        }
    }

    private void syncAccountListing(OzonSyncListingMessageDO messageDO) {
        StopWatch started = StopWatch.createStarted();
        String accountNumber = messageDO.getAccountNumber();
        Long feedTaskId = messageDO.getFeedTaskId();
        FeedTask feedTask = new FeedTask();
        feedTask.setId(feedTaskId);
        feedTask.setTaskType(OzonFeedTaskEnums.TaskType.SYNC_ITEM.name());
        ozonFeedTaskService.updateTaskStatus(feedTask, FeedTaskStatusEnum.RUNNING.getTaskStatus());

        ApiResult<Map<Long, List<String>>> apiResult = syncListingHandler.loadAllSyncProductIds(accountNumber, true);
        if (!apiResult.isSuccess() || MapUtils.isEmpty(apiResult.getResult())) {
            if (MapUtils.isEmpty(apiResult.getResult())) {
                apiResult.setErrorMsg(apiResult.getErrorMsg());
            }
            ozonFeedTaskService.failTask(feedTask, "获取所有同步归档商品ID失败, 错误原因: " + apiResult.getErrorMsg());
            return;
        }
        Map<Long, List<String>> productStateMap = apiResult.getResult();
        List<Long> productIdList = new ArrayList<>(productStateMap.keySet());
        // 同步归档商品
        syncArchivedProduct(productIdList, productStateMap, accountNumber);
        log.info("同步归档商品详情完成, 店铺: {}, 数量: {}, 耗时：{} ms", accountNumber, productIdList.size(), started.getTime(TimeUnit.MILLISECONDS));
        ozonFeedTaskService.succeedTask(feedTask,  "已同步归档产品:" + productIdList.size());
    }
    /**
     * 同步商品详情
     * @param accountNumber
     * @param productIdList
     * @param productStateMap
     * @return
     */
    private int executeSyncProductInfo(String accountNumber, List<Long> productIdList, Map<Long, List<String>> productStateMap, Executor taskExecutor, Executor stockExecutor) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return 0;
        }
        // 分批同步详情
        AtomicInteger count = new AtomicInteger();
        List<List<Long>> partition = Lists.partition(productIdList, 500);
        List<CompletableFuture<List<OzonItemDO>>> completableFutures = partition.stream()
                .map(partitionProductIdList -> CompletableFuture.supplyAsync(() -> {
                    try {
                        // 获取商品详情
                        return syncListingHandler.batchSyncAndSaveItemInfo(accountNumber, partitionProductIdList, productStateMap);
                    } catch (Exception e) {
                        log.error("提交同步商品详细任务 exception:", e);
                        return null;
                    }
                }, taskExecutor))
                .collect(Collectors.toList());
        List<List<OzonItemDO>> itemList = completableFutures.stream().map(CompletableFuture::join).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemList)) {
            return 0;
        }
        itemList.forEach(items -> {
            count.addAndGet(items.size());
            // 同步仓库库存
            List<Long> skuIds = items.stream()
                    .map(OzonItemDO::getOzonSku)
                    .filter(Objects::nonNull)
                    .filter(skuId -> skuId > 0)
                    .collect(Collectors.toList());

            List<Long> productIds = items.stream().map(OzonItemDO::getProductId).collect(Collectors.toList());
            stockExecutor.execute(() -> {
                // 同步库存
                syncListingHandler.batchSyncWarehouseStockInfo(skuIds, accountNumber);
                // 同步属性
                syncListingHandler.syncAttributes(productIds, accountNumber);
            });
        });
        return count.get();
    }

    /**
     * 同步新品
     */
    private void syncArchivedProduct(List<Long> productIdList, Map<Long, List<String>> productStateMap, String accountNumber) {
        StopWatch started = StopWatch.createStarted();
        int executeTotal = executeSyncProductInfo(accountNumber, productIdList, productStateMap, OzonExecutors.SYNC_ACCOUNT_ARCHIVED_LISTING_INFO, OzonExecutors.SYNC_ACCOUNT_ARCHIVED_LISTING_INFO);
        log.info("同步归档详情, 店铺: {}, 数量: {}, 已完成: {}, 耗时：{} ms", accountNumber, productIdList.size(), executeTotal, started.getTime(TimeUnit.MILLISECONDS));
    }

}
