package com.estone.erp.publish.tiktok.call.promotion.domain;

public interface TiktokActivitiesEnums {

    /**
     * Activity status enum
     */
    enum Status {
        /**
         * Promotion activities with this status are not available to TikTok users.
         */
        DRAFT("DRAFT"),

        /**
         * Promotion activities with this status are not available to TikTok users until the set activity start time.
         */
        NOT_START("NOT_START"),

        /**
         * Promotion activities with this status are available to TikTok users.
         */
        ONGOING("ONGOING"),

        /**
         * Promotion activities with this status are not available to TikTok users because it has expired.
         */
        EXPIRED("EXPIRED"),

        /**
         * The activity has been deactivated by the seller and is not available to TikTok users.
         */
        DEACTIVATED("DEACTIVATED"),

        /**
         * The activity is terminated by the platform and is not available to TikTok users.
         */
        NOT_EFFECTIVE("NOT_EFFECTIVE");

        private final String value;

        Status(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static Status fromValue(String value) {
            for (Status status : Status.values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown status value: " + value);
        }
    }

}
