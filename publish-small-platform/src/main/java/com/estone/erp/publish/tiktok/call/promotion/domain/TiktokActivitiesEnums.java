package com.estone.erp.publish.tiktok.call.promotion.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface TiktokActivitiesEnums {

    /**
     * Activity status enum
     * 活动状态枚举
     */
    @Getter
    @AllArgsConstructor
    enum Status {
        /**
         * Promotion activities with this status are not available to TikTok users.
         * 草稿状态，此状态下的促销活动对TikTok用户不可见
         */
        DRAFT("DRAFT", "草稿"),

        /**
         * Promotion activities with this status are not available to TikTok users until the set activity start time.
         * 未开始状态，此状态下的促销活动在设定的活动开始时间之前对TikTok用户不可见
         */
        NOT_START("NOT_START", "未开始"),

        /**
         * Promotion activities with this status are available to TikTok users.
         * 进行中状态，此状态下的促销活动对TikTok用户可见
         */
        ONGOING("ONGOING", "进行中"),

        /**
         * Promotion activities with this status are not available to TikTok users because it has expired.
         * 已过期状态，此状态下的促销活动因为已过期对TikTok用户不可见
         */
        EXPIRED("EXPIRED", "已过期"),

        /**
         * The activity has been deactivated by the seller and is not available to TikTok users.
         * 已停用状态，活动已被卖家停用，对TikTok用户不可见
         */
        DEACTIVATED("DEACTIVATED", "已停用"),

        /**
         * The activity is terminated by the platform and is not available to TikTok users.
         * 无效状态，活动被平台终止，对TikTok用户不可见
         */
        NOT_EFFECTIVE("NOT_EFFECTIVE", "无效");

        private final String value;
        private final String description;

        public static Status fromValue(String value) {
            for (Status status : Status.values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown status value: " + value);
        }
    }


}
