package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeItemService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.api.v2.param.bundledeal.ShopeeDeleteBundleDealV2;
import com.estone.erp.publish.shopee.api.v2.param.bundledeal.ShopeeDeleteBundleDealtemV2;
import com.estone.erp.publish.shopee.api.v2.param.bundledeal.ShopeeEndBundleDealtemV2;
import com.estone.erp.publish.shopee.call.v2.ShopeeBundelDealCallV2;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskEnum;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingBundleDealStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingBundleDealSuitTypeEnum;
import com.estone.erp.publish.shopee.handler.ShopeeBundleDealHandler;
import com.estone.erp.publish.shopee.model.ShopeeMarketingBundleDeal;
import com.estone.erp.publish.shopee.model.ShopeeMarketingBundleDealExample;
import com.estone.erp.publish.shopee.model.ShopeeMarketingConfig;
import com.estone.erp.publish.shopee.mq.model.ShopeeAddComposeBundleDealItemMessage;
import com.estone.erp.publish.shopee.service.ShopeeMarketingBundleDealService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingConfigService;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListing;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListingExample;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeBundleDealProductListingService;
import com.google.common.collect.Lists;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * SHOPEE_ADD_COMPOSE_BUNDLE_DEAL_ITEM_QUEUE
 *
 * @Description: 添加组合优惠套装商品到优惠套装队列监听器
 * <AUTHOR>
 * @Date 2024/9/20 下午3:23
 */
@Slf4j
public class ShopeeAddComposeBundleDealItemMqListener implements ChannelAwareMessageListener {

    @Resource
    private EsShopeeItemService esShopeeItemService;
    @Resource
    private ShopeeMarketingConfigService shopeeMarketingConfigService;

    @Autowired
    private ShopeeBundleDealProductListingService shopeeBundleDealProductListingService;

    @Autowired
    private ShopeeMarketingBundleDealService shopeeMarketingBundleDealService;
    @Autowired
    private ShopeeBundleDealHandler shopeeBundleDealHandler;


    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            log.info("接收到添加组合优惠套装商品到优惠套装队列消息：{}", body);
            ShopeeAddComposeBundleDealItemMessage taskMessage = JSON.parseObject(body, ShopeeAddComposeBundleDealItemMessage.class);
            Integer configId = taskMessage.getConfigId();
            String accountNumber = taskMessage.getAccountNumber();
            List<String> spuCompose = taskMessage.getSpuCompose();

            ShopeeMarketingConfig shopeeMarketingConfig = shopeeMarketingConfigService.selectByPrimaryKey(configId);
            if (ObjectUtils.isEmpty(shopeeMarketingConfig)) {
                log.error("未找到对应的配置信息，配置id：{}", configId);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            // 获取店铺信息
            SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), accountNumber, true);

            // 获取当前店铺可以添加的商品数据
            List<EsShopeeItem> esShopeeItemList = getEsShopeeItemsByAccount(accountNumber, spuCompose);
            if (CollectionUtils.isEmpty(esShopeeItemList)) {
                log.error("当前店铺:{}，没有可添加的商品数据", accountNumber);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            // 添加商品到组合优惠套装中
            addComposeBundelDealItem(taskMessage, esShopeeItemList, saleAccount, shopeeMarketingConfig);

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("添加商品任务执行失败，原因：{}", e.getMessage(), e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private void addComposeBundelDealItem(ShopeeAddComposeBundleDealItemMessage taskMessage, List<EsShopeeItem> esShopeeItemList, SaleAccountAndBusinessResponse saleAccount, ShopeeMarketingConfig config) {
        // 获取当前店铺可参加获取商品的已存在组合优惠套装活动
        List<Long> itemIds = esShopeeItemList.stream().map(EsShopeeItem::getItemId).map(Long::valueOf).collect(Collectors.toList());
        List<ShopeeBundleDealProductListing> shopeeBundleDealProductListings = getItemExistComposeBundleDeal(saleAccount.getAccountNumber(), itemIds);
        if (CollectionUtils.isEmpty(shopeeBundleDealProductListings)) {
            // 创建新的组合套装活动
            createNewComposeBundelDeal(esShopeeItemList, config, taskMessage, saleAccount);
            return;
        }

        log.info("店铺:{}，存在组合优惠套装活动，为组合优惠套装商品进行重组合优惠套装活动, 商品数量:{}, 已添加到活动:{}个", saleAccount.getAccountNumber(), esShopeeItemList.size(), shopeeBundleDealProductListings.size());
        // 获取存在的优惠套装详情
        List<Long> bundleDealIds = shopeeBundleDealProductListings.stream().map(ShopeeBundleDealProductListing::getBundleDealId).distinct().collect(Collectors.toList());
        List<ShopeeMarketingBundleDeal> shopeeMarketingBundleDeals = getComposeBundelDealDetail(bundleDealIds);
        if (CollectionUtils.isEmpty(shopeeMarketingBundleDeals)) {
            throw new BusinessException("未找到进行中的优惠套装活动，" + StringUtils.join(bundleDealIds, ","));
        }

        Map<Integer, List<ShopeeMarketingBundleDeal>> suitTypeBundleDealMap = shopeeMarketingBundleDeals.stream()
                .filter(deal -> !deal.getStatus().equals(ShopeeMarketingBundleDealStatusEnum.EXPIRED.getCode()))
                .collect(Collectors.groupingBy(ShopeeMarketingBundleDeal::getSuitType));
        // 普通优惠套装退出
        if (suitTypeBundleDealMap.containsKey(ShopeeMarketingBundleDealSuitTypeEnum.NORMAL.getCode())) {
            log.info("店铺:{}，存在普通优惠套装活动，为普通优惠套装商品进行退出", saleAccount.getAccountNumber());
            List<ShopeeMarketingBundleDeal> normalBundleDeals = suitTypeBundleDealMap.get(ShopeeMarketingBundleDealSuitTypeEnum.NORMAL.getCode());
            List<Long> normalBundleIds = normalBundleDeals.stream().map(ShopeeMarketingBundleDeal::getBundleDealId).collect(Collectors.toList());
            List<ShopeeBundleDealProductListing> normalBundleListings = shopeeBundleDealProductListings.stream().filter(item -> normalBundleIds.contains(item.getBundleDealId())).collect(Collectors.toList());
            // 调用接口退出
            exitNormalBundleDeal(normalBundleListings, saleAccount);
        }

        // 检查组合优惠套装数据
        if (suitTypeBundleDealMap.containsKey(ShopeeMarketingBundleDealSuitTypeEnum.COMPOSE.getCode())) {
            List<ShopeeMarketingBundleDeal> composeBundelDeals = suitTypeBundleDealMap.get(ShopeeMarketingBundleDealSuitTypeEnum.COMPOSE.getCode());
            reComposeBundelDeal(composeBundelDeals, esShopeeItemList, shopeeBundleDealProductListings, saleAccount, config, taskMessage);
        } else {
            // 创建新的组合优惠套装活动
            createNewComposeBundelDeal(esShopeeItemList, config, taskMessage, saleAccount);
        }


    }

    /**
     * 存在组合优惠套装活动，重新组合
     *
     * @param composeBundelDeals
     * @param shopeeBundleDealProductListings
     * @param saleAccount
     * @param shopeeMarketingConfig
     * @param taskMessage
     */
    private void reComposeBundelDeal(List<ShopeeMarketingBundleDeal> composeBundelDeals, List<EsShopeeItem> esShopeeItemList, List<ShopeeBundleDealProductListing> shopeeBundleDealProductListings, SaleAccountAndBusinessResponse saleAccount, ShopeeMarketingConfig shopeeMarketingConfig, ShopeeAddComposeBundleDealItemMessage taskMessage) {
        List<String> addItemIds = esShopeeItemList.stream().map(EsShopeeItem::getItemId).distinct().collect(Collectors.toList());
        String accountNumber = saleAccount.getAccountNumber();
        // 找种子与需要解散的组合优惠活动
        Pair<List<ShopeeMarketingBundleDeal>, List<ShopeeMarketingBundleDeal>> baseComposeBundelDeal = findBaseComposeBundelDeal(composeBundelDeals);
        List<ShopeeMarketingBundleDeal> baseBundelDeals = baseComposeBundelDeal.getLeft();
        List<ShopeeMarketingBundleDeal> releaseBundelDeals = baseComposeBundelDeal.getRight();

        log.info("{},重组合优惠套装活动，种子活动：{}，需要释放的活动：{}", accountNumber, baseBundelDeals.size(), releaseBundelDeals.size());
        // 释放组合优惠套装商品
        List<ShopeeBundleDealProductListing> releaseBundleDealItems = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(releaseBundelDeals)) {
            List<ShopeeBundleDealProductListing> releaseBundleDealListing = releaseBundleDealItem(releaseBundelDeals, saleAccount);
            log.info("释放组合优惠套装商品完成，accountNumber:{},释放商品数量:{}", accountNumber, releaseBundleDealListing.size());
            if (CollectionUtils.isNotEmpty(releaseBundleDealListing)) {
                releaseBundleDealItems.addAll(releaseBundleDealListing);
            }
        }

        if (CollectionUtils.isNotEmpty(releaseBundleDealItems)) {
            List<String> itemIds = releaseBundleDealItems.stream().map(ShopeeBundleDealProductListing::getItemId).map(String::valueOf).collect(Collectors.toList());
            // 过滤待添加不存在的item
            List<String> needSearchItems = itemIds.stream().filter(itemId -> !addItemIds.contains(itemId))
                    .collect(Collectors.toList());
            List<EsShopeeItem> releaseBundleDealItemList = searchEsShopeeItemByItemIds(needSearchItems, accountNumber);
            if (CollectionUtils.isNotEmpty(releaseBundleDealItemList)) {
                esShopeeItemList.addAll(releaseBundleDealItemList);
            }
        }

        // esShopeeItemList 按ItemId去重
        esShopeeItemList = esShopeeItemList.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(EsShopeeItem::getItemId, Function.identity(), (existing, replacement) -> existing),
                        map -> new ArrayList<>(map.values())
                ));

        if (CollectionUtils.isNotEmpty(baseBundelDeals)) {
            // 优化：先构建bundleDealId集合，避免嵌套流操作
            Set<Long> baseBundelDealIdSet = baseBundelDeals.stream()
                    .map(ShopeeMarketingBundleDeal::getBundleDealId)
                    .collect(Collectors.toSet());

            // 过滤出已存在活动已报名的商品
            Set<Long> baseBundelDealItemIds = shopeeBundleDealProductListings.stream()
                    .filter(listing -> baseBundelDealIdSet.contains(listing.getBundleDealId()))
                    .map(ShopeeBundleDealProductListing::getItemId)
                    .collect(Collectors.toSet());

            // 安全的类型转换，避免NumberFormatException
            esShopeeItemList.removeIf(item -> {
                try {
                    return baseBundelDealItemIds.contains(Long.parseLong(item.getItemId()));
                } catch (NumberFormatException e) {
                    log.warn("{}，商品itemId格式异常：{}", accountNumber, item.getItemId());
                    return false;
                }
            });
            log.info("{}，过滤已存在活动已报名的商品，剩余:{}个商品未添加", accountNumber, esShopeeItemList.size());
            if (CollectionUtils.isEmpty(esShopeeItemList)) {
                return;
            }

            // 对剩下的待分配商品根据缺少的数量按所需数量分配指标,分配到的数据就不能变更至其他活动
            Map<Long, Integer> bundelDealNeedItemMap = baseBundelDeals.stream()
                    .collect(Collectors.toMap(
                            ShopeeMarketingBundleDeal::getBundleDealId,
                            bundelDeal -> Math.max(0, 2000 - bundelDeal.getProductNumber())
                    ));

            // 优化：使用迭代器安全地修改集合，避免并发修改异常
            List<EsShopeeItem> remainingItems = new ArrayList<>(esShopeeItemList);

            for (ShopeeMarketingBundleDeal bundelDeal : baseBundelDeals) {
                int needItemNum = bundelDealNeedItemMap.get(bundelDeal.getBundleDealId());
                if (needItemNum <= 0 || remainingItems.isEmpty()) {
                    continue;
                }

                // 取需要的数量，但不超过剩余商品数量
                int actualAllocateNum = Math.min(needItemNum, remainingItems.size());
                List<EsShopeeItem> allocationItemList = remainingItems.subList(0, actualAllocateNum);
                List<EsShopeeItem> allocatedItems = new ArrayList<>(allocationItemList);

                // 从剩余商品中移除已分配的商品
                remainingItems.removeAll(allocatedItems);
                log.info("{}，对存在活动:{}，进行补充, 原活动已添加数量:{},分配数量：{}, 剩余:{}个商品未添加",
                        accountNumber, bundelDeal.getBundleDealId(), bundelDeal.getProductNumber(),
                        allocatedItems.size(), remainingItems.size());

                shopeeBundleDealHandler.submitItem2ComposeBundelDeal(allocatedItems, bundelDeal, saleAccount, shopeeMarketingConfig);
                log.info("{}，对存在活动:{}，进行补充完成, 补充失败数量:{}", accountNumber, bundelDeal.getBundleDealId(), allocatedItems.size());
            }

            // 更新原集合为剩余商品
            esShopeeItemList.clear();
            esShopeeItemList.addAll(remainingItems);
        }
        log.info("{}，重组合优惠套装活动完成，剩余:{}个商品未添加", accountNumber, esShopeeItemList.size());
        if (CollectionUtils.isNotEmpty(esShopeeItemList)) {
            createNewComposeBundelDeal(esShopeeItemList, shopeeMarketingConfig, taskMessage, saleAccount);
        }
    }

    private List<EsShopeeItem> searchEsShopeeItemByItemIds(List<String> itemIds, String accountNumber) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Collections.emptyList();
        }
        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemSeller(accountNumber);
        request.setItemIdList(itemIds);
        request.setQueryFields(new String[]{"itemId", "spu", "articleNumber"});
        request.setItemStatus("NORMAL");
        request.setStockNotEqual(0);
        request.setIsGoods(true);
        List<EsShopeeItem> esShopeeItems = esShopeeItemService.getEsShopeeItems(request);
        if (CollectionUtils.isEmpty(esShopeeItems)) {
            return Collections.emptyList();
        }

        // 去重保留唯一的item数据（保留最后一个）
        return esShopeeItems.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                EsShopeeItem::getItemId,
                                Function.identity(),
                                (existing, replacement) -> replacement,
                                LinkedHashMap::new
                        ),
                        map -> new ArrayList<>(map.values())
                ));

    }


    /**
     * 找种子与需要解散的组合优惠活动
     *
     * @param composeBundelDeals
     * @return left 需要补充的活动 right 需要释放的活动
     */
    private Pair<List<ShopeeMarketingBundleDeal>, List<ShopeeMarketingBundleDeal>> findBaseComposeBundelDeal(List<ShopeeMarketingBundleDeal> composeBundelDeals) {
        if (composeBundelDeals.size() == 1) {
            return Pair.of(composeBundelDeals, Collections.emptyList());
        }

        List<ShopeeMarketingBundleDeal> left = composeBundelDeals.stream().filter(bundelDeal -> {
            Integer productNumber = bundelDeal.getProductNumber();
            return productNumber >= 200 && productNumber < 2000;
        }).collect(Collectors.toList());

        List<ShopeeMarketingBundleDeal> right = composeBundelDeals.stream().filter(bundelDeal -> {
            Integer productNumber = bundelDeal.getProductNumber();
            return productNumber < 200;
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(left)) {
            if (CollectionUtils.isNotEmpty(right)) {
                // 获取最大的一个作为种子活动
                left.add(right.stream().max(Comparator.comparing(ShopeeMarketingBundleDeal::getProductNumber)).get());
                right.remove(left.get(0));
            }
        }
        return Pair.of(left, right);

    }

    /**
     * 释放组合优惠套装商品
     *
     * @param releaseBundleDealItems
     * @param saleAccount
     */
    private List<ShopeeBundleDealProductListing> releaseBundleDealItem(List<ShopeeMarketingBundleDeal> releaseBundleDealItems, SaleAccountAndBusinessResponse saleAccount) {
        releaseBundleDealItems.forEach(bundleDeal -> {
            if (ShopeeMarketingBundleDealStatusEnum.ONGOING.getCode().equals(bundleDeal.getStatus())) {
                log.info("释放结束进行中的组合优惠套装商品，accountNumber:{},bundleDealId:{}", saleAccount.getAccountNumber(), bundleDeal.getBundleDealId());
                ShopeeEndBundleDealtemV2 shopeeEndBundleDealtemV2 = new ShopeeEndBundleDealtemV2();
                shopeeEndBundleDealtemV2.setBundle_deal_id(bundleDeal.getBundleDealId());
                ShopeeResponse shopeeResponse = ShopeeBundelDealCallV2.endBundelDealItem(saleAccount, shopeeEndBundleDealtemV2);
                if (ObjectUtils.isEmpty(shopeeResponse) || StringUtils.isNotBlank(shopeeResponse.getError())) {
                    log.error("释放结束组合优惠套装商品失败，accountNumber:{},bundleDealId:{},error:{}", saleAccount.getAccountNumber(), bundleDeal.getBundleDealId(), JSON.toJSONString(shopeeResponse));
                    throw new BusinessException("释放组合优惠套装商品失败，accountNumber:" + saleAccount.getAccountNumber() + ",bundleDealId:" + bundleDeal.getBundleDealId() + ",error:" + JSON.toJSONString(shopeeResponse));
                }
            }

            if (ShopeeMarketingBundleDealStatusEnum.NEXT.getCode().equals(bundleDeal.getStatus())) {
                log.info("释放删除待开始的组合优惠套装商品，accountNumber:{},bundleDealId:{}", saleAccount.getAccountNumber(), bundleDeal.getBundleDealId());
                ShopeeDeleteBundleDealV2 shopeeDeleteBundleDealV2 = new ShopeeDeleteBundleDealV2();
                shopeeDeleteBundleDealV2.setBundle_deal_id(bundleDeal.getBundleDealId());
                ShopeeResponse shopeeResponse = ShopeeBundelDealCallV2.deleteBundleDeal(saleAccount, shopeeDeleteBundleDealV2);
                if (ObjectUtils.isEmpty(shopeeResponse) || StringUtils.isNotBlank(shopeeResponse.getError())) {
                    log.error("释放组合优惠套装商品失败，accountNumber:{},bundleDealId:{},error:{}", saleAccount.getAccountNumber(), bundleDeal.getBundleDealId(), JSON.toJSONString(shopeeResponse));
                    throw new BusinessException("释放组合优惠套装商品失败，accountNumber:" + saleAccount.getAccountNumber() + ",bundleDealId:" + bundleDeal.getBundleDealId() + ",error:" + JSON.toJSONString(shopeeResponse));
                }
            }
        });
        releaseBundleDealItems.forEach(releaseBundleDeal -> {
            // 修改状态
            releaseBundleDeal.setStatus(ShopeeMarketingBundleDealStatusEnum.EXPIRED.getCode());
            shopeeMarketingBundleDealService.updateByPrimaryKeySelective(releaseBundleDeal);
        });

        // 获取释放的商品
        List<Long> bundleDealIds = releaseBundleDealItems.stream().map(ShopeeMarketingBundleDeal::getBundleDealId).collect(Collectors.toList());
        ShopeeBundleDealProductListingExample example = new ShopeeBundleDealProductListingExample();
        example.createCriteria()
                .andAccountNumberEqualTo(saleAccount.getAccountNumber())
                .andStatusEqualTo(1)
                .andBundleDealIdIn(bundleDealIds);
        List<ShopeeBundleDealProductListing> releaseBundleDealListing = shopeeBundleDealProductListingService.selectByExample(example);
        if (CollectionUtils.isNotEmpty(releaseBundleDealItems)) {
            shopeeBundleDealProductListingService.deleteByPrimaryKey(releaseBundleDealListing.stream().map(ShopeeBundleDealProductListing::getId).collect(Collectors.toList()));
        }
        return releaseBundleDealListing;
    }

    private void exitNormalBundleDeal(List<ShopeeBundleDealProductListing> normalBundleListings, SaleAccountAndBusinessResponse saleAccount) {
        Map<Long, List<ShopeeBundleDealProductListing>> bundleIdMap = normalBundleListings.stream().collect(Collectors.groupingBy(ShopeeBundleDealProductListing::getBundleDealId));
        bundleIdMap.forEach((bundleId, listings) -> {
            List<Long> itemIds = listings.stream().map(ShopeeBundleDealProductListing::getItemId).collect(Collectors.toList());
            ShopeeDeleteBundleDealtemV2 shopeeDeleteBundleDealtemV2 = new ShopeeDeleteBundleDealtemV2();
            shopeeDeleteBundleDealtemV2.setBundle_deal_id(bundleId);
            shopeeDeleteBundleDealtemV2.addItem_list(itemIds);
            ShopeeResponse shopeeResponse = ShopeeBundelDealCallV2.deleteBundleDealItem(saleAccount, shopeeDeleteBundleDealtemV2);
            String error = shopeeResponse.getError();
            if (StringUtils.isNotBlank(error)) {
                log.error("退出普通优惠套装失败，accountNumber:{},bundleDealId:{},error:{}", saleAccount.getAccountNumber(), bundleId, JSON.toJSONString(shopeeResponse));
            } else {
                // 删除本地数据
                shopeeBundleDealProductListingService.deleteByPrimaryKey(listings.stream().map(ShopeeBundleDealProductListing::getId).collect(Collectors.toList()));
            }
        });
    }

    private List<ShopeeMarketingBundleDeal> getComposeBundelDealDetail(List<Long> bundleDealIds) {
        ShopeeMarketingBundleDealExample example = new ShopeeMarketingBundleDealExample();
        example.createCriteria()
                .andBundleDealIdIn(bundleDealIds);
        return shopeeMarketingBundleDealService.selectByExample(example);
    }

    /**
     * 创建新的组合优惠套装
     *
     * @param esShopeeItemList
     * @param config
     * @param taskMessage
     * @param saleAccount
     */
    private void createNewComposeBundelDeal(List<EsShopeeItem> esShopeeItemList, ShopeeMarketingConfig config, ShopeeAddComposeBundleDealItemMessage taskMessage, SaleAccountAndBusinessResponse saleAccount) {
        List<List<EsShopeeItem>> partition = Lists.partition(esShopeeItemList, 2000);
        for (int i = 0; i < partition.size(); i++) {
            List<EsShopeeItem> addItems = partition.get(i);
            String taskIndex = taskMessage.getIndex() == 1 ? "" : "-" + taskMessage.getIndex();
            String subIndex = i == 0 ? "" : "-" + i;
            String composeBundelDealName = "组合产品" + taskIndex + subIndex + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            log.info("创建新的组合优惠套装，accountNumber:{},suitName:{}, itemSize:{}", saleAccount.getAccountNumber(), composeBundelDealName, esShopeeItemList.size());
            ShopeeMarketingBundleDeal composeBundelDeal = shopeeBundleDealHandler.createComposeBundelDeal(config, taskMessage.getSpuCompose(), saleAccount, composeBundelDealName);
            if (composeBundelDeal == null) {
                return;
            }
            // 添加商品到组合优惠套装中
            shopeeBundleDealHandler.submitItem2ComposeBundelDeal(addItems, composeBundelDeal, saleAccount, config);
        }
    }



    private List<ShopeeBundleDealProductListing> getItemExistComposeBundleDeal(String accountNumber, List<Long> itemIds) {
        ShopeeBundleDealProductListingExample example = new ShopeeBundleDealProductListingExample();
        example.createCriteria()
                .andAccountNumberEqualTo(accountNumber)
                .andItemIdIn(itemIds);
        List<ShopeeBundleDealProductListing> shopeeBundleDealProductListings = shopeeBundleDealProductListingService.selectByExample(example);
        if (CollectionUtils.isEmpty(shopeeBundleDealProductListings)) {
            return null;
        }


        return shopeeBundleDealProductListings;
    }

    /**
     * 从ES获取可添加商品数据
     *
     * @param accountNumber
     * @param spuCompose    spu组合
     * @return
     */
    private List<EsShopeeItem> getEsShopeeItemsByAccount(String accountNumber, List<String> spuCompose) {
        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemSeller(accountNumber);
        request.setSpuList(spuCompose);
        request.setQueryFields(new String[]{"itemId", "spu", "articleNumber"});
        request.setItemStatus("NORMAL");
        request.setStockNotEqual(0);
        request.setIsGoods(true);
        List<EsShopeeItem> itemServiceEsShopeeItems = esShopeeItemService.getEsShopeeItems(request);

        // 3、过滤最近七天涨价的item数据（ES-10753）
        Map<String, List<EsShopeeItem>> itemIdMap = itemServiceEsShopeeItems.stream().collect(Collectors.groupingBy(EsShopeeItem::getItemId));
        Iterator<Map.Entry<String, List<EsShopeeItem>>> iterator = itemIdMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<EsShopeeItem>> entry = iterator.next();
            String itemId = entry.getKey();
            List<EsShopeeItem> shopeeItems = entry.getValue();
            try {
                LocalDate endDate = LocalDate.now();
                LocalDate startDate = endDate.minusDays(7);
                List<String> list = shopeeItems.stream().map(EsShopeeItem::getArticleNumber).collect(Collectors.toList());
                List<String> feedTaskUpdatePricePrice = ShopeeFeedTaskHandleUtil.getFeedTaskUpdatePricePrice(ShopeeFeedTaskEnum.UPDATE_PRICE.getValue(), accountNumber, itemId, list, startDate, endDate);
                if (CollectionUtils.isNotEmpty(feedTaskUpdatePricePrice)) {
                    iterator.remove(); // 使用 iterator 安全地移除元素
                }
            } catch (Exception e) {
                log.error("ShopeeAddBundleDealItemMqListener 查询商品涨价信息异常：{}", e.getMessage(), e);
            }
        }

        // 根据itemID去重保留唯一的item数据（每个itemId保留最后一个）
        // 优化：由于itemIdMap已经按itemId分组，每组内取最后一个元素即可
        return itemIdMap.values().stream()
                .filter(CollectionUtils::isNotEmpty)
                .map(itemList -> itemList.get(itemList.size() - 1))
                .collect(Collectors.toList());
    }

}
