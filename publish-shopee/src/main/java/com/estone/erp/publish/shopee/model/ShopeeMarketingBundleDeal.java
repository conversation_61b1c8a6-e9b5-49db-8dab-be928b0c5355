package com.estone.erp.publish.shopee.model;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.TableField;
import com.estone.erp.publish.shopee.component.marking.BundleDealConfigParam;
import com.estone.erp.publish.shopee.dto.BundleDealProductVO;
import com.estone.erp.publish.shopee.enums.ShopeeBundleDealTypeEnum;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingBundleDealStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingBundleDealSuitTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShopeeMarketingBundleDeal implements Serializable {
    private static final long serialVersionUID = 1L;
    public List<BundleDealProductVO> bundleDealProductVoList;

    /**
     * id database column shopee_marketing_bundle_deal.id
     */
    private Long id;

    /**
     * 平台套装id（平台标识） database column shopee_marketing_bundle_deal.bundle_deal_id
     */
    private Long bundleDealId;


    /**
     * 店铺账号 database column shopee_marketing_bundle_deal.account_number
     */
    private String accountNumber;


    /**
     * 套装名称 database column shopee_marketing_bundle_deal.name
     */
    private String name;

    /**
     * 套装类型 database column shopee_marketing_bundle_deal.type
     */
    private Integer type;

    /**
     * 套装方式 1 普通优惠套装 2 组合优惠套装 database column shopee_marketing_bundle_deal.suit_type
     */
    private Integer suitType;

    /**
     * 商品数量 database column shopee_marketing_bundle_deal.product_number
     */
    private Integer productNumber;

    /**
     * 添加状态 0待添加 1已添加满 2未添加满 database column shopee_marketing_bundle_deal.add_status
     */
    private Integer addStatus;

    /**
     * 状态(未开始，进行中，已过期) database column shopee_marketing_bundle_deal.status
     */
    private Integer status;

    /**
     * 套装开始时间 database column shopee_marketing_bundle_deal.bundle_deal_start_time
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp bundleDealStartTime;

    /**
     * 套装结束时间 database column shopee_marketing_bundle_deal.bundle_deal_end_time
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp bundleDealEndTime;

    /**
     * 同步时间 database column shopee_marketing_bundle_deal.sync_time
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp syncTime;

    /**
     * 创建时间 database column shopee_marketing_bundle_deal.created_time
     */
    private Timestamp createdTime;

    /**
     * 更新时间 database column shopee_marketing_bundle_deal.updated_time
     */
    private Timestamp updatedTime;

    /**
     * 配置ID database column shopee_marketing_bundle_deal.config_id
     */
    private Long configId;

    /**
     * 上次job执行时间
     */
    private Timestamp lastSyncProductExecTime;

    /*拓展字段*/

    /**
     * 站点 database column shopee_marketing_bundle_deal.site
     */
    private String site;

    /**
     * 物流渠道id database column shopee_marketing_bundle_deal.logistics_id
     */
    private Integer logisticsId;

    /**
     * 商家id database column shopee_marketing_bundle_deal.merchant_id
     */
    private String merchantId;

    /**
     * 商家名称 database column shopee_marketing_bundle_deal.merchant_name
     */
    private String merchantName;


    /**
     * 销售
     */
    private String salesman;

    /**
     * 销售组长
     */
    private String salesTeamLeader;

    /**
     * 销售主管
     */
    private String salesSupervisorName;

    /**
     * 规格内容
     */
    private String ruleJson;


    private String typeStr;


    /**
     * 停止状态
     */
    private Integer stopStatus;

    /**
     * 规则名称
     */
    @TableField(exist = false)
    private String ruleName;

    @TableField(exist = false)
    private String groupName;

    /**
     * 失败备注
     */
    private String errorMsg;

    /**
     * spu组合 database column shopee_marketing_bundle_deal.spu_compose
     */
    private String spuCompose;

    public String getTypeStr() {
        String convert = ShopeeBundleDealTypeEnum.convert(type);
        BundleDealConfigParam bundleDealConfigParam = JSON.parseObject(ruleJson, BundleDealConfigParam.class);
        if (bundleDealConfigParam == null) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(convert);
        stringBuilder.append(":");
        if (Objects.equals(suitType, ShopeeMarketingBundleDealSuitTypeEnum.COMPOSE.getCode()) && CollectionUtils.isNotEmpty(bundleDealConfigParam.getSuitDealDetailList())) {
            for (int i = 0; i < bundleDealConfigParam.getSuitDealDetailList().size(); i++) {
                BundleDealConfigParam.DealDetail dealDetail = bundleDealConfigParam.getSuitDealDetailList().get(i);
                if (Objects.equals(dealDetail.getRule_type(), ShopeeBundleDealTypeEnum.FIX_PRICE.getCode())) {
                    stringBuilder.append("购买" + dealDetail.getProductNum() + "个商品共" + dealDetail.getFix_price() + "元");
                } else if (Objects.equals(dealDetail.getRule_type(), ShopeeBundleDealTypeEnum.DISCOUNT_PERCENTAGE.getCode())) {
                    stringBuilder.append("购买" + dealDetail.getProductNum() + "个商品减少" + dealDetail.getDiscount_percentage() + "%");
                } else if (Objects.equals(dealDetail.getRule_type(), ShopeeBundleDealTypeEnum.DISCOUNT_VALUE.getCode())) {
                    stringBuilder.append("购买" + dealDetail.getProductNum() + "个商品减少" + dealDetail.getDiscount_value() + "元");
                }
                if (i != bundleDealConfigParam.getSuitDealDetailList().size() - 1) {
                    stringBuilder.append("\n");
                }
            }
        }

        if (Objects.equals(suitType, ShopeeMarketingBundleDealSuitTypeEnum.NORMAL.getCode()) && CollectionUtils.isNotEmpty(bundleDealConfigParam.getDealDetailList())) {
            for (int i = 0; i < bundleDealConfigParam.getDealDetailList().size(); i++) {
                BundleDealConfigParam.DealDetail dealDetail = bundleDealConfigParam.getDealDetailList().get(i);
                if (Objects.equals(dealDetail.getRule_type(), ShopeeBundleDealTypeEnum.FIX_PRICE.getCode())) {
                    stringBuilder.append("购买" + dealDetail.getProductNum() + "个商品共" + dealDetail.getFix_price() + "元");
                } else if (Objects.equals(dealDetail.getRule_type(), ShopeeBundleDealTypeEnum.DISCOUNT_PERCENTAGE.getCode())) {
                    stringBuilder.append("购买" + dealDetail.getProductNum() + "个商品减少" + dealDetail.getDiscount_percentage() + "%");
                } else if (Objects.equals(dealDetail.getRule_type(), ShopeeBundleDealTypeEnum.DISCOUNT_VALUE.getCode())) {
                    stringBuilder.append("购买" + dealDetail.getProductNum() + "个商品减少" + dealDetail.getDiscount_value() + "元");
                }
                if (i != bundleDealConfigParam.getDealDetailList().size() - 1) {
                    stringBuilder.append("\n");
                }
            }
        }
        return stringBuilder.toString();
    }

    private String statusStr;

    public String getStatusStr() {
        return ShopeeMarketingBundleDealStatusEnum.convert(status);
    }

}