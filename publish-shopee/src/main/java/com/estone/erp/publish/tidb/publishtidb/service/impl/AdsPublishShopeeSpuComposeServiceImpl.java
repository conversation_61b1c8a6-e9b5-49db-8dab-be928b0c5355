package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.AdsPublishShopeeSpuCompose;
import com.estone.erp.publish.tidb.publishtidb.mapper.AdsPublishShopeeSpuComposeMapper;
import com.estone.erp.publish.tidb.publishtidb.service.AdsPublishShopeeSpuComposeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class AdsPublishShopeeSpuComposeServiceImpl extends ServiceImpl<AdsPublishShopeeSpuComposeMapper, AdsPublishShopeeSpuCompose> implements AdsPublishShopeeSpuComposeService {
    @Override
    public CQueryResult<AdsPublishShopeeSpuCompose> queryPage(CQuery<AdsPublishShopeeSpuCompose> query) {
        IPage<AdsPublishShopeeSpuCompose> page = new Page<>(query.getPage(), query.getLimit());
        LambdaQueryWrapper<AdsPublishShopeeSpuCompose> wrapper = new LambdaQueryWrapper<>();
        // TODO: build query wrapper by query.getSearch()
        
        IPage<AdsPublishShopeeSpuCompose> pageResult = page(page, wrapper);
        
        CQueryResult<AdsPublishShopeeSpuCompose> result = new CQueryResult<>();
        result.setTotal(pageResult.getTotal());
        result.setTotalPages(Long.valueOf(pageResult.getPages()).intValue());
        result.setRows(pageResult.getRecords());
        result.setSuccess(true);
        return result;
    }
}
