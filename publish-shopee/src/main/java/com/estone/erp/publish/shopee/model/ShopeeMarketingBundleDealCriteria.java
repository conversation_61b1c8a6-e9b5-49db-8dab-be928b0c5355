package com.estone.erp.publish.shopee.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024-06-18 18:53:27
 */
@Data
public class ShopeeMarketingBundleDealCriteria extends ShopeeMarketingBundleDeal {
    private static final long serialVersionUID = 1L;

    private List<Long> idList;

    /**
     * 销售
     */
    private List<String> saleManList;

    /**
     * 销售组长
     */
    private List<String> saleTeamLeaderList;

    /**
     * 销售主管
     */
    private List<String> salesSupervisorList;

    /**
     * 账号
     */
    private List<String> accountNumberList;

    /**
     * 站点
     */
    private List<String> siteList;



    /**
     * 套装名称
     */
    private String name;

    /**
     * status 状态
     */
    private List<String> statusList;

    /**
     * 分组id
     */
    private List<Integer> groupIdList;

    /**
     * 停止
     */
    private List<Integer> stopStatusList;

    /**
     * 配置id
     */
    private List<Integer> marketingIdList;

    /**
     * 规则名称
     */
    private String likeRuleName;


    /**
     * 开始时间 从
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp startTimeFrom;
    /**
     * 开始时间 到
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp startTimeTo;

    /**
     * 结束时间 从
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp endTimeFrom;
    /**
     * 结束时间 到
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp endTimeTo;

    /**
     * 同步时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp syncTimeFrom;
    /**
     * 同步时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp syncTimeTo;


    public ShopeeMarketingBundleDealExample getExample() {
        ShopeeMarketingBundleDealExample example = new ShopeeMarketingBundleDealExample();
        ShopeeMarketingBundleDealExample.Criteria criteria = example.createCriteria();
        if (this.getBundleDealId() != null) {
            criteria.andBundleDealIdEqualTo(this.getBundleDealId());
        }
        if (this.getMerchantId() != null) {
            criteria.andMerchantIdEqualTo(this.getMerchantId());
        }
        if (StringUtils.isNotBlank(this.getMerchantName())) {
            criteria.andMerchantNameEqualTo(this.getMerchantName());
        }
        if (StringUtils.isNotBlank(this.getAccountNumber())) {
            criteria.andAccountNumberEqualTo(this.getAccountNumber());
        }
        if (StringUtils.isNotBlank(this.getSite())) {
            criteria.andSiteEqualTo(this.getSite());
        }
        if (this.getType() != null) {
            criteria.andTypeEqualTo(this.getType());
        }
        if (this.getSuitType() != null) {
            criteria.andSuitTypeEqualTo(this.getSuitType());
        }

        if (this.getStatus() != null) {
            criteria.andStatusEqualTo(this.getStatus());
        }
        if (this.getBundleDealStartTime() != null) {
            criteria.andBundleDealStartTimeEqualTo(this.getBundleDealStartTime());
        }
        if (this.getBundleDealEndTime() != null) {
            criteria.andBundleDealEndTimeEqualTo(this.getBundleDealEndTime());
        }
        if (this.getSyncTime() != null) {
            criteria.andSyncTimeEqualTo(this.getSyncTime());
        }
        if (this.getCreatedTime() != null) {
            criteria.andCreatedTimeEqualTo(this.getCreatedTime());
        }
        if (this.getUpdatedTime() != null) {
            criteria.andUpdatedTimeEqualTo(this.getUpdatedTime());
        }

        if (CollectionUtils.isNotEmpty(this.getIdList())) {
            criteria.andIdIn(this.getIdList());
        }

        if (CollectionUtils.isNotEmpty(this.getAccountNumberList())) {
            criteria.andAccountNumberIn(this.getAccountNumberList());
        }
        if (CollectionUtils.isNotEmpty(this.getSiteList())) {
            criteria.andSiteIn(this.getSiteList());
        }

        if (StringUtils.isNotBlank(this.getName())) {
            criteria.andNameLike("%" + this.getName() + "%");
        }

        if (this.getStartTimeFrom() != null) {
            criteria.andBundleDealStartTimeGreaterThanOrEqualTo(this.getStartTimeFrom());
        }
        if (this.getStartTimeTo() != null) {
            criteria.andBundleDealStartTimeLessThanOrEqualTo(this.getStartTimeTo());
        }
        if (this.getEndTimeFrom() != null) {
            criteria.andBundleDealEndTimeGreaterThanOrEqualTo(this.getEndTimeFrom());
        }
        if (this.getEndTimeTo() != null) {
            criteria.andBundleDealEndTimeLessThanOrEqualTo(this.getEndTimeTo());
        }

        if (this.getSyncTimeFrom() != null) {
            criteria.andSyncTimeGreaterThanOrEqualTo(this.getSyncTimeFrom());
        }
        if (this.getSyncTimeTo() != null) {
            criteria.andSyncTimeLessThanOrEqualTo(this.getSyncTimeTo());
        }

        if (CollectionUtils.isNotEmpty(this.getStatusList())) {
            List<Integer> statusIntegerList = this.getStatusList().stream().map(t -> Integer.valueOf(t)).collect(Collectors.toList());
            criteria.andStatusIn(statusIntegerList);
        }

        if (CollectionUtils.isNotEmpty(this.marketingIdList)) {
            criteria.andMarketingIdIn(this.marketingIdList);
        }
        if (CollectionUtils.isNotEmpty(this.stopStatusList)) {
            criteria.andStopStatusIn(this.stopStatusList);
        }

        if(this.getStopStatus() != null) {
            criteria.andStopStatusEqualTo(this.getStopStatus());
        }
        return example;
    }
}