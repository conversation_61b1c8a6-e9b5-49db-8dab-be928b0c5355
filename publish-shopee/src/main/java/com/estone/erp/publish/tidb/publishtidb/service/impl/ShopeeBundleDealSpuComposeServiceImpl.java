package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealSpuCompose;
import com.estone.erp.publish.tidb.publishtidb.mapper.ShopeeBundleDealSpuComposeMapper;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeBundleDealSpuComposeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 组合套装数据集 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class ShopeeBundleDealSpuComposeServiceImpl extends ServiceImpl<ShopeeBundleDealSpuComposeMapper, ShopeeBundleDealSpuCompose> implements ShopeeBundleDealSpuComposeService {
    @Override
    public CQueryResult<ShopeeBundleDealSpuCompose> queryPage(CQuery<ShopeeBundleDealSpuCompose> query) {
        IPage<ShopeeBundleDealSpuCompose> page = new Page<>(query.getPage(), query.getLimit());
        LambdaQueryWrapper<ShopeeBundleDealSpuCompose> wrapper = new LambdaQueryWrapper<>();
        // TODO: build query wrapper by query.getSearch()
        
        IPage<ShopeeBundleDealSpuCompose> pageResult = page(page, wrapper);
        
        CQueryResult<ShopeeBundleDealSpuCompose> result = new CQueryResult<>();
        result.setTotal(pageResult.getTotal());
        result.setTotalPages(Long.valueOf(pageResult.getPages()).intValue());
        result.setRows(pageResult.getRecords());
        result.setSuccess(true);
        return result;
    }
}
