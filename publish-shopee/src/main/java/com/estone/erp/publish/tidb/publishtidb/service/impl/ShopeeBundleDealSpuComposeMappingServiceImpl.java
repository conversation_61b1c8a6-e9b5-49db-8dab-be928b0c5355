package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealSpuComposeMapping;
import com.estone.erp.publish.tidb.publishtidb.mapper.ShopeeBundleDealSpuComposeMappingMapper;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeBundleDealSpuComposeMappingService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * SPU组合套装映射 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class ShopeeBundleDealSpuComposeMappingServiceImpl extends ServiceImpl<ShopeeBundleDealSpuComposeMappingMapper, ShopeeBundleDealSpuComposeMapping> implements ShopeeBundleDealSpuComposeMappingService {
    @Override
    public CQueryResult<ShopeeBundleDealSpuComposeMapping> queryPage(CQuery<ShopeeBundleDealSpuComposeMapping> query) {
        IPage<ShopeeBundleDealSpuComposeMapping> page = new Page<>(query.getPage(), query.getLimit());
        LambdaQueryWrapper<ShopeeBundleDealSpuComposeMapping> wrapper = new LambdaQueryWrapper<>();
        // TODO: build query wrapper by query.getSearch()
        
        IPage<ShopeeBundleDealSpuComposeMapping> pageResult = page(page, wrapper);
        
        CQueryResult<ShopeeBundleDealSpuComposeMapping> result = new CQueryResult<>();
        result.setTotal(pageResult.getTotal());
        result.setTotalPages(Long.valueOf(pageResult.getPages()).intValue());
        result.setRows(pageResult.getRecords());
        result.setSuccess(true);
        return result;
    }
}
