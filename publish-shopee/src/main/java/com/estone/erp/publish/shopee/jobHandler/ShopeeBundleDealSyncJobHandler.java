package com.estone.erp.publish.shopee.jobHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.api.v2.param.bundledeal.BundleDealItemV2;
import com.estone.erp.publish.shopee.api.v2.param.bundledeal.BundleDealListVov2;
import com.estone.erp.publish.shopee.api.v2.param.bundledeal.BundleDealPageV2;
import com.estone.erp.publish.shopee.api.v2.param.bundledeal.ShopeeAddBundleDealtemV2;
import com.estone.erp.publish.shopee.call.v2.ShopeeBundelDealCallV2;
import com.estone.erp.publish.shopee.component.marking.BundleDealConfigParam;
import com.estone.erp.publish.shopee.enums.*;
import com.estone.erp.publish.shopee.model.ShopeeMarketingBundleDeal;
import com.estone.erp.publish.shopee.model.ShopeeMarketingBundleDealExample;
import com.estone.erp.publish.shopee.model.ShopeeMarketingConfig;
import com.estone.erp.publish.shopee.model.ShopeeMarketingConfigExample;
import com.estone.erp.publish.shopee.service.ShopeeMarketingBundleDealService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingConfigService;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.tidb.publishtidb.mapper.ShopeeBundleDealProductListingMapper;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListing;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 拉取优惠套装和优惠套装对应的商品
 *
 * <AUTHOR>
 * @date 2024/6/18 16:13
 */
@Slf4j
@Component
public class ShopeeBundleDealSyncJobHandler extends AbstractJobHandler {

    @Resource
    private ShopeeMarketingConfigService shopeeMarketingConfigService;

    @Autowired
    private ShopeeBundleDealProductListingMapper shopeeBundleDealProductListingMapper;

    @Autowired
    private ShopeeMarketingBundleDealService shopeeMarketingBundleDealService;

    public ShopeeBundleDealSyncJobHandler() {
        super("ShopeeBundleDealSyncJobHandler");
    }

    @Data
    public static class InnerParam {
        private List<Integer> configIdList;
    }

    @Override
    @XxlJob("ShopeeBundleDealSyncJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        ShopeeBundleDealSyncJobHandler.InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, ShopeeBundleDealSyncJobHandler.InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if (innerParam == null) {
            innerParam = new ShopeeBundleDealSyncJobHandler.InnerParam();
        }

        XxlJobLogger.log("-------开始同步优惠套装--------");
        //解析入参查看是否传入店铺列表
        ShopeeMarketingConfigExample shopeeMarketingConfigExample = new ShopeeMarketingConfigExample();
        ShopeeMarketingConfigExample.Criteria criteria = shopeeMarketingConfigExample.createCriteria();
        criteria.andTypeEqualTo(ShopeeMarketingConfigTypeEnum.BUNDLE_DEAL.getCode());
        if (CollectionUtils.isNotEmpty(innerParam.getConfigIdList())) {
            criteria.andIdIn(innerParam.getConfigIdList());
        }
        List<ShopeeMarketingConfig> shopeeMarketingConfigs = shopeeMarketingConfigService.selectByExample(shopeeMarketingConfigExample);
        if (CollectionUtils.isEmpty(shopeeMarketingConfigs)) {
            XxlJobLogger.log("----------没有配置优惠套装");
            return ReturnT.SUCCESS;
        }
        List<String> accountNumberList = shopeeMarketingConfigs.stream().flatMap(config -> Arrays.stream(config.getAccounts().split(","))).collect(Collectors.toList());
        for (String accountNumber : accountNumberList) {
            ShopeeExecutors.bundleDealSyncJob(() -> {
                updateBundleDeal(accountNumber);
            });
        }
        XxlJobLogger.log("-------同步优惠套装结束--------");
        return ReturnT.SUCCESS;
    }

    /**
     * 更新优惠套装活动列表
     *
     * @param accountNumber
     */
    public void updateBundleDeal(String accountNumber) {
        FeedTask insertFeedTask = ShopeeFeedTaskHandleUtil.insertFeedTask(feedTask -> {
            feedTask.setAccountNumber(accountNumber);
            feedTask.setTaskType(ShopeeFeedTaskEnum.SYNC_BUNDLE_DEAL.getValue());
            feedTask.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
            feedTask.setRunTime(new Timestamp(System.currentTimeMillis()));
            feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
            feedTask.setCreatedBy("admin");
        });

        try {
            // 当前店铺下的优惠套装活动id列表
            List<Long> bundleDealIds = new ArrayList<>();

            SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), accountNumber, true);

            List<BundleDealPageV2.TimeStatusEnum> timeStatusEnums = BundleDealPageV2.TimeStatusEnum.syncStatusList();
            for (BundleDealPageV2.TimeStatusEnum timeStatusEnum : timeStatusEnums) {
                int pageNum = 1;
                int pageSize = 1000;
                while (true) {
                    // 获取当前店铺下的优惠套装活动列表
                    BundleDealPageV2 bundleDealPageV2 = new BundleDealPageV2();
                    bundleDealPageV2.setPageNo(pageNum);
                    bundleDealPageV2.setPageSize(pageSize);
                    bundleDealPageV2.setTimeStatus(timeStatusEnum.getCode());
                    ShopeeResponse bundleDealListResponse = ShopeeBundelDealCallV2.getBundleDealList(saleAccount, bundleDealPageV2);
                    if (ObjectUtils.isEmpty(bundleDealListResponse) || StringUtils.isNotBlank(bundleDealListResponse.getError())) {
                        ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(insertFeedTask, ResultStatusEnum.RESULT_FAIL.getStatusCode(), bundleDealListResponse.getMessage());
                        break;
                    }

                    JSONObject responseJSON = JSON.parseObject(bundleDealListResponse.getResponse());
                    List<BundleDealListVov2> bundleDealVoList = JSON.parseObject(responseJSON.getString("bundle_deal_list"), new TypeReference<List<BundleDealListVov2>>() {
                    });
                    if (CollectionUtils.isEmpty(bundleDealVoList)) {
                        ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(insertFeedTask, ResultStatusEnum.RESULT_SUCCESS.getStatusCode(), "同步完成");
                        break;
                    }

                    // 获取优惠套装id列表
                    List<Long> bundleDealIdList = bundleDealVoList.stream().map(BundleDealListVov2::getBundle_deal_id).collect(Collectors.toList());
                    bundleDealIds.addAll(bundleDealIdList);

                    // 根据优惠套装id过滤出数据库中已存在的优惠套装
                    ShopeeMarketingBundleDealExample shopeeMarketingBundleDealExample = new ShopeeMarketingBundleDealExample();
                    ShopeeMarketingBundleDealExample.Criteria criteria = shopeeMarketingBundleDealExample.createCriteria();
                    criteria.andBundleDealIdIn(bundleDealIdList);
                    List<ShopeeMarketingBundleDeal> shopeeMarketingBundleDealList = shopeeMarketingBundleDealService.selectByExample(shopeeMarketingBundleDealExample);

                    // 获取优惠套装id为key，优惠套装对象为value的map
                    Map<Long, ShopeeMarketingBundleDeal> bundleDealMap = shopeeMarketingBundleDealList.stream().collect(Collectors.toMap(ShopeeMarketingBundleDeal::getBundleDealId, t -> t, (t1, t2) -> t1));

                    // 遍历平台优惠套装列表，判断优惠套装是否需要更新
                    for (BundleDealListVov2 bundleDealListVov2 : bundleDealVoList) {
                        // 1、根据优惠套装id获取优惠套装商品列表
                        BundleDealItemV2 bundleDealItemV2 = new BundleDealItemV2();
                        bundleDealItemV2.setBundle_deal_id(bundleDealListVov2.getBundle_deal_id());
                        ShopeeResponse bundleDealItemListResponse = ShopeeBundelDealCallV2.getBundleDealItemList(saleAccount, bundleDealItemV2);
                        if (ObjectUtils.isEmpty(bundleDealItemListResponse) || StringUtils.isNotBlank(bundleDealItemListResponse.getError())) {
                            continue;
                        }

                        JSONObject bundleDealItemListResponseJSON = JSON.parseObject(bundleDealItemListResponse.getResponse());
                        List<ShopeeAddBundleDealtemV2.ItemListDTO> bundleDealItemDTOList = JSON.parseObject(bundleDealItemListResponseJSON.getString("item_list"), new TypeReference<List<ShopeeAddBundleDealtemV2.ItemListDTO>>() {
                        });

                        int productNumber = CollectionUtils.isEmpty(bundleDealItemDTOList) ? 0 : bundleDealItemDTOList.size();
                        ShopeeMarketingBundleDeal shopeeMarketingBundleDeal = bundleDealMap.get(bundleDealListVov2.getBundle_deal_id());
                        if (Objects.isNull(shopeeMarketingBundleDeal)) {
                            // 1、新增优惠套装数据
                            shopeeMarketingBundleDeal = ShopeeMarketingBundleDeal.builder()
                                    .accountNumber(accountNumber)
                                    .productNumber(productNumber)
                                    .bundleDealId(Long.valueOf(bundleDealListVov2.getBundle_deal_id()))
                                    .addStatus(ShopeeMarketingBundleDealAddStatusEnum.ADD_STATUS_PENDING.getCode())
                                    .createdTime(new Timestamp(System.currentTimeMillis()))
                                    .build();
                            updateBundleDeal(shopeeMarketingBundleDeal, bundleDealListVov2);
                            shopeeMarketingBundleDealService.insert(shopeeMarketingBundleDeal);
                        } else {
                            // 2、更新套装详情数据
                            shopeeMarketingBundleDeal.setProductNumber(productNumber);
                            updateBundleDeal(shopeeMarketingBundleDeal, bundleDealListVov2);
                            shopeeMarketingBundleDealService.updateByPrimaryKeySelective(shopeeMarketingBundleDeal);
                        }

                        // 3、如果有新增商品，则添加到数据库
                        List<ShopeeBundleDealProductListing> insertShopeeBundleDealProductListings = bundleDealItemDTOList.stream()
                                .map(item -> {
                                    ShopeeBundleDealProductListing shopeeBundleDealProductListing = new ShopeeBundleDealProductListing();
                                    shopeeBundleDealProductListing.setAccountNumber(accountNumber);
                                    shopeeBundleDealProductListing.setItemId(item.getItem_id());
                                    shopeeBundleDealProductListing.setStatus(item.getStatus());
                                    shopeeBundleDealProductListing.setBundleDealId(bundleDealListVov2.getBundle_deal_id());
                                    shopeeBundleDealProductListing.setCreateTime(new Timestamp(System.currentTimeMillis()));
                                    return shopeeBundleDealProductListing;
                                })
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(insertShopeeBundleDealProductListings)) {
                            // 3.1、删除当前套装商品
                            shopeeBundleDealProductListingMapper.deleteByBundleDealId(shopeeMarketingBundleDeal.getBundleDealId());

                            // 3.2、插入新增商品
                            shopeeBundleDealProductListingMapper.batchInsert(insertShopeeBundleDealProductListings);
                        }
                    }

                    pageNum++;
                }
            }

            // 更新不存在的套装状态
            XxlJobLogger.log("更新不存在的套装状态为：EXPIRED, accountNumber:{}, bundleDealIds:{}", accountNumber, bundleDealIds);
            ShopeeMarketingBundleDeal shopeeMarketingBundleDeal = new ShopeeMarketingBundleDeal();
            shopeeMarketingBundleDeal.setStatus(ShopeeMarketingBundleDealStatusEnum.EXPIRED.getCode());
            ShopeeMarketingBundleDealExample marketingBundleDealExample = new ShopeeMarketingBundleDealExample();
            ShopeeMarketingBundleDealExample.Criteria criteria = marketingBundleDealExample.createCriteria()
                    .andAccountNumberEqualTo(accountNumber);
            if (CollectionUtils.isNotEmpty(bundleDealIds)) {
                criteria.andBundleDealIdNotIn(bundleDealIds);
            }
            shopeeMarketingBundleDealService.updateByExampleSelective(shopeeMarketingBundleDeal, marketingBundleDealExample);

        } catch (Exception e) {
            // 记录处理报告
            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(insertFeedTask, ResultStatusEnum.RESULT_FAIL.getStatusCode(), e.getMessage());
        }
    }

    private void updateBundleDeal(ShopeeMarketingBundleDeal shopeeMarketingBundleDeal, BundleDealListVov2 bundleDealListVov2) {
        Timestamp nowTime = new Timestamp(System.currentTimeMillis());
        shopeeMarketingBundleDeal.setBundleDealStartTime(new Timestamp(bundleDealListVov2.getStart_time() * 1000));
        shopeeMarketingBundleDeal.setBundleDealEndTime(new Timestamp(bundleDealListVov2.getEnd_time() * 1000));
        shopeeMarketingBundleDeal.setName(bundleDealListVov2.getName());
        if (shopeeMarketingBundleDeal.getName().startsWith("组合产品")) {
            shopeeMarketingBundleDeal.setSuitType(ShopeeMarketingBundleDealSuitTypeEnum.COMPOSE.getCode());
        } else {
            shopeeMarketingBundleDeal.setSuitType(ShopeeMarketingBundleDealSuitTypeEnum.NORMAL.getCode());
        }

        if (nowTime.compareTo(shopeeMarketingBundleDeal.getBundleDealStartTime()) < 0) {
            shopeeMarketingBundleDeal.setStatus(ShopeeMarketingBundleDealStatusEnum.NEXT.getCode());
        } else if (nowTime.compareTo(shopeeMarketingBundleDeal.getBundleDealStartTime()) >= 0 && nowTime.compareTo(shopeeMarketingBundleDeal.getBundleDealEndTime()) < 0) {
            shopeeMarketingBundleDeal.setStatus(ShopeeMarketingBundleDealStatusEnum.ONGOING.getCode());
        } else {
            shopeeMarketingBundleDeal.setStatus(ShopeeMarketingBundleDealStatusEnum.EXPIRED.getCode());
        }

        /*设置规则*/
        BundleDealConfigParam bundleDealConfigParam = new BundleDealConfigParam();
        bundleDealConfigParam.setPurchase_limit(bundleDealListVov2.getPurchase_limit());
        ArrayList<BundleDealConfigParam.DealDetail> dealDetailList = Lists.newArrayList();
        if (ObjectUtils.isNotEmpty(bundleDealListVov2.getBundle_deal_rule())) {
            log.info("id:{}, name:{}, rule:{}", bundleDealListVov2.getBundle_deal_id(), bundleDealListVov2.getName(), JSON.toJSONString(bundleDealListVov2.getBundle_deal_rule()));
            BundleDealConfigParam.DealDetail dealDetail = BeanUtil.copyProperties(bundleDealListVov2.getBundle_deal_rule(), BundleDealConfigParam.DealDetail.class);
            dealDetail.setProductNum(bundleDealListVov2.getBundle_deal_rule().getMin_amount());
            if (dealDetail.getRule_type() == null) {
                dealDetail.setRule_type(bundleDealListVov2.getBundle_deal_rule().getRule_type());
            }
            dealDetailList.add(dealDetail);

            shopeeMarketingBundleDeal.setType(bundleDealListVov2.getBundle_deal_rule().getRule_type());

            List<BundleDealListVov2.BundleDealRuleDTO.AdditionalTiersDTO> additionalTiers = bundleDealListVov2.getBundle_deal_rule().getAdditional_tiers();

            if (CollectionUtils.isNotEmpty(additionalTiers)) {
                List<BundleDealConfigParam.DealDetail> dealDetailTempList = additionalTiers.stream().map(t -> {
                    BundleDealConfigParam.DealDetail dealDetail1 = BeanUtil.copyProperties(t, BundleDealConfigParam.DealDetail.class);
                    if (dealDetail1.getRule_type() == null) {
                        dealDetail1.setRule_type(bundleDealListVov2.getBundle_deal_rule().getRule_type());
                    }
                    dealDetail1.setProductNum(t.getMin_amount());
                    return dealDetail1;
                }).collect(Collectors.toList());
                dealDetailList.addAll(dealDetailTempList);
            }
        }
        if (ShopeeMarketingBundleDealSuitTypeEnum.COMPOSE.getCode().equals(shopeeMarketingBundleDeal.getSuitType())) {
            bundleDealConfigParam.setSuitDealDetailList(dealDetailList);
        } else {
            bundleDealConfigParam.setDealDetailList(dealDetailList);
        }
        shopeeMarketingBundleDeal.setRuleJson(JSON.toJSONString(bundleDealConfigParam));
        shopeeMarketingBundleDeal.setSyncTime(nowTime);
    }
}
