package com.estone.erp.publish.shopee.enums;

import lombok.Getter;


public enum ShopeeMarketingBundleDealSuitTypeEnum {

    NORMAL(1, "普通优惠套装"),
    COMPOSE(2, "组合优惠套装");

    @Getter
    private final Integer code;

    @Getter
    private final String desc;

    ShopeeMarketingBundleDealSuitTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String convert(Integer value) {
        if (value == null) {
            return null;
        }
        for (ShopeeMarketingBundleDealSuitTypeEnum item : ShopeeMarketingBundleDealSuitTypeEnum.values()) {
            if (item.getCode().equals(value)) {
                return item.getDesc();
            }
        }
        return null;
    }
}
