package com.estone.erp.publish.shopee.call.v2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.api.v2.param.add.DescriptionInfoV2;
import com.estone.erp.publish.shopee.api.v2.param.listing.GetItemBaseInfoV2;
import com.estone.erp.publish.shopee.enums.ShopeeDescriptionFiledTypeEnum;
import com.estone.erp.publish.shopee.enums.ShopeeDescriptionTypeEnum;
import com.estone.erp.publish.shopee.enums.ShopeeListingDataSourceEnum;
import com.estone.erp.publish.shopee.util.ShopeeDescriptionUtil;
import com.estone.erp.publish.shopee.util.ShopeeHttpUtils;
import com.estone.erp.publish.shopee.util.ShopeeSkuUtil;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 获取产品的基本信息
 */
@Slf4j
public class ShopeeGetItemBaseInfoCallV2 {


    /**
     *  获取产品的基础信息
     * @param account
     * @param itemIdList    最多50个id
     * @return
     */
    public static ApiResult<List<EsShopeeItem>> getItemBaseInfo(SaleAccountAndBusinessResponse account, List<String> itemIdList) {
        if(CollectionUtils.isEmpty(itemIdList)){
            return ApiResult.newError("itemId 为空!");
        }

        List<EsShopeeItem> itemList = new ArrayList<>();
        ApiResult<List<EsShopeeItem>> result = ApiResult.newError(null);

        GetItemBaseInfoV2 param = new GetItemBaseInfoV2();
        List<Long> ids = itemIdList.stream().map(o -> Long.valueOf(o)).collect(Collectors.toList());
        param.setItemIdList(ids);

        ShopeeResponse response = ShopeeHttpUtils.doGetV2(account, param);
        int tryN = 2;
        while ("product.error_server".equalsIgnoreCase(response.getError()) && tryN-- > 0){
            //重试：服务器异常
            response = ShopeeHttpUtils.doGetV2(account, param);
        }
        if(StringUtils.isNotBlank(response.getError())){
            result.setErrorMsg(JSON.toJSONString(response));
//            log.info(account.getAccountNumber() + ",获取Listing error :" + JSON.toJSONString(response));
            return result;
        }else{
            JSONObject jsonObject = JSON.parseObject(response.getResponse());
            if(jsonObject != null && jsonObject.size() > 0){
                JSONArray item_list = jsonObject.getJSONArray("item_list");
                if(item_list == null){
                    result.setErrorMsg("结果解析为空: "+JSON.toJSONString(response));
                }else{
                    //解析数据
                    for (int i = 0; i < item_list.size(); i++) {
                        JSONObject jsonItem = item_list.getJSONObject(i);
                        try {
                            EsShopeeItem item = transBaseItem(account, jsonItem);
                            if(item != null){
                                itemList.add(item);
                            }
                        }catch (Exception e){
                            log.error("解析transBaseItem 出错：", e);
                        }
                    }
                }
            }
        }

        result.setResult(itemList);
        result.setSuccess(true);
        return result;
    }

    private static EsShopeeItem transBaseItem(SaleAccountAndBusinessResponse account, JSONObject jsonItem) {
        EsShopeeItem item = new EsShopeeItem();
        item.setItemId(jsonItem.getString("item_id"));
        item.setId(item.getItemId());
        item.setItemSeller(account.getAccountNumber());
        item.setSite(account.getAccountSite());
        item.setHasVariation(jsonItem.getBoolean("has_model"));
        item.setIsFather(true);
        // 判断是否是商品 父sku且有变体 则本身不是商品
        if(BooleanUtils.isTrue(item.getHasVariation())) {
            item.setIsGoods(false);
        } else {
            item.setIsGoods(true);
        }
        item.setItemStatus(jsonItem.getString("item_status"));
        //如果产品状态是 DELETED 过滤掉
//        if("DELETED".equalsIgnoreCase(item.getItemStatus())){
//            return null;
//        }
        item.setShopId(Integer.valueOf(account.getMarketplaceId()));
//                    item.setShopId(jsonItem.getInteger("shopid"));
        //判断itemSku 是否是mtsku 生成的
        item.setItemSku(jsonItem.getString("item_sku").trim());
        item.setArticleNumber(item.getItemSku().toUpperCase());
        if (StringUtils.isNotBlank(item.getArticleNumber())) {
            // 判断是否是以 FJ041- 作为前缀的，是的话去掉
            boolean b = item.getArticleNumber().startsWith("FJ041-");
            if (b) {
                String s = item.getArticleNumber().replaceFirst("FJ041-", "");
                item.setArticleNumber(s);
            }
        }
        //默认普通listing
        item.setDataSource(ShopeeListingDataSourceEnum.ORDINARY.name());
        if(item.getArticleNumber().contains("_")){
            item.setArticleNumber(ShopeeSkuUtil.spliMpsku(item.getArticleNumber()));
            //包含下滑线就为mpsku
//            item.setDataSource(ShopeeListingDataSourceEnum.MPSKU.name());
        }
        // 去除前后特殊字符
        item.setArticleNumber(StrUtil.strDeldSpecialChar(item.getArticleNumber()));
        //解析spu
        item.setSpu(ShopeeSkuUtil.spliSpu(item.getArticleNumber()));

        item.setName(jsonItem.getString("item_name"));
        JSONArray price_infos = jsonItem.getJSONArray("price_info");
        if(price_infos != null && price_infos.size() > 0){
            JSONObject price_info = price_infos.getJSONObject(0);
            item.setCurrency(price_info.getString("currency"));
            item.setPrice(price_info.getDouble("current_price"));
            item.setOriginalPrice(price_info.getDouble("original_price"));
            item.setSipItemPrice(price_info.getDouble("sip_item_price"));
        }
//        JSONArray stock_infos = jsonItem.getJSONArray("stock_info");
//        for (int i = 0; stock_infos != null && i < stock_infos.size(); i++) {
//            JSONObject stock_info = stock_infos.getJSONObject(i);
//            //1：Shopee仓库库存
//            //2：卖方库存
//            if(stock_info.getInteger("stock_type") == 2){
//                item.setStock(stock_info.getInteger("normal_stock"));
//            }
//        }
//        if(stock_infos != null && stock_infos.size() > 0){
//            JSONObject stock_info = stock_infos.getJSONObject(0);
////            item.setStock(stock_info.getInteger("current_stock"));
//            item.setStock(stock_info.getInteger("normal_stock"));
//        }
        JSONObject stock_info_v2 = jsonItem.getJSONObject("stock_info_v2");
        if(null != stock_info_v2) {
            JSONObject summary_info = stock_info_v2.getJSONObject("summary_info");
            if(null != summary_info) {
                item.setStock(summary_info.getInteger("total_available_stock"));
            }
        }
        item.setWeight(jsonItem.getDouble("weight"));
        item.setCategoryId(jsonItem.getInteger("category_id"));
        // /api/v2/product/get_item_extra_info 接口去获取  ShopeeGetItemExtraInfoCallV2
//                    item.setRatingStar(jsonItem.getDouble("rating_star"));
//                    item.setCmtCount(jsonItem.getInteger("comment_count"));
//                    item.setSales(jsonItem.getInteger("sale"));
//                    item.setViews(jsonItem.getInteger("views"));
//                    item.setLikes(jsonItem.getInteger("likes"));
        JSONObject dimension = jsonItem.getJSONObject("dimension");
        if(dimension != null){
            //单位为CM
            item.setPackageLength(dimension.getDouble("package_length"));
            item.setPackageWidth(dimension.getDouble("package_width"));
            item.setPackageHeight(dimension.getDouble("package_height"));
        }
        JSONObject pre_order = jsonItem.getJSONObject("pre_order");
        if(pre_order != null){
            item.setDaysToShip(pre_order.getInteger("days_to_ship"));
        }
        item.setItemCondition(jsonItem.getString("condition"));
//                    item.setIs2tierItem(jsonItem.getBoolean("is_2tier_item"));
        //促销编号
        Long itemDiscountId = jsonItem.getLong("promotion_id");
        item.setDiscountId(itemDiscountId);
        // 可能存在无变体数据 需要提前设置是否有折扣 有变体数据变体获取时候会重新设置
        if(null != itemDiscountId && itemDiscountId > 0) {
            item.setItemHasDiscount(true);
        } else {
            item.setItemHasDiscount(false);
        }
        item.setWholesales(jsonItem.getString("wholesales"));
        item.setLogistics(jsonItem.getString("logistic_info"));
        item.setAttributes(jsonItem.getString("attribute_list"));
        JSONObject image = jsonItem.getJSONObject("image");
        if(image != null){
            item.setImages(image.getString("image_url_list"));
        }

        String descriptionType = jsonItem.getString("description_type");
        if (StringUtils.isNotBlank(descriptionType) && descriptionType.equals(ShopeeDescriptionTypeEnum.EXTENDED.getCode())) {
            JSONObject jsonObject = jsonItem.getJSONObject("description_info");
            DescriptionInfoV2 descriptionInfoV2 = jsonObject.toJavaObject(DescriptionInfoV2.class);
            DescriptionInfoV2.ExtendInfo extendedDescription = descriptionInfoV2.getExtendedDescription();
            if (extendedDescription != null) {
                List<DescriptionInfoV2.FiledInfo> fieldList = extendedDescription.getFieldList();
                String description = ShopeeDescriptionUtil.filedInfoToString(fieldList);
                item.setDescription(description);
                List<DescriptionInfoV2.DescriptionInfoImage> collect = fieldList.stream()
                        .filter(a -> a.getFieldType().equals(ShopeeDescriptionFiledTypeEnum.IMAGE.getCode()))
                        .map(DescriptionInfoV2.FiledInfo::getImageInfo)
                        .filter(Objects::nonNull).collect(Collectors.toList());
                if (!collect.isEmpty()) {
                    item.setDescImgMapping(JSON.toJSONString(collect));
                }
            }
            item.setDescriptionType(ShopeeDescriptionTypeEnum.EXTENDED.getCode());
        } else {
            item.setDescriptionType(ShopeeDescriptionTypeEnum.NORMAL.getCode());
            item.setDescription(jsonItem.getString("description"));
            item.setDescImgMapping(null);
        }

        JSONObject brandJson = jsonItem.getJSONObject("brand");
        if(null != brandJson) {
            item.setOriginalBrandName(brandJson.getString("original_brand_name"));
            item.setBrandId(brandJson.getString("brand_id"));
        }
        //shopee接口返回来的时间戳单位是秒，不是毫秒，要进行转换
        item.setUploadDate(new Timestamp(jsonItem.getLong("create_time") * 1000));
        item.setModifyDate(new Timestamp(jsonItem.getLong("update_time") * 1000));
        item.setCreationDate(new Date()); // 创建时间 存在则会被已存在数据覆盖
        item.setSyncDate(new Date());

        item.setCreatedBy(StrConstant.ADMIN);
        item.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        item.setLastUpdatedBy(StrConstant.ADMIN);
        return item;
    }

}
