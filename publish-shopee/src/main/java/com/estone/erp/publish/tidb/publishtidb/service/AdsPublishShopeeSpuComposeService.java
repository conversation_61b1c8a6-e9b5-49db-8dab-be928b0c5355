package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.publish.tidb.publishtidb.model.AdsPublishShopeeSpuCompose;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface AdsPublishShopeeSpuComposeService extends IService<AdsPublishShopeeSpuCompose> {
    /**
     * 分页查询
     * @param query
     * @return
     */
    CQueryResult<AdsPublishShopeeSpuCompose> queryPage(CQuery<AdsPublishShopeeSpuCompose> query);
}
