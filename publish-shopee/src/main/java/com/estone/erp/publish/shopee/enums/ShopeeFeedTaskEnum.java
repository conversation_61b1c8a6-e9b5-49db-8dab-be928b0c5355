package com.estone.erp.publish.shopee.enums;

import lombok.Getter;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

public enum ShopeeFeedTaskEnum {
    PRODUCT_PUBLISH("product_publish", "产品刊登"),
    SYNC_LISTING("sync_listing", "同步Listing"),
    UPDATE_STOCK("update_stock", "修改库存"),
    UPDATE_PRICE("update_price", "修改价格"),
    DELETE_ITEM("delete_item", "删除产品"),
    DELETE_MODEL("delete_model", "删除子SKU"),
    START_ITEM("start_item", "上架产品"),
    END_ITEM("end_item", "下架产品"),
    UPDATE_TITLE("update_title", "更新标题"),
    UPDATE_DESC("update_desc", "更新描述"),
    UPDATE_IMAGE("update_image", "修改主图附图"),
    UPDATE_SKU_IMAGE("update_sku_image", "修改子SKU图片"),
    UPDATE_ITEM_ATTRIBUTES("update_item_attributes", "修改属性"),
    GLOBAL_PRODUCT_PUBLISH("global_product_publish", "mtsku产品刊登"),
    UPDATE_SIP_ITEM_PRICE("update_sip_item_price", "修改sip成本价"),
    PUSH_DISCOUNT("push_discount", "创建折扣活动"),
    ADD_DISCOUNT_ITEM("add_discount_item", "添加折扣商品"),
    DELETE_DISCOUNT("delete_discount", "删除折扣"),
    END_DISCOUNT("end_discount", "结束折扣活动"),
    SYNC_DISCOUNT("sync_discount", "同步折扣活动"),
    SYNC_DISCOUNT_ITEM("sync_discount_item", "同步折扣商品"),
    UPDATE_DISCOUNT("update_discount", "修改折扣"),
    UPDATE_GLOBAL_ITEM_VIDEO("update_global_item_video", "上传全球商品视频"),
    UPDATE_DAYS_TO_SHIP("update_days_to_ship", "修改发货天数"),
    ADD_FOLLOW_PRIZE("add_follow_prize", "添加店铺关注礼"),
    SYNC_FOLLOW_PRIZE("sync_follow_prize", "同步店铺关注礼"),
    ADD_FLASH_PRIZE("add_flash_prize", "创建秒杀活动"),
    ADD_CAMPAIGN("add_campaign", "参与官方商品活动"),
    ADD_VOUCHER("add_voucher", "添加店铺优惠券"),
    SYNC_VOUCHER("sync_voucher", "同步店铺优惠券"),
    END_VOUCHER("end_voucher", "结束店铺优惠券"),
    /**
     * 同步优惠套装
     */
    SYNC_BUNDLE_DEAL("SYNC_BUNDLE_DEAL", "同步优惠套装"),
    /**
     * 创建优惠套装
     */
    CREATE_BUNDLE_DEAL("CREATE_BUNDLE_DEAL", "创建优惠套装"),
    /**
     * 优惠套装添加商品
     */
    ADD_BUNDLE_DEAL_ITEM("ADD_BUNDLE_DEAL_ITEM", "优惠套装添加商品"),
    STOP_FLASH_PRIZE("STOP_FLASH_PRIZE", "停止秒杀活动"),
    STOP_VOUCHER("STOP_VOUCHER", "停止优惠券"),
    STOP_FOLLOW_PRIZE("STOP_FOLLOW_PRIZE", "停止关注礼"),
    STOP_BUNDLE_DEAL("STOP_BUNDLE_DEAL", "停止套装"),

    SYNC_SIZE_CHART("SYNC_SIZE_CHART", "同步关联尺码表模版"),
    UPDATE_SIZE_CHART("UPDATE_SIZE_CHART", "更新关联尺码表模版"),
    CANCEL_SIZE_CHART("CANCEL_SIZE_CHART", "取消关联尺码表模版"),
    /**
     * 创建优惠套装
     */
    CREATE_COMPOSE_BUNDLE_DEAL("CREATE_COMPOSE_BUNDLE_DEAL", "创建组合优惠套装"),
    ADD_COMPOSE_BUNDLE_DEAL_ITEM("ADD_COMPOSE_BUNDLE_DEAL_ITEM", "组合优惠套装添加商品"),

    ;

    static List<ShopeeFeedTaskEnum> MARKETING_TASK_ENUM = new ArrayList<>();
    static {
        MARKETING_TASK_ENUM.add(ADD_BUNDLE_DEAL_ITEM);
        MARKETING_TASK_ENUM.add(ADD_VOUCHER);
        MARKETING_TASK_ENUM.add(ADD_CAMPAIGN);
        MARKETING_TASK_ENUM.add(ADD_FLASH_PRIZE);
        MARKETING_TASK_ENUM.add(ADD_FOLLOW_PRIZE);
        MARKETING_TASK_ENUM.add(PUSH_DISCOUNT);
    }

    @Getter
    private String value;

    @Getter
    private String desc;

    ShopeeFeedTaskEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 判断是否为市场营销 任务
     *
     * @param value
     * @return
     */
    public static boolean isMarketingAddTask(String value) {
        if (StringUtils.isBlank(value)) {
            return false;
        }
        for (ShopeeFeedTaskEnum shopeeFeedTaskEnum : MARKETING_TASK_ENUM) {
            if (shopeeFeedTaskEnum.getValue().equals(value)) {
                return true;
            }
        }
        return false;
    }

    public static String convert(String value) {
        for (ShopeeFeedTaskEnum shopeeFeedTaskEnum : ShopeeFeedTaskEnum.values()) {
            if (shopeeFeedTaskEnum.getValue().equals(value)) {
                return shopeeFeedTaskEnum.getDesc();
            }
        }
        return "";
    }
}
