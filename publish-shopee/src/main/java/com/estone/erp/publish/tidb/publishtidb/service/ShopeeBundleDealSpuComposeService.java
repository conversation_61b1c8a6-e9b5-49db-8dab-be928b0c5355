package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealSpuCompose;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;

/**
 * <p>
 * 组合套装数据集 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface ShopeeBundleDealSpuComposeService extends IService<ShopeeBundleDealSpuCompose> {
    /**
     * 分页查询
     * @param query
     * @return
     */
    CQueryResult<ShopeeBundleDealSpuCompose> queryPage(CQuery<ShopeeBundleDealSpuCompose> query);
}
