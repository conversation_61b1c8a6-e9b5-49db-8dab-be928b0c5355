package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.mapper.ShopeeBundleDealProductListingMapper;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListing;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListingCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListingExample;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeBundleDealProductListingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * 2024-06-18 18:41:23
 */
@Service("shopeeBundleDealProductListingService")
@Slf4j
public class ShopeeBundleDealProductListingServiceImpl implements ShopeeBundleDealProductListingService {
    @Resource
    private ShopeeBundleDealProductListingMapper shopeeBundleDealProductListingMapper;

    @Override
    public int countByExample(ShopeeBundleDealProductListingExample example) {
        Assert.notNull(example, "example is null!");
        return shopeeBundleDealProductListingMapper.countByExample(example);
    }

    @Override
    public CQueryResult<ShopeeBundleDealProductListing> search(CQuery<ShopeeBundleDealProductListingCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        ShopeeBundleDealProductListingCriteria query = cquery.getSearch();
        ShopeeBundleDealProductListingExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = shopeeBundleDealProductListingMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<ShopeeBundleDealProductListing> shopeeBundleDealProductListings = shopeeBundleDealProductListingMapper.selectByExample(example);
        // 组装结果
        CQueryResult<ShopeeBundleDealProductListing> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(shopeeBundleDealProductListings);
        return result;
    }

    @Override
    public ShopeeBundleDealProductListing selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return shopeeBundleDealProductListingMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ShopeeBundleDealProductListing> selectByExample(ShopeeBundleDealProductListingExample example) {
        Assert.notNull(example, "example is null!");
        return shopeeBundleDealProductListingMapper.selectByExample(example);
    }

    @Override
    public int insert(ShopeeBundleDealProductListing record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return shopeeBundleDealProductListingMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ShopeeBundleDealProductListing record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return shopeeBundleDealProductListingMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(ShopeeBundleDealProductListing record, ShopeeBundleDealProductListingExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return shopeeBundleDealProductListingMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return shopeeBundleDealProductListingMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public void batchInsert(List<ShopeeBundleDealProductListing> bundleDealProductListings) {
        if (CollectionUtils.isEmpty(bundleDealProductListings)) {
            return;
        }
        shopeeBundleDealProductListingMapper.batchInsert(bundleDealProductListings);
    }
}