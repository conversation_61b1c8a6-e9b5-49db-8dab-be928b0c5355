package com.estone.erp.publish.shopee.jobHandler.bundledeal;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingBundleDealStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingBundleDealSuitTypeEnum;
import com.estone.erp.publish.shopee.model.*;
import com.estone.erp.publish.shopee.service.ShopeeMarketingBundleDealService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 优惠套装添加商品
 * <AUTHOR>
 * @Date 2024/10/8 上午11:31
 */
@Slf4j
@Component
public class ShopeeAddBundleDealItemJobHandlerV2 extends AbstractJobHandler {

    @Resource
    private RabbitMqSender rabbitMqSender;

    @Autowired
    private ShopeeMarketingBundleDealService shopeeMarketingBundleDealService;

    public ShopeeAddBundleDealItemJobHandlerV2() {
        super(ShopeeAddBundleDealItemJobHandlerV2.class.getName());
    }

    @Override
    @XxlJob("ShopeeAddBundleDealItemJobHandlerV2")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("-------优惠套装添加商品job开始-------");

        // 查询出所有未结束的优惠套装
        ShopeeMarketingBundleDealExample shopeeMarketingBundleDealExample = new ShopeeMarketingBundleDealExample();
        ShopeeMarketingBundleDealExample.Criteria criteria = shopeeMarketingBundleDealExample.createCriteria();
        criteria.andStatusIn(Lists.newArrayList(ShopeeMarketingBundleDealStatusEnum.NEXT.getCode(), ShopeeMarketingBundleDealStatusEnum.ONGOING.getCode())).andSuitTypeEqualTo(ShopeeMarketingBundleDealSuitTypeEnum.NORMAL.getCode());
        List<ShopeeMarketingBundleDeal> shopeeMarketingBundleDealList = shopeeMarketingBundleDealService.selectByExample(shopeeMarketingBundleDealExample);

        // 根据店铺分组
        Map<String, List<ShopeeMarketingBundleDeal>> shopeeMarketingBundleDealMap = shopeeMarketingBundleDealList.stream().collect(Collectors.groupingBy(ShopeeMarketingBundleDeal::getAccountNumber));
        shopeeMarketingBundleDealMap.forEach((account, bundleDealList) -> {
            if (CollectionUtils.isNotEmpty(bundleDealList)) {
                // 根据优惠套装添加商品
                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("account", account);
                dataMap.put("bundleDealList", bundleDealList);
                rabbitMqSender.publishShopeeVHostRabbitTemplateSend(PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_ADD_BUNDLE_DEAL_ITEM_KEY, JSON.toJSON(dataMap));
            }
        });

        XxlJobLogger.log("-------优惠套装添加商品job结束--------");
        return ReturnT.SUCCESS;
    }

}
