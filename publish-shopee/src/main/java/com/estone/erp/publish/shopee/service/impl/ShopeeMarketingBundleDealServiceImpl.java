package com.estone.erp.publish.shopee.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.shopee.dto.ShopeeMarketingBundleDealDetailsDTO;
import com.estone.erp.publish.shopee.dto.ShopeeMarketingBundleDealVO;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingBundleDealStatusEnum;
import com.estone.erp.publish.shopee.mapper.ShopeeMarketingBundleDealMapper;
import com.estone.erp.publish.shopee.model.*;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingBundleDealService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingConfigService;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListing;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListingExample;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeBundleDealProductListingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * 2024-06-18 18:53:27
 */
@Service("shopeeMarketingBundleDealService")
@Slf4j
public class ShopeeMarketingBundleDealServiceImpl implements ShopeeMarketingBundleDealService {

    @Resource
    private ShopeeMarketingBundleDealMapper shopeeMarketingBundleDealMapper;

    @Autowired
    private ShopeeBundleDealProductListingService shopeeBundleDealProductListingService;

    @Autowired
    private ShopeeAccountConfigService shopeeAccountConfigService;

    @Resource
    private ShopeeMarketingConfigService shopeeMarketingConfigService;

    @Resource
    private ShopeeAccountConfigService accountConfigService;

    @Override
    public int countByExample(ShopeeMarketingBundleDealExample example) {
        Assert.notNull(example, "example is null!");
        return shopeeMarketingBundleDealMapper.countByExample(example);
    }

    @Override
    public CQueryResult<ShopeeMarketingBundleDealVO> search(CQuery<ShopeeMarketingBundleDealCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        ShopeeMarketingBundleDealCriteria query = cquery.getSearch();
        ShopeeMarketingBundleDealExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = shopeeMarketingBundleDealMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<ShopeeMarketingBundleDealVO> shopeeMarketingBundleDealsVO = shopeeMarketingBundleDealMapper.selectByExample(example).stream().map(marketingBundleDeal -> BeanUtil.copyProperties(marketingBundleDeal, ShopeeMarketingBundleDealVO.class)).collect(Collectors.toList());
        //设置规则名称和分组名称
        List<String> accountList = shopeeMarketingBundleDealsVO.stream().map(ShopeeMarketingBundleDealVO::getAccountNumber).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<Integer> collect = shopeeMarketingBundleDealsVO.stream().map(ShopeeMarketingBundleDealVO::getConfigId)
                .filter(Objects::nonNull)
                .map(Long::intValue)
                .collect(Collectors.toList());
        Map<Integer, String> marketingNameMap = shopeeMarketingConfigService.getRuleNameById(collect);
        Map<String, String> accountGroupNameMap = shopeeAccountConfigService.getAccountAndGroupName(accountList);
        for (ShopeeMarketingBundleDealVO vo : shopeeMarketingBundleDealsVO) {
            String accountGroupName = accountGroupNameMap.get(vo.getAccountNumber());
            vo.setGroupName(accountGroupName);
            Long configId = vo.getConfigId();
            if (configId != null) {
                vo.setRuleName(marketingNameMap.get(configId.intValue()));
            }
        }
        if (CollectionUtils.isNotEmpty(shopeeMarketingBundleDealsVO)) {
            // 获取店铺配置信息
            List<String> accountNumberList = shopeeMarketingBundleDealsVO.stream().map(ShopeeMarketingBundleDealVO::getAccountNumber).collect(Collectors.toList());
            ShopeeAccountConfigExample shopeeAccountConfigExample = new ShopeeAccountConfigExample();
            shopeeAccountConfigExample.createCriteria().andAccountIn(accountNumberList);
            List<ShopeeAccountConfig> shopeeAccountConfigs = shopeeAccountConfigService.selectByExample(shopeeAccountConfigExample);
            Map<String, ShopeeAccountConfig> shopeeAccountMap = shopeeAccountConfigs.stream().collect(Collectors.toMap(ShopeeAccountConfig::getAccount, t -> t, (t1, t2) -> t1));

            // 获取店铺销售人员信息
            Map<String, Triple<String, String, String>> salesmanAccountDetailMap = NewUsermgtUtils.getSaleSuperiorMap(accountNumberList, SaleChannel.CHANNEL_SHOPEE);


            // 获取启用数量
            List<Long> bundleDealIds = shopeeMarketingBundleDealsVO.stream().map(ShopeeMarketingBundleDealVO::getBundleDealId).collect(Collectors.toList());
            ShopeeBundleDealProductListingExample dealProductListingExample = new ShopeeBundleDealProductListingExample();
            dealProductListingExample.createCriteria().andBundleDealIdIn(bundleDealIds).andStatusEqualTo(1);
            List<ShopeeBundleDealProductListing> shopeeBundleDealProductListings = shopeeBundleDealProductListingService.selectByExample(dealProductListingExample);
            Map<Long, Long> bundleDealIdCountMap = shopeeBundleDealProductListings.stream().collect(Collectors.groupingBy(ShopeeBundleDealProductListing::getBundleDealId, Collectors.counting()));

            shopeeMarketingBundleDealsVO.forEach(vo -> {
                ShopeeAccountConfig shopeeAccountConfig = shopeeAccountMap.get(vo.getAccountNumber());
                if (ObjectUtils.isNotEmpty(shopeeAccountConfig)) {
                    vo.setMerchantId(shopeeAccountConfig.getMerchantId());
                    vo.setMerchantName(shopeeAccountConfig.getMerchant());
                    vo.setSite(shopeeAccountConfig.getSite());
                }

                if (MapUtils.isNotEmpty(salesmanAccountDetailMap)) {
                    Triple<String, String, String> saleSuperiorTriple = salesmanAccountDetailMap.get(vo.getAccountNumber());
                    if (saleSuperiorTriple != null) {
                        vo.setSalesman(saleSuperiorTriple.getLeft());
                        vo.setSalesTeamLeader(saleSuperiorTriple.getMiddle());
                        vo.setSalesSupervisorName(saleSuperiorTriple.getRight());
                    }
                }
                vo.setStartCount(
                        Optional.ofNullable(bundleDealIdCountMap.get(vo.getBundleDealId())).orElse(0L).intValue()
                );
                vo.setStatusStr(ShopeeMarketingBundleDealStatusEnum.convert(vo.getStatus()));

            });
        }

        // 组装结果
        CQueryResult<ShopeeMarketingBundleDealVO> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(shopeeMarketingBundleDealsVO);
        return result;
    }

    @Override
    public ShopeeMarketingBundleDeal selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return shopeeMarketingBundleDealMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ShopeeMarketingBundleDeal> selectByExample(ShopeeMarketingBundleDealExample example) {
        Assert.notNull(example, "example is null!");
        return shopeeMarketingBundleDealMapper.selectByExample(example);
    }

    @Override
    public int insert(ShopeeMarketingBundleDeal record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return shopeeMarketingBundleDealMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ShopeeMarketingBundleDeal record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return shopeeMarketingBundleDealMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(ShopeeMarketingBundleDeal record, ShopeeMarketingBundleDealExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return shopeeMarketingBundleDealMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return shopeeMarketingBundleDealMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public List<ShopeeMarketingBundleDeal> getUpcomingOrOngoingVoucherBeforeSyncTime(String account, Timestamp time) {
        ShopeeMarketingBundleDealExample example = new ShopeeMarketingBundleDealExample();
        example.createCriteria().andAccountNumberEqualTo(account)
                .andStatusIn(List.of(ShopeeMarketingBundleDealStatusEnum.NEXT.getCode(), ShopeeMarketingBundleDealStatusEnum.ONGOING.getCode()));
        return selectByExample(example);
    }

    @Override
    public List<Long> getNotExpireBundleId(String accountNumber) {
        return shopeeMarketingBundleDealMapper.getNotExpireBundleId(accountNumber);
    }

    @Override
    public List<ShopeeMarketingBundleDealDetailsDTO> getT1ExecutionDetailsByAccountNumber(List<String> accountNumbers) {
        List<CompletableFuture<ShopeeMarketingBundleDealDetailsDTO>> completableFutures = accountNumbers.stream()
                .map(account -> CompletableFuture.supplyAsync(() -> {
                            ShopeeMarketingBundleDealDetailsDTO t1ExecutionDetailsByAccountNumber = shopeeMarketingBundleDealMapper.getT1ExecutionDetailsByAccountNumber(account);
                            if (ObjectUtils.isEmpty(t1ExecutionDetailsByAccountNumber)) {
                                return null;
                            }
                    ShopeeAccountConfig accountConfig = accountConfigService.getAccountConfigCache(account);
                    t1ExecutionDetailsByAccountNumber.setTotalNum(0);
                    if (ObjectUtils.isNotEmpty(accountConfig)) {
                        t1ExecutionDetailsByAccountNumber.setTotalNum(Optional.ofNullable(accountConfig.getItemCount()).orElseGet(() -> 0));
                    }
                    int failNum = t1ExecutionDetailsByAccountNumber.getTotalNum() - t1ExecutionDetailsByAccountNumber.getSuccessNum();
                    t1ExecutionDetailsByAccountNumber.setFailNum(Math.max(failNum, 0));
                            return t1ExecutionDetailsByAccountNumber;
                        }, ShopeeExecutors.SHOPEE_STATISTICS_DETAILS_POOL)
                ).collect(Collectors.toList());
        // 等待所有任务完成
        return completableFutures.stream()
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    /**
     * 获取物流ID与批次商品映射
     *
     * @param esShopeeItemList
     * @param logisticsId
     * @return
     */
    @Override
    public Map<Integer, List<EsShopeeItem>> getLogisticsGroupsByItemsMap(List<EsShopeeItem> esShopeeItemList, Integer logisticsId) {
        Map<Integer, List<EsShopeeItem>> logisticGroups = new HashMap<>();
        for (EsShopeeItem item : esShopeeItemList) {
            // 获取商品支持的物流 ID 列表
            if (ObjectUtils.isEmpty(item.getLogistics())) {
                continue;
            }
            JSONArray logisticsArray = JSON.parseArray(item.getLogistics());
            Set<Integer> logisticIds = logisticsArray.stream()
                    .filter(o -> ((JSONObject) o).getBoolean("enabled"))
                    .map(o -> ((JSONObject) o).getInteger("logistic_id"))
                    .collect(Collectors.toSet());
            if (!logisticIds.contains(logisticsId)) {
                continue;
            }

            // 物流ID的分组
            logisticGroups.putIfAbsent(logisticsId, new ArrayList<>());

            // 将当前商品添加到该分组
            logisticGroups.get(logisticsId).add(item);
        }
        return logisticGroups;
    }

    /**
     * 获取商品列表物流ID和名称映射
     *
     * @param esShopeeItemList
     * @return
     */
    @Override
    public Map<Integer, String> getLogisticsIdAndNameMapByItemList(List<EsShopeeItem> esShopeeItemList) {
        // 生成最终的映射
        Map<Integer, String> resultMap = new LinkedHashMap<>();

        // 提取物流 ID 和名称，并累加序号
        Map<Integer, String> logisticIdNameMap = esShopeeItemList.stream()
                .flatMap(item -> {
                    // 获取商品支持的物流 ID 列表
                    if (ObjectUtils.isEmpty(item.getLogistics())) {
                        return Stream.empty();
                    }
                    JSONArray logisticsArray = JSON.parseArray(item.getLogistics());
                    return logisticsArray.stream()
                            .filter(o -> ((JSONObject) o).getBoolean("enabled"))
                            .map(o -> Map.entry(((JSONObject) o).getInteger("logistic_id"), ((JSONObject) o).getString("logistic_name")));
                })
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (name1, name2) -> name1
                ));

        // 创建记录首字母计数的 Map
        Map<String, Integer> prefixCounter = new HashMap<>();
        logisticIdNameMap.forEach((id, name) -> {
            // 获取首字母
            String prefix = name.substring(0, 1).toUpperCase();

            // 获取当前首字母的计数
            int count = prefixCounter.getOrDefault(prefix, 0) + 1;

            // 更新计数器
            prefixCounter.put(prefix, count);

            // 拼接首字母和序号
            String finalName = prefix + count;

            // 存入结果 Map
            resultMap.put(id, finalName);
        });
        return resultMap;
    }

}