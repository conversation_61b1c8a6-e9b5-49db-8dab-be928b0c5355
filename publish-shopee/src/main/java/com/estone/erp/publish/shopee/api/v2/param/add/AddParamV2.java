package com.estone.erp.publish.shopee.api.v2.param.add;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.common.constant.ShopeeDaysToShipConstant;
import com.estone.erp.common.util.GoogleTranslateUtils;
import com.estone.erp.publish.common.util.NumberUtils;
import com.estone.erp.publish.shopee.api.constant.ShopeeApiV2Constant;
import com.estone.erp.publish.shopee.api.param.item.add.ShopeeImageParam;
import com.estone.erp.publish.shopee.api.param.item.add.ShopeeLogisticParam;
import com.estone.erp.publish.shopee.api.v2.common.RequestCommon;
import com.estone.erp.publish.shopee.bo.ShopeeSku;
import com.estone.erp.publish.shopee.enums.ShopeeCountryEnum;
import com.estone.erp.publish.shopee.model.ShopeeAttributeV2;
import com.estone.erp.publish.shopee.model.ShopeeTemplateNew;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/5/12 17:35
 * @description
 */
@Getter
@Setter
public class AddParamV2 implements RequestCommon{

    /** Item price*/
    @JSONField(name = "original_price")
    private Double originalPrice ;

    /** Description of item*/
    @JSONField(name = "description")
    private String description;

    /** Weight of item*/
    @JSONField(name = "weight")
    private Double weight;

    /** 标题*/
    @JSONField(name = "item_name")
    private String itemName;

    /** Item status, could be UNLIST or NORMAL*/
    @JSONField(name = "item_status")
    private String itemStatus;

    /** Item dimension 如果这个字段不为空，那就是必填*/
    @JSONField(name = "dimension")
    private DimensionDtoV2 dimension;

    @JSONField(name = "seller_stock")
    private List<ShopeeSellerStockV2> sellerStocks;

    /** 物流渠道设置 */
    @JSONField(name = "logistic_info")
    private List<ShopeeLogisticParam> logisticInfo;

    /** 根据不同类别下的特定属性，此字段是可选的（印度尼西亚）*/
    @JSONField(name = "attribute_list")
    private List<ShopeeAttributeDtoV2> attributeList;

    /** 类别编号*/
    @JSONField(name = "category_id")
    private Integer categoryId ;

    /** 图片 */
    @JSONField(name = "image")
    private ImageDtoV2 image = new ImageDtoV2();

    /** 预购设定 */
    @JSONField(name = "pre_order")
    private PreOrderDtoV2 preOrder;

    /** sku */
    @JSONField(name = "item_sku")
    private String itemSku;

    /** Condition of item, could be USED or NEW */
    @JSONField(name = "condition")
    private String condition;

    /** 批发设置 */
    @JSONField(name = "wholesale")
    private List<WholesaleDtoV2> wholesale;

    /** 从视频上传API返回的视频上传ID。 最多只能接受一个 */
    @JSONField(name = "video_upload_id")
    private List<String> videoUploadId;

    /** 品牌 */
    @JSONField(name = "brand")
    private BrandDtoV2 brand;

    @JSONField(name = "size_chart_info")
    private ShopeeSizeChartInfoParamV2 sizeChartInfo;

    public AddParamV2(SaleAccountAndBusinessResponse account, ShopeeTemplateNew template, Map<String, String> imgMappingMap) {
        String accountSiteCode = account.getAccountSite();

        //类目id
        this.categoryId = template.getCategoryId();

        //属性
        if (StringUtils.isNotEmpty(template.getAttributesStr())) {
            List<ShopeeAttributeV2> attributes = JSONArray.parseArray(template.getAttributesStr(), ShopeeAttributeV2.class);
            if(CollectionUtils.isNotEmpty(attributes)){
                this.attributeList = new ArrayList<>(attributes.size());
                for (ShopeeAttributeV2 attr : attributes) {
                    ShopeeAttributeDtoV2 attParam = new ShopeeAttributeDtoV2();
                    attParam.setAttributeId(attr.getAttributeId());
                    String attrValueListStr = attr.getAttributeValueList();
                    JSONArray jsonArray = JSON.parseArray(attrValueListStr);
                    for (int i = 0; jsonArray != null && i < jsonArray.size(); i++) {
                        JSONObject valueJson = jsonArray.getJSONObject(i);
                        ShopeeAttributeValueDtoV2 value = JSON.parseObject(valueJson.toJSONString(), new TypeReference<ShopeeAttributeValueDtoV2>() {
                        });
                        attParam.getAttributeValueList().add(value);

                        //如果存在父子结构 也要设置属性,如果有多个只取一个就可以
                        JSONArray parentArray = valueJson.getJSONArray("parent_attribute_list");
                        if(parentArray != null && parentArray.size() > 0){
                            JSONObject parentJson = parentArray.getJSONObject(0);
                            Integer parent_attribute_id = parentJson.getInteger("parent_attribute_id");
                            Integer parent_value_id = parentJson.getInteger("parent_value_id");
                            ShopeeAttributeDtoV2 parentAttParam = new ShopeeAttributeDtoV2();
                            parentAttParam.setAttributeId(parent_attribute_id);
                            ShopeeAttributeValueDtoV2 parentValue = new ShopeeAttributeValueDtoV2();
                            parentValue.setValueId(parent_value_id);
                            parentAttParam.getAttributeValueList().add(parentValue);
                            if (CollectionUtils.isNotEmpty(attParam.getAttributeValueList())) {
                                this.attributeList.add(parentAttParam);
                            }
                        }
                    }
                    if (CollectionUtils.isNotEmpty(attParam.getAttributeValueList())) {
                        this.attributeList.add(attParam);
                    }
                }
            }
        }

        //发货天数
        // 保证发货天数。预订时，请输入7到30之间的值；对于非预购商品，请排除此字段，该字段将默认为您所在商店的相应标准值。（例如，CrossBorder为2）
        int defaultDayToShip =  template.getDaysToShip() == null ? ShopeeDaysToShipConstant.DEFAULT_SHOPEE_DAYS_TO_SHIP : template.getDaysToShip();
        boolean isPreOrder = ShopeeDaysToShipConstant.isPreOrder(defaultDayToShip);
        if(isPreOrder){
            this.preOrder = new PreOrderDtoV2();
            this.preOrder.setDaysToShip(defaultDayToShip);
            this.preOrder.setIsPreOrder(isPreOrder);
        }


        //图片，shopee会对重复的图片做删除处理，如果主图和某张附图重复会有一定概率导致主图被删，所以这里要去掉和主图重复的附图
        List<ShopeeImageParam> imgList = JSONArray.parseArray(template.getImagesStr(), ShopeeImageParam.class);
        List<ShopeeImageParam> deleteImageList = new ArrayList<>();
        for (int i = 1; i < imgList.size(); i++) {
            if(imgList.get(i).getUrl().equals(imgList.get(0).getUrl())) {
                deleteImageList.add(imgList.get(i));
            }
        }
        imgList.removeAll(deleteImageList);
        if (imgList.size() > 9) {
            imgList = new ArrayList<>(imgList.subList(0, 9));
        }
        //转换图片，把图片转换成阿里云的地址
        for (ShopeeImageParam image : imgList) {
            String url = image.getUrl();
            //替换图片为 image_id
            url = imgMappingMap.get(url);
            this.image.getImageIdList().add(url);
        }


        // 主货号
        this.itemSku = "FJ041-" + template.getSku();

        // 物流
        this.logisticInfo = JSONArray.parseArray(template.getLogisticsStr(),ShopeeLogisticParam.class);
        if(CollectionUtils.isNotEmpty(logisticInfo)){
            logisticInfo.stream().forEach(o -> o.setSite(null));
        }

        // 台湾站点用中文标题和描述
//        String keyword = StringUtils.isEmpty(template.getKeyword())?"":template.getKeyword() + "\n";
        String itemName = GoogleTranslateUtils.googleTranslateText(template.getName(), "en", GoogleTranslateUtils.changeDestLang(accountSiteCode));
        this.itemName = Optional.ofNullable(itemName).orElse(template.getName());
        String description = GoogleTranslateUtils.googleTranslateText(template.getDescription(), "en", GoogleTranslateUtils.changeDestLang(accountSiteCode));
        this.description = Optional.ofNullable(description).orElse(template.getDescription());
        //标题不能超过255个字符
        if(this.itemName.length() > 255) {
            this.itemName = this.itemName.substring(0,255);
        }
        //如果是台湾的限制60长度
        if(ShopeeCountryEnum.Taiwan.getCode().equalsIgnoreCase(template.getSite()) && this.itemName.length() > 60){
            this.itemName = this.itemName.substring(0, 60);
        }

        //重量：1.舍弃小数点    2.个位数向上取整（如果是0就不用向上取整）   3.再加10g
        double maxWeight = 0;
        List<ShopeeSku> shopeeSkus = JSONArray.parseArray(template.getShopeeSkusStr(), ShopeeSku.class);
        for (ShopeeSku shopeeSku : shopeeSkus) {
            if(shopeeSku.getShippingWeight() != null) {
                if(shopeeSku.getShippingWeight() > maxWeight) {
                    maxWeight = shopeeSku.getShippingWeight();
                }
            }
        }
        Double shippingWeightKg = maxWeight;
        Double shippingWeightG = maxWeight * 1000;
        //个位数的值
        int units = shippingWeightG.intValue() % 10;
        if(units == 0) {
            this.weight = NumberUtils.format(shippingWeightKg + 0.01);
        } else{
            this.weight = NumberUtils.format(shippingWeightKg + 0.02 - Double.parseDouble("0.00" + units));
        }


        //如果不是多属性产品的话，直接在这里设置价格
        if (shopeeSkus.size() == 1 && StringUtils.equalsIgnoreCase(shopeeSkus.get(0).getSku(), template.getSku())) {
            ShopeeSku sku = shopeeSkus.get(0);
            this.originalPrice = sku.getPrice();
            this.sellerStocks = List.of(ShopeeSellerStockV2.builder().stock(sku.getQuantity()).build());
        }
        else {
            //多维度属性需要另外的接口设置，但是stock和price在这个接口是必须的，所以这里必须设置一个值
            //因为越南站点和印尼站点的货币面值较大，所以这两个站点的价格要设置大一点
            if(ShopeeCountryEnum.Vietnam.getCode().equalsIgnoreCase(accountSiteCode)
                    || ShopeeCountryEnum.Indonesia.getCode().equalsIgnoreCase(accountSiteCode)
                    || ShopeeCountryEnum.Colombia.getCode().equalsIgnoreCase(accountSiteCode)
                    || ShopeeCountryEnum.Chile.getCode().equalsIgnoreCase(accountSiteCode)) {
                this.originalPrice = 999000.0;
            } else {
                this.originalPrice = 999.0;
            }
            this.sellerStocks = List.of(ShopeeSellerStockV2.builder().stock(999).build());
        }

        // 不做批发价
//         this.wholesale = JSON.parseObject(template.getWholesale(), new TypeReference<List<WholesaleDtoV2>>(){});

        //长宽高
        this.dimension = JSON.parseObject(template.getDimension(), new TypeReference<DimensionDtoV2>() {
        });

        //品牌
        this.brand = JSON.parseObject(template.getBrand(), new TypeReference<BrandDtoV2>(){});
        if(this.brand == null){
//            this.brand = "";
        }

        // 尺码表
        if (StringUtils.isNotEmpty(template.getSizeChartInfo())) {
            ShopeeSizeChartInfoParamV2 shopeeSizeChartInfoParamV2 = JSON.parseObject(template.getSizeChartInfo(), new TypeReference<ShopeeSizeChartInfoParamV2>() {
            });
            if (StringUtils.isNotBlank(shopeeSizeChartInfoParamV2.getSizeChart())) {
                String imageId = imgMappingMap.get(shopeeSizeChartInfoParamV2.getSizeChart());
                shopeeSizeChartInfoParamV2.setSizeChart(imageId);
            }
            this.sizeChartInfo = shopeeSizeChartInfoParamV2;
        }
    }


    @Override
    public String requestUrl() {
        return ShopeeApiV2Constant.ADD_ITEM;
    }
}
