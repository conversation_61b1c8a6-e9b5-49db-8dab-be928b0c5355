package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealSpuComposeMapping;
import com.baomidou.mybatisplus.extension.service.IService;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;

/**
 * <p>
 * SPU组合套装映射 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface ShopeeBundleDealSpuComposeMappingService extends IService<ShopeeBundleDealSpuComposeMapping> {
    /**
     * 分页查询
     * @param query
     * @return
     */
    CQueryResult<ShopeeBundleDealSpuComposeMapping> queryPage(CQuery<ShopeeBundleDealSpuComposeMapping> query);
}
