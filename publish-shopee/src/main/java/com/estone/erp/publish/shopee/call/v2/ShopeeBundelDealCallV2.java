package com.estone.erp.publish.shopee.call.v2;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.common.util.DynamicLimiter;
import com.estone.erp.publish.common.util.RetryUtil;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.api.v2.param.bundledeal.*;
import com.estone.erp.publish.shopee.component.marking.BundleDealConfigParam;
import com.estone.erp.publish.shopee.enums.ShopeeBundleDealTypeEnum;
import com.estone.erp.publish.shopee.model.ShopeeMarketingBundleDeal;
import com.estone.erp.publish.shopee.util.ShopeeHttpUtils;
import com.estone.erp.publish.shopee.util.ShopeeMarketingUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListing;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/5/25 16:42
 * @description 折扣操作辅助类
 */
@Slf4j
public class ShopeeBundelDealCallV2 {

    /**
     * 添加优惠套装
     */
    public static ShopeeResponse addBundelDeal(String logisticName, SaleAccountAndBusinessResponse account, BundleDealConfigParam param, Timestamp startTimeStamp, ShopeeMarketingBundleDeal.ShopeeMarketingBundleDealBuilder bundleDealBuilder, Timestamp nowTime) {
        for (BundleDealConfigParam.DealDetail dealDetail : param.getDealDetailList()) {
            if (ShopeeBundleDealTypeEnum.FIX_PRICE.getCode().equals(dealDetail.getRule_type())) {
                Double transformFixPrice = ShopeeMarketingUtils.calculationsExchangeRate(account.getAccountSite(), BigDecimal.valueOf(dealDetail.getFix_price()), Collections.emptyList());
                dealDetail.setFix_price(transformFixPrice.floatValue());
            } else if (ShopeeBundleDealTypeEnum.DISCOUNT_VALUE.getCode().equals(dealDetail.getRule_type())) {
                Double transformDiscountValue = ShopeeMarketingUtils.calculationsExchangeRate(account.getAccountSite(), BigDecimal.valueOf(dealDetail.getDiscount_value()), Collections.emptyList());
                dealDetail.setDiscount_value(transformDiscountValue.floatValue());
            }
        }
        ShopeeAddBundleDealV2 shopeeAddBundleDealV2 = BeanUtil.copyProperties(param.getDealDetailList().get(0), ShopeeAddBundleDealV2.class);
        shopeeAddBundleDealV2.setMin_amount(param.getDealDetailList().get(0).getProductNum());

        shopeeAddBundleDealV2.setStart_time(startTimeStamp.getTime() / 1000);
        LocalDateTime endLocalDateTime = startTimeStamp.toLocalDateTime().plusMonths(param.getDuration());
        shopeeAddBundleDealV2.setEnd_time(Timestamp.valueOf(endLocalDateTime).getTime() / 1000);
        shopeeAddBundleDealV2.setName(logisticName + "-" + account.getAccountNumber() + "-" + startTimeStamp.toLocalDateTime().format(DateTimeFormatter.ofPattern("MM-dd-yyyy")));
        // 设置名称长度
        shopeeAddBundleDealV2.setName(shopeeAddBundleDealV2.getName().length() > 25 ? shopeeAddBundleDealV2.getName().substring(0, 25) : shopeeAddBundleDealV2.getName());
        shopeeAddBundleDealV2.setPurchase_limit(param.getPurchase_limit());

        if (param.getDealDetailList().size() > 1) {
            List<BundleDealConfigParam.DealDetail> dealDetails = param.getDealDetailList().subList(1, param.getDealDetailList().size());

            List<ShopeeAddBundleDealV2.AdditionalTiersDTO> additionalTiersDTOList = dealDetails.stream().map(t -> {
                ShopeeAddBundleDealV2.AdditionalTiersDTO additionalTiersDTO = BeanUtil.copyProperties(t, ShopeeAddBundleDealV2.AdditionalTiersDTO.class);
                additionalTiersDTO.setMin_amount(t.getProductNum());
                return additionalTiersDTO;
            }).collect(Collectors.toList());

            shopeeAddBundleDealV2.setAdditional_tiers(additionalTiersDTOList);
        }

        ShopeeResponse shopeeResponse = ShopeeHttpUtils.doPostV2(account, shopeeAddBundleDealV2);
        shopeeResponse.setPushParam(shopeeAddBundleDealV2);

        bundleDealBuilder.name(shopeeAddBundleDealV2.getName())
                .type(param.getDealDetailList().get(0).getRule_type())
                .status(0)
                .bundleDealStartTime(startTimeStamp)
                .bundleDealEndTime(Timestamp.valueOf(endLocalDateTime))
                .syncTime(nowTime)
                .createdTime(nowTime);

        return shopeeResponse;
    }


    /**
     * 套装添加商品
     * @param saleAccount
     * @param shopeeBundleDealProductListings
     * @return void
     * <AUTHOR>
     * @date 2024/6/20 9:15
     */
    public static ShopeeResponse addBundelDealItem(SaleAccountAndBusinessResponse saleAccount, List<ShopeeBundleDealProductListing> shopeeBundleDealProductListings, ShopeeMarketingBundleDeal shopeeMarketingBundleDeal) {
        // 接口限流处理
        String merchantId = Optional.ofNullable(saleAccount.getColStr1()).orElse("1234567");
        DynamicLimiter.getInstance(String.format("SHOPEE-BUNDLE-DEAL-ITEM-ADD-%s", merchantId), 0.5d).acquire();

        return RetryUtil.doRetry(() -> {
            try {
                // 构建请求参数
                ShopeeAddBundleDealtemV2 shopeeAddBundleDealtemV2 = new ShopeeAddBundleDealtemV2();
                shopeeAddBundleDealtemV2.setBundle_deal_id(shopeeMarketingBundleDeal.getBundleDealId());
                List<ShopeeAddBundleDealtemV2.ItemListDTO> itemListDTOList = shopeeBundleDealProductListings.stream().map(t -> {
                    ShopeeAddBundleDealtemV2.ItemListDTO itemListDTO = new ShopeeAddBundleDealtemV2.ItemListDTO();
                    itemListDTO.setItem_id(t.getItemId());
                    itemListDTO.setStatus(1);
                    return itemListDTO;
                }).collect(Collectors.toList());

                shopeeAddBundleDealtemV2.setItem_list(itemListDTOList);

                ShopeeResponse shopeeResponse = ShopeeHttpUtils.doPostV2(saleAccount, shopeeAddBundleDealtemV2);
                shopeeResponse.setPushParam(shopeeAddBundleDealtemV2);
                if (StringUtils.isNotBlank(shopeeResponse.getError())
                        && (shopeeResponse.getError().contains("error_rate_limit")
                        || shopeeResponse.getError().contains("error_unknown"))) {
                    throw new BusinessException(JSON.toJSONString(shopeeResponse));
                }

                return shopeeResponse;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, 3);

    }

    public static ShopeeResponse deleteBundleDealItem(SaleAccountAndBusinessResponse saleAccount, ShopeeDeleteBundleDealtemV2 shopeeDeleteBundleDealtemV2) {
        ShopeeResponse shopeeResponse = ShopeeHttpUtils.doPostV2(saleAccount, shopeeDeleteBundleDealtemV2);
        return shopeeResponse;
    }

    /**
     * 结束套装
     * @param saleAccount
     * @return void
     */
    public static ShopeeResponse endBundelDealItem(SaleAccountAndBusinessResponse saleAccount,ShopeeEndBundleDealtemV2 shopeeEndBundleDealtemV2) {
        ShopeeResponse shopeeResponse = ShopeeHttpUtils.doPostV2(saleAccount, shopeeEndBundleDealtemV2);
        return shopeeResponse;
    }

    public static ShopeeResponse getBundleDealList(SaleAccountAndBusinessResponse account, BundleDealPageV2 pageV2) {
        ShopeeResponse shopeeResponse = ShopeeHttpUtils.doGetV2(account, pageV2);
        shopeeResponse.setPushParam(pageV2);
        return shopeeResponse;
    }

    public static ShopeeResponse getBundleDealItemList(SaleAccountAndBusinessResponse saleAccount, BundleDealItemV2 bundleDealItemV2) {
        ShopeeResponse shopeeResponse = ShopeeHttpUtils.doGetV2(saleAccount, bundleDealItemV2);
        return shopeeResponse;
    }

    public static ShopeeResponse deleteBundleDeal(SaleAccountAndBusinessResponse saleAccount, ShopeeDeleteBundleDealV2 deleteBundleDealV2) {
        ShopeeResponse shopeeResponse = ShopeeHttpUtils.doPostV2(saleAccount, deleteBundleDealV2);
        return shopeeResponse;
    }


    public static ShopeeResponse updateBundelDealItem(SaleAccountAndBusinessResponse saleAccount, List<ShopeeBundleDealProductListing> shopeeBundleDealProductListings, ShopeeMarketingBundleDeal shopeeMarketingBundleDeal) {
        ShopeeUpdateBundleDealtemV2 shopeeUpdateBundleDealtemV2 = new ShopeeUpdateBundleDealtemV2();
        shopeeUpdateBundleDealtemV2.setBundle_deal_id(shopeeMarketingBundleDeal.getBundleDealId());
        List<ShopeeUpdateBundleDealtemV2.ItemListDTO> itemListDTOList = shopeeBundleDealProductListings.stream().map(t -> {
            ShopeeUpdateBundleDealtemV2.ItemListDTO itemListDTO = new ShopeeUpdateBundleDealtemV2.ItemListDTO();
            itemListDTO.setItem_id(t.getItemId());
            itemListDTO.setStatus(1);
            return itemListDTO;
        }).collect(Collectors.toList());

        shopeeUpdateBundleDealtemV2.setItem_list(itemListDTOList);

        ShopeeResponse shopeeResponse = ShopeeHttpUtils.doPostV2(saleAccount, shopeeUpdateBundleDealtemV2);
        shopeeResponse.setPushParam(shopeeUpdateBundleDealtemV2);
        return shopeeResponse;

    }

    public static ShopeeResponse getBundleDealDetail(SaleAccountAndBusinessResponse response, BundleDealDetailV2 detailV2) {
        return ShopeeHttpUtils.doGetV2(response, detailV2);
    }

    public static ShopeeResponse addComposeBundelDeal(String composeBundelDealName, SaleAccountAndBusinessResponse account, BundleDealConfigParam param, Timestamp startTimeStamp, ShopeeMarketingBundleDeal.ShopeeMarketingBundleDealBuilder bundleDealBuilder, Timestamp nowTime) {
        for (BundleDealConfigParam.DealDetail dealDetail : param.getSuitDealDetailList()) {
            if (ShopeeBundleDealTypeEnum.FIX_PRICE.getCode().equals(dealDetail.getRule_type())) {
                Double transformFixPrice = ShopeeMarketingUtils.calculationsExchangeRate(account.getAccountSite(), BigDecimal.valueOf(dealDetail.getFix_price()), Collections.emptyList());
                dealDetail.setFix_price(transformFixPrice.floatValue());
            } else if (ShopeeBundleDealTypeEnum.DISCOUNT_VALUE.getCode().equals(dealDetail.getRule_type())) {
                Double transformDiscountValue = ShopeeMarketingUtils.calculationsExchangeRate(account.getAccountSite(), BigDecimal.valueOf(dealDetail.getDiscount_value()), Collections.emptyList());
                dealDetail.setDiscount_value(transformDiscountValue.floatValue());
            }
        }
        ShopeeAddBundleDealV2 shopeeAddBundleDealV2 = BeanUtil.copyProperties(param.getSuitDealDetailList().get(0), ShopeeAddBundleDealV2.class);
        shopeeAddBundleDealV2.setMin_amount(param.getSuitDealDetailList().get(0).getProductNum());

        shopeeAddBundleDealV2.setStart_time(startTimeStamp.getTime() / 1000);
        LocalDateTime endLocalDateTime = startTimeStamp.toLocalDateTime().plusMonths(param.getDuration());
        shopeeAddBundleDealV2.setEnd_time(Timestamp.valueOf(endLocalDateTime).getTime() / 1000);
        shopeeAddBundleDealV2.setName(composeBundelDealName);
        // 设置名称长度
        shopeeAddBundleDealV2.setName(shopeeAddBundleDealV2.getName().length() > 25 ? shopeeAddBundleDealV2.getName().substring(0, 25) : shopeeAddBundleDealV2.getName());
        shopeeAddBundleDealV2.setPurchase_limit(param.getPurchase_limit());

        if (param.getSuitDealDetailList().size() > 1) {
            List<BundleDealConfigParam.DealDetail> dealDetails = param.getSuitDealDetailList().subList(1, param.getSuitDealDetailList().size());

            List<ShopeeAddBundleDealV2.AdditionalTiersDTO> additionalTiersDTOList = dealDetails.stream().map(t -> {
                ShopeeAddBundleDealV2.AdditionalTiersDTO additionalTiersDTO = BeanUtil.copyProperties(t, ShopeeAddBundleDealV2.AdditionalTiersDTO.class);
                additionalTiersDTO.setMin_amount(t.getProductNum());
                return additionalTiersDTO;
            }).collect(Collectors.toList());

            shopeeAddBundleDealV2.setAdditional_tiers(additionalTiersDTOList);
        }

        ShopeeResponse shopeeResponse = ShopeeHttpUtils.doPostV2(account, shopeeAddBundleDealV2);
        shopeeResponse.setPushParam(shopeeAddBundleDealV2);

        bundleDealBuilder.name(shopeeAddBundleDealV2.getName())
                .type(param.getSuitDealDetailList().get(0).getRule_type())
                .status(0)
                .bundleDealStartTime(startTimeStamp)
                .bundleDealEndTime(Timestamp.valueOf(endLocalDateTime))
                .syncTime(nowTime)
                .createdTime(nowTime);

        return shopeeResponse;
    }
}
