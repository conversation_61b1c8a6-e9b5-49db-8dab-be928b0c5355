package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.MqUtil;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSkuBindRequest;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.bo.ShopeeGlobalTemplateBo;
import com.estone.erp.publish.shopee.bo.ShopeeSku;
import com.estone.erp.publish.shopee.model.ShopeeGlobalTemplateExample;
import com.estone.erp.publish.shopee.mq.model.ShopeeProductSpSkuMSg;
import com.estone.erp.publish.shopee.mq.model.ShopeeProductSpSonSku;
import com.estone.erp.publish.shopee.service.ShopeeGlobalTemplateService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/25 11:30
 * @description 试卖sku 产品系统转正常推送 监听队列
 */
@Slf4j
@Component
public class ShopeeProductMqReceiver {


    @Resource
    private ShopeeGlobalTemplateService shopeeGlobalTemplateService;
    @Resource
    private EsSkuBindService esSkuBindService;
    @Resource
    private RabbitMqSender rabbitMqSender;

    //@RabbitListener(queues = PublishQueues.PRODUCT_CHANGE_SALE_SKU_2_PUBLISH_SHOPEE_QUEUE, containerFactory = "shopeeMqManualFactory")
    public void updateStockFromOrder(Message message, Channel channel) throws UnsupportedEncodingException {

        String body = new String(message.getBody(), "UTF-8");
        if (StringUtils.isBlank(body)) {
            return;
        }
        try {
            Boolean isSuccess = this.doService(body);

            if(isSuccess) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }else {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            }
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            MqUtil.dealFailMq(message, channel, body);
        }
    }

    public Boolean doService(String body) {
        ShopeeProductSpSkuMSg productSpSkuMSg = null;
        try {
            productSpSkuMSg = JSON.parseObject(body,ShopeeProductSpSkuMSg.class);
            if (null == productSpSkuMSg || StringUtils.isEmpty(productSpSkuMSg.getMainSku()) || CollectionUtils.isEmpty(productSpSkuMSg.getSkuList())) {
                return false;
            }
        }catch(Exception e) {
            log.error("解析mq消息体异常 -> {}", body);
        }


        try {
            if (ObjectUtils.isEmpty(productSpSkuMSg)){
                return false;
            }
            String mainSku = productSpSkuMSg.getMainSku();
            List<ShopeeProductSpSonSku> productSpSkuMSgSkuList = productSpSkuMSg.getSkuList();
            Map<String, String> saleSkuAndSonSkuMap = productSpSkuMSgSkuList.stream()
                    .filter(o->StringUtils.isNotBlank(o.getSaleSku()))
                    .collect(Collectors.toMap(o -> o.getSaleSku(), o ->o.getSonSku(), (k1,k2)->k1));

            EsSkuBindRequest request = new EsSkuBindRequest();
            request.setSkuDataSource(SkuDataSourceEnum.ERP_DATA_SYSTEM.getCode());
            request.setPlatform(Platform.Shopee.name());
            request.setSkus(new ArrayList<>(saleSkuAndSonSkuMap.keySet()));
            List<EsSkuBind> esSkuBinds = esSkuBindService.getEsSkuBinds(request);
            if(CollectionUtils.isNotEmpty(esSkuBinds)) {
                for (EsSkuBind esSkuBind : esSkuBinds) {
                    String sku = esSkuBind.getSku();
                    // 兼容大写
                    String sonSku = saleSkuAndSonSkuMap.get(sku);
                    esSkuBind.setSystemSku(sonSku);
                    esSkuBind.setMainSku(mainSku);
                }
                esSkuBindService.saveAll(esSkuBinds, Platform.Shopee.name());

                rabbitMqSender.send(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE,
                        PublishMqConfig.PUBLISH_AMAZON_SKUBIND_2_SALE_ORDER_QUEUE, JSON.toJSON(esSkuBinds));
            }

            //更新范本
//            this.updateSku(productSpSkuMSg);

        }catch(Exception e) {
            log.error("试卖转正常消费出错：", e);
        }

        return true;
    }

    /**
     * 修改为系统sku
     * @param productSpSkuMSg
     */
    public void updateSku(ShopeeProductSpSkuMSg productSpSkuMSg) {
        if(null == productSpSkuMSg || CollectionUtils.isEmpty(productSpSkuMSg.getSkuList())) {
            return;
        }

        List<ShopeeProductSpSonSku> productSpSonSkus = productSpSkuMSg.getSkuList();
        Map<String, String> skuMap = productSpSonSkus.stream().collect(Collectors.toMap(ShopeeProductSpSonSku::getSaleSku,ShopeeProductSpSonSku::getSonSku));
        String mainSku = productSpSkuMSg.getMainSku();

        // 修改试卖sku为系统sku
        ShopeeGlobalTemplateExample updateSkuexample = new ShopeeGlobalTemplateExample();
         updateSkuexample.createCriteria()
                 .andSkuEqualTo(mainSku)
                 .andDataSourceEqualTo(SkuDataSourceEnum.ERP_DATA_SYSTEM.getCode())
                 .andIsParentEqualTo(true);

        List<ShopeeGlobalTemplateBo> templateBos = shopeeGlobalTemplateService.selectByExample(updateSkuexample);
        for (ShopeeGlobalTemplateBo template : templateBos) {
            List<ShopeeSku> shopeeSkus = template.parseShopeeSkus();
            if (CollectionUtils.isEmpty(shopeeSkus)) {
                continue;
            }

            for (ShopeeSku itemVariation : shopeeSkus) {
                String sonSku = itemVariation.getSku();
                if (StringUtils.isNotBlank(sonSku)) {
                    // 商品编号换成系统sku
                    String systemSku = skuMap.get(sonSku);
                    if(StringUtils.isNotBlank(systemSku)) {
                        itemVariation.setSku(systemSku);
                    }
                }
            }

            ShopeeGlobalTemplateBo newTemplate = new ShopeeGlobalTemplateBo();
            newTemplate.setId(template.getId());
            newTemplate.setShopeeSkusStr(JSON.toJSONString(shopeeSkus));
            shopeeGlobalTemplateService.updateByPrimaryKeySelective(newTemplate);
        }
    }
}
