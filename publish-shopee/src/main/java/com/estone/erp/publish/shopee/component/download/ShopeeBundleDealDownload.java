package com.estone.erp.publish.shopee.component.download;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.shopee.component.download.template.ShopeeDownloadService;
import com.estone.erp.publish.shopee.dto.ExcelShopeeMarketingBundleDeal;
import com.estone.erp.publish.shopee.dto.ShopeeMarketingBundleDealVO;
import com.estone.erp.publish.shopee.model.ShopeeMarketingBundleDealCriteria;
import com.estone.erp.publish.shopee.service.ShopeeMarketingBundleDealService;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * shopee 套装优惠
 */
@Component
public class ShopeeBundleDealDownload extends ShopeeDownloadService {

    @Resource
    private ShopeeMarketingBundleDealService marketingBundleDealService;

    @Override
    public ShopeeDownloadTypeEnums downloadType() {
        return ShopeeDownloadTypeEnums.BUNDLE_DEAL_RECORD;
    }

    @Override
    public void download(ExcelDownloadLog downloadLog, File temFile) {
        DataContextHolder.setUsername(downloadLog.getCreateBy());
        ShopeeMarketingBundleDealCriteria shopeeMarketingBundleDealCriteria = JSON.parseObject(downloadLog.getQueryCondition(), ShopeeMarketingBundleDealCriteria.class);
        CQuery<ShopeeMarketingBundleDealCriteria> cquery = new CQuery<>();
        cquery.setSearch(shopeeMarketingBundleDealCriteria);
        ExcelWriter excelWriter = EasyExcel.write(temFile, ExcelShopeeMarketingBundleDeal.class).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();

        int pageIndex = 0;
        int pageSize = 200;
        int total = 0;
        Set<String> accountNumberSet = new HashSet<>();
        while (true) {
            cquery.setOffset(pageIndex * pageSize);
            cquery.setLimit(pageSize);
            CQueryResult<ShopeeMarketingBundleDealVO> search = marketingBundleDealService.search(cquery);
            List<ShopeeMarketingBundleDealVO> resultList = search.getRows();
            if (CollectionUtils.isEmpty(resultList)) {
                break;
            }
            List<ExcelShopeeMarketingBundleDeal> excelShopeeMarketingBundleDeals = BeanUtil.copyBeanList(resultList, ExcelShopeeMarketingBundleDeal.class);
            excelShopeeMarketingBundleDeals.forEach(bundleDeal -> {
                bundleDeal.setSource(Objects.nonNull(bundleDeal.getConfigId()) ? "系统创建" : "销售创建");
                bundleDeal.setSuitTypeStr(Objects.nonNull(bundleDeal.getSuitType()) ? bundleDeal.getSuitType() == 1 ? "普通优惠套装" : "组合优惠套装" : "");

            });

            Set<String> collect = excelShopeeMarketingBundleDeals.stream().map(ExcelShopeeMarketingBundleDeal::getAccountNumber).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
            accountNumberSet.addAll(collect);
            total += excelShopeeMarketingBundleDeals.size();
            excelWriter.write(excelShopeeMarketingBundleDeals, writeSheet);
            pageIndex++;
            if (resultList.size() < pageSize) {
                break;
            }
        }

        excelWriter.finish();
        downloadLog.setDownloadCount(total);
        downloadLog.setAccountNumber(accountNumberSet.stream().limit(50).collect(Collectors.joining(",")));
    }
}
