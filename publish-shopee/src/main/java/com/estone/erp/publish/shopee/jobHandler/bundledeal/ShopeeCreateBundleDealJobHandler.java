package com.estone.erp.publish.shopee.jobHandler.bundledeal;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeItemService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.call.v2.ShopeeBundelDealCallV2;
import com.estone.erp.publish.shopee.component.marking.BundleDealConfigParam;
import com.estone.erp.publish.shopee.enums.*;
import com.estone.erp.publish.shopee.jobHandler.marketing.ShopeeMarketingVoucherGenTaskJob;
import com.estone.erp.publish.shopee.mapper.ShopeeAccountConfigMapper;
import com.estone.erp.publish.shopee.model.*;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.estone.erp.publish.shopee.service.ShopeeConfigTaskService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingBundleDealService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingConfigService;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListing;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListingExample;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListingTask;
import com.estone.erp.publish.tidb.publishtidb.service.IShopeeBundleDealProductListingTaskService;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeBundleDealProductListingService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生成优惠套装
 *
 * <AUTHOR>
 * @date 2024/6/18 16:13
 */
@Slf4j
@Component
public class ShopeeCreateBundleDealJobHandler extends AbstractJobHandler {
    @Resource
    private EsShopeeItemService esShopeeItemService;
    @Autowired
    private ShopeeAccountConfigMapper shopeeAccountConfigMapper;
    @Autowired
    private ShopeeAccountConfigService shopeeAccountConfigService;
    @Autowired
    private ShopeeBundleDealProductListingService shopeeBundleDealProductListingService;
    @Autowired
    private IShopeeBundleDealProductListingTaskService shopeeBundleDealProductListingTaskService;
    @Autowired
    private ShopeeMarketingBundleDealService shopeeMarketingBundleDealService;
    @Resource
    private ShopeeMarketingConfigService shopeeMarketingConfigService;
    @Resource
    private ShopeeConfigTaskService shopeeConfigTaskService;


    public ShopeeCreateBundleDealJobHandler() {
        super("ShopeeCreateBundleDealJobHandler");
    }

    @Data
    public static class InnerParam {
        private List<String> accountNumberList;
        private List<Integer> configIdList;
    }

    @Override
    @XxlJob("ShopeeCreateBundleDealJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("-------创建优惠套装job开始--------");
        ShopeeMarketingVoucherGenTaskJob.InnerParam innerParam = Optional.ofNullable(passParam(param, ShopeeMarketingVoucherGenTaskJob.InnerParam.class)).orElseGet(ShopeeMarketingVoucherGenTaskJob.InnerParam::new);
        List<String> paramAccountList = innerParam.getAccountNumberList();
        List<Integer> configIdList = innerParam.getConfigIdList();

        // 获取到要跑的配置了
        Date currentDate = new Date();
        List<ShopeeMarketingConfig> runningConfigByDateHours = shopeeMarketingConfigService.getRunningConfigByDateHours(currentDate, ShopeeMarketingConfigTypeEnum.BUNDLE_DEAL);
        if (CollectionUtils.isNotEmpty(configIdList)) {
            runningConfigByDateHours = runningConfigByDateHours.stream().filter(o -> configIdList.contains(o.getId())).collect(Collectors.toList());
        }

        for (ShopeeMarketingConfig marketingConfig : runningConfigByDateHours) {
            ShopeeExecutors.createBundleDealJob(() -> {
                // 根据配置生成优惠套装
                syncTask(marketingConfig, paramAccountList);
            });
        }

        XxlJobLogger.log("-------创建优惠套装job结束--------");
        return ReturnT.SUCCESS;
    }

    /**
     * 同步任务
     *
     * @param config
     * @param limitationAccountNumberList
     */
    private void syncTask(ShopeeMarketingConfig config, List<String> limitationAccountNumberList) {
        XxlJobLogger.log("优惠套装配置配置：{}创建优惠套装开始执行", config);
        Timestamp nowTime = new Timestamp(new Date().getTime());
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();

        List<String> accounts = shopeeConfigTaskService.filterExistingAccountList(config.getAccounts(), ShopeeConfigTypeEnum.BUNDLE_DEAL, config.getId());
        if (CollectionUtils.isNotEmpty(limitationAccountNumberList)) {
            accounts.retainAll(limitationAccountNumberList);
        }

        for (String account : accounts) {
            try {
                XxlJobLogger.log("单个店铺：{}创建优惠套装开始执行", account);

                // 判断是否是SIP店铺
                SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), account, true);
                if (BooleanUtils.isTrue(saleAccount.getColBool2())) {
                    XxlJobLogger.log("{}为SIP店铺，不做处理", saleAccount.getAccountNumber());
                    continue;
                }

                // shopee_marketing_bundle_deal表会存拉取/创建的优惠套装数据，判断是否创建了，一段时间只有一条(进行中或者未开始)填报记录
                ShopeeMarketingBundleDealExample shopeeMarketingBundleDealExample = new ShopeeMarketingBundleDealExample();
                ShopeeMarketingBundleDealExample.Criteria criteria = shopeeMarketingBundleDealExample.createCriteria();
                criteria.andAccountNumberEqualTo(account).andConfigIdIsNotNull();
                criteria.andStatusEqualTo(ShopeeMarketingBundleDealStatusEnum.ONGOING.getCode());
                criteria.andSuitTypeEqualTo(ShopeeMarketingBundleDealSuitTypeEnum.NORMAL.getCode());
                criteria.andBundleDealEndTimeGreaterThan(nowTime);
                List<ShopeeMarketingBundleDeal> marketingBundleDealList = shopeeMarketingBundleDealService.selectByExample(shopeeMarketingBundleDealExample);
                if (CollectionUtils.isNotEmpty(marketingBundleDealList)) {
                    // 根据物流渠道id分组
                    Map<Integer, List<ShopeeMarketingBundleDeal>> bundleDealListByLogisticsId = marketingBundleDealList.stream().collect(Collectors.groupingBy(ShopeeMarketingBundleDeal::getLogisticsId));
                    bundleDealListByLogisticsId.forEach((logisticsId, shopeeMarketingBundleDealList) -> {
                        /*
                         * 如果明天优惠套装明天过期了，今天以上一个优惠套装的结束时间作为开始时间创建新的优惠套装：最后的套装的结束时间是明天，提前生成套装
                         * 否则不继续生成套装
                         **/
                        String logisticsName = null;
                        Timestamp startTime = null;
                        if (CollectionUtils.isNotEmpty(shopeeMarketingBundleDealList)) {
                            LocalDate tomorrowLocalDate = nowTime.toLocalDateTime().plusDays(1).toLocalDate();
                            List<ShopeeMarketingBundleDeal> sortShopeeMarketingBundleDealList = shopeeMarketingBundleDealList.stream()
                                    .sorted(Comparator.comparing(ShopeeMarketingBundleDeal::getBundleDealEndTime))
                                    .collect(Collectors.toList());

                            ShopeeMarketingBundleDeal maxEndTimeBundleDeal = sortShopeeMarketingBundleDealList.get(sortShopeeMarketingBundleDealList.size() - 1);
                            if (tomorrowLocalDate.equals(maxEndTimeBundleDeal.getBundleDealEndTime().toLocalDateTime().toLocalDate())) {
                                startTime = maxEndTimeBundleDeal.getBundleDealEndTime();
                            } else {
                                XxlJobLogger.log("当前店铺{}已有正在进行的优惠套装，不再生成新的优惠套装", account);
                                return;
                            }
                            // 截取第一个-前面的数据
                            logisticsName = maxEndTimeBundleDeal.getName().split("-")[0];
                        }

                        // 添加优惠套装
                        addNormalBundelDeal(config, account, logisticsId, nowTime, currentUser, startTime, logisticsName, saleAccount);
                    });
                    continue;
                }

                // 获取当前店铺的商品列表(为了获取item中的物流渠道)
                Map<Integer, String> logisticIdNameMap = getLogisticsIdAndNameMapByItemList(account);
                XxlJobLogger.log("店铺：{}无进行中套装活动，开始创建调整，物流渠道列表：{}", account, logisticIdNameMap.keySet());
                logisticIdNameMap.forEach((logisticId, logisticName) -> {
                    XxlJobLogger.log("店铺：{}，物流渠道：{}", account, logisticName);
                    // 添加优惠套装
                    addNormalBundelDeal(config, account, logisticId, nowTime, currentUser, null, logisticName, saleAccount);
                });
            } catch (Exception e) {
                XxlJobLogger.log("配置名称：{}，店铺：{}创建优惠套装失败,原因：{}", config.getName(), account, e.getMessage());
            }

        }
    }

    /**
     * 从ES获取可添加商品数据（并过滤物流渠道未开启的商品）
     *
     * @param account
     * @return
     */
    public Map<Integer, String> getLogisticsIdAndNameMapByItemList(String account) {
        // 查询出所有未结束的优惠套装
        ShopeeMarketingBundleDealExample shopeeMarketingBundleDealExample = new ShopeeMarketingBundleDealExample();
        ShopeeMarketingBundleDealExample.Criteria criteria = shopeeMarketingBundleDealExample.createCriteria();
        criteria.andStatusIn(Lists.newArrayList(ShopeeMarketingBundleDealStatusEnum.NEXT.getCode(), ShopeeMarketingBundleDealStatusEnum.ONGOING.getCode())).andAccountNumberEqualTo(account);
        List<ShopeeMarketingBundleDeal> shopeeMarketingBundleDealList = shopeeMarketingBundleDealService.selectByExample(shopeeMarketingBundleDealExample);
        List<Long> bundleDealIds = shopeeMarketingBundleDealList.stream().map(ShopeeMarketingBundleDeal::getBundleDealId).collect(Collectors.toList());

        // 1、通过店铺查询已经添加的商品
        ShopeeBundleDealProductListingExample dealProductListingExample = new ShopeeBundleDealProductListingExample();
        ShopeeBundleDealProductListingExample.Criteria bundleDealProductListingCriteria = dealProductListingExample.createCriteria();
        bundleDealProductListingCriteria.andAccountNumberEqualTo(account);
        if (CollectionUtils.isNotEmpty(bundleDealIds)) {
            bundleDealProductListingCriteria.andBundleDealIdIn(bundleDealIds);
        }
        List<ShopeeBundleDealProductListing> shopeeBundleDealProductListings = shopeeBundleDealProductListingService.selectByExample(dealProductListingExample);

        // 2、获取当前店铺下在线列表商品数据，并排除已存在的商品
        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemSeller(account);
        Optional.ofNullable(shopeeBundleDealProductListings).ifPresent(list -> {
            // 获取item并将其转换为String
            request.setNotItemIdList(list.stream().map(ShopeeBundleDealProductListing::getItemId).map(String::valueOf).collect(Collectors.toList()));
        });
        request.setQueryFields(new String[]{"itemId", "logistics"});
        request.setItemStatus("NORMAL");
        request.setStockNotEqual(0);
        request.setIsGoods(true);
        List<EsShopeeItem> esShopeeItemList = esShopeeItemService.getEsShopeeItems(request).stream()
                .filter(item -> {
                    // 获取商品支持的物流 ID 列表
                    if (ObjectUtils.isEmpty(item.getLogistics())) {
                        return false;
                    }

                    JSONArray logisticsArray = JSON.parseArray(item.getLogistics());
                    Set<Integer> logisticIds = logisticsArray.stream()
                            .filter(o -> ((JSONObject) o).getBoolean("enabled"))
                            .map(o -> ((JSONObject) o).getInteger("logistic_id"))
                            .collect(Collectors.toSet());
                    return CollectionUtils.isNotEmpty(logisticIds);
                })
                .collect(Collectors.toList());


        // 获取物流ID和名称映射
        return shopeeMarketingBundleDealService.getLogisticsIdAndNameMapByItemList(esShopeeItemList);
    }


    /**
     * 添加优惠套装
     *
     * @param config
     * @param account
     * @param logisticsId
     * @param nowTime
     * @param currentUser
     * @param startTime
     * @param logisticsName
     * @param saleAccount
     */
    public void addNormalBundelDeal(ShopeeMarketingConfig config, String account, Integer logisticsId, Timestamp nowTime, String currentUser, Timestamp startTime, String logisticsName, SaleAccountAndBusinessResponse saleAccount) {
        // 创建任务
        ShopeeConfigTask shopeeConfigTask = getShopeeConfigTask(config, account, nowTime);
        // 记录处理报告
        FeedTask insertFeedTask = getInsertFeedTask(config, account, nowTime, currentUser);
        try {
            if (ObjectUtils.isEmpty(startTime)) {
                // 如果是取当前时间加两个小时：平台限制开始时间只能是当前时间一个小时之后
                startTime = Timestamp.valueOf(nowTime.toLocalDateTime().plusHours(2));
            }

            // 构建请求参数
            ShopeeMarketingBundleDeal.ShopeeMarketingBundleDealBuilder bundleDealBuilder = ShopeeMarketingBundleDeal.builder();
            BundleDealConfigParam bundleDealConfigParam = JSON.parseObject(config.getRuleJson(), BundleDealConfigParam.class);
            ShopeeResponse shopeeResponse = ShopeeBundelDealCallV2.addBundelDeal(logisticsName, saleAccount, bundleDealConfigParam, startTime, bundleDealBuilder, nowTime);

            if (ObjectUtils.isNotEmpty(shopeeResponse) && (StringUtils.isBlank(shopeeResponse.getError()) || "-".equals(shopeeResponse.getError())) && StringUtils.isNotBlank(shopeeResponse.getResponse())) {
                String bundleDealId = JSON.parseObject(shopeeResponse.getResponse()).get("bundle_deal_id").toString();

                ShopeeAccountConfig accountConfigByAccount = shopeeAccountConfigService.getAccountConfigByAccount(account);
                if (ObjectUtils.isNotEmpty(accountConfigByAccount)) {
                    bundleDealBuilder.site(accountConfigByAccount.getSite());
                }
                // 保存优惠套装数据
                ShopeeMarketingBundleDeal shopeeMarketingBundleDeal = bundleDealBuilder
                        .accountNumber(account)
                        .logisticsId(logisticsId)
                        .configId(Long.valueOf(config.getId()))
                        .bundleDealId(Long.valueOf(bundleDealId))
                        .configId(Long.valueOf(config.getId()))
                        .ruleJson(config.getRuleJson())
                        .suitType(ShopeeMarketingBundleDealSuitTypeEnum.NORMAL.getCode())
                        .status(ShopeeMarketingBundleDealStatusEnum.NEXT.getCode())
                        .addStatus(ShopeeMarketingBundleDealAddStatusEnum.ADD_STATUS_PENDING.getCode())
                        .productNumber(0)
                        .build();
                shopeeMarketingBundleDealService.insert(shopeeMarketingBundleDeal);

                // 同步添加优惠套装任务表
                ShopeeAccountConfigExample shopeeAccountConfigExample = new ShopeeAccountConfigExample();
                ShopeeAccountConfigExample.Criteria shopeeAccountConfigCriteria = shopeeAccountConfigExample.createCriteria();
                shopeeAccountConfigCriteria.andAccountEqualTo(account);
                List<ShopeeAccountConfig> shopeeAccountConfigs = shopeeAccountConfigMapper.selectByExample(shopeeAccountConfigExample);
                if (CollectionUtils.isEmpty(shopeeAccountConfigs)) {
                    throw new RuntimeException("未找到店铺信息");
                }
                this.saveShopeeBundleDealProductListingTask(config, account, shopeeAccountConfigs, bundleDealId, shopeeMarketingBundleDeal);

                shopeeConfigTaskService.successTask(shopeeConfigTask.getId(), "创建成功");
                // 记录处理报告
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(insertFeedTask, ResultStatusEnum.RESULT_SUCCESS.getStatusCode(), "创建成功，套装名称：" + shopeeMarketingBundleDeal.getName());
            } else {
                shopeeConfigTaskService.failTask(shopeeConfigTask.getId(), "创建优惠套装失败" + JSON.toJSONString(shopeeResponse));
                ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(insertFeedTask, ResultStatusEnum.RESULT_FAIL.getStatusCode(), "创建优惠套装失败，shopee平台接口报错:" + JSON.toJSONString(shopeeResponse));
            }
        } catch (NumberFormatException e) {
            shopeeConfigTaskService.failTask(shopeeConfigTask.getId(), e.getMessage());
            ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(insertFeedTask, ResultStatusEnum.RESULT_FAIL.getStatusCode(), "创建优惠套装失败，shopee平台接口报错:" + e.getMessage());
        }
    }


    private static FeedTask getInsertFeedTask(ShopeeMarketingConfig config, String account, Timestamp nowTime, String currentUser) {
        FeedTask insertFeedTask = ShopeeFeedTaskHandleUtil.insertFeedTask(feedTask -> {
            feedTask.setAttribute1(config.getName());
            feedTask.setAccountNumber(account);
            feedTask.setTaskType(ShopeeFeedTaskEnum.CREATE_BUNDLE_DEAL.getValue());
            feedTask.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
            feedTask.setRunTime(nowTime);
            feedTask.setFinishTime(new Timestamp(new Date().getTime()));
            feedTask.setCreatedBy(StringUtils.isBlank(currentUser) ? "admin" : currentUser);
        });
        return insertFeedTask;
    }

    /**
     * 保存优惠套装任务表
     *
     * @param config
     * @param account
     * @param shopeeAccountConfigs
     * @param bundleDealId
     * @param shopeeMarketingBundleDeal
     */
    private void saveShopeeBundleDealProductListingTask(ShopeeMarketingConfig config, String account, List<ShopeeAccountConfig> shopeeAccountConfigs, String bundleDealId, ShopeeMarketingBundleDeal shopeeMarketingBundleDeal) {
        LocalDateTime now = LocalDateTime.now();
        ShopeeAccountConfig shopeeAccountConfig = shopeeAccountConfigs.get(0);
        ShopeeBundleDealProductListingTask bundleDealProductListingTask = new ShopeeBundleDealProductListingTask();
        bundleDealProductListingTask.setMarketingId(Long.valueOf(config.getId()));
        bundleDealProductListingTask.setMarketingName(config.getName());
        bundleDealProductListingTask.setMerchantName(shopeeAccountConfig.getMerchant());
        bundleDealProductListingTask.setMerchantId(shopeeAccountConfig.getMerchantId());
        bundleDealProductListingTask.setShopId(shopeeAccountConfig.getShopId());
        bundleDealProductListingTask.setAccountNumber(account);
        bundleDealProductListingTask.setSellerAccount(shopeeAccountConfig.getSubAccount());
        bundleDealProductListingTask.setBundleDealId(Long.valueOf(bundleDealId));
        bundleDealProductListingTask.setBundleStartTime(shopeeMarketingBundleDeal.getBundleDealStartTime().toLocalDateTime());
        bundleDealProductListingTask.setBundleEndTime(shopeeMarketingBundleDeal.getBundleDealEndTime().toLocalDateTime());
        bundleDealProductListingTask.setOperatorStatus(0);
        bundleDealProductListingTask.setOperatorUpdateTime(now);
        bundleDealProductListingTask.setExecTime(now);
        bundleDealProductListingTask.setCreateTime(now);
        bundleDealProductListingTask.setUpdateTime(now);
        shopeeBundleDealProductListingTaskService.save(bundleDealProductListingTask);
    }

    /**
     * 获取任务参数
     *
     * @param marketingConfig
     * @param accountNumber
     * @param timestamp
     * @return
     */
    private ShopeeConfigTask getShopeeConfigTask(ShopeeMarketingConfig marketingConfig, String accountNumber, Timestamp timestamp) {
        ShopeeConfigTask configTask = new ShopeeConfigTask();
        configTask.setConfigType(ShopeeConfigTypeEnum.BUNDLE_DEAL.getCode());
        configTask.setConfigId(marketingConfig.getId());
        configTask.setConfigName(marketingConfig.getName());
        configTask.setConfigRuleJson(marketingConfig.getRuleJson());
        configTask.setAccountNumber(accountNumber);
        configTask.setExecTime(timestamp);
        configTask.setOperatorStatus(ShopeeConfigOperatorStatusEnum.WAITING.getCode());
        configTask.setOperatorTime(timestamp);
        configTask.setCreatedTime(timestamp);
        configTask.setUpdatedTime(timestamp);
        shopeeConfigTaskService.insert(configTask);
        return configTask;
    }
}