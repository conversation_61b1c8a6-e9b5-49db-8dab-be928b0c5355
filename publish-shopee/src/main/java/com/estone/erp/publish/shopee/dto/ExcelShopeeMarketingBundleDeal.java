package com.estone.erp.publish.shopee.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.component.converter.TimestampFormatConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExcelShopeeMarketingBundleDeal implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 商家名称 database column shopee_marketing_bundle_deal.merchant_name
     */
    @ExcelProperty("商家")
    private String merchantName;

    /**
     * 店铺账号 database column shopee_marketing_bundle_deal.account_number
     */
    @ExcelProperty("店铺")
    private String accountNumber;

    /**
     * 站点 database column shopee_marketing_bundle_deal.site
     */
    @ExcelProperty("站点")
    private String site;

    /**
     * 套装名称 database column shopee_marketing_bundle_deal.name
     */
    @ExcelProperty("套装名称")
    private String name;

    /**
     * 套装类型 database column shopee_marketing_bundle_deal.type
     */
    @ExcelIgnore
    private Integer type;


    @ExcelProperty("套装类型")
    private String typeStr;

    @ExcelIgnore
    private Integer suitType;

    @ExcelProperty("套装方式")
    private String suitTypeStr;

    /**
     * 套装来源
     */
    @ExcelProperty("套装来源")
    private String source;

    /**
     * 商品数量 database column shopee_marketing_bundle_deal.product_number
     */
    @ExcelProperty("商品数")
    private int productNumber;

    /**
     * 启用商品数
     */
    @ExcelProperty("启用商品数")
    private Integer startCount;

    /**
     * 状态(未开始，进行中，已过期) database column shopee_marketing_bundle_deal.status
     */
    @ExcelIgnore
    private Integer status;

    @ExcelProperty("状态")
    private String statusStr;

    /**
     * 套装开始时间 database column shopee_marketing_bundle_deal.bundle_deal_start_time
     */
    @ExcelProperty(value = "套装开始时间", converter = TimestampFormatConverter.class)
    private Timestamp bundleDealStartTime;

    /**
     * 套装结束时间 database column shopee_marketing_bundle_deal.bundle_deal_end_time
     */
    @ExcelProperty(value = "套装结束时间", converter = TimestampFormatConverter.class)
    private Timestamp bundleDealEndTime;

    /**
     * 销售
     */
    @ExcelProperty("销售")
    private String salesman;

    /**
     * 销售组长
     */
    @ExcelProperty("销售组长")
    private String salesTeamLeader;

    /**
     * 销售主管
     */
    @ExcelProperty("销售主管")
    private String salesSupervisorName;

    /**
     * 同步时间 database column shopee_marketing_bundle_deal.sync_time
     */
    @ExcelProperty(value = "同步时间", converter = TimestampFormatConverter.class)
    private Timestamp syncTime;


    /**
     * 配置ID database column shopee_marketing_bundle_deal.config_id
     */
    @ExcelIgnore
    private Long configId;

    @ExcelIgnore
    private String ruleJson;

}