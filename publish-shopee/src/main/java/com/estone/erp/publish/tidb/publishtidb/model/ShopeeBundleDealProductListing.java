package com.estone.erp.publish.tidb.publishtidb.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class ShopeeBundleDealProductListing implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column shopee_bundle_deal_product_listing.id
     */
    private Long id;

    /**
     * 店铺账号 database column shopee_bundle_deal_product_listing.account_number
     */
    private String accountNumber;

    /**
     * 商品id database column shopee_bundle_deal_product_listing.item_id
     */
    private Long itemId;

    /**
     * 状态 database column shopee_bundle_deal_product_listing.status
     * The status of items：enable = 1，disable =0
     */
    private Integer status;

    /**
     * 套装id database column shopee_bundle_deal_product_listing.bundle_deal_id
     */
    private Long bundleDealId;

    /**
     * 创建时间 database column shopee_bundle_deal_product_listing.create_time
     */
    private Timestamp createTime;
}