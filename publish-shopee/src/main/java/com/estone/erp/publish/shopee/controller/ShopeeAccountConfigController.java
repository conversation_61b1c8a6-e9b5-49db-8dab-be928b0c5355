package com.estone.erp.publish.shopee.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.util.*;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.model.dto.ShopMonthTotalDataDO;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeAccountOrderReport;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeAccountOrderReportService;
import com.estone.erp.publish.platform.model.WatermarkTemplate;
import com.estone.erp.publish.platform.service.WatermarkTemplateService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.bo.ShopeeAccountConfigUpdateBo;
import com.estone.erp.publish.shopee.component.download.ShopeeDownloadTypeEnums;
import com.estone.erp.publish.shopee.dto.ShopeeSaleAccountVO;
import com.estone.erp.publish.shopee.dto.ShopeeSubMerchantAccountDto;
import com.estone.erp.publish.shopee.model.*;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigLogService;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.estone.erp.publish.shopee.service.ShopeeAccountGroupService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.account.ShopeeMerchant;
import com.estone.erp.publish.system.excel.enums.ExcelDownloadStatusEnums;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import com.estone.erp.publish.system.excel.service.ExcelDownloadLogService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> shopee_account_config
 * 2020-12-03 18:18:19
 */
@Slf4j
@RestController
@RequestMapping("shopeeAccountConfig")
public class ShopeeAccountConfigController {

    @Resource
    private ShopeeAccountConfigService shopeeAccountConfigService;
    @Resource
    private ShopeeAccountConfigLogService shopeeAccountConfigLogService;
    @Resource
    private WatermarkTemplateService watermarkTemplateService;
    @Resource
    private EsShopeeAccountOrderReportService esShopeeAccountOrderReportService;

    @Resource
    private ShopeeAccountGroupService shopeeAccountGroupService;

    @Resource
    private ExcelDownloadLogService excelDownloadLogService;

    @PostMapping
    public ApiResult<?> postShopeeAccountConfig(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        String userName = WebUtils.getUserName();
        String ids;
        JSONObject paramObject;
        String account;
        String accounts;
        ShopeeAccountConfig originShopeeAccountConfig;
        ShopeeAccountConfig updateShopeeAccountConfig;
        ShopeeAccountConfigExample example;
        ShopeeAccountConfigExample.Criteria criteria;
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchShopeeAccountConfig": // 查询列表
                    CQuery<ShopeeAccountConfigCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<ShopeeAccountConfigCriteria>>() {
                    });
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    /*权限控制----start*/
                    //如果入参店铺为空则且不是超管或最高权限者，只查询当前登录人的店铺
                    ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SHOPEE);
                    Boolean dataSupportDepartment = NewUsermgtUtils.isDataSupportDepartment();
                    if (!superAdminOrEquivalent.isSuccess()) {
                        return ApiResult.newError(superAdminOrEquivalent.getErrorMsg());
                    }
                    if (StringUtils.isBlank(cquery.getSearch().getAccounts()) && !superAdminOrEquivalent.getResult() && BooleanUtils.isFalse(dataSupportDepartment)) {
                        ApiResult<List<String>> authorAccountListResult = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_SHOPEE, false);
                        if (!authorAccountListResult.isSuccess()) {
                            return ApiResult.newError(authorAccountListResult.getErrorMsg());
                        }
                        //查询销售对应店铺列表
                        List<String> authorAccountList = authorAccountListResult.getResult();
                        if (CollectionUtils.isEmpty(authorAccountList)) {
                            return ApiResult.newError("未查询到可用店铺列表！");
                        }
                        cquery.getSearch().setAccounts(StringUtils.join(authorAccountList, ","));
                    }
                    /*权限控制----end*/
                    CQueryResult<ShopeeAccountConfig> results = shopeeAccountConfigService.search(cquery);
                    return results;
                case "updateShopeeAccountConfig": // 编辑
                    //ShopeeAccountConfig updateShopeeAccountConfig = requestParam.getArgsValue(new TypeReference<ShopeeAccountConfig>() {});
                    paramObject = requestParam.getArgsValue(new TypeReference<JSONObject>() {
                    });
                    updateShopeeAccountConfig = JSONObject.parseObject(paramObject.getString("shopeeAccountConfig"), ShopeeAccountConfig.class);
                    // 判断水印模板数据是否匹配对应店铺
                    if (StringUtils.isNotBlank(updateShopeeAccountConfig.getWatermarkTemplateIdStr())) {
                        String[] watermarkTemplateIds = updateShopeeAccountConfig.getWatermarkTemplateIdStr().split(",");
                        List<WatermarkTemplate> watermarkTemplates = watermarkTemplateService.getWatermarkTemplate(Arrays.stream(watermarkTemplateIds).map(Integer::parseInt).collect(Collectors.toList()));
                        if (CollectionUtils.isEmpty(watermarkTemplates)) {
                            return ApiResult.newError("水印模板数据不存在！");
                        }

                        // 按照店铺分组
                        Map<String, List<WatermarkTemplate>> watermarkTemplateMap = watermarkTemplates.stream().collect(Collectors.groupingBy(WatermarkTemplate::getAccountNumber));
                        // 如果这个店铺数据和数据里面的店铺对不上，或者不相等则报错
                        if (!watermarkTemplateMap.containsKey(updateShopeeAccountConfig.getAccount()) || watermarkTemplateMap.size() > 1) {
                            return ApiResult.newError("水印模板数据不匹配对应店铺！");
                        }
                    }

                    originShopeeAccountConfig = shopeeAccountConfigService.selectByPrimaryKey(updateShopeeAccountConfig.getId());
                    //先生成日志再修改，不然逻辑会有问题
                    shopeeAccountConfigService.generateAccountConfigLog(originShopeeAccountConfig, updateShopeeAccountConfig, userName);
                    //可刊登类目前后加逗号
                    if (StringUtils.isNotBlank(updateShopeeAccountConfig.getCategoryIds())) {
                        updateShopeeAccountConfig.setCategoryIds(StrUtil.strAddComma(updateShopeeAccountConfig.getCategoryIds()));
                    }
                    if (StringUtils.isNotBlank(updateShopeeAccountConfig.getNonPublishableTag())) {
                        updateShopeeAccountConfig.setNonPublishableTag(StrUtil.strAddComma(updateShopeeAccountConfig.getNonPublishableTag()));
                    }

                    // 预售目标值 发生修改要填充时间
                    String oldTarget = originShopeeAccountConfig.getPercentPreOrderTarget();
                    String newTarget = updateShopeeAccountConfig.getPercentPreOrderTarget();
                    if (!StringUtils.equals(oldTarget, newTarget)) {
                        updateShopeeAccountConfig.setUpdatePreOrderTargetTime(new Timestamp(new Date().getTime()));
                    }

                    // 不可刊登sku排个序
                    List<String> nonPublishableSkuList = CommonUtils.splitList(updateShopeeAccountConfig.getNonPublishableSku(), ",");
                    Collections.sort(nonPublishableSkuList);
                    String nonPublishableSkuJoin = nonPublishableSkuList.stream().collect(Collectors.joining(","));
                    updateShopeeAccountConfig.setNonPublishableSku(nonPublishableSkuJoin);

                    updateShopeeAccountConfig.setIsAbnormalConfig(false);
                    updateShopeeAccountConfig.setCreateTime(new Timestamp(System.currentTimeMillis()));
                    updateShopeeAccountConfig.setCreator(userName);
                    shopeeAccountConfigService.updateByPrimaryKeySelective(updateShopeeAccountConfig);
                    //然后把同一个组的配置都改成一样的
//                    this.updateGroupShopeeAccount(originShopeeAccountConfig.getGroupCode(), updateShopeeAccountConfig, userName);
                    return ApiResult.newSuccess();
                case "syncAccount": // 同步账号
                    shopeeAccountConfigService.syncAccount(userName);
                    return ApiResult.newSuccess();
                case "batchModifyTitlePrefix":
                    paramObject = requestParam.getArgsValue(new TypeReference<JSONObject>() {
                    });
                    ids = paramObject.getString("ids");
                    String titlePrefix = paramObject.getString("titlePrefix");
                    for (String id : ids.split(",")) {
                        originShopeeAccountConfig = shopeeAccountConfigService.selectByPrimaryKey(Integer.valueOf(id));
                        if (originShopeeAccountConfig != null) {
                            updateShopeeAccountConfig = new ShopeeAccountConfig();
                            updateShopeeAccountConfig.setTitlePrefix(titlePrefix);
                            //先生成日志再修改，不然逻辑会有问题
                            updateShopeeAccountConfig = new ShopeeAccountConfig();
                            updateShopeeAccountConfig.setTitlePrefix(titlePrefix);
                            shopeeAccountConfigService.generateAccountConfigLog(originShopeeAccountConfig, updateShopeeAccountConfig, userName);
                            originShopeeAccountConfig.setTitlePrefix(titlePrefix);
                            originShopeeAccountConfig.setCreateTime(new Timestamp(System.currentTimeMillis()));
                            originShopeeAccountConfig.setCreator(userName);
                            shopeeAccountConfigService.updateByPrimaryKeySelective(originShopeeAccountConfig);
                            //然后把同一个组的配置都改成一样的
//                            this.updateGroupShopeeAccount(originShopeeAccountConfig.getGroupCode(), updateShopeeAccountConfig, userName);
                        }
                    }
                    return ApiResult.newSuccess();
                case "batchModifyAutomaticPublishProduct"://新增批量修改自动上架新品
                    paramObject = requestParam.getArgsValue(new TypeReference<JSONObject>() {
                    });
                    ids = paramObject.getString("ids");
                    Boolean automaticPublishProduct = paramObject.getBoolean("automaticPublishProduct");
                    for (String id : ids.split(",")) {
                        originShopeeAccountConfig = shopeeAccountConfigService.selectByPrimaryKey(Integer.valueOf(id));
                        if (originShopeeAccountConfig != null) {
                            updateShopeeAccountConfig = new ShopeeAccountConfig();
                            updateShopeeAccountConfig.setAutomaticPublishProduct(automaticPublishProduct);
                            //先生成日志再修改，不然逻辑会有问题
                            updateShopeeAccountConfig = new ShopeeAccountConfig();
                            updateShopeeAccountConfig.setAutomaticPublishProduct(automaticPublishProduct);
                            shopeeAccountConfigService.generateAccountConfigLog(originShopeeAccountConfig, updateShopeeAccountConfig, userName);
                            originShopeeAccountConfig.setAutomaticPublishProduct(automaticPublishProduct);
                            originShopeeAccountConfig.setCreateTime(new Timestamp(System.currentTimeMillis()));
                            originShopeeAccountConfig.setCreator(userName);
                            shopeeAccountConfigService.updateByPrimaryKeySelective(originShopeeAccountConfig);
                            //然后把同一个组的配置都改成一样的
//                            this.updateGroupShopeeAccount(originShopeeAccountConfig.getGroupCode(), updateShopeeAccountConfig, userName);
                        }
                    }

                    return ApiResult.newSuccess();
                case "batchModifyClassify"://新增批量修改分类
                    paramObject = requestParam.getArgsValue(new TypeReference<JSONObject>() {
                    });
                    ids = paramObject.getString("ids");
                    String categoryIds = paramObject.getString("categoryIds");
                    for (String id : ids.split(",")) {
                        originShopeeAccountConfig = shopeeAccountConfigService.selectByPrimaryKey(Integer.valueOf(id));
                        if (originShopeeAccountConfig != null) {
                            updateShopeeAccountConfig = new ShopeeAccountConfig();
                            updateShopeeAccountConfig.setCategoryIds(categoryIds);
                            //先生成日志再修改，不然逻辑会有问题
                            updateShopeeAccountConfig = new ShopeeAccountConfig();
                            updateShopeeAccountConfig.setCategoryIds(categoryIds);
                            shopeeAccountConfigService.generateAccountConfigLog(originShopeeAccountConfig, updateShopeeAccountConfig, userName);
                            originShopeeAccountConfig.setCategoryIds(categoryIds);
                            originShopeeAccountConfig.setCreateTime(new Timestamp(System.currentTimeMillis()));
                            originShopeeAccountConfig.setCreator(userName);
                            shopeeAccountConfigService.updateByPrimaryKeySelective(originShopeeAccountConfig);
                            //然后把同一个组的配置都改成一样的 分类不需要强制改为一样
                            //this.updateGroupShopeeAccount(originShopeeAccountConfig.getGroupCode(), updateShopeeAccountConfig, userName);
                        }
                    }
                    return ApiResult.newSuccess();
                case "batchModifyMaxPublishNum"://新增批量修改最大刊登数量
                    paramObject = requestParam.getArgsValue(new TypeReference<JSONObject>() {
                    });
                    ids = paramObject.getString("ids");
                    int maxPublishAmount = paramObject.getInteger("maxPublishAmount");
                    for (String id : ids.split(",")) {
                        originShopeeAccountConfig = shopeeAccountConfigService.selectByPrimaryKey(Integer.valueOf(id));
                        if (originShopeeAccountConfig != null) {
                            updateShopeeAccountConfig = new ShopeeAccountConfig();
                            updateShopeeAccountConfig.setMaxPublishAmount(maxPublishAmount);
                            //先生成日志再修改，不然逻辑会有问题
                            updateShopeeAccountConfig = new ShopeeAccountConfig();
                            updateShopeeAccountConfig.setMaxPublishAmount(maxPublishAmount);
                            shopeeAccountConfigService.generateAccountConfigLog(originShopeeAccountConfig, updateShopeeAccountConfig, userName);
                            originShopeeAccountConfig.setMaxPublishAmount(maxPublishAmount);
                            originShopeeAccountConfig.setCreateTime(new Timestamp(System.currentTimeMillis()));
                            originShopeeAccountConfig.setCreator(userName);
                            shopeeAccountConfigService.updateByPrimaryKeySelective(originShopeeAccountConfig);
                            //然后把同一个组的配置都改成一样的
//                            this.updateGroupShopeeAccount(originShopeeAccountConfig.getGroupCode(), updateShopeeAccountConfig, userName);
                        }
                    }
                    return ApiResult.newSuccess();
                case "batchModifyMaxPublishDate"://新增批量修改定时刊登时间
                    paramObject = requestParam.getArgsValue(new TypeReference<JSONObject>() {
                    });
                    ids = paramObject.getString("ids");
                    String publishBeginTime = paramObject.getString("publishBeginTime");
                    int timerInterval = paramObject.getInteger("timerInterval");
                    for (String id : ids.split(",")) {
                        originShopeeAccountConfig = shopeeAccountConfigService.selectByPrimaryKey(Integer.valueOf(id));
                        if (originShopeeAccountConfig != null) {
                            updateShopeeAccountConfig = new ShopeeAccountConfig();
                            updateShopeeAccountConfig.setPublishBeginTime(publishBeginTime);
                            updateShopeeAccountConfig.setTimerInterval(timerInterval);
                            //先生成日志再修改，不然逻辑会有问题
                            updateShopeeAccountConfig = new ShopeeAccountConfig();
                            updateShopeeAccountConfig.setPublishBeginTime(publishBeginTime);
                            updateShopeeAccountConfig.setTimerInterval(timerInterval);
                            shopeeAccountConfigService.generateAccountConfigLog(originShopeeAccountConfig, updateShopeeAccountConfig, userName);
                            originShopeeAccountConfig.setPublishBeginTime(publishBeginTime);
                            originShopeeAccountConfig.setTimerInterval(timerInterval);
                            originShopeeAccountConfig.setCreateTime(new Timestamp(System.currentTimeMillis()));
                            originShopeeAccountConfig.setCreator(userName);
                            shopeeAccountConfigService.updateByPrimaryKeySelective(originShopeeAccountConfig);
                            //然后把同一个组的配置都改成一样的
//                            this.updateGroupShopeeAccount(originShopeeAccountConfig.getGroupCode(), updateShopeeAccountConfig, userName);
                        }
                    }
                    return ApiResult.newSuccess();
                case "getAccountConfigById":
                    Integer id = requestParam.getArgsValue(new TypeReference<Integer>() {
                    });
                    ShopeeAccountConfig selectShopeeAccountConfig = shopeeAccountConfigService.selectByPrimaryKey(id);
                    return ApiResult.newSuccess(selectShopeeAccountConfig);
                case "getAccountConfigLog":
                    account = requestParam.getArgs();
                    ShopeeAccountConfigLogExample logExample = new ShopeeAccountConfigLogExample();
                    ShopeeAccountConfigLogExample.Criteria logCriteria = logExample.createCriteria();
                    logCriteria.andAccountEqualTo(account);
                    logExample.setOrderByClause("id desc");
                    List<ShopeeAccountConfigLog> shopeeAccountConfigLogList = shopeeAccountConfigLogService.selectByExample(logExample);
                    return ApiResult.newSuccess(shopeeAccountConfigLogList);
                case "getAccountConfigForSpu":
                    accounts = requestParam.getArgs();
                    String resultAccount = null;
                    for (String accountNumber : accounts.split(",")) {
                        if (accountNumber.endsWith(".my") || accountNumber.endsWith(".MY")) {
                            resultAccount = accountNumber;
                            break;
                        }
                    }
                    if (resultAccount == null) {
                        resultAccount = accounts.split(",")[0];
                    }
                    ShopeeAccountConfig shopeeAccountConfig = shopeeAccountConfigService.getAccountConfigByAccount(resultAccount);
                    return ApiResult.newSuccess(shopeeAccountConfig);
            }
        }
        return ApiResult.newSuccess();
    }

    /**
     * 批量修改
     * @param updateBO
     * @return
     */
    @PostMapping("/batchUpdate")
    public ApiResult<?> batchUpdate(@RequestBody(required = true) ShopeeAccountConfigUpdateBo updateBO) {
        if(updateBO == null || updateBO.getSearchCriteria() == null || updateBO.getUpdateAccountConfig() == null) {
            return ApiResult.newError("修改值和条件都不可以为空");
        }

        List<ShopeeAccountConfig> originAccountConfigs = shopeeAccountConfigService.selectByExample(updateBO.getSearchCriteria().getExample());
        if(CollectionUtils.isEmpty(originAccountConfigs)) {
            return ApiResult.newError("选择的数据未找到");
        }

        String userName = WebUtils.getUserName();
        ShopeeAccountConfig updateAccountConfig = updateBO.getUpdateAccountConfig();
        for (ShopeeAccountConfig originAccountConfig : originAccountConfigs) {
            ShopeeAccountConfig updateShopeeAccountConfig = new ShopeeAccountConfig();
            updateShopeeAccountConfig.setMonthSaleTarget(updateAccountConfig.getMonthSaleTarget());
            updateShopeeAccountConfig.setYearSaleTarget(updateAccountConfig.getYearSaleTarget());
            updateShopeeAccountConfig.setMonthAddListingTarget(updateAccountConfig.getMonthAddListingTarget());
            updateShopeeAccountConfig.setDiscountProfit(updateAccountConfig.getDiscountProfit());
            updateShopeeAccountConfig.setProfit(updateAccountConfig.getProfit());
            shopeeAccountConfigService.generateAccountConfigLog(originAccountConfig, updateShopeeAccountConfig, userName);
            updateShopeeAccountConfig.setId(originAccountConfig.getId());
            shopeeAccountConfigService.updateByPrimaryKeySelective(updateShopeeAccountConfig);
        }
        return ApiResult.newSuccess();
    }

        private void updateGroupShopeeAccount(String groupCode, ShopeeAccountConfig newShopeeAccountConfig, String userName) {
        //然后把同一个组的配置都改成一样的
        if (StringUtils.isNotEmpty(groupCode)) {
            List<ShopeeAccountConfig> shopeeAccountConfigList = shopeeAccountConfigService.getGroupAccountConfig(groupCode);
            for (int i = 0; i < shopeeAccountConfigList.size(); i++) {
                ShopeeAccountConfig originShopeeAccountConfig = shopeeAccountConfigList.get(i);
                newShopeeAccountConfig.setId(originShopeeAccountConfig.getId());
                newShopeeAccountConfig.setAccount(originShopeeAccountConfig.getAccount());
                newShopeeAccountConfig.setCategoryIds(originShopeeAccountConfig.getCategoryIds());
                newShopeeAccountConfig.setNonPublishableTag(originShopeeAccountConfig.getNonPublishableTag());
                newShopeeAccountConfig.setNonPublishableSku(originShopeeAccountConfig.getNonPublishableSku());
                newShopeeAccountConfig.setWatermarkTemplateIdStr(originShopeeAccountConfig.getWatermarkTemplateIdStr());
                shopeeAccountConfigService.generateAccountConfigLog(originShopeeAccountConfig, newShopeeAccountConfig, userName);
                newShopeeAccountConfig.setCreateTime(new Timestamp(System.currentTimeMillis()));
                newShopeeAccountConfig.setCreator(userName);
                shopeeAccountConfigService.updateByPrimaryKeySelective(newShopeeAccountConfig);
            }
        }
    }

    /**
     * 按站点修改不可刊登sku
     * @param criteria
     * @return
     */
    @PostMapping("/updateNonPublishableSkuBySites")
    public ApiResult<?> updateNonPublishableSkuBySites(@RequestBody ShopeeAccountConfigCriteria criteria) {
        if(null == criteria){
            return ApiResult.newError("请求对象为空！");
        }
        String nonPublishableSku = criteria.getNonPublishableSku();
        List<String> sites = criteria.getSites();
        if(CollectionUtils.isEmpty(sites) || StringUtils.isEmpty(nonPublishableSku)) {
            return ApiResult.newError("请选择站点并输入不可刊登SKU");
        }

        shopeeAccountConfigService.updateNonPublishableSkuBySites(nonPublishableSku, sites);
        return ApiResult.newSuccess();
    }

    /**
     * 获取账号信息
     *
     * @param account
     * @return
     */
    @GetMapping("/getAccountInfo")
    public ApiResult<?> getAccountInfo(@RequestParam String account) {
        if (StringUtils.isEmpty(account)) {
            return ApiResult.newSuccess();
        }
//        SaleAccountAndBusinessResponse shopeeAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, account, false);
        ShopeeAccountConfigExample ex = new ShopeeAccountConfigExample();
        ex.createCriteria().andAccountEqualTo(account);
        List<ShopeeAccountConfig> list = shopeeAccountConfigService.selectByExample(ex);
        if (CollectionUtils.isNotEmpty(list)) {
            ShopeeAccountConfig accountConfig = list.get(0);
            accountConfig.setCreateTime(null);
            accountConfig.setCreator(null);
//            JSONObject json = JSON.parseObject(JSON.toJSONString(accountConfig));
//            json.put("site", shopeeAccount.getAccountSite());

            return ApiResult.newSuccess(accountConfig);
        }
        return ApiResult.newSuccess();
    }

    @PostMapping(value = "/download")
    public ApiResult<?> downloadMethod(@RequestBody(required = false) ShopeeAccountConfigCriteria criteria) {
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SHOPEE);
        Boolean dataSupportDepartment = NewUsermgtUtils.isDataSupportDepartment();
        if (!superAdminOrEquivalent.isSuccess()) {
            return ApiResult.newError(superAdminOrEquivalent.getErrorMsg());
        }
        if (StringUtils.isBlank(criteria.getAccounts()) && !superAdminOrEquivalent.getResult() && BooleanUtils.isFalse(dataSupportDepartment)) {
            ApiResult<List<String>> authorAccountListResult = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_SHOPEE, false);
            if (!authorAccountListResult.isSuccess()) {
                return ApiResult.newError(authorAccountListResult.getErrorMsg());
            }
            //查询销售对应店铺列表
            List<String> authorAccountList = authorAccountListResult.getResult();
            if (CollectionUtils.isEmpty(authorAccountList)) {
                return ApiResult.newError("未查询到可用店铺列表！");
            }
            criteria.setAccounts(StringUtils.join(authorAccountList, ","));
        }
        int count = shopeeAccountConfigService.countByExample(criteria.getExample());
        if (count == 0) {
            return ApiResult.newError("下载实际内容为空，请确认条件！");
        }

        // 构造导出日志
        ExcelDownloadLog downloadLog = new ExcelDownloadLog();
        downloadLog.setType(ShopeeDownloadTypeEnums.ACCOUNT_CONFIG_RECORD.getType());
        downloadLog.setQueryCondition(JSONObject.toJSONString(criteria));
        downloadLog.setDownloadCount(count);
        downloadLog.setStatus(ExcelDownloadStatusEnums.WAIT.getCode());
        downloadLog.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
        downloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
        // 发送队列
        excelDownloadLogService.addAndPushLog(downloadLog, SaleChannel.CHANNEL_SHOPEE, PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_DOWNLOAD_QUEUE_KEY);
        return ApiResult.newSuccess("导出成功，稍后前往导出日志查看！");
    }


    @GetMapping("/getAccountMerchantByEmployee")
    public ApiResult<?> getAccountMerchantByEmployee() {
        List<SaleAccountAndBusinessResponse> list = AccountUtils.getAccountMerchantByEmployee(Platform.Shopee.name());
        return ApiResult.newSuccess(list);
    }

    /**
     * 获取当前用户：子账号 商家 店铺集合
     *
     * @return
     */
    @GetMapping("/getSubMerchantAccount")
    public ApiResult<?> getSubMerchantAccount() {
//        List<SaleAccountAndBusinessResponse> list = AccountUtils.getAccountMerchantByEmployee(Platform.Shopee.name());
        ApiResult<List<SaleAccount>> result = EsAccountUtils.getAccountMerchantByEmployee(Platform.Shopee.name(), false, null);
        if(!result.isSuccess()){
            return result;
        }
        List<SaleAccount> list = result.getResult();

        //母账号集合
        Set<String> monAccountList = new HashSet<>();
        //子账号集合
        Set<String> subAccountList = new HashSet<>();
        //商家标记
//        Set<String> merchants = new HashSet<>();
        //子账号 关联下 商家集合
        Map<String, List<ShopeeSubMerchantAccountDto>> merchantMap = new HashMap<>();
        //商家 关联下 站点-店铺集合
        Map<String, Map<String, List<ShopeeSubMerchantAccountDto>>> accountMap = new HashMap<>();
        for (SaleAccount o : list) {
            if (StringUtils.isNotBlank(o.getColStr4())) {
                monAccountList.add(o.getColStr4());
            }

            if (StringUtils.isNotBlank(o.getColStr5())) {
                //添加子账号
                subAccountList.add(o.getColStr5());

                ShopeeMerchant shopeeMerchant = o.getShopeeMerchant();
                if (shopeeMerchant != null && StringUtils.isNotBlank(shopeeMerchant.getMerchantName()) && StringUtils.isNotBlank(o.getMerchantId())) {
                    //子账号下 - 添加商家
                    List<ShopeeSubMerchantAccountDto> merchantList = MapUtils.putIfAbsent(merchantMap, o.getColStr5(), () -> new ArrayList<>());

                    //不同的店铺归属同一个商家 但是店铺会分给不同的子账号
                    long count = merchantList.stream().filter(mm -> mm.getMerchantId().equalsIgnoreCase(o.getMerchantId())).count();
                    if(count == 0){
                        ShopeeSubMerchantAccountDto merchant = new ShopeeSubMerchantAccountDto();
                        merchant.setSubAccount(o.getColStr5());
                        merchant.setMerchantId(o.getMerchantId());
                        merchant.setMerchantName(shopeeMerchant.getMerchantName());
                        merchantList.add(merchant);
                    }

                    //商家下 - 添加店铺
                    Map<String, List<ShopeeSubMerchantAccountDto>> siteMap = MapUtils.putIfAbsent(accountMap, o.getMerchantId() + "-" + o.getColStr5(), () -> new HashMap<>());

                    if (StringUtils.isNotBlank(o.getAccountSite())) {
                        String accountSite = o.getAccountSite() == null ? "" : o.getAccountSite();
                        List<ShopeeSubMerchantAccountDto> accountList = MapUtils.putIfAbsent(siteMap, accountSite, () -> new ArrayList<>());
                        ShopeeSubMerchantAccountDto account = new ShopeeSubMerchantAccountDto();
                        account.setMerchantId(o.getMerchantId());
                        account.setAccountNumber(o.getAccountNumber());
                        account.setAccountSite(o.getAccountSite());
                        accountList.add(account);
                    }
                }
            }
        }

        //商家集合
        List<Object> merchantList = merchantMap.values().stream().collect(ArrayList::new, ArrayList::addAll, ArrayList::addAll);

        Map<String, Object> resultMap = new LinkedHashMap<>(6);
        resultMap.put("monAccountList", monAccountList);
        resultMap.put("subAccountList", subAccountList);
        resultMap.put("merchantMap", merchantMap);
        resultMap.put("accountMap", accountMap);
        resultMap.put("merchantList", merchantList);
        return ApiResult.newSuccess(resultMap);
    }

    @GetMapping("/getSaleAccount")
    public ApiResult<?> getSaleAccount(@RequestParam String account) {
        ShopeeSaleAccountVO shopeeSaleAccountVO = new ShopeeSaleAccountVO();
        SaleAccountAndBusinessResponse shopeeAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, account, true);
        if(null == shopeeAccount) {
            return ApiResult.newSuccess("未找到账号 " + account);
        }
        shopeeSaleAccountVO = shopeeSaleAccountVO.toShopeeSaleAccountVO(shopeeAccount);
        return ApiResult.newSuccess(shopeeSaleAccountVO);
    }

    @GetMapping(value = "/deleteWatermarkTemplate/{id}")
    public ApiResult<?> deleteWatermarkTemplate(@PathVariable(value = "id", required = true) Integer id) {
        // 校验账号配置是否有在使用这个水印模板
        WatermarkTemplate watermarkTemplate = watermarkTemplateService.selectByPrimaryKey(id);
        if (watermarkTemplate == null) {
            return ApiResult.newError("水印模板未找到，请确认是否已被删除！");
        }

        if(StringUtils.isNotBlank(watermarkTemplate.getAccountNumber())) {
            ShopeeAccountConfig shopeeAccountConfig = shopeeAccountConfigService.selectByAccountNumber(watermarkTemplate.getAccountNumber());
            if(null != shopeeAccountConfig && StringUtils.isNotBlank(shopeeAccountConfig.getWatermarkTemplateIdStr())) {
                List<Integer> watermarkTemplateIds = CommonUtils.splitIntList(shopeeAccountConfig.getWatermarkTemplateIdStr(), ",");
                if (CollectionUtils.isNotEmpty(watermarkTemplateIds) && watermarkTemplateIds.contains(id)) {
                    return ApiResult.newError("该水印模板在使用中，请在店铺默认模板移除后再试!");
                }
            }
        }

        watermarkTemplateService.deleteByPrimaryKey(Arrays.asList(id));
        return ApiResult.newSuccess();
    }

    @PostMapping(value = "/sumTarget")
    public ApiResult<ShopMonthTotalDataDO> sumTarget(@RequestBody(required =false) List<String> accountNumbers) {
        try {
            ShopMonthTotalDataDO shopMonthTotalDataDO = shopeeAccountConfigService.sumTarget(accountNumbers);
            return ApiResult.newSuccess(shopMonthTotalDataDO);
        }catch(Exception e) {
            return ApiResult.newError("出错 " + e.getMessage());
        }
    }

    @PostMapping(value = "/getLast30dOrderAvg")
    public ApiResult<Double> getLast30dOrderAvg(@RequestParam String accountNumber) {
        try {
            EsShopeeAccountOrderReport accountOrderReport = esShopeeAccountOrderReportService.findAllById(accountNumber);
            return ApiResult.newSuccess(accountOrderReport == null ? null : accountOrderReport.getLast30dOrderAvg());
        }catch(Exception e) {
            return ApiResult.newError("出错 " + e.getMessage());
        }
    }

    @GetMapping("/getShopeeGroupList")
    public ApiResult<List<ShopeeAccountGroup>> getShopeeGroupList() {
        return ApiResult.newSuccess(shopeeAccountGroupService.getShopeeGroupList());
    }

    /**
     * 获取没有分组的账号
     * @return
     */
    @GetMapping("/getNotGroupAccount")
    public ApiResult<List<String>> getNotGroupAccount() {
        return ApiResult.newSuccess(shopeeAccountConfigService.getNotGroupAccount());
    }


    /**
     * Feign:提供给shopee广告系统调用
     * 根据账号获取账号名称和群组名称
     *
     * @return
     */
    @PostMapping("/getAccountAndGroupName")
    public ApiResult<Map<String, String>> getAccountAndGroupName(@RequestBody List<String> accountNumberList) {
        return ApiResult.newSuccess(shopeeAccountConfigService.getAccountAndGroupName(accountNumberList));
    }

    /**
     * 获取当前用户：子账号 商家 店铺集合
     *
     * @return
     */
    @GetMapping("/getSubMerchantAccountByMtsku")
    public ApiResult<?> getSubMerchantAccountByMtsku() {
//        List<SaleAccountAndBusinessResponse> list = AccountUtils.getAccountMerchantByEmployee(Platform.Shopee.name());
        ApiResult<List<SaleAccount>> result = EsAccountUtils.getAccountMerchantByEmployee(Platform.Shopee.name(), false, false);
        if(!result.isSuccess()){
            return result;
        }
        List<SaleAccount> list = result.getResult();

        //母账号集合
        Set<String> monAccountList = new HashSet<>();
        //子账号集合
        Set<String> subAccountList = new HashSet<>();
        //商家标记
//        Set<String> merchants = new HashSet<>();
        //子账号 关联下 商家集合
        Map<String, List<ShopeeSubMerchantAccountDto>> merchantMap = new HashMap<>();
        //商家 关联下 站点-店铺集合
        Map<String, Map<String, List<ShopeeSubMerchantAccountDto>>> accountMap = new HashMap<>();
        for (SaleAccount o : list) {
            if (StringUtils.isNotBlank(o.getColStr4())) {
                monAccountList.add(o.getColStr4());
            }

            if (StringUtils.isNotBlank(o.getColStr5())) {
                //添加子账号
                subAccountList.add(o.getColStr5());

                ShopeeMerchant shopeeMerchant = o.getShopeeMerchant();
                if (shopeeMerchant != null && StringUtils.isNotBlank(shopeeMerchant.getMerchantName()) && StringUtils.isNotBlank(o.getMerchantId())) {
                    //子账号下 - 添加商家
                    List<ShopeeSubMerchantAccountDto> merchantList = MapUtils.putIfAbsent(merchantMap, o.getColStr5(), () -> new ArrayList<>());

                    //不同的店铺归属同一个商家 但是店铺会分给不同的子账号
                    long count = merchantList.stream().filter(mm -> mm.getMerchantId().equalsIgnoreCase(o.getMerchantId())).count();
                    if(count == 0){
                        ShopeeSubMerchantAccountDto merchant = new ShopeeSubMerchantAccountDto();
                        merchant.setSubAccount(o.getColStr5());
                        merchant.setMerchantId(o.getMerchantId());
                        merchant.setMerchantName(shopeeMerchant.getMerchantName());
                        merchantList.add(merchant);
                    }

                    //商家下 - 添加店铺
                    Map<String, List<ShopeeSubMerchantAccountDto>> siteMap = MapUtils.putIfAbsent(accountMap, o.getMerchantId() + "-" + o.getColStr5(), () -> new HashMap<>());

                    if (StringUtils.isNotBlank(o.getAccountSite())) {
                        String accountSite = o.getAccountSite() == null ? "" : o.getAccountSite();
                        List<ShopeeSubMerchantAccountDto> accountList = MapUtils.putIfAbsent(siteMap, accountSite, () -> new ArrayList<>());
                        ShopeeSubMerchantAccountDto account = new ShopeeSubMerchantAccountDto();
                        account.setMerchantId(o.getMerchantId());
                        account.setAccountNumber(o.getAccountNumber());
                        account.setAccountSite(o.getAccountSite());
                        accountList.add(account);
                    }
                }
            }
        }

        //商家集合
        List<Object> merchantList = merchantMap.values().stream().collect(ArrayList::new, ArrayList::addAll, ArrayList::addAll);

        Map<String, Object> resultMap = new LinkedHashMap<>(6);
        resultMap.put("monAccountList", monAccountList);
        resultMap.put("subAccountList", subAccountList);
        resultMap.put("merchantMap", merchantMap);
        resultMap.put("accountMap", accountMap);
        resultMap.put("merchantList", merchantList);
        return ApiResult.newSuccess(resultMap);
    }

}
