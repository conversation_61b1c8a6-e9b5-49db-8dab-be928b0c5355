package com.estone.erp.publish.tidb.publishtidb.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListing;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListingCriteria;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListingExample;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-06-18 18:41:23
 */
public interface ShopeeBundleDealProductListingService {
    int countByExample(ShopeeBundleDealProductListingExample example);

    CQueryResult<ShopeeBundleDealProductListing> search(CQuery<ShopeeBundleDealProductListingCriteria> cquery);

    List<ShopeeBundleDealProductListing> selectByExample(ShopeeBundleDealProductListingExample example);

    ShopeeBundleDealProductListing selectByPrimaryKey(Long id);

    int insert(ShopeeBundleDealProductListing record);

    int updateByPrimaryKeySelective(ShopeeBundleDealProductListing record);

    int updateByExampleSelective(ShopeeBundleDealProductListing record, ShopeeBundleDealProductListingExample example);

    int deleteByPrimaryKey(List<Long> ids);

    void batchInsert(List<ShopeeBundleDealProductListing> successBundleDealProductListing);

}