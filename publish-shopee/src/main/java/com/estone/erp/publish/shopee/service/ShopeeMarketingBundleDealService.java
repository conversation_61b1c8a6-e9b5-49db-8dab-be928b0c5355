package com.estone.erp.publish.shopee.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.shopee.dto.ShopeeMarketingBundleDealDetailsDTO;
import com.estone.erp.publish.shopee.dto.ShopeeMarketingBundleDealVO;
import com.estone.erp.publish.shopee.model.ShopeeMarketingBundleDeal;
import com.estone.erp.publish.shopee.model.ShopeeMarketingBundleDealCriteria;
import com.estone.erp.publish.shopee.model.ShopeeMarketingBundleDealExample;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * 2024-06-18 18:53:27
 */
public interface ShopeeMarketingBundleDealService {
    int countByExample(ShopeeMarketingBundleDealExample example);

    CQueryResult<ShopeeMarketingBundleDealVO> search(CQuery<ShopeeMarketingBundleDealCriteria> cquery);

    List<ShopeeMarketingBundleDeal> selectByExample(ShopeeMarketingBundleDealExample example);

    ShopeeMarketingBundleDeal selectByPrimaryKey(Long id);

    int insert(ShopeeMarketingBundleDeal record);

    int updateByPrimaryKeySelective(ShopeeMarketingBundleDeal record);

    int updateByExampleSelective(ShopeeMarketingBundleDeal record, ShopeeMarketingBundleDealExample example);

    int deleteByPrimaryKey(List<Long> ids);

    List<ShopeeMarketingBundleDeal> getUpcomingOrOngoingVoucherBeforeSyncTime(String account, Timestamp time);

    /**
     * 获取T1执行明细
     *
     * @param accountNumbers
     * @return
     */
    List<ShopeeMarketingBundleDealDetailsDTO> getT1ExecutionDetailsByAccountNumber(List<String> accountNumbers);

    List<Long> getNotExpireBundleId(String accountNumber);

    /**
     * 获取商品列表物流ID和名称映射
     *
     * @param esShopeeItemList
     * @return
     */
    Map<Integer, String> getLogisticsIdAndNameMapByItemList(List<EsShopeeItem> esShopeeItemList);

    /**
     * 获取物流ID与批次商品映射
     *
     * @param esShopeeItemList
     * @param logisticsId
     * @return
     */
    Map<Integer, List<EsShopeeItem>> getLogisticsGroupsByItemsMap(List<EsShopeeItem> esShopeeItemList, Integer logisticsId);

}