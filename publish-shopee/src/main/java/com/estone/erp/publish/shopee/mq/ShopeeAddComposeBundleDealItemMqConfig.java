package com.estone.erp.publish.shopee.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqManualFactory;
import com.estone.erp.common.mq.RabbitMqVirtualHosts;
import com.estone.erp.common.mq.model.VhBinding;
import com.estone.erp.common.mq.model.VhQueue;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Description: 添加优惠套装队列配置
 * <AUTHOR>
 * @Date 2024/9/20 下午3:24
 */
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class ShopeeAddComposeBundleDealItemMqConfig {

    private int shopeeAddComposeBundleDealItemMqConsumers;
    private int shopeeAddComposeBundleDealItemMqPrefetchCount;
    private boolean shopeeAddComposeBundleDealItemMqListener;

    @Bean
    public VhQueue shopeeAddComposeBundleDealItemQueue() {
        return new VhQueue(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST, PublishQueues.SHOPEE_ADD_COMPOSE_BUNDLE_DEAL_ITEM_QUEUE, true, false, false, null);
    }

    @Bean
    public VhBinding shopeeAddComposeBundleDealItemQueueBinding() {
        return new VhBinding(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST, PublishQueues.SHOPEE_ADD_COMPOSE_BUNDLE_DEAL_ITEM_QUEUE, VhBinding.DestinationType.QUEUE,
                PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_ADD_COMPOSE_BUNDLE_DEAL_ITEM_QUEUE_KEY, null);
    }

    @Bean
    public ShopeeAddComposeBundleDealItemMqListener shopeeAddComposeBundleDealItemMqListener() {
        return new ShopeeAddComposeBundleDealItemMqListener();
    }

    @Bean
    public SimpleMessageListenerContainer shopeeAddComposeBundleDealItemMqListenerContainer(ShopeeAddComposeBundleDealItemMqListener listener, RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.PUBLISH_SHOPEE_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (shopeeAddComposeBundleDealItemMqListener) {
            container.setQueueNames(PublishQueues.SHOPEE_ADD_COMPOSE_BUNDLE_DEAL_ITEM_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(shopeeAddComposeBundleDealItemMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(shopeeAddComposeBundleDealItemMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(listener);// 监听处理类
        }

        return container;
    }


}
