package com.estone.erp.publish.shopee.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/20 上午8:59
 */
@Getter
@AllArgsConstructor
public enum ShopeeConfigTypeEnum {

    /**
     * 店铺活动
     */
    CAMPAIGN(1, "店铺活动"),

    /**
     * 优惠券
     */
    VOUCHER(2, "优惠券"),

    /**
     * 店内秒杀
     */
    FLASH_SALE(3, "店内秒杀"),

    /**
     * 关注礼
     */
    FOLLOW_PRIZE(4, "关注礼"),

    /**
     * 折扣活动
     */
    DISCOUNT(5, "折扣活动"),

    /**
     * 优惠套装
     */
    BUNDLE_DEAL(6, "优惠套装"),

    /**
     * 短视频
     */
    SHORT_VIDEO(7, "短视频"),

    /**
     * 上架商品
     */
    PRODUCT_ONLINE(8, "上架商品"),

    /**
     * 下架商品
     */
    PRODUCT_OFFLINE(9, "下架商品"),

    /**
     * 调整库存配置
     */
    INVENTORY(10, "调库存"),

    /**
     * 跨境活动
     */
    CROSS_BORDER_ACTIVITIES(11, "跨境活动"),
    /**
     * 竞价活动
     */
    BIDDING_ACTIVITY(12, "竞价活动"),

    /**
     * 组合优惠套装
     */
    COMPOSE_BUNDLE_DEAL(13, "组合优惠套装");

    private int code;
    private String desc;

    public static String convert(Integer value) {
        for (ShopeeConfigTypeEnum item : ShopeeConfigTypeEnum.values()) {
            if (item.getCode() == value) {
                return item.getDesc();
            }
        }
        return null;
    }

}
