package com.estone.erp.publish.shopee.jobHandler.bundledeal;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemRequest;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeItemService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.component.marking.BundleDealConfigParam;
import com.estone.erp.publish.shopee.enums.*;
import com.estone.erp.publish.shopee.handler.ShopeeBundleDealHandler;
import com.estone.erp.publish.shopee.model.*;
import com.estone.erp.publish.shopee.mq.model.ShopeeAddComposeBundleDealItemMessage;
import com.estone.erp.publish.shopee.service.ShopeeConfigTaskService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingBundleDealService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingConfigService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListing;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListingExample;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeBundleDealProductListingService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;



/**
 * 创建组合优惠套装活动
 */
@Component
public class ShopeeCreateComposeBundleDealJobHandler extends AbstractJobHandler {

    @Resource
    private ShopeeBundleDealProductListingService shopeeBundleDealProductListingService;
    @Autowired
    private ShopeeMarketingBundleDealService shopeeMarketingBundleDealService;
    @Resource
    private ShopeeMarketingConfigService shopeeMarketingConfigService;
    @Resource
    private ShopeeConfigTaskService shopeeConfigTaskService;
    @Resource
    private ShopeeBundleDealHandler shopeeBundleDealHandler;
    @Resource
    private EsShopeeItemService esShopeeItemService;
    @Resource
    private RabbitMqSender rabbitMqSender;


    public ShopeeCreateComposeBundleDealJobHandler() {
        super(ShopeeCreateComposeBundleDealJobHandler.class.getName());
    }


    @Data
    public static class InnerParam {
        private List<String> accountNumberList;
        private List<Integer> configIdList;
        private String executeType;
    }

    /**
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    @XxlJob("ShopeeCreateComposeBundleDealJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            XxlJobLogger.log("参数不能为空");
            return ReturnT.FAIL;
        }

        if ("renewal".equals(innerParam.getExecuteType())) {
            renewalExpireBundleDeal();
            return ReturnT.SUCCESS;
        }


        // 获取最大SPU组合
        List<List<String>> composeSpuList = shopeeBundleDealHandler.getConnectedSpuComponents();
        if (CollectionUtils.isEmpty(composeSpuList)) {
            XxlJobLogger.log("未获取到最大spu组合数据集");
            return ReturnT.SUCCESS;
        }
        XxlJobLogger.log("获取最大spu组合数量：{}", composeSpuList.size());
        // 获取可执行配置，按店铺执行, `shopee_marketing_config`
        List<String> paramAccountList = innerParam.getAccountNumberList();
        List<Integer> configIdList = innerParam.getConfigIdList();

        Date currentDate = new Date();
        List<ShopeeMarketingConfig> runningConfigByDateHours = getRunningConfigByDateHours(currentDate, null);
        if (CollectionUtils.isNotEmpty(configIdList)) {
            runningConfigByDateHours = runningConfigByDateHours.stream().filter(o -> configIdList.contains(o.getId())).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(runningConfigByDateHours)) {
            XxlJobLogger.log("未找到可执行的营销配置");
            return ReturnT.SUCCESS;
        }
        XxlJobLogger.log("找到{}个可执行的营销配置", runningConfigByDateHours.size());
        CountDownLatch countDownLatch = new CountDownLatch(runningConfigByDateHours.size());

        for (ShopeeMarketingConfig marketingConfig : runningConfigByDateHours) {
            ShopeeExecutors.createBundleDealJob(() -> {
                try {
                    // 根据配置生成优惠套装
                    syncTask(marketingConfig, paramAccountList, composeSpuList);
                } catch (Exception e) {
                    XxlJobLogger.log("配置名称：{}，创建优惠套装失败,原因：{}", marketingConfig.getName(), e.getMessage());
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        // 设置超时时间，避免无限等待
        boolean completed = countDownLatch.await(30, TimeUnit.MINUTES);
        if (!completed) {
            XxlJobLogger.log("警告：部分任务未在30分钟内完成，可能存在超时任务");
        }

        XxlJobLogger.log("-------创建优惠套装job结束--------");
        return ReturnT.SUCCESS;


    }

    /**
     * 查询已过期组合优惠套装活动
     */
    private void renewalExpireBundleDeal() {
        // 查询最近到期的组合优惠套装活动
        List<ShopeeMarketingBundleDeal> shopeeMarketingBundleDealList = getExpireBundleDeal();
        if (CollectionUtils.isEmpty(shopeeMarketingBundleDealList)) {
            XxlJobLogger.log("未找到过期的组合优惠套装活动");
            return;
        }

        List<Integer> configIds = shopeeMarketingBundleDealList.stream().map(ShopeeMarketingBundleDeal::getConfigId)
                .filter(Objects::nonNull)
                .map(Long::intValue)
                .distinct()
                .collect(Collectors.toList());
        // 进行中的配置
        List<ShopeeMarketingConfig> shopeeMarketingConfigList = getRunningConfigByDateHours(new Date(), configIds);
        if (CollectionUtils.isEmpty(shopeeMarketingConfigList)) {
            XxlJobLogger.log("未找到进行中的配置");
            return;
        }

        Map<Integer, ShopeeMarketingConfig> marketingConfigParamMap = shopeeMarketingConfigList.stream().collect(Collectors.toMap(ShopeeMarketingConfig::getId, Function.identity()));
        shopeeMarketingBundleDealList.stream()
                .filter(bundleDeal -> marketingConfigParamMap.containsKey(bundleDeal.getConfigId().intValue()))
                .forEach(bundleDeal -> {
                    try {
                        // 续期组合活动
                        ShopeeMarketingConfig config = marketingConfigParamMap.get(bundleDeal.getConfigId().intValue());
                        renewalComposeBundleDeal(bundleDeal, config);
                    } catch (Exception e) {
                        XxlJobLogger.log("续期组合优惠套装活动失败，活动ID：{}，原因：{}", bundleDeal.getId(), e.getMessage());
                    }
                });
        XxlJobLogger.log("续期普通优惠套装活动完成");
    }

    private void renewalComposeBundleDeal(ShopeeMarketingBundleDeal bundleDeal, ShopeeMarketingConfig config) {
        XxlJobLogger.log("续期组合优惠套装活动，店铺：{}，活动ID：{}, 活动名称：{}", bundleDeal.getAccountNumber(), bundleDeal.getId(), bundleDeal.getName());

        // 获取过期组合活动的item
        List<ShopeeBundleDealProductListing> shopeeBundleDealProductListings = getBundleDealItemList(bundleDeal.getBundleDealId());
        if (CollectionUtils.isEmpty(shopeeBundleDealProductListings)) {
            XxlJobLogger.log("未找到过期组合优惠套装活动的商品");
            return;
        }

        List<String> itemIds = shopeeBundleDealProductListings.stream()
                .map(ShopeeBundleDealProductListing::getItemId)
                .map(String::valueOf)
                .collect(Collectors.toList());

        List<EsShopeeItem> esShopeeItemList = getEsShopeeItemsByItemIds(itemIds, bundleDeal.getAccountNumber());
        if (CollectionUtils.isEmpty(esShopeeItemList)) {
            XxlJobLogger.log("未找到过期组合优惠套装活动的商品");
            return;
        }
        XxlJobLogger.log("找到过期组合优惠套装活动的商品数量：{}", esShopeeItemList.size());
        String composeBundelDealName = "组合产品" + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        XxlJobLogger.log("创建新的组合优惠套装，accountNumber:{},suitName:{}, itemSize:{}", bundleDeal.getAccountNumber(), composeBundelDealName, esShopeeItemList.size());
        // 获取店铺信息
        SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), bundleDeal.getAccountNumber(), true);
        List<String> spuCompose = new ArrayList<>();
        if (StringUtils.isNotBlank(bundleDeal.getSpuCompose())) {
            spuCompose = Arrays.asList(bundleDeal.getSpuCompose().split(","));
        }
        config.setName("续期" + config.getName() + "," + bundleDeal.getId() + ",config:" + config.getName());
        ShopeeMarketingBundleDeal composeBundelDeal = shopeeBundleDealHandler.createComposeBundelDeal(config, spuCompose, saleAccount, composeBundelDealName);
        if (composeBundelDeal == null) {
            return;
        }
        // 添加商品到组合优惠套装中
        shopeeBundleDealHandler.submitItem2ComposeBundelDeal(esShopeeItemList, composeBundelDeal, saleAccount, config);
        XxlJobLogger.log("续期组合优惠套装活动完成，店铺：{}，活动ID：{}, 活动名称：{}", bundleDeal.getAccountNumber(), bundleDeal.getId(), bundleDeal.getName());
    }

    private List<EsShopeeItem> getEsShopeeItemsByItemIds(List<String> itemIds, String accountNumber) {
        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemSeller(accountNumber);
        request.setItemIdList(itemIds);
        request.setQueryFields(new String[]{"itemId", "spu", "articleNumber"});
        request.setItemStatus("NORMAL");
        request.setStockNotEqual(0);
        request.setIsGoods(true);
        List<EsShopeeItem> esShopeeItems = esShopeeItemService.getEsShopeeItems(request);
        if (CollectionUtils.isEmpty(esShopeeItems)) {
            return Collections.emptyList();
        }
        return esShopeeItems.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(EsShopeeItem::getItemId, Function.identity(), (existing, replacement) -> existing),
                        map -> new ArrayList<>(map.values())
                ));
    }

    private List<ShopeeBundleDealProductListing> getBundleDealItemList(Long bundleDealId) {
        ShopeeBundleDealProductListingExample example = new ShopeeBundleDealProductListingExample();
        example.createCriteria().andBundleDealIdEqualTo(bundleDealId);
        return shopeeBundleDealProductListingService.selectByExample(example);
    }


    private List<ShopeeMarketingBundleDeal> getExpireBundleDeal() {
        LocalDateTime startDateTime = LocalDateTime.of(LocalDate.now().minusDays(1), LocalTime.MIN);
        LocalDateTime endDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        ShopeeMarketingBundleDealExample shopeeMarketingBundleDealExample = new ShopeeMarketingBundleDealExample();
        ShopeeMarketingBundleDealExample.Criteria criteria = shopeeMarketingBundleDealExample.createCriteria();
        criteria.andConfigIdIsNotNull();
        criteria.andStatusEqualTo(ShopeeMarketingBundleDealStatusEnum.EXPIRED.getCode());
        criteria.andSuitTypeEqualTo(ShopeeMarketingBundleDealSuitTypeEnum.COMPOSE.getCode());
        criteria.andBundleDealEndTimeBetween(Timestamp.valueOf(startDateTime), Timestamp.valueOf(endDateTime));
        return shopeeMarketingBundleDealService.selectByExample(shopeeMarketingBundleDealExample);
    }

    /**
     * 执行任务
     *
     * @param config           营销配置-优惠套装
     * @param paramAccountList 参数店铺
     * @param composeSpuList   最大spu组合
     */
    private void syncTask(ShopeeMarketingConfig config, List<String> paramAccountList, List<List<String>> composeSpuList) {
        XxlJobLogger.log("组合优惠套装配置配置：{} 创建优惠套装开始执行", config);
        Timestamp nowTime = new Timestamp(new Date().getTime());
        BundleDealConfigParam bundleDealConfigParam = JSON.parseObject(config.getRuleJson(), BundleDealConfigParam.class);
        if (bundleDealConfigParam == null) {
            XxlJobLogger.log("配置参数解析失败");
            return;
        }
        List<BundleDealConfigParam.DealDetail> suitDealDetailList = bundleDealConfigParam.getSuitDealDetailList();
        if (CollectionUtils.isEmpty(suitDealDetailList)) {
            XxlJobLogger.log("配置名称：{}, 未配置组合优惠套装", config.getName());
            return;
        }


        List<String> accounts = shopeeConfigTaskService.filterExistingAccountList(config.getAccounts(), ShopeeConfigTypeEnum.COMPOSE_BUNDLE_DEAL, config.getId());
        if (CollectionUtils.isNotEmpty(paramAccountList)) {
            accounts.retainAll(paramAccountList);
        }

        int processedCount = 0;
        int successCount = 0;

        for (String accountNumber : accounts) {
            try {
                processedCount++;
                XxlJobLogger.log("单个店铺：{}创建优惠套装开始执行 ({}/{})", accountNumber, processedCount, accounts.size());

                // 判断是否是SIP店铺
                SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), accountNumber, true);
                if (BooleanUtils.isTrue(saleAccount.getColBool2())) {
                    XxlJobLogger.log("{}为SIP店铺，不做处理", saleAccount.getAccountNumber());
                    continue;
                }

                ShopeeConfigTask shopeeConfigTask = getShopeeConfigTask(config, accountNumber, nowTime);
                int validSpuComposeCount = 0;
                // 遍历每个最大spu组合，查询店铺是否存在对应SPU的listing, 如果listing数量超过2个则创建spu组合优惠套装
                for (List<String> spuCompose : composeSpuList) {
                    try {
                        List<String> spuComposeItemSpus = findAccountSpuComposeItemSpu(accountNumber, spuCompose);
                        XxlJobLogger.log("店铺：{}，SPU组合：{}，listing数量：{}", accountNumber, String.join(",", spuCompose), spuComposeItemSpus.size());
                        if (CollectionUtils.isNotEmpty(spuComposeItemSpus) && spuComposeItemSpus.size() >= 2) {
                            validSpuComposeCount++;
                            // 发送消息
                            pushCreatedComposeBundleDealMessage(config, accountNumber, spuCompose, validSpuComposeCount, shopeeConfigTask.getId());

                        }
                    } catch (Exception e) {
                        XxlJobLogger.log("处理店铺：{}的SPU组合：{}时发生错误：{}", accountNumber, String.join(",", spuCompose), e.getMessage());
                    }
                }
                shopeeConfigTaskService.successTask(shopeeConfigTask.getId(), "处理完成，找到{}个有效的SPU组合");
                XxlJobLogger.log("店铺：{}处理完成，找到{}个有效的SPU组合", accountNumber, validSpuComposeCount);
                successCount++;

            } catch (Exception e) {
                XxlJobLogger.log("配置名称：{}，店铺：{}创建优惠套装失败,原因：{}", config.getName(), accountNumber, e.getMessage());
            }
        }

        XxlJobLogger.log("配置：{}执行完成，处理店铺数：{}，成功数：{}", config.getName(), processedCount, successCount);


    }



    /**
     * 发送创建组合优惠套装消息
     * SHOPEE_ADD_COMPOSE_BUNDLE_DEAL_ITEM_QUEUE
     *
     * @param config
     * @param accountNumber
     * @param spuCompose
     * @param messageIndex
     * @param taskId
     */
    private void pushCreatedComposeBundleDealMessage(ShopeeMarketingConfig config, String accountNumber, List<String> spuCompose, int messageIndex, Integer taskId) {
        ShopeeAddComposeBundleDealItemMessage message = new ShopeeAddComposeBundleDealItemMessage();
        message.setAccountNumber(accountNumber);
        message.setConfigId(config.getId());
        message.setIndex(messageIndex);
        message.setTaskId(taskId);
        message.setSpuCompose(spuCompose);
        rabbitMqSender.publishShopeeVHostRabbitTemplateSend(PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_ADD_COMPOSE_BUNDLE_DEAL_ITEM_QUEUE_KEY, message);

    }

    private List<String> findAccountSpuComposeItemSpu(String accountNumber, List<String> spuCompose) {
        EsShopeeItemRequest request = new EsShopeeItemRequest();
        request.setItemSeller(accountNumber);
        request.setSpuList(spuCompose);
        request.setQueryFields(new String[]{"itemId", "spu", "articleNumber"});
        request.setItemStatus("NORMAL");
        request.setStockNotEqual(0);
        request.setIsGoods(true);
        List<EsShopeeItem> itemServiceEsShopeeItems = esShopeeItemService.getEsShopeeItems(request);

        if (CollectionUtils.isEmpty(itemServiceEsShopeeItems)) {
            return Collections.emptyList();
        }

        return itemServiceEsShopeeItems.stream().map(EsShopeeItem::getSpu).distinct().collect(Collectors.toList());
    }



    /**
     * 获取任务参数
     *
     * @param marketingConfig
     * @param accountNumber
     * @param timestamp
     * @return
     */
    private ShopeeConfigTask getShopeeConfigTask(ShopeeMarketingConfig marketingConfig, String accountNumber, Timestamp timestamp) {
        ShopeeConfigTask configTask = new ShopeeConfigTask();
        configTask.setConfigType(ShopeeConfigTypeEnum.COMPOSE_BUNDLE_DEAL.getCode());
        configTask.setConfigId(marketingConfig.getId());
        configTask.setConfigName(marketingConfig.getName());
        configTask.setConfigRuleJson(marketingConfig.getRuleJson());
        configTask.setAccountNumber(accountNumber);
        configTask.setExecTime(timestamp);
        configTask.setOperatorStatus(ShopeeConfigOperatorStatusEnum.WAITING.getCode());
        configTask.setOperatorTime(timestamp);
        configTask.setCreatedTime(timestamp);
        configTask.setUpdatedTime(timestamp);
        shopeeConfigTaskService.insert(configTask);
        return configTask;
    }


    private List<ShopeeMarketingConfig> getRunningConfigByDateHours(Date date, List<Integer> configIds) {
        String day = DateUtils.format(date, "yyyy-MM-dd");
        // yyyy-MM-dd HH
        ShopeeMarketingConfigExample example = new ShopeeMarketingConfigExample();
        ShopeeMarketingConfigExample.Criteria criteria = example.createCriteria();
        criteria.andTypeEqualTo(ShopeeMarketingConfigTypeEnum.BUNDLE_DEAL.getCode());
        criteria.andStatusEqualTo(1);
        if (CollectionUtils.isNotEmpty(configIds)) {
            criteria.andIdIn(configIds);
        }

        List<ShopeeMarketingConfig> shopeeMarketingConfigs = shopeeMarketingConfigService.selectByExample(example);

        // 筛选出当前时间段的配置
        List<ShopeeMarketingConfig> list = new ArrayList<>();
        for (ShopeeMarketingConfig shopeeMarketingConfig : shopeeMarketingConfigs) {
            Timestamp strategyStartTime = shopeeMarketingConfig.getStrategyStartTime();
            Timestamp strategyEndTime = shopeeMarketingConfig.getStrategyEndTime();
            String execTime = shopeeMarketingConfig.getExecTime();
            if (org.apache.commons.lang.StringUtils.isBlank(execTime)) {
                XxlJobLogger.log("配置id:[{}],执行时间为空", shopeeMarketingConfig.getId());
                continue;
            }
            String[] split = execTime.split(":");
            String hour = split[0];
            Date execTimeDate = DateUtils.parseDate(day + " " + hour, "yyyy-MM-dd HH", "yyyy-MM-dd HH");
            if (execTimeDate == null) {
                continue;
            }
            // 接下来判断执行时间是否在策略时间内
            if (strategyStartTime != null && strategyEndTime != null) {
                if (DateUtils.betweenStartTimeAndEndTime(execTimeDate, strategyStartTime, strategyEndTime)) {
                    list.add(shopeeMarketingConfig);
                    continue;
                }
            }
            if (strategyStartTime != null && strategyEndTime == null) {
                if (execTimeDate.getTime() == strategyStartTime.getTime() || execTimeDate.after(strategyStartTime)) {
                    list.add(shopeeMarketingConfig);
                    continue;
                }
            }
            if (strategyStartTime == null && strategyEndTime != null) {
                if (execTimeDate.getTime() == strategyEndTime.getTime() || execTimeDate.before(strategyEndTime)) {
                    list.add(shopeeMarketingConfig);
                    continue;
                }
            }
            if (strategyStartTime == null && strategyEndTime == null) {
                list.add(shopeeMarketingConfig);
            }
        }
        return list;
    }
}
