package com.estone.erp.publish.shopee.component.marking;

import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.publish.shopee.annotation.NeedToLog;
import com.estone.erp.publish.shopee.enums.ShopeeBundleDealTypeEnum;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;

/**
 * 店铺活动配置
 */
@Data
public class BundleDealConfigParam implements MarketingConfigParam {

    /**
     * 优惠券领取时长:月
     */
    @NeedToLog(description = "优惠券领取时长")
    private int duration;
    /**
     * 套装类型描述
     */
    @NeedToLog(description = "套装类型")
    private String ruleTypeDesc;
    /**
     * 套装类型
     */
    @NeedToLog(description = "套装类型规则")
    private List<DealDetail> dealDetailList;


    /**
     * 套装类型描述
     */
    @NeedToLog(description = "组合出单套装类型")
    private String suitRuleTypeDesc;

    /**
     * 套装类型
     */
    @NeedToLog(description = "组合出单套装类型规则")
    private List<DealDetail> suitDealDetailList;

    /**
     * 购买最大数量限制
     */
    @NeedToLog(description = "购买限制")
    private int purchase_limit;

    @Data
    public static class DealDetail {
        /**
         * 套装类型,套装特价 = 1 ；折扣比例 = 2； 折扣金额 = 3
         */
        private Integer rule_type;

        /**
         * rule_type=3时必填
         */
        private Float discount_value;

        /**
         * rule_type=1时必填
         */
        private Float fix_price;

        /**
         * rule_type=2时必填
         */
        private Integer discount_percentage;

        /**
         * 购买的商品数
         */
        private Integer productNum;

        public String toString() {
            StringJoiner sj = new StringJoiner("");
//            if (Objects.nonNull(rule_type)) {
//                sj.add("套装类型:" + ShopeeBundleDealTypeEnum.convert(rule_type));
//            }
            if (Objects.equals(rule_type, ShopeeBundleDealTypeEnum.FIX_PRICE.getCode())) {
                sj.add(" 购买" + productNum + "个商品共" + fix_price + "￥");
            } else if (Objects.equals(rule_type, ShopeeBundleDealTypeEnum.DISCOUNT_PERCENTAGE.getCode())) {
                sj.add(" 购买" + productNum + "个商品享受" + discount_percentage + "%折扣");
            } else if (Objects.equals(rule_type, ShopeeBundleDealTypeEnum.DISCOUNT_VALUE.getCode())) {
                sj.add(" 购买" + productNum + "个商品减" + discount_value + "￥");
            }
            return sj.toString();
        }

    }


    @Override
    public boolean check() {
        if (duration <= 0 || purchase_limit <= 0) {
            return false;
        }
        if (CollectionUtils.isEmpty(dealDetailList)) {
            return false;
        }

        for (DealDetail dealDetail : dealDetailList) {
            if (StringUtils.isBlank(ShopeeBundleDealTypeEnum.convert(dealDetail.getRule_type()))) {
                return false;
            }

            if (ObjectUtils.isEmpty(dealDetail.getProductNum()) || dealDetail.getProductNum() <= 1) {
                throw new BusinessException("商品数要大于1");
            }


            if (ShopeeBundleDealTypeEnum.DISCOUNT_PERCENTAGE.getCode().equals(dealDetail.getRule_type()) && (dealDetail.getDiscount_percentage() < 1 || dealDetail.getDiscount_percentage() > 30)) {
                return false;
            }
            if (ShopeeBundleDealTypeEnum.DISCOUNT_VALUE.getCode().equals(dealDetail.getRule_type()) && (dealDetail.getDiscount_value() <= 0)) {
                return false;
            }
            if (ShopeeBundleDealTypeEnum.FIX_PRICE.getCode().equals(dealDetail.getRule_type()) && (dealDetail.getFix_price() <= 0)) {
                return false;
            }

        }

        for (DealDetail dealDetail : suitDealDetailList) {
            if (StringUtils.isBlank(ShopeeBundleDealTypeEnum.convert(dealDetail.getRule_type()))) {
                return false;
            }

            if (ObjectUtils.isEmpty(dealDetail.getProductNum()) || dealDetail.getProductNum() <= 1) {
                throw new BusinessException("商品数要大于1");
            }


            if (ShopeeBundleDealTypeEnum.DISCOUNT_PERCENTAGE.getCode().equals(dealDetail.getRule_type()) && (dealDetail.getDiscount_percentage() < 1 || dealDetail.getDiscount_percentage() > 30)) {
                return false;
            }
            if (ShopeeBundleDealTypeEnum.DISCOUNT_VALUE.getCode().equals(dealDetail.getRule_type()) && (dealDetail.getDiscount_value() <= 0)) {
                return false;
            }
            if (ShopeeBundleDealTypeEnum.FIX_PRICE.getCode().equals(dealDetail.getRule_type()) && (dealDetail.getFix_price() <= 0)) {
                return false;
            }

        }


        if (dealDetailList.size() > 1) {
            for (int i = 0; i < dealDetailList.size() - 1; i++) {
                if (dealDetailList.get(i + 1).getProductNum() <= dealDetailList.get(i).getProductNum()) {
                    throw new BusinessException("较高阶层购买的商品数必须大于较低阶层");
                }
                if (ShopeeBundleDealTypeEnum.DISCOUNT_PERCENTAGE.getCode().equals(dealDetailList.get(0).getRule_type())) {
                    if (dealDetailList.get(i + 1).getDiscount_percentage() < dealDetailList.get(i).getDiscount_percentage()) {
                        throw new BusinessException("较高阶层折扣比例必须大于等于较低阶层");
                    }
                }

            }

        }


        if (suitDealDetailList.size() > 1) {
            for (int i = 0; i < suitDealDetailList.size() - 1; i++) {
                if (suitDealDetailList.get(i + 1).getProductNum() <= suitDealDetailList.get(i).getProductNum()) {
                    throw new BusinessException("较高阶层购买的商品数必须大于较低阶层");
                }
                if (ShopeeBundleDealTypeEnum.DISCOUNT_PERCENTAGE.getCode().equals(suitDealDetailList.get(0).getRule_type())) {
                    if (suitDealDetailList.get(i + 1).getDiscount_percentage() < suitDealDetailList.get(i).getDiscount_percentage()) {
                        throw new BusinessException("较高阶层折扣比例必须大于等于较低阶层");
                    }
                }

            }

        }


        return MarketingConfigParam.super.check();
    }

    @Override
    public String toExcel() {
        StringJoiner sj = new StringJoiner("\n");
        sj.add("优惠券领取时长: " + duration);
        DealDetail dealDetail1 = dealDetailList.get(0);
        Integer ruleType = dealDetail1.getRule_type();
        String convert = ShopeeBundleDealTypeEnum.convert(ruleType);
        sj.add("普通套装类型:");
        sj.add("套装类型: " + convert);
        int i = 0;
        for (DealDetail dealDetail : dealDetailList) {
            i++;
            if (Objects.equals(ruleType, ShopeeBundleDealTypeEnum.FIX_PRICE.getCode())) {
                sj.add("第" + i + "层" + " 购买" + dealDetail.getProductNum() + "个商品共" + dealDetail.getFix_price() + "￥");
            } else if (Objects.equals(ruleType, ShopeeBundleDealTypeEnum.DISCOUNT_PERCENTAGE.getCode())) {
                sj.add("第" + i + "层" + " 购买" + dealDetail.getProductNum() + "个商品享受" + dealDetail.getDiscount_percentage() + "%折扣");
            } else if (Objects.equals(ruleType, ShopeeBundleDealTypeEnum.DISCOUNT_VALUE.getCode())) {
                sj.add("第" + i + "层" + " 购买" + dealDetail.getProductNum() + "个商品减" + dealDetail.getDiscount_value() + "￥");
            }
        }

        if (CollectionUtils.isNotEmpty(suitDealDetailList)) {
            DealDetail suitDetail = suitDealDetailList.get(0);
            Integer suitRuleType = suitDetail.getRule_type();
            String convert2 = ShopeeBundleDealTypeEnum.convert(suitRuleType);
            sj.add("组合出单套装类型:");
            sj.add("套装类型: " + convert2);
            int j = 0;
            for (DealDetail dealDetail : suitDealDetailList) {
                j++;
                if (Objects.equals(dealDetail.getRule_type(), ShopeeBundleDealTypeEnum.FIX_PRICE.getCode())) {
                    sj.add("第" + j + "层" + " 购买" + dealDetail.getProductNum() + "个商品共" + dealDetail.getFix_price() + "￥");
                } else if (Objects.equals(dealDetail.getRule_type(), ShopeeBundleDealTypeEnum.DISCOUNT_PERCENTAGE.getCode())) {
                    sj.add("第" + j + "层" + " 购买" + dealDetail.getProductNum() + "个商品享受" + dealDetail.getDiscount_percentage() + "%折扣");
                } else if (Objects.equals(dealDetail.getRule_type(), ShopeeBundleDealTypeEnum.DISCOUNT_VALUE.getCode())) {
                    sj.add("第" + j + "层" + " 购买" + dealDetail.getProductNum() + "个商品减" + dealDetail.getDiscount_value() + "￥");
                }
            }
        }

        sj.add("购买限制: " + purchase_limit);
        return sj.toString();
    }

    @Override
    public String initJson() {
        // 补充这个字段
        this.ruleTypeDesc = ShopeeBundleDealTypeEnum.convert(dealDetailList.get(0).getRule_type());
        this.suitRuleTypeDesc = ShopeeBundleDealTypeEnum.convert(suitDealDetailList.get(0).getRule_type());
        return MarketingConfigParam.super.initJson();
    }
}
