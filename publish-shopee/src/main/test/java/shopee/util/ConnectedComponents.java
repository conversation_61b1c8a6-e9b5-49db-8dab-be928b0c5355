package shopee.util;

import java.util.*;

public class ConnectedComponents {

    /**
     * 构建图的邻接表表示
     *
     * @param edges 边的列表，每条边可以包含2个或更多节点
     * @return 图的邻接表表示
     */
    public static Map<String, Set<String>> buildGraph(List<List<String>> edges) {
        Map<String, Set<String>> graph = new HashMap<>();

        for (List<String> edge : edges) {
            // 为每个节点初始化邻接集合
            for (String node : edge) {
                graph.putIfAbsent(node, new HashSet<>());
            }

            // 将每个节点的邻居加入图中（无向图，互相连接）
            for (String node : edge) {
                Set<String> neighbors = graph.get(node);
                for (String neighbor : edge) {
                    if (!neighbor.equals(node)) {
                        neighbors.add(neighbor);
                    }
                }
            }
        }

        return graph;
    }

    /**
     * 深度优先搜索，找到从指定节点开始的连通分量
     *
     * @param graph   图的邻接表表示
     * @param start   起始节点
     * @param visited 已访问节点集合
     * @return 当前连通分量中的所有节点
     */
    public static List<String> dfs(Map<String, Set<String>> graph, String start, Set<String> visited) {
        visited.add(start); // 标记当前节点为已访问
        List<String> component = new ArrayList<>();
        component.add(start); // 将当前节点加入连通分量

        // 遍历当前节点的所有邻居
        Set<String> neighbors = graph.getOrDefault(start, new HashSet<>());
        for (String neighbor : neighbors) {
            if (!visited.contains(neighbor)) {
                // 递归访问未访问的邻居节点
                List<String> subComponent = dfs(graph, neighbor, visited);
                component.addAll(subComponent);
            }
        }

        return component;
    }

    /**
     * 找到图中所有的连通分量
     *
     * @param graph 图的邻接表表示
     * @return 所有连通分量的列表
     */
    public static List<List<String>> findConnectedComponents(Map<String, Set<String>> graph) {
        Set<String> visited = new HashSet<>(); // 已访问的节点
        List<List<String>> components = new ArrayList<>(); // 所有连通分量

        // 遍历图中的所有节点
        for (String node : graph.keySet()) {
            if (!visited.contains(node)) {
                // 从未访问的节点开始DFS，找到一个连通分量
                List<String> component = dfs(graph, node, visited);
                components.add(component);
            }
        }

        return components;
    }

    /**
     * 主方法，演示算法的使用
     */
    public static void main(String[] args) {
        // 示例输入数据
        List<List<String>> edges = Arrays.asList(
                Arrays.asList("A", "B", "C"),  // A、B、C 三个节点互相连通
                Arrays.asList("B", "F"),       // B 和 F 连通
                Arrays.asList("D", "E", "B"),      // D 和 E 连通，但与其他节点分离
                Arrays.asList("G", "k")
        );

        edges.forEach(edge -> {
            System.out.println(String.join(",", edge));
        });
        // 构建图
        Map<String, Set<String>> graph = buildGraph(edges);

        // 打印图的结构（用于调试）
//        System.out.println("图的邻接表表示:");
//        for (Map.Entry<String, Set<String>> entry : graph.entrySet()) {
//            System.out.println(entry.getKey() + " -> " + entry.getValue());
//        }
//        System.out.println();

        // 找到所有连通分量
        List<List<String>> components = findConnectedComponents(graph);

        // 输出结果
        System.out.println("连通分量:");
        for (int i = 0; i < components.size(); i++) {
            List<String> component = components.get(i);
            // 对节点进行排序以便于阅读
            Collections.sort(component);
            System.out.println("out " + (i + 1) + ": " + String.join(", ", component));
        }
    }
} 