<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.shopee.mapper.ShopeeMarketingBundleDealMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.shopee.model.ShopeeMarketingBundleDeal" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="bundle_deal_id" property="bundleDealId" jdbcType="BIGINT" />
    <result column="merchant_id" property="merchantId" jdbcType="BIGINT" />
    <result column="merchant_name" property="merchantName" jdbcType="VARCHAR" />
    <result column="account_number" property="accountNumber" jdbcType="VARCHAR" />
    <result column="site" property="site" jdbcType="VARCHAR" />
    <result column="logistics_id" property="logisticsId" jdbcType="INTEGER"/>
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="product_number" property="productNumber" jdbcType="INTEGER" />
    <result column="add_status" property="addStatus" jdbcType="INTEGER"/>
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="bundle_deal_start_time" property="bundleDealStartTime" jdbcType="TIMESTAMP" />
    <result column="bundle_deal_end_time" property="bundleDealEndTime" jdbcType="TIMESTAMP" />
    <result column="sync_time" property="syncTime" jdbcType="TIMESTAMP" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
    <result column="rule_json" property="ruleJson"/>
    <result column="last_sync_product_exec_time" property="lastSyncProductExecTime" />
    <result column="stop_status" property="stopStatus" jdbcType="INTEGER" />
    <result column="config_id" property="configId" jdbcType="BIGINT" />
    <result column="suit_type" property="suitType" jdbcType="INTEGER"/>
    <result column="spu_compose" property="spuCompose" jdbcType="VARCHAR"/>
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id
    , bundle_deal_id, merchant_id, merchant_name, account_number, site,`name`, `type`,
    product_number, `add_status`, `status`, bundle_deal_start_time, bundle_deal_end_time, sync_time,
    created_time, updated_time,rule_json,last_sync_product_exec_time,stop_status,config_id, logistics_id, suit_type, spu_compose
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.shopee.model.ShopeeMarketingBundleDealExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from shopee_marketing_bundle_deal
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from shopee_marketing_bundle_deal
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from shopee_marketing_bundle_deal
    where id IN
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.shopee.model.ShopeeMarketingBundleDeal" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into shopee_marketing_bundle_deal (bundle_deal_id, merchant_id, merchant_name,
    account_number, site, logistics_id, `name`,
    `type`, product_number, `status`, add_status,
    bundle_deal_start_time, bundle_deal_end_time,
    sync_time, created_time, updated_time,rule_json,config_id, suit_type, spu_compose
    )
    values (#{bundleDealId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, #{merchantName,jdbcType=VARCHAR},
    #{accountNumber,jdbcType=VARCHAR}, #{site,jdbcType=VARCHAR}, #{logisticsId,jdbcType=INTEGER},
    #{name,jdbcType=VARCHAR},
    #{type,jdbcType=INTEGER}, #{productNumber,jdbcType=INTEGER}, #{status,jdbcType=INTEGER},
    #{addStatus,jdbcType=INTEGER},
    #{bundleDealStartTime,jdbcType=TIMESTAMP}, #{bundleDealEndTime,jdbcType=TIMESTAMP},
    #{syncTime,jdbcType=TIMESTAMP}, #{createdTime,jdbcType=TIMESTAMP},
    #{updatedTime,jdbcType=TIMESTAMP},#{ruleJson},#{configId}, #{suitType,jdbcType=INTEGER}, #{spuCompose,jdbcType=VARCHAR}
    )
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.shopee.model.ShopeeMarketingBundleDealExample" resultType="java.lang.Integer" >
    select count(*) from shopee_marketing_bundle_deal
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <select id="getT1ExecutionDetailsByAccountNumber"
          resultType="com.estone.erp.publish.shopee.dto.ShopeeMarketingBundleDealDetailsDTO">
    SELECT account_number AS account,
           SUM(product_number) AS successNum
    FROM shopee_marketing_bundle_deal
    WHERE account_number = #{accountNumber}
      AND status != 3
    GROUP BY account_number
    ORDER BY account_number;
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update shopee_marketing_bundle_deal
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bundleDealId != null" >
        bundle_deal_id = #{record.bundleDealId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null" >
        merchant_id = #{record.merchantId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantName != null" >
        merchant_name = #{record.merchantName,jdbcType=VARCHAR},
      </if>
      <if test="record.accountNumber != null" >
        account_number = #{record.accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.site != null" >
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.logisticsId != null">
        logistics_id = #{record.logisticsId,jdbcType=INTEGER},
      </if>
      <if test="record.name != null" >
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.productNumber != null" >
        product_number = #{record.productNumber,jdbcType=INTEGER},
      </if>
      <if test="record.addStatus != null">
        `add_status` = #{record.addStatus,jdbcType=INTEGER},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.bundleDealStartTime != null" >
        bundle_deal_start_time = #{record.bundleDealStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.bundleDealEndTime != null" >
        bundle_deal_end_time = #{record.bundleDealEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.syncTime != null" >
        sync_time = #{record.syncTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdTime != null" >
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedTime != null" >
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ruleJson != null">
        rule_json = #{record.ruleJson},
      </if>
      <if test="record.suitType != null">
        suit_type = #{record.suitType,jdbcType=INTEGER},
      </if>
      <if test="record.spuCompose != null">
        spu_compose = #{record.spuCompose,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.shopee.model.ShopeeMarketingBundleDeal" >
    update shopee_marketing_bundle_deal
    <set >
      <if test="bundleDealId != null" >
        bundle_deal_id = #{bundleDealId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null" >
        merchant_id = #{merchantId,jdbcType=BIGINT},
      </if>
      <if test="merchantName != null" >
        merchant_name = #{merchantName,jdbcType=VARCHAR},
      </if>
      <if test="accountNumber != null" >
        account_number = #{accountNumber,jdbcType=VARCHAR},
      </if>
      <if test="site != null" >
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="logisticsId != null">
        logistics_id = #{logisticsId,jdbcType=INTEGER},
      </if>
      <if test="name != null" >
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="productNumber != null" >
        product_number = #{productNumber,jdbcType=INTEGER},
      </if>
      <if test="addStatus != null">
        `add_status` = #{addStatus,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="bundleDealStartTime != null" >
        bundle_deal_start_time = #{bundleDealStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bundleDealEndTime != null" >
        bundle_deal_end_time = #{bundleDealEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="syncTime != null" >
        sync_time = #{syncTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null" >
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastSyncProductExecTime != null" >
        last_sync_product_exec_time = #{lastSyncProductExecTime},
      </if>
      <if test="stopStatus != null" >
        stop_status = #{stopStatus,jdbcType=INTEGER},
      </if>
      <if test="ruleJson != null">
        rule_json = #{ruleJson},
      </if>
      <if test="suitType != null">
        suit_type = #{suitType,jdbcType=INTEGER},
      </if>
      <if test="spuCompose != null">
        spu_compose = #{spuCompose,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getNotExpireBundleId" resultType="java.lang.Long">
    select bundle_deal_id
    from shopee_marketing_bundle_deal
    where status in (1, 2)
      and account_number = #{accountNumber}
  </select>
</mapper>